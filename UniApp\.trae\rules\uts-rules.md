---
description: UTS 语言规则
globs: *.uts
alwaysApply: false
---

# UTS 语言规则

## 基础语法
- UTS 是 TypeScript 的子集，语法与 TypeScript 基本一致
- 支持强类型检查
- 编译时进行类型检查和优化
- 跨平台编译，生成对应平台的原生代码

## 文件结构
```uts
// 导入声明
import { ComponentPublicInstance } from 'vue'
import { RequestOptions } from '@/types/request'

// 类型定义
export interface UserInfo {
  id: number
  name: string
  avatar?: string
}

// 函数定义
export function getUserInfo(id: number): UserInfo | null {
  // 函数实现
  return null
}

// 类定义
export class ApiService {
  private baseUrl: string
  
  constructor(baseUrl: string) {
    this.baseUrl = baseUrl
  }
  
  public request<T>(options: RequestOptions): Promise<T> {
    // 方法实现
    return Promise.resolve({} as T)
  }
}

// 默认导出
export default {
  getUserInfo,
  ApiService
}
```

## 类型系统

### 基础类型
```uts
// 基础类型
let isDone: boolean = false
let count: number = 42
let name: string = "TypeScript"
let list: number[] = [1, 2, 3]
let tuple: [string, number] = ["hello", 10]

// 枚举
enum Color {
  Red,
  Green,
  Blue
}
let c: Color = Color.Green

// Any 类型（谨慎使用）
let notSure: any = 4
notSure = "maybe a string instead"
```

### null 处理
```uts
// 可选类型
let nullableString: string | null = null
let optionalString?: string

// 非空断言
function processString(str: string | null) {
  if (str != null) {
    // 类型收窄，str 在此处为 string 类型
    console.log(str.length)
  }
}

// 空值合并
let result = nullableString ?? "default value"
```

### 条件语句
```uts
// if-else 语句
if (condition) {
  // 执行代码
} else if (anotherCondition) {
  // 执行其他代码
} else {
  // 默认代码
}

// 三元运算符
let result = condition ? "true" : "false"

// switch 语句
switch (value) {
  case 1:
    console.log("One")
    break
  case 2:
    console.log("Two")
    break
  default:
    console.log("Other")
}
```

### 对象类型
```uts
// 接口定义
interface Person {
  readonly id: number
  name: string
  age?: number
  [key: string]: any
}

// 类型别名
type PersonType = {
  id: number
  name: string
}

// 联合类型
type Status = "pending" | "success" | "error"

// 交叉类型
type Employee = Person & {
  department: string
  salary: number
}
```

### 泛型
```uts
// 泛型函数
function identity<T>(arg: T): T {
  return arg
}

// 泛型接口
interface GenericIdentityFn<T> {
  (arg: T): T
}

// 泛型类
class GenericNumber<T> {
  zeroValue: T
  add: (x: T, y: T) => T
  
  constructor(zeroValue: T, addFn: (x: T, y: T) => T) {
    this.zeroValue = zeroValue
    this.add = addFn
  }
}

// 泛型约束
interface Lengthwise {
  length: number
}

function loggingIdentity<T extends Lengthwise>(arg: T): T {
  console.log(arg.length)
  return arg
}
```

## 函数定义
```uts
// 函数声明
function add(x: number, y: number): number {
  return x + y
}

// 函数表达式
const multiply = function(x: number, y: number): number {
  return x * y
}

// 箭头函数
const divide = (x: number, y: number): number => {
  return x / y
}

// 可选参数
function buildName(firstName: string, lastName?: string): string {
  if (lastName) {
    return firstName + " " + lastName
  } else {
    return firstName
  }
}

// 默认参数
function greet(name: string = "World"): string {
  return `Hello, ${name}!`
}

// 剩余参数
function sum(...numbers: number[]): number {
  return numbers.reduce((a, b) => a + b, 0)
}

// 函数重载
function reverse(x: string): string
function reverse(x: number[]): number[]
function reverse(x: string | number[]): string | number[] {
  if (typeof x === "string") {
    return x.split("").reverse().join("")
  } else {
    return x.slice().reverse()
  }
}
```

## 类和继承
```uts
// 基础类
class Animal {
  protected name: string
  private age: number
  
  constructor(name: string, age: number) {
    this.name = name
    this.age = age
  }
  
  public move(distance: number = 0): void {
    console.log(`${this.name} moved ${distance}m.`)
  }
  
  protected getAge(): number {
    return this.age
  }
}

// 继承
class Dog extends Animal {
  private breed: string
  
  constructor(name: string, age: number, breed: string) {
    super(name, age)
    this.breed = breed
  }
  
  public bark(): void {
    console.log(`${this.name} barks!`)
  }
  
  public move(distance: number = 5): void {
    console.log("Running...")
    super.move(distance)
  }
}

// 抽象类
abstract class Shape {
  abstract calculateArea(): number
  
  public displayArea(): void {
    console.log(`Area: ${this.calculateArea()}`)
  }
}

class Circle extends Shape {
  private radius: number
  
  constructor(radius: number) {
    super()
    this.radius = radius
  }
  
  calculateArea(): number {
    return Math.PI * this.radius * this.radius
  }
}
```

## 模块系统
```uts
// 命名导出
export const PI = 3.14159
export function calculateArea(radius: number): number {
  return PI * radius * radius
}

export class Calculator {
  add(a: number, b: number): number {
    return a + b
  }
}

// 默认导出
export default class MathUtils {
  static max(a: number, b: number): number {
    return a > b ? a : b
  }
}

// 重新导出
export { Calculator as MathCalculator } from './calculator'
export * from './constants'
```

## 错误处理
```uts
// try-catch 语句
try {
  const result = riskyOperation()
  console.log(result)
} catch (error: any) {
  console.error('操作失败:', error.message)
} finally {
  console.log('清理资源')
}

// 自定义错误类
class CustomError extends Error {
  public code: number
  
  constructor(message: string, code: number) {
    super(message)
    this.name = 'CustomError'
    this.code = code
  }
}

// 抛出错误
function validateInput(input: string): void {
  if (input.length === 0) {
    throw new CustomError('输入不能为空', 400)
  }
}
```

## 最佳实践

### 代码质量
```uts
// 使用明确的类型注解
function processUser(user: UserInfo): Promise<boolean> {
  // 实现
  return Promise.resolve(true)
}

// 避免使用 any 类型
// 不推荐
function badFunction(data: any): any {
  return data.someProperty
}

// 推荐
function goodFunction(data: { someProperty: string }): string {
  return data.someProperty
}

// 使用类型守卫
function isString(value: unknown): value is string {
  return typeof value === 'string'
}

function processValue(value: unknown): void {
  if (isString(value)) {
    // value 在此处被推断为 string 类型
    console.log(value.toUpperCase())
  }
}
```

### 性能优化
```uts
// 避免在循环中创建函数
// 不推荐
for (let i = 0; i < items.length; i++) {
  items[i].onClick = function() {
    console.log(i)
  }
}

// 推荐
const handleClick = (index: number) => {
  console.log(index)
}

for (let i = 0; i < items.length; i++) {
  items[i].onClick = () => handleClick(i)
}

// 使用对象池避免频繁创建对象
class ObjectPool<T> {
  private pool: T[] = []
  private createFn: () => T
  
  constructor(createFn: () => T) {
    this.createFn = createFn
  }
  
  get(): T {
    return this.pool.pop() ?? this.createFn()
  }
  
  release(obj: T): void {
    this.pool.push(obj)
  }
}
```

### 代码风格
```uts
// 使用 const 和 let，避免 var
const CONSTANT_VALUE = 100
let mutableValue = 0

// 使用模板字符串
const message = `Hello, ${name}! You have ${count} messages.`

// 使用解构赋值
const { id, name, email } = user
const [first, second, ...rest] = array

// 使用扩展运算符
const newArray = [...oldArray, newItem]
const newObject = { ...oldObject, newProperty: value }

// 使用可选链
const userEmail = user?.profile?.email

// 使用空值合并
const displayName = user.name ?? 'Anonymous'
```