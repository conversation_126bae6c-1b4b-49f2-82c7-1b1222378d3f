using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using Admin.NET.Plugin.MQTT.Interfaces;
using Admin.NET.Plugin.MQTT.Models;

namespace Admin.NET.Plugin.MQTT.Services
{
    /// <summary>
    /// 缓存服务实现
    /// 提供高性能的数据缓存、过期管理、内存优化等功能
    /// 支持多级缓存、LRU淘汰策略、统计监控等特性
    /// </summary>
    public class CacheService : ICacheService, IDisposable
    {
        #region 私有字段
        
        private readonly ILogger<CacheService> _logger;
        private readonly IMqttConfigurationManager _configurationManager;
        private readonly IMqttExceptionHandler _exceptionHandler;
        private readonly IMemoryCache _memoryCache;
        
        private readonly ConcurrentDictionary<string, CacheEntry> _cacheEntries;
        private readonly ConcurrentDictionary<string, CacheStatistics> _cacheStatistics;
        private readonly ConcurrentQueue<CacheAccessRecord> _accessHistory;
        
        private readonly Timer _cleanupTimer;
        private readonly Timer _statisticsTimer;
        private readonly ReaderWriterLockSlim _cacheLock;
        
        private volatile bool _isDisposed;
        private long _totalHits;
        private long _totalMisses;
        private long _totalEvictions;
        private long _totalMemoryUsage;
        
        #endregion
        
        #region 事件
        
        /// <summary>
        /// 缓存项过期事件
        /// </summary>
        public event EventHandler<CacheItemExpiredEventArgs> CacheItemExpired;
        
        /// <summary>
        /// 缓存项淘汰事件
        /// </summary>
        public event EventHandler<CacheItemEvictedEventArgs> CacheItemEvicted;
        
        /// <summary>
        /// 内存警告事件
        /// </summary>
        public event EventHandler<MemoryWarningEventArgs> MemoryWarning;
        
        /// <summary>
        /// 缓存统计更新事件
        /// </summary>
        public event EventHandler<CacheStatisticsUpdatedEventArgs> StatisticsUpdated;
        
        #endregion
        
        #region 属性
        
        /// <summary>
        /// 缓存命中率
        /// </summary>
        public double HitRatio
        {
            get
            {
                var totalRequests = _totalHits + _totalMisses;
                return totalRequests > 0 ? (double)_totalHits / totalRequests : 0.0;
            }
        }
        
        /// <summary>
        /// 缓存项数量
        /// </summary>
        public int Count => _cacheEntries.Count;
        
        /// <summary>
        /// 内存使用量（字节）
        /// </summary>
        public long MemoryUsage => Interlocked.Read(ref _totalMemoryUsage);
        
        /// <summary>
        /// 总命中次数
        /// </summary>
        public long TotalHits => Interlocked.Read(ref _totalHits);
        
        /// <summary>
        /// 总未命中次数
        /// </summary>
        public long TotalMisses => Interlocked.Read(ref _totalMisses);
        
        /// <summary>
        /// 总淘汰次数
        /// </summary>
        public long TotalEvictions => Interlocked.Read(ref _totalEvictions);
        
        #endregion
        
        #region 构造函数
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="configurationManager">配置管理器</param>
        /// <param name="exceptionHandler">异常处理器</param>
        /// <param name="memoryCache">内存缓存</param>
        public CacheService(
            ILogger<CacheService> logger,
            IMqttConfigurationManager configurationManager,
            IMqttExceptionHandler exceptionHandler,
            IMemoryCache memoryCache)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configurationManager = configurationManager ?? throw new ArgumentNullException(nameof(configurationManager));
            _exceptionHandler = exceptionHandler ?? throw new ArgumentNullException(nameof(exceptionHandler));
            _memoryCache = memoryCache ?? throw new ArgumentNullException(nameof(memoryCache));
            
            _cacheEntries = new ConcurrentDictionary<string, CacheEntry>();
            _cacheStatistics = new ConcurrentDictionary<string, CacheStatistics>();
            _accessHistory = new ConcurrentQueue<CacheAccessRecord>();
            _cacheLock = new ReaderWriterLockSlim();
            
            var config = _configurationManager.GetCacheConfiguration();
            
            // 初始化定时器
            _cleanupTimer = new Timer(CleanupCallback, null, 
                TimeSpan.FromMinutes(config.CleanupIntervalMinutes), 
                TimeSpan.FromMinutes(config.CleanupIntervalMinutes));
            
            _statisticsTimer = new Timer(StatisticsCallback, null, 
                TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
            
            _logger.LogInformation("缓存服务已初始化，最大条目数: {MaxEntries}，默认过期时间: {DefaultExpiration}分钟", 
                config.MaxCacheEntries, config.DefaultExpirationMinutes);
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 获取缓存项
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">缓存键</param>
        /// <returns>缓存值</returns>
        public T Get<T>(string key)
        {
            if (string.IsNullOrWhiteSpace(key))
                throw new ArgumentException("缓存键不能为空", nameof(key));
            
            _cacheLock.EnterReadLock();
            try
            {
                // 记录访问历史
                RecordAccess(key, CacheAccessType.Get);
                
                // 首先检查内存缓存
                if (_memoryCache.TryGetValue(key, out var memoryValue))
                {
                    Interlocked.Increment(ref _totalHits);
                    UpdateCacheStatistics(key, true);
                    
                    _logger.LogTrace("内存缓存命中: {Key}", key);
                    return (T)memoryValue;
                }
                
                // 检查自定义缓存
                if (_cacheEntries.TryGetValue(key, out var entry))
                {
                    // 检查是否过期
                    if (entry.IsExpired)
                    {
                        // 异步移除过期项
                        _ = Task.Run(() => RemoveExpiredEntry(key, entry));
                        
                        Interlocked.Increment(ref _totalMisses);
                        UpdateCacheStatistics(key, false);
                        
                        _logger.LogTrace("缓存项已过期: {Key}", key);
                        return default(T);
                    }
                    
                    // 更新访问时间
                    entry.LastAccessTime = DateTime.UtcNow;
                    entry.AccessCount++;
                    
                    Interlocked.Increment(ref _totalHits);
                    UpdateCacheStatistics(key, true);
                    
                    _logger.LogTrace("缓存命中: {Key}", key);
                    return JsonSerializer.Deserialize<T>(entry.SerializedValue);
                }
                
                Interlocked.Increment(ref _totalMisses);
                UpdateCacheStatistics(key, false);
                
                _logger.LogTrace("缓存未命中: {Key}", key);
                return default(T);
            }
            catch (Exception ex)
            {
                _exceptionHandler.HandleExceptionAsync(ex, $"CacheGet_{key}").Wait();
                return default(T);
            }
            finally
            {
                _cacheLock.ExitReadLock();
            }
        }
        
        /// <summary>
        /// 异步获取缓存项
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">缓存键</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>缓存值</returns>
        public async Task<T> GetAsync<T>(string key, CancellationToken cancellationToken = default)
        {
            return await Task.Run(() => Get<T>(key), cancellationToken);
        }
        
        /// <summary>
        /// 设置缓存项
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">缓存键</param>
        /// <param name="value">缓存值</param>
        /// <param name="expiration">过期时间</param>
        /// <param name="priority">缓存优先级</param>
        /// <returns>是否设置成功</returns>
        public bool Set<T>(string key, T value, TimeSpan? expiration = null, CachePriority priority = CachePriority.Normal)
        {
            if (string.IsNullOrWhiteSpace(key))
                throw new ArgumentException("缓存键不能为空", nameof(key));
            
            if (value == null)
                throw new ArgumentNullException(nameof(value));
            
            _cacheLock.EnterWriteLock();
            try
            {
                var config = _configurationManager.GetCacheConfiguration();
                var actualExpiration = expiration ?? TimeSpan.FromMinutes(config.DefaultExpirationMinutes);
                
                // 检查缓存容量
                if (_cacheEntries.Count >= config.MaxCacheEntries)
                {
                    EvictLeastRecentlyUsed();
                }
                
                // 序列化值
                var serializedValue = JsonSerializer.Serialize(value);
                var valueSize = System.Text.Encoding.UTF8.GetByteCount(serializedValue);
                
                // 检查内存限制
                if (config.MaxMemoryUsageMB > 0)
                {
                    var maxMemoryBytes = config.MaxMemoryUsageMB * 1024 * 1024;
                    if (_totalMemoryUsage + valueSize > maxMemoryBytes)
                    {
                        EvictByMemoryPressure(valueSize);
                    }
                }
                
                var now = DateTime.UtcNow;
                var entry = new CacheEntry
                {
                    Key = key,
                    SerializedValue = serializedValue,
                    ValueType = typeof(T).FullName,
                    CreationTime = now,
                    LastAccessTime = now,
                    ExpirationTime = now.Add(actualExpiration),
                    Priority = priority,
                    AccessCount = 0,
                    Size = valueSize
                };
                
                // 移除旧条目（如果存在）
                if (_cacheEntries.TryRemove(key, out var oldEntry))
                {
                    Interlocked.Add(ref _totalMemoryUsage, -oldEntry.Size);
                    _memoryCache.Remove(key);
                }
                
                // 添加新条目
                _cacheEntries.TryAdd(key, entry);
                Interlocked.Add(ref _totalMemoryUsage, valueSize);
                
                // 同时添加到内存缓存（用于快速访问）
                var memoryCacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = actualExpiration,
                    Priority = ConvertToCacheItemPriority(priority),
                    Size = valueSize
                };
                
                _memoryCache.Set(key, value, memoryCacheOptions);
                
                // 记录访问历史
                RecordAccess(key, CacheAccessType.Set);
                
                _logger.LogTrace("缓存项已设置: {Key}，大小: {Size} 字节，过期时间: {Expiration}", 
                    key, valueSize, actualExpiration);
                
                return true;
            }
            catch (Exception ex)
            {
                _exceptionHandler.HandleExceptionAsync(ex, $"CacheSet_{key}").Wait();
                return false;
            }
            finally
            {
                _cacheLock.ExitWriteLock();
            }
        }
        
        /// <summary>
        /// 异步设置缓存项
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">缓存键</param>
        /// <param name="value">缓存值</param>
        /// <param name="expiration">过期时间</param>
        /// <param name="priority">缓存优先级</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否设置成功</returns>
        public async Task<bool> SetAsync<T>(string key, T value, TimeSpan? expiration = null, CachePriority priority = CachePriority.Normal, CancellationToken cancellationToken = default)
        {
            return await Task.Run(() => Set(key, value, expiration, priority), cancellationToken);
        }
        
        /// <summary>
        /// 获取或设置缓存项
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">缓存键</param>
        /// <param name="factory">值工厂函数</param>
        /// <param name="expiration">过期时间</param>
        /// <param name="priority">缓存优先级</param>
        /// <returns>缓存值</returns>
        public T GetOrSet<T>(string key, Func<T> factory, TimeSpan? expiration = null, CachePriority priority = CachePriority.Normal)
        {
            if (string.IsNullOrWhiteSpace(key))
                throw new ArgumentException("缓存键不能为空", nameof(key));
            
            if (factory == null)
                throw new ArgumentNullException(nameof(factory));
            
            var cachedValue = Get<T>(key);
            if (cachedValue != null && !cachedValue.Equals(default(T)))
            {
                return cachedValue;
            }
            
            var newValue = factory();
            if (newValue != null)
            {
                Set(key, newValue, expiration, priority);
            }
            
            return newValue;
        }
        
        /// <summary>
        /// 异步获取或设置缓存项
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">缓存键</param>
        /// <param name="factory">异步值工厂函数</param>
        /// <param name="expiration">过期时间</param>
        /// <param name="priority">缓存优先级</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>缓存值</returns>
        public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null, CachePriority priority = CachePriority.Normal, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(key))
                throw new ArgumentException("缓存键不能为空", nameof(key));
            
            if (factory == null)
                throw new ArgumentNullException(nameof(factory));
            
            var cachedValue = await GetAsync<T>(key, cancellationToken);
            if (cachedValue != null && !cachedValue.Equals(default(T)))
            {
                return cachedValue;
            }
            
            var newValue = await factory();
            if (newValue != null)
            {
                await SetAsync(key, newValue, expiration, priority, cancellationToken);
            }
            
            return newValue;
        }
        
        /// <summary>
        /// 移除缓存项
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <returns>是否移除成功</returns>
        public bool Remove(string key)
        {
            if (string.IsNullOrWhiteSpace(key))
                throw new ArgumentException("缓存键不能为空", nameof(key));
            
            _cacheLock.EnterWriteLock();
            try
            {
                var removed = false;
                
                // 从自定义缓存移除
                if (_cacheEntries.TryRemove(key, out var entry))
                {
                    Interlocked.Add(ref _totalMemoryUsage, -entry.Size);
                    removed = true;
                }
                
                // 从内存缓存移除
                _memoryCache.Remove(key);
                
                // 记录访问历史
                RecordAccess(key, CacheAccessType.Remove);
                
                if (removed)
                {
                    _logger.LogTrace("缓存项已移除: {Key}", key);
                }
                
                return removed;
            }
            catch (Exception ex)
            {
                _exceptionHandler.HandleExceptionAsync(ex, $"CacheRemove_{key}").Wait();
                return false;
            }
            finally
            {
                _cacheLock.ExitWriteLock();
            }
        }
        
        /// <summary>
        /// 异步移除缓存项
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否移除成功</returns>
        public async Task<bool> RemoveAsync(string key, CancellationToken cancellationToken = default)
        {
            return await Task.Run(() => Remove(key), cancellationToken);
        }
        
        /// <summary>
        /// 检查缓存项是否存在
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <returns>是否存在</returns>
        public bool Exists(string key)
        {
            if (string.IsNullOrWhiteSpace(key))
                throw new ArgumentException("缓存键不能为空", nameof(key));
            
            _cacheLock.EnterReadLock();
            try
            {
                // 检查内存缓存
                if (_memoryCache.TryGetValue(key, out _))
                {
                    return true;
                }
                
                // 检查自定义缓存
                if (_cacheEntries.TryGetValue(key, out var entry))
                {
                    return !entry.IsExpired;
                }
                
                return false;
            }
            finally
            {
                _cacheLock.ExitReadLock();
            }
        }
        
        /// <summary>
        /// 清空所有缓存
        /// </summary>
        public void Clear()
        {
            _cacheLock.EnterWriteLock();
            try
            {
                var count = _cacheEntries.Count;
                
                _cacheEntries.Clear();
                _cacheStatistics.Clear();
                
                // 清空访问历史（保留最近的一些记录）
                var recentRecords = new List<CacheAccessRecord>();
                var maxRecentRecords = 1000;
                
                while (_accessHistory.TryDequeue(out var record) && recentRecords.Count < maxRecentRecords)
                {
                    if (DateTime.UtcNow - record.Timestamp < TimeSpan.FromMinutes(5))
                    {
                        recentRecords.Add(record);
                    }
                }
                
                foreach (var record in recentRecords)
                {
                    _accessHistory.Enqueue(record);
                }
                
                // 清空内存缓存
                if (_memoryCache is MemoryCache mc)
                {
                    mc.Compact(1.0); // 压缩100%
                }
                
                Interlocked.Exchange(ref _totalMemoryUsage, 0);
                
                _logger.LogInformation("缓存已清空，移除了 {Count} 个条目", count);
            }
            catch (Exception ex)
            {
                _exceptionHandler.HandleExceptionAsync(ex, "CacheClear").Wait();
            }
            finally
            {
                _cacheLock.ExitWriteLock();
            }
        }
        
        /// <summary>
        /// 获取所有缓存键
        /// </summary>
        /// <param name="pattern">键模式（支持通配符）</param>
        /// <returns>缓存键列表</returns>
        public IEnumerable<string> GetKeys(string pattern = null)
        {
            _cacheLock.EnterReadLock();
            try
            {
                var keys = _cacheEntries.Keys.AsEnumerable();
                
                if (!string.IsNullOrWhiteSpace(pattern))
                {
                    var regex = new System.Text.RegularExpressions.Regex(
                        "^" + System.Text.RegularExpressions.Regex.Escape(pattern).Replace("\\*", ".*").Replace("\\?", ".") + "$",
                        System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                    
                    keys = keys.Where(k => regex.IsMatch(k));
                }
                
                return keys.ToList();
            }
            finally
            {
                _cacheLock.ExitReadLock();
            }
        }
        
        /// <summary>
        /// 获取缓存统计信息
        /// </summary>
        /// <returns>缓存统计信息</returns>
        public CacheStatisticsInfo GetStatistics()
        {
            _cacheLock.EnterReadLock();
            try
            {
                var config = _configurationManager.GetCacheConfiguration();
                
                return new CacheStatisticsInfo
                {
                    TotalEntries = _cacheEntries.Count,
                    TotalHits = TotalHits,
                    TotalMisses = TotalMisses,
                    TotalEvictions = TotalEvictions,
                    HitRatio = HitRatio,
                    MemoryUsageBytes = MemoryUsage,
                    MemoryUsageMB = MemoryUsage / (1024.0 * 1024.0),
                    MaxEntries = config.MaxCacheEntries,
                    MaxMemoryMB = config.MaxMemoryUsageMB,
                    MemoryUtilization = config.MaxMemoryUsageMB > 0 ? 
                        (MemoryUsage / (1024.0 * 1024.0)) / config.MaxMemoryUsageMB : 0.0,
                    AverageEntrySize = _cacheEntries.Count > 0 ? MemoryUsage / _cacheEntries.Count : 0,
                    ExpiredEntries = _cacheEntries.Values.Count(e => e.IsExpired),
                    Timestamp = DateTime.UtcNow
                };
            }
            finally
            {
                _cacheLock.ExitReadLock();
            }
        }
        
        /// <summary>
        /// 获取缓存项详细信息
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <returns>缓存项信息</returns>
        public CacheEntryInfo GetEntryInfo(string key)
        {
            if (string.IsNullOrWhiteSpace(key))
                throw new ArgumentException("缓存键不能为空", nameof(key));
            
            _cacheLock.EnterReadLock();
            try
            {
                if (_cacheEntries.TryGetValue(key, out var entry))
                {
                    return new CacheEntryInfo
                    {
                        Key = entry.Key,
                        ValueType = entry.ValueType,
                        Size = entry.Size,
                        CreationTime = entry.CreationTime,
                        LastAccessTime = entry.LastAccessTime,
                        ExpirationTime = entry.ExpirationTime,
                        Priority = entry.Priority,
                        AccessCount = entry.AccessCount,
                        IsExpired = entry.IsExpired,
                        TimeToLive = entry.IsExpired ? TimeSpan.Zero : entry.ExpirationTime - DateTime.UtcNow
                    };
                }
                
                return null;
            }
            finally
            {
                _cacheLock.ExitReadLock();
            }
        }
        
        /// <summary>
        /// 刷新缓存项过期时间
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <param name="expiration">新的过期时间</param>
        /// <returns>是否刷新成功</returns>
        public bool Refresh(string key, TimeSpan? expiration = null)
        {
            if (string.IsNullOrWhiteSpace(key))
                throw new ArgumentException("缓存键不能为空", nameof(key));
            
            _cacheLock.EnterWriteLock();
            try
            {
                if (_cacheEntries.TryGetValue(key, out var entry))
                {
                    var config = _configurationManager.GetCacheConfiguration();
                    var newExpiration = expiration ?? TimeSpan.FromMinutes(config.DefaultExpirationMinutes);
                    
                    entry.ExpirationTime = DateTime.UtcNow.Add(newExpiration);
                    entry.LastAccessTime = DateTime.UtcNow;
                    
                    // 记录访问历史
                    RecordAccess(key, CacheAccessType.Refresh);
                    
                    _logger.LogTrace("缓存项已刷新: {Key}，新过期时间: {Expiration}", key, newExpiration);
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                _exceptionHandler.HandleExceptionAsync(ex, $"CacheRefresh_{key}").Wait();
                return false;
            }
            finally
            {
                _cacheLock.ExitWriteLock();
            }
        }
        
        /// <summary>
        /// 压缩缓存（移除过期项）
        /// </summary>
        /// <returns>移除的条目数</returns>
        public int Compact()
        {
            _cacheLock.EnterWriteLock();
            try
            {
                var expiredKeys = _cacheEntries
                    .Where(kvp => kvp.Value.IsExpired)
                    .Select(kvp => kvp.Key)
                    .ToList();
                
                var removedCount = 0;
                foreach (var key in expiredKeys)
                {
                    if (_cacheEntries.TryRemove(key, out var entry))
                    {
                        Interlocked.Add(ref _totalMemoryUsage, -entry.Size);
                        _memoryCache.Remove(key);
                        removedCount++;
                        
                        // 触发过期事件
                        CacheItemExpired?.Invoke(this, new CacheItemExpiredEventArgs
                        {
                            Key = key,
                            Entry = entry,
                            Timestamp = DateTime.UtcNow
                        });
                    }
                }
                
                // 压缩内存缓存
                if (_memoryCache is MemoryCache mc)
                {
                    mc.Compact(0.1); // 压缩10%
                }
                
                if (removedCount > 0)
                {
                    _logger.LogDebug("缓存压缩完成，移除了 {Count} 个过期条目", removedCount);
                }
                
                return removedCount;
            }
            catch (Exception ex)
            {
                _exceptionHandler.HandleExceptionAsync(ex, "CacheCompact").Wait();
                return 0;
            }
            finally
            {
                _cacheLock.ExitWriteLock();
            }
        }
        
        #endregion
        
        #region 私有方法
        
        /// <summary>
        /// 记录访问历史
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <param name="accessType">访问类型</param>
        private void RecordAccess(string key, CacheAccessType accessType)
        {
            var record = new CacheAccessRecord
            {
                Key = key,
                AccessType = accessType,
                Timestamp = DateTime.UtcNow
            };
            
            _accessHistory.Enqueue(record);
            
            // 限制访问历史记录数量
            while (_accessHistory.Count > 10000)
            {
                _accessHistory.TryDequeue(out _);
            }
        }
        
        /// <summary>
        /// 更新缓存统计
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <param name="isHit">是否命中</param>
        private void UpdateCacheStatistics(string key, bool isHit)
        {
            var stats = _cacheStatistics.AddOrUpdate(key,
                new CacheStatistics
                {
                    Key = key,
                    HitCount = isHit ? 1 : 0,
                    MissCount = isHit ? 0 : 1,
                    LastAccessTime = DateTime.UtcNow
                },
                (k, existing) =>
                {
                    if (isHit)
                        existing.HitCount++;
                    else
                        existing.MissCount++;
                    
                    existing.LastAccessTime = DateTime.UtcNow;
                    return existing;
                });
        }
        
        /// <summary>
        /// 淘汰最近最少使用的条目
        /// </summary>
        private void EvictLeastRecentlyUsed()
        {
            var config = _configurationManager.GetCacheConfiguration();
            var evictCount = Math.Max(1, (int)(_cacheEntries.Count * 0.1)); // 淘汰10%
            
            var candidatesForEviction = _cacheEntries.Values
                .Where(e => e.Priority != CachePriority.NeverRemove)
                .OrderBy(e => e.Priority)
                .ThenBy(e => e.LastAccessTime)
                .Take(evictCount)
                .ToList();
            
            foreach (var entry in candidatesForEviction)
            {
                if (_cacheEntries.TryRemove(entry.Key, out var removedEntry))
                {
                    Interlocked.Add(ref _totalMemoryUsage, -removedEntry.Size);
                    Interlocked.Increment(ref _totalEvictions);
                    
                    _memoryCache.Remove(entry.Key);
                    
                    // 触发淘汰事件
                    CacheItemEvicted?.Invoke(this, new CacheItemEvictedEventArgs
                    {
                        Key = entry.Key,
                        Entry = removedEntry,
                        Reason = EvictionReason.Capacity,
                        Timestamp = DateTime.UtcNow
                    });
                    
                    _logger.LogTrace("LRU淘汰缓存项: {Key}", entry.Key);
                }
            }
        }
        
        /// <summary>
        /// 根据内存压力淘汰条目
        /// </summary>
        /// <param name="requiredSpace">需要的空间</param>
        private void EvictByMemoryPressure(long requiredSpace)
        {
            var config = _configurationManager.GetCacheConfiguration();
            var maxMemoryBytes = config.MaxMemoryUsageMB * 1024 * 1024;
            var targetMemory = maxMemoryBytes * 0.8; // 目标使用80%内存
            var spaceToFree = _totalMemoryUsage + requiredSpace - (long)targetMemory;
            
            if (spaceToFree <= 0)
                return;
            
            var candidatesForEviction = _cacheEntries.Values
                .Where(e => e.Priority != CachePriority.NeverRemove)
                .OrderBy(e => e.Priority)
                .ThenByDescending(e => e.Size) // 优先淘汰大的条目
                .ThenBy(e => e.LastAccessTime)
                .ToList();
            
            long freedSpace = 0;
            foreach (var entry in candidatesForEviction)
            {
                if (freedSpace >= spaceToFree)
                    break;
                
                if (_cacheEntries.TryRemove(entry.Key, out var removedEntry))
                {
                    freedSpace += removedEntry.Size;
                    Interlocked.Add(ref _totalMemoryUsage, -removedEntry.Size);
                    Interlocked.Increment(ref _totalEvictions);
                    
                    _memoryCache.Remove(entry.Key);
                    
                    // 触发淘汰事件
                    CacheItemEvicted?.Invoke(this, new CacheItemEvictedEventArgs
                    {
                        Key = entry.Key,
                        Entry = removedEntry,
                        Reason = EvictionReason.Memory,
                        Timestamp = DateTime.UtcNow
                    });
                    
                    _logger.LogTrace("内存压力淘汰缓存项: {Key}，释放空间: {Size} 字节", entry.Key, removedEntry.Size);
                }
            }
            
            // 触发内存警告事件
            if (freedSpace > 0)
            {
                MemoryWarning?.Invoke(this, new MemoryWarningEventArgs
                {
                    TotalMemoryUsage = MemoryUsage,
                    MaxMemoryLimit = maxMemoryBytes,
                    FreedSpace = freedSpace,
                    Timestamp = DateTime.UtcNow
                });
            }
        }
        
        /// <summary>
        /// 移除过期条目
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <param name="entry">缓存条目</param>
        private async Task RemoveExpiredEntry(string key, CacheEntry entry)
        {
            try
            {
                if (_cacheEntries.TryRemove(key, out var removedEntry))
                {
                    Interlocked.Add(ref _totalMemoryUsage, -removedEntry.Size);
                    _memoryCache.Remove(key);
                    
                    // 触发过期事件
                    CacheItemExpired?.Invoke(this, new CacheItemExpiredEventArgs
                    {
                        Key = key,
                        Entry = removedEntry,
                        Timestamp = DateTime.UtcNow
                    });
                    
                    _logger.LogTrace("过期缓存项已移除: {Key}", key);
                }
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, $"RemoveExpiredEntry_{key}");
            }
        }
        
        /// <summary>
        /// 转换缓存优先级
        /// </summary>
        /// <param name="priority">自定义优先级</param>
        /// <returns>内存缓存优先级</returns>
        private static Microsoft.Extensions.Caching.Memory.CacheItemPriority ConvertToCacheItemPriority(CachePriority priority)
        {
            return priority switch
            {
                CachePriority.Low => Microsoft.Extensions.Caching.Memory.CacheItemPriority.Low,
                CachePriority.Normal => Microsoft.Extensions.Caching.Memory.CacheItemPriority.Normal,
                CachePriority.High => Microsoft.Extensions.Caching.Memory.CacheItemPriority.High,
                CachePriority.NeverRemove => Microsoft.Extensions.Caching.Memory.CacheItemPriority.NeverRemove,
                _ => Microsoft.Extensions.Caching.Memory.CacheItemPriority.Normal
            };
        }
        
        /// <summary>
        /// 清理回调
        /// </summary>
        /// <param name="state">状态对象</param>
        private async void CleanupCallback(object state)
        {
            if (_isDisposed)
                return;
            
            try
            {
                var removedCount = Compact();
                
                // 清理访问历史
                var cutoffTime = DateTime.UtcNow.AddHours(-1);
                var recentRecords = new List<CacheAccessRecord>();
                
                while (_accessHistory.TryDequeue(out var record))
                {
                    if (record.Timestamp > cutoffTime)
                    {
                        recentRecords.Add(record);
                    }
                }
                
                foreach (var record in recentRecords)
                {
                    _accessHistory.Enqueue(record);
                }
                
                // 清理过期统计
                var expiredStatKeys = _cacheStatistics
                    .Where(kvp => DateTime.UtcNow - kvp.Value.LastAccessTime > TimeSpan.FromHours(24))
                    .Select(kvp => kvp.Key)
                    .ToList();
                
                foreach (var key in expiredStatKeys)
                {
                    _cacheStatistics.TryRemove(key, out _);
                }
                
                if (removedCount > 0 || expiredStatKeys.Any())
                {
                    _logger.LogDebug("缓存清理完成: 移除过期条目 {ExpiredEntries}, 清理统计 {ExpiredStats}", 
                        removedCount, expiredStatKeys.Count);
                }
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "CacheCleanupCallback");
            }
        }
        
        /// <summary>
        /// 统计回调
        /// </summary>
        /// <param name="state">状态对象</param>
        private async void StatisticsCallback(object state)
        {
            if (_isDisposed)
                return;
            
            try
            {
                var statistics = GetStatistics();
                
                // 触发统计更新事件
                StatisticsUpdated?.Invoke(this, new CacheStatisticsUpdatedEventArgs
                {
                    Statistics = statistics,
                    Timestamp = DateTime.UtcNow
                });
                
                // 检查内存使用警告
                var config = _configurationManager.GetCacheConfiguration();
                if (config.MaxMemoryUsageMB > 0 && statistics.MemoryUtilization > 0.9)
                {
                    MemoryWarning?.Invoke(this, new MemoryWarningEventArgs
                    {
                        TotalMemoryUsage = statistics.MemoryUsageBytes,
                        MaxMemoryLimit = config.MaxMemoryUsageMB * 1024 * 1024,
                        FreedSpace = 0,
                        Timestamp = DateTime.UtcNow
                    });
                }
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "CacheStatisticsCallback");
            }
        }
        
        #endregion
        
        #region IDisposable实现
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed)
                return;
            
            _isDisposed = true;
            
            try
            {
                // 停止定时器
                _cleanupTimer?.Dispose();
                _statisticsTimer?.Dispose();
                
                // 清空缓存
                Clear();
                
                // 释放锁
                _cacheLock?.Dispose();
                
                _logger.LogInformation("缓存服务已释放资源");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放缓存服务资源时发生错误");
            }
        }
        
        #endregion
    }
    
    #region 辅助类和枚举
    
    /// <summary>
    /// 缓存条目
    /// </summary>
    internal class CacheEntry
    {
        /// <summary>
        /// 缓存键
        /// </summary>
        public string Key { get; set; }
        
        /// <summary>
        /// 序列化后的值
        /// </summary>
        public string SerializedValue { get; set; }
        
        /// <summary>
        /// 值类型
        /// </summary>
        public string ValueType { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreationTime { get; set; }
        
        /// <summary>
        /// 最后访问时间
        /// </summary>
        public DateTime LastAccessTime { get; set; }
        
        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime ExpirationTime { get; set; }
        
        /// <summary>
        /// 缓存优先级
        /// </summary>
        public CachePriority Priority { get; set; }
        
        /// <summary>
        /// 访问次数
        /// </summary>
        public long AccessCount { get; set; }
        
        /// <summary>
        /// 条目大小（字节）
        /// </summary>
        public long Size { get; set; }
        
        /// <summary>
        /// 是否已过期
        /// </summary>
        public bool IsExpired => DateTime.UtcNow > ExpirationTime;
    }
    
    /// <summary>
    /// 缓存访问记录
    /// </summary>
    internal class CacheAccessRecord
    {
        /// <summary>
        /// 缓存键
        /// </summary>
        public string Key { get; set; }
        
        /// <summary>
        /// 访问类型
        /// </summary>
        public CacheAccessType AccessType { get; set; }
        
        /// <summary>
        /// 访问时间
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
    
    /// <summary>
    /// 缓存统计信息
    /// </summary>
    internal class CacheStatistics
    {
        /// <summary>
        /// 缓存键
        /// </summary>
        public string Key { get; set; }
        
        /// <summary>
        /// 命中次数
        /// </summary>
        public long HitCount { get; set; }
        
        /// <summary>
        /// 未命中次数
        /// </summary>
        public long MissCount { get; set; }
        
        /// <summary>
        /// 最后访问时间
        /// </summary>
        public DateTime LastAccessTime { get; set; }
        
        /// <summary>
        /// 命中率
        /// </summary>
        public double HitRatio
        {
            get
            {
                var total = HitCount + MissCount;
                return total > 0 ? (double)HitCount / total : 0.0;
            }
        }
    }
    
    /// <summary>
    /// 缓存访问类型
    /// </summary>
    public enum CacheAccessType
    {
        /// <summary>
        /// 获取
        /// </summary>
        Get,
        
        /// <summary>
        /// 设置
        /// </summary>
        Set,
        
        /// <summary>
        /// 移除
        /// </summary>
        Remove,
        
        /// <summary>
        /// 刷新
        /// </summary>
        Refresh
    }
    
    /// <summary>
    /// 缓存优先级
    /// </summary>
    public enum CachePriority
    {
        /// <summary>
        /// 低优先级
        /// </summary>
        Low = 0,
        
        /// <summary>
        /// 普通优先级
        /// </summary>
        Normal = 1,
        
        /// <summary>
        /// 高优先级
        /// </summary>
        High = 2,
        
        /// <summary>
        /// 永不移除
        /// </summary>
        NeverRemove = 3
    }
    
    /// <summary>
    /// 淘汰原因
    /// </summary>
    public enum EvictionReason
    {
        /// <summary>
        /// 容量限制
        /// </summary>
        Capacity,
        
        /// <summary>
        /// 内存压力
        /// </summary>
        Memory,
        
        /// <summary>
        /// 过期
        /// </summary>
        Expired,
        
        /// <summary>
        /// 手动移除
        /// </summary>
        Manual
    }
    
    #endregion
    
    #region 事件参数类
    
    /// <summary>
    /// 缓存项过期事件参数
    /// </summary>
    public class CacheItemExpiredEventArgs : EventArgs
    {
        /// <summary>
        /// 缓存键
        /// </summary>
        public string Key { get; set; }
        
        /// <summary>
        /// 缓存条目
        /// </summary>
        public CacheEntry Entry { get; set; }
        
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
    
    /// <summary>
    /// 缓存项淘汰事件参数
    /// </summary>
    public class CacheItemEvictedEventArgs : EventArgs
    {
        /// <summary>
        /// 缓存键
        /// </summary>
        public string Key { get; set; }
        
        /// <summary>
        /// 缓存条目
        /// </summary>
        public CacheEntry Entry { get; set; }
        
        /// <summary>
        /// 淘汰原因
        /// </summary>
        public EvictionReason Reason { get; set; }
        
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
    
    /// <summary>
    /// 内存警告事件参数
    /// </summary>
    public class MemoryWarningEventArgs : EventArgs
    {
        /// <summary>
        /// 总内存使用量
        /// </summary>
        public long TotalMemoryUsage { get; set; }
        
        /// <summary>
        /// 最大内存限制
        /// </summary>
        public long MaxMemoryLimit { get; set; }
        
        /// <summary>
        /// 释放的空间
        /// </summary>
        public long FreedSpace { get; set; }
        
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
    
    /// <summary>
    /// 缓存统计更新事件参数
    /// </summary>
    public class CacheStatisticsUpdatedEventArgs : EventArgs
    {
        /// <summary>
        /// 统计信息
        /// </summary>
        public CacheStatisticsInfo Statistics { get; set; }
        
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
    
    #endregion
}