using Microsoft.Extensions.Logging;
using System;
using System.Security.Cryptography;
using System.Text;

namespace Admin.NET.Plugin.MQTT.Test;

/// <summary>
/// MQTT凭证生成测试类
/// 用于验证MqttOptions生成的凭证与SysAuthService保持一致
/// </summary>
public class MqttCredentialsTest
{
    private readonly ILogger<MqttCredentialsTest>? _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public MqttCredentialsTest(ILogger<MqttCredentialsTest>? logger = null)
    {
        _logger = logger;
    }

    /// <summary>
    /// 测试凭证生成的一致性
    /// 验证MqttOptions生成的凭证与SysAuthService的实现是否一致
    /// </summary>
    /// <param name="instanceId">实例ID</param>
    /// <param name="accessKeyId">访问密钥ID</param>
    /// <param name="accessKeySecret">访问密钥密码</param>
    /// <param name="deviceId">设备ID（如IMEI）</param>
    /// <returns>测试结果</returns>
    public TestResult TestCredentialConsistency(string instanceId, string accessKeyId, string accessKeySecret, string deviceId)
    {
        try
        {
            _logger?.LogInformation("开始测试MQTT凭证生成一致性...");

            // 使用SysAuthService的算法生成凭证（参考实现）
            var expectedCredentials = GenerateCredentialsUsingSysAuthLogic(instanceId, accessKeyId, accessKeySecret, deviceId);
            
            // 创建MqttOptions实例并生成凭证
            var mqttOptions = CreateTestMqttOptions(instanceId, accessKeyId, accessKeySecret);
            var actualCredentials = mqttOptions.GenerateCredentials(deviceId);

            // 比较结果
            var result = new TestResult
            {
                IsSuccess = true,
                ExpectedCredentials = expectedCredentials,
                ActualCredentials = actualCredentials,
                TestTime = DateTime.Now
            };

            // 验证ClientId
            if (expectedCredentials.ClientId != actualCredentials.ClientId)
            {
                result.IsSuccess = false;
                result.ErrorMessage += $"ClientId不匹配: 期望={expectedCredentials.ClientId}, 实际={actualCredentials.ClientId}; ";
            }

            // 验证Username
            if (expectedCredentials.Username != actualCredentials.Username)
            {
                result.IsSuccess = false;
                result.ErrorMessage += $"Username不匹配: 期望={expectedCredentials.Username}, 实际={actualCredentials.Username}; ";
            }

            // 验证Password
            if (expectedCredentials.Password != actualCredentials.Password)
            {
                result.IsSuccess = false;
                result.ErrorMessage += $"Password不匹配: 期望={expectedCredentials.Password}, 实际={actualCredentials.Password}; ";
            }

            if (result.IsSuccess)
            {
                _logger?.LogInformation("MQTT凭证生成一致性测试通过");
            }
            else
            {
                _logger?.LogError("MQTT凭证生成一致性测试失败: {ErrorMessage}", result.ErrorMessage);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "MQTT凭证生成一致性测试发生异常");
            return new TestResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                TestTime = DateTime.Now
            };
        }
    }

    /// <summary>
    /// 使用SysAuthService的逻辑生成凭证（参考实现）
    /// 基于SysAuthService.CreateMqttToken方法的实现
    /// </summary>
    /// <param name="instanceId">实例ID</param>
    /// <param name="accessKeyId">访问密钥ID</param>
    /// <param name="accessKeySecret">访问密钥密码</param>
    /// <param name="deviceId">设备ID</param>
    /// <returns>生成的凭证</returns>
    private MqttCredentials GenerateCredentialsUsingSysAuthLogic(string instanceId, string accessKeyId, string accessKeySecret, string deviceId)
    {
        // 根据SysAuthService.CreateMqttToken的实现逻辑
        var clientId = $"{instanceId}@@@{deviceId}";
        var username = $"DeviceCredential|{accessKeyId}|{clientId}";
        var password = ComputeHmacSha1Signature(clientId, accessKeySecret);

        return new MqttCredentials
        {
            ClientId = clientId,
            Username = username,
            Password = password
        };
    }

    /// <summary>
    /// 计算HMAC-SHA1签名
    /// 与SysAuthService.ComputeHmacSha1Signature方法保持一致
    /// </summary>
    /// <param name="data">待签名数据</param>
    /// <param name="key">签名密钥</param>
    /// <returns>Base64编码的签名结果</returns>
    private string ComputeHmacSha1Signature(string data, string key)
    {
        using (var hmac = new HMACSHA1(Encoding.UTF8.GetBytes(key)))
        {
            var hashBytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
            return Convert.ToBase64String(hashBytes);
        }
    }

    /// <summary>
    /// 创建测试用的MqttOptions实例
    /// </summary>
    /// <param name="instanceId">实例ID</param>
    /// <param name="accessKeyId">访问密钥ID</param>
    /// <param name="accessKeySecret">访问密钥密码</param>
    /// <returns>配置好的MqttOptions实例</returns>
    private MqttOptions CreateTestMqttOptions(string instanceId, string accessKeyId, string accessKeySecret)
    {
        var options = new MqttOptions();
        
        // 设置必要的配置参数
        options.InstanceId = instanceId;
        options.AccessKeyId = accessKeyId;
        options.AccessKeySecret = accessKeySecret;
        
        return options;
    }

    /// <summary>
    /// 运行完整的凭证验证测试
    /// 使用MQTT.json配置文件中的实际参数进行测试
    /// </summary>
    /// <returns>测试结果</returns>
    public TestResult RunFullCredentialTest()
    {
        // 使用MQTT.json中的实际配置参数
        var instanceId = "energylight";
        var accessKeyId = "ak_c371a2975f7c4c0eb672b4675bfd2";
        var accessKeySecret = "sk_5905b2a8fb62457691f22aa2446b7d49b9174283b3d64ec785f8557c5ed2d";
        var deviceId = "AdminNET_MQTT_Client";

        _logger?.LogInformation("使用实际配置参数运行完整凭证验证测试");
        _logger?.LogInformation("InstanceId: {InstanceId}", instanceId);
        _logger?.LogInformation("AccessKeyId: {AccessKeyId}", accessKeyId);
        _logger?.LogInformation("DeviceId: {DeviceId}", deviceId);

        return TestCredentialConsistency(instanceId, accessKeyId, accessKeySecret, deviceId);
    }
}

/// <summary>
/// 测试结果类
/// 包含测试执行的详细结果信息
/// </summary>
public class TestResult
{
    /// <summary>
    /// 测试是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误信息（如果测试失败）
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 期望的凭证（SysAuthService生成）
    /// </summary>
    public MqttCredentials? ExpectedCredentials { get; set; }

    /// <summary>
    /// 实际的凭证（MqttOptions生成）
    /// </summary>
    public MqttCredentials? ActualCredentials { get; set; }

    /// <summary>
    /// 测试执行时间
    /// </summary>
    public DateTime TestTime { get; set; }

    /// <summary>
    /// 获取测试结果的详细描述
    /// </summary>
    /// <returns>测试结果描述</returns>
    public override string ToString()
    {
        var sb = new StringBuilder();
        sb.AppendLine($"测试结果: {(IsSuccess ? "通过" : "失败")}");
        sb.AppendLine($"测试时间: {TestTime:yyyy-MM-dd HH:mm:ss}");
        
        if (!IsSuccess)
        {
            sb.AppendLine($"错误信息: {ErrorMessage}");
        }
        
        if (ExpectedCredentials != null)
        {
            sb.AppendLine("期望凭证:");
            sb.AppendLine($"  ClientId: {ExpectedCredentials.ClientId}");
            sb.AppendLine($"  Username: {ExpectedCredentials.Username}");
            sb.AppendLine($"  Password: {ExpectedCredentials.Password}");
        }
        
        if (ActualCredentials != null)
        {
            sb.AppendLine("实际凭证:");
            sb.AppendLine($"  ClientId: {ActualCredentials.ClientId}");
            sb.AppendLine($"  Username: {ActualCredentials.Username}");
            sb.AppendLine($"  Password: {ActualCredentials.Password}");
        }
        
        return sb.ToString();
    }
}