/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { Calendar } from './calendar';
import { CompareInfo } from './compare-info';
import { CultureInfo } from './culture-info';
import { CultureTypes } from './culture-types';
import { DateTimeFormatInfo } from './date-time-format-info';
import { NumberFormatInfo } from './number-format-info';
import { TextInfo } from './text-info';
/**
 * 
 * @export
 * @interface CultureInfo
 */
export interface CultureInfo {
    /**
     * 
     * @type {CultureInfo}
     * @memberof CultureInfo
     */
    parent?: CultureInfo;
    /**
     * 
     * @type {number}
     * @memberof CultureInfo
     */
    lcid?: number;
    /**
     * 
     * @type {number}
     * @memberof CultureInfo
     */
    keyboardLayoutId?: number;
    /**
     * 
     * @type {string}
     * @memberof CultureInfo
     */
    name?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CultureInfo
     */
    ietfLanguageTag?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CultureInfo
     */
    displayName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CultureInfo
     */
    nativeName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CultureInfo
     */
    englishName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CultureInfo
     */
    twoLetterISOLanguageName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CultureInfo
     */
    threeLetterISOLanguageName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CultureInfo
     */
    threeLetterWindowsLanguageName?: string | null;
    /**
     * 
     * @type {CompareInfo}
     * @memberof CultureInfo
     */
    compareInfo?: CompareInfo;
    /**
     * 
     * @type {TextInfo}
     * @memberof CultureInfo
     */
    textInfo?: TextInfo;
    /**
     * 
     * @type {boolean}
     * @memberof CultureInfo
     */
    isNeutralCulture?: boolean;
    /**
     * 
     * @type {CultureTypes}
     * @memberof CultureInfo
     */
    cultureTypes?: CultureTypes;
    /**
     * 
     * @type {NumberFormatInfo}
     * @memberof CultureInfo
     */
    numberFormat?: NumberFormatInfo;
    /**
     * 
     * @type {DateTimeFormatInfo}
     * @memberof CultureInfo
     */
    dateTimeFormat?: DateTimeFormatInfo;
    /**
     * 
     * @type {Calendar}
     * @memberof CultureInfo
     */
    calendar?: Calendar;
    /**
     * 
     * @type {Array<Calendar>}
     * @memberof CultureInfo
     */
    optionalCalendars?: Array<Calendar> | null;
    /**
     * 
     * @type {boolean}
     * @memberof CultureInfo
     */
    useUserOverride?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof CultureInfo
     */
    isReadOnly?: boolean;
}
