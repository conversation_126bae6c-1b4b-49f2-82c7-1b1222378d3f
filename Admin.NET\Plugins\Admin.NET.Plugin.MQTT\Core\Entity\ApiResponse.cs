// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System.Text.Json.Serialization;

namespace Admin.NET.Plugin.MQTT.Core.Entity;

/// <summary>
/// 统一API响应格式
/// 提供标准化的API响应结构，包含状态码、消息、数据和错误信息
/// </summary>
/// <typeparam name="T">响应数据类型</typeparam>
public class ApiResponse<T>
{
    /// <summary>
    /// 响应状态码
    /// 200: 成功, 400: 客户端错误, 500: 服务器错误
    /// </summary>
    [JsonPropertyName("code")]
    public int Code { get; set; }

    /// <summary>
    /// 响应消息
    /// 描述操作结果的文本信息
    /// </summary>
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 响应数据
    /// 实际的业务数据内容
    /// </summary>
    [JsonPropertyName("data")]
    public T? Data { get; set; }

    /// <summary>
    /// 操作是否成功
    /// true: 成功, false: 失败
    /// </summary>
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    /// <summary>
    /// 错误代码
    /// 当操作失败时提供具体的错误标识
    /// </summary>
    [JsonPropertyName("errorCode")]
    public string? ErrorCode { get; set; }

    /// <summary>
    /// 响应时间戳
    /// 服务器处理请求的时间
    /// </summary>
    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 请求追踪ID
    /// 用于日志追踪和问题排查
    /// </summary>
    [JsonPropertyName("traceId")]
    public string? TraceId { get; set; }

    /// <summary>
    /// 创建成功响应
    /// </summary>
    /// <param name="data">响应数据</param>
    /// <param name="message">成功消息</param>
    /// <param name="traceId">追踪ID</param>
    /// <returns>成功响应对象</returns>
    public static ApiResponse<T> Success(T data, string message = "操作成功", string? traceId = null)
    {
        return new ApiResponse<T>
        {
            Code = 200,
            Success = true,
            Message = message,
            Data = data,
            TraceId = traceId ?? Guid.NewGuid().ToString("N")[..8],
            Timestamp = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 创建失败响应
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="errorCode">错误代码</param>
    /// <param name="code">HTTP状态码</param>
    /// <param name="traceId">追踪ID</param>
    /// <returns>失败响应对象</returns>
    public static ApiResponse<T> Failure(string message, string? errorCode = null, int code = 400, string? traceId = null)
    {
        return new ApiResponse<T>
        {
            Code = code,
            Success = false,
            Message = message,
            ErrorCode = errorCode,
            Data = default,
            TraceId = traceId ?? Guid.NewGuid().ToString("N")[..8],
            Timestamp = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 创建服务器错误响应
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="errorCode">错误代码</param>
    /// <param name="traceId">追踪ID</param>
    /// <returns>服务器错误响应对象</returns>
    public static ApiResponse<T> ServerError(string message = "服务器内部错误", string? errorCode = "INTERNAL_ERROR", string? traceId = null)
    {
        return Failure(message, errorCode, 500, traceId);
    }

    /// <summary>
    /// 创建参数验证错误响应
    /// </summary>
    /// <param name="message">验证错误消息</param>
    /// <param name="traceId">追踪ID</param>
    /// <returns>验证错误响应对象</returns>
    public static ApiResponse<T> ValidationError(string message, string? traceId = null)
    {
        return Failure(message, "VALIDATION_ERROR", 400, traceId);
    }

    /// <summary>
    /// 创建业务逻辑错误响应
    /// </summary>
    /// <param name="message">业务错误消息</param>
    /// <param name="errorCode">错误代码</param>
    /// <param name="traceId">追踪ID</param>
    /// <returns>业务错误响应对象</returns>
    public static ApiResponse<T> BusinessError(string message, string errorCode = "BUSINESS_ERROR", string? traceId = null)
    {
        return Failure(message, errorCode, 400, traceId);
    }

    /// <summary>
    /// 创建未授权响应
    /// </summary>
    /// <param name="message">未授权消息</param>
    /// <param name="traceId">追踪ID</param>
    /// <returns>未授权响应对象</returns>
    public static ApiResponse<T> Unauthorized(string message = "未授权访问", string? traceId = null)
    {
        return Failure(message, "UNAUTHORIZED", 401, traceId);
    }

    /// <summary>
    /// 创建禁止访问响应
    /// </summary>
    /// <param name="message">禁止访问消息</param>
    /// <param name="traceId">追踪ID</param>
    /// <returns>禁止访问响应对象</returns>
    public static ApiResponse<T> Forbidden(string message = "禁止访问", string? traceId = null)
    {
        return Failure(message, "FORBIDDEN", 403, traceId);
    }

    /// <summary>
    /// 创建资源未找到响应
    /// </summary>
    /// <param name="message">未找到消息</param>
    /// <param name="traceId">追踪ID</param>
    /// <returns>未找到响应对象</returns>
    public static ApiResponse<T> NotFound(string message = "资源未找到", string? traceId = null)
    {
        return Failure(message, "NOT_FOUND", 404, traceId);
    }

    /// <summary>
    /// 创建超时响应
    /// </summary>
    /// <param name="message">超时消息</param>
    /// <param name="traceId">追踪ID</param>
    /// <returns>超时响应对象</returns>
    public static ApiResponse<T> Timeout(string message = "请求超时", string? traceId = null)
    {
        return Failure(message, "TIMEOUT", 408, traceId);
    }
}

/// <summary>
/// 非泛型API响应格式
/// 用于不需要返回具体数据的场景
/// </summary>
public class ApiResponse : ApiResponse<object>
{
    /// <summary>
    /// 创建成功响应（无数据）
    /// </summary>
    /// <param name="message">成功消息</param>
    /// <param name="traceId">追踪ID</param>
    /// <returns>成功响应对象</returns>
    public static new ApiResponse Success(string message = "操作成功", string? traceId = null)
    {
        return new ApiResponse
        {
            Code = 200,
            Success = true,
            Message = message,
            Data = null,
            TraceId = traceId ?? Guid.NewGuid().ToString("N")[..8],
            Timestamp = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 创建失败响应（无数据）
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="errorCode">错误代码</param>
    /// <param name="code">HTTP状态码</param>
    /// <param name="traceId">追踪ID</param>
    /// <returns>失败响应对象</returns>
    public static new ApiResponse Failure(string message, string? errorCode = null, int code = 400, string? traceId = null)
    {
        return new ApiResponse
        {
            Code = code,
            Success = false,
            Message = message,
            ErrorCode = errorCode,
            Data = null,
            TraceId = traceId ?? Guid.NewGuid().ToString("N")[..8],
            Timestamp = DateTime.UtcNow
        };
    }
}