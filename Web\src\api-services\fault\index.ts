import { service as request } from '/@/utils/request';
import { useBase<PERSON>pi } from '../base';

/**
 * 故障管理API接口集合
 * @method getPage 获取故障分页列表
 * @method getList 获取故障列表
 * @method add 添加故障
 * @method update 更新故障
 * @method delete 删除故障
 * @method handle 处理故障
 */
export function useFaultApi() {
	return {
		...useBaseApi('energyFault'),
		
		// 处理故障
		handle: (data: any) => {
			return request({
				url: '/api/energyFault/handle',
				method: 'post',
				data,
			});
		},
	};
}