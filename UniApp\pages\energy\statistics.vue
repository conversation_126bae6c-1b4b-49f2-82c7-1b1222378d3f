<template>
  <view class="statistics-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">能耗统计</text>
      <view class="header-actions">
        <button class="btn-export" @click="exportData">
          <text class="icon">📊</text>
          <text>导出</text>
        </button>
        <button class="btn-filter" @click="showFilter">
          <text class="icon">🔍</text>
          <text>筛选</text>
        </button>
      </view>
    </view>

    <!-- 时间范围选择 -->
    <view class="time-selector">
      <view class="time-tabs">
        <text 
          v-for="tab in timeTabs" 
          :key="tab.value"
          class="time-tab"
          :class="{ active: activeTimeTab === tab.value }"
          @click="switchTimeTab(tab.value)"
        >
          {{ tab.label }}
        </text>
      </view>
      
      <view v-if="activeTimeTab === 'custom'" class="custom-time">
        <picker 
          mode="date" 
          :value="customStartDate" 
          @change="updateStartDate"
        >
          <view class="date-picker">
            <text class="date-label">开始日期</text>
            <text class="date-value">{{ customStartDate }}</text>
          </view>
        </picker>
        
        <text class="date-separator">至</text>
        
        <picker 
          mode="date" 
          :value="customEndDate" 
          @change="updateEndDate"
        >
          <view class="date-picker">
            <text class="date-label">结束日期</text>
            <text class="date-value">{{ customEndDate }}</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 统计概览 -->
    <view class="overview-section">
      <view class="overview-cards">
        <view class="overview-card">
          <view class="card-header">
            <text class="card-title">总用电量</text>
            <text class="card-period">{{ getPeriodText() }}</text>
          </view>
          <view class="card-content">
            <text class="card-value">{{ totalConsumption.toFixed(2) }}</text>
            <text class="card-unit">kWh</text>
            <view class="card-trend" :class="getTrendClass(consumptionTrend)">
              <text class="trend-icon">{{ getTrendIcon(consumptionTrend) }}</text>
              <text class="trend-text">{{ Math.abs(consumptionTrend).toFixed(1) }}%</text>
            </view>
          </view>
        </view>
        
        <view class="overview-card">
          <view class="card-header">
            <text class="card-title">总费用</text>
            <text class="card-period">{{ getPeriodText() }}</text>
          </view>
          <view class="card-content">
            <text class="card-value">{{ totalCost.toFixed(2) }}</text>
            <text class="card-unit">元</text>
            <view class="card-trend" :class="getTrendClass(costTrend)">
              <text class="trend-icon">{{ getTrendIcon(costTrend) }}</text>
              <text class="trend-text">{{ Math.abs(costTrend).toFixed(1) }}%</text>
            </view>
          </view>
        </view>
        
        <view class="overview-card">
          <view class="card-header">
            <text class="card-title">平均功耗</text>
            <text class="card-period">{{ getPeriodText() }}</text>
          </view>
          <view class="card-content">
            <text class="card-value">{{ averagePower.toFixed(1) }}</text>
            <text class="card-unit">kW</text>
            <view class="card-trend" :class="getTrendClass(powerTrend)">
              <text class="trend-icon">{{ getTrendIcon(powerTrend) }}</text>
              <text class="trend-text">{{ Math.abs(powerTrend).toFixed(1) }}%</text>
            </view>
          </view>
        </view>
        
        <view class="overview-card">
          <view class="card-header">
            <text class="card-title">节能效果</text>
            <text class="card-period">vs 上期</text>
          </view>
          <view class="card-content">
            <text class="card-value">{{ energySaving.toFixed(1) }}</text>
            <text class="card-unit">%</text>
            <view class="card-trend positive">
              <text class="trend-icon">💡</text>
              <text class="trend-text">节能{{ energySavingAmount.toFixed(1) }}kWh</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 图表分析 -->
    <view class="chart-section">
      <view class="chart-tabs">
        <text 
          v-for="chart in chartTabs" 
          :key="chart.value"
          class="chart-tab"
          :class="{ active: activeChart === chart.value }"
          @click="switchChart(chart.value)"
        >
          {{ chart.label }}
        </text>
      </view>
      
      <view class="chart-container">
        <!-- 用电量趋势图 -->
        <view v-if="activeChart === 'consumption'" class="chart-wrapper">
          <view class="chart-header">
            <text class="chart-title">用电量趋势</text>
            <view class="chart-options">
              <picker 
                :value="consumptionChartType" 
                :range="chartTypes" 
                range-key="label"
                @change="updateConsumptionChartType"
              >
                <view class="chart-type-selector">
                  <text>{{ getChartTypeLabel(consumptionChartType) }}</text>
                  <text class="selector-arrow">▼</text>
                </view>
              </picker>
            </view>
          </view>
          
          <view class="chart-canvas-wrapper">
            <canvas 
              canvas-id="consumptionChart" 
              class="chart-canvas"
              @touchstart="onChartTouch"
            ></canvas>
            
            <!-- 图表提示 -->
            <view v-if="chartTooltip.show" class="chart-tooltip" :style="tooltipStyle">
              <text class="tooltip-date">{{ chartTooltip.date }}</text>
              <text class="tooltip-value">{{ chartTooltip.value }}kWh</text>
            </view>
          </view>
          
          <view class="chart-legend">
            <view class="legend-item">
              <view class="legend-color" style="background-color: #1890ff;"></view>
              <text class="legend-text">当期用电量</text>
            </view>
            <view class="legend-item">
              <view class="legend-color" style="background-color: #52c41a;"></view>
              <text class="legend-text">同期用电量</text>
            </view>
          </view>
        </view>
        
        <!-- 费用分析图 -->
        <view v-if="activeChart === 'cost'" class="chart-wrapper">
          <view class="chart-header">
            <text class="chart-title">费用分析</text>
            <view class="chart-options">
              <view class="cost-breakdown">
                <view class="breakdown-item">
                  <text class="breakdown-label">基础电费</text>
                  <text class="breakdown-value">{{ baseCost.toFixed(2) }}元</text>
                </view>
                <view class="breakdown-item">
                  <text class="breakdown-label">峰谷电费</text>
                  <text class="breakdown-value">{{ peakValleyCost.toFixed(2) }}元</text>
                </view>
                <view class="breakdown-item">
                  <text class="breakdown-label">其他费用</text>
                  <text class="breakdown-value">{{ otherCost.toFixed(2) }}元</text>
                </view>
              </view>
            </view>
          </view>
          
          <view class="chart-canvas-wrapper">
            <canvas 
              canvas-id="costChart" 
              class="chart-canvas"
            ></canvas>
          </view>
        </view>
        
        <!-- 设备对比图 -->
        <view v-if="activeChart === 'device'" class="chart-wrapper">
          <view class="chart-header">
            <text class="chart-title">设备能耗对比</text>
            <view class="chart-options">
              <picker 
                :value="deviceCompareType" 
                :range="deviceCompareTypes" 
                range-key="label"
                @change="updateDeviceCompareType"
              >
                <view class="chart-type-selector">
                  <text>{{ getDeviceCompareTypeLabel() }}</text>
                  <text class="selector-arrow">▼</text>
                </view>
              </picker>
            </view>
          </view>
          
          <view class="chart-canvas-wrapper">
            <canvas 
              canvas-id="deviceChart" 
              class="chart-canvas"
            ></canvas>
          </view>
          
          <view class="device-ranking">
            <view class="ranking-header">
              <text class="ranking-title">设备排行</text>
              <text class="ranking-subtitle">按{{ getDeviceCompareTypeLabel() }}排序</text>
            </view>
            
            <view class="ranking-list">
              <view 
                v-for="(device, index) in deviceRanking" 
                :key="device.id"
                class="ranking-item"
              >
                <view class="ranking-number" :class="getRankClass(index)">
                  <text>{{ index + 1 }}</text>
                </view>
                <view class="device-info">
                  <text class="device-name">{{ device.name }}</text>
                  <text class="device-location">{{ device.location }}</text>
                </view>
                <view class="device-value">
                  <text class="value-number">{{ getDeviceValue(device) }}</text>
                  <text class="value-unit">{{ getDeviceUnit() }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 时段分析图 -->
        <view v-if="activeChart === 'period'" class="chart-wrapper">
          <view class="chart-header">
            <text class="chart-title">时段用电分析</text>
            <view class="chart-options">
              <view class="period-stats">
                <view class="period-item">
                  <text class="period-label">峰时段</text>
                  <text class="period-value">{{ peakPeriodConsumption.toFixed(1) }}kWh</text>
                  <text class="period-percent">({{ (peakPeriodConsumption / totalConsumption * 100).toFixed(1) }}%)</text>
                </view>
                <view class="period-item">
                  <text class="period-label">平时段</text>
                  <text class="period-value">{{ normalPeriodConsumption.toFixed(1) }}kWh</text>
                  <text class="period-percent">({{ (normalPeriodConsumption / totalConsumption * 100).toFixed(1) }}%)</text>
                </view>
                <view class="period-item">
                  <text class="period-label">谷时段</text>
                  <text class="period-value">{{ valleyPeriodConsumption.toFixed(1) }}kWh</text>
                  <text class="period-percent">({{ (valleyPeriodConsumption / totalConsumption * 100).toFixed(1) }}%)</text>
                </view>
              </view>
            </view>
          </view>
          
          <view class="chart-canvas-wrapper">
            <canvas 
              canvas-id="periodChart" 
              class="chart-canvas"
            ></canvas>
          </view>
        </view>
      </view>
    </view>

    <!-- 详细报表 -->
    <view class="report-section">
      <view class="section-header">
        <text class="section-title">详细报表</text>
        <view class="report-actions">
          <picker 
            :value="reportType" 
            :range="reportTypes" 
            range-key="label"
            @change="updateReportType"
          >
            <view class="report-type-selector">
              <text>{{ getReportTypeLabel() }}</text>
              <text class="selector-arrow">▼</text>
            </view>
          </picker>
          
          <button class="btn-download" @click="downloadReport">
            <text class="icon">📥</text>
            <text>下载</text>
          </button>
        </view>
      </view>
      
      <view class="report-table">
        <view class="table-header">
          <text 
            v-for="column in getTableColumns()" 
            :key="column.key"
            class="table-cell header-cell"
            :style="{ width: column.width }"
          >
            {{ column.title }}
          </text>
        </view>
        
        <scroll-view class="table-body" scroll-y>
          <view 
            v-for="(row, index) in getTableData()" 
            :key="index"
            class="table-row"
            :class="{ 'row-even': index % 2 === 0 }"
          >
            <text 
              v-for="column in getTableColumns()" 
              :key="column.key"
              class="table-cell"
              :style="{ width: column.width }"
            >
              {{ formatCellValue(row[column.key], column.type) }}
            </text>
          </view>
        </scroll-view>
      </view>
      
      <!-- 分页 -->
      <view class="pagination">
        <button 
          class="page-btn"
          :disabled="currentPage === 1"
          @click="prevPage"
        >
          上一页
        </button>
        
        <view class="page-info">
          <text>第 {{ currentPage }} 页，共 {{ totalPages }} 页</text>
        </view>
        
        <button 
          class="page-btn"
          :disabled="currentPage === totalPages"
          @click="nextPage"
        >
          下一页
        </button>
      </view>
    </view>

    <!-- 筛选弹窗 -->
    <uni-popup ref="filterModal" type="center">
      <view class="filter-modal">
        <view class="modal-header">
          <text class="modal-title">筛选条件</text>
          <button class="btn-close" @click="closeFilter">✕</button>
        </view>
        
        <view class="modal-content">
          <view class="filter-section">
            <text class="filter-title">设备类型</text>
            <view class="filter-options">
              <view 
                v-for="type in deviceTypes" 
                :key="type.value"
                class="filter-option"
                :class="{ active: selectedDeviceTypes.includes(type.value) }"
                @click="toggleDeviceType(type.value)"
              >
                <text class="option-text">{{ type.label }}</text>
                <text v-if="selectedDeviceTypes.includes(type.value)" class="option-check">✓</text>
              </view>
            </view>
          </view>
          
          <view class="filter-section">
            <text class="filter-title">区域位置</text>
            <view class="filter-options">
              <view 
                v-for="location in locations" 
                :key="location.value"
                class="filter-option"
                :class="{ active: selectedLocations.includes(location.value) }"
                @click="toggleLocation(location.value)"
              >
                <text class="option-text">{{ location.label }}</text>
                <text v-if="selectedLocations.includes(location.value)" class="option-check">✓</text>
              </view>
            </view>
          </view>
          
          <view class="filter-section">
            <text class="filter-title">用电量范围</text>
            <view class="range-inputs">
              <input 
                v-model.number="consumptionRange.min" 
                class="range-input" 
                type="number" 
                placeholder="最小值"
              />
              <text class="range-separator">-</text>
              <input 
                v-model.number="consumptionRange.max" 
                class="range-input" 
                type="number" 
                placeholder="最大值"
              />
              <text class="range-unit">kWh</text>
            </view>
          </view>
          
          <view class="filter-section">
            <text class="filter-title">费用范围</text>
            <view class="range-inputs">
              <input 
                v-model.number="costRange.min" 
                class="range-input" 
                type="number" 
                placeholder="最小值"
              />
              <text class="range-separator">-</text>
              <input 
                v-model.number="costRange.max" 
                class="range-input" 
                type="number" 
                placeholder="最大值"
              />
              <text class="range-unit">元</text>
            </view>
          </view>
        </view>
        
        <view class="modal-actions">
          <button class="btn-reset" @click="resetFilter">重置</button>
          <button class="btn-apply" @click="applyFilter">应用</button>
        </view>
      </view>
    </uni-popup>

    <!-- 导出弹窗 -->
    <uni-popup ref="exportModal" type="center">
      <view class="export-modal">
        <view class="modal-header">
          <text class="modal-title">导出数据</text>
          <button class="btn-close" @click="closeExport">✕</button>
        </view>
        
        <view class="modal-content">
          <view class="export-section">
            <text class="export-title">导出格式</text>
            <view class="export-formats">
              <view 
                v-for="format in exportFormats" 
                :key="format.value"
                class="format-option"
                :class="{ active: selectedExportFormat === format.value }"
                @click="selectExportFormat(format.value)"
              >
                <view class="format-icon">
                  <text>{{ format.icon }}</text>
                </view>
                <view class="format-info">
                  <text class="format-name">{{ format.label }}</text>
                  <text class="format-desc">{{ format.description }}</text>
                </view>
              </view>
            </view>
          </view>
          
          <view class="export-section">
            <text class="export-title">导出内容</text>
            <view class="export-contents">
              <view 
                v-for="content in exportContents" 
                :key="content.value"
                class="content-option"
                :class="{ active: selectedExportContents.includes(content.value) }"
                @click="toggleExportContent(content.value)"
              >
                <text class="content-text">{{ content.label }}</text>
                <text v-if="selectedExportContents.includes(content.value)" class="content-check">✓</text>
              </view>
            </view>
          </view>
          
          <view class="export-section">
            <text class="export-title">文件名称</text>
            <input 
              v-model="exportFileName" 
              class="export-filename" 
              placeholder="请输入文件名称"
            />
          </view>
        </view>
        
        <view class="modal-actions">
          <button class="btn-cancel" @click="closeExport">取消</button>
          <button class="btn-export-confirm" @click="confirmExport">导出</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'

export default {
  name: 'StatisticsPage',
  setup() {
    // 响应式数据
    const activeTimeTab = ref('month')
    const activeChart = ref('consumption')
    const customStartDate = ref('2024-01-01')
    const customEndDate = ref('2024-01-31')
    const consumptionChartType = ref(0)
    const deviceCompareType = ref(0)
    const reportType = ref(0)
    const currentPage = ref(1)
    const pageSize = ref(20)
    const selectedExportFormat = ref('excel')
    const exportFileName = ref('')
    
    // 定时器变量
    const exportTimer = ref(null)
    const downloadTimer = ref(null)
    const tooltipTimer = ref(null)
    
    // 统计数据
    const totalConsumption = ref(1245.67)
    const consumptionTrend = ref(-5.2)
    const totalCost = ref(892.34)
    const costTrend = ref(-3.8)
    const averagePower = ref(15.6)
    const powerTrend = ref(2.1)
    const energySaving = ref(12.5)
    const energySavingAmount = ref(156.8)
    
    // 费用分解
    const baseCost = ref(650.20)
    const peakValleyCost = ref(180.45)
    const otherCost = ref(61.69)
    
    // 时段用电
    const peakPeriodConsumption = ref(456.78)
    const normalPeriodConsumption = ref(523.45)
    const valleyPeriodConsumption = ref(265.44)
    
    const chartTooltip = reactive({
      show: false,
      x: 0,
      y: 0,
      date: '',
      value: ''
    })
    
    // 筛选条件
    const selectedDeviceTypes = ref([])
    const selectedLocations = ref([])
    const consumptionRange = reactive({ min: null, max: null })
    const costRange = reactive({ min: null, max: null })
    const selectedExportContents = ref(['consumption', 'cost'])
    
    // 配置数据
    const timeTabs = [
      { value: 'week', label: '本周' },
      { value: 'month', label: '本月' },
      { value: 'quarter', label: '本季度' },
      { value: 'year', label: '本年' },
      { value: 'custom', label: '自定义' }
    ]
    
    const chartTabs = [
      { value: 'consumption', label: '用电量趋势' },
      { value: 'cost', label: '费用分析' },
      { value: 'device', label: '设备对比' },
      { value: 'period', label: '时段分析' }
    ]
    
    const chartTypes = [
      { value: 'line', label: '折线图' },
      { value: 'bar', label: '柱状图' },
      { value: 'area', label: '面积图' }
    ]
    
    const deviceCompareTypes = [
      { value: 'consumption', label: '用电量' },
      { value: 'cost', label: '费用' },
      { value: 'efficiency', label: '能效' },
      { value: 'runtime', label: '运行时长' }
    ]
    
    const reportTypes = [
      { value: 'daily', label: '日报表' },
      { value: 'weekly', label: '周报表' },
      { value: 'monthly', label: '月报表' },
      { value: 'device', label: '设备报表' }
    ]
    
    const deviceTypes = [
      { value: 'led', label: 'LED灯' },
      { value: 'fluorescent', label: '荧光灯' },
      { value: 'halogen', label: '卤素灯' },
      { value: 'sensor', label: '感应灯' },
      { value: 'emergency', label: '应急灯' }
    ]
    
    const locations = [
      { value: 'office', label: '办公区' },
      { value: 'meeting', label: '会议室' },
      { value: 'corridor', label: '走廊' },
      { value: 'lobby', label: '大厅' },
      { value: 'storage', label: '储物间' }
    ]
    
    const exportFormats = [
      {
        value: 'excel',
        label: 'Excel',
        icon: '📊',
        description: '适合数据分析和处理'
      },
      {
        value: 'pdf',
        label: 'PDF',
        icon: '📄',
        description: '适合打印和分享'
      },
      {
        value: 'csv',
        label: 'CSV',
        icon: '📋',
        description: '适合数据导入'
      }
    ]
    
    const exportContents = [
      { value: 'consumption', label: '用电量数据' },
      { value: 'cost', label: '费用数据' },
      { value: 'device', label: '设备信息' },
      { value: 'statistics', label: '统计分析' }
    ]
    
    // 模拟设备数据
    const deviceRanking = ref([
      {
        id: 1,
        name: '办公室主灯A',
        location: '办公楼A座201',
        consumption: 125.6,
        cost: 89.2,
        efficiency: 92.5,
        runtime: 720 // 小时
      },
      {
        id: 2,
        name: '会议室吊灯',
        location: '会议室A',
        consumption: 98.4,
        cost: 69.8,
        efficiency: 88.3,
        runtime: 580
      },
      {
        id: 3,
        name: '走廊感应灯',
        location: '走廊',
        consumption: 76.2,
        cost: 54.1,
        efficiency: 95.1,
        runtime: 720
      },
      {
        id: 4,
        name: '休息区氛围灯',
        location: '休息区',
        consumption: 65.8,
        cost: 46.7,
        efficiency: 85.2,
        runtime: 480
      },
      {
        id: 5,
        name: '楼梯应急灯',
        location: '楼梯间',
        consumption: 45.3,
        cost: 32.1,
        efficiency: 90.8,
        runtime: 720
      }
    ])
    
    // 模拟报表数据
    const reportData = ref({
      daily: [
        { date: '2024-01-01', consumption: 45.6, cost: 32.4, devices: 24, efficiency: 89.2 },
        { date: '2024-01-02', consumption: 48.2, cost: 34.2, devices: 25, efficiency: 87.8 },
        { date: '2024-01-03', consumption: 42.1, cost: 29.9, devices: 23, efficiency: 91.5 }
      ],
      weekly: [
        { week: '第1周', consumption: 312.5, cost: 221.8, avgDevices: 24.2, efficiency: 89.6 },
        { week: '第2周', consumption: 298.7, cost: 212.1, avgDevices: 23.8, efficiency: 90.2 },
        { week: '第3周', consumption: 325.1, cost: 230.7, avgDevices: 24.6, efficiency: 88.9 }
      ],
      monthly: [
        { month: '1月', consumption: 1245.6, cost: 884.2, avgDevices: 24.1, efficiency: 89.5 },
        { month: '2月', consumption: 1156.8, cost: 821.4, avgDevices: 23.9, efficiency: 90.8 },
        { month: '3月', consumption: 1298.4, cost: 922.1, avgDevices: 24.3, efficiency: 88.7 }
      ],
      device: deviceRanking.value
    })

    // 计算属性
    const tooltipStyle = computed(() => {
      return {
        left: chartTooltip.x + 'px',
        top: chartTooltip.y + 'px'
      }
    })
    
    const totalPages = computed(() => {
      const data = getTableData()
      return Math.ceil(data.length / pageSize.value)
    })

    // 方法
    const getPeriodText = () => {
      const periodMap = {
        week: '本周',
        month: '本月',
        quarter: '本季度',
        year: '本年',
        custom: '自定义期间'
      }
      return periodMap[activeTimeTab.value] || '本月'
    }

    const getTrendClass = (trend) => {
      return {
        'trend-up': trend > 0,
        'trend-down': trend < 0,
        'trend-stable': trend === 0
      }
    }

    const getTrendIcon = (trend) => {
      if (trend > 0) return '↗'
      if (trend < 0) return '↘'
      return '→'
    }

    const switchTimeTab = (tab) => {
      activeTimeTab.value = tab
      refreshData()
    }

    const switchChart = (chart) => {
      activeChart.value = chart
      drawChart()
    }

    const updateStartDate = (event) => {
      customStartDate.value = event.detail.value
      if (activeTimeTab.value === 'custom') {
        refreshData()
      }
    }

    const updateEndDate = (event) => {
      customEndDate.value = event.detail.value
      if (activeTimeTab.value === 'custom') {
        refreshData()
      }
    }

    const updateConsumptionChartType = (event) => {
      consumptionChartType.value = event.detail.value
      drawChart()
    }

    const updateDeviceCompareType = (event) => {
      deviceCompareType.value = event.detail.value
      drawChart()
    }

    const updateReportType = (event) => {
      reportType.value = event.detail.value
      currentPage.value = 1
    }

    const getChartTypeLabel = (index) => {
      return chartTypes[index]?.label || '折线图'
    }

    const getDeviceCompareTypeLabel = () => {
      return deviceCompareTypes[deviceCompareType.value]?.label || '用电量'
    }

    const getReportTypeLabel = () => {
      return reportTypes[reportType.value]?.label || '日报表'
    }

    const getRankClass = (index) => {
      return {
        'rank-first': index === 0,
        'rank-second': index === 1,
        'rank-third': index === 2
      }
    }

    const getDeviceValue = (device) => {
      const type = deviceCompareTypes[deviceCompareType.value]?.value
      switch (type) {
        case 'consumption':
          return device.consumption.toFixed(1)
        case 'cost':
          return device.cost.toFixed(2)
        case 'efficiency':
          return device.efficiency.toFixed(1)
        case 'runtime':
          return device.runtime
        default:
          return device.consumption.toFixed(1)
      }
    }

    const getDeviceUnit = () => {
      const type = deviceCompareTypes[deviceCompareType.value]?.value
      const unitMap = {
        consumption: 'kWh',
        cost: '元',
        efficiency: '%',
        runtime: 'h'
      }
      return unitMap[type] || 'kWh'
    }

    const getTableColumns = () => {
      const type = reportTypes[reportType.value]?.value
      const columnMap = {
        daily: [
          { key: 'date', title: '日期', width: '25%', type: 'date' },
          { key: 'consumption', title: '用电量(kWh)', width: '25%', type: 'number' },
          { key: 'cost', title: '费用(元)', width: '25%', type: 'currency' },
          { key: 'efficiency', title: '能效(%)', width: '25%', type: 'percent' }
        ],
        weekly: [
          { key: 'week', title: '周次', width: '25%', type: 'text' },
          { key: 'consumption', title: '用电量(kWh)', width: '25%', type: 'number' },
          { key: 'cost', title: '费用(元)', width: '25%', type: 'currency' },
          { key: 'efficiency', title: '能效(%)', width: '25%', type: 'percent' }
        ],
        monthly: [
          { key: 'month', title: '月份', width: '25%', type: 'text' },
          { key: 'consumption', title: '用电量(kWh)', width: '25%', type: 'number' },
          { key: 'cost', title: '费用(元)', width: '25%', type: 'currency' },
          { key: 'efficiency', title: '能效(%)', width: '25%', type: 'percent' }
        ],
        device: [
          { key: 'name', title: '设备名称', width: '30%', type: 'text' },
          { key: 'location', title: '位置', width: '25%', type: 'text' },
          { key: 'consumption', title: '用电量(kWh)', width: '22.5%', type: 'number' },
          { key: 'cost', title: '费用(元)', width: '22.5%', type: 'currency' }
        ]
      }
      return columnMap[type] || columnMap.daily
    }

    const getTableData = () => {
      const type = reportTypes[reportType.value]?.value
      return reportData.value[type] || []
    }

    const formatCellValue = (value, type) => {
      if (value === null || value === undefined) return '-'
      
      switch (type) {
        case 'number':
          return typeof value === 'number' ? value.toFixed(1) : value
        case 'currency':
          return typeof value === 'number' ? `¥${value.toFixed(2)}` : value
        case 'percent':
          return typeof value === 'number' ? `${value.toFixed(1)}%` : value
        case 'date':
          return value
        default:
          return value
      }
    }

    const prevPage = () => {
      if (currentPage.value > 1) {
        currentPage.value--
      }
    }

    const nextPage = () => {
      if (currentPage.value < totalPages.value) {
        currentPage.value++
      }
    }

    // 筛选相关方法
    const showFilter = () => {
      uni.$refs.filterModal.open()
    }

    const closeFilter = () => {
      uni.$refs.filterModal.close()
    }

    const toggleDeviceType = (type) => {
      const index = selectedDeviceTypes.value.indexOf(type)
      if (index > -1) {
        selectedDeviceTypes.value.splice(index, 1)
      } else {
        selectedDeviceTypes.value.push(type)
      }
    }

    const toggleLocation = (location) => {
      const index = selectedLocations.value.indexOf(location)
      if (index > -1) {
        selectedLocations.value.splice(index, 1)
      } else {
        selectedLocations.value.push(location)
      }
    }

    const resetFilter = () => {
      selectedDeviceTypes.value = []
      selectedLocations.value = []
      consumptionRange.min = null
      consumptionRange.max = null
      costRange.min = null
      costRange.max = null
    }

    const applyFilter = () => {
      // 应用筛选条件
      refreshData()
      closeFilter()
      uni.showToast({ title: '筛选已应用', icon: 'success' })
    }

    // 导出相关方法
    const exportData = () => {
      exportFileName.value = `能耗统计_${getPeriodText()}_${new Date().toISOString().slice(0, 10)}`
      uni.$refs.exportModal.open()
    }

    const closeExport = () => {
      uni.$refs.exportModal.close()
    }

    const selectExportFormat = (format) => {
      selectedExportFormat.value = format
    }

    const toggleExportContent = (content) => {
      const index = selectedExportContents.value.indexOf(content)
      if (index > -1) {
        selectedExportContents.value.splice(index, 1)
      } else {
        selectedExportContents.value.push(content)
      }
    }

    const confirmExport = async () => {
      if (!exportFileName.value.trim()) {
        uni.showToast({ title: '请输入文件名称', icon: 'error' })
        return
      }
      
      if (selectedExportContents.value.length === 0) {
        uni.showToast({ title: '请选择导出内容', icon: 'error' })
        return
      }
      
      try {
        uni.showLoading({ title: '导出中...' })
        
        // 模拟导出过程
        await new Promise(resolve => {
          exportTimer.value = setTimeout(() => {
            exportTimer.value = null
            resolve()
          }, 2000)
        })
        
        uni.hideLoading()
        uni.showToast({ title: '导出成功', icon: 'success' })
        closeExport()
      } catch (error) {
        uni.hideLoading()
        uni.showToast({ title: '导出失败', icon: 'error' })
      }
    }

    const downloadReport = async () => {
      try {
        uni.showLoading({ title: '下载中...' })
        
        // 模拟下载过程
        await new Promise(resolve => {
          downloadTimer.value = setTimeout(() => {
            downloadTimer.value = null
            resolve()
          }, 1500)
        })
        
        uni.hideLoading()
        uni.showToast({ title: '下载成功', icon: 'success' })
      } catch (error) {
        uni.hideLoading()
        uni.showToast({ title: '下载失败', icon: 'error' })
      }
    }

    // 图表相关方法
    const drawChart = () => {
      // 这里应该使用实际的图表库绘制图表
      console.log('绘制图表:', activeChart.value)
    }

    const onChartTouch = (event) => {
      const touch = event.touches[0]
      chartTooltip.x = touch.x
      chartTooltip.y = touch.y - 50
      chartTooltip.date = '2024-01-15'
      chartTooltip.value = '45.6'
      chartTooltip.show = true
      
      if (tooltipTimer.value) {
        clearTimeout(tooltipTimer.value)
      }
      
      tooltipTimer.value = setTimeout(() => {
        chartTooltip.show = false
        tooltipTimer.value = null
      }, 3000)
    }

    const refreshData = () => {
      // 根据当前筛选条件刷新数据
      console.log('刷新数据', {
        timeTab: activeTimeTab.value,
        deviceTypes: selectedDeviceTypes.value,
        locations: selectedLocations.value
      })
      
      // 重新绘制图表
      drawChart()
    }

    // 生命周期
    onMounted(() => {
      refreshData()
    })
    
    onUnmounted(() => {
      // 清理所有定时器
      if (exportTimer.value) {
        clearTimeout(exportTimer.value)
        exportTimer.value = null
      }
      if (downloadTimer.value) {
        clearTimeout(downloadTimer.value)
        downloadTimer.value = null
      }
      if (tooltipTimer.value) {
        clearTimeout(tooltipTimer.value)
        tooltipTimer.value = null
      }
    })

    return {
      activeTimeTab,
      activeChart,
      customStartDate,
      customEndDate,
      consumptionChartType,
      deviceCompareType,
      reportType,
      currentPage,
      selectedExportFormat,
      exportFileName,
      totalConsumption,
      consumptionTrend,
      totalCost,
      costTrend,
      averagePower,
      powerTrend,
      energySaving,
      energySavingAmount,
      baseCost,
      peakValleyCost,
      otherCost,
      peakPeriodConsumption,
      normalPeriodConsumption,
      valleyPeriodConsumption,
      chartTooltip,
      selectedDeviceTypes,
      selectedLocations,
      consumptionRange,
      costRange,
      selectedExportContents,
      timeTabs,
      chartTabs,
      chartTypes,
      deviceCompareTypes,
      reportTypes,
      deviceTypes,
      locations,
      exportFormats,
      exportContents,
      deviceRanking,
      tooltipStyle,
      totalPages,
      getPeriodText,
      getTrendClass,
      getTrendIcon,
      switchTimeTab,
      switchChart,
      updateStartDate,
      updateEndDate,
      updateConsumptionChartType,
      updateDeviceCompareType,
      updateReportType,
      getChartTypeLabel,
      getDeviceCompareTypeLabel,
      getReportTypeLabel,
      getRankClass,
      getDeviceValue,
      getDeviceUnit,
      getTableColumns,
      getTableData,
      formatCellValue,
      prevPage,
      nextPage,
      showFilter,
      closeFilter,
      toggleDeviceType,
      toggleLocation,
      resetFilter,
      applyFilter,
      exportData,
      closeExport,
      selectExportFormat,
      toggleExportContent,
      confirmExport,
      downloadReport,
      onChartTouch
    }
  }
}
</script>

<style scoped>
.statistics-page {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 15rpx;
}

.btn-export,
.btn-filter {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background-color: white;
  border: 1rpx solid #d9d9d9;
  border-radius: 6rpx;
  font-size: 26rpx;
  color: #666;
}

.btn-export {
  background-color: #52c41a;
  color: white;
  border-color: #52c41a;
}

.icon {
  font-size: 24rpx;
}

/* 时间选择器 */
.time-selector {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.time-tabs {
  display: flex;
  gap: 10rpx;
  margin-bottom: 20rpx;
}

.time-tab {
  padding: 12rpx 24rpx;
  background-color: #f0f0f0;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  cursor: pointer;
  transition: all 0.3s;
}

.time-tab.active {
  background-color: #1890ff;
  color: white;
}

.custom-time {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.date-picker {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  padding: 15rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #dee2e6;
}

.date-label {
  font-size: 22rpx;
  color: #666;
}

.date-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.date-separator {
  font-size: 24rpx;
  color: #666;
}

/* 统计概览 */
.overview-section {
  margin-bottom: 30rpx;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.overview-card {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 28rpx;
  color: #666;
}

.card-period {
  font-size: 22rpx;
  color: #999;
}

.card-content {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
}

.card-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.card-unit {
  font-size: 24rpx;
  color: #666;
}

.card-trend {
  display: flex;
  align-items: center;
  gap: 4rpx;
  font-size: 22rpx;
  margin-left: auto;
}

.trend-up {
  color: #f5222d;
}

.trend-down {
  color: #52c41a;
}

.trend-stable {
  color: #666;
}

.positive {
  color: #52c41a;
}

.trend-icon {
  font-size: 20rpx;
}

/* 图表区域 */
.chart-section {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.chart-tabs {
  display: flex;
  gap: 10rpx;
  margin-bottom: 30rpx;
  overflow-x: auto;
}

.chart-tab {
  padding: 12rpx 20rpx;
  background-color: #f0f0f0;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666;
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.3s;
}

.chart-tab.active {
  background-color: #1890ff;
  color: white;
}

.chart-container {
  min-height: 400rpx;
}

.chart-wrapper {
  width: 100%;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.chart-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.chart-options {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.chart-type-selector {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 15rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #dee2e6;
  border-radius: 6rpx;
  font-size: 24rpx;
  color: #495057;
}

.selector-arrow {
  font-size: 20rpx;
  color: #999;
}

.chart-canvas-wrapper {
  position: relative;
  height: 350rpx;
  background-color: #fafafa;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.chart-canvas {
  width: 100%;
  height: 100%;
}

.chart-tooltip {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10rpx 15rpx;
  border-radius: 6rpx;
  font-size: 22rpx;
  pointer-events: none;
  z-index: 10;
}

.tooltip-date {
  display: block;
  margin-bottom: 5rpx;
}

.tooltip-value {
  font-weight: bold;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 30rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.legend-color {
  width: 20rpx;
  height: 20rpx;
  border-radius: 2rpx;
}

.legend-text {
  font-size: 24rpx;
  color: #666;
}

/* 费用分解 */
.cost-breakdown {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 12rpx;
  background-color: #f8f9fa;
  border-radius: 6rpx;
}

.breakdown-label {
  font-size: 22rpx;
  color: #666;
}

.breakdown-value {
  font-size: 24rpx;
  font-weight: bold;
  color: #1890ff;
}

/* 时段统计 */
.period-stats {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.period-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 8rpx 12rpx;
  background-color: #f8f9fa;
  border-radius: 6rpx;
}

.period-label {
  font-size: 22rpx;
  color: #666;
  min-width: 60rpx;
}

.period-value {
  font-size: 24rpx;
  font-weight: bold;
  color: #1890ff;
}

.period-percent {
  font-size: 20rpx;
  color: #999;
}

/* 设备排行 */
.device-ranking {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #e9ecef;
}

.ranking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.ranking-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

.ranking-subtitle {
  font-size: 22rpx;
  color: #666;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 15rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.ranking-number {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  font-weight: bold;
  color: white;
  background-color: #d9d9d9;
}

.rank-first {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #333;
}

.rank-second {
  background: linear-gradient(135deg, #c0c0c0 0%, #e8e8e8 100%);
  color: #333;
}

.rank-third {
  background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
  color: white;
}

.device-info {
  flex: 1;
}

.device-name {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}

.device-location {
  font-size: 20rpx;
  color: #666;
}

.device-value {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
}

.value-number {
  font-size: 24rpx;
  font-weight: bold;
  color: #1890ff;
}

.value-unit {
  font-size: 20rpx;
  color: #666;
}

/* 报表区域 */
.report-section {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.report-actions {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.report-type-selector {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 15rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #dee2e6;
  border-radius: 6rpx;
  font-size: 24rpx;
  color: #495057;
}

.btn-download {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 6rpx;
  font-size: 24rpx;
}

/* 表格样式 */
.report-table {
  border: 1rpx solid #e9ecef;
  border-radius: 8rpx;
  overflow: hidden;
}

.table-header {
  display: flex;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #e9ecef;
}

.table-body {
  max-height: 600rpx;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}

.table-row:last-child {
  border-bottom: none;
}

.row-even {
  background-color: #fafafa;
}

.table-cell {
  padding: 20rpx 15rpx;
  font-size: 24rpx;
  color: #333;
  display: flex;
  align-items: center;
  border-right: 1rpx solid #f0f0f0;
}

.table-cell:last-child {
  border-right: none;
}

.header-cell {
  font-weight: bold;
  color: #666;
  background-color: #f8f9fa;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30rpx;
  padding: 20rpx 0;
}

.page-btn {
  padding: 12rpx 24rpx;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 6rpx;
  font-size: 24rpx;
}

.page-btn:disabled {
  background-color: #d9d9d9;
  color: #999;
}

.page-info {
  font-size: 24rpx;
  color: #666;
}

/* 弹窗样式 */
.filter-modal,
.export-modal {
  width: 600rpx;
  background-color: white;
  border-radius: 12rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #e9ecef;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.btn-close {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
  border: none;
  font-size: 24rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  padding: 30rpx;
  max-height: 800rpx;
  overflow-y: auto;
}

.filter-section,
.export-section {
  margin-bottom: 30rpx;
}

.filter-title,
.export-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.filter-options,
.export-contents {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.filter-option,
.content-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12rpx 20rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #dee2e6;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s;
}

.filter-option.active,
.content-option.active {
  background-color: #1890ff;
  color: white;
  border-color: #1890ff;
}

.option-text,
.content-text {
  margin-right: 8rpx;
}

.option-check,
.content-check {
  font-size: 20rpx;
  font-weight: bold;
}

.range-inputs {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.range-input {
  flex: 1;
  padding: 12rpx 15rpx;
  border: 1rpx solid #dee2e6;
  border-radius: 6rpx;
  font-size: 24rpx;
  background-color: white;
}

.range-separator {
  font-size: 24rpx;
  color: #666;
}

.range-unit {
  font-size: 22rpx;
  color: #666;
}

.export-formats {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.format-option {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #dee2e6;
  border-radius: 8rpx;
  cursor: pointer;
  transition: all 0.3s;
}

.format-option.active {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

.format-icon {
  font-size: 32rpx;
}

.format-info {
  flex: 1;
}

.format-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}

.format-desc {
  font-size: 22rpx;
  color: #666;
}

.export-filename {
  width: 100%;
  padding: 15rpx;
  border: 1rpx solid #dee2e6;
  border-radius: 6rpx;
  font-size: 24rpx;
  background-color: white;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15rpx;
  padding: 30rpx;
  background-color: #f8f9fa;
  border-top: 1rpx solid #e9ecef;
}

.btn-reset,
.btn-cancel {
  padding: 12rpx 30rpx;
  background-color: white;
  color: #666;
  border: 1rpx solid #d9d9d9;
  border-radius: 6rpx;
  font-size: 24rpx;
}

.btn-apply,
.btn-export-confirm {
  padding: 12rpx 30rpx;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 6rpx;
  font-size: 24rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .overview-cards {
    grid-template-columns: 1fr;
  }
  
  .chart-tabs {
    flex-wrap: wrap;
  }
  
  .chart-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 10rpx;
  }
  
  .cost-breakdown,
  .period-stats {
    gap: 8rpx;
  }
  
  .breakdown-item,
  .period-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5rpx;
  }
  
  .filter-modal,
  .export-modal {
    width: 90vw;
    max-width: 500rpx;
  }
  
  .range-inputs {
    flex-direction: column;
    align-items: stretch;
  }
  
  .range-separator {
    text-align: center;
  }
}
</style>