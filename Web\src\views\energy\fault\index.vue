<template>
  <div class="system-fault fault-management mobile-fault-management responsive-table mobile-safe-area">
    <!-- 查询表单 -->
    <el-card class="box-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>故障管理</span>
        </div>
      </template>
      
      <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px" class="mobile-form">
        <el-form-item label="设备名称" prop="deviceName">
          <el-input
            v-model="queryParams.deviceName"
            placeholder="请输入设备名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
            class="mobile-input"
          />
        </el-form-item>
        <el-form-item label="故障类型" prop="faultType">
          <el-select v-model="queryParams.faultType" placeholder="请选择故障类型" clearable style="width: 150px">
            <el-option label="硬件故障" value="hardware" />
            <el-option label="软件故障" value="software" />
            <el-option label="网络故障" value="network" />
            <el-option label="电源故障" value="power" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="处理状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择处理状态" clearable style="width: 150px">
            <el-option label="待处理" value="1" />
            <el-option label="处理中" value="2" />
            <el-option label="已解决" value="3" />
            <el-option label="已关闭" value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="故障时间" prop="dateRange">
          <el-date-picker
            v-model="queryParams.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value pending">{{ pendingCount }}</div>
            <div class="stat-label">待处理</div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value processing">{{ processingCount }}</div>
            <div class="stat-label">处理中</div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value resolved">{{ resolvedCount }}</div>
            <div class="stat-label">已解决</div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value closed">{{ closedCount }}</div>
            <div class="stat-label">已关闭</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 故障记录表格 -->
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>故障记录</span>
          <el-button type="primary" icon="Plus" @click="handleAdd">新增故障</el-button>
          <el-button type="success" @click="openEnhancedFeatures" :icon="TrendCharts">增强功能</el-button>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="faultList"
        row-key="id"
        border
        stripe
        style="width: 100%"
      >
        <el-table-column prop="deviceName" label="设备名称" min-width="120" show-overflow-tooltip />
        <el-table-column prop="deviceCode" label="设备编号" min-width="120" show-overflow-tooltip />
        <el-table-column prop="faultType" label="故障类型" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getFaultTypeColor(row.faultType)">{{ getFaultTypeLabel(row.faultType) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="faultDescription" label="故障描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="severity" label="严重程度" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getSeverityColor(row.severity)">{{ getSeverityLabel(row.severity) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="处理状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)">{{ getStatusLabel(row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reportTime" label="故障时间" width="180" align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.reportTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="handler" label="处理人" width="100" align="center" show-overflow-tooltip />
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" size="small" @click="viewDetail(row)">
              详情
            </el-button>
            <el-button link type="primary" size="small" @click="handleProcess(row)" v-if="row.status !== '3' && row.status !== '4'">
              处理
            </el-button>
            <el-button link type="success" size="small" @click="handleResolve(row)" v-if="row.status === '2'">
              解决
            </el-button>
            <el-button link type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 新增/编辑故障弹窗 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="700px" destroy-on-close>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备名称" prop="deviceId">
              <el-select v-model="form.deviceId" placeholder="请选择设备" filterable style="width: 100%">
                <el-option
                  v-for="device in deviceList"
                  :key="device.id"
                  :label="device.deviceName"
                  :value="device.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="故障类型" prop="faultType">
              <el-select v-model="form.faultType" placeholder="请选择故障类型" style="width: 100%">
                <el-option label="硬件故障" value="hardware" />
                <el-option label="软件故障" value="software" />
                <el-option label="网络故障" value="network" />
                <el-option label="电源故障" value="power" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="严重程度" prop="severity">
              <el-select v-model="form.severity" placeholder="请选择严重程度" style="width: 100%">
                <el-option label="低" value="1" />
                <el-option label="中" value="2" />
                <el-option label="高" value="3" />
                <el-option label="紧急" value="4" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="故障时间" prop="reportTime">
              <el-date-picker
                v-model="form.reportTime"
                type="datetime"
                placeholder="请选择故障时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="故障描述" prop="faultDescription">
          <el-input v-model="form.faultDescription" type="textarea" :rows="3" placeholder="请输入故障描述" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="2" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 处理故障弹窗 -->
    <el-dialog v-model="processVisible" title="处理故障" width="600px" destroy-on-close>
      <el-form ref="processFormRef" :model="processForm" :rules="processRules" label-width="100px">
        <el-form-item label="处理状态" prop="status">
          <el-radio-group v-model="processForm.status">
            <el-radio label="2">处理中</el-radio>
            <el-radio label="3">已解决</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="处理方案" prop="solution">
          <el-input v-model="processForm.solution" type="textarea" :rows="4" placeholder="请输入处理方案" />
        </el-form-item>
        <el-form-item label="处理时间" prop="processTime">
          <el-date-picker
            v-model="processForm.processTime"
            type="datetime"
            placeholder="请选择处理时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="processVisible = false">取消</el-button>
          <el-button type="primary" @click="handleProcessSubmit" :loading="processLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 详情弹窗 -->
    <el-dialog v-model="detailVisible" title="故障详情" width="700px">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="设备名称">{{ detailData.deviceName }}</el-descriptions-item>
        <el-descriptions-item label="设备编号">{{ detailData.deviceCode }}</el-descriptions-item>
        <el-descriptions-item label="故障类型">
          <el-tag :type="getFaultTypeColor(detailData.faultType)">{{ getFaultTypeLabel(detailData.faultType) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="严重程度">
          <el-tag :type="getSeverityColor(detailData.severity)">{{ getSeverityLabel(detailData.severity) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="处理状态">
          <el-tag :type="getStatusColor(detailData.status)">{{ getStatusLabel(detailData.status) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="故障时间">{{ formatDateTime(detailData.reportTime) }}</el-descriptions-item>
        <el-descriptions-item label="故障描述" :span="2">{{ detailData.faultDescription }}</el-descriptions-item>
        <el-descriptions-item label="处理方案" :span="2">{{ detailData.solution || '暂无' }}</el-descriptions-item>
        <el-descriptions-item label="处理人">{{ detailData.handler || '暂无' }}</el-descriptions-item>
        <el-descriptions-item label="处理时间">{{ detailData.processTime ? formatDateTime(detailData.processTime) : '暂无' }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ detailData.remark || '无' }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 增强功能组件 -->
    <FaultEnhanced
      ref="faultEnhancedRef"
      v-model:visible="enhancedVisible"
      @refresh="handleQuery"
    />
  </div>
</template>

<script setup lang="ts" name="fault">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { TrendCharts } from '@element-plus/icons-vue'
import { useFaultApi } from '/@/api-services/fault';
import { useDeviceApi } from '/@/api-services/device';
import { formatDateTime } from '@/utils/formatTime'
import FaultEnhanced from './faultEnhanced.vue'

const loading = ref(false)
const total = ref(0)
const faultList = ref([])
const deviceList = ref([])
const dialogVisible = ref(false)
const processVisible = ref(false)
const detailVisible = ref(false)
const dialogTitle = ref('')
const submitLoading = ref(false)
const processLoading = ref(false)
const detailData = ref({})
const enhancedVisible = ref(false)
const faultEnhancedRef = ref()

// 统计数据
const pendingCount = ref(0)
const processingCount = ref(0)
const resolvedCount = ref(0)
const closedCount = ref(0)

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  deviceName: '',
  faultType: '',
  status: '',
  dateRange: []
})

const form = reactive({
  id: undefined,
  deviceId: '',
  faultType: '',
  severity: '',
  faultDescription: '',
  reportTime: '',
  remark: ''
})

const processForm = reactive({
  id: '',
  status: '2',
  solution: '',
  processTime: ''
})

const rules = reactive({
  deviceId: [{ required: true, message: '请选择设备', trigger: 'change' }],
  faultType: [{ required: true, message: '请选择故障类型', trigger: 'change' }],
  severity: [{ required: true, message: '请选择严重程度', trigger: 'change' }],
  faultDescription: [{ required: true, message: '请输入故障描述', trigger: 'blur' }],
  reportTime: [{ required: true, message: '请选择故障时间', trigger: 'change' }]
})

const processRules = reactive({
  status: [{ required: true, message: '请选择处理状态', trigger: 'change' }],
  solution: [{ required: true, message: '请输入处理方案', trigger: 'blur' }],
  processTime: [{ required: true, message: '请选择处理时间', trigger: 'change' }]
})

// 获取列表
const getList = async () => {
  loading.value = true
  try {
    const params = {
      ...queryParams,
      startTime: queryParams.dateRange?.[0],
      endTime: queryParams.dateRange?.[1]
    }
    delete params.dateRange
    
    const { data } = await EnergyFaultApi.apiEnergyFaultPagePost(params)
    faultList.value = data.items || []
    total.value = data.total || 0
  } catch (error) {
    console.error('获取故障记录失败:', error)
    ElMessage.error('获取故障记录失败')
  } finally {
    loading.value = false
  }
}

// 获取设备列表
const getDeviceList = async () => {
  try {
    const deviceApi = useDeviceApi();
		const { data } = await deviceApi.getList();
    deviceList.value = data || []
  } catch (error) {
    console.error('获取设备列表失败:', error)
  }
}

// 获取统计数据
const getStatistics = async () => {
  try {
    const { data } = await EnergyFaultApi.apiEnergyFaultStatisticsGet()
    pendingCount.value = data.pendingCount || 0
    processingCount.value = data.processingCount || 0
    resolvedCount.value = data.resolvedCount || 0
    closedCount.value = data.closedCount || 0
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 获取故障类型标签
const getFaultTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    'hardware': '硬件故障',
    'software': '软件故障',
    'network': '网络故障',
    'power': '电源故障',
    'other': '其他'
  }
  return typeMap[type] || type
}

// 获取故障类型颜色
const getFaultTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'hardware': 'danger',
    'software': 'warning',
    'network': 'info',
    'power': 'danger',
    'other': ''
  }
  return colorMap[type] || ''
}

// 获取严重程度标签
const getSeverityLabel = (severity: string) => {
  const severityMap: Record<string, string> = {
    '1': '低',
    '2': '中',
    '3': '高',
    '4': '紧急'
  }
  return severityMap[severity] || severity
}

// 获取严重程度颜色
const getSeverityColor = (severity: string) => {
  const colorMap: Record<string, string> = {
    '1': 'success',
    '2': 'warning',
    '3': 'danger',
    '4': 'danger'
  }
  return colorMap[severity] || ''
}

// 获取状态标签
const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    '1': '待处理',
    '2': '处理中',
    '3': '已解决',
    '4': '已关闭'
  }
  return statusMap[status] || status
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    '1': 'danger',
    '2': 'warning',
    '3': 'success',
    '4': 'info'
  }
  return colorMap[status] || ''
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置
const resetQuery = () => {
  Object.assign(queryParams, {
    deviceName: '',
    faultType: '',
    status: '',
    dateRange: [],
    pageNum: 1
  })
  getList()
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增故障'
  dialogVisible.value = true
  Object.assign(form, {
    id: undefined,
    deviceId: '',
    faultType: '',
    severity: '',
    faultDescription: '',
    reportTime: '',
    remark: ''
  })
}

// 处理故障
const handleProcess = (row: any) => {
  processVisible.value = true
  Object.assign(processForm, {
    id: row.id,
    status: '2',
    solution: '',
    processTime: ''
  })
}

// 解决故障
const handleResolve = (row: any) => {
  processVisible.value = true
  Object.assign(processForm, {
    id: row.id,
    status: '3',
    solution: '',
    processTime: ''
  })
}

// 删除
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这条故障记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await EnergyFaultApi.apiEnergyFaultDeletePost({ id: row.id })
    ElMessage.success('删除成功')
    getList()
    getStatistics()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 查看详情
const viewDetail = (row: any) => {
  detailData.value = { ...row }
  detailVisible.value = true
}

// 提交表单
const handleSubmit = async () => {
  // 表单提交逻辑
  try {
    submitLoading.value = true
    
    if (form.id) {
      await EnergyFaultApi.apiEnergyFaultUpdatePost(form)
      ElMessage.success('编辑成功')
    } else {
      await EnergyFaultApi.apiEnergyFaultAddPost(form)
      ElMessage.success('新增成功')
    }
    
    dialogVisible.value = false
    getList()
    getStatistics()
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 提交处理表单
const handleProcessSubmit = async () => {
  try {
    processLoading.value = true
    
    await EnergyFaultApi.apiEnergyFaultProcessPost(processForm)
    ElMessage.success('处理成功')
    
    processVisible.value = false
    getList()
    getStatistics()
  } catch (error) {
    console.error('处理失败:', error)
    ElMessage.error('处理失败')
  } finally {
    processLoading.value = false
  }
}

// 打开增强功能
const openEnhancedFeatures = () => {
  enhancedVisible.value = true
}

onMounted(() => {
  getList()
  getDeviceList()
  getStatistics()
})
</script>

<style scoped lang="scss">
.system-fault {
  .box-card {
    margin-bottom: 20px;
  }
  
  .stats-cards {
    margin-bottom: 20px;
    
    .stat-card {
      .stat-item {
        text-align: center;
        
        .stat-value {
          font-size: 28px;
          font-weight: bold;
          margin-bottom: 8px;
          
          &.pending {
            color: #F56C6C;
          }
          
          &.processing {
            color: #E6A23C;
          }
          
          &.resolved {
            color: #67C23A;
          }
          
          &.closed {
            color: #909399;
          }
        }
        
        .stat-label {
          font-size: 14px;
          color: #909399;
        }
      }
    }
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .dialog-footer {
    text-align: right;
  }
}
</style>