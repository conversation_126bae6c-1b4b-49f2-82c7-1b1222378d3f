/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { EnergyDeviceOutput } from './energy-device-output';
import { EnergySceneOutput } from './energy-scene-output';
import { SceneDeviceExecuteHistoryItem } from './scene-device-execute-history-item';
import { SceneDeviceExecuteRecord } from './scene-device-execute-record';
import { SceneDeviceStatInfo } from './scene-device-stat-info';
/**
 * 场景设备详情输出参数
 * @export
 * @interface EnergySceneDeviceDetailOutput
 */
export interface EnergySceneDeviceDetailOutput {
    /**
     * 主键ID
     * @type {number}
     * @memberof EnergySceneDeviceDetailOutput
     */
    id?: number;
    /**
     * 场景ID
     * @type {number}
     * @memberof EnergySceneDeviceDetailOutput
     */
    sceneId?: number;
    /**
     * 场景名称
     * @type {string}
     * @memberof EnergySceneDeviceDetailOutput
     */
    sceneName?: string | null;
    /**
     * 设备ID
     * @type {number}
     * @memberof EnergySceneDeviceDetailOutput
     */
    deviceId?: number;
    /**
     * 设备编码
     * @type {string}
     * @memberof EnergySceneDeviceDetailOutput
     */
    deviceCode?: string | null;
    /**
     * 设备名称
     * @type {string}
     * @memberof EnergySceneDeviceDetailOutput
     */
    deviceName?: string | null;
    /**
     * 控制类型
     * @type {string}
     * @memberof EnergySceneDeviceDetailOutput
     */
    controlType?: string | null;
    /**
     * 控制命令
     * @type {string}
     * @memberof EnergySceneDeviceDetailOutput
     */
    controlCommand?: string | null;
    /**
     * 控制参数
     * @type {string}
     * @memberof EnergySceneDeviceDetailOutput
     */
    controlParams?: string | null;
    /**
     * 延迟时间(秒)
     * @type {number}
     * @memberof EnergySceneDeviceDetailOutput
     */
    delayTime?: number;
    /**
     * 延迟时间(秒) - 兼容字段
     * @type {number}
     * @memberof EnergySceneDeviceDetailOutput
     */
    delaySeconds?: number;
    /**
     * 执行次数
     * @type {number}
     * @memberof EnergySceneDeviceDetailOutput
     */
    executeCount?: number;
    /**
     * 成功次数
     * @type {number}
     * @memberof EnergySceneDeviceDetailOutput
     */
    successCount?: number;
    /**
     * 成功率
     * @type {number}
     * @memberof EnergySceneDeviceDetailOutput
     */
    successRate?: number;
    /**
     * 最后执行时间
     * @type {Date}
     * @memberof EnergySceneDeviceDetailOutput
     */
    lastExecuteTime?: Date | null;
    /**
     * 平均执行时长(毫秒)
     * @type {number}
     * @memberof EnergySceneDeviceDetailOutput
     */
    avgExecuteTime?: number | null;
    /**
     * 平均执行持续时间(毫秒)
     * @type {number}
     * @memberof EnergySceneDeviceDetailOutput
     */
    avgExecuteDuration?: number | null;
    /**
     * 排序
     * @type {number}
     * @memberof EnergySceneDeviceDetailOutput
     */
    sort?: number;
    /**
     * 状态
     * @type {number}
     * @memberof EnergySceneDeviceDetailOutput
     */
    status?: number;
    /**
     * 状态名称
     * @type {string}
     * @memberof EnergySceneDeviceDetailOutput
     */
    statusName?: string | null;
    /**
     * 租户ID
     * @type {number}
     * @memberof EnergySceneDeviceDetailOutput
     */
    tenantId?: number | null;
    /**
     * 创建时间
     * @type {Date}
     * @memberof EnergySceneDeviceDetailOutput
     */
    createTime?: Date;
    /**
     * 更新时间
     * @type {Date}
     * @memberof EnergySceneDeviceDetailOutput
     */
    updateTime?: Date | null;
    /**
     * 
     * @type {EnergySceneOutput}
     * @memberof EnergySceneDeviceDetailOutput
     */
    scene?: EnergySceneOutput;
    /**
     * 
     * @type {EnergyDeviceOutput}
     * @memberof EnergySceneDeviceDetailOutput
     */
    device?: EnergyDeviceOutput;
    /**
     * 设备位置
     * @type {string}
     * @memberof EnergySceneDeviceDetailOutput
     */
    deviceLocation?: string | null;
    /**
     * 设备类型
     * @type {string}
     * @memberof EnergySceneDeviceDetailOutput
     */
    deviceType?: string | null;
    /**
     * 设备型号
     * @type {string}
     * @memberof EnergySceneDeviceDetailOutput
     */
    deviceModel?: string | null;
    /**
     * 执行历史记录
     * @type {Array<SceneDeviceExecuteHistoryItem>}
     * @memberof EnergySceneDeviceDetailOutput
     */
    executeHistory?: Array<SceneDeviceExecuteHistoryItem> | null;
    /**
     * 最近执行记录
     * @type {Array<SceneDeviceExecuteRecord>}
     * @memberof EnergySceneDeviceDetailOutput
     */
    recentExecuteRecords?: Array<SceneDeviceExecuteRecord> | null;
    /**
     * 
     * @type {SceneDeviceStatInfo}
     * @memberof EnergySceneDeviceDetailOutput
     */
    statInfo?: SceneDeviceStatInfo;
    /**
     * 今日执行次数
     * @type {number}
     * @memberof EnergySceneDeviceDetailOutput
     */
    todayExecuteCount?: number;
    /**
     * 本月执行次数
     * @type {number}
     * @memberof EnergySceneDeviceDetailOutput
     */
    thisMonthExecuteCount?: number;
    /**
     * 今日成功次数
     * @type {number}
     * @memberof EnergySceneDeviceDetailOutput
     */
    todaySuccessCount?: number;
    /**
     * 本月成功次数
     * @type {number}
     * @memberof EnergySceneDeviceDetailOutput
     */
    thisMonthSuccessCount?: number;
}
