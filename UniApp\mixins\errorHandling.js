/**
 * 错误处理混入
 * 为页面组件提供统一的错误处理功能
 */

import { handleError, handleCriticalError } from '../utils/errorHandler.js'
import { Loading } from '@/utils/loadingManager'

export default {
  data() {
    return {
      // 错误状态
      hasError: false,
      errorMessage: '',
      errorRetryCount: 0,
      maxRetryCount: 3,
      
      // 加载状态
      isLoading: false,
      loadingText: '加载中...',
      currentLoadingId: null,
      
      // 网络状态
      isOnline: true,
      networkType: 'unknown'
    }
  },
  
  onLoad() {
    // 监听网络状态变化
    this.initNetworkListener()
    // 检查初始网络状态
    this.checkNetworkStatus()
  },
  
  methods: {
    /**
     * 统一错误处理方法
     * @param {Error} error - 错误对象
     * @param {Object} context - 错误上下文
     * @param {Object} options - 处理选项
     */
    handleError(error, context = {}, options = {}) {
      const pageContext = {
        page: this.$options.name || 'Unknown',
        route: getCurrentPages().pop()?.route,
        timestamp: new Date().toISOString(),
        userAgent: uni.getSystemInfoSync(),
        ...context
      }
      
      // 更新错误状态
      this.hasError = true
      this.errorMessage = error.message || '未知错误'
      
      // 调用全局错误处理
      return handleError(error, pageContext, {
        showToast: true,
        logError: true,
        reportError: true,
        ...options
      })
    },
    
    /**
     * 处理关键错误
     * @param {Error} error - 错误对象
     * @param {Object} context - 错误上下文
     */
    handleCriticalError(error, context = {}) {
      const pageContext = {
        page: this.$options.name || 'Unknown',
        route: getCurrentPages().pop()?.route,
        critical: true,
        ...context
      }
      
      this.hasError = true
      this.errorMessage = error.message || '系统错误'
      
      return handleCriticalError(error, pageContext)
    },
    
    /**
     * 处理网络错误
     * @param {Error} error - 网络错误
     * @param {Object} context - 错误上下文
     */
    handleNetworkError(error, context = {}) {
      return this.handleError(error, {
        type: 'network',
        networkType: this.networkType,
        isOnline: this.isOnline,
        ...context
      }, {
        retryable: true,
        showToast: true,
        toastMessage: '网络连接异常，请检查网络设置'
      })
    },
    
    /**
     * 处理API错误
     * @param {Error} error - API错误
     * @param {string} apiName - API名称
     * @param {Object} context - 错误上下文
     */
    handleApiError(error, apiName = '', context = {}) {
      const errorContext = {
        type: 'api',
        api: apiName,
        statusCode: error.statusCode,
        responseData: error.data,
        ...context
      }
      
      // 根据状态码处理不同类型的错误
      if (error.statusCode === 401) {
        return this.handleAuthError(error, errorContext)
      } else if (error.statusCode === 403) {
        return this.handlePermissionError(error, errorContext)
      } else if (error.statusCode >= 500) {
        return this.handleServerError(error, errorContext)
      } else {
        return this.handleError(error, errorContext)
      }
    },
    
    /**
     * 处理认证错误
     * @param {Error} error - 认证错误
     * @param {Object} context - 错误上下文
     */
    handleAuthError(error, context = {}) {
      return this.handleError(error, {
        type: 'auth',
        ...context
      }, {
        showToast: true,
        toastMessage: '登录已过期，请重新登录',
        reportError: false,
        callback: () => {
          // 清除用户信息并跳转到登录页
          this.$store.dispatch('user/logout')
          uni.reLaunch({
            url: '/pages/login/login'
          })
        }
      })
    },
    
    /**
     * 处理权限错误
     * @param {Error} error - 权限错误
     * @param {Object} context - 错误上下文
     */
    handlePermissionError(error, context = {}) {
      return this.handleError(error, {
        type: 'permission',
        ...context
      }, {
        showToast: true,
        toastMessage: '权限不足，无法执行此操作',
        reportError: false
      })
    },
    
    /**
     * 处理服务器错误
     * @param {Error} error - 服务器错误
     * @param {Object} context - 错误上下文
     */
    handleServerError(error, context = {}) {
      return this.handleError(error, {
        type: 'server',
        ...context
      }, {
        showToast: true,
        toastMessage: '服务器异常，请稍后重试',
        retryable: true
      })
    },
    
    /**
     * 处理业务错误
     * @param {Error} error - 业务错误
     * @param {Object} context - 错误上下文
     */
    handleBusinessError(error, context = {}) {
      return this.handleError(error, {
        type: 'business',
        ...context
      }, {
        showToast: true,
        reportError: false
      })
    },
    
    /**
     * 处理验证错误
     * @param {Error} error - 验证错误
     * @param {Object} context - 错误上下文
     */
    handleValidationError(error, context = {}) {
      return this.handleError(error, {
        type: 'validation',
        ...context
      }, {
        showToast: true,
        toastMessage: error.message || '数据验证失败',
        reportError: false
      })
    },
    
    /**
     * 重试操作
     * @param {Function} operation - 要重试的操作
     * @param {Object} options - 重试选项
     */
    async retryOperation(operation, options = {}) {
      const {
        maxRetries = this.maxRetryCount,
        delay = 1000,
        backoff = true
      } = options
      
      let lastError
      
      for (let i = 0; i <= maxRetries; i++) {
        try {
          const result = await operation()
          // 重试成功，重置错误状态
          this.hasError = false
          this.errorMessage = ''
          this.errorRetryCount = 0
          return result
        } catch (error) {
          lastError = error
          this.errorRetryCount = i + 1
          
          if (i < maxRetries) {
            // 计算延迟时间（指数退避）
            const retryDelay = backoff ? delay * Math.pow(2, i) : delay
            await new Promise(resolve => setTimeout(resolve, retryDelay))
          }
        }
      }
      
      // 所有重试都失败了
      throw lastError
    },
    
    /**
     * 清除错误状态
     */
    clearError() {
      this.hasError = false
      this.errorMessage = ''
      this.errorRetryCount = 0
    },
    
    /**
     * 显示加载状态
     * @param {string|Object} options - 加载选项或文本
     */
    showLoading(options = {}) {
      // 支持字符串参数向后兼容
      if (typeof options === 'string') {
        options = { text: options }
      }
      
      this.isLoading = true
      this.loadingText = options.text || '加载中...'
      this.currentLoadingId = Loading.show({
        text: '加载中...',
        mask: true,
        ...options
      })
      
      return this.currentLoadingId
    },
    
    /**
     * 隐藏加载状态
     * @param {string} loadingId - 加载ID
     */
    hideLoading(loadingId) {
      this.isLoading = false
      
      if (loadingId) {
        Loading.hide(loadingId)
      } else if (this.currentLoadingId) {
        Loading.hide(this.currentLoadingId)
        this.currentLoadingId = null
      } else {
        Loading.hideGlobal()
      }
    },
    
    /**
     * 显示骨架屏
     * @param {Object} options - 骨架屏选项
     */
    showSkeleton(options = {}) {
      this.isLoading = true
      this.currentLoadingId = Loading.showSkeleton(options)
      return this.currentLoadingId
    },
    
    /**
     * 显示进度加载
     * @param {Object} options - 进度选项
     */
    showProgress(options = {}) {
      this.isLoading = true
      this.currentLoadingId = Loading.showProgress(options)
      return this.currentLoadingId
    },
    
    /**
     * 更新进度
     * @param {number} progress - 进度值
     * @param {string} loadingId - 加载ID
     */
    updateProgress(progress, loadingId) {
      const id = loadingId || this.currentLoadingId
      if (id) {
        Loading.updateProgress(id, progress)
      }
    },
    
    /**
     * 初始化网络监听器
     */
    initNetworkListener() {
      // 监听网络状态变化
      uni.onNetworkStatusChange((res) => {
        this.isOnline = res.isConnected
        this.networkType = res.networkType
        
        if (!res.isConnected) {
          this.handleNetworkError(new Error('网络连接已断开'), {
            networkType: res.networkType
          })
        }
      })
    },
    
    /**
     * 清理网络监听器
     */
    cleanupNetworkListener() {
      // uni-app 会自动清理监听器，这里可以做一些额外的清理工作
    },
    
    /**
     * 检查网络状态
     */
    async checkNetworkStatus() {
      try {
        const res = await new Promise((resolve, reject) => {
          uni.getNetworkType({
            success: resolve,
            fail: reject
          })
        })
        
        this.isOnline = res.networkType !== 'none'
        this.networkType = res.networkType
      } catch (error) {
        console.warn('获取网络状态失败:', error)
        this.isOnline = false
        this.networkType = 'unknown'
      }
    },
    
    /**
     * 安全执行异步操作
     * @param {Function} operation - 异步操作
     * @param {Object} options - 选项
     */
    async safeExecute(operation, options = {}) {
      const {
        showLoading = false,
        loadingText = '处理中...',
        retryable = false,
        onError = null
      } = options
      
      try {
        if (showLoading) {
          this.showLoading(loadingText)
        }
        
        const result = retryable 
          ? await this.retryOperation(operation)
          : await operation()
          
        return result
      } catch (error) {
        if (onError) {
          onError(error)
        } else {
          this.handleError(error)
        }
        throw error
      } finally {
        if (showLoading) {
          this.hideLoading()
        }
      }
    }
  }
}