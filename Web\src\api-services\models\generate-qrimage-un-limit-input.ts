/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 生成带参数小程序二维码(获取不受限制的小程序码)
 * @export
 * @interface GenerateQRImageUnLimitInput
 */
export interface GenerateQRImageUnLimitInput {
    /**
     * 扫码进入的小程序页面路径，最大长度 128 个字符，不能为空； eg: pages/index?id=0001
     * @type {string}
     * @memberof GenerateQRImageUnLimitInput
     */
    pagePath?: string | null;
    /**
     * 文件保存的名称
     * @type {string}
     * @memberof GenerateQRImageUnLimitInput
     */
    imageName?: string | null;
    /**
     * 图片宽度 默认430
     * @type {number}
     * @memberof GenerateQRImageUnLimitInput
     */
    width?: number;
    /**
     * 二维码携带的参数 eg:a=1（最大32个可见字符，只支持数字，大小写英文以及部分特殊字符：<!-- !#$&'()*+,/:;=?@-._~ -->）
     * @type {string}
     * @memberof GenerateQRImageUnLimitInput
     */
    scene?: string | null;
}
