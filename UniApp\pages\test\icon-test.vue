<template>
  <view class="icon-test-page">
    <view class="header">
      <text class="title">图标显示测试</text>
    </view>
    
    <view class="icon-section">
      <text class="section-title">底部导航图标</text>
      <view class="icon-grid">
        <view class="icon-item" v-for="icon in navIcons" :key="icon.name">
          <IconLoader 
            :name="icon.name" 
            :size="48" 
            @error="onIconError" 
            @load="onIconLoad"
          />
          <text class="icon-name">{{ icon.name }}</text>
          <text class="icon-status" :class="icon.status">{{ icon.status }}</text>
        </view>
      </view>
    </view>
    
    <view class="icon-section">
      <text class="section-title">设备图标</text>
      <view class="icon-grid">
        <view class="icon-item" v-for="icon in deviceIcons" :key="icon.name">
          <IconLoader 
            :name="icon.name" 
            :size="48" 
            @error="onIconError" 
            @load="onIconLoad"
          />
          <text class="icon-name">{{ icon.name }}</text>
          <text class="icon-status" :class="icon.status">{{ icon.status }}</text>
        </view>
      </view>
    </view>
    
    <view class="icon-section">
      <text class="section-title">状态图标</text>
      <view class="icon-grid">
        <view class="icon-item" v-for="icon in statusIcons" :key="icon.name">
          <IconLoader 
            :name="icon.name" 
            :size="40" 
            @error="onIconError(icon.name)" 
            @load="onIconLoad(icon.name)"
          />
          <text class="icon-name">{{ icon.name }}</text>
          <text class="icon-status" :class="{ error: iconErrors[icon.name], success: iconLoaded[icon.name] }">
            {{ iconErrors[icon.name] ? '加载失败' : (iconLoaded[icon.name] ? '加载成功' : '加载中') }}
          </text>
        </view>
      </view>
    </view>
    
    <view class="summary">
      <text class="summary-title">加载统计</text>
      <text class="summary-text">成功: {{ loadedCount }} / 失败: {{ errorCount }} / 总计: {{ totalCount }}</text>
    </view>
  </view>
</template>

<script>
import IconLoader from '@/components/IconLoader.vue'
import iconManager from '@/utils/iconManager.js'

export default {
  components: {
    IconLoader
  },
  data() {
    return {
      loadedCount: 0,
      errorCount: 0,
      iconManager,
      iconErrors: {},
      iconLoaded: {},
      navIcons: [
        { name: 'home' },
        { name: 'home-active' },
        { name: 'device' },
        { name: 'device-active' },
        { name: 'lighting' },
        { name: 'lighting-active' },
        { name: 'energy' },
        { name: 'energy-active' },
        { name: 'user' },
        { name: 'user-active' }
      ],
      deviceIcons: [
        { name: 'light-on' },
        { name: 'light-off' },
        { name: 'led-on' },
        { name: 'led-off' },
        { name: 'sensor' },
        { name: 'switch' },
        { name: 'device-default' }
      ],
      statusIcons: [
        { name: 'status-online' },
        { name: 'status-offline' },
        { name: 'status-warning' },
        { name: 'add' },
        { name: 'delete' },
        { name: 'edit' },
        { name: 'filter' },
        { name: 'settings' }
      ]
    }
  },
  computed: {
    totalCount() {
      return this.navIcons.length + this.deviceIcons.length + this.statusIcons.length
    }
  },
  methods: {
    onIconLoad(iconName) {
      this.iconLoaded[iconName] = true
      this.iconErrors[iconName] = false
      this.loadedCount++
      console.log('图标加载成功:', iconName)
    },
    onIconError(iconName) {
      this.iconErrors[iconName] = true
      this.iconLoaded[iconName] = false
      this.errorCount++
      console.error('图标加载失败:', iconName)
    }
  }
}
</script>

<style scoped>
.icon-test-page {
  padding: 20rpx;
  background-color: #f5f5f5;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.icon-section {
  margin-bottom: 40rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.icon-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 140rpx;
  padding: 20rpx;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  background-color: #fafafa;
}

.icon-image {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 10rpx;
}

.icon-name {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  margin-bottom: 5rpx;
}

.icon-status {
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  text-align: center;
}

.icon-status.loading {
  background-color: #e6f7ff;
  color: #1890ff;
}

.icon-status.loaded {
  background-color: #f6ffed;
  color: #52c41a;
}

.icon-status.error {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.summary {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  text-align: center;
}

.summary-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.summary-text {
  font-size: 26rpx;
  color: #666;
}
</style>