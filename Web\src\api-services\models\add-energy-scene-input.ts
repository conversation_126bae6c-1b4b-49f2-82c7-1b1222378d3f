/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 添加场景输入参数
 * @export
 * @interface AddEnergySceneInput
 */
export interface AddEnergySceneInput {
    /**
     * 场景名称
     * @type {string}
     * @memberof AddEnergySceneInput
     */
    sceneName: string;
    /**
     * 场景类型
     * @type {string}
     * @memberof AddEnergySceneInput
     */
    sceneType: string;
    /**
     * 场景描述
     * @type {string}
     * @memberof AddEnergySceneInput
     */
    sceneDescription?: string | null;
    /**
     * 场景图标
     * @type {string}
     * @memberof AddEnergySceneInput
     */
    sceneIcon?: string | null;
    /**
     * 执行条件
     * @type {string}
     * @memberof AddEnergySceneInput
     */
    executeCondition?: string | null;
    /**
     * 定时执行
     * @type {boolean}
     * @memberof AddEnergySceneInput
     */
    isScheduled?: boolean;
    /**
     * 定时表达式
     * @type {string}
     * @memberof AddEnergySceneInput
     */
    scheduleExpression?: string | null;
    /**
     * 排序
     * @type {number}
     * @memberof AddEnergySceneInput
     */
    sort?: number;
    /**
     * 状态
     * @type {number}
     * @memberof AddEnergySceneInput
     */
    status?: number;
    /**
     * 备注
     * @type {string}
     * @memberof AddEnergySceneInput
     */
    remark?: string | null;
}
