# 项目开发标准与文档管理规范 v2.0

## 文档信息
- **文档版本**: v2.0
- **创建日期**: 2025-01-29
- **文档类型**: 项目开发标准与文档管理规范
- **适用范围**: 节能灯管理系统全栈开发团队
- **维护团队**: 架构与规范团队

---

## 1. 项目架构标准

### 1.1 整体架构概述

#### 1.1.1 架构设计原则

节能灯管理系统采用前后端分离的微服务架构，遵循以下核心设计原则：

- **分层架构**: 清晰的分层设计，职责分离
- **模块化**: 高内聚、低耦合的模块设计
- **可扩展性**: 支持水平扩展和功能扩展
- **高可用性**: 容错设计和故障恢复机制
- **安全性**: 多层次安全防护体系
- **性能优化**: 缓存策略和性能监控

#### 1.1.2 技术架构图

```mermaid
graph TB
    subgraph "前端层 Frontend Layer"
        A[Web管理端<br/>React + TypeScript]
        B[移动端APP<br/>UniApp + Vue3]
        C[第三方集成<br/>API调用]
    end
    
    subgraph "网关层 Gateway Layer"
        D[API网关<br/>Nginx + 负载均衡]
        E[认证网关<br/>JWT验证]
    end
    
    subgraph "应用服务层 Application Layer"
        F[Admin.NET后端<br/>ASP.NET Core 8.0]
        G[MQTT插件服务<br/>MQTTnet]
        H[数据处理服务<br/>后台任务]
    end
    
    subgraph "数据层 Data Layer"
        I[MySQL主库<br/>业务数据]
        J[MySQL从库<br/>读取优化]
        K[Redis缓存<br/>会话+缓存]
        L[时序数据库<br/>监控数据]
    end
    
    subgraph "消息层 Message Layer"
        M[EMQX集群<br/>MQTT消息代理]
        N[消息队列<br/>异步处理]
    end
    
    subgraph "设备层 Device Layer"
        O[智能照明设备]
        P[网关设备]
        Q[传感器设备]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    F --> G
    F --> H
    F --> I
    F --> J
    F --> K
    G --> M
    H --> N
    M --> P
    P --> O
    P --> Q
```

### 1.2 分层架构优化

#### 1.2.1 表现层 (Presentation Layer)

**职责边界**:
- 用户界面展示和交互
- 用户输入验证和格式化
- 前端路由和状态管理
- API调用和数据绑定

**技术选型**:
- **Web端**: React 18 + TypeScript + Ant Design
- **移动端**: UniApp + Vue 3 + TypeScript + uView UI
- **状态管理**: Zustand (Web) / Pinia (UniApp)
- **HTTP客户端**: Axios with interceptors

**架构规范**:
```
src/
├── components/          # 公共组件
│   ├── common/         # 通用组件
│   ├── business/       # 业务组件
│   └── layout/         # 布局组件
├── pages/              # 页面组件
│   ├── dashboard/      # 仪表板
│   ├── devices/        # 设备管理
│   └── settings/       # 系统设置
├── services/           # API服务
├── stores/             # 状态管理
├── utils/              # 工具函数
├── hooks/              # 自定义Hook
└── types/              # TypeScript类型定义
```

#### 1.2.2 业务逻辑层 (Business Logic Layer)

**职责边界**:
- 业务规则实现和验证
- 数据转换和处理
- 事务管理和协调
- 业务流程控制

**分层设计**:
```
Admin.NET.Application/
├── Services/           # 应用服务层
│   ├── DeviceService/  # 设备管理服务
│   ├── UserService/    # 用户管理服务
│   └── EnergyService/  # 能耗管理服务
├── DTOs/               # 数据传输对象
├── Mappers/            # 对象映射
├── Validators/         # 业务验证
└── Interfaces/         # 服务接口
```

**服务层规范**:
- 每个服务类专注单一业务领域
- 使用依赖注入管理服务依赖
- 实现接口分离原则
- 统一异常处理和日志记录

#### 1.2.3 数据访问层 (Data Access Layer)

**职责边界**:
- 数据持久化操作
- 数据库连接管理
- 查询优化和缓存
- 数据一致性保证

**仓储模式实现**:
```
Admin.NET.Core/
├── Repositories/       # 仓储实现
│   ├── IRepository.cs  # 通用仓储接口
│   ├── BaseRepository.cs # 基础仓储实现
│   └── DeviceRepository.cs # 设备仓储
├── Entities/           # 实体模型
├── DbContext/          # 数据库上下文
└── Migrations/         # 数据库迁移
```

### 1.3 微服务拆分建议

#### 1.3.1 服务拆分原则

1. **业务边界清晰**: 按业务领域拆分服务
2. **数据独立**: 每个服务拥有独立的数据存储
3. **松耦合**: 服务间通过API或消息通信
4. **高内聚**: 相关功能聚合在同一服务内
5. **可独立部署**: 支持独立开发、测试、部署

#### 1.3.2 建议拆分方案

| 服务名称 | 职责范围 | 数据存储 | 通信方式 |
|----------|----------|----------|----------|
| 用户服务 | 用户管理、认证授权 | MySQL | HTTP API |
| 设备服务 | 设备管理、状态监控 | MySQL + Redis | HTTP API + MQTT |
| 能耗服务 | 能耗数据采集分析 | InfluxDB + MySQL | HTTP API + MQ |
| 通知服务 | 消息推送、告警通知 | Redis + MySQL | HTTP API + WebSocket |
| 网关服务 | 设备通信、协议转换 | Redis + MySQL | MQTT + HTTP API |

#### 1.3.3 服务间通信

**同步通信**:
- HTTP/HTTPS RESTful API
- gRPC (高性能场景)
- GraphQL (复杂查询场景)

**异步通信**:
- MQTT (设备通信)
- RabbitMQ (业务消息)
- Redis Streams (实时数据)
- WebSocket (实时推送)

---

## 2. Admin.NET后端架构规范

### 2.1 项目结构标准

#### 2.1.1 解决方案结构

```
Admin.NET.EnergyLight/
├── Admin.NET.Web.Entry/        # Web API入口项目
├── Admin.NET.Application/      # 应用服务层
├── Admin.NET.Core/            # 核心业务层
├── Admin.NET.Infrastructure/   # 基础设施层
├── Admin.NET.Plugin.MQTT/     # MQTT插件
├── Admin.NET.Plugin.Energy/   # 能耗插件
└── Admin.NET.Tests/           # 单元测试项目
```

#### 2.1.2 各层职责定义

**Web.Entry层**:
- API控制器定义
- 中间件配置
- 依赖注入配置
- Swagger文档配置

**Application层**:
- 应用服务实现
- DTO定义和映射
- 业务验证逻辑
- 事务协调

**Core层**:
- 领域实体定义
- 业务规则实现
- 仓储接口定义
- 领域服务

**Infrastructure层**:
- 数据访问实现
- 外部服务集成
- 缓存实现
- 消息队列实现

### 2.2 编码规范

#### 2.2.1 命名规范

**类命名**:
- 使用PascalCase命名法
- 控制器以Controller结尾
- 服务类以Service结尾
- 仓储类以Repository结尾

```csharp
// 正确示例
public class DeviceController : BaseController
public class DeviceService : IDeviceService
public class DeviceRepository : BaseRepository<Device>

// 错误示例
public class deviceController  // 首字母应大写
public class Device_Service    // 不应使用下划线
```

**方法命名**:
- 使用PascalCase命名法
- 动词开头，表达明确的操作意图
- 异步方法以Async结尾

```csharp
// 正确示例
public async Task<Device> GetDeviceByIdAsync(string deviceId)
public bool ValidateDeviceStatus(Device device)
public void UpdateDeviceConfiguration(Device device, DeviceConfig config)

// 错误示例
public async Task<Device> GetDevice(string deviceId)  // 缺少Async后缀
public bool Check(Device device)                      // 动词不明确
```

**变量命名**:
- 使用camelCase命名法
- 私有字段以下划线开头
- 常量使用PascalCase

```csharp
// 正确示例
private readonly IDeviceService _deviceService;
public const int MaxRetryCount = 3;
var deviceList = await _deviceService.GetDevicesAsync();

// 错误示例
private readonly IDeviceService deviceService;  // 缺少下划线前缀
public const int maxRetryCount = 3;             // 常量应使用PascalCase
```

#### 2.2.2 代码注释规范

**类级注释**:
```csharp
/// <summary>
/// 设备管理服务
/// 提供设备的增删改查、状态监控、控制指令等功能
/// </summary>
/// <remarks>
/// 该服务集成了MQTT通信功能，支持实时设备状态更新
/// 作者: 开发团队
/// 创建时间: 2025-01-29
/// </remarks>
public class DeviceService : IDeviceService
{
    // 实现代码
}
```

**方法级注释**:
```csharp
/// <summary>
/// 根据设备ID获取设备详细信息
/// </summary>
/// <param name="deviceId">设备唯一标识符</param>
/// <param name="includeStatus">是否包含实时状态信息</param>
/// <returns>设备详细信息，如果设备不存在则返回null</returns>
/// <exception cref="ArgumentException">当deviceId为空或无效时抛出</exception>
/// <exception cref="DeviceNotFoundException">当设备不存在时抛出</exception>
public async Task<DeviceDetailDto> GetDeviceDetailAsync(string deviceId, bool includeStatus = true)
{
    // 实现代码
}
```

### 2.3 异常处理规范

#### 2.3.1 异常分类

```csharp
// 业务异常基类
public abstract class BusinessException : Exception
{
    public string ErrorCode { get; }
    public object[] Parameters { get; }
    
    protected BusinessException(string errorCode, string message, params object[] parameters) 
        : base(message)
    {
        ErrorCode = errorCode;
        Parameters = parameters;
    }
}

// 设备相关异常
public class DeviceNotFoundException : BusinessException
{
    public DeviceNotFoundException(string deviceId) 
        : base("DEVICE_NOT_FOUND", $"设备 {deviceId} 不存在", deviceId)
    {
    }
}

// MQTT通信异常
public class MqttConnectionException : BusinessException
{
    public MqttConnectionException(string reason) 
        : base("MQTT_CONNECTION_FAILED", $"MQTT连接失败: {reason}", reason)
    {
    }
}
```

#### 2.3.2 全局异常处理

```csharp
public class GlobalExceptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<GlobalExceptionMiddleware> _logger;
    
    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "全局异常处理: {Message}", ex.Message);
            await HandleExceptionAsync(context, ex);
        }
    }
    
    private static async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        var response = new ApiResponse<object>
        {
            Success = false,
            Message = GetErrorMessage(exception),
            ErrorCode = GetErrorCode(exception)
        };
        
        context.Response.ContentType = "application/json";
        context.Response.StatusCode = GetStatusCode(exception);
        
        await context.Response.WriteAsync(JsonSerializer.Serialize(response));
    }
}
```

---

## 3. Web前端架构规范

### 3.1 项目结构标准

#### 3.1.1 目录结构

```
src/
├── components/              # 公共组件
│   ├── common/             # 通用UI组件
│   │   ├── Button/         # 按钮组件
│   │   ├── Modal/          # 弹窗组件
│   │   └── Table/          # 表格组件
│   ├── business/           # 业务组件
│   │   ├── DeviceCard/     # 设备卡片
│   │   ├── EnergyChart/    # 能耗图表
│   │   └── StatusIndicator/ # 状态指示器
│   └── layout/             # 布局组件
│       ├── Header/         # 页头
│       ├── Sidebar/        # 侧边栏
│       └── Footer/         # 页脚
├── pages/                  # 页面组件
│   ├── Dashboard/          # 仪表板页面
│   ├── Devices/            # 设备管理页面
│   │   ├── DeviceList/     # 设备列表
│   │   ├── DeviceDetail/   # 设备详情
│   │   └── DeviceControl/  # 设备控制
│   ├── Energy/             # 能耗管理页面
│   ├── Users/              # 用户管理页面
│   └── Settings/           # 系统设置页面
├── services/               # API服务
│   ├── api/                # API接口定义
│   │   ├── device.ts       # 设备相关API
│   │   ├── user.ts         # 用户相关API
│   │   └── energy.ts       # 能耗相关API
│   ├── http/               # HTTP客户端配置
│   └── websocket/          # WebSocket服务
├── stores/                 # 状态管理
│   ├── useAuthStore.ts     # 认证状态
│   ├── useDeviceStore.ts   # 设备状态
│   └── useAppStore.ts      # 应用全局状态
├── hooks/                  # 自定义Hook
│   ├── useApi.ts           # API调用Hook
│   ├── useWebSocket.ts     # WebSocket Hook
│   └── usePermission.ts    # 权限检查Hook
├── utils/                  # 工具函数
│   ├── format.ts           # 格式化工具
│   ├── validation.ts       # 验证工具
│   └── constants.ts        # 常量定义
├── types/                  # TypeScript类型定义
│   ├── api.ts              # API类型
│   ├── device.ts           # 设备类型
│   └── user.ts             # 用户类型
└── styles/                 # 样式文件
    ├── globals.css         # 全局样式
    ├── variables.css       # CSS变量
    └── components/         # 组件样式
```

#### 3.1.2 组件开发规范

**函数组件标准**:
```typescript
import React, { useState, useEffect } from 'react';
import { Button, Card, message } from 'antd';
import { DeviceService } from '@/services/api/device';
import { Device } from '@/types/device';
import styles from './DeviceCard.module.css';

/**
 * 设备卡片组件
 * 显示设备基本信息和状态，支持快速控制操作
 */
interface DeviceCardProps {
  /** 设备信息 */
  device: Device;
  /** 是否显示控制按钮 */
  showControls?: boolean;
  /** 点击事件回调 */
  onClick?: (device: Device) => void;
  /** 控制操作回调 */
  onControl?: (deviceId: string, action: string) => void;
}

export const DeviceCard: React.FC<DeviceCardProps> = ({
  device,
  showControls = true,
  onClick,
  onControl
}) => {
  const [loading, setLoading] = useState(false);
  
  /**
   * 处理设备控制操作
   * @param action 控制动作
   */
  const handleControl = async (action: string) => {
    try {
      setLoading(true);
      await DeviceService.controlDevice(device.id, action);
      message.success('操作成功');
      onControl?.(device.id, action);
    } catch (error) {
      message.error('操作失败');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <Card 
      className={styles.deviceCard}
      onClick={() => onClick?.(device)}
      hoverable
    >
      <div className={styles.deviceInfo}>
        <h3>{device.name}</h3>
        <p>状态: {device.online ? '在线' : '离线'}</p>
      </div>
      
      {showControls && (
        <div className={styles.controls}>
          <Button 
            type="primary" 
            loading={loading}
            onClick={(e) => {
              e.stopPropagation();
              handleControl('toggle');
            }}
          >
            {device.powerState === 'on' ? '关闭' : '开启'}
          </Button>
        </div>
      )}
    </Card>
  );
};

export default DeviceCard;
```

**自定义Hook规范**:
```typescript
import { useState, useEffect, useCallback } from 'react';
import { DeviceService } from '@/services/api/device';
import { Device } from '@/types/device';

/**
 * 设备管理Hook
 * 提供设备列表获取、状态更新、控制操作等功能
 */
export const useDevices = () => {
  const [devices, setDevices] = useState<Device[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  /**
   * 获取设备列表
   */
  const fetchDevices = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await DeviceService.getDevices();
      setDevices(response.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取设备列表失败');
    } finally {
      setLoading(false);
    }
  }, []);
  
  /**
   * 控制设备
   * @param deviceId 设备ID
   * @param action 控制动作
   */
  const controlDevice = useCallback(async (deviceId: string, action: string) => {
    try {
      await DeviceService.controlDevice(deviceId, action);
      // 更新本地状态
      setDevices(prev => prev.map(device => 
        device.id === deviceId 
          ? { ...device, powerState: action === 'on' ? 'on' : 'off' }
          : device
      ));
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : '设备控制失败');
    }
  }, []);
  
  useEffect(() => {
    fetchDevices();
  }, [fetchDevices]);
  
  return {
    devices,
    loading,
    error,
    fetchDevices,
    controlDevice
  };
};
```

### 3.2 状态管理规范

#### 3.2.1 Zustand Store设计

```typescript
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { Device } from '@/types/device';

/**
 * 设备状态管理
 */
interface DeviceState {
  // 状态
  devices: Device[];
  selectedDevice: Device | null;
  loading: boolean;
  error: string | null;
  
  // 操作
  setDevices: (devices: Device[]) => void;
  addDevice: (device: Device) => void;
  updateDevice: (deviceId: string, updates: Partial<Device>) => void;
  removeDevice: (deviceId: string) => void;
  selectDevice: (device: Device | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

export const useDeviceStore = create<DeviceState>()()
  devtools(
    persist(
      (set, get) => ({
        // 初始状态
        devices: [],
        selectedDevice: null,
        loading: false,
        error: null,
        
        // 操作方法
        setDevices: (devices) => set({ devices }),
        
        addDevice: (device) => set((state) => ({
          devices: [...state.devices, device]
        })),
        
        updateDevice: (deviceId, updates) => set((state) => ({
          devices: state.devices.map(device => 
            device.id === deviceId ? { ...device, ...updates } : device
          ),
          selectedDevice: state.selectedDevice?.id === deviceId 
            ? { ...state.selectedDevice, ...updates } 
            : state.selectedDevice
        })),
        
        removeDevice: (deviceId) => set((state) => ({
          devices: state.devices.filter(device => device.id !== deviceId),
          selectedDevice: state.selectedDevice?.id === deviceId 
            ? null 
            : state.selectedDevice
        })),
        
        selectDevice: (device) => set({ selectedDevice: device }),
        setLoading: (loading) => set({ loading }),
        setError: (error) => set({ error }),
        clearError: () => set({ error: null })
      }),
      {
        name: 'device-store',
        partialize: (state) => ({ 
          devices: state.devices,
          selectedDevice: state.selectedDevice 
        })
      }
    ),
    { name: 'DeviceStore' }
  )
);
```

---

## 4. 文档管理规范

### 4.1 文档分类体系

#### 4.1.1 一级分类

| 分类编号 | 分类名称 | 英文名称 | 描述 |
|----------|----------|----------|------|
| 01 | 产品需求 | Product Requirements | 产品功能需求、业务流程、用户故事 |
| 02 | 技术架构 | Technical Architecture | 系统架构、技术选型、接口设计 |
| 03 | 开发规范 | Development Standards | 编码规范、架构模式、最佳实践 |
| 04 | 部署运维 | Deployment Operations | 部署指南、运维手册、监控配置 |
| 05 | 用户手册 | User Manuals | 用户指南、操作手册、FAQ |
| 06 | 项目管理 | Project Management | 项目计划、进度跟踪、风险管理 |
| 07 | 测试文档 | Testing | 测试计划、测试用例、测试报告 |
| 08 | 资源文件 | Resources | 配置文件、图片资源、模板文件 |

#### 4.1.2 二级分类

**01-产品需求**:
- `prd/` - 产品需求文档
- `business-process/` - 业务流程
- `user-stories/` - 用户故事
- `ui-ux/` - UI/UX设计

**02-技术架构**:
- `system-architecture/` - 系统架构
- `api-design/` - API设计
- `database-design/` - 数据库设计
- `integration/` - 集成方案

**03-开发规范**:
- `coding-standards/` - 编码规范
- `architecture-patterns/` - 架构模式
- `best-practices/` - 最佳实践
- `security/` - 安全规范

### 4.2 目录结构规范

#### 4.2.1 标准目录结构

```
.trae/documents/
├── 01-product-requirements/     # 产品需求文档
│   ├── prd/                    # 产品需求文档
│   ├── business-process/       # 业务流程文档
│   ├── user-stories/           # 用户故事
│   └── ui-ux/                  # UI/UX设计文档
├── 02-technical-architecture/   # 技术架构文档
│   ├── system-architecture/    # 系统架构设计
│   ├── api-design/            # API接口设计
│   ├── database-design/       # 数据库设计
│   └── integration/           # 系统集成方案
├── 03-development-standards/    # 开发规范文档
│   ├── coding-standards/      # 编码规范
│   ├── architecture-patterns/ # 架构模式
│   ├── best-practices/        # 最佳实践
│   └── security/              # 安全规范
├── 04-deployment-operations/    # 部署运维文档
│   ├── deployment/            # 部署指南
│   ├── operations/            # 运维手册
│   ├── monitoring/            # 监控配置
│   └── troubleshooting/       # 故障排除
├── 05-user-manuals/            # 用户手册
│   ├── admin-guide/           # 管理员指南
│   ├── user-guide/            # 用户指南
│   ├── api-reference/         # API参考
│   └── faq/                   # 常见问题
├── 06-project-management/       # 项目管理文档
│   ├── planning/              # 项目计划
│   ├── progress/              # 进度跟踪
│   ├── risks/                 # 风险管理
│   └── meetings/              # 会议记录
├── 07-testing/                 # 测试文档
│   ├── test-plans/            # 测试计划
│   ├── test-cases/            # 测试用例
│   ├── test-reports/          # 测试报告
│   └── automation/            # 自动化测试
├── 08-resources/               # 资源文件
│   ├── configs/               # 配置文件
│   ├── images/                # 图片资源
│   └── templates/             # 模板文件
├── README.md                   # 文档中心入口
└── 文档索引表.md               # 文档索引表
```

#### 4.2.2 命名规则

**目录命名**:
- 使用小写字母和连字符
- 英文命名，简洁明确
- 数字前缀表示优先级

**文件命名**:
- 格式: `{主题}_{文档类型}_{版本号}.md`
- 示例: `节能灯管理系统_产品需求文档_v1.0.md`
- 使用中文名称，便于识别

### 4.3 文件命名规范

#### 4.3.1 命名格式

**标准格式**:
```
{项目/模块名称}_{文档类型}_{版本号}.{扩展名}
```

**文档类型缩写**:
- `产品需求文档` - PRD (Product Requirements Document)
- `技术架构文档` - TAD (Technical Architecture Document)
- `API设计文档` - API (API Design Document)
- `数据库设计文档` - DBD (Database Design Document)
- `用户手册` - UM (User Manual)
- `部署指南` - DG (Deployment Guide)

#### 4.3.2 命名示例

| 文档类型 | 命名示例 | 说明 |
|----------|----------|------|
| 产品需求 | `节能灯管理系统_产品需求文档_v1.0.md` | 主系统PRD |
| 技术架构 | `MQTT插件服务器_技术架构文档_v1.0.md` | MQTT模块架构 |
| API设计 | `设备管理_API设计文档_v1.0.md` | 设备管理API |
| 数据库设计 | `能耗数据_数据库设计文档_v1.0.md` | 能耗数据库设计 |
| 用户手册 | `Web管理端_用户手册_v1.0.md` | Web端用户指南 |
| 部署指南 | `Docker容器化_部署指南_v1.0.md` | Docker部署 |

#### 4.3.3 命名规则

1. **一致性**: 同类文档使用统一命名格式
2. **可读性**: 使用中文名称，便于团队理解
3. **版本化**: 包含版本号，支持版本管理
4. **唯一性**: 确保文件名在项目中唯一
5. **简洁性**: 避免过长的文件名

### 4.4 文档版本管理

#### 4.4.1 版本号规则

**语义化版本控制**:
- 格式: `v{主版本}.{次版本}.{修订版本}`
- 主版本: 重大架构变更或不兼容更新
- 次版本: 功能新增或重要更新
- 修订版本: 错误修复或小幅调整

**版本号示例**:
- `v1.0` - 初始版本
- `v1.1` - 功能增强
- `v1.1.1` - 错误修复
- `v2.0` - 重大更新

#### 4.4.2 版本管理策略

**文档生命周期**:
1. **草稿 (Draft)** - 文档编写阶段
2. **审核 (Review)** - 内部审核阶段
3. **发布 (Released)** - 正式发布版本
4. **归档 (Archived)** - 过期版本归档

**版本控制流程**:
```mermaid
graph LR
    A[创建草稿] --> B[内容编写]
    B --> C[内部审核]
    C --> D{审核通过?}
    D -->|是| E[发布版本]
    D -->|否| B
    E --> F[版本维护]
    F --> G[版本归档]
```

#### 4.4.3 历史版本管理

**版本存储结构**:
```
.trae/documents/
├── current/                    # 当前版本
│   └── 节能灯管理系统_PRD_v2.0.md
├── archive/                    # 历史版本
│   ├── v1.0/
│   │   └── 节能灯管理系统_PRD_v1.0.md
│   └── v1.1/
│       └── 节能灯管理系统_PRD_v1.1.md
└── CHANGELOG.md               # 变更日志
```

**变更日志格式**:
```markdown
# 变更日志

## [v2.0] - 2025-01-29
### 新增
- 整合产品需求与技术架构文档
- 新增MQTT通信架构设计
- 完善API接口定义

### 修改
- 优化系统架构图
- 更新数据库设计
- 完善错误处理机制

### 删除
- 移除过时的接口定义
- 清理重复的配置说明

## [v1.1] - 2025-01-15
### 修复
- 修正API文档中的错误示例
- 更新过期的技术栈信息
```

### 4.5 文档索引和检索

#### 4.5.1 文档索引表

**索引表结构**:
```markdown
# 文档索引表

## 文档概览
- **文档总数**: 15份
- **最后更新**: 2025-01-29
- **维护团队**: 技术文档团队

## 分类统计
| 分类 | 文档数量 | 最新版本 |
|------|----------|----------|
| 产品需求 | 3 | v2.0 |
| 技术架构 | 4 | v2.0 |
| 开发规范 | 2 | v2.0 |
| 部署运维 | 2 | v1.0 |
| 用户手册 | 2 | v1.0 |
| 项目管理 | 1 | v1.0 |
| 测试文档 | 1 | v1.0 |

## 详细文档清单

### 产品需求文档
| 文档名称 | 版本 | 更新日期 | 负责人 | 状态 |
|----------|------|----------|--------|------|
| 节能灯管理系统综合产品需求与架构文档 | v2.0 | 2025-01-29 | 产品团队 | 发布 |
```

#### 4.5.2 标签体系

**功能模块标签**:
- `#设备管理` - 设备相关功能
- `#能耗监控` - 能耗管理功能
- `#用户管理` - 用户权限管理
- `#MQTT通信` - MQTT消息通信
- `#数据分析` - 数据统计分析

**技术栈标签**:
- `#Admin.NET` - Admin.NET框架
- `#React` - React前端
- `#UniApp` - UniApp移动端
- `#MySQL` - MySQL数据库
- `#Redis` - Redis缓存
- `#EMQX` - EMQX消息代理

**文档类型标签**:
- `#需求文档` - 产品需求类
- `#架构文档` - 技术架构类
- `#API文档` - 接口设计类
- `#用户手册` - 使用指南类
- `#规范文档` - 开发规范类

#### 4.5.3 检索机制

**全文检索**:
- 支持关键词搜索
- 支持标签筛选
- 支持文档类型筛选
- 支持时间范围筛选

**快速导航**:
- 按功能模块导航
- 按技术栈导航
- 按文档类型导航
- 按更新时间导航

---

## 5. 质量保证体系

### 5.1 代码质量标准

#### 5.1.1 代码审查制度

**审查流程**:
1. **提交代码** - 开发者提交Pull Request
2. **自动检查** - CI/CD自动运行代码检查
3. **同行审查** - 至少2名同事进行代码审查
4. **架构审查** - 架构师审查重要变更
5. **合并代码** - 审查通过后合并到主分支

**审查清单**:
- [ ] 代码符合编码规范
- [ ] 单元测试覆盖率达标
- [ ] 性能影响评估
- [ ] 安全漏洞检查
- [ ] 文档更新完整
- [ ] 向后兼容性检查

#### 5.1.2 自动化测试

**测试金字塔**:
```mermaid
graph TD
    A[端到端测试 E2E<br/>少量，关键业务流程] --> B[集成测试 Integration<br/>中等数量，模块间交互]
    B --> C[单元测试 Unit<br/>大量，函数级别测试]
```

**测试覆盖率要求**:
- 单元测试覆盖率 ≥ 80%
- 集成测试覆盖率 ≥ 60%
- 端到端测试覆盖核心业务流程

**测试工具栈**:
- **后端单元测试**: xUnit + Moq
- **前端单元测试**: Jest + React Testing Library
- **集成测试**: TestContainers + WebApplicationFactory
- **端到端测试**: Playwright + Cucumber

### 5.2 性能标准

#### 5.2.1 性能指标

| 指标类型 | 指标名称 | 目标值 | 监控方式 |
|----------|----------|--------|----------|
| 响应时间 | API响应时间 | < 200ms (P95) | APM监控 |
| 吞吐量 | 并发用户数 | > 1000 | 负载测试 |
| 可用性 | 系统可用率 | > 99.9% | 健康检查 |
| 资源使用 | CPU使用率 | < 70% | 系统监控 |
| 资源使用 | 内存使用率 | < 80% | 系统监控 |
| 数据库 | 查询响应时间 | < 100ms (P95) | 数据库监控 |

#### 5.2.2 性能测试

**负载测试场景**:
```javascript
// K6负载测试脚本示例
import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
  stages: [
    { duration: '2m', target: 100 },   // 2分钟内增加到100用户
    { duration: '5m', target: 100 },   // 保持100用户5分钟
    { duration: '2m', target: 200 },   // 2分钟内增加到200用户
    { duration: '5m', target: 200 },   // 保持200用户5分钟
    { duration: '2m', target: 0 },     // 2分钟内减少到0用户
  ],
};

export default function () {
  // 测试设备列表API
  let response = http.get('http://localhost:5000/api/devices');
  check(response, {
    '状态码为200': (r) => r.status === 200,
    '响应时间小于200ms': (r) => r.timings.duration < 200,
  });
  
  sleep(1);
}
```

### 5.3 安全标准

#### 5.3.1 安全检查清单

**认证与授权**:
- [ ] 实施强密码策略
- [ ] 使用JWT令牌认证
- [ ] 实现基于角色的访问控制(RBAC)
- [ ] 支持多因素认证(MFA)
- [ ] 定期轮换密钥和证书

**数据保护**:
- [ ] 敏感数据加密存储
- [ ] 传输层加密(TLS 1.2+)
- [ ] 数据脱敏和匿名化
- [ ] 定期数据备份
- [ ] 数据访问审计日志

**输入验证**:
- [ ] 服务端输入验证
- [ ] SQL注入防护
- [ ] XSS攻击防护
- [ ] CSRF攻击防护
- [ ] 文件上传安全检查

#### 5.3.2 安全扫描

**静态代码扫描**:
- 使用SonarQube进行代码质量和安全扫描
- 集成到CI/CD流水线
- 修复高危和中危安全漏洞

**依赖项扫描**:
- 使用npm audit / dotnet list package --vulnerable
- 定期更新依赖项版本
- 监控安全漏洞通告

**渗透测试**:
- 定期进行安全渗透测试
- 使用OWASP ZAP进行自动化安全测试
- 修复发现的安全问题

---

## 6. 团队协作规范

### 6.1 Git工作流

#### 6.1.1 分支策略

**Git Flow分支模型**:
```mermaid
gitgraph
    commit id: "Initial"
    branch develop
    checkout develop
    commit id: "Feature A"
    branch feature/device-management
    checkout feature/device-management
    commit id: "Add device CRUD"
    commit id: "Add device control"
    checkout develop
    merge feature/device-management
    commit id: "Merge feature"
    branch release/v1.0
    checkout release/v1.0
    commit id: "Bug fixes"
    checkout main
    merge release/v1.0
    tag: "v1.0"
    checkout develop
    merge release/v1.0
```

**分支命名规范**:
- `main` - 主分支，生产环境代码
- `develop` - 开发分支，集成最新功能
- `feature/{功能名称}` - 功能分支
- `release/{版本号}` - 发布分支
- `hotfix/{问题描述}` - 热修复分支

#### 6.1.2 提交规范

**提交消息格式**:
```
<type>(<scope>): <subject>

<body>

<footer>
```

**类型说明**:
- `feat` - 新功能
- `fix` - 错误修复
- `docs` - 文档更新
- `style` - 代码格式调整
- `refactor` - 代码重构
- `test` - 测试相关
- `chore` - 构建工具、辅助工具变动

**提交示例**:
```
feat(device): 添加设备批量控制功能

- 实现设备批量选择
- 添加批量控制API
- 更新前端控制界面

Closes #123
```

### 6.2 代码审查流程

#### 6.2.1 Pull Request模板

```markdown
## 变更描述
简要描述本次变更的内容和目的

## 变更类型
- [ ] 新功能
- [ ] 错误修复
- [ ] 性能优化
- [ ] 代码重构
- [ ] 文档更新

## 测试
- [ ] 单元测试已通过
- [ ] 集成测试已通过
- [ ] 手动测试已完成

## 检查清单
- [ ] 代码符合编码规范
- [ ] 已添加必要的测试
- [ ] 文档已更新
- [ ] 无安全漏洞
- [ ] 性能影响已评估

## 相关Issue
Closes #issue_number

## 截图
如有UI变更，请提供截图
```

#### 6.2.2 审查要点

**功能性审查**:
- 功能实现是否正确
- 边界条件处理
- 错误处理机制
- 性能影响评估

**代码质量审查**:
- 代码可读性和可维护性
- 设计模式使用是否合理
- 代码复用和模块化
- 注释和文档完整性

**安全性审查**:
- 输入验证和输出编码
- 权限检查和访问控制
- 敏感信息处理
- 依赖项安全性

### 6.3 沟通协作

#### 6.3.1 会议制度

**每日站会 (Daily Standup)**:
- 时间: 每日上午9:30
- 时长: 15分钟
- 内容: 昨日完成、今日计划、遇到问题

**迭代计划会 (Sprint Planning)**:
- 时间: 每两周一次
- 时长: 2小时
- 内容: 需求评估、任务分解、工作量估算

**迭代回顾会 (Sprint Retrospective)**:
- 时间: 每迭代结束
- 时长: 1小时
- 内容: 总结经验、改进建议、行动计划

#### 6.3.2 文档协作

**文档编写流程**:
1. **需求确认** - 明确文档需求和目标读者
2. **大纲设计** - 制定文档结构和章节安排
3. **内容编写** - 按照规范编写文档内容
4. **内部审查** - 团队成员交叉审查
5. **发布更新** - 发布正式版本并通知相关人员

**协作工具**:
- **版本控制**: Git + GitHub/GitLab
- **文档编辑**: Markdown + VS Code
- **实时协作**: 腾讯文档 / 飞书文档
- **沟通交流**: 企业微信 / 钉钉

---

## 7. 持续改进机制

### 7.1 技术债务管理

#### 7.1.1 技术债务识别

**技术债务类型**:
- **代码债务**: 代码质量问题、重复代码
- **架构债务**: 架构设计缺陷、技术选型问题
- **测试债务**: 测试覆盖不足、测试质量问题
- **文档债务**: 文档缺失、文档过时
- **基础设施债务**: 部署环境问题、监控缺失

**债务评估标准**:
| 等级 | 影响范围 | 修复紧急度 | 修复成本 |
|------|----------|------------|----------|
| 高 | 影响核心功能 | 立即修复 | 高 |
| 中 | 影响部分功能 | 下个迭代 | 中 |
| 低 | 影响开发效率 | 有时间时修复 | 低 |

#### 7.1.2 债务偿还计划

**偿还策略**:
- 每个迭代分配20%时间用于技术债务偿还
- 优先修复高等级技术债务
- 结合新功能开发进行重构
- 定期评估和更新债务清单

### 7.2 知识管理

#### 7.2.1 知识库建设

**知识分类**:
- **技术知识**: 框架使用、最佳实践、问题解决方案
- **业务知识**: 业务流程、领域知识、用户需求
- **项目知识**: 项目历史、决策记录、经验教训
- **工具知识**: 开发工具、部署工具、监控工具

**知识共享机制**:
- 技术分享会 (每月一次)
- 代码审查中的知识传递
- 文档编写和维护
- 新人培训和导师制度

#### 7.2.2 最佳实践总结

**实践收集**:
- 项目开发中的成功经验
- 问题解决的有效方法
- 性能优化的实用技巧
- 安全防护的实施方案

**实践推广**:
- 编写最佳实践文档
- 在代码审查中推广
- 在培训中传授
- 在新项目中应用

### 7.3 流程优化

#### 7.3.1 效率度量

**开发效率指标**:
- 代码提交频率
- 功能交付周期
- 缺陷修复时间
- 代码审查时间

**质量指标**:
- 缺陷密度
- 测试覆盖率
- 代码复杂度
- 技术债务数量

#### 7.3.2 持续改进

**改进循环**:
```mermaid
graph LR
    A[度量现状] --> B[识别问题]
    B --> C[制定改进方案]
    C --> D[实施改进]
    D --> E[评估效果]
    E --> A
```

**改进实施**:
- 定期回顾和评估
- 收集团队反馈
- 调整流程和规范
- 推广成功经验

---

## 8. 附录

### 8.1 工具清单

#### 8.1.1 开发工具

| 工具类型 | 工具名称 | 版本要求 | 用途 |
|----------|----------|----------|------|
| IDE | Visual Studio 2022 | 17.8+ | .NET开发 |
| IDE | VS Code | 1.85+ | 前端开发 |
| 数据库工具 | MySQL Workbench | 8.0+ | 数据库管理 |
| API测试 | Postman | 10.0+ | API测试 |
| 版本控制 | Git | 2.40+ | 代码版本控制 |
| 容器 | Docker | 24.0+ | 容器化部署 |

#### 8.1.2 监控工具

| 工具名称 | 用途 | 配置要求 |
|----------|------|----------|
| Prometheus | 指标收集 | 内存 ≥ 4GB |
| Grafana | 数据可视化 | CPU ≥ 2核 |
| ELK Stack | 日志分析 | 磁盘 ≥ 100GB |
| Jaeger | 链路追踪 | 网络带宽 ≥ 100Mbps |

### 8.2 参考资料

#### 8.2.1 技术文档

1. **Admin.NET官方文档**: https://www.admin.net.cn/
2. **ASP.NET Core文档**: https://docs.microsoft.com/aspnet/core/
3. **React官方文档**: https://react.dev/
4. **UniApp开发文档**: https://uniapp.dcloud.net.cn/
5. **MQTT协议规范**: https://mqtt.org/mqtt-specification/
6. **MySQL官方文档**: https://dev.mysql.com/doc/
7. **Redis官方文档**: https://redis.io/documentation

#### 8.2.2 最佳实践

1. **Clean Architecture**: Robert C. Martin
2. **Domain-Driven Design**: Eric Evans
3. **Microservices Patterns**: Chris Richardson
4. **Building Microservices**: Sam Newman
5. **The Pragmatic Programmer**: Andy Hunt, Dave Thomas

### 8.3 更新记录

| 版本 | 日期 | 更新内容 | 更新人 |
|------|------|----------|--------|
| v2.0 | 2025-01-29 | 整合项目架构优化文档和文档管理规范，形成完整的开发标准体系 | 架构团队 |
| v1.1 | 2025-01-15 | 完善编码规范和代码审查流程 | 开发团队 |
| v1.0 | 2025-01-01 | 初始版本，建立基础开发规范 | 项目团队 |

---

## 结语

本文档作为节能灯管理系统项目的开发标准与文档管理规范，旨在为团队提供统一的开发指导和协作框架。通过遵循这些规范，我们能够：

- **提高开发效率**: 统一的标准减少沟通成本，提升协作效率
- **保证代码质量**: 规范的流程确保代码质量和系统稳定性
- **促进知识共享**: 完善的文档体系促进团队知识传承
- **支持项目扩展**: 标准化的架构支持系统持续演进

我们鼓励团队成员积极参与规范的完善和优化，共同构建高质量的软件产品。

**文档维护**: 本文档将根据项目发展和团队反馈持续更新，确保规范的实用性和时效性。

**联系方式**: 如有疑问或建议，请联系架构团队或通过项目协作平台反馈。