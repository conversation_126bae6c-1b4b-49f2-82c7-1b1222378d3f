// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core;

/// <summary>
/// MQTT配置选项 🧩
/// 用于解析MQTT.json配置文件中的MQTT连接参数
/// </summary>
public sealed class MQTTOptions : IConfigurableOptions
{
    /// <summary>
    /// MQTT代理配置
    /// </summary>
    public MQTTBrokerOptions MQTTBroker { get; set; } = new();
}

/// <summary>
/// MQTT代理配置选项 🧩
/// 包含不同MQTT代理平台的配置信息
/// </summary>
public sealed class MQTTBrokerOptions
{
    /// <summary>
    /// EMQX平台配置
    /// </summary>
    public EMQXOptions EMQX { get; set; } = new();
}

/// <summary>
/// EMQX平台配置选项 🧩
/// 包含EMQX平台的认证参数和连接信息
/// </summary>
public sealed class EMQXOptions
{
    /// <summary>
    /// 实例标识符
    /// 用于标识MQTT实例的唯一ID
    /// </summary>
    public string InstanceId { get; set; } = string.Empty;

    /// <summary>
    /// 访问密钥ID
    /// 用于MQTT连接认证的访问密钥标识
    /// </summary>
    public string AccessKeyId { get; set; } = string.Empty;

    /// <summary>
    /// 访问密钥密码
    /// 用于MQTT连接认证的访问密钥密码
    /// </summary>
    public string AccessKeySecret { get; set; } = string.Empty;

    /// <summary>
    /// 认证有效期（分钟）
    /// 设备认证令牌的有效时间，默认为60分钟
    /// </summary>
    public int TokenExpirationMinutes { get; set; } = 60;

    /// <summary>
    /// MQTT服务器地址
    /// MQTT代理服务器的连接地址
    /// </summary>
    public string ServerAddress { get; set; } = "localhost";

    /// <summary>
    /// MQTT服务器端口
    /// MQTT代理服务器的连接端口，默认为1883
    /// </summary>
    public int ServerPort { get; set; } = 1883;

    /// <summary>
    /// 是否启用SSL/TLS加密
    /// 控制MQTT连接是否使用SSL/TLS加密传输
    /// </summary>
    public bool EnableSsl { get; set; } = false;

    /// <summary>
    /// 连接超时时间（秒）
    /// MQTT连接的超时时间，默认为30秒
    /// </summary>
    public int ConnectionTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// 保持连接心跳间隔（秒）
    /// MQTT保持连接的心跳间隔时间，默认为60秒
    /// </summary>
    public int KeepAliveSeconds { get; set; } = 60;
}