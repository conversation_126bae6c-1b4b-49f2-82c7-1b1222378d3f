using Admin.NET.Core;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Threading.Tasks;

namespace Admin.NET.Plugin.MQTT.Test
{
    /// <summary>
    /// 设备数据库操作测试类
    /// 用于验证设备扫描后的数据库保存和查询功能
    /// </summary>
    public class DeviceDbTest
    {
        private readonly ISqlSugarRepository<EnergyDevice> _energyDeviceRep;
        private readonly ILogger<DeviceDbTest> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="energyDeviceRep">设备数据仓储</param>
        /// <param name="logger">日志记录器</param>
        public DeviceDbTest(ISqlSugarRepository<EnergyDevice> energyDeviceRep, ILogger<DeviceDbTest> logger)
        {
            _energyDeviceRep = energyDeviceRep;
            _logger = logger;
        }

        /// <summary>
        /// 测试设备数据库保存功能
        /// </summary>
        /// <returns></returns>
        public async Task<bool> TestDeviceSaveAsync()
        {
            try
            {
                // 创建测试设备
                var testDevice = new EnergyDevice
                {
                    DeviceCode = "TEST_" + DateTime.Now.Ticks,
                    DeviceName = "测试设备_" + DateTime.Now.ToString("HHmmss"),
                    DeviceType = "LED灯",
                    DeviceModel = "测试型号",
                    Location = "自动发现",
                    Status = 1,
                    IsOnline = true,
                    InstallDate = DateTime.Now,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                };

                // 保存到数据库
                var result = await _energyDeviceRep.InsertAsync(testDevice);
                _logger.LogInformation("测试设备保存结果: {Result}, 设备ID: {DeviceId}", result, testDevice.Id);

                // 验证保存是否成功
                var savedDevice = await _energyDeviceRep.GetByIdAsync(testDevice.Id);
                if (savedDevice != null)
                {
                    _logger.LogInformation("设备保存验证成功: {DeviceName}, 编码: {DeviceCode}", 
                        savedDevice.DeviceName, savedDevice.DeviceCode);
                    
                    // 清理测试数据
                    await _energyDeviceRep.DeleteAsync(testDevice.Id);
                    return true;
                }
                else
                {
                    _logger.LogError("设备保存验证失败: 无法查询到保存的设备");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设备数据库保存测试失败");
                return false;
            }
        }

        /// <summary>
        /// 测试设备查询功能
        /// </summary>
        /// <returns></returns>
        public async Task<int> TestDeviceQueryAsync()
        {
            try
            {
                // 查询所有设备
                var allDevices = await _energyDeviceRep.GetListAsync();
                _logger.LogInformation("数据库中总设备数量: {Count}", allDevices.Count);

                // 查询在线设备
                var onlineDevices = await _energyDeviceRep.GetListAsync(d => d.IsOnline == true);
                _logger.LogInformation("在线设备数量: {Count}", onlineDevices.Count);

                // 查询最近创建的设备
                var recentDevices = await _energyDeviceRep.AsQueryable()
                    .Where(d => d.CreateTime >= DateTime.Now.AddHours(-1))
                    .ToListAsync();
                _logger.LogInformation("最近1小时创建的设备数量: {Count}", recentDevices.Count);

                foreach (var device in recentDevices)
                {
                    _logger.LogInformation("最近设备: {DeviceName} ({DeviceCode}), 创建时间: {CreateTime}", 
                        device.DeviceName, device.DeviceCode, device.CreateTime);
                }

                return allDevices.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设备查询测试失败");
                return -1;
            }
        }

        /// <summary>
        /// 检查特定设备是否存在
        /// </summary>
        /// <param name="deviceCode">设备编码</param>
        /// <param name="deviceName">设备名称</param>
        /// <returns></returns>
        public async Task<bool> CheckDeviceExistsAsync(string deviceCode, string deviceName)
        {
            try
            {
                var device = await _energyDeviceRep.GetFirstAsync(d => 
                    d.DeviceCode == deviceCode || d.DeviceName == deviceName);
                
                if (device != null)
                {
                    _logger.LogInformation("找到设备: {DeviceName} ({DeviceCode}), 状态: {Status}, 在线: {IsOnline}", 
                        device.DeviceName, device.DeviceCode, device.Status, device.IsOnline);
                    return true;
                }
                else
                {
                    _logger.LogWarning("未找到设备: 编码={DeviceCode}, 名称={DeviceName}", deviceCode, deviceName);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查设备存在性失败: 编码={DeviceCode}, 名称={DeviceName}", deviceCode, deviceName);
                return false;
            }
        }
    }
}