/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 更新能耗记录输入参数
 * @export
 * @interface UpdateEnergyConsumptionInput
 */
export interface UpdateEnergyConsumptionInput {
    /**
     * 设备ID
     * @type {number}
     * @memberof UpdateEnergyConsumptionInput
     */
    deviceId: number;
    /**
     * 记录时间
     * @type {Date}
     * @memberof UpdateEnergyConsumptionInput
     */
    recordTime: Date;
    /**
     * 记录类型
     * @type {string}
     * @memberof UpdateEnergyConsumptionInput
     */
    recordType: string;
    /**
     * 电压
     * @type {number}
     * @memberof UpdateEnergyConsumptionInput
     */
    voltage?: number | null;
    /**
     * 电流
     * @type {number}
     * @memberof UpdateEnergyConsumptionInput
     */
    current?: number | null;
    /**
     * 功率
     * @type {number}
     * @memberof UpdateEnergyConsumptionInput
     */
    power?: number | null;
    /**
     * 功率因数
     * @type {number}
     * @memberof UpdateEnergyConsumptionInput
     */
    powerFactor?: number | null;
    /**
     * 用电量
     * @type {number}
     * @memberof UpdateEnergyConsumptionInput
     */
    energyConsumption?: number | null;
    /**
     * 累计用电量
     * @type {number}
     * @memberof UpdateEnergyConsumptionInput
     */
    totalEnergyConsumption?: number | null;
    /**
     * 电费
     * @type {number}
     * @memberof UpdateEnergyConsumptionInput
     */
    electricityFee?: number | null;
    /**
     * 温度
     * @type {number}
     * @memberof UpdateEnergyConsumptionInput
     */
    temperature?: number | null;
    /**
     * 湿度
     * @type {number}
     * @memberof UpdateEnergyConsumptionInput
     */
    humidity?: number | null;
    /**
     * 备注
     * @type {string}
     * @memberof UpdateEnergyConsumptionInput
     */
    remark?: string | null;
    /**
     * 主键ID
     * @type {number}
     * @memberof UpdateEnergyConsumptionInput
     */
    id: number;
}
