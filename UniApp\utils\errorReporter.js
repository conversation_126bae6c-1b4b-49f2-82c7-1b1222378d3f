/**
 * 错误上报工具
 * 负责将错误信息收集并发送到后端进行分析
 */

// 错误上报配置
const REPORT_CONFIG = {
  // 上报地址
  reportUrl: '/api/system/error-report',
  
  // 批量上报配置
  batchSize: 10,
  batchTimeout: 30000, // 30秒
  
  // 重试配置
  maxRetries: 3,
  retryDelay: 5000,
  
  // 采样率（0-1，1表示100%上报）
  sampleRate: 1.0,
  
  // 是否启用上报
  enabled: true,
  
  // 过滤规则
  filters: {
    // 忽略的错误类型
    ignoreTypes: ['validation', 'business'],
    // 忽略的错误消息关键词
    ignoreMessages: ['网络连接已断开', '用户取消'],
    // 最小错误级别
    minLevel: 'warn'
  }
}

/**
 * 错误上报器类
 */
class ErrorReporter {
  constructor(config = {}) {
    this.config = { ...REPORT_CONFIG, ...config }
    this.errorQueue = []
    this.isReporting = false
    this.batchTimer = null
    
    // 初始化
    this.init()
  }
  
  /**
   * 初始化错误上报器
   */
  init() {
    // 启动批量上报定时器
    this.startBatchTimer()
    
    // 监听应用生命周期
    this.setupLifecycleListeners()
  }
  
  /**
   * 上报错误
   * @param {Object} errorInfo - 错误信息
   */
  async report(errorInfo) {
    if (!this.config.enabled) {
      return
    }
    
    // 采样检查
    if (Math.random() > this.config.sampleRate) {
      return
    }
    
    // 过滤检查
    if (this.shouldIgnoreError(errorInfo)) {
      return
    }
    
    // 格式化错误信息
    const formattedError = this.formatError(errorInfo)
    
    // 添加到队列
    this.errorQueue.push(formattedError)
    
    // 检查是否需要立即上报
    if (this.shouldReportImmediately(formattedError)) {
      await this.flushQueue()
    } else if (this.errorQueue.length >= this.config.batchSize) {
      await this.flushQueue()
    }
  }
  
  /**
   * 检查是否应该忽略错误
   * @param {Object} errorInfo - 错误信息
   * @returns {boolean}
   */
  shouldIgnoreError(errorInfo) {
    const { filters } = this.config
    
    // 检查错误类型
    if (filters.ignoreTypes.includes(errorInfo.type)) {
      return true
    }
    
    // 检查错误消息
    const message = errorInfo.message || ''
    if (filters.ignoreMessages.some(keyword => message.includes(keyword))) {
      return true
    }
    
    // 检查错误级别
    const levelPriority = {
      'debug': 0,
      'info': 1,
      'warn': 2,
      'error': 3,
      'critical': 4
    }
    
    const errorLevel = levelPriority[errorInfo.level] || 0
    const minLevel = levelPriority[filters.minLevel] || 0
    
    return errorLevel < minLevel
  }
  
  /**
   * 检查是否需要立即上报
   * @param {Object} errorInfo - 错误信息
   * @returns {boolean}
   */
  shouldReportImmediately(errorInfo) {
    // 关键错误立即上报
    return errorInfo.level === 'critical' || errorInfo.critical
  }
  
  /**
   * 格式化错误信息
   * @param {Object} errorInfo - 原始错误信息
   * @returns {Object}
   */
  formatError(errorInfo) {
    const systemInfo = uni.getSystemInfoSync()
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    
    return {
      // 错误基本信息
      id: this.generateErrorId(),
      timestamp: new Date().toISOString(),
      message: errorInfo.message || 'Unknown error',
      stack: errorInfo.stack,
      type: errorInfo.type || 'unknown',
      level: errorInfo.level || 'error',
      
      // 错误上下文
      context: {
        page: errorInfo.context?.page || currentPage?.route,
        component: errorInfo.context?.component,
        action: errorInfo.context?.action,
        api: errorInfo.context?.api,
        ...errorInfo.context
      },
      
      // 用户信息
      user: {
        id: uni.getStorageSync('userId') || 'anonymous',
        token: uni.getStorageSync('token') ? 'exists' : 'none'
      },
      
      // 设备信息
      device: {
        platform: systemInfo.platform,
        system: systemInfo.system,
        version: systemInfo.version,
        model: systemInfo.model,
        brand: systemInfo.brand,
        screenWidth: systemInfo.screenWidth,
        screenHeight: systemInfo.screenHeight,
        pixelRatio: systemInfo.pixelRatio
      },
      
      // 应用信息
      app: {
        version: systemInfo.appVersion || '1.0.0',
        platform: systemInfo.uniPlatform,
        runtime: systemInfo.uniRuntimeVersion
      },
      
      // 网络信息
      network: {
        type: errorInfo.networkType || 'unknown',
        isOnline: errorInfo.isOnline !== false
      },
      
      // 页面栈信息
      pageStack: pages.map(page => ({
        route: page.route,
        options: page.options
      })),
      
      // 性能信息
      performance: {
        memory: this.getMemoryInfo(),
        timing: errorInfo.timing
      }
    }
  }
  
  /**
   * 生成错误ID
   * @returns {string}
   */
  generateErrorId() {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
  
  /**
   * 获取内存信息
   * @returns {Object}
   */
  getMemoryInfo() {
    try {
      // 尝试获取内存信息（部分平台支持）
      const perf = typeof performance !== 'undefined' ? performance : null;
      if (perf && perf.memory) {
        return {
          usedJSHeapSize: perf.memory.usedJSHeapSize,
          totalJSHeapSize: perf.memory.totalJSHeapSize,
          jsHeapSizeLimit: perf.memory.jsHeapSizeLimit
        }
      }
      
      // 如果不支持 performance.memory，尝试获取系统内存信息
      const systemInfo = uni.getSystemInfoSync();
      if (systemInfo && systemInfo.memory) {
        return {
          totalMemory: systemInfo.memory,
          availableMemory: systemInfo.availableMemory
        }
      }
    } catch (e) {
      // 忽略获取内存信息的错误
      console.warn('获取内存信息失败:', e)
    }
    
    // 如果无法获取内存信息，返回基本系统信息
    return {
      platform: uni.getSystemInfoSync().platform,
      available: false
    }
  }
  
  /**
   * 刷新队列，上报所有错误
   */
  async flushQueue() {
    if (this.isReporting || this.errorQueue.length === 0) {
      return
    }
    
    this.isReporting = true
    
    try {
      const errors = [...this.errorQueue]
      this.errorQueue = []
      
      await this.sendErrors(errors)
    } catch (error) {
      console.error('错误上报失败:', error)
      // 将错误重新加入队列（限制重试次数）
      this.handleReportFailure(error)
    } finally {
      this.isReporting = false
    }
  }
  
  /**
   * 发送错误到服务器
   * @param {Array} errors - 错误列表
   */
  async sendErrors(errors) {
    const reportData = {
      errors,
      reportTime: new Date().toISOString(),
      batchId: this.generateBatchId()
    }
    
    // 使用uni.request发送数据
    return new Promise((resolve, reject) => {
      uni.request({
        url: this.config.reportUrl,
        method: 'POST',
        data: reportData,
        header: {
          'Content-Type': 'application/json',
          'Authorization': (() => {
            const token = uni.getStorageSync('token') || uni.getStorageSync('Authorization')
            return token ? (token.startsWith('Bearer ') ? token : `Bearer ${token}`) : ''
          })()
        },
        timeout: 10000,
        success: (res) => {
          if (res.statusCode === 200) {
            console.log(`成功上报 ${errors.length} 个错误`)
            resolve(res.data)
          } else {
            reject(new Error(`上报失败: ${res.statusCode}`))
          }
        },
        fail: (error) => {
          reject(new Error(`网络错误: ${error.errMsg}`))
        }
      })
    })
  }
  
  /**
   * 生成批次ID
   * @returns {string}
   */
  generateBatchId() {
    return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
  
  /**
   * 处理上报失败
   * @param {Error} error - 上报错误
   */
  handleReportFailure(error) {
    // 这里可以实现重试逻辑或本地存储
    console.warn('错误上报失败，将在下次尝试:', error.message)
  }
  
  /**
   * 启动批量上报定时器
   */
  startBatchTimer() {
    if (this.batchTimer) {
      clearInterval(this.batchTimer)
    }
    
    this.batchTimer = setInterval(() => {
      if (this.errorQueue.length > 0) {
        this.flushQueue()
      }
    }, this.config.batchTimeout)
  }
  
  /**
   * 停止批量上报定时器
   */
  stopBatchTimer() {
    if (this.batchTimer) {
      clearInterval(this.batchTimer)
      this.batchTimer = null
    }
  }
  
  /**
   * 设置生命周期监听器
   */
  setupLifecycleListeners() {
    // 应用隐藏时上报剩余错误
    uni.onAppHide(() => {
      this.flushQueue()
    })
    
    // 应用显示时重新启动定时器
    uni.onAppShow(() => {
      this.startBatchTimer()
    })
  }
  
  /**
   * 更新配置
   * @param {Object} newConfig - 新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig }
    
    // 重新启动定时器
    this.startBatchTimer()
  }
  
  /**
   * 获取队列状态
   * @returns {Object}
   */
  getQueueStatus() {
    return {
      queueLength: this.errorQueue.length,
      isReporting: this.isReporting,
      config: this.config
    }
  }
  
  /**
   * 清空队列
   */
  clearQueue() {
    this.errorQueue = []
  }
  
  /**
   * 销毁错误上报器
   */
  destroy() {
    // 上报剩余错误
    this.flushQueue()
    
    // 停止定时器
    this.stopBatchTimer()
    
    // 清空队列
    this.clearQueue()
  }
}

// 创建全局错误上报器实例
const errorReporter = new ErrorReporter()

// 导出错误上报器
export default errorReporter

// 导出便捷方法
export const reportError = (errorInfo) => errorReporter.report(errorInfo)
export const flushErrorQueue = () => errorReporter.flushQueue()
export const getReporterStatus = () => errorReporter.getQueueStatus()
export const updateReporterConfig = (config) => errorReporter.updateConfig(config)