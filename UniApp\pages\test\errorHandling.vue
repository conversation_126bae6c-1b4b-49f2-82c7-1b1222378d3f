<template>
	<view class="error-test-container">
		<view class="test-header">
			<text class="test-title">错误处理系统测试</text>
			<text class="test-subtitle">测试各种错误场景和处理机制</text>
		</view>
		
		<!-- 错误状态显示 -->
		<view v-if="hasError" class="error-status">
			<view class="error-info">
				<text class="error-title">{{ errorInfo.title }}</text>
				<text class="error-message">{{ errorInfo.message }}</text>
			</view>
			<view class="error-actions">
				<view class="retry-btn" @tap="$retryLastOperation" v-if="canRetry">
					<text>重试</text>
				</view>
				<view class="clear-btn" @tap="$clearError">
					<text>清除错误</text>
				</view>
			</view>
		</view>
		
		<!-- 测试按钮组 -->
		<view class="test-buttons">
			<view class="test-section">
				<text class="section-title">网络错误测试</text>
				<view class="button-group">
					<view class="test-btn" @tap="testNetworkError">
						<text>网络连接失败</text>
					</view>
					<view class="test-btn" @tap="testTimeoutError">
						<text>请求超时</text>
					</view>
					<view class="test-btn" @tap="testServerError">
						<text>服务器错误</text>
					</view>
				</view>
			</view>
			
			<view class="test-section">
				<text class="section-title">业务错误测试</text>
				<view class="button-group">
					<view class="test-btn" @tap="testBusinessError">
						<text>业务逻辑错误</text>
					</view>
					<view class="test-btn" @tap="testValidationError">
						<text>数据验证错误</text>
					</view>
					<view class="test-btn" @tap="testPermissionError">
						<text>权限错误</text>
					</view>
				</view>
			</view>
			
			<view class="test-section">
				<text class="section-title">系统错误测试</text>
				<view class="button-group">
					<view class="test-btn" @tap="testSystemError">
						<text>系统异常</text>
					</view>
					<view class="test-btn" @tap="testCriticalError">
						<text>严重错误</text>
					</view>
					<view class="test-btn" @tap="testUnknownError">
						<text>未知错误</text>
					</view>
				</view>
			</view>
			
			<view class="test-section">
				<text class="section-title">安全执行测试</text>
				<view class="button-group">
					<view class="test-btn" @tap="testSafeExecute">
						<text>安全执行</text>
					</view>
					<view class="test-btn" @tap="testSafeExecuteWithRetry">
						<text>带重试的安全执行</text>
					</view>
					<view class="test-btn" @tap="testNetworkCheck">
						<text>网络检查执行</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 错误日志显示 -->
		<view class="error-logs">
			<view class="logs-header">
				<text class="logs-title">错误日志</text>
				<view class="logs-actions">
					<text class="clear-logs" @tap="clearLogs">清空</text>
				</view>
			</view>
			<scroll-view class="logs-content" scroll-y>
				<view 
					class="log-item" 
					v-for="(log, index) in errorLogs" 
					:key="index"
					:class="`log-${log.level}`"
				>
					<view class="log-header">
						<text class="log-time">{{ formatTime(log.timestamp) }}</text>
						<text class="log-level">{{ log.level.toUpperCase() }}</text>
					</view>
					<text class="log-message">{{ log.message }}</text>
					<text v-if="log.context" class="log-context">上下文: {{ log.context }}</text>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import errorMixin from '@/mixins/errorMixin'
// import { handleError, handleNetworkError, handleBusinessError, handleCriticalError } from '@/utils/errorHandler'

export default {
	mixins: [errorMixin],
	
	data() {
		return {
			errorLogs: []
		}
	},
	
	onLoad() {
		// 监听错误事件，记录到日志
		this.setupErrorLogging()
	},
	
	onUnload() {
		// 页面卸载时清理资源
		// 清空错误日志数组，释放内存
		this.errorLogs = []
		
		// 恢复原始的console.error方法
		if (this.originalConsoleError) {
			console.error = this.originalConsoleError
			this.originalConsoleError = null
		}
		
		// 重置测试计数器
		if (this.testCount) {
			this.testCount = 0
		}
		
		// 隐藏loading状态
		uni.hideLoading()
	},

	methods: {
		// 设置错误日志记录
		setupErrorLogging() {
			// 保存原始的console.error方法
			this.originalConsoleError = console.error
			// 重写console.error来捕获错误日志
			console.error = (...args) => {
				this.originalConsoleError.apply(console, args)
				this.addErrorLog('error', args.join(' '))
			}
		},
		
		// 添加错误日志
		addErrorLog(level, message, context = '') {
			this.errorLogs.unshift({
				level,
				message,
				context,
				timestamp: new Date()
			})
			
			// 限制日志数量
			if (this.errorLogs.length > 50) {
				this.errorLogs = this.errorLogs.slice(0, 50)
			}
		},
		
		// 清空日志
		clearLogs() {
			this.errorLogs = []
			this.$showSuccess('日志已清空')
		},
		
		// 测试网络错误
		async testNetworkError() {
			this.addErrorLog('info', '开始测试网络错误', '网络连接失败模拟')
			
			const error = new Error('网络连接失败')
			error.code = 'NETWORK_ERROR'
			error.type = 'network'
			
			await this.$handleNetworkError(error, {
				context: '测试网络错误',
				showRetry: true,
				retryAction: () => this.testNetworkError()
			})
		},
		
		// 测试超时错误
		async testTimeoutError() {
			this.addErrorLog('info', '开始测试超时错误', '请求超时模拟')
			
			const error = new Error('请求超时')
			error.code = 'TIMEOUT'
			error.type = 'timeout'
			
			await this.$handleNetworkError(error, {
				context: '测试超时错误',
				showRetry: true
			})
		},
		
		// 测试服务器错误
		async testServerError() {
			this.addErrorLog('info', '开始测试服务器错误', '500服务器内部错误')
			
			const error = new Error('服务器内部错误')
			error.code = 500
			error.type = 'server'
			
			await this.$handleNetworkError(error, {
				context: '测试服务器错误'
			})
		},
		
		// 测试业务错误
		async testBusinessError() {
			this.addErrorLog('info', '开始测试业务错误', '业务逻辑验证失败')
			
			const error = new Error('用户权限不足')
			error.code = 'PERMISSION_DENIED'
			error.type = 'business'
			
			await this.$handleBusinessError(error, {
				context: '测试业务错误'
			})
		},
		
		// 测试验证错误
		async testValidationError() {
			this.addErrorLog('info', '开始测试验证错误', '数据格式验证失败')
			
			const error = new Error('输入数据格式不正确')
			error.code = 'VALIDATION_ERROR'
			error.type = 'validation'
			error.details = {
				field: 'email',
				message: '邮箱格式不正确'
			}
			
			await this.$handleError(error, {
				context: '测试验证错误'
			})
		},
		
		// 测试权限错误
		async testPermissionError() {
			this.addErrorLog('info', '开始测试权限错误', '访问权限不足')
			
			const error = new Error('访问被拒绝')
			error.code = 403
			error.type = 'permission'
			
			await this.$handleError(error, {
				context: '测试权限错误'
			})
		},
		
		// 测试系统错误
		async testSystemError() {
			this.addErrorLog('info', '开始测试系统错误', '系统异常模拟')
			
			const error = new Error('系统内部异常')
			error.code = 'SYSTEM_ERROR'
			error.type = 'system'
			error.stack = 'Error: 系统内部异常\n    at testSystemError'
			
			await this.$handleError(error, {
				context: '测试系统错误'
			})
		},
		
		// 测试严重错误
		async testCriticalError() {
			this.addErrorLog('info', '开始测试严重错误', '严重系统故障')
			
			const error = new Error('严重系统故障，需要立即处理')
			error.code = 'CRITICAL_ERROR'
			error.type = 'critical'
			error.level = 'critical'
			
			await this.$handleCriticalError(error, {
				context: '测试严重错误'
			})
		},
		
		// 测试未知错误
		async testUnknownError() {
			this.addErrorLog('info', '开始测试未知错误', '未分类错误类型')
			
			const error = new Error('未知的错误类型')
			// 不设置type，让系统自动分类
			
			await this.$handleError(error, {
				context: '测试未知错误'
			})
		},
		
		// 测试安全执行
		async testSafeExecute() {
			this.addErrorLog('info', '开始测试安全执行', '模拟可能失败的操作')
			
			await this.$safeExecute(async () => {
				// 模拟一个可能失败的操作
				if (Math.random() > 0.5) {
					throw new Error('随机失败的操作')
				}
				
				this.$showSuccess('安全执行成功')
			}, {
				context: '测试安全执行'
			})
		},
		
		// 测试带重试的安全执行
		async testSafeExecuteWithRetry() {
			this.addErrorLog('info', '开始测试带重试的安全执行', '最多重试3次')
			
			this.testCount++
			
			await this.$safeExecuteWithRetry(async () => {
				// 模拟前几次失败，第3次成功
				if (this.testCount < 3) {
					throw new Error(`第${this.testCount}次尝试失败`)
				}
				
				this.$showSuccess('重试执行成功')
				this.testCount = 0
			}, {
				context: '测试带重试的安全执行',
				maxRetries: 3,
				retryDelay: 1000
			})
		},
		
		// 测试网络检查执行
		async testNetworkCheck() {
			this.addErrorLog('info', '开始测试网络检查执行', '检查网络状态后执行')
			
			await this.$checkNetworkAndExecute(async () => {
				this.$showSuccess('网络检查通过，操作执行成功')
			}, {
				context: '测试网络检查执行'
			})
		},
		
		// 格式化时间
		formatTime(date) {
			const now = new Date(date)
			const hours = String(now.getHours()).padStart(2, '0')
			const minutes = String(now.getMinutes()).padStart(2, '0')
			const seconds = String(now.getSeconds()).padStart(2, '0')
			return `${hours}:${minutes}:${seconds}`
		}
	}
}
</script>

<style lang="scss" scoped>
.error-test-container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.test-header {
	text-align: center;
	margin-bottom: 40rpx;
	
	.test-title {
		display: block;
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 10rpx;
	}
	
	.test-subtitle {
		display: block;
		font-size: 28rpx;
		color: #666;
	}
}

.error-status {
	background-color: #fff2f0;
	border: 2rpx solid #ffccc7;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 30rpx;
	
	.error-info {
		margin-bottom: 20rpx;
		
		.error-title {
			display: block;
			font-size: 32rpx;
			font-weight: bold;
			color: #cf1322;
			margin-bottom: 8rpx;
		}
		
		.error-message {
			display: block;
			font-size: 28rpx;
			color: #a8071a;
		}
	}
	
	.error-actions {
		display: flex;
		gap: 20rpx;
		
		.retry-btn, .clear-btn {
			padding: 12rpx 24rpx;
			border-radius: 8rpx;
			font-size: 26rpx;
			text-align: center;
		}
		
		.retry-btn {
			background-color: #1890ff;
			color: white;
		}
		
		.clear-btn {
			background-color: #f5f5f5;
			color: #666;
		}
	}
}

.test-buttons {
	margin-bottom: 40rpx;
}

.test-section {
	margin-bottom: 30rpx;
	
	.section-title {
		display: block;
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
	}
}

.button-group {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
}

.test-btn {
	padding: 20rpx 30rpx;
	background-color: #ffffff;
	border: 2rpx solid #d9d9d9;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333;
	text-align: center;
	min-width: 200rpx;
	transition: all 0.3s;
	
	&:active {
		background-color: #f0f0f0;
		border-color: #1890ff;
		color: #1890ff;
	}
}

.error-logs {
	background-color: #ffffff;
	border-radius: 12rpx;
	padding: 20rpx;
	height: 600rpx;
	
	.logs-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
		padding-bottom: 16rpx;
		border-bottom: 2rpx solid #f0f0f0;
		
		.logs-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
		}
		
		.clear-logs {
			font-size: 26rpx;
			color: #1890ff;
			padding: 8rpx 16rpx;
			border-radius: 6rpx;
			background-color: #f0f8ff;
		}
	}
	
	.logs-content {
		height: 500rpx;
	}
}

.log-item {
	padding: 16rpx;
	margin-bottom: 12rpx;
	border-radius: 8rpx;
	border-left: 6rpx solid #d9d9d9;
	
	&.log-info {
		background-color: #f6ffed;
		border-left-color: #52c41a;
	}
	
	&.log-error {
		background-color: #fff2f0;
		border-left-color: #ff4d4f;
	}
	
	&.log-warn {
		background-color: #fffbe6;
		border-left-color: #faad14;
	}
	
	.log-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 8rpx;
		
		.log-time {
			font-size: 24rpx;
			color: #999;
		}
		
		.log-level {
			font-size: 22rpx;
			padding: 4rpx 8rpx;
			border-radius: 4rpx;
			background-color: #f0f0f0;
			color: #666;
		}
	}
	
	.log-message {
		display: block;
		font-size: 26rpx;
		color: #333;
		margin-bottom: 6rpx;
		word-break: break-all;
	}
	
	.log-context {
		display: block;
		font-size: 24rpx;
		color: #666;
		font-style: italic;
	}
}
</style>