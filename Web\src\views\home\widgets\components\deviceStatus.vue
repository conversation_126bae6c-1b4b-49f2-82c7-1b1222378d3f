<template>
	<el-card shadow="hover" header="设备状态统计" v-loading="loading">
		<div class="device-status-container">
			<div class="status-overview">
				<div class="status-item online">
					<div class="status-icon">
						<el-icon><ele-CircleCheckFilled /></el-icon>
					</div>
					<div class="status-info">
						<div class="status-number">{{ deviceStats.online }}</div>
						<div class="status-label">在线设备</div>
					</div>
				</div>
				<div class="status-item offline">
					<div class="status-icon">
						<el-icon><ele-CircleCloseFilled /></el-icon>
					</div>
					<div class="status-info">
						<div class="status-number">{{ deviceStats.offline }}</div>
						<div class="status-label">离线设备</div>
					</div>
				</div>
				<div class="status-item fault">
					<div class="status-icon">
						<el-icon><ele-WarningFilled /></el-icon>
					</div>
					<div class="status-info">
						<div class="status-number">{{ deviceStats.fault }}</div>
						<div class="status-label">故障设备</div>
					</div>
				</div>
				<div class="status-item total">
					<div class="status-icon">
						<el-icon><ele-Monitor /></el-icon>
					</div>
					<div class="status-info">
						<div class="status-number">{{ deviceStats.total }}</div>
						<div class="status-label">设备总数</div>
					</div>
				</div>
			</div>
			<div class="device-type-chart">
				<scEcharts ref="deviceTypeChart" height="200px" :option="deviceTypeOption"></scEcharts>
			</div>
		</div>
	</el-card>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import scEcharts from '/@/components/scEcharts/index.vue';
import { useDeviceApi } from '/@/api-services/device';

// 组件元信息
const title = '设备状态统计';
const icon = 'ele-Monitor';
const description = '实时显示设备在线状态和类型分布';

const loading = ref(true);
const refreshTimer = ref<NodeJS.Timeout | null>(null);

// 设备统计数据
const deviceStats = reactive({
	online: 0,
	offline: 0,
	fault: 0,
	total: 0
});

// 设备类型分布图表配置
const deviceTypeOption = ref({
	tooltip: {
		trigger: 'item',
		formatter: '{a} <br/>{b}: {c} ({d}%)'
	},
	legend: {
		orient: 'vertical',
		left: 'left',
		textStyle: {
			fontSize: 12
		}
	},
	series: [
		{
			name: '设备类型',
			type: 'pie',
			radius: ['40%', '70%'],
			avoidLabelOverlap: false,
			label: {
				show: false,
				position: 'center'
			},
			emphasis: {
				label: {
					show: true,
					fontSize: '18',
					fontWeight: 'bold'
				}
			},
			labelLine: {
				show: false
			},
			data: []
		}
	]
});

// 获取设备统计数据
const fetchDeviceStats = async () => {
	try {
		// 获取设备状态统计
		const deviceApi = useDeviceApi();
		const statsResponse = await deviceApi.getStats();
		if (statsResponse.data) {
			deviceStats.online = statsResponse.data.online || 0;
			deviceStats.offline = statsResponse.data.offline || 0;
			deviceStats.fault = statsResponse.data.fault || 0;
			deviceStats.total = statsResponse.data.total || 0;
		}

		// 获取设备类型分布
		const typeResponse = await deviceApi.getTypeDistribution();
		if (typeResponse.data && Array.isArray(typeResponse.data)) {
			deviceTypeOption.value.series[0].data = typeResponse.data.map((item: any) => ({
				value: item.count,
				name: item.deviceType
			}));
		}
	} catch (error) {
		console.error('获取设备统计数据失败:', error);
		// 使用模拟数据
		deviceStats.online = 156;
		deviceStats.offline = 12;
		deviceStats.fault = 3;
		deviceStats.total = 171;
		
		deviceTypeOption.value.series[0].data = [
			{ value: 89, name: 'LED灯' },
			{ value: 45, name: '节能灯' },
			{ value: 23, name: '智能开关' },
			{ value: 14, name: '传感器' }
		];
	} finally {
		loading.value = false;
	}
};

// 启动定时刷新
const startRefresh = () => {
	refreshTimer.value = setInterval(() => {
		fetchDeviceStats();
	}, 30000); // 30秒刷新一次
};

// 停止定时刷新
const stopRefresh = () => {
	if (refreshTimer.value) {
		clearInterval(refreshTimer.value);
		refreshTimer.value = null;
	}
};

onMounted(() => {
	fetchDeviceStats();
	startRefresh();
});

onUnmounted(() => {
	stopRefresh();
});

// 导出组件元信息
defineExpose({
	title,
	icon,
	description
});
</script>

<style scoped lang="scss">
.device-status-container {
	display: flex;
	flex-direction: column;
	gap: 20px;
}

.status-overview {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
	gap: 16px;
	margin-bottom: 20px;
}

.status-item {
	display: flex;
	align-items: center;
	padding: 16px;
	border-radius: 8px;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	transition: all 0.3s ease;
	cursor: pointer;

	&:hover {
		transform: translateY(-2px);
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	}

	&.online {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
		
		.status-icon {
			color: #52c41a;
		}
	}

	&.offline {
		background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
		color: white;
		
		.status-icon {
			color: #ff4d4f;
		}
	}

	&.fault {
		background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
		color: #d46b08;
		
		.status-icon {
			color: #faad14;
		}
	}

	&.total {
		background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
		color: #1890ff;
		
		.status-icon {
			color: #1890ff;
		}
	}
}

.status-icon {
	font-size: 24px;
	margin-right: 12px;
	display: flex;
	align-items: center;
}

.status-info {
	flex: 1;
}

.status-number {
	font-size: 24px;
	font-weight: bold;
	line-height: 1;
	margin-bottom: 4px;
}

.status-label {
	font-size: 12px;
	opacity: 0.8;
	line-height: 1;
}

.device-type-chart {
	min-height: 200px;
}

@media (max-width: 768px) {
	.status-overview {
		grid-template-columns: repeat(2, 1fr);
	}
	
	.status-item {
		padding: 12px;
	}
	
	.status-number {
		font-size: 20px;
	}
	
	.status-icon {
		font-size: 20px;
		margin-right: 8px;
	}
}
</style>