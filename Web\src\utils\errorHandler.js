/**
 * 统一错误处理工具
 * 提供标准化的错误处理和日志记录功能
 */

import { ElMessage } from 'element-plus'

/**
 * 错误类型枚举
 */
export const ErrorTypes = {
  API_ERROR: 'API_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  BUSINESS_ERROR: 'BUSINESS_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
}

/**
 * 错误级别枚举
 */
export const ErrorLevels = {
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error',
  FATAL: 'fatal'
}

/**
 * 统一错误处理类
 */
class ErrorHandler {
  constructor() {
    this.errorQueue = []
    this.maxQueueSize = 100
  }

  /**
   * 处理错误
   * @param {Error|string} error - 错误对象或错误信息
   * @param {Object} options - 配置选项
   * @param {string} options.type - 错误类型
   * @param {string} options.level - 错误级别
   * @param {string} options.context - 错误上下文
   * @param {boolean} options.showMessage - 是否显示用户提示
   * @param {string} options.userMessage - 用户友好的错误信息
   * @param {boolean} options.logToConsole - 是否输出到控制台
   * @param {Function} options.onError - 自定义错误处理回调
   */
  handle(error, options = {}) {
    const {
      type = ErrorTypes.UNKNOWN_ERROR,
      level = ErrorLevels.ERROR,
      context = '',
      showMessage = true,
      userMessage = '',
      logToConsole = true,
      onError = null
    } = options

    // 构建错误信息对象
    const errorInfo = {
      timestamp: new Date().toISOString(),
      type,
      level,
      context,
      message: error?.message || error || '未知错误',
      stack: error?.stack || '',
      userAgent: navigator.userAgent,
      url: window.location.href
    }

    // 添加到错误队列
    this.addToQueue(errorInfo)

    // 控制台日志
    if (logToConsole) {
      this.logToConsole(errorInfo)
    }

    // 显示用户提示
    if (showMessage) {
      this.showUserMessage(errorInfo, userMessage)
    }

    // 执行自定义错误处理
    if (onError && typeof onError === 'function') {
      try {
        onError(errorInfo)
      } catch (callbackError) {
        console.error('错误处理回调执行失败:', callbackError)
      }
    }

    return errorInfo
  }

  /**
   * 添加错误到队列
   */
  addToQueue(errorInfo) {
    this.errorQueue.push(errorInfo)
    
    // 限制队列大小
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift()
    }
  }

  /**
   * 输出到控制台
   */
  logToConsole(errorInfo) {
    const { level, context, message, type } = errorInfo
    const logMessage = `[${type}] ${context ? `${context}: ` : ''}${message}`
    
    switch (level) {
      case ErrorLevels.INFO:
        console.info(logMessage)
        break
      case ErrorLevels.WARN:
        console.warn(logMessage)
        break
      case ErrorLevels.ERROR:
        console.error(logMessage)
        break
      case ErrorLevels.FATAL:
        console.error(`🚨 FATAL: ${logMessage}`)
        break
      default:
        console.log(logMessage)
    }
  }

  /**
   * 显示用户提示
   */
  showUserMessage(errorInfo, customMessage) {
    const message = customMessage || this.getDefaultUserMessage(errorInfo.type)
    
    switch (errorInfo.level) {
      case ErrorLevels.INFO:
        ElMessage.info(message)
        break
      case ErrorLevels.WARN:
        ElMessage.warning(message)
        break
      case ErrorLevels.ERROR:
      case ErrorLevels.FATAL:
        ElMessage.error(message)
        break
      default:
        ElMessage(message)
    }
  }

  /**
   * 获取默认用户提示信息
   */
  getDefaultUserMessage(type) {
    const messages = {
      [ErrorTypes.API_ERROR]: '服务器请求失败，请稍后重试',
      [ErrorTypes.VALIDATION_ERROR]: '输入数据格式不正确',
      [ErrorTypes.NETWORK_ERROR]: '网络连接异常，请检查网络设置',
      [ErrorTypes.PERMISSION_ERROR]: '权限不足，无法执行此操作',
      [ErrorTypes.BUSINESS_ERROR]: '操作失败，请检查输入信息',
      [ErrorTypes.UNKNOWN_ERROR]: '操作失败，请稍后重试'
    }
    
    return messages[type] || messages[ErrorTypes.UNKNOWN_ERROR]
  }

  /**
   * 获取错误队列
   */
  getErrorQueue() {
    return [...this.errorQueue]
  }

  /**
   * 清空错误队列
   */
  clearErrorQueue() {
    this.errorQueue = []
  }

  /**
   * 获取错误统计
   */
  getErrorStats() {
    const stats = {
      total: this.errorQueue.length,
      byType: {},
      byLevel: {},
      recent: this.errorQueue.slice(-10)
    }

    this.errorQueue.forEach(error => {
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1
      stats.byLevel[error.level] = (stats.byLevel[error.level] || 0) + 1
    })

    return stats
  }
}

// 创建全局错误处理器实例
const errorHandler = new ErrorHandler()

/**
 * 便捷的错误处理函数
 */
export const handleError = (error, options = {}) => {
  return errorHandler.handle(error, options)
}

/**
 * API错误处理
 */
export const handleApiError = (error, context = '', userMessage = '') => {
  return handleError(error, {
    type: ErrorTypes.API_ERROR,
    context,
    userMessage
  })
}

/**
 * 验证错误处理
 */
export const handleValidationError = (error, context = '', userMessage = '') => {
  return handleError(error, {
    type: ErrorTypes.VALIDATION_ERROR,
    level: ErrorLevels.WARN,
    context,
    userMessage
  })
}

/**
 * 网络错误处理
 */
export const handleNetworkError = (error, context = '', userMessage = '') => {
  return handleError(error, {
    type: ErrorTypes.NETWORK_ERROR,
    context,
    userMessage
  })
}

/**
 * 权限错误处理
 */
export const handlePermissionError = (error, context = '', userMessage = '') => {
  return handleError(error, {
    type: ErrorTypes.PERMISSION_ERROR,
    context,
    userMessage
  })
}

/**
 * 业务错误处理
 */
export const handleBusinessError = (error, context = '', userMessage = '') => {
  return handleError(error, {
    type: ErrorTypes.BUSINESS_ERROR,
    context,
    userMessage
  })
}

/**
 * 静默错误处理（不显示用户提示）
 */
export const handleSilentError = (error, context = '') => {
  return handleError(error, {
    context,
    showMessage: false
  })
}

export default errorHandler