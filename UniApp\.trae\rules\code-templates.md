---
description: Uni-App X 代码模板
globs: *.uvue,*.uts
alwaysApply: false
---

# 代码模板

## 基础页面模板

### 简单页面模板
```uvue
<template>
  <cl-page>
    <view class="container">
      <text class="title">页面标题</text>
    </view>
  </cl-page>
</template>

<script lang="ts" setup>
// 页面逻辑
</script>

<style>
.container {
  padding: 20px;
}

.title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}
</style>
```

### 带滚动的页面模板
```uvue
<template>
  <cl-page>
    <!-- #ifdef APP -->
    <scroll-view class="scroll-container" scroll-y="true">
    <!-- #endif -->
      <view class="content">
        <text class="title">可滚动内容</text>
        <!-- 页面内容 -->
      </view>
    <!-- #ifdef APP -->
    </scroll-view>
    <!-- #endif -->
  </cl-page>
</template>

<script lang="ts" setup>
// 页面逻辑
</script>

<style>
.scroll-container {
  flex: 1;
}

.content {
  padding: 20px;
}

.title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}
</style>
```

### 列表页面模板
```uvue
<template>
  <cl-page>
    <list-view class="list-container" @scrolltolower="loadMore">
      <list-item v-for="(item, index) in list" :key="index" class="list-item">
        <view class="item-content">
          <text class="item-title">{{ item.title }}</text>
          <text class="item-desc">{{ item.description }}</text>
        </view>
      </list-item>
    </list-view>
  </cl-page>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

type ListItem = {
  title: string
  description: string
}

const list = ref<ListItem[]>([])

// 加载更多数据
const loadMore = () => {
  // 加载更多逻辑
}

// 初始化数据
const initData = () => {
  // 初始化逻辑
}

// 页面加载时执行
initData()
</script>

<style>
.list-container {
  flex: 1;
}

.list-item {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.item-content {
  flex-direction: column;
}

.item-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.item-desc {
  font-size: 14px;
  color: #666;
}
</style>
```

## 组件模板

### 基础组件模板
```uvue
<template>
  <view class="custom-component">
    <text class="component-text">{{ title }}</text>
    <slot></slot>
  </view>
</template>

<script lang="ts" setup>
type Props = {
  title: string
}

const props = defineProps<Props>()

type Emits = {
  click: []
}

const emit = defineEmits<Emits>()

const handleClick = () => {
  emit('click')
}
</script>

<style>
.custom-component {
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.component-text {
  font-size: 16px;
  color: #333;
}
</style>
```

## UTS 插件模板

### 基础 UTS 插件
```uts
// 插件接口定义
export interface PluginOptions {
  timeout?: number
  callback?: (result: string) => void
}

// 插件主要功能
export function pluginMethod(options: PluginOptions): string {
  const { timeout = 3000, callback } = options
  
  try {
    // 插件逻辑实现
    const result = "插件执行结果"
    
    if (callback != null) {
      callback(result)
    }
    
    return result
  } catch (error: any) {
    console.error('插件执行错误:', error)
    return ""
  }
}

// 默认导出
export default {
  pluginMethod
}
```