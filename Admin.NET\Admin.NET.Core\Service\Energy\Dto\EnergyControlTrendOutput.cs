// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core.Service;

/// <summary>
/// 控制趋势输出参数
/// </summary>
public class EnergyControlTrendOutput
{
    /// <summary>
    /// 时间点
    /// </summary>
    public DateTime Time { get; set; }

    /// <summary>
    /// 时间点
    /// </summary>
    public DateTime TimePoint { get; set; }

    /// <summary>
    /// 总控制次数
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 控制次数
    /// </summary>
    public int ControlCount { get; set; }

    /// <summary>
    /// 成功次数
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败次数
    /// </summary>
    public int FailedCount { get; set; }

    /// <summary>
    /// 失败次数
    /// </summary>
    public int FailCount { get; set; }

    /// <summary>
    /// 手动控制次数
    /// </summary>
    public int ManualCount { get; set; }

    /// <summary>
    /// 自动控制次数
    /// </summary>
    public int AutoCount { get; set; }

    /// <summary>
    /// 成功率(%)
    /// </summary>
    public decimal SuccessRate { get; set; }

    /// <summary>
    /// 平均响应时间(毫秒)
    /// </summary>
    public decimal? AvgResponseTime { get; set; }

    /// <summary>
    /// 平均执行时长(毫秒)
    /// </summary>
    public decimal AvgExecuteDuration { get; set; }

    /// <summary>
    /// 开灯次数
    /// </summary>
    public int TurnOnCount { get; set; }

    /// <summary>
    /// 关灯次数
    /// </summary>
    public int TurnOffCount { get; set; }

    /// <summary>
    /// 调光次数
    /// </summary>
    public int DimCount { get; set; }

    /// <summary>
    /// 场景执行次数
    /// </summary>
    public int SceneCount { get; set; }

    /// <summary>
    /// 设备数量
    /// </summary>
    public int DeviceCount { get; set; }

    /// <summary>
    /// 活跃设备数量
    /// </summary>
    public int ActiveDeviceCount { get; set; }
}