/**
 * 网络请求工具
 * 集成缓存、离线处理、错误处理、JWT验证等功能
 */

// 导入错误处理器
import errorHandler, { handleNetworkError, handleBusinessError } from './errorHandler.js'
// 导入缓存管理器
import { cacheManager, offlineManager } from './cache.js'

/**
 * JWT解码工具类
 */
class JWTDecoder {
  /**
   * Base64URL解码
   * @param {string} str Base64URL编码的字符串
   * @returns {string} 解码后的字符串
   */
  static base64UrlDecode(str) {
    // 补齐Base64填充字符
    str += new Array(5 - str.length % 4).join('=')
    // 替换Base64URL字符
    str = str.replace(/-/g, '+').replace(/_/g, '/')
    try {
      // 在uni-app中使用Buffer或自定义base64解码
      return decodeURIComponent(escape(atob(str)))
    } catch (e) {
      console.error('Base64解码失败:', e)
      return null
    }
  }

  /**
   * 解码JWT token
   * @param {string} token JWT token
   * @returns {object|null} 解码后的payload或null
   */
  static decode(token) {
    if (!token || typeof token !== 'string') {
      return null
    }

    // 移除Bearer前缀
    const cleanToken = token.replace(/^Bearer\s+/i, '')
    
    // JWT由三部分组成，用.分隔
    const parts = cleanToken.split('.')
    if (parts.length !== 3) {
      console.error('无效的JWT格式')
      return null
    }

    try {
      // 解码payload部分（第二部分）
      const payload = this.base64UrlDecode(parts[1])
      if (!payload) {
        return null
      }
      return JSON.parse(payload)
    } catch (error) {
      console.error('JWT解码失败:', error)
      return null
    }
  }

  /**
   * 验证token是否过期
   * @param {string} token JWT token
   * @returns {boolean} true表示有效，false表示过期或无效
   */
  static isTokenValid(token) {
    const payload = this.decode(token)
    if (!payload) {
      return false
    }

    // 检查过期时间
    if (payload.exp) {
      const currentTime = Math.floor(Date.now() / 1000)
      if (currentTime >= payload.exp) {
        console.log('Token已过期')
        return false
      }
    }

    return true
  }
}

class RequestManager {
  constructor() {
    // 根据平台设置不同的API地址
    this.baseURL = this.getBaseURL()
    this.timeout = 10000
    this.retryCount = 3
    this.retryDelay = 1000
    
    // 绑定方法以确保 this 上下文正确
    this.request = this.request.bind(this)
    this.checkCache = this.checkCache.bind(this)
    this.get = this.get.bind(this)
    this.post = this.post.bind(this)
    this.put = this.put.bind(this)
    this.delete = this.delete.bind(this)
  }

  /**
   * 根据运行平台获取API基础URL
   * @returns {string} API基础URL
   */
  getBaseURL() {
    // 使用运行时检测替代条件编译
    if (typeof plus !== 'undefined') {
      // APP-PLUS 环境
      return 'http://*************:5005'
    } else {
      // 其他环境（H5、小程序等）
      return 'http://localhost:5005'
    }
  }

  /**
   * 发起请求
   * @param {object} options 请求配置
   * @returns {Promise} 请求结果
   */
  async request(options) {
    const {
      url,
      method = 'GET',
      data = {},
      header = {},
      cache = false,
      cacheTime = 30 * 60 * 1000, // 30分钟
      offline = false,
      retry = true
    } = options

    // 构建完整URL
    let fullUrl = url.startsWith('http') ? url : this.baseURL + url
    //fullUrl = fullUrl.replace('*************', 'localhost') // 强制替换为localhost进行测试
    
    // 请求拦截器 - 处理认证
    const processedOptions = await this.requestInterceptor({
      url: fullUrl,
      method,
      data,
      header,
      cache,
      cacheTime,
      offline,
      retry
    })
    
    // 生成缓存键
    const cacheKey = this.generateCacheKey(method, processedOptions.url, processedOptions.data)
    
    // 检查缓存 - 只有当cache为true且为GET请求时才使用缓存
    if (processedOptions.cache === true && processedOptions.method === 'GET') {
      const cachedData = this.checkCache(cacheKey, { cacheTime: processedOptions.cacheTime })
      if (cachedData) {
        console.log('从缓存获取数据:', cacheKey)
        return Promise.resolve(cachedData)
      }
    }

    // 检查网络状态
    const isOnline = await offlineManager.checkNetworkStatus()
    
    if (!isOnline) {
      // 离线状态处理
      if (processedOptions.offline && processedOptions.method === 'GET') {
        const offlineData = offlineManager.getOfflineData(cacheKey.replace('api_', ''))
        if (offlineData) {
          console.log('从离线数据获取:', cacheKey)
          return Promise.resolve({
            code: 200,
            message: '离线数据',
            data: offlineData,
            fromOffline: true
          })
        } else {
          // 没有离线数据时的处理
          console.warn('离线状态且无可用离线数据:', processedOptions.url)
          return Promise.reject({
            code: -1,
            message: '网络不可用，且无离线数据',
            error: new Error('Network unavailable and no offline data')
          })
        }
      }
      
      if (processedOptions.method !== 'GET') {
        // 非GET请求添加到同步队列
        offlineManager.addToSyncQueue({
          url: processedOptions.url,
          method: processedOptions.method,
          data: processedOptions.data,
          header: processedOptions.header
        })
        
        return Promise.reject({
          code: 'OFFLINE',
          message: '当前网络不可用，操作已保存，将在网络恢复后同步'
        })
      }
      
      return Promise.reject({
        code: 'NETWORK_ERROR',
        message: '网络连接失败，请检查网络设置'
      })
    }

    // 发起网络请求
    return this.makeRequest({
      url: processedOptions.url,
      method: processedOptions.method,
      data: processedOptions.data,
      header: processedOptions.header,
      timeout: this.timeout
    }, processedOptions.retry ? this.retryCount : 0)
      .then(response => {
        // 处理响应
        const result = this.handleResponse(response)
        
        // 缓存处理 - 只有当cache为true且为GET请求且响应成功时才缓存
        if (processedOptions.cache === true && processedOptions.method === 'GET' && result.code === 200) {
          const cacheTime = processedOptions.cacheTime || 30 * 60 * 1000 // 默认30分钟
          const success = cacheManager.set(cacheKey.replace('api_', ''), result.data, cacheTime)
          if (success) {
            console.log('数据已缓存:', cacheKey, '过期时间:', cacheTime + 'ms')
          }
        }
        
        // 离线数据保存 - 只有当offline为true且为GET请求且响应成功时才保存
        if (processedOptions.offline === true && processedOptions.method === 'GET' && result.code === 200) {
          const success = offlineManager.saveOfflineData(cacheKey.replace('api_', ''), result.data)
          if (success) {
            console.log('离线数据已保存:', cacheKey)
          }
        }
        
        return result
      })
      .catch(error => {
        // 使用统一错误处理系统
        const context = {
          url: processedOptions.url,
          method: processedOptions.method,
          data: processedOptions.data,
          timestamp: new Date().toISOString()
        }
        
        const standardError = this.handleError(error)
        
        // 使用统一错误处理器
        const errorContext = {
          url: processedOptions.url,
          method: processedOptions.method,
          status: error.statusCode,
          timestamp: Date.now()
        }
        
        // 根据错误类型进行分类处理
        if (standardError.code === 404) {
          console.warn('API端点不存在:', context.url)
          errorHandler.handleError(new Error('请求的资源不存在'), errorContext, {
            showToast: true,
            enableRetry: false
          })
        } else if (standardError.code === 'TIMEOUT') {
          errorHandler.handleError(new Error('请求超时'), errorContext, {
            showToast: true,
            enableRetry: true,
            retryCallback: () => this.makeRequest(processedOptions)
          })
        } else if (standardError.code === 'NETWORK_ERROR') {
          errorHandler.handleError(new Error('网络请求失败'), errorContext, {
            showToast: true,
            enableRetry: true,
            retryCallback: () => this.makeRequest(processedOptions)
          })
        } else {
          errorHandler.handleError(error, errorContext, {
            showToast: true,
            enableRetry: false
          })
        }
        
        throw standardError
      })
  }

  /**
   * 请求拦截器 - 处理认证和其他预处理
   * @param {object} options 请求配置
   * @returns {Promise<object>} 处理后的请求配置
   */
  async requestInterceptor(options) {
    const processedOptions = { ...options }
    
    // console.log('🔍 请求拦截器被调用，URL:', processedOptions.url)
    
    // 设置默认请求头
    processedOptions.header = {
      'Content-Type': 'application/json',
      ...processedOptions.header
    }
    
    // 检查是否为需要排除认证的接口
    const excludeAuthUrls = [
      '/api/sysAuth/login',
      '/api/sysAuth/captcha',
      '/api/sysAuth/register',
      '/api/sysAuth/sendSmsCode',
      '/api/sysAuth/sendEmailCode',
      '/api/sysAuth/verifyResetCode',
      '/api/sysAuth/resetPassword',
      '/api/sysAuth/sendResetCode'
    ]
    
    const isExcludeAuth = excludeAuthUrls.some(excludeUrl => 
      processedOptions.url.includes(excludeUrl)
    )
    
    // console.log('🔐 是否排除认证:', isExcludeAuth)
    
    // 如果不是排除的接口，则添加认证头并验证token
    if (!isExcludeAuth) {
      const authToken = this.getAuthToken()
      // console.log('🎫 获取到的token:', authToken ? '存在' : '不存在')
      
      // 检查token是否存在
      if (!authToken) {
        // console.warn('❌ 未找到认证token，跳转到登录页面')
        this.redirectToLogin()
        throw {
          code: 'NO_TOKEN',
          message: '未登录，请先登录'
        }
      }
      
      // 验证token有效性
      if (!JWTDecoder.isTokenValid(authToken)) {
        // console.warn('❌ Token已过期或无效，跳转到登录页面')
        this.clearAuthData()
        this.redirectToLogin()
        throw {
          code: 'TOKEN_EXPIRED',
          message: 'Token已过期，请重新登录'
        }
      }
      
      // 添加认证头
      processedOptions.header.Authorization = authToken
      // console.log('✅ 已添加Authorization头到请求')
    }
    
    // console.log('📤 最终请求头:', processedOptions.header)
    return processedOptions
  }
  
  /**
   * 清除认证数据
   */
  clearAuthData() {
    try {
      uni.removeStorageSync('token')
      uni.removeStorageSync('Authorization')
      uni.removeStorageSync('userInfo')
      // console.log('认证数据已清除')
    } catch (error) {
      // console.error('清除认证数据失败:', error)
    }
  }
  
  /**
   * 跳转到登录页面
   */
  redirectToLogin() {
    try {
      // 获取当前页面路径，用于登录后跳转回来
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const currentRoute = currentPage ? currentPage.route : ''
      
      // 保存当前页面路径
      if (currentRoute && currentRoute !== 'pages/user/login') {
        uni.setStorageSync('redirectUrl', '/' + currentRoute)
      }
      
      // 跳转到登录页面
      uni.reLaunch({
        url: '/pages/user/login',
        fail: (error) => {
          console.error('跳转登录页面失败:', error)
          // 如果reLaunch失败，尝试使用navigateTo
          uni.navigateTo({
            url: '/pages/user/login',
            fail: (navError) => {
              console.error('导航到登录页面失败:', navError)
            }
          })
        }
      })
    } catch (error) {
      console.error('跳转登录页面异常:', error)
    }
  }

  /**
   * 实际发起请求
   * @param {object} options 请求配置
   * @param {number} retryCount 重试次数
   * @returns {Promise} 请求结果
   */
  makeRequest(options, retryCount = 0) {
    const startTime = Date.now()
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    // 记录请求开始日志
    console.log(`[${requestId}] 请求开始:`, {
      method: options.method,
      url: options.url,
      timestamp: new Date().toISOString()
    })
    return new Promise((resolve, reject) => {
      uni.request({
        ...options,
        success: (response) => {
          const endTime = Date.now()
          const duration = endTime - startTime
          
          // 记录请求成功日志
          // console.log(`[${requestId}] 请求成功:`, {
          //   status: response.statusCode,
          //   duration: `${duration}ms`,
          //   timestamp: new Date().toISOString()
          // })
          
          // 性能监控：记录慢请求
          // if (duration > 3000) {
          //   console.warn(`[${requestId}] 慢请求警告: ${duration}ms`, {
          //     method: options.method,
          //     url: options.url
          //   })
          // }
          
          resolve(response)
        },
        fail: (error) => {
          const endTime = Date.now()
          const duration = endTime - startTime
          
          // 记录请求失败日志
          console.error(`[${requestId}] 请求失败:`, {
            error: error.errMsg || error.message,
            duration: `${duration}ms`,
            timestamp: new Date().toISOString()
          })
          
          if (retryCount > 0) {
            console.log(`请求失败，${this.retryDelay}ms后重试，剩余重试次数: ${retryCount}`)
            setTimeout(() => {
              this.makeRequest(options, retryCount - 1)
                .then(resolve)
                .catch(reject)
            }, this.retryDelay)
          } else {
            reject(error)
          }
        }
      })
    })
  }

  /**
   * 处理响应数据
   * @param {object} response 响应对象
   * @returns {object} 处理后的数据
   */
  handleResponse(response) {
    const { statusCode, data } = response
    
    if (statusCode === 200) {
      // 根据后端API格式调整
      if (data && typeof data === 'object') {
        return {
          code: data.code || 200,
          message: data.message || 'success',
          data: data.data || data
        }
      }
      return {
        code: 200,
        message: 'success',
        data
      }
    }
    
    throw {
      code: statusCode,
      message: `HTTP ${statusCode}`,
      data: null
    }
  }

  /**
   * 处理错误
   * @param {object} error 错误对象
   * @returns {object} 标准化错误
   */
  handleError(error) {
    if (error.code) {
      return error
    }
    
    // 网络错误
    if (error.errMsg) {
      if (error.errMsg.includes('timeout')) {
        return {
          code: 'TIMEOUT',
          message: '请求超时，请重试'
        }
      }
      if (error.errMsg.includes('fail')) {
        return {
          code: 'NETWORK_ERROR',
          message: '网络连接失败'
        }
      }
    }
    
    return {
      code: 'UNKNOWN_ERROR',
      message: '未知错误'
    }
  }

  /**
   * 生成缓存键
   * @param {string} method 请求方法
   * @param {string} url 请求URL
   * @param {object} data 请求数据
   * @param {Object} headers 请求头（可选）
   * @returns {string} 缓存键
   */
  generateCacheKey(method, url, data, headers = {}) {
    // 排除敏感信息的请求头
    const safeHeaders = {}
    const excludeHeaders = ['authorization', 'cookie', 'x-csrf-token']
    
    Object.keys(headers).forEach(key => {
      if (!excludeHeaders.includes(key.toLowerCase())) {
        safeHeaders[key] = headers[key]
      }
    })
    
    const cacheData = {
      method,
      url: url.split('?')[0], // 移除URL中的查询参数
      data: this.sortObject(data),
      headers: this.sortObject(safeHeaders)
    }
    
    const cacheStr = JSON.stringify(cacheData)
    return `api_${this.hashCode(cacheStr)}`
  }
  
  /**
   * 对象属性排序（确保缓存键一致性）
   * @param {Object} obj 要排序的对象
   * @returns {Object} 排序后的对象
   */
  sortObject(obj) {
    if (!obj || typeof obj !== 'object') return obj
    
    const sorted = {}
    Object.keys(obj).sort().forEach(key => {
      sorted[key] = obj[key]
    })
    return sorted
  }

  /**
   * 简单哈希函数
   * @param {string} str 字符串
   * @returns {string} 哈希值
   */
  hashCode(str) {
    let hash = 0
    if (str.length === 0) return hash
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash).toString()
  }

  /**
   * 检查缓存是否存在且未过期
   * @param {string} key 缓存键
   * @param {object} options 缓存选项
   * @returns {any|null} 缓存数据或null
   */
  checkCache(key, options = {}) {
    const { cacheTime = 30 * 60 * 1000 } = options
    
    try {
      // 使用缓存管理器检查缓存
      const cachedData = cacheManager.get(key.replace('api_', ''))
      if (cachedData) {
        return {
          code: 200,
          message: 'success',
          data: cachedData,
          fromCache: true
        }
      }
      return null
    } catch (error) {
      console.error('检查缓存失败:', error)
      return null
    }
  }

  /**
   * 获取认证token
   * @returns {string} token
   */
  getAuthToken() {
    try {
      // 优先从Authorization获取完整的Bearer token
      let authHeader = uni.getStorageSync('Authorization')
      if (authHeader) {
        return authHeader
      }
      
      // 如果没有Authorization，则从token获取并拼接Bearer
      const token = uni.getStorageSync('token')
      return token ? `Bearer ${token}` : ''
    } catch (err) {
      return ''
    }
  }

  /**
   * GET请求
   * @param {string} url 请求URL
   * @param {object} params 请求参数
   * @param {object} options 其他配置
   * @returns {Promise} 请求结果
   */
  get(url, params = {}, options = {}) {
    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&')
    
    const fullUrl = queryString ? `${url}?${queryString}` : url
    
    // 为验证码请求添加时间戳确保唯一性
    const finalUrl = url.includes('/captcha') ? 
      `${fullUrl}${fullUrl.includes('?') ? '&' : '?'}_t=${Date.now()}` : fullUrl
    
    return this.request({
      url: finalUrl,
      method: 'GET',
      cache: true,
      offline: true,
      ...options  // options会覆盖默认的cache和offline设置
    })
  }

  /**
   * POST请求
   * @param {string} url 请求URL
   * @param {object} data 请求数据
   * @param {object} options 其他配置
   * @returns {Promise} 请求结果
   */
  post(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...options
    })
  }

  /**
   * PUT请求
   * @param {string} url 请求URL
   * @param {object} data 请求数据
   * @param {object} options 其他配置
   * @returns {Promise} 请求结果
   */
  put(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...options
    })
  }

  /**
   * DELETE请求
   * @param {string} url 请求URL
   * @param {object} options 其他配置
   * @returns {Promise} 请求结果
   */
  delete(url, options = {}) {
    return this.request({
      url,
      method: 'DELETE',
      ...options
    })
  }

  /**
   * 处理网络错误
   * @param {Object} errorInfo 错误信息
   */
  handleNetworkError(errorInfo) {
    console.error('网络错误:', errorInfo)
    
    // 使用统一错误处理器
    if (typeof handleNetworkError !== 'undefined') {
      handleNetworkError(errorInfo, {})
    } else {
      // 降级处理
      uni.showToast({
        title: '网络连接异常，请检查网络设置',
        icon: 'none',
        duration: 3000
      })
    }
  }

  /**
   * 处理业务错误
   * @param {Object} errorInfo 错误信息
   */
  handleBusinessError(errorInfo) {
    console.error('业务错误:', errorInfo)
    
    // 使用统一错误处理器
    if (typeof handleBusinessError !== 'undefined') {
      handleBusinessError(errorInfo, {
        showToast: true,
        enableRetry: false
      })
    } else {
      // 降级处理
      uni.showToast({
        title: errorInfo.message || '操作失败',
        icon: 'none',
        duration: 3000
      })
    }
  }

  /**
   * 同步离线数据
   * @returns {Promise} 同步结果
   */
  async syncOfflineData() {
    const isOnline = await offlineManager.checkNetworkStatus()
    if (!isOnline) {
      return { success: false, message: '网络不可用' }
    }

    const syncQueue = offlineManager.getSyncQueue()
    if (syncQueue.length === 0) {
      return { success: true, message: '没有需要同步的数据' }
    }

    let successCount = 0
    let failCount = 0

    for (const item of syncQueue) {
      try {
        await this.request({
          url: item.url,
          method: item.method,
          data: item.data,
          header: item.header,
          retry: false
        })
        successCount++
      } catch (error) {
        console.error('同步失败:', item, error)
        failCount++
      }
    }

    // 清空同步队列
    if (successCount > 0) {
      offlineManager.clearSyncQueue()
    }

    return {
      success: failCount === 0,
      message: `同步完成：成功${successCount}个，失败${failCount}个`,
      successCount,
      failCount
    }
  }
}

// 创建实例
const requestManager = new RequestManager()

// 导出便捷方法
export const get = (url, params, options) => requestManager.get(url, params, options)
export const post = (url, data, options) => requestManager.post(url, data, options)
export const put = (url, data, options) => requestManager.put(url, data, options)
export const del = (url, options) => requestManager.delete(url, options)

export default requestManager
export { RequestManager }