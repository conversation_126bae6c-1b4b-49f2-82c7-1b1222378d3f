/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 故障排行输出参数
 * @export
 * @interface EnergyFaultRankOutput
 */
export interface EnergyFaultRankOutput {
    /**
     * 设备ID
     * @type {number}
     * @memberof EnergyFaultRankOutput
     */
    deviceId?: number;
    /**
     * 设备编码
     * @type {string}
     * @memberof EnergyFaultRankOutput
     */
    deviceCode?: string | null;
    /**
     * 设备名称
     * @type {string}
     * @memberof EnergyFaultRankOutput
     */
    deviceName?: string | null;
    /**
     * 设备类型
     * @type {string}
     * @memberof EnergyFaultRankOutput
     */
    deviceType?: string | null;
    /**
     * 设备位置
     * @type {string}
     * @memberof EnergyFaultRankOutput
     */
    deviceLocation?: string | null;
    /**
     * 故障总数
     * @type {number}
     * @memberof EnergyFaultRankOutput
     */
    totalFaultCount?: number;
    /**
     * 待处理数量
     * @type {number}
     * @memberof EnergyFaultRankOutput
     */
    pendingCount?: number;
    /**
     * 已修复数量
     * @type {number}
     * @memberof EnergyFaultRankOutput
     */
    repairedCount?: number;
    /**
     * 平均修复时间(小时)
     * @type {number}
     * @memberof EnergyFaultRankOutput
     */
    avgRepairTime?: number;
    /**
     * 位置
     * @type {string}
     * @memberof EnergyFaultRankOutput
     */
    location?: string | null;
    /**
     * 故障次数
     * @type {number}
     * @memberof EnergyFaultRankOutput
     */
    faultCount?: number;
    /**
     * 排名
     * @type {number}
     * @memberof EnergyFaultRankOutput
     */
    rank?: number;
    /**
     * 最近故障时间
     * @type {Date}
     * @memberof EnergyFaultRankOutput
     */
    lastFaultTime?: Date | null;
    /**
     * 故障率
     * @type {number}
     * @memberof EnergyFaultRankOutput
     */
    faultRate?: number;
}
