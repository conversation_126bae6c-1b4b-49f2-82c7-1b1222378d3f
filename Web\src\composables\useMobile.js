/**
 * 移动端适配组合式函数
 * 提供Vue 3组件中使用的移动端适配功能
 */

import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import {
  isMobile,
  isTablet,
  isIOS,
  isAndroid,
  getScreenSize,
  addTouchSupport,
  addPullToRefresh,
  addLoadMore,
  addOrientationListener,
  optimizeTableForMobile,
  optimizeDialogForMobile,
  debounce,
  throttle
} from '@/utils/mobile'

/**
 * 设备检测组合函数
 */
export function useDeviceDetection() {
  const deviceInfo = ref({
    isMobile: isMobile(),
    isTablet: isTablet(),
    isIOS: isIOS(),
    isAndroid: isAndroid(),
    screenSize: getScreenSize()
  })

  const updateDeviceInfo = () => {
    deviceInfo.value = {
      isMobile: isMobile(),
      isTablet: isTablet(),
      isIOS: isIOS(),
      isAndroid: isAndroid(),
      screenSize: getScreenSize()
    }
  }

  onMounted(() => {
    window.addEventListener('resize', debounce(updateDeviceInfo, 300))
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateDeviceInfo)
  })

  return {
    deviceInfo,
    updateDeviceInfo
  }
}

/**
 * 下拉刷新组合函数
 */
export function usePullToRefresh(containerRef, refreshCallback) {
  const isRefreshing = ref(false)
  let cleanup = null
  let isDestroyed = false

  const setupPullToRefresh = async () => {
    try {
      await nextTick()
      // 检查组件是否已被销毁
      if (isDestroyed) return
      
      // 添加null检查，确保containerRef和其value存在
      if (containerRef && containerRef.value && isMobile()) {
        cleanup = addPullToRefresh(containerRef.value, async () => {
          // 再次检查组件状态
          if (isDestroyed) return
          
          isRefreshing.value = true
          try {
            if (refreshCallback && typeof refreshCallback === 'function') {
              await refreshCallback()
            }
          } finally {
            // 确保在设置状态前检查组件是否仍然存在
            if (!isDestroyed) {
              isRefreshing.value = false
            }
          }
        })
      }
    } catch (error) {
      console.warn('设置下拉刷新时出错:', error)
    }
  }

  onMounted(setupPullToRefresh)

  onUnmounted(() => {
    isDestroyed = true
    try {
      if (cleanup && typeof cleanup === 'function') {
        cleanup()
      }
    } catch (error) {
      console.warn('清理下拉刷新时出错:', error)
    }
    cleanup = null
  })

  return {
    isRefreshing,
    setupPullToRefresh
  }
}

/**
 * 上拉加载更多组合函数
 */
export function useLoadMore(containerRef, loadMoreCallback, options = {}) {
  const isLoading = ref(false)
  const hasMore = ref(true)
  let cleanup = null

  const setupLoadMore = async () => {
    await nextTick()
    if (containerRef.value && isMobile()) {
      cleanup = addLoadMore(containerRef.value, async () => {
        isLoading.value = true
        try {
          const result = await loadMoreCallback()
          if (result === false) {
            hasMore.value = false
          }
          return result
        } finally {
          isLoading.value = false
        }
      }, options)
    }
  }

  const resetLoadMore = () => {
    hasMore.value = true
    isLoading.value = false
  }

  onMounted(setupLoadMore)

  onUnmounted(() => {
    if (cleanup) {
      cleanup()
    }
  })

  return {
    isLoading,
    hasMore,
    setupLoadMore,
    resetLoadMore
  }
}

/**
 * 触摸事件组合函数
 */
export function useTouchEvents(elementRef, options = {}) {
  const touchData = ref({
    startX: 0,
    startY: 0,
    deltaX: 0,
    deltaY: 0,
    isTouching: false
  })

  let cleanup = null

  const setupTouchEvents = async () => {
    await nextTick()
    if (elementRef.value && isMobile()) {
      cleanup = addTouchSupport(elementRef.value, {
        onTouchStart: (e) => {
          touchData.value.isTouching = true
          touchData.value.startX = e.touches[0].clientX
          touchData.value.startY = e.touches[0].clientY
          options.onTouchStart?.(e, touchData.value)
        },
        onTouchMove: (e, delta) => {
          touchData.value.deltaX = delta.deltaX
          touchData.value.deltaY = delta.deltaY
          options.onTouchMove?.(e, touchData.value)
        },
        onTouchEnd: (e) => {
          touchData.value.isTouching = false
          options.onTouchEnd?.(e, touchData.value)
          // 重置delta
          setTimeout(() => {
            touchData.value.deltaX = 0
            touchData.value.deltaY = 0
          }, 100)
        },
        preventDefault: options.preventDefault
      })
    }
  }

  onMounted(setupTouchEvents)

  onUnmounted(() => {
    if (cleanup) {
      cleanup()
    }
  })

  return {
    touchData,
    setupTouchEvents
  }
}

/**
 * 横屏监听组合函数
 */
export function useOrientation() {
  const orientation = ref({
    isLandscape: window.innerWidth > window.innerHeight,
    width: window.innerWidth,
    height: window.innerHeight
  })

  let cleanup = null

  onMounted(() => {
    cleanup = addOrientationListener((data) => {
      orientation.value = data
    })
  })

  onUnmounted(() => {
    if (cleanup) {
      cleanup()
    }
  })

  return {
    orientation
  }
}

/**
 * 移动端表格优化组合函数
 */
export function useMobileTable(tableRef) {
  let isDestroyed = false
  
  const optimizeTable = async () => {
    try {
      await nextTick()
      // 检查组件是否已被销毁
      if (isDestroyed) return
      
      // 添加null检查，确保tableRef和其value存在
      if (tableRef && tableRef.value) {
        optimizeTableForMobile(tableRef.value)
      }
    } catch (error) {
      console.warn('优化移动端表格时出错:', error)
    }
  }

  onMounted(optimizeTable)
  
  onUnmounted(() => {
    isDestroyed = true
  })

  return {
    optimizeTable
  }
}

/**
 * 移动端对话框优化组合函数
 */
export function useMobileDialog(dialogRef) {
  const optimizeDialog = async () => {
    await nextTick()
    if (dialogRef.value) {
      optimizeDialogForMobile(dialogRef.value)
    }
  }

  return {
    optimizeDialog
  }
}

/**
 * 移动端响应式布局组合函数
 */
export function useResponsiveLayout() {
  const { deviceInfo } = useDeviceDetection()
  const { orientation } = useOrientation()

  const layoutConfig = ref({
    columns: 1,
    gutter: 16,
    cardSpan: 24
  })

  const updateLayout = () => {
    const { screenSize } = deviceInfo.value
    const { isLandscape } = orientation.value

    if (screenSize === 'xs') {
      layoutConfig.value = {
        columns: 1,
        gutter: 8,
        cardSpan: 24
      }
    } else if (screenSize === 'sm') {
      layoutConfig.value = {
        columns: isLandscape ? 2 : 1,
        gutter: 12,
        cardSpan: isLandscape ? 12 : 24
      }
    } else if (screenSize === 'md') {
      layoutConfig.value = {
        columns: isLandscape ? 3 : 2,
        gutter: 16,
        cardSpan: isLandscape ? 8 : 12
      }
    } else {
      layoutConfig.value = {
        columns: 4,
        gutter: 20,
        cardSpan: 6
      }
    }
  }

  onMounted(() => {
    updateLayout()
    window.addEventListener('resize', debounce(updateLayout, 300))
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateLayout)
  })

  return {
    layoutConfig,
    updateLayout
  }
}

/**
 * 移动端搜索优化组合函数
 */
export function useMobileSearch(searchCallback, delay = 500) {
  const searchText = ref('')
  const isSearching = ref(false)

  const debouncedSearch = debounce(async (value) => {
    if (!value.trim()) return
    
    isSearching.value = true
    try {
      await searchCallback(value)
    } finally {
      isSearching.value = false
    }
  }, delay)

  const handleSearch = (value) => {
    searchText.value = value
    debouncedSearch(value)
  }

  const clearSearch = () => {
    searchText.value = ''
    searchCallback('')
  }

  return {
    searchText,
    isSearching,
    handleSearch,
    clearSearch
  }
}

/**
 * 移动端性能优化组合函数
 */
export function useMobilePerformance() {
  const { deviceInfo } = useDeviceDetection()

  // 根据设备性能调整渲染策略
  const getRenderConfig = () => {
    const { isMobile, screenSize } = deviceInfo.value
    
    if (!isMobile) {
      return {
        pageSize: 20,
        enableVirtualScroll: false,
        enableLazyLoad: false
      }
    }

    if (screenSize === 'xs') {
      return {
        pageSize: 10,
        enableVirtualScroll: true,
        enableLazyLoad: true
      }
    }

    return {
      pageSize: 15,
      enableVirtualScroll: false,
      enableLazyLoad: true
    }
  }

  const throttledCallback = (callback, limit = 100) => {
    return throttle(callback, limit)
  }

  const debouncedCallback = (callback, wait = 300) => {
    return debounce(callback, wait)
  }

  return {
    getRenderConfig,
    throttledCallback,
    debouncedCallback
  }
}

export default {
  useDeviceDetection,
  usePullToRefresh,
  useLoadMore,
  useTouchEvents,
  useOrientation,
  useMobileTable,
  useMobileDialog,
  useResponsiveLayout,
  useMobileSearch,
  useMobilePerformance
}