/**
 * 能耗管理相关API
 */

import request from '../utils/request.js'
import { createCachedApi } from '../src/utils/apiCache.js' 

const BASE_URL = "/api/energyConsumption";


/**
 * 获取能耗概览数据
 * @param {Object} params 查询参数
 * @returns {Promise} 能耗概览数据
 */
export const getEnergyOverview = (params = {}) => {
  return request.get(`${BASE_URL}/overview`, params)
}

/**
 * 获取今日统计数据
 * @param {Object} params 查询参数
 * @returns {Promise} 今日统计数据
 */
export const getTodayStats = (params = {}) => {
  return request.get(`${BASE_URL}/today-stats`, params)
}

/**
 * 获取实时数据
 * @param {Object} params 查询参数
 * @returns {Promise} 实时数据
 */
export const getRealtimeData = (params = {}) => {
  return request.get(`${BASE_URL}/realtime`, params)
}

/**
 * 获取能耗统计数据
 * @param {Object} params 查询参数
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @returns {Promise} 能耗统计数据
 */
export const getEnergyStats = (params = {}) => {
  return request.get(`${BASE_URL}/stat`, params, {
    cache: true,
    cacheTime: 2 * 60 * 1000, // 2分钟缓存
    offline: true
  })
}

/**
 * 获取图表数据
 * @param {string} period 时间周期
 * @param {Object} params 查询参数
 * @returns {Promise} 图表数据
 */
export const getChartData = (period, params = {}) => {
  return request.get(`${BASE_URL}/chart/${period}`, params)
}

/**
 * 获取设备能耗排行
 * @param {string} period 时间周期
 * @param {Object} params 查询参数
 * @returns {Promise} 设备排行数据
 */
export const getDeviceRanking = (period, params = {}) => {
  return request.get(`${BASE_URL}/device-ranking/${period}`, params)
}

/**
 * 获取历史记录
 * @param {Object} params 查询参数
 * @returns {Promise} 历史记录数据
 */
export const getHistoryRecords = (params = {}) => {
  return request.get(`${BASE_URL}/history-records`, params)
}

/**
 * 应用节能建议
 * @param {string} tipId 建议ID
 * @param {Object} params 应用参数
 * @returns {Promise} 应用结果
 */
export const applyEnergyTip = (tipId, params = {}) => {
  return request.post(`${BASE_URL}/apply-tip/${tipId}`, params)
}

/**
 * 获取实时能耗数据
 * @param {Object} params 查询参数
 * @param {Array} params.deviceIds 设备ID列表
 * @param {string} params.groupId 设备分组ID
 * @returns {Promise} 实时能耗数据
 */
export const getRealTimeEnergy = (params = {}) => {
  return request.get(`${BASE_URL}/realtime`, params)
}

/**
 * 获取历史能耗数据
 * @param {Object} params 查询参数
 * @param {Array} params.deviceIds 设备ID列表
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @param {string} params.granularity 数据粒度 hour/day/week/month
 * @returns {Promise} 历史能耗数据
 */
export const getEnergyHistory = (params) => {
  return request.get(`${BASE_URL}/history`, params, {
    cache: true,
    cacheTime: 10 * 60 * 1000, // 10分钟缓存
    offline: true
  })
}

 

/**
 * 获取能耗排行榜
 * @param {Object} params 查询参数
 * @param {string} params.period 统计周期
 * @param {string} params.type 排行类型 device/group/area
 * @param {number} params.limit 返回数量限制
 * @returns {Promise} 能耗排行榜
 */
export const getEnergyRanking = (params = {}) => {
  return request.get(`${BASE_URL}/ranking`, params, {
    cache: true,
    cacheTime: 5 * 60 * 1000, // 5分钟缓存
    offline: true
  })
}

/**
 * 获取能耗趋势分析
 * @param {Object} params 查询参数
 * @param {Array} params.deviceIds 设备ID列表
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @param {string} params.compareType 对比类型 period/device
 * @returns {Promise} 能耗趋势数据
 */
export const getEnergyTrend = (params = {}) => {
  return request.get(`${BASE_URL}/trend`, params, {
    cache: true,
    cacheTime: 5 * 60 * 1000, // 5分钟缓存
    offline: true
  })
}

/**
 * 获取能耗对比分析
 * @param {Object} params 查询参数
 * @param {Array} params.deviceIds 设备ID列表
 * @param {string} params.currentPeriod 当前周期
 * @param {string} params.comparePeriod 对比周期
 * @returns {Promise} 能耗对比数据
 */
export const getEnergyComparison = (params = {}) => {
  return request.get(`${BASE_URL}/comparison`, params, {
    cache: true,
    cacheTime: 10 * 60 * 1000, // 10分钟缓存
    offline: true
  })
}

/**
 * 获取峰谷电分析
 * @param {Object} params 查询参数
 * @param {Array} params.deviceIds 设备ID列表
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @returns {Promise} 峰谷电分析数据
 */
export const getPeakValleyAnalysis = (params = {}) => {
  return request.get(`${BASE_URL}/peak-valley`, params, {
    cache: true,
    cacheTime: 10 * 60 * 1000, // 10分钟缓存
    offline: true
  })
}

/**
 * 获取碳排放数据
 * @param {Object} params 查询参数
 * @param {Array} params.deviceIds 设备ID列表
 * @param {string} params.period 统计周期
 * @returns {Promise} 碳排放数据
 */
export const getCarbonEmission = (params = {}) => {
  return request.get(`${BASE_URL}/carbon-emission`, params, {
    cache: true,
    cacheTime: 10 * 60 * 1000, // 10分钟缓存
    offline: true
  })
}

/**
 * 获取节能建议
 * @param {Object} params 查询参数
 * @param {Array} params.deviceIds 设备ID列表
 * @param {string} params.analysisType 分析类型
 * @returns {Promise} 节能建议列表
 */
export const getEnergySuggestions = (params = {}) => {
  return request.get(`${BASE_URL}/suggestions`, params, {
    cache: true,
    cacheTime: 30 * 60 * 1000, // 30分钟缓存
    offline: true
  })
}

/**
 * 设置能耗预警阈值
 * @param {Object} alertData 预警配置
 * @param {Array} alertData.deviceIds 设备ID列表
 * @param {number} alertData.dailyThreshold 日能耗阈值
 * @param {number} alertData.monthlyThreshold 月能耗阈值
 * @param {boolean} alertData.enabled 是否启用
 * @param {Array} alertData.notifyUsers 通知用户列表
 * @returns {Promise} 设置结果
 */
export const setEnergyAlert = (alertData) => {
  return request.post(`${BASE_URL}/alerts`, alertData, {
    cache: false,
    offline: false
  })
}

/**
 * 获取能耗预警列表
 * @param {Object} params 查询参数
 * @returns {Promise} 预警列表
 */
export const getEnergyAlerts = (params = {}) => {
  return request.get(`${BASE_URL}/alerts`, params, {
    cache: true,
    cacheTime: 1 * 60 * 1000, // 1分钟缓存
    offline: true
  })
}

/**
 * 更新能耗预警配置
 * @param {string} alertId 预警ID
 * @param {Object} alertData 预警配置
 * @returns {Promise} 更新结果
 */
export const updateEnergyAlert = (alertId, alertData) => {
  return request.put(`${BASE_URL}/alerts/${alertId}`, alertData, {
    cache: false,
    offline: false
  })
}

/**
 * 删除能耗预警
 * @param {string} alertId 预警ID
 * @returns {Promise} 删除结果
 */
export const deleteEnergyAlert = (alertId) => {
  return request.delete(`${BASE_URL}/alerts/${alertId}`, {
    cache: false,
    offline: false
  })
}

/**
 * 生成能耗报表
 * @param {Object} reportData 报表参数
 * @param {string} reportData.type 报表类型 daily/weekly/monthly/yearly
 * @param {Array} reportData.deviceIds 设备ID列表
 * @param {string} reportData.startTime 开始时间
 * @param {string} reportData.endTime 结束时间
 * @param {string} reportData.format 导出格式 pdf/excel
 * @returns {Promise} 报表生成结果
 */
export const getEnergyReport = (params) => {
  return request.get(`${BASE_URL}/report`, params, {
    cache: true,
    cacheTime: 15 * 60 * 1000, // 15分钟缓存
    offline: true
  })
}

/**
 * 获取报表列表
 * @param {Object} params 查询参数
 * @returns {Promise} 报表列表
 */
export const exportEnergyReport = (params) => {
  return request.post(`${BASE_URL}/export`, params, {
    cache: false,
    offline: false
  })
}

/**
 * 下载能耗报表
 * @param {string} reportId 报表ID
 * @returns {Promise} 下载链接
 */
export const downloadEnergyReport = (reportId) => {
  return request.get(`${BASE_URL}/reports/${reportId}/download`, {}, {
    cache: false,
    offline: false
  })
}

/**
 * 删除能耗报表
 * @param {string} reportId 报表ID
 * @returns {Promise} 删除结果
 */
export const deleteEnergyReport = (reportId) => {
  return request.delete(`${BASE_URL}/reports/${reportId}`, {
    cache: false,
    offline: false
  })
}

/**
 * 获取电费计算
 * @param {Object} params 查询参数
 * @param {Array} params.deviceIds 设备ID列表
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @param {string} params.tariffType 电价类型
 * @returns {Promise} 电费计算结果
 */
export const getElectricityBill = (params = {}) => {
  return request.get(`${BASE_URL}/electricity-bill`, params, {
    cache: true,
    cacheTime: 5 * 60 * 1000, // 5分钟缓存
    offline: true
  })
}

/**
 * 设置电价配置
 * @param {Object} tariffData 电价配置
 * @param {string} tariffData.name 电价名称
 * @param {string} tariffData.type 电价类型 fixed/tiered/peak-valley
 * @param {Array} tariffData.rates 电价费率
 * @param {boolean} tariffData.isDefault 是否默认
 * @returns {Promise} 设置结果
 */
export const setElectricityTariff = (tariffData) => {
  return request.post(`${BASE_URL}/tariffs`, tariffData, {
    cache: false,
    offline: false
  })
}

/**
 * 获取电价配置列表
 * @returns {Promise} 电价配置列表
 */
export const getElectricityTariffs = () => {
  return request.get(`${BASE_URL}/tariffs`, {}, {
    cache: true,
    cacheTime: 30 * 60 * 1000, // 30分钟缓存
    offline: true
  })
}

/**
 * 获取能效分析
 * @param {Object} params 查询参数
 * @param {Array} params.deviceIds 设备ID列表
 * @param {string} params.period 分析周期
 * @returns {Promise} 能效分析数据
 */
export const getEnergyEfficiency = (params = {}) => {
  return request.get(`${BASE_URL}/efficiency`, params, {
    cache: true,
    cacheTime: 10 * 60 * 1000, // 10分钟缓存
    offline: true
  })
}

/**
 * 获取负载分析
 * @param {Object} params 查询参数
 * @param {Array} params.deviceIds 设备ID列表
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @returns {Promise} 负载分析数据
 */
export const getLoadAnalysis = (params = {}) => {
  return request.get(`${BASE_URL}/load-analysis`, params, {
    cache: true,
    cacheTime: 10 * 60 * 1000, // 10分钟缓存
    offline: true
  })
}

/**
 * 设置节能目标
 * @param {Object} targetData 节能目标
 * @param {Array} targetData.deviceIds 设备ID列表
 * @param {number} targetData.targetReduction 目标节能比例
 * @param {string} targetData.period 目标周期
 * @param {string} targetData.description 目标描述
 * @returns {Promise} 设置结果
 */
export const setEnergySavingTarget = (targetData) => {
  return request.post(`${BASE_URL}/saving-targets`, targetData, {
    cache: false,
    offline: false
  })
}

/**
 * 获取节能目标完成情况
 * @param {Object} params 查询参数
 * @returns {Promise} 节能目标完成情况
 */
export const getEnergySavingProgress = (params = {}) => {
  return request.get(`${BASE_URL}/saving-progress`, params, {
    cache: true,
    cacheTime: 5 * 60 * 1000, // 5分钟缓存
    offline: true
  })
}

// 创建缓存版本的API函数
const cachedGetEnergyOverview = createCachedApi(getEnergyOverview, 'energy_overview', 2 * 60 * 1000) // 2分钟缓存
const cachedGetTodayStats = createCachedApi(getTodayStats, 'today_stats', 1 * 60 * 1000) // 1分钟缓存
const cachedGetRealtimeData = createCachedApi(getRealtimeData, 'realtime_data', 30 * 1000) // 30秒缓存
const cachedGetChartData = createCachedApi(getChartData, 'chart_data', 5 * 60 * 1000) // 5分钟缓存
const cachedGetDeviceRanking = createCachedApi(getDeviceRanking, 'device_ranking', 5 * 60 * 1000) // 5分钟缓存
const cachedGetHistoryRecords = createCachedApi(getHistoryRecords, 'history_records', 10 * 60 * 1000) // 10分钟缓存
const cachedGetRealTimeEnergy = createCachedApi(getRealTimeEnergy, 'realtime_energy', 30 * 1000) // 30秒缓存

const cachedGetEnergyRanking = createCachedApi(getEnergyRanking, 'energy_ranking', 5 * 60 * 1000) // 5分钟缓存
const cachedGetEnergyAlerts = createCachedApi(getEnergyAlerts, 'energy_alerts', 1 * 60 * 1000) // 1分钟缓存
const cachedGetEnergyReport = createCachedApi(getEnergyReport, 'energy_report', 15 * 60 * 1000) // 15分钟缓存
const cachedGetElectricityBill = createCachedApi(getElectricityBill, 'electricity_bill', 5 * 60 * 1000) // 5分钟缓存
const cachedGetElectricityTariffs = createCachedApi(getElectricityTariffs, 'electricity_tariffs', 30 * 60 * 1000) // 30分钟缓存
const cachedGetEnergyEfficiency = createCachedApi(getEnergyEfficiency, 'energy_efficiency', 10 * 60 * 1000) // 10分钟缓存
const cachedGetLoadAnalysis = createCachedApi(getLoadAnalysis, 'load_analysis', 10 * 60 * 1000) // 10分钟缓存
const cachedGetEnergySavingProgress = createCachedApi(getEnergySavingProgress, 'energy_saving_progress', 5 * 60 * 1000) // 5分钟缓存

// 为兼容性添加别名导出
export const getOverview = getEnergyOverview
export const getAlerts = getEnergyAlerts

// 默认导出所有能耗监控相关API
export default {
  // 原始API函数
  getEnergyOverview,
  getTodayStats,
  getRealtimeData,
  getChartData,
  getDeviceRanking,
  getHistoryRecords,
  applyEnergyTip,
  getRealTimeEnergy,
  getEnergyHistory,
  getEnergyStats,

  getEnergyRanking,
  getEnergyTrend,
  getEnergyComparison,
  getPeakValleyAnalysis,
  getCarbonEmission,
  getEnergySuggestions,
  setEnergyAlert,
  getEnergyAlerts,
  updateEnergyAlert,
  deleteEnergyAlert,
  getEnergyReport,
  exportEnergyReport,
  downloadEnergyReport,
  deleteEnergyReport,
  getElectricityBill,
  setElectricityTariff,
  getElectricityTariffs,
  getEnergyEfficiency,
  getLoadAnalysis,
  setEnergySavingTarget,
  getEnergySavingProgress,
  // 缓存版本API函数
  cachedGetEnergyOverview,
  cachedGetTodayStats,
  cachedGetRealtimeData,
  cachedGetChartData,
  cachedGetDeviceRanking,
  cachedGetHistoryRecords,
  cachedGetRealTimeEnergy,

  cachedGetEnergyRanking,
  cachedGetEnergyAlerts,
  cachedGetEnergyReport,
  cachedGetElectricityBill,
  cachedGetElectricityTariffs,
  cachedGetEnergyEfficiency,
  cachedGetLoadAnalysis,
  cachedGetEnergySavingProgress
}