<template>
  <view class="fault-list-container">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">故障管理</text>
    </view>

    <!-- 故障统计概览 -->
    <view class="stats-overview">
      <view class="stats-card">
        <text class="stats-number">{{ totalFaults }}</text>
        <text class="stats-label">总故障数</text>
      </view>
      <view class="stats-card pending">
        <text class="stats-number">{{ pendingFaults }}</text>
        <text class="stats-label">待处理</text>
      </view>
      <view class="stats-card processing">
        <text class="stats-number">{{ processingFaults }}</text>
        <text class="stats-label">处理中</text>
      </view>
      <view class="stats-card resolved">
        <text class="stats-number">{{ resolvedFaults }}</text>
        <text class="stats-label">已解决</text>
      </view>
    </view>

    <!-- 筛选条件 -->
    <view class="filter-section">
      <view class="filter-row">
        <picker :value="selectedStatus" :range="statusOptions" @change="onStatusChange">
          <view class="filter-item">
            <text class="filter-label">状态：</text>
            <text class="filter-value">{{ statusOptions[selectedStatus] }}</text>
            <text class="filter-arrow">▼</text>
          </view>
        </picker>
        
        <picker :value="selectedLevel" :range="levelOptions" @change="onLevelChange">
          <view class="filter-item">
            <text class="filter-label">级别：</text>
            <text class="filter-value">{{ levelOptions[selectedLevel] }}</text>
            <text class="filter-arrow">▼</text>
          </view>
        </picker>
      </view>
      
      <view class="filter-actions">
        <button class="filter-btn" @click="applyFilter">筛选</button>
        <button class="reset-btn" @click="resetFilter">重置</button>
      </view>
    </view>

    <!-- 批量操作 -->
    <view class="batch-actions" v-if="selectedFaults.length > 0">
      <text class="batch-text">已选择 {{ selectedFaults.length }} 项</text>
      <button class="batch-btn assign" @click="batchAssign">批量分配</button>
      <button class="batch-btn close" @click="batchClose">批量关闭</button>
    </view>

    <!-- 故障列表 -->
    <view class="fault-list">
      <view 
        class="fault-item" 
        v-for="fault in filteredFaults" 
        :key="fault.id"
        @click="viewDetail(fault.id)"
      >
        <view class="fault-header">
          <checkbox 
            :checked="selectedFaults.includes(fault.id)"
            @click.stop="toggleSelect(fault.id)"
            class="fault-checkbox"
          />
          <view class="fault-info">
            <text class="fault-title">{{ fault.title }}</text>
            <view class="fault-meta">
              <text class="fault-id">#{{ fault.id }}</text>
              <text class="fault-device">{{ fault.deviceName }}</text>
            </view>
          </view>
          <view class="fault-status" :class="fault.status">
            <text>{{ getStatusText(fault.status) }}</text>
          </view>
        </view>
        
        <view class="fault-content">
          <text class="fault-description">{{ fault.description }}</text>
          <view class="fault-details">
            <view class="detail-item">
              <text class="detail-label">级别：</text>
              <text class="detail-value" :class="fault.level">{{ getLevelText(fault.level) }}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">报告时间：</text>
              <text class="detail-value">{{ fault.reportTime }}</text>
            </view>
            <view class="detail-item" v-if="fault.assignee">
              <text class="detail-label">负责人：</text>
              <text class="detail-value">{{ fault.assignee }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" v-if="hasMore">
      <button class="load-more-btn" @click="loadMore" :loading="loading">加载更多</button>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-if="filteredFaults.length === 0">
      <text class="empty-text">暂无故障记录</text>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnload } from 'vue'

// 响应式数据
const faults = ref([])
const selectedFaults = ref([])
const selectedStatus = ref(0)
const selectedLevel = ref(0)
const loading = ref(false)
const hasMore = ref(true)

// 筛选选项
const statusOptions = ['全部状态', '待处理', '处理中', '已解决', '已关闭']
const levelOptions = ['全部级别', '低', '中', '高', '紧急']

// 计算属性
const totalFaults = computed(() => faults.value.length)
const pendingFaults = computed(() => faults.value.filter(f => f.status === 'pending').length)
const processingFaults = computed(() => faults.value.filter(f => f.status === 'processing').length)
const resolvedFaults = computed(() => faults.value.filter(f => f.status === 'resolved').length)

const filteredFaults = computed(() => {
  let result = faults.value
  
  if (selectedStatus.value > 0) {
    const statusMap = ['', 'pending', 'processing', 'resolved', 'closed']
    result = result.filter(f => f.status === statusMap[selectedStatus.value])
  }
  
  if (selectedLevel.value > 0) {
    const levelMap = ['', 'low', 'medium', 'high', 'critical']
    result = result.filter(f => f.level === levelMap[selectedLevel.value])
  }
  
  return result
})

// 方法
const loadFaults = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const mockFaults = [
      {
        id: 'F001',
        title: '设备离线故障',
        description: '办公区域A-001设备突然离线，无法正常通信',
        deviceName: '智能灯具A-001',
        status: 'pending',
        level: 'high',
        reportTime: '2024-01-15 14:30:25',
        assignee: null
      },
      {
        id: 'F002',
        title: '亮度调节异常',
        description: '会议室B-005设备亮度调节功能失效',
        deviceName: '智能灯具B-005',
        status: 'processing',
        level: 'medium',
        reportTime: '2024-01-15 13:15:10',
        assignee: '张工程师'
      },
      {
        id: 'F003',
        title: '传感器数据异常',
        description: '走廊C区光照传感器数据波动异常',
        deviceName: '光照传感器C-012',
        status: 'resolved',
        level: 'low',
        reportTime: '2024-01-15 10:45:33',
        assignee: '李技术员'
      },
      {
        id: 'F004',
        title: '电源模块故障',
        description: '停车场D区电源模块出现间歇性故障',
        deviceName: '电源控制器D-008',
        status: 'pending',
        level: 'critical',
        reportTime: '2024-01-15 09:20:15',
        assignee: null
      },
      {
        id: 'F005',
        title: '通信协议错误',
        description: 'E区设备群组通信协议版本不匹配',
        deviceName: '设备群组E',
        status: 'processing',
        level: 'medium',
        reportTime: '2024-01-14 16:55:42',
        assignee: '王工程师'
      }
    ]
    
    faults.value = mockFaults
  } catch (error) {
    console.error('加载故障列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

const onStatusChange = (e) => {
  selectedStatus.value = e.detail.value
}

const onLevelChange = (e) => {
  selectedLevel.value = e.detail.value
}

const applyFilter = () => {
  uni.showToast({
    title: '筛选已应用',
    icon: 'success'
  })
}

const resetFilter = () => {
  selectedStatus.value = 0
  selectedLevel.value = 0
  uni.showToast({
    title: '筛选已重置',
    icon: 'success'
  })
}

const toggleSelect = (faultId) => {
  const index = selectedFaults.value.indexOf(faultId)
  if (index > -1) {
    selectedFaults.value.splice(index, 1)
  } else {
    selectedFaults.value.push(faultId)
  }
}

const batchAssign = () => {
  uni.showModal({
    title: '批量分配',
    content: `确定要分配选中的 ${selectedFaults.value.length} 个故障吗？`,
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '分配成功',
          icon: 'success'
        })
        selectedFaults.value = []
      }
    }
  })
}

const batchClose = () => {
  uni.showModal({
    title: '批量关闭',
    content: `确定要关闭选中的 ${selectedFaults.value.length} 个故障吗？`,
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '关闭成功',
          icon: 'success'
        })
        selectedFaults.value = []
      }
    }
  })
}

const viewDetail = (faultId) => {
  uni.navigateTo({
    url: `/pages/fault/detail?id=${faultId}`
  })
}

const loadMore = async () => {
  loading.value = true
  // 模拟加载更多数据
  await new Promise(resolve => setTimeout(resolve, 1000))
  hasMore.value = false
  loading.value = false
}

const getStatusText = (status) => {
  const statusMap = {
    pending: '待处理',
    processing: '处理中',
    resolved: '已解决',
    closed: '已关闭'
  }
  return statusMap[status] || status
}

const getLevelText = (level) => {
  const levelMap = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '紧急'
  }
  return levelMap[level] || level
}

// 生命周期
onMounted(() => {
  loadFaults()
})

// 页面卸载时清理资源
onUnload(() => {
  // 清空数据数组，释放内存
  faults.value = []
  selectedFaults.value = []
  
  // 重置筛选条件
  selectedStatus.value = 0
  selectedLevel.value = 0
  
  // 重置加载状态
  loading.value = false
  hasMore.value = true
  
  // 隐藏loading状态
  uni.hideLoading()
})
</script>

<style scoped>
.fault-list-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 30rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 统计概览样式 */
.stats-overview {
  display: flex;
  margin-bottom: 30rpx;
  gap: 20rpx;
}

.stats-card {
  flex: 1;
  background: white;
  border-radius: 12rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.stats-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

.stats-card.pending .stats-number {
  color: #ff9500;
}

.stats-card.processing .stats-number {
  color: #007aff;
}

.stats-card.resolved .stats-number {
  color: #34c759;
}

/* 筛选区域样式 */
.filter-section {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.filter-row {
  display: flex;
  gap: 30rpx;
  margin-bottom: 30rpx;
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  border: 2rpx solid #e0e0e0;
}

.filter-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.filter-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.filter-arrow {
  font-size: 24rpx;
  color: #999;
}

.filter-actions {
  display: flex;
  gap: 20rpx;
}

.filter-btn, .reset-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
}

.filter-btn {
  background: #007aff;
  color: white;
}

.reset-btn {
  background: #f0f0f0;
  color: #333;
}

/* 批量操作样式 */
.batch-actions {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 12rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.batch-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.batch-btn {
  padding: 15rpx 30rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  margin-left: 20rpx;
  border: none;
}

.batch-btn.assign {
  background: #007aff;
  color: white;
}

.batch-btn.close {
  background: #ff3b30;
  color: white;
}

/* 故障列表样式 */
.fault-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.fault-item {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.fault-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.fault-checkbox {
  margin-right: 20rpx;
  margin-top: 5rpx;
}

.fault-info {
  flex: 1;
}

.fault-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.fault-meta {
  display: flex;
  gap: 20rpx;
}

.fault-id, .fault-device {
  font-size: 24rpx;
  color: #666;
}

.fault-status {
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  text-align: center;
  min-width: 120rpx;
}

.fault-status.pending {
  background: #fff3e0;
  color: #ff9500;
}

.fault-status.processing {
  background: #e3f2fd;
  color: #007aff;
}

.fault-status.resolved {
  background: #e8f5e8;
  color: #34c759;
}

.fault-status.closed {
  background: #f0f0f0;
  color: #666;
}

.fault-content {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.fault-description {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.fault-details {
  display: flex;
  flex-wrap: wrap;
  gap: 30rpx;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 10rpx;
}

.detail-value {
  font-size: 24rpx;
  color: #333;
}

.detail-value.low {
  color: #34c759;
}

.detail-value.medium {
  color: #ff9500;
}

.detail-value.high {
  color: #ff3b30;
}

.detail-value.critical {
  color: #ff3b30;
  font-weight: bold;
}

/* 加载更多样式 */
.load-more {
  text-align: center;
  margin-top: 40rpx;
}

.load-more-btn {
  padding: 20rpx 60rpx;
  background: #f0f0f0;
  color: #333;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 100rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style>