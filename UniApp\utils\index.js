/**
 * Utils工具模块统一导出
 * 提供项目中所有工具函数的统一入口
 */

// 导入各个工具模块
import storage, { StorageKeys, setUserInfo, getUserInfo, clearUserInfo, setDeviceList, getDeviceList, clearDeviceList } from './storage.js'
import { REGEX, validators, rules, FormValidator, validate, commonRules } from './validate.js'
import { dateFormat, numberFormat, stringFormat, deviceFormat, faultFormat, format } from './format.js'
import { PERMISSIONS, ROLES, ROLE_PERMISSIONS, PermissionChecker, permissionDirective, routeGuard, requirePermission, requireRole } from './permission.js'
import { MQTT_CONFIG, MQTT_TOPICS, MQTTClient, getMqttClient, deviceMqtt } from './mqtt.js'

/**
 * 存储相关工具
 */
export const storageUtils = {
  ...storage,
  StorageKeys,
  setUserInfo,
  getUserInfo,
  clearUserInfo,
  setDeviceList,
  getDeviceList,
  clearDeviceList
}

/**
 * 验证相关工具
 */
export const validateUtils = {
  REGEX,
  validators,
  rules,
  FormValidator,
  validate,
  commonRules
}

/**
 * 格式化相关工具
 */
export const formatUtils = {
  dateFormat,
  numberFormat,
  stringFormat,
  deviceFormat,
  faultFormat,
  format
}

/**
 * 权限相关工具
 */
export const permissionUtils = {
  PERMISSIONS,
  ROLES,
  ROLE_PERMISSIONS,
  PermissionChecker,
  permissionDirective,
  routeGuard,
  requirePermission,
  requireRole
}

/**
 * MQTT通信相关工具
 */
export const mqttUtils = {
  MQTT_CONFIG,
  MQTT_TOPICS,
  MQTTClient,
  getMqttClient,
  deviceMqtt
}

/**
 * 常用工具函数集合
 */
export const utils = {
  // 存储工具
  storage: storageUtils,
  
  // 验证工具
  validate: validateUtils,
  
  // 格式化工具
  format: formatUtils,
  
  // 权限工具
  permission: permissionUtils,
  
  // MQTT工具
  mqtt: mqttUtils,
  
  /**
   * 防抖函数
   * @param {Function} func 要防抖的函数
   * @param {number} wait 等待时间
   * @param {boolean} immediate 是否立即执行
   * @returns {Function} 防抖后的函数
   */
  debounce(func, wait, immediate = false) {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        timeout = null
        if (!immediate) func.apply(this, args)
      }
      const callNow = immediate && !timeout
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
      if (callNow) func.apply(this, args)
    }
  },
  
  /**
   * 节流函数
   * @param {Function} func 要节流的函数
   * @param {number} limit 时间间隔
   * @returns {Function} 节流后的函数
   */
  throttle(func, limit) {
    let inThrottle
    return function(...args) {
      if (!inThrottle) {
        func.apply(this, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  },
  
  /**
   * 深拷贝
   * @param {any} obj 要拷贝的对象
   * @returns {any} 拷贝后的对象
   */
  deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj
    if (obj instanceof Date) return new Date(obj.getTime())
    if (obj instanceof Array) return obj.map(item => this.deepClone(item))
    if (typeof obj === 'object') {
      const clonedObj = {}
      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          clonedObj[key] = this.deepClone(obj[key])
        }
      }
      return clonedObj
    }
  },
  
  /**
   * 生成唯一ID
   * @param {number} length ID长度
   * @returns {string} 唯一ID
   */
  generateId(length = 8) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  },
  
  /**
   * 获取URL参数
   * @param {string} name 参数名
   * @param {string} url URL地址
   * @returns {string|null} 参数值
   */
  getUrlParam(name, url = window.location.href) {
    const regex = new RegExp('[?&]' + name + '=([^&#]*)', 'i')
    const match = regex.exec(url)
    return match ? decodeURIComponent(match[1]) : null
  },
  
  /**
   * 设置URL参数
   * @param {string} name 参数名
   * @param {string} value 参数值
   * @param {string} url URL地址
   * @returns {string} 新的URL
   */
  setUrlParam(name, value, url = window.location.href) {
    const regex = new RegExp('([?&])' + name + '=.*?(&|$)', 'i')
    const separator = url.indexOf('?') !== -1 ? '&' : '?'
    
    if (url.match(regex)) {
      return url.replace(regex, '$1' + name + '=' + encodeURIComponent(value) + '$2')
    } else {
      return url + separator + name + '=' + encodeURIComponent(value)
    }
  },
  
  /**
   * 数组去重
   * @param {Array} arr 原数组
   * @param {string} key 去重的键名（对象数组时使用）
   * @returns {Array} 去重后的数组
   */
  unique(arr, key) {
    if (!Array.isArray(arr)) return []
    
    if (key) {
      const seen = new Set()
      return arr.filter(item => {
        const val = item[key]
        if (seen.has(val)) {
          return false
        }
        seen.add(val)
        return true
      })
    }
    
    return [...new Set(arr)]
  },
  
  /**
   * 数组分组
   * @param {Array} arr 原数组
   * @param {string|Function} key 分组键或分组函数
   * @returns {Object} 分组后的对象
   */
  groupBy(arr, key) {
    if (!Array.isArray(arr)) return {}
    
    return arr.reduce((groups, item) => {
      const groupKey = typeof key === 'function' ? key(item) : item[key]
      if (!groups[groupKey]) {
        groups[groupKey] = []
      }
      groups[groupKey].push(item)
      return groups
    }, {})
  },
  
  /**
   * 对象合并（深度合并）
   * @param {Object} target 目标对象
   * @param {...Object} sources 源对象
   * @returns {Object} 合并后的对象
   */
  merge(target, ...sources) {
    if (!sources.length) return target
    const source = sources.shift()
    
    if (this.isObject(target) && this.isObject(source)) {
      for (const key in source) {
        if (this.isObject(source[key])) {
          if (!target[key]) Object.assign(target, { [key]: {} })
          this.merge(target[key], source[key])
        } else {
          Object.assign(target, { [key]: source[key] })
        }
      }
    }
    
    return this.merge(target, ...sources)
  },
  
  /**
   * 判断是否为对象
   * @param {any} item 要判断的值
   * @returns {boolean} 是否为对象
   */
  isObject(item) {
    return item && typeof item === 'object' && !Array.isArray(item)
  },
  
  /**
   * 休眠函数
   * @param {number} ms 休眠时间（毫秒）
   * @returns {Promise} Promise对象
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  },
  
  /**
   * 重试函数
   * @param {Function} fn 要重试的函数
   * @param {number} times 重试次数
   * @param {number} delay 重试间隔
   * @returns {Promise} Promise对象
   */
  async retry(fn, times = 3, delay = 1000) {
    let lastError
    
    for (let i = 0; i < times; i++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error
        if (i < times - 1) {
          await this.sleep(delay)
        }
      }
    }
    
    throw lastError
  }
}

// 默认导出
export default utils

// 单独导出各个模块
export {
  storage,
  StorageKeys,
  setUserInfo,
  getUserInfo,
  clearUserInfo,
  setDeviceList,
  getDeviceList,
  clearDeviceList
} from './storage.js'

export {
  REGEX,
  validators,
  rules,
  FormValidator,
  validate,
  commonRules
} from './validate.js'

export {
  dateFormat,
  numberFormat,
  stringFormat,
  deviceFormat,
  faultFormat,
  format
} from './format.js'

export {
  PERMISSIONS,
  ROLES,
  ROLE_PERMISSIONS,
  PermissionChecker,
  permissionDirective,
  routeGuard,
  requirePermission,
  requireRole
} from './permission.js'

export {
  MQTT_CONFIG,
  MQTT_TOPICS,
  MQTTClient,
  getMqttClient,
  deviceMqtt
} from './mqtt.js'