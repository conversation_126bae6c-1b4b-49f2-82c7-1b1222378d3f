<template>
	<view class="device-detail-container">
		<!-- 设备基本信息 -->
		<view class="device-info-card">
			<view class="device-header">
				<view class="device-avatar">
					<text class="device-icon">💡</text>
				</view>
				<view class="device-basic">
					<view class="device-name">{{ deviceInfo.name }}</view>
					<view class="device-code">{{ deviceInfo.code }}</view>
					<view class="device-status" :class="deviceInfo.statusClass">
						<view class="status-dot"></view>
						<text class="status-text">{{ deviceInfo.statusText }}</text>
					</view>
				</view>
				<view class="device-actions">
					<view class="action-icon" @tap="toggleFavorite">
						<text>{{ deviceInfo.isFavorite ? '❤️' : '🤍' }}</text>
					</view>
					<view class="action-icon" @tap="shareDevice">
						<text>📤</text>
					</view>
				</view>
			</view>
			
			<view class="device-details">
				<view class="detail-row">
					<view class="detail-item">
						<text class="detail-label">设备类型</text>
						<text class="detail-value">{{ deviceInfo.typeText }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">安装位置</text>
						<text class="detail-value">{{ deviceInfo.location }}</text>
					</view>
				</view>
				<view class="detail-row">
					<view class="detail-item">
						<text class="detail-label">额定功率</text>
						<text class="detail-value">{{ deviceInfo.power }}W</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">安装日期</text>
						<text class="detail-value">{{ formatDate(deviceInfo.installDate, 'YYYY-MM-DD') }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 实时状态 -->
		<view class="status-card">
			<view class="card-header">
				<view class="card-title">实时状态</view>
				<view class="refresh-btn" @tap="refreshStatus">
					<text class="refresh-icon" :class="{ rotating: isRefreshing }">🔄</text>
				</view>
			</view>
			
			<view class="status-grid">
				<view class="status-item" v-for="(item, index) in statusData" :key="index">
					<view class="status-icon" :class="item.iconClass">
						<text>{{ item.icon }}</text>
					</view>
					<view class="status-content">
						<view class="status-value">{{ item.value }}</view>
						<view class="status-label">{{ item.label }}</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 设备控制 -->
		<view class="control-card">
			<view class="card-header">
				<view class="card-title">设备控制</view>
				<view class="control-status">
					<text :class="deviceInfo.isOn ? 'status-on' : 'status-off'">
						{{ deviceInfo.isOn ? '已开启' : '已关闭' }}
					</text>
				</view>
			</view>
			
			<!-- 开关控制 -->
			<view class="control-section">
				<view class="control-item">
					<view class="control-label">
						<text class="label-text">设备开关</text>
						<text class="label-desc">控制设备的开启和关闭</text>
					</view>
					<view class="control-switch">
						<switch 
							:checked="deviceInfo.isOn" 
							@change="handleSwitchChange"
							color="#1890ff"
						/>
					</view>
				</view>
			</view>
			
			<!-- 亮度控制 -->
			<view class="control-section" v-if="deviceInfo.isOn">
				<view class="control-item">
					<view class="control-label">
						<text class="label-text">亮度调节</text>
						<text class="label-desc">当前亮度：{{ deviceInfo.brightness }}%</text>
					</view>
				</view>
				<view class="brightness-control">
					<view class="brightness-icon">🔅</view>
					<slider 
						class="brightness-slider"
						:value="deviceInfo.brightness" 
						:min="0" 
						:max="100" 
						:step="1"
						@change="handleBrightnessChange"
						active-color="#1890ff"
						background-color="#f0f0f0"
					/>
					<view class="brightness-icon">🔆</view>
				</view>
				<view class="brightness-presets">
					<view 
						class="preset-btn" 
						v-for="preset in brightnessPresets" 
						:key="preset.value"
						:class="{ active: deviceInfo.brightness === preset.value }"
						@tap="setBrightness(preset.value)"
					>
						{{ preset.label }}
					</view>
				</view>
			</view>
			
			<!-- 色温控制 -->
			<view class="control-section" v-if="deviceInfo.isOn && deviceInfo.supportColorTemp">
				<view class="control-item">
					<view class="control-label">
						<text class="label-text">色温调节</text>
						<text class="label-desc">当前色温：{{ deviceInfo.colorTemp }}K</text>
					</view>
				</view>
				<view class="colortemp-control">
					<view class="colortemp-icon warm">🟡</view>
					<slider 
						class="colortemp-slider"
						:value="deviceInfo.colorTemp" 
						:min="2700" 
						:max="6500" 
						:step="100"
						@change="handleColorTempChange"
						active-color="#faad14"
						background-color="#f0f0f0"
					/>
					<view class="colortemp-icon cool">⚪</view>
				</view>
			</view>
		</view>
		
		<!-- 定时任务 -->
		<view class="timer-card">
			<view class="card-header">
				<view class="card-title">定时任务</view>
				<view class="add-timer-btn" @tap="addTimer">
					<text>+ 添加</text>
				</view>
			</view>
			
			<view class="timer-list">
				<view class="timer-item" v-for="timer in timerList" :key="timer.id">
					<view class="timer-info">
						<view class="timer-time">{{ timer.time }}</view>
						<view class="timer-desc">{{ timer.description }}</view>
						<view class="timer-repeat">{{ timer.repeatText }}</view>
					</view>
					<view class="timer-actions">
						<switch 
							:checked="timer.enabled" 
							@change="toggleTimer(timer)"
							color="#1890ff"
						/>
						<view class="timer-edit" @tap="editTimer(timer)">
							<text>✏️</text>
						</view>
					</view>
				</view>
				
				<view v-if="timerList.length === 0" class="empty">
					<view class="empty-icon">⏰</view>
					<view class="empty-text">暂无定时任务</view>
					<view class="empty-action" @tap="addTimer">
						<text>添加定时任务</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 历史记录 -->
		<view class="history-card">
			<view class="card-header">
				<view class="card-title">操作历史</view>
				<view class="view-all-btn" @tap="viewAllHistory">
					<text>查看全部</text>
				</view>
			</view>
			
			<view class="history-list">
				<view class="history-item" v-for="record in historyList" :key="record.id">
					<view class="history-icon" :class="record.iconClass">
						<text>{{ record.icon }}</text>
					</view>
					<view class="history-content">
						<view class="history-action">{{ record.action }}</view>
						<view class="history-time">{{ formatTime(record.createTime) }}</view>
					</view>
					<view class="history-result" :class="record.resultClass">
						{{ record.result }}
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部操作栏 -->
		<view class="bottom-actions">
			<view class="action-btn btn-secondary" @tap="editDevice">
				<text class="btn-icon">✏️</text>
				<text class="btn-text">编辑</text>
			</view>
			<view class="action-btn btn-warning" @tap="resetDevice">
				<text class="btn-icon">🔄</text>
				<text class="btn-text">重置</text>
			</view>
			<view class="action-btn btn-danger" @tap="deleteDevice">
				<text class="btn-icon">🗑️</text>
				<text class="btn-text">删除</text>
			</view>
		</view>
	</view>
</template>

<script>
import { getDeviceDetail, getDeviceStatus, toggleDeviceSwitch, setDeviceBrightness, setDeviceColorTemp, toggleDeviceTimer, resetDevice, deleteDevice } from '@/api/device'

export default {
	data() {
		return {
			deviceId: '',
			isRefreshing: false,
			// 设备信息
			deviceInfo: {},
			// 实时状态数据
			statusData: [],
			// 亮度预设
			brightnessPresets: [
				{ label: '10%', value: 10 },
				{ label: '30%', value: 30 },
				{ label: '50%', value: 50 },
				{ label: '80%', value: 80 },
				{ label: '100%', value: 100 }
			],
			// 定时任务列表
			timerList: [
				{
					id: 1,
					time: '08:00',
					description: '自动开启',
					repeat: ['mon', 'tue', 'wed', 'thu', 'fri'],
					repeatText: '工作日',
					enabled: true,
					action: 'on',
					brightness: 80
				},
				{
					id: 2,
					time: '18:30',
					description: '自动关闭',
					repeat: ['mon', 'tue', 'wed', 'thu', 'fri'],
					repeatText: '工作日',
					enabled: true,
					action: 'off'
				}
			],
			// 操作历史
			historyList: [
				{
					id: 1,
					action: '手动开启设备',
					icon: '💡',
					iconClass: 'history-on',
					result: '成功',
					resultClass: 'result-success',
					createTime: new Date(Date.now() - 10 * 60 * 1000)
				},
				{
					id: 2,
					action: '调节亮度至80%',
					icon: '🔆',
					iconClass: 'history-brightness',
					result: '成功',
					resultClass: 'result-success',
					createTime: new Date(Date.now() - 30 * 60 * 1000)
				},
				{
					id: 3,
					action: '定时任务执行',
					icon: '⏰',
					iconClass: 'history-timer',
					result: '成功',
					resultClass: 'result-success',
					createTime: new Date(Date.now() - 2 * 60 * 60 * 1000)
				}
			]
		}
	},
	
	onLoad(options) {
		if (options.id) {
			this.deviceId = options.id
			this.loadDeviceDetail()
		}
	},
	
	onShow() {
		// 页面显示时刷新状态
		this.refreshStatus()
	},
	
	onUnload() {
		// 清理定时器，防止内存泄漏
		if (this.brightnessTimer) {
			clearTimeout(this.brightnessTimer)
			this.brightnessTimer = null
		}
		if (this.colorTempTimer) {
			clearTimeout(this.colorTempTimer)
			this.colorTempTimer = null
		}
	},
	
	methods: {
		// 加载设备详情
		async loadDeviceDetail() {
			try {
				uni.showLoading({ title: '加载设备信息...' })
				
				const data = await getDeviceDetail(this.deviceId)
				
				// 处理设备信息
				this.deviceInfo = {
					...data,
					statusText: this.getStatusText(data.status),
					statusClass: `status-${data.status}`,
					typeText: this.getTypeText(data.type),
					lastUpdate: new Date(data.lastUpdate || data.updatedAt || Date.now())
				}
				
				// 处理状态数据
				this.updateStatusData(data)
				
				uni.hideLoading()
			} catch (error) {
				console.error('加载设备详情失败:', error)
				uni.hideLoading()
				uni.showToast({
					title: error.message || '设备信息加载失败',
					icon: 'error'
				})
			}
		},
		
		// 刷新状态
		async refreshStatus() {
			try {
				this.isRefreshing = true
				
				const data = await getDeviceStatus(this.deviceId)
				this.updateStatusData(data)
				this.deviceInfo.lastUpdate = new Date()
				this.isRefreshing = false
			} catch (error) {
				console.error('刷新状态失败:', error)
				this.isRefreshing = false
				uni.showToast({
					title: error.message || '状态刷新失败',
					icon: 'error'
				})
			}
		},
		
		// 切换收藏
		toggleFavorite() {
			this.deviceInfo.isFavorite = !this.deviceInfo.isFavorite
			const message = this.deviceInfo.isFavorite ? '已添加到收藏' : '已取消收藏'
			this.showSuccess(message)
		},
		
		// 分享设备
		shareDevice() {
			uni.share({
				provider: 'weixin',
				scene: 'WXSceneSession',
				type: 0,
				href: `设备信息：${this.deviceInfo.name}`,
				title: '节能灯设备分享',
				summary: `设备编号：${this.deviceInfo.code}，位置：${this.deviceInfo.location}`,
				success: () => {
					this.showSuccess('分享成功')
				},
				fail: () => {
					this.showError('分享失败')
				}
			})
		},
		
		// 开关变化处理
		async handleSwitchChange(e) {
			try {
				const isOn = e.detail.value
				const action = isOn ? '开启' : '关闭'
				
				uni.showLoading({ title: `${action}中...` })
				
				// 调用API
				await toggleDeviceSwitch(this.deviceId, isOn)
				
				// 更新设备状态
				this.deviceInfo.isOn = isOn
				if (!isOn) {
					this.deviceInfo.brightness = 0
				} else if (this.deviceInfo.brightness === 0) {
					this.deviceInfo.brightness = 80
				}
				this.deviceInfo.lastUpdate = new Date()
				
				// 添加历史记录
				this.addHistoryRecord(`手动${action}设备`, isOn ? '💡' : '🌙', 'success')
				
				uni.hideLoading()
				uni.showToast({ title: `设备已${action}`, icon: 'success' })
			} catch (error) {
				console.error('切换设备状态失败:', error)
				uni.hideLoading()
				uni.showToast({ title: error.message || '操作失败', icon: 'error' })
			}
		},
		
		// 亮度变化处理
		async handleBrightnessChange(e) {
			try {
				const brightness = e.detail.value
				
				// 防抖处理
				clearTimeout(this.brightnessTimer)
				this.brightnessTimer = setTimeout(async () => {
					await this.updateBrightness(brightness)
				}, 500)
			} catch (error) {
				console.error('亮度调节失败:', error)
			}
		},
		
		// 设置预设亮度
		async setBrightness(value) {
			this.deviceInfo.brightness = value
			await this.updateBrightness(value)
		},
		
		// 更新亮度
		async updateBrightness(brightness) {
			try {
				// 调用API
				await setDeviceBrightness(this.deviceId, brightness)
				
				this.deviceInfo.brightness = brightness
				this.deviceInfo.lastUpdate = new Date()
				
				// 添加历史记录
				this.addHistoryRecord(`调节亮度至${brightness}%`, '🔆', 'success')
			} catch (error) {
				console.error('更新亮度失败:', error)
				uni.showToast({ title: error.message || '亮度调节失败', icon: 'error' })
			}
		},
		
		// 色温变化处理
		async handleColorTempChange(e) {
			try {
				const colorTemp = e.detail.value
				
				// 防抖处理
				clearTimeout(this.colorTempTimer)
				this.colorTempTimer = setTimeout(async () => {
					await this.updateColorTemp(colorTemp)
				}, 500)
			} catch (error) {
				console.error('色温调节失败:', error)
			}
		},
		
		// 更新色温
		async updateColorTemp(colorTemp) {
			try {
				await setDeviceColorTemp(this.deviceId, colorTemp)
				
				this.deviceInfo.colorTemp = colorTemp
				this.deviceInfo.lastUpdate = new Date()
				
				// 添加历史记录
				this.addHistoryRecord(`调节色温至${colorTemp}K`, '🌈', 'success')
			} catch (error) {
				console.error('更新色温失败:', error)
				uni.showToast({ title: error.message || '色温调节失败', icon: 'error' })
			}
		},
		
		// 添加定时任务
		addTimer() {
			uni.navigateTo({
				url: `/pages/device/timer?deviceId=${this.deviceId}`
			})
		},
		
		// 编辑定时任务
		editTimer(timer) {
			uni.navigateTo({
				url: `/pages/device/timer?deviceId=${this.deviceId}&timerId=${timer.id}`
			})
		},
		
		// 切换定时任务
		async toggleTimer(timer) {
			try {
				const enabled = !timer.enabled
				uni.showLoading({ title: enabled ? '启用中...' : '禁用中...' })
				
				await toggleDeviceTimer(timer.id, enabled)
				timer.enabled = enabled
				
				uni.hideLoading()
				const message = enabled ? '定时任务已启用' : '定时任务已禁用'
				uni.showToast({ title: message, icon: 'success' })
			} catch (error) {
				console.error('切换定时任务失败:', error)
				uni.hideLoading()
				uni.showToast({ title: error.message || '操作失败', icon: 'error' })
			}
		},
		
		// 查看全部历史
		viewAllHistory() {
			uni.navigateTo({
				url: `/pages/device/history?deviceId=${this.deviceId}`
			})
		},
		
		// 编辑设备
		editDevice() {
			uni.navigateTo({
				url: `/pages/device/edit?id=${this.deviceId}`
			})
		},
		
		// 重置设备
		async resetDevice() {
			try {
				const result = await new Promise((resolve) => {
					uni.showModal({
						title: '确认重置设备？',
						content: '重置后设备将恢复出厂设置',
						success: (res) => resolve(res.confirm)
					})
				})
				if (!result) return
				
				uni.showLoading({ title: '重置中...' })
				
				await resetDevice(this.deviceId)
				
				// 重置设备状态
				this.deviceInfo.brightness = 100
				this.deviceInfo.colorTemp = 4000
				this.deviceInfo.isOn = false
				this.deviceInfo.lastUpdate = new Date()
				
				// 清空定时任务
				this.timerList = []
				
				// 添加历史记录
				this.addHistoryRecord('设备重置', '🔄', 'warning')
				
				uni.hideLoading()
				uni.showToast({ title: '设备重置成功', icon: 'success' })
			} catch (error) {
				console.error('重置设备失败:', error)
				uni.hideLoading()
				uni.showToast({ title: error.message || '设备重置失败', icon: 'error' })
			}
		},
		
		// 删除设备
		async deleteDevice() {
			try {
				const confirm = await this.showConfirm('确定要删除设备吗？删除后无法恢复。')
				if (confirm) {
					uni.showLoading({ title: '删除中...' })
					
					// 调用API
					await deleteDevice(this.deviceId)
					
					uni.hideLoading()
					uni.showToast({ title: '设备删除成功', icon: 'success' })
					
					// 返回上一页
					setTimeout(() => {
						uni.navigateBack()
					}, 1000)
				}
			} catch (error) {
				console.error('删除设备失败:', error)
				uni.hideLoading()
				uni.showToast({ title: error.message || '删除失败', icon: 'error' })
			}
		},
		
		// 添加历史记录
		addHistoryRecord(action, icon, result) {
			const record = {
				id: Date.now(),
				action,
				icon,
				iconClass: `history-${result}`,
				result: result === 'success' ? '成功' : '失败',
				resultClass: `result-${result}`,
				createTime: new Date()
			}
			
			this.historyList.unshift(record)
			
			// 保持最多10条记录
			if (this.historyList.length > 10) {
				this.historyList = this.historyList.slice(0, 10)
			}
		},
		
		// 格式化时间
		formatTime(date) {
			const now = new Date()
			const diff = now - date
			const minutes = Math.floor(diff / (1000 * 60))
			const hours = Math.floor(diff / (1000 * 60 * 60))
			
			if (minutes < 1) {
				return '刚刚'
			} else if (minutes < 60) {
				return `${minutes}分钟前`
			} else if (hours < 24) {
				return `${hours}小时前`
			} else {
				return this.formatDate(date, 'MM-DD HH:mm')
			}
		},
		
		// 更新状态数据
		updateStatusData(data) {
			if (data) {
				this.statusData = {
					...this.statusData,
					isOnline: data.status === 'online',
					isOn: data.isOn || data.switch,
					brightness: data.brightness || 100,
					colorTemp: data.colorTemp || data.colorTemperature || 3000,
					power: data.power || 0,
					voltage: data.voltage || 220,
					current: data.current || 0,
					temperature: data.temperature || 25
				}
			}
		},
		
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				online: '在线',
				offline: '离线',
				fault: '故障',
				maintenance: '维护中'
			}
			return statusMap[status] || '未知'
		},
		
		// 获取设备类型文本
		getTypeText(type) {
			const typeMap = {
				led: 'LED灯',
				street_light: '路灯',
				indoor_light: '室内灯',
				outdoor_light: '户外灯',
				smart_light: '智能灯'
			}
			return typeMap[type] || '普通灯具'
		},
		
		// 格式化日期
		formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
			const d = new Date(date)
			const year = d.getFullYear()
			const month = String(d.getMonth() + 1).padStart(2, '0')
			const day = String(d.getDate()).padStart(2, '0')
			const hours = String(d.getHours()).padStart(2, '0')
			const minutes = String(d.getMinutes()).padStart(2, '0')
			const seconds = String(d.getSeconds()).padStart(2, '0')
			
			return format
				.replace('YYYY', year)
				.replace('MM', month)
				.replace('DD', day)
				.replace('HH', hours)
				.replace('mm', minutes)
				.replace('ss', seconds)
		},
		
		// 显示确认对话框
		showConfirm(content, title = '提示') {
			return new Promise((resolve) => {
				uni.showModal({
					title,
					content,
					success: (res) => {
						resolve(res.confirm)
					},
					fail: () => {
						resolve(false)
					}
				})
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.device-detail-container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

// 设备信息卡片
.device-info-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.device-header {
	display: flex;
	align-items: flex-start;
	margin-bottom: 24rpx;
}

.device-avatar {
	width: 100rpx;
	height: 100rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}

.device-icon {
	font-size: 48rpx;
}

.device-basic {
	flex: 1;
}

.device-name {
	font-size: 36rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.device-code {
	font-size: 24rpx;
	color: #999999;
	margin-bottom: 12rpx;
}

.device-status {
	display: flex;
	align-items: center;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	width: fit-content;
}

.status-dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	margin-right: 8rpx;
}

.status-text {
	font-size: 24rpx;
}

.status-online {
	background-color: #f6ffed;
	color: #52c41a;
	
	.status-dot {
		background-color: #52c41a;
	}
}

.device-actions {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.action-icon {
	width: 60rpx;
	height: 60rpx;
	background-color: #f8f9fa;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	transition: all 0.3s;
}

.action-icon:active {
	background-color: #e9ecef;
	transform: scale(0.9);
}

.device-details {
	padding-top: 24rpx;
	border-top: 2rpx solid #f0f0f0;
}

.detail-row {
	display: flex;
	margin-bottom: 16rpx;
}

.detail-item {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.detail-label {
	font-size: 24rpx;
	color: #999999;
	margin-bottom: 8rpx;
}

.detail-value {
	font-size: 28rpx;
	color: #333333;
	font-weight: 500;
}

// 状态卡片
.status-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.card-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.refresh-btn {
	padding: 8rpx;
}

.refresh-icon {
	font-size: 24rpx;
	color: #1890ff;
	transition: transform 0.5s;
}

.refresh-icon.rotating {
	animation: rotate 1s linear infinite;
}

@keyframes rotate {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

.status-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

.status-item {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 12rpx;
}

.status-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
	font-size: 24rpx;
}

.icon-power {
	background-color: #fff7e6;
}

.icon-temp {
	background-color: #e6f7ff;
}

.icon-signal {
	background-color: #f6ffed;
}

.icon-time {
	background-color: #fff2f0;
}

.status-content {
	flex: 1;
}

.status-value {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 4rpx;
}

.status-label {
	font-size: 20rpx;
	color: #999999;
}

// 控制卡片
.control-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.control-status {
	font-size: 24rpx;
}

.status-on {
	color: #52c41a;
}

.status-off {
	color: #999999;
}

.control-section {
	margin-bottom: 32rpx;
}

.control-section:last-child {
	margin-bottom: 0;
}

.control-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.control-label {
	flex: 1;
}

.label-text {
	font-size: 28rpx;
	color: #333333;
	margin-bottom: 4rpx;
	display: block;
}

.label-desc {
	font-size: 24rpx;
	color: #999999;
}

.brightness-control,
.colortemp-control {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.brightness-icon,
.colortemp-icon {
	font-size: 32rpx;
	margin: 0 16rpx;
}

.brightness-slider,
.colortemp-slider {
	flex: 1;
}

.brightness-presets {
	display: flex;
	justify-content: space-between;
	gap: 12rpx;
}

.preset-btn {
	flex: 1;
	height: 60rpx;
	border: 2rpx solid #e8e8e8;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	color: #666666;
	transition: all 0.3s;
}

.preset-btn.active {
	background-color: #1890ff;
	border-color: #1890ff;
	color: #ffffff;
}

// 定时任务卡片
.timer-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.add-timer-btn {
	padding: 8rpx 16rpx;
	background-color: #1890ff;
	color: #ffffff;
	border-radius: 8rpx;
	font-size: 24rpx;
}

.timer-list {
	max-height: 400rpx;
	overflow-y: auto;
}

.timer-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 2rpx solid #f0f0f0;
}

.timer-item:last-child {
	border-bottom: none;
}

.timer-info {
	flex: 1;
	margin-right: 16rpx;
}

.timer-time {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 4rpx;
}

.timer-desc {
	font-size: 24rpx;
	color: #666666;
	margin-bottom: 4rpx;
}

.timer-repeat {
	font-size: 20rpx;
	color: #999999;
}

.timer-actions {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.timer-edit {
	padding: 8rpx;
	font-size: 24rpx;
}

// 历史记录卡片
.history-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.view-all-btn {
	font-size: 24rpx;
	color: #1890ff;
}

.history-list {
	max-height: 400rpx;
	overflow-y: auto;
}

.history-item {
	display: flex;
	align-items: center;
	padding: 16rpx 0;
	border-bottom: 2rpx solid #f0f0f0;
}

.history-item:last-child {
	border-bottom: none;
}

.history-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
	font-size: 24rpx;
	background-color: #f8f9fa;
}

.history-content {
	flex: 1;
	margin-right: 16rpx;
}

.history-action {
	font-size: 28rpx;
	color: #333333;
	margin-bottom: 4rpx;
}

.history-time {
	font-size: 20rpx;
	color: #999999;
}

.history-result {
	padding: 4rpx 12rpx;
	border-radius: 4rpx;
	font-size: 20rpx;
}

.result-success {
	background-color: #f6ffed;
	color: #52c41a;
}

.result-error {
	background-color: #fff2f0;
	color: #f5222d;
}

// 底部操作栏
.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #ffffff;
	padding: 20rpx;
	display: flex;
	gap: 16rpx;
	box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
	z-index: 100;
}

.action-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 8rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	transition: all 0.3s;
}

.btn-secondary {
	background-color: #f0f0f0;
	color: #333333;
}

.btn-warning {
	background-color: #faad14;
	color: #ffffff;
}

.btn-danger {
	background-color: #f5222d;
	color: #ffffff;
}

.btn-icon {
	font-size: 24rpx;
	margin-bottom: 4rpx;
}

.btn-text {
	font-size: 20rpx;
}
</style>