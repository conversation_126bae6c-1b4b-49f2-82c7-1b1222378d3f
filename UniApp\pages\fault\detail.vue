<template>
  <view class="fault-detail-container">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">故障详情</text>
      <view class="header-actions">
        <button class="action-btn edit" @click="editFault" v-if="canEdit">编辑</button>
        <button class="action-btn assign" @click="assignFault" v-if="canAssign">分配</button>
      </view>
    </view>

    <!-- 故障基本信息 -->
    <view class="info-section">
      <view class="section-title">
        <text class="title-text">基本信息</text>
        <view class="fault-status" :class="faultDetail.status">
          <text>{{ getStatusText(faultDetail.status) }}</text>
        </view>
      </view>
      
      <view class="info-grid">
        <view class="info-item">
          <text class="info-label">故障编号</text>
          <text class="info-value">#{{ faultDetail.id }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">故障级别</text>
          <text class="info-value level" :class="faultDetail.level">{{ getLevelText(faultDetail.level) }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">设备名称</text>
          <text class="info-value">{{ faultDetail.deviceName }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">设备位置</text>
          <text class="info-value">{{ faultDetail.deviceLocation }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">报告时间</text>
          <text class="info-value">{{ faultDetail.reportTime }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">报告人</text>
          <text class="info-value">{{ faultDetail.reporter }}</text>
        </view>
      </view>
    </view>

    <!-- 故障描述 -->
    <view class="info-section">
      <view class="section-title">
        <text class="title-text">故障描述</text>
      </view>
      <view class="description-content">
        <text class="description-text">{{ faultDetail.description }}</text>
      </view>
      
      <!-- 故障图片 -->
      <view class="fault-images" v-if="faultDetail.images && faultDetail.images.length > 0">
        <text class="images-title">故障图片</text>
        <view class="images-grid">
          <image 
            v-for="(image, index) in faultDetail.images" 
            :key="index"
            :src="image"
            class="fault-image"
            mode="aspectFill"
            @click="previewImage(image, faultDetail.images)"
          />
        </view>
      </view>
    </view>

    <!-- 处理流程 -->
    <view class="info-section">
      <view class="section-title">
        <text class="title-text">处理流程</text>
      </view>
      
      <view class="timeline">
        <view 
          class="timeline-item" 
          v-for="(step, index) in faultDetail.processSteps" 
          :key="index"
          :class="{ active: step.status === 'completed', current: step.status === 'current' }"
        >
          <view class="timeline-dot"></view>
          <view class="timeline-content">
            <view class="step-header">
              <text class="step-title">{{ step.title }}</text>
              <text class="step-time" v-if="step.time">{{ step.time }}</text>
            </view>
            <text class="step-description" v-if="step.description">{{ step.description }}</text>
            <text class="step-operator" v-if="step.operator">操作人：{{ step.operator }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 维修记录 -->
    <view class="info-section">
      <view class="section-title">
        <text class="title-text">维修记录</text>
        <button class="add-record-btn" @click="addRecord" v-if="canAddRecord">添加记录</button>
      </view>
      
      <view class="records-list">
        <view 
          class="record-item" 
          v-for="(record, index) in faultDetail.repairRecords" 
          :key="index"
        >
          <view class="record-header">
            <text class="record-title">{{ record.title }}</text>
            <text class="record-time">{{ record.time }}</text>
          </view>
          <text class="record-content">{{ record.content }}</text>
          <view class="record-footer">
            <text class="record-operator">维修人员：{{ record.operator }}</text>
            <text class="record-cost" v-if="record.cost">费用：¥{{ record.cost }}</text>
          </view>
        </view>
      </view>
      
      <view class="empty-records" v-if="!faultDetail.repairRecords || faultDetail.repairRecords.length === 0">
        <text class="empty-text">暂无维修记录</text>
      </view>
    </view>

    <!-- 相关文档 -->
    <view class="info-section">
      <view class="section-title">
        <text class="title-text">相关文档</text>
        <button class="upload-btn" @click="uploadDocument" v-if="canUpload">上传文档</button>
      </view>
      
      <view class="documents-list">
        <view 
          class="document-item" 
          v-for="(doc, index) in faultDetail.documents" 
          :key="index"
          @click="downloadDocument(doc)"
        >
          <view class="doc-icon">
            <text class="icon-text">{{ getDocIcon(doc.type) }}</text>
          </view>
          <view class="doc-info">
            <text class="doc-name">{{ doc.name }}</text>
            <text class="doc-meta">{{ doc.size }} · {{ doc.uploadTime }}</text>
          </view>
          <view class="doc-actions">
            <text class="download-icon">↓</text>
          </view>
        </view>
      </view>
      
      <view class="empty-documents" v-if="!faultDetail.documents || faultDetail.documents.length === 0">
        <text class="empty-text">暂无相关文档</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons" v-if="showActionButtons">
      <button class="action-button secondary" @click="goBack">返回</button>
      <button class="action-button primary" @click="updateStatus" v-if="canUpdateStatus">
        {{ getActionButtonText() }}
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

// 响应式数据
const faultDetail = ref({})
const loading = ref(false)
const faultId = ref('')

// 定时器变量
const loadTimer = ref(null)

// 权限控制
const canEdit = computed(() => faultDetail.value.status !== 'closed')
const canAssign = computed(() => faultDetail.value.status === 'pending')
const canAddRecord = computed(() => faultDetail.value.status === 'processing')
const canUpload = computed(() => faultDetail.value.status !== 'closed')
const canUpdateStatus = computed(() => faultDetail.value.status !== 'closed')
const showActionButtons = computed(() => true)

// 方法
const loadFaultDetail = async (id) => {
  loading.value = true
  try {
    // 清除之前的定时器
    if (loadTimer.value) {
      clearTimeout(loadTimer.value)
      loadTimer.value = null
    }
    
    // 模拟API调用
    await new Promise(resolve => {
      loadTimer.value = setTimeout(() => {
        resolve()
        loadTimer.value = null
      }, 1000)
    })
    
    // 模拟故障详情数据
    const mockDetail = {
      id: id || 'F001',
      title: '设备离线故障',
      description: '办公区域A-001设备突然离线，无法正常通信。经初步检查，可能是网络连接问题或设备电源故障。需要进一步排查具体原因并及时修复，以免影响正常照明使用。',
      deviceName: '智能灯具A-001',
      deviceLocation: '办公区域A栋1楼',
      status: 'processing',
      level: 'high',
      reportTime: '2024-01-15 14:30:25',
      reporter: '张三',
      assignee: '李工程师',
      images: [
        'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=broken%20smart%20light%20fixture%20in%20office&image_size=square',
        'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=electrical%20panel%20with%20fault%20indicator&image_size=square'
      ],
      processSteps: [
        {
          title: '故障报告',
          description: '用户报告设备离线',
          time: '2024-01-15 14:30:25',
          operator: '张三',
          status: 'completed'
        },
        {
          title: '故障确认',
          description: '技术人员确认故障并分析原因',
          time: '2024-01-15 15:00:10',
          operator: '李工程师',
          status: 'completed'
        },
        {
          title: '维修处理',
          description: '正在进行设备检修和网络排查',
          time: '2024-01-15 15:30:00',
          operator: '李工程师',
          status: 'current'
        },
        {
          title: '测试验证',
          description: '待维修完成后进行功能测试',
          status: 'pending'
        },
        {
          title: '故障关闭',
          description: '确认故障已解决并关闭工单',
          status: 'pending'
        }
      ],
      repairRecords: [
        {
          title: '初步检查',
          content: '检查设备电源连接正常，网络指示灯异常闪烁，初步判断为网络通信问题。',
          time: '2024-01-15 15:15:30',
          operator: '李工程师',
          cost: null
        },
        {
          title: '网络排查',
          content: '检查网络交换机端口，发现端口松动。重新插拔网线后，设备恢复正常通信。',
          time: '2024-01-15 15:45:20',
          operator: '李工程师',
          cost: 0
        }
      ],
      documents: [
        {
          name: '设备技术手册.pdf',
          type: 'pdf',
          size: '2.5MB',
          uploadTime: '2024-01-15 16:00:00',
          url: '#'
        },
        {
          name: '维修报告.docx',
          type: 'doc',
          size: '1.2MB',
          uploadTime: '2024-01-15 16:30:00',
          url: '#'
        }
      ]
    }
    
    faultDetail.value = mockDetail
  } catch (error) {
    console.error('加载故障详情失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

const getStatusText = (status) => {
  const statusMap = {
    pending: '待处理',
    processing: '处理中',
    resolved: '已解决',
    closed: '已关闭'
  }
  return statusMap[status] || status
}

const getLevelText = (level) => {
  const levelMap = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '紧急'
  }
  return levelMap[level] || level
}

const getDocIcon = (type) => {
  const iconMap = {
    pdf: '📄',
    doc: '📝',
    docx: '📝',
    xls: '📊',
    xlsx: '📊',
    img: '🖼️',
    zip: '📦'
  }
  return iconMap[type] || '📄'
}

const getActionButtonText = () => {
  const textMap = {
    pending: '开始处理',
    processing: '标记解决',
    resolved: '关闭故障'
  }
  return textMap[faultDetail.value.status] || '更新状态'
}

const editFault = () => {
  uni.showToast({
    title: '编辑功能开发中',
    icon: 'none'
  })
}

const assignFault = () => {
  uni.showActionSheet({
    itemList: ['分配给张工程师', '分配给李技术员', '分配给王维修员'],
    success: (res) => {
      const assignees = ['张工程师', '李技术员', '王维修员']
      uni.showToast({
        title: `已分配给${assignees[res.tapIndex]}`,
        icon: 'success'
      })
    }
  })
}

const addRecord = () => {
  uni.navigateTo({
    url: `/pages/fault/add-record?faultId=${faultDetail.value.id}`
  })
}

const uploadDocument = () => {
  uni.chooseFile({
    count: 1,
    type: 'file',
    success: () => {
      uni.showToast({
        title: '文档上传成功',
        icon: 'success'
      })
    }
  })
}

const previewImage = (current, urls) => {
  uni.previewImage({
    current,
    urls
  })
}

const downloadDocument = () => {
  uni.showToast({
    title: '下载功能开发中',
    icon: 'none'
  })
}

const updateStatus = () => {
  const nextStatus = {
    pending: 'processing',
    processing: 'resolved',
    resolved: 'closed'
  }
  
  const nextStatusText = {
    pending: '处理中',
    processing: '已解决',
    resolved: '已关闭'
  }
  
  const newStatus = nextStatus[faultDetail.value.status]
  const statusText = nextStatusText[faultDetail.value.status]
  
  uni.showModal({
    title: '确认操作',
    content: `确定要将故障状态更新为"${statusText}"吗？`,
    success: (res) => {
      if (res.confirm) {
        faultDetail.value.status = newStatus
        uni.showToast({
          title: '状态更新成功',
          icon: 'success'
        })
      }
    }
  })
}

const goBack = () => {
  uni.navigateBack()
}

// 生命周期
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  faultId.value = currentPage.options.id || 'F001'
  
  loadFaultDetail(faultId.value)
})

onUnmounted(() => {
  // 清理定时器
  if (loadTimer.value) {
    clearTimeout(loadTimer.value)
    loadTimer.value = null
  }
})
</script>

<style scoped>
.fault-detail-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  padding: 15rpx 30rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  border: none;
}

.action-btn.edit {
  background: #007aff;
  color: white;
}

.action-btn.assign {
  background: #ff9500;
  color: white;
}

/* 信息区域样式 */
.info-section {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.fault-status {
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  text-align: center;
}

.fault-status.pending {
  background: #fff3e0;
  color: #ff9500;
}

.fault-status.processing {
  background: #e3f2fd;
  color: #007aff;
}

.fault-status.resolved {
  background: #e8f5e8;
  color: #34c759;
}

.fault-status.closed {
  background: #f0f0f0;
  color: #666;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.info-label {
  font-size: 24rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.info-value.level.low {
  color: #34c759;
}

.info-value.level.medium {
  color: #ff9500;
}

.info-value.level.high {
  color: #ff3b30;
}

.info-value.level.critical {
  color: #ff3b30;
  font-weight: bold;
}

/* 描述内容样式 */
.description-content {
  margin-bottom: 30rpx;
}

.description-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

.fault-images {
  margin-top: 30rpx;
}

.images-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.images-grid {
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
}

.fault-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  border: 1rpx solid #e0e0e0;
}

/* 时间线样式 */
.timeline {
  position: relative;
}

.timeline-item {
  display: flex;
  margin-bottom: 40rpx;
  position: relative;
}

.timeline-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 20rpx;
  top: 40rpx;
  width: 2rpx;
  height: calc(100% + 20rpx);
  background: #e0e0e0;
}

.timeline-item.active::after {
  background: #007aff;
}

.timeline-dot {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: #e0e0e0;
  margin-right: 30rpx;
  flex-shrink: 0;
}

.timeline-item.active .timeline-dot {
  background: #007aff;
}

.timeline-item.current .timeline-dot {
  background: #ff9500;
}

.timeline-content {
  flex: 1;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.step-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.step-time {
  font-size: 24rpx;
  color: #666;
}

.step-description {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.step-operator {
  font-size: 24rpx;
  color: #999;
}

/* 记录列表样式 */
.add-record-btn, .upload-btn {
  padding: 15rpx 30rpx;
  background: #007aff;
  color: white;
  border-radius: 8rpx;
  font-size: 24rpx;
  border: none;
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.record-item {
  padding: 30rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  border-left: 4rpx solid #007aff;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.record-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.record-time {
  font-size: 24rpx;
  color: #666;
}

.record-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 15rpx;
}

.record-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-operator, .record-cost {
  font-size: 24rpx;
  color: #999;
}

.record-cost {
  color: #ff9500;
  font-weight: 500;
}

/* 文档列表样式 */
.documents-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.document-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  border: 1rpx solid #e0e0e0;
}

.doc-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.icon-text {
  font-size: 40rpx;
}

.doc-info {
  flex: 1;
}

.doc-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 5rpx;
}

.doc-meta {
  font-size: 24rpx;
  color: #666;
}

.doc-actions {
  padding: 10rpx;
}

.download-icon {
  font-size: 32rpx;
  color: #007aff;
}

/* 空状态样式 */
.empty-records, .empty-documents {
  text-align: center;
  padding: 60rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 操作按钮样式 */
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 20rpx;
  padding: 20rpx;
  background: white;
  border-top: 1rpx solid #e0e0e0;
}

.action-button {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
}

.action-button.secondary {
  background: #f0f0f0;
  color: #333;
}

.action-button.primary {
  background: #007aff;
  color: white;
}
</style>