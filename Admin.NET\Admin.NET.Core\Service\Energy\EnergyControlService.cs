// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System.Linq;
using Microsoft.AspNetCore.Mvc;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.Drawing;

namespace Admin.NET.Core.Service;

/// <summary>
/// 设备控制记录服务 🧩
/// </summary>
[ApiDescriptionSettings(Order = 504)]
public class EnergyControlService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<EnergyControl> _energyControlRep;
    private readonly SqlSugarRepository<EnergyDevice> _energyDeviceRep;
    private readonly SqlSugarRepository<EnergyScene> _energySceneRep;
    private readonly SysCacheService _sysCacheService;
    private readonly UserManager _userManager;

    public EnergyControlService(
        SqlSugarRepository<EnergyControl> energyControlRep,
        SqlSugarRepository<EnergyDevice> energyDeviceRep,
        SqlSugarRepository<EnergyScene> energySceneRep,
        SysCacheService sysCacheService,
        UserManager userManager)
    {
        _energyControlRep = energyControlRep;
        _energyDeviceRep = energyDeviceRep;
        _energySceneRep = energySceneRep;
        _sysCacheService = sysCacheService;
        _userManager = userManager;
    }

    /// <summary>
    /// 获取控制记录分页列表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取控制记录分页列表")]
    public async Task<SqlSugarPagedList<EnergyControlOutput>> GetPage([FromQuery] EnergyControlInput input)
    {
        var query = _energyControlRep.AsQueryable()
            .LeftJoin<EnergyDevice>((c, d) => c.DeviceId == d.Id)
            .LeftJoin<EnergyScene>((c, d, s) => c.SceneId == s.Id)
            .WhereIF(input.DeviceId > 0, (c, d, s) => c.DeviceId == input.DeviceId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeviceCode), (c, d, s) => d.DeviceCode.Contains(input.DeviceCode))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeviceName), (c, d, s) => d.DeviceName.Contains(input.DeviceName))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ControlType), (c, d, s) => c.ControlType.ToString().Contains(input.ControlType))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ControlSource), (c, d, s) => c.ControlSource.ToString().Contains(input.ControlSource))
            .WhereIF(input.ExecuteStatus.HasValue, (c, d, s) => c.ExecuteStatus == input.ExecuteStatus)
            .WhereIF(input.StartTime.HasValue, (c, d, s) => c.ControlTime >= input.StartTime)
            .WhereIF(input.EndTime.HasValue, (c, d, s) => c.ControlTime <= input.EndTime)
            .Select((c, d, s) => new EnergyControlOutput
            {
                Id = c.Id,
                DeviceId = c.DeviceId,
                DeviceCode = d.DeviceCode,
                DeviceName = d.DeviceName,
                DeviceLocation = d.Location,
                ControlType = c.ControlType.ToString(),
                ControlCommand = c.ControlCommand,
                ControlParams = c.ControlParams,
                ControlParameters = c.ControlParams,
                ControlTime = c.ControlTime,
                ExecuteStatus = c.ExecuteStatus,
                ExecuteStatusName = c.ExecuteStatus == 1 ? "待执行" : c.ExecuteStatus == 2 ? "执行中" : c.ExecuteStatus == 3 ? "执行成功" : "执行失败",
                ExecuteResult = c.ExecuteResult,
                ErrorMessage = c.ErrorMessage,
                ControlSource = c.ControlSource,
                SceneId = c.SceneId,
                SceneName = s.SceneName,
                ExecuteDuration = c.ExecuteDuration,
                CreateTime = c.CreateTime,
                TenantId = c.TenantId
            })
            .OrderBy(c => c.ControlTime, OrderByType.Desc);

        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取控制记录详情 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取控制记录详情")]
    public async Task<EnergyControlOutput> GetDetail([FromQuery] QueryByIdEnergyControlInput input)
    {
        var control = await _energyControlRep.AsQueryable()
            .LeftJoin<EnergyDevice>((c, d) => c.DeviceId == d.Id)
            .LeftJoin<EnergyScene>((c, d, s) => c.SceneId == s.Id)
            .Where((c, d, s) => c.Id == input.Id)
            .Select((c, d, s) => new EnergyControlOutput
            {
                Id = c.Id,
                DeviceId = c.DeviceId,
                DeviceCode = d.DeviceCode,
                DeviceName = d.DeviceName,
                DeviceLocation = d.Location,
                DeviceType = d.DeviceType,
                DeviceModel = d.DeviceModel,
                ControlType = c.ControlType.ToString(),
                ControlCommand = c.ControlCommand,
                ControlParameters = c.ControlParams,
                ControlTime = c.ControlTime,
                ExecuteStatus = c.ExecuteStatus,
                ExecuteResult = c.ExecuteResult,
                ErrorMessage = c.ErrorMessage,
                ControlSource = c.ControlSource,
                SceneId = c.SceneId,
                SceneName = s.SceneName,
                ExecuteDuration = c.ExecuteDuration,
                CreateTime = c.CreateTime,
                UpdateTime = c.UpdateTime,
                TenantId = c.TenantId
            })
            .FirstAsync();

        // 设置执行状态名称
        control.ExecuteStatusName = control.ExecuteStatus switch
        {
            1 => "待执行",
            2 => "执行中",
            3 => "执行成功",
            _ => "执行失败"
        };

        return control;
    }

    /// <summary>
    /// 增加控制记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [DisplayName("增加控制记录")]
    public async Task<long> AddControl(AddEnergyControlInput input)
    {
        // 检查设备是否存在
        var deviceExist = await _energyDeviceRep.IsAnyAsync(u => u.Id == input.DeviceId);
        if (!deviceExist)
            throw Oops.Oh("设备不存在");

        // 检查场景是否存在（如果指定了场景）
        if (input.SceneId.HasValue && input.SceneId > 0)
        {
            var sceneExist = await _energySceneRep.IsAnyAsync(u => u.Id == input.SceneId);
            if (!sceneExist)
                throw Oops.Oh("场景不存在");
        }

        var control = input.Adapt<EnergyControl>();
        control.TenantId = _userManager.TenantId;
        control.CreateUserId = _userManager.UserId;
        control.CreateTime = DateTime.Now;
        control.ExecuteStatus = 0; // 默认执行中状态

        // 如果未指定控制时间，使用当前时间
        if (control.ControlTime == default)
            control.ControlTime = DateTime.Now;

        var newControl = await _energyControlRep.AsInsertable(control).ExecuteReturnEntityAsync();

        // TODO: 异步执行设备控制命令
        // _ = Task.Run(async () => await ExecuteDeviceControlAsync(newControl));

        return newControl.Id;
    }

    /// <summary>
    /// 设备控制指令 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "DeviceControl"), HttpPost]
    [DisplayName("设备控制指令")]
    public async Task<long> DeviceControl(DeviceControlInput input)
    {
        // 检查设备是否存在且在线
        var device = await _energyDeviceRep.GetByIdAsync(input.DeviceId);
        if (device == null)
            throw Oops.Oh("设备不存在");

        if (device.Status != 1)
            throw Oops.Oh("设备离线，无法执行控制指令");

        // 创建控制记录
        var control = new EnergyControl
        {
            DeviceId = input.DeviceId,
            ControlType = int.TryParse(input.ControlType, out var controlType) ? controlType : 1,
            ControlCommand = input.ControlCommand,
            ControlParams = input.ControlParams,
            ControlTime = DateTime.Now,
            ExecuteStatus = 0, // 执行中
            ControlSource = 1, // 手动控制
            TenantId = _userManager.TenantId,
            CreateUserId = _userManager.UserId,
            CreateTime = DateTime.Now
        };

        var newControl = await _energyControlRep.AsInsertable(control).ExecuteReturnEntityAsync();

        // TODO: 发送MQTT控制指令到设备
        // var success = await SendControlCommandAsync(device, input);
        // await UpdateControlResultAsync(newControl.Id, success);

        return newControl.Id;
    }

    /// <summary>
    /// 更新控制记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [DisplayName("更新控制记录")]
    public async Task UpdateControl(UpdateEnergyControlInput input)
    {
        // 检查记录是否存在
        var control = await _energyControlRep.GetByIdAsync(input.Id);
        if (control == null)
            throw Oops.Oh("控制记录不存在");

        // 检查设备是否存在
        var deviceExist = await _energyDeviceRep.IsAnyAsync(u => u.Id == input.DeviceId);
        if (!deviceExist)
            throw Oops.Oh("设备不存在");

        // 检查场景是否存在（如果指定了场景）
        if (input.SceneId.HasValue && input.SceneId > 0)
        {
            var sceneExist = await _energySceneRep.IsAnyAsync(u => u.Id == input.SceneId);
            if (!sceneExist)
                throw Oops.Oh("场景不存在");
        }

        var updateControl = input.Adapt<EnergyControl>();
        updateControl.UpdateUserId = _userManager.UserId;
        updateControl.UpdateTime = DateTime.Now;

        await _energyControlRep.AsUpdateable(updateControl).ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除控制记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    [DisplayName("删除控制记录")]
    public async Task DeleteControl(DeleteEnergyControlInput input)
    {
        var control = await _energyControlRep.GetByIdAsync(input.Id);
        if (control == null)
            throw Oops.Oh("控制记录不存在");

        await _energyControlRep.DeleteAsync(control);
    }

    /// <summary>
    /// 更新控制结果 🔖
    /// </summary>
    /// <param name="controlId">控制记录ID</param>
    /// <param name="status">执行状态</param>
    /// <param name="result">执行结果</param>
    /// <param name="errorMessage">错误信息</param>
    /// <param name="duration">执行耗时</param>
    /// <returns></returns>
    [DisplayName("更新控制结果")]
    public async Task UpdateControlResult(long controlId, int status, string result = null, string errorMessage = null, int? duration = null)
    {
        var control = await _energyControlRep.GetByIdAsync(controlId);
        if (control == null)
            return;

        control.ExecuteStatus = status;
        control.ExecuteResult = result;
        control.ErrorMessage = errorMessage;
        control.ExecuteDuration = duration ?? 0;
        control.UpdateUserId = _userManager.UserId;
        control.UpdateTime = DateTime.Now;

        await _energyControlRep.AsUpdateable(control)
            .UpdateColumns(u => new { u.ExecuteStatus, u.ExecuteResult, u.ErrorMessage, u.ExecuteDuration, u.UpdateUserId, u.UpdateTime })
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取控制统计数据 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取控制统计数据")]
    public async Task<EnergyControlStatOutput> GetStat([FromQuery] EnergyControlStatInput input)
    {
        var query = _energyControlRep.AsQueryable()
            .LeftJoin<EnergyDevice>((c, d) => c.DeviceId == d.Id)
            .WhereIF(input.DeviceId > 0, (c, d) => c.DeviceId == input.DeviceId)
            .WhereIF(input.GroupId > 0, (c, d) => d.GroupId == input.GroupId)
            .WhereIF(input.StartTime.HasValue, (c, d) => c.ControlTime >= input.StartTime)
            .WhereIF(input.EndTime.HasValue, (c, d) => c.ControlTime <= input.EndTime);

        var totalCount = await query.CountAsync();
        var successCount = await query.Where((c, d) => c.ExecuteStatus == 1).CountAsync();
        var failedCount = await query.Where((c, d) => c.ExecuteStatus == 2).CountAsync();
        var timeoutCount = await query.Where((c, d) => c.ExecuteStatus == 3).CountAsync();
        var executingCount = await query.Where((c, d) => c.ExecuteStatus == 0).CountAsync();

        var sourceStats = await query
            .GroupBy((c, d) => c.ControlSource)
            .Select((c, d) => new { Source = c.ControlSource, Count = SqlFunc.AggregateCount(c.Id) })
            .ToListAsync();

        var manualCount = sourceStats.FirstOrDefault(x => x.Source == 1)?.Count ?? 0;
        var autoCount = sourceStats.FirstOrDefault(x => x.Source == 4)?.Count ?? 0;
        var sceneCount = sourceStats.FirstOrDefault(x => x.Source == 3)?.Count ?? 0;
        var timerCount = sourceStats.FirstOrDefault(x => x.Source == 2)?.Count ?? 0;

        // 计算平均执行时长（毫秒）
        var avgDuration = await query
            .Where((c, d) => c.ExecuteDuration > 0)
            .Select((c, d) => c.ExecuteDuration)
            .ToListAsync();

        return new EnergyControlStatOutput
        {
            StatTime = DateTime.Now,
            TotalCount = totalCount,
            SuccessCount = successCount,
            FailedCount = failedCount,
            TimeoutCount = timeoutCount,
            ExecutingCount = executingCount,
            ManualCount = manualCount,
            AutoCount = autoCount,
            SceneCount = sceneCount,
            TimerCount = timerCount,
            AvgExecuteDuration = avgDuration.Any() ? (decimal)avgDuration.Average() : 0,
            SuccessRate = totalCount > 0 ? (decimal)successCount / totalCount * 100 : 0
        };
    }

    /// <summary>
    /// 获取控制趋势数据 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取控制趋势数据")]
    public async Task<List<EnergyControlTrendOutput>> GetTrend([FromQuery] EnergyControlStatInput input)
    {
        var query = _energyControlRep.AsQueryable()
            .LeftJoin<EnergyDevice>((c, d) => c.DeviceId == d.Id)
            .WhereIF(input.DeviceId > 0, (c, d) => c.DeviceId == input.DeviceId)
            .WhereIF(input.GroupId > 0, (c, d) => d.GroupId == input.GroupId)
            .WhereIF(input.StartTime.HasValue, (c, d) => c.ControlTime >= input.StartTime)
            .WhereIF(input.EndTime.HasValue, (c, d) => c.ControlTime <= input.EndTime);

        // 按小时分组统计
        var trendData = await query
            .GroupBy((c, d) => new { Hour = SqlFunc.DateValue(c.ControlTime, DateType.Hour) })
            .Select((c, d) => new EnergyControlTrendOutput
            {
                Time = DateTime.Now,
                TotalCount = SqlFunc.AggregateCount(c.Id),
                SuccessCount = SqlFunc.AggregateSum(SqlFunc.IIF(c.ExecuteStatus == 1, 1, 0)),
                FailedCount = SqlFunc.AggregateSum(SqlFunc.IIF(c.ExecuteStatus == 2, 1, 0)),
                ManualCount = SqlFunc.AggregateSum(SqlFunc.IIF(c.ControlSource == 1, 1, 0)),
                AutoCount = SqlFunc.AggregateSum(SqlFunc.IIF(c.ControlSource == 4, 1, 0)),
                SceneCount = SqlFunc.AggregateSum(SqlFunc.IIF(c.ControlSource == 3, 1, 0)),
                AvgExecuteDuration = SqlFunc.AggregateAvg(c.ExecuteDuration)
            })
            .OrderBy(x => x.Time)
            .ToListAsync();

        return trendData;
    }

    /// <summary>
    /// 批量删除历史记录 🔖
    /// </summary>
    /// <param name="days">保留天数</param>
    /// <returns></returns>
    [DisplayName("批量删除历史记录")]
    public async Task<int> CleanHistoryData(int days = 30)
    {
        var cutoffDate = DateTime.Now.AddDays(-days);
        return await _energyControlRep.AsDeleteable()
            .Where(u => u.CreateTime < cutoffDate)
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 重试失败的控制指令 🔖
    /// </summary>
    /// <param name="controlId">控制记录ID</param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Retry"), HttpPost]
    [DisplayName("重试失败的控制指令")]
    public async Task<bool> RetryControl(long controlId)
    {
        var control = await _energyControlRep.GetByIdAsync(controlId);
        if (control == null)
            throw Oops.Oh("控制记录不存在");

        if (control.ExecuteStatus == 1)
            throw Oops.Oh("控制指令已成功执行，无需重试");

        // 重置状态为执行中
        control.ExecuteStatus = 0;
        control.ErrorMessage = null;
        control.UpdateUserId = _userManager.UserId;
        control.UpdateTime = DateTime.Now;

        await _energyControlRep.AsUpdateable(control)
            .UpdateColumns(u => new { u.ExecuteStatus, u.ErrorMessage, u.UpdateUserId, u.UpdateTime })
            .ExecuteCommandAsync();

        // TODO: 重新发送控制指令
        // return await ResendControlCommandAsync(control);

        return true;
    }

    /// <summary>
    /// 导出控制记录数据 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出控制记录数据")]
    public async Task<FileStreamResult> ExportData([FromQuery] EnergyControlInput input)
    {
        var data = await _energyControlRep.AsQueryable()
            .LeftJoin<EnergyDevice>((c, d) => c.DeviceId == d.Id)
            .LeftJoin<EnergyScene>((c, d, s) => c.SceneId == s.Id)
            .WhereIF(input.DeviceId > 0, (c, d, s) => c.DeviceId == input.DeviceId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeviceCode), (c, d, s) => d.DeviceCode.Contains(input.DeviceCode))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeviceName), (c, d, s) => d.DeviceName.Contains(input.DeviceName))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ControlType), (c, d, s) => c.ControlType.ToString().Contains(input.ControlType))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ControlSource), (c, d, s) => c.ControlSource.ToString().Contains(input.ControlSource))
            .WhereIF(input.ExecuteStatus.HasValue, (c, d, s) => c.ExecuteStatus == input.ExecuteStatus)
            .WhereIF(input.StartTime.HasValue, (c, d, s) => c.ControlTime >= input.StartTime)
            .WhereIF(input.EndTime.HasValue, (c, d, s) => c.ControlTime <= input.EndTime)
            .OrderBy((c, d, s) => c.ControlTime, OrderByType.Desc)
            .Select((c, d, s) => new
            {
                设备编码 = d.DeviceCode,
                设备名称 = d.DeviceName,
                设备位置 = d.Location,
                控制类型 = c.ControlType.ToString(),
                控制指令 = c.ControlCommand,
                控制参数 = c.ControlParams,
                控制时间 = c.ControlTime,
                执行状态 = c.ExecuteStatus == 1 ? "待执行" : c.ExecuteStatus == 2 ? "执行中" : c.ExecuteStatus == 3 ? "执行成功" : "执行失败",
                执行结果 = c.ExecuteResult,
                错误信息 = c.ErrorMessage,
                控制来源 = c.ControlSource.ToString(),
                场景名称 = s.SceneName,
                执行时长_毫秒 = c.ExecuteDuration,
                创建时间 = c.CreateTime
            })
            .ToListAsync();

        // 实现Excel导出功能
        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add("控制记录数据");
        
        // 设置表头
        var headers = new string[]
        {
            "设备编码", "设备名称", "设备位置", "控制类型", "控制指令", "控制参数", "控制时间", 
            "执行状态", "执行结果", "错误信息", "控制来源", "场景名称", "执行时长(毫秒)", "创建时间"
        };
        
        for (int i = 0; i < headers.Length; i++)
        {
            worksheet.Cells[1, i + 1].Value = headers[i];
            worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            worksheet.Cells[1, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
            worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(211, 211, 211, 255);
        }
        
        // 填充数据
        for (int row = 0; row < data.Count; row++)
        {
            var item = data[row];
            worksheet.Cells[row + 2, 1].Value = item.设备编码;
            worksheet.Cells[row + 2, 2].Value = item.设备名称;
            worksheet.Cells[row + 2, 3].Value = item.设备位置;
            worksheet.Cells[row + 2, 4].Value = item.控制类型;
            worksheet.Cells[row + 2, 5].Value = item.控制指令;
            worksheet.Cells[row + 2, 6].Value = item.控制参数;
            worksheet.Cells[row + 2, 7].Value = item.控制时间.ToString("yyyy-MM-dd HH:mm:ss");
            worksheet.Cells[row + 2, 8].Value = item.执行状态;
            worksheet.Cells[row + 2, 9].Value = item.执行结果;
            worksheet.Cells[row + 2, 10].Value = item.错误信息;
            worksheet.Cells[row + 2, 11].Value = item.控制来源;
            worksheet.Cells[row + 2, 12].Value = item.场景名称;
            worksheet.Cells[row + 2, 13].Value = item.执行时长_毫秒;
            worksheet.Cells[row + 2, 14].Value = item.创建时间.ToString("yyyy-MM-dd HH:mm:ss");
        }
        
        // 自动调整列宽
        worksheet.Cells.AutoFitColumns();
        
        var stream = new MemoryStream();
        package.SaveAs(stream);
        stream.Position = 0;
        
        return new FileStreamResult(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        {
            FileDownloadName = $"控制记录数据_{DateTime.Now:yyyyMMddHHmmss}.xlsx"
        };
    }

    /// <summary>
    /// 导出控制记录数据到Excel 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "ExportExcel"), HttpPost]
    [DisplayName("导出控制记录数据到Excel")]
    public async Task<IActionResult> ExportExcel(EnergyControlInput input)
    {
        var excelData = await ExportData(input);
        var fileName = $"控制记录数据_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
        
        return new FileContentResult(((MemoryStream)excelData.FileStream).ToArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        {
            FileDownloadName = fileName
        };
    }
}