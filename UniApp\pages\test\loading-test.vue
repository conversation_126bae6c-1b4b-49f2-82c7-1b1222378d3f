<template>
  <view class="loading-test-container">
    <view class="test-header">
      <text class="title">加载状态管理测试</text>
    </view>
    
    <scroll-view class="test-content" scroll-y>
      <!-- 全局Loading测试 -->
      <view class="test-section">
        <text class="section-title">全局Loading测试</text>
        <view class="button-group">
          <button @tap="testGlobalLoading" class="test-btn">测试全局Loading</button>
          <button @tap="testGlobalLoadingWithText" class="test-btn">带文字的全局Loading</button>
          <button @tap="testGlobalLoadingTimeout" class="test-btn">超时测试</button>
        </view>
      </view>
      
      <!-- 局部Loading测试 -->
      <view class="test-section">
        <text class="section-title">局部Loading测试</text>
        <view class="local-loading-demo" :class="{ 'loading': localLoading }">
          <view v-if="localLoading" class="local-loading-overlay">
            <view class="loading-spinner"></view>
            <text class="loading-text">加载中...</text>
          </view>
          <view v-else class="demo-content">
            <text>这是局部内容区域</text>
            <text>点击按钮测试局部Loading</text>
          </view>
        </view>
        <button @tap="testLocalLoading" class="test-btn">测试局部Loading</button>
      </view>
      
      <!-- 骨架屏测试 -->
      <view class="test-section">
        <text class="section-title">骨架屏测试</text>
        <view class="skeleton-demo">
          <view v-if="showSkeleton" class="skeleton-content">
            <view class="skeleton-item">
              <view class="skeleton-avatar"></view>
              <view class="skeleton-text">
                <view class="skeleton-line long"></view>
                <view class="skeleton-line short"></view>
              </view>
            </view>
            <view class="skeleton-item">
              <view class="skeleton-avatar"></view>
              <view class="skeleton-text">
                <view class="skeleton-line long"></view>
                <view class="skeleton-line short"></view>
              </view>
            </view>
          </view>
          <view v-else class="real-content">
            <view class="content-item">
              <image class="avatar" src="/static/images/avatar1.png" mode="aspectFill"></image>
              <view class="text-content">
                <text class="name">张三</text>
                <text class="desc">这是真实的内容数据</text>
              </view>
            </view>
            <view class="content-item">
              <image class="avatar" src="/static/images/avatar2.png" mode="aspectFill"></image>
              <view class="text-content">
                <text class="name">李四</text>
                <text class="desc">骨架屏加载完成后显示</text>
              </view>
            </view>
          </view>
        </view>
        <button @tap="testSkeleton" class="test-btn">测试骨架屏</button>
      </view>
      
      <!-- 进度条Loading测试 -->
      <view class="test-section">
        <text class="section-title">进度条Loading测试</text>
        <view class="progress-demo">
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: progress + '%' }"></view>
          </view>
          <text class="progress-text">{{ progress }}%</text>
        </view>
        <button @tap="testProgressLoading" class="test-btn">测试进度条</button>
      </view>
      
      <!-- 网络请求Loading测试 -->
      <view class="test-section">
        <text class="section-title">网络请求Loading测试</text>
        <view class="request-demo">
          <text class="demo-text">{{ requestResult }}</text>
        </view>
        <view class="button-group">
          <button @tap="testSuccessRequest" class="test-btn success">成功请求</button>
          <button @tap="testFailRequest" class="test-btn error">失败请求</button>
          <button @tap="testRetryRequest" class="test-btn warning">重试请求</button>
        </view>
      </view>
      
      <!-- 并发Loading测试 -->
      <view class="test-section">
        <text class="section-title">并发Loading测试</text>
        <view class="concurrent-demo">
          <text class="demo-text">当前活跃Loading数量: {{ activeLoadingCount }}</text>
        </view>
        <view class="button-group">
          <button @tap="testConcurrentLoading" class="test-btn">并发Loading</button>
          <button @tap="clearAllLoading" class="test-btn error">清除所有Loading</button>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { Loading } from '../../utils/loadingManager.js'

export default {
  name: 'LoadingTest',
  data() {
    return {
      localLoading: false,
      showSkeleton: false,
      progress: 0,
      requestResult: '点击按钮测试网络请求',
      activeLoadingCount: 0,
      loadingTimers: [], // 存储所有setTimeout定时器
      progressTimer: null // 存储进度条定时器
    }
  },
  
  methods: {
    /**
     * 测试全局Loading
     */
    async testGlobalLoading() {
      Loading.showGlobal()
      
      // 模拟异步操作
      await this.delay(2000)
      
      Loading.hideGlobal()
      this.showToast('全局Loading测试完成')
    },
    
    /**
     * 测试带文字的全局Loading
     */
    async testGlobalLoadingWithText() {
      Loading.showGlobal({
        text: '正在处理数据...',
        type: 'spinner'
      })
      
      await this.delay(3000)
      
      Loading.hideGlobal()
      this.showToast('带文字的全局Loading测试完成')
    },
    
    /**
     * 测试全局Loading超时
     */
    async testGlobalLoadingTimeout() {
      Loading.showGlobal({
        text: '测试超时处理...',
        timeout: 2000
      })
      
      // 不手动隐藏，让其超时自动隐藏
      await this.delay(3000)
      
      this.showToast('超时测试完成')
    },
    
    /**
     * 测试局部Loading
     */
    async testLocalLoading() {
      this.localLoading = true
      
      await this.delay(2000)
      
      this.localLoading = false
      this.showToast('局部Loading测试完成')
    },
    
    /**
     * 测试骨架屏
     */
    async testSkeleton() {
      this.showSkeleton = true
      
      // 模拟数据加载
      await this.delay(3000)
      
      this.showSkeleton = false
      this.showToast('骨架屏测试完成')
    },
    
    /**
     * 测试进度条Loading
     */
    async testProgressLoading() {
      this.progress = 0
      
      // 清理之前的定时器
      if (this.progressTimer) {
        clearInterval(this.progressTimer)
        this.progressTimer = null
      }
      
      this.progressTimer = setInterval(() => {
        this.progress += 10
        
        if (this.progress >= 100) {
          clearInterval(this.progressTimer)
          this.progressTimer = null
          this.showToast('进度条测试完成')
        }
      }, 200)
    },
    
    /**
     * 测试成功请求
     */
    async testSuccessRequest() {
      this.requestResult = '请求中...'
      
      Loading.showGlobal({ text: '请求数据中...' })
      
      try {
        // 模拟成功请求
        await this.delay(2000)
        this.requestResult = '请求成功！数据加载完成'
        this.showToast('请求成功')
      } catch (error) {
        this.requestResult = '请求失败: ' + error.message
      } finally {
        Loading.hideGlobal()
      }
    },
    
    /**
     * 测试失败请求
     */
    async testFailRequest() {
      this.requestResult = '请求中...'
      
      Loading.showGlobal({ text: '请求数据中...' })
      
      try {
        // 模拟失败请求
        await this.delay(1500)
        throw new Error('网络连接失败')
      } catch (error) {
        this.requestResult = '请求失败: ' + error.message
        uni.showToast({
          title: '请求失败',
          icon: 'error',
          duration: 2000
        })
      } finally {
        Loading.hideGlobal()
      }
    },
    
    /**
     * 测试重试请求
     */
    async testRetryRequest() {
      this.requestResult = '请求中...'
      let retryCount = 0
      const maxRetries = 3
      
      const attemptRequest = async () => {
        Loading.showGlobal({ text: `请求中... (${retryCount + 1}/${maxRetries + 1})` })
        
        try {
          await this.delay(1000)
          
          // 前两次模拟失败，第三次成功
          if (retryCount < 2) {
            throw new Error('网络超时')
          }
          
          this.requestResult = '重试成功！数据加载完成'
          this.showToast('重试成功')
        } catch (error) {
          if (retryCount < maxRetries) {
            retryCount++
            this.requestResult = `请求失败，正在重试... (${retryCount}/${maxRetries})`
            Loading.hideGlobal()
            await this.delay(1000)
            return attemptRequest()
          } else {
            this.requestResult = '重试失败: ' + error.message
            uni.showToast({
              title: '重试失败',
              icon: 'error',
              duration: 2000
            })
          }
        } finally {
          Loading.hideGlobal()
        }
      }
      
      await attemptRequest()
    },
    
    /**
     * 测试并发Loading
     */
    async testConcurrentLoading() {
      const loadingIds = []
      
      // 创建多个并发Loading
      for (let i = 0; i < 3; i++) {
        const id = Loading.show({
          text: `Loading ${i + 1}`,
          type: 'spinner'
        })
        loadingIds.push(id)
        this.activeLoadingCount++
      }
      
      // 分别在不同时间隐藏
      const timer1 = setTimeout(() => {
        Loading.hide(loadingIds[0])
        this.activeLoadingCount--
      }, 2000)
      this.loadingTimers.push(timer1)
      
      const timer2 = setTimeout(() => {
        Loading.hide(loadingIds[1])
        this.activeLoadingCount--
      }, 3000)
      this.loadingTimers.push(timer2)
      
      const timer3 = setTimeout(() => {
        Loading.hide(loadingIds[2])
        this.activeLoadingCount--
        this.showToast('并发Loading测试完成')
      }, 4000)
      this.loadingTimers.push(timer3)
    },
    
    /**
     * 清除所有Loading
     */
    clearAllLoading() {
      Loading.hideAll()
      this.activeLoadingCount = 0
      this.localLoading = false
      this.showToast('已清除所有Loading')
    },
    
    /**
     * 延迟函数
     */
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    },
    
    /**
     * 显示提示
     */
    showToast(title) {
      uni.showToast({
        title,
        icon: 'success',
        duration: 2000
      })
    }
  },
  
  // 页面卸载时清理定时器
  onUnload() {
    // 清理进度条定时器
    if (this.progressTimer) {
      clearInterval(this.progressTimer)
      this.progressTimer = null
    }
    
    // 清理所有可能的setTimeout定时器
    if (this.loadingTimers) {
      this.loadingTimers.forEach(timer => {
        if (timer) {
          clearTimeout(timer)
        }
      })
      this.loadingTimers = []
    }
  }
}
</script>

<style lang="scss" scoped>
.loading-test-container {
  height: 100vh;
  background-color: #f5f5f5;
}

.test-header {
  padding: 40rpx 32rpx 20rpx;
  background-color: #ffffff;
  border-bottom: 2rpx solid #eee;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.test-content {
  flex: 1;
  padding: 20rpx;
}

.test-section {
  margin-bottom: 40rpx;
  padding: 32rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  
  .section-title {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 24rpx;
  }
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.test-btn {
  flex: 1;
  min-width: 200rpx;
  height: 80rpx;
  background-color: #1890ff;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  
  &.success {
    background-color: #52c41a;
  }
  
  &.error {
    background-color: #ff4d4f;
  }
  
  &.warning {
    background-color: #faad14;
  }
}

.local-loading-demo {
  position: relative;
  height: 200rpx;
  background-color: #f9f9f9;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  
  .local-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 10;
  }
  
  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 6rpx solid #f3f3f3;
    border-top: 6rpx solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  .loading-text {
    margin-top: 16rpx;
    font-size: 28rpx;
    color: #666;
  }
  
  .demo-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    
    text {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 8rpx;
    }
  }
}

.skeleton-demo {
  margin-bottom: 20rpx;
}

.skeleton-content {
  .skeleton-item {
    display: flex;
    align-items: center;
    margin-bottom: 32rpx;
    
    .skeleton-avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: skeleton-loading 1.5s infinite;
    }
    
    .skeleton-text {
      flex: 1;
      margin-left: 24rpx;
      
      .skeleton-line {
        height: 24rpx;
        border-radius: 4rpx;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        margin-bottom: 16rpx;
        
        &.long {
          width: 80%;
        }
        
        &.short {
          width: 60%;
        }
      }
    }
  }
}

.real-content {
  .content-item {
    display: flex;
    align-items: center;
    margin-bottom: 32rpx;
    
    .avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
    }
    
    .text-content {
      flex: 1;
      margin-left: 24rpx;
      
      .name {
        display: block;
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 8rpx;
      }
      
      .desc {
        display: block;
        font-size: 28rpx;
        color: #666;
      }
    }
  }
}

.progress-demo {
  margin-bottom: 20rpx;
  
  .progress-bar {
    width: 100%;
    height: 16rpx;
    background-color: #f0f0f0;
    border-radius: 8rpx;
    overflow: hidden;
    margin-bottom: 16rpx;
    
    .progress-fill {
      height: 100%;
      background-color: #1890ff;
      border-radius: 8rpx;
      transition: width 0.3s ease;
    }
  }
  
  .progress-text {
    display: block;
    text-align: center;
    font-size: 28rpx;
    color: #666;
  }
}

.request-demo {
  margin-bottom: 20rpx;
  
  .demo-text {
    display: block;
    padding: 24rpx;
    background-color: #f9f9f9;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: #333;
    min-height: 80rpx;
  }
}

.concurrent-demo {
  margin-bottom: 20rpx;
  
  .demo-text {
    display: block;
    padding: 24rpx;
    background-color: #f0f9ff;
    border: 2rpx solid #1890ff;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: #1890ff;
    text-align: center;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes skeleton-loading {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}
</style>