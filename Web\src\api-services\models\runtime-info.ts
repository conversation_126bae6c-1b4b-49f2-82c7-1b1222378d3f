/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 
 * @export
 * @interface RuntimeInfo
 */
export interface RuntimeInfo {
    /**
     * 
     * @type {string}
     * @memberof RuntimeInfo
     */
    osName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof RuntimeInfo
     */
    osDescription?: string | null;
    /**
     * 
     * @type {string}
     * @memberof RuntimeInfo
     */
    osVersion?: string | null;
    /**
     * 
     * @type {string}
     * @memberof RuntimeInfo
     */
    osArchitecture?: string | null;
    /**
     * 
     * @type {string}
     * @memberof RuntimeInfo
     */
    processArchitecture?: string | null;
    /**
     * 
     * @type {string}
     * @memberof RuntimeInfo
     */
    frameworkDescription?: string | null;
    /**
     * 
     * @type {string}
     * @memberof RuntimeInfo
     */
    runtimeVersion?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof RuntimeInfo
     */
    is64BitOperatingSystem?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof RuntimeInfo
     */
    is64BitProcess?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof RuntimeInfo
     */
    isInteractive?: boolean;
    /**
     * 
     * @type {string}
     * @memberof RuntimeInfo
     */
    interactiveMode?: string | null;
    /**
     * 
     * @type {number}
     * @memberof RuntimeInfo
     */
    processorCount?: number;
    /**
     * 
     * @type {string}
     * @memberof RuntimeInfo
     */
    systemDirectory?: string | null;
    /**
     * 
     * @type {string}
     * @memberof RuntimeInfo
     */
    currentDirectory?: string | null;
    /**
     * 
     * @type {string}
     * @memberof RuntimeInfo
     */
    machineName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof RuntimeInfo
     */
    userName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof RuntimeInfo
     */
    userDomainName?: string | null;
    /**
     * 
     * @type {number}
     * @memberof RuntimeInfo
     */
    workingSet?: number;
    /**
     * 
     * @type {Date}
     * @memberof RuntimeInfo
     */
    systemStartTime?: Date;
    /**
     * 
     * @type {string}
     * @memberof RuntimeInfo
     */
    systemUptime?: string;
    /**
     * 
     * @type {Date}
     * @memberof RuntimeInfo
     */
    processStartTime?: Date;
    /**
     * 
     * @type {string}
     * @memberof RuntimeInfo
     */
    processUptime?: string;
    /**
     * 
     * @type {number}
     * @memberof RuntimeInfo
     */
    processId?: number;
    /**
     * 
     * @type {string}
     * @memberof RuntimeInfo
     */
    processName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof RuntimeInfo
     */
    clrVersion?: string | null;
    /**
     * 
     * @type {number}
     * @memberof RuntimeInfo
     */
    environmentVariableCount?: number;
    /**
     * 
     * @type {Array<string>}
     * @memberof RuntimeInfo
     */
    commandLineArgs?: Array<string> | null;
}
