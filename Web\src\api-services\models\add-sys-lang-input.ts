/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { DirectionEnum } from './direction-enum';
import { WeekEnum } from './week-enum';
/**
 * 多语言增加输入参数
 * @export
 * @interface AddSysLangInput
 */
export interface AddSysLangInput {
    /**
     * 语言名称
     * @type {string}
     * @memberof AddSysLangInput
     */
    name: string;
    /**
     * 语言代码
     * @type {string}
     * @memberof AddSysLangInput
     */
    code: string;
    /**
     * ISO 语言代码
     * @type {string}
     * @memberof AddSysLangInput
     */
    isoCode: string;
    /**
     * URL 语言代码
     * @type {string}
     * @memberof AddSysLangInput
     */
    urlCode: string;
    /**
     * 
     * @type {DirectionEnum}
     * @memberof AddSysLangInput
     */
    direction: DirectionEnum;
    /**
     * 日期格式
     * @type {string}
     * @memberof AddSysLangInput
     */
    dateFormat: string;
    /**
     * 时间格式
     * @type {string}
     * @memberof AddSysLangInput
     */
    timeFormat: string;
    /**
     * 
     * @type {WeekEnum}
     * @memberof AddSysLangInput
     */
    weekStart: WeekEnum;
    /**
     * 分组符号
     * @type {string}
     * @memberof AddSysLangInput
     */
    grouping: string;
    /**
     * 小数点符号
     * @type {string}
     * @memberof AddSysLangInput
     */
    decimalPoint: string;
    /**
     * 千分位分隔符
     * @type {string}
     * @memberof AddSysLangInput
     */
    thousandsSep?: string | null;
    /**
     * 是否启用
     * @type {boolean}
     * @memberof AddSysLangInput
     */
    active: boolean;
}
