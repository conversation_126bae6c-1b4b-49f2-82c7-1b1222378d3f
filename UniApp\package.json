{"name": "energy-light-management", "version": "1.0.0", "description": "节能灯管理系统 - uni-app移动端应用", "main": "main.js", "scripts": {"serve": "npm run dev:h5", "build": "npm run build:h5", "dev:h5": "echo 'H5开发服务器启动'", "build:h5": "echo 'H5构建完成'", "dev:app-plus": "uni -p app-plus", "build:app-plus": "uni build -p app-plus", "lint": "eslint --ext .js,.vue --fix .", "check": "npm run lint"}, "dependencies": {"dayjs": "^1.11.7", "echarts": "^5.4.0", "mqtt": "^4.3.7", "sharp": "^0.34.3", "sm-crypto": "^0.3.13", "vue": "^3.3.4", "vuex": "^4.0.2", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@dcloudio/types": "^3.4.8", "@dcloudio/uni-app-plus": "3.0.0-alpha-4030220241101001", "@dcloudio/uni-cli-shared": "3.0.0-alpha-4030220241101001", "@dcloudio/uni-components": "^3.0.0-alpha-3000020210521001", "@dcloudio/uni-h5": "3.0.0-alpha-4030220241101001", "@dcloudio/vite-plugin-uni": "3.0.0-alpha-4030220241101001", "@eslint/js": "^9.34.0", "@vitejs/plugin-vue": "^4.0.0", "@vue/eslint-config-typescript": "^14.6.0", "eslint": "^9.34.0", "eslint-plugin-vue": "^10.4.0", "sass": "^1.77.4", "typescript": "^5.0.2", "vite": "^5.2.8", "vue-tsc": "^1.8.27"}}