/**
 * 节能灯MQTT通信工具
 * 基于网关主题订阅规则标准实现的设备通信功能
 * 支持完整的设备控制指令和事件上报处理
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @date 2025-01-27
 */

/**
 * MQTT配置常量
 * 定义连接参数、QoS等级等基础配置
 */
export const MQTT_CONFIG = {
    // 默认连接配置
    DEFAULT_HOST: "localhost",
    DEFAULT_PORT: 8083,
    DEFAULT_PATH: "/mqtt",
    DEFAULT_PROTOCOL: "ws",

    // 连接选项
    CONNECT_TIMEOUT: 30000,
    KEEPALIVE: 60,
    RECONNECT_PERIOD: 5000,
    MAX_RECONNECT_TIMES: 10,

    // 默认产品密钥和设备名称
    DEFAULT_PRODUCT_KEY: "energylight",
    DEFAULT_DEVICE_NAME: "energylight@@@18C8E716B349",

    // QoS等级定义
    QOS_LEVELS: {
        AT_MOST_ONCE: 0, // 最多一次
        AT_LEAST_ONCE: 1, // 至少一次
        EXACTLY_ONCE: 2 // 恰好一次
    },

    // 频率控制配置
    REPORT_INTERVALS: {
        HEARTBEAT: 30000, // 心跳30秒
        CONSUMPTION: 1800000, // 能耗30分钟
        SETTING: 3600000 // 照明参数1小时
    }
};

/**
 * MQTT主题常量
 * 基于网关主题订阅规则标准定义的主题格式
 */
export const MQTT_TOPICS = {
  // 厂家网关订阅主题（云端→网关→设备）
  GATEWAY_SUBSCRIBE: (productKey, deviceName) => `/${productKey}/${deviceName}/user/get`,
  
  // 厂家网关发布主题（设备→网关→云端）
  GATEWAY_PUBLISH: {
    // 广播消息
    BEACON: (productKey, deviceName) => `/sys/${productKey}/${deviceName}/thing/event/beacon/post`,
    // 照明参数
    SETTING: (productKey, deviceName) => `/sys/${productKey}/${deviceName}/thing/event/setting/post`,
    // 传感器配置
    SENSOR: (productKey, deviceName) => `/sys/${productKey}/${deviceName}/thing/event/sensor/post`,
    // 调光参数
    DIMMING: (productKey, deviceName) => `/sys/${productKey}/${deviceName}/thing/event/dimming/post`,
    // mesh组网参数
    NETWORK: (productKey, deviceName) => `/sys/${productKey}/${deviceName}/thing/event/network/post`,
    // 红外遥控器配置
    IRC: (productKey, deviceName) => `/sys/${productKey}/${deviceName}/thing/event/irc/post`,
    // 能耗信息
    CONSUMPTION: (productKey, deviceName) => `/sys/${productKey}/${deviceName}/thing/event/consumption/post`,
    // 感应消息
    TRIGGER: (productKey, deviceName) => `/sys/${productKey}/${deviceName}/thing/event/trigger/post`,
    // 心跳消息
    HEARTBEAT: (productKey, deviceName) => `/sys/${productKey}/${deviceName}/thing/event/heartbeat/post`,
    // 情景模式照明参数
    SCENE: (productKey, deviceName) => `/sys/${productKey}/${deviceName}/thing/event/scene/post`,
    // 实时亮度、色温
    CURRENT: (productKey, deviceName) => `/sys/${productKey}/${deviceName}/thing/event/current/post`
  },
  
  // 通配符订阅主题
  WILDCARD_TOPICS: {
    // 订阅所有事件类型
    ALL_EVENTS: (productKey, deviceName) => `/sys/${productKey}/${deviceName}/thing/event/+/post`,
    // 订阅特定产品的所有设备事件
    PRODUCT_EVENTS: (productKey) => `/sys/${productKey}/+/thing/event/+/post`
  }
}

/**
 * 控制类型编码常量
 * 定义不同控制方式的编码规范
 */
/**
 * 控制类型编码常量
 * 定义不同控制范围的编码值，用于地址编码和消息路由
 */
export const CONTROL_CODES = {
  SINGLE_DEVICE: 100,  // 单灯控制 - address=number
  GROUP_CONTROL: 200,  // 群组控制 - address=cluster
  LABEL_CONTROL: 300,  // 标签控制 - address=label
  AREA_CONTROL: 400    // 全区控制 - address=area
}

/**
 * 设备指令常量
 * 定义所有支持的设备控制指令
 */
export const DEVICE_ACTIONS = {
  // 设备扫描类指令
  SCAN: 'scan',
  STOP_SCAN: 'stopScan',
  
  // 设备状态获取指令
  GET_SETTING: 'getSetting',
  GET_CURRENT: 'getCurrent',
  GET_SENSOR: 'getSensor',
  GET_DIMMING: 'getDimming',
  GET_NETWORK: 'getNetwork',
  GET_IRC: 'getIrc',
  GET_CONSUMPTION: 'getConsumption',
  
  // 设备控制指令
  BLINK: 'blink',
  STOP_BLINK: 'stopBlink',
  LIGHT_ON: 'lightOn',
  LIGHT_OFF: 'lightOff',
  LIGHT_SLEEP: 'lightSleep',
  SSR_CONTROL: 'ssrControl',
  
  // 传感器控制指令
  SSR_ON: 'ssrOn',
  SSR_OFF: 'ssrOff',
  
  // 网络控制指令
  NET_ON: 'netOn',
  NET_OFF: 'netOff',
  RELAY_ON: 'relayOn',
  RELAY_OFF: 'relayOff',
  
  // 红外遥控器指令
  IRC_ON: 'ircOn',
  IRC_OFF: 'ircOff',
  
  // 标签和邻组指令
  GET_LABEL: 'getLabel',
  GET_NEIGHBOUR: 'getNeighbour',
  LABEL_ENABLE: 'labelEnable',
  LABEL_DISABLE: 'labelDisable',
  NEIGHBOUR_ENABLE: 'neighbourEnable',
  NEIGHBOUR_DISABLE: 'neighbourDisable',
  
  // 自动上报指令
  REPORT_SETTING: 'reportSetting',
  REPORT_SETTING_ACK: 'reportSettingAck',
  REPORT_SETTING_STOP: 'reportSettingStop',
  REPORT_CONSUMPTION: 'reportConsumption',
  REPORT_CONSUMPTION_ACK: 'reportConsumptionAck',
  REPORT_CONSUMPTION_STOP: 'reportConsumptionStop',
  REPORT_LABEL: 'reportLabel',
  REPORT_LABEL_STOP: 'reportLabelStop',
  REPORT_NEIGHBOUR: 'reportNeighbour',
  REPORT_NEIGHBOUR_STOP: 'reportNeighbourStop',
  
  // 带参数指令
  SET_LIGHT_MODE: 'setLightMode',
  SET_DELAY_MODE: 'setDelayMode',
  SET_ALS_MODE: 'setAlsMode',
  CALL_SCENE: 'callScene',
  READ_SCENE: 'readScene',
  SET_HIGH_BRIGHT: 'setHighBright',
  SET_STANDBY_BRIGHT: 'setStandbyBright',
  SET_CCT_BRIGHT: 'setCctBright',
  SET_BRIGHT_RISE_TIME: 'setBrightRiseTime',
  SET_BRIGHT_FALL_TIME: 'setBrightFallTime',
  SET_CCT_RISE_TIME: 'setCctRiseTime',
  SET_CCT_FALL_TIME: 'setCctFallTime',
  SET_DELAY_TIME: 'setDelayTime',
  SET_DELAY_TIME2: 'setDelayTime2',
  SET_SENSOR_INTERVAL: 'setSensorInterval',
  SET_NET_TTL: 'setNetTtl',
  SET_GROUP_TTL: 'setGroupTtl',
  SET_TX_TIMES: 'setTxTimes',
  SET_NUMBER_ADDRESS: 'setNumberAddress',
  SET_CLUSTER_ADDRESS: 'setClusterAddress',
  SET_AREA_ADDRESS: 'setAreaAddress',
  SET_LABEL: 'setLabel',
  SET_NEIGHBOUR: 'setNeighbour',
  SET_SCENE: 'setScene',
  SAVE_TO_SCENE: 'savetoScene',
  DELETE_LABEL: 'deleteLabel',
  CLEAR_LABEL: 'clearLabel',
  DELETE_NEIGHBOUR: 'deleteNeighbour',
  CLEAR_NEIGHBOUR: 'clearNeighbour'
}

/**
 * 参数对照表常量
 * 定义中英文参数映射关系
 */
/**
 * 参数映射常量
 * 定义中文参数值与英文标准值的映射关系
 */
export const PARAM_MAPPING = {
  // 亮灯模式
  LIGHT_MODES: {
    '常亮': 'Always_Brt',
    '常灭': 'Always_Off',
    '感应': 'Sensor_Control'
  },
  // 感应延时模式
  DELAY_MODES: {
    '一段': 'PhaseI',
    '二段': 'PhaseII'
  },
  // 恒照度控制模式
  ALS_MODES: {
    '无效': 'None',
    '自控': 'Inner',
    '被控': 'Ext'
  }
}

/**
 * MQTT客户端类
 * 实现完整的MQTT通信功能，支持设备控制和事件处理
 * 
 * 主要功能：
 * - MQTT连接管理（连接、断开、重连）
 * - 消息订阅和发布
 * - 设备控制指令处理
 * - 事件上报数据解析
 * - 连接健康监控
 * - 设备缓存管理
 * 
 * @class MQTTClient
 * @property {Object} client - MQTT客户端实例
 * @property {boolean} connected - 连接状态
 * @property {Map} subscriptions - 订阅信息映射
 * @property {Map} eventHandlers - 事件处理器映射
 * @property {Map} deviceCache - 设备缓存
 * @property {number} reconnectCount - 重连计数
 * @property {Object} reconnectTimer - 重连定时器
 * @property {Object} connectionMonitor - 连接监控定时器
 * @property {number} connectTime - 连接时间戳
 * @property {number} lastMessageTime - 最后消息时间戳
 */
export class MQTTClient {
  /**
   * 构造函数
   * @param {Object} options - 配置选项
   * @param {string} options.productKey - 产品密钥
   * @param {string} options.deviceName - 设备名称
   * @param {string} options.host - MQTT服务器地址
   * @param {number} options.port - MQTT服务器端口
   * @param {string} options.username - 用户名
   * @param {string} options.password - 密码
   */
  constructor(options = {}) {
    this.options = {
      host: options.host || MQTT_CONFIG.DEFAULT_HOST,
      port: options.port || MQTT_CONFIG.DEFAULT_PORT,
      path: options.path || MQTT_CONFIG.DEFAULT_PATH,
      protocol: options.protocol || MQTT_CONFIG.DEFAULT_PROTOCOL,
      clientId: options.clientId || this.generateClientId(),
      username: options.username || '',
      password: options.password || '',
      keepalive: options.keepalive || MQTT_CONFIG.KEEPALIVE,
      connectTimeout: options.connectTimeout || MQTT_CONFIG.CONNECT_TIMEOUT,
      reconnectPeriod: options.reconnectPeriod || MQTT_CONFIG.RECONNECT_PERIOD,
      clean: options.clean !== false,
      ...options
    }
    
    this.client = null
    this.connected = false
    this.reconnectCount = 0
    this.maxReconnectTimes = options.maxReconnectTimes || MQTT_CONFIG.MAX_RECONNECT_TIMES
    this.subscriptions = new Map()
    this.messageHandlers = new Map()
    this.eventHandlers = new Map()
    this.reportTimers = new Map() // 自动上报定时器
    this.messageQueue = [] // 消息队列
    this.deviceCache = new Map() // 设备状态缓存
    
    // 连接管理相关属性
    this.reconnectTimer = null // 重连定时器
    this.connectionMonitor = null // 连接监控定时器
    this.connectTime = null // 连接时间
    this.lastMessageTime = null // 最后消息时间
    
    // 产品和设备信息
    this.productKey = options.productKey || MQTT_CONFIG.DEFAULT_PRODUCT_KEY
    this.deviceName = options.deviceName || MQTT_CONFIG.DEFAULT_DEVICE_NAME
    
    // 绑定方法
    this.onConnect = this.onConnect.bind(this)
    this.onMessage = this.onMessage.bind(this)
    this.onError = this.onError.bind(this)
    this.onClose = this.onClose.bind(this)
    this.onReconnect = this.onReconnect.bind(this)
    
    // 初始化消息处理器
    this.initMessageHandlers()
  }

  /**
   * 生成客户端ID
   * @returns {string} 客户端ID
   */
  generateClientId() {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 8)
    return `energy_light_${timestamp}_${random}`
  }

  /**
   * 初始化消息处理器
   * 设置各种事件类型的处理函数
   */
  initMessageHandlers() {
    // 设备事件处理器
    this.messageHandlers.set('beacon', this.handleBeaconEvent.bind(this))
    this.messageHandlers.set('setting', this.handleSettingEvent.bind(this))
    this.messageHandlers.set('sensor', this.handleSensorEvent.bind(this))
    this.messageHandlers.set('dimming', this.handleDimmingEvent.bind(this))
    this.messageHandlers.set('network', this.handleNetworkEvent.bind(this))
    this.messageHandlers.set('irc', this.handleIrcEvent.bind(this))
    this.messageHandlers.set('consumption', this.handleConsumptionEvent.bind(this))
    this.messageHandlers.set('trigger', this.handleTriggerEvent.bind(this))
    this.messageHandlers.set('heartbeat', this.handleHeartbeatEvent.bind(this))
    this.messageHandlers.set('scene', this.handleSceneEvent.bind(this))
    this.messageHandlers.set('current', this.handleCurrentEvent.bind(this))
  }

  /**
   * 连接MQTT服务器
   * @returns {Promise<void>}
   */
  async connect() {
    try {
      if (this.connected) {
        console.log('MQTT已连接')
        return
      }

      console.log('正在连接MQTT服务器...')
      
      // 检查是否在uni-app环境中
      if (typeof uni !== 'undefined' && uni.connectSocket) {
        await this.connectWithUniApp()
      } else {
        // 使用mqtt.js库连接
        await this.connectWithMqttJs()
      }
      
      // 连接成功后订阅网关主题
      this.subscribeGatewayTopics()
      
    } catch (error) {
      console.error('MQTT连接失败:', error)
      throw error
    }
  }

  /**
   * 使用uni-app WebSocket连接
   * @returns {Promise<void>}
   */
  async connectWithUniApp() {
    return new Promise((resolve, reject) => {
      const url = `${this.options.protocol}://${this.options.host}:${this.options.port}${this.options.path}`
      
      this.client = uni.connectSocket({
        url,
        protocols: ['mqtt'],
        success: () => {
          console.log('MQTT WebSocket连接成功')
        },
        fail: (error) => {
          console.error('MQTT WebSocket连接失败:', error)
          reject(error)
        }
      })

      this.client.onOpen(() => {
        console.log('MQTT连接已建立')
        this.connected = true
        this.connectTime = Date.now()
        this.reconnectCount = 0
        
        // 启动连接监控
        this.startConnectionMonitor()
        
        // 订阅网关主题
        this.subscribeGatewayTopics()
        
        // 发送上线心跳
        this.sendHeartbeat('online')
        
        // 启动定时心跳
        this.startHeartbeat()
        
        this.emit('connect')
        resolve()
      })

      this.client.onMessage((res) => {
        this.handleMessage(res.data)
      })

      this.client.onClose(() => {
        console.log('MQTT连接已关闭')
        this.connected = false
        this.emit('disconnect')
        this.handleReconnect()
      })

      this.client.onError((error) => {
        console.error('MQTT连接错误:', error)
        this.emit('error', error)
        reject(error)
      })
    })
  }

  /**
   * 使用mqtt.js库连接
   * @returns {Promise<void>}
   */
  async connectWithMqttJs() {
    return new Promise((resolve, reject) => {
      try {
        // 检查mqtt.js是否可用
        if (typeof mqtt === 'undefined') {
          throw new Error('mqtt.js库未找到，请确保已正确引入')
        }

        const url = `${this.options.protocol}://${this.options.host}:${this.options.port}${this.options.path}`
        
        this.client = mqtt.connect(url, {
          clientId: this.options.clientId,
          username: this.options.username,
          password: this.options.password,
          keepalive: this.options.keepalive,
          connectTimeout: this.options.connectTimeout,
          reconnectPeriod: this.options.reconnectPeriod,
          clean: this.options.clean
        })

        this.client.on('connect', () => {
          console.log('MQTT连接已建立')
          this.connected = true
          this.connectTime = Date.now()
          this.reconnectCount = 0
          
          // 启动连接监控
          this.startConnectionMonitor()
          
          // 订阅网关主题
          this.subscribeGatewayTopics()
          
          // 发送上线心跳
          this.sendHeartbeat('online')
          
          // 启动定时心跳
          this.startHeartbeat()
          
          this.emit('connect')
          resolve()
        })

        this.client.on('message', (topic, message) => {
          this.handleMessage(message.toString(), topic)
        })

        this.client.on('close', () => {
          console.log('MQTT连接已关闭')
          this.connected = false
          this.emit('disconnect')
        })

        this.client.on('error', (error) => {
          console.error('MQTT连接错误:', error)
          this.emit('error', error)
          reject(error)
        })

        this.client.on('reconnect', () => {
          console.log('MQTT正在重连...')
          this.reconnectCount++
          this.emit('reconnect')
        })

      } catch (error) {
        console.error('MQTT连接初始化失败:', error)
        reject(error)
      }
    })
  }

  /**
   * 订阅网关主题
   * 根据标准规范订阅相关主题
   */
  subscribeGatewayTopics() {
    try {
      // 订阅网关控制主题
      const controlTopic = MQTT_TOPICS.GATEWAY_SUBSCRIBE(this.productKey, this.deviceName)
      this.subscribe(controlTopic, this.handleControlMessage.bind(this))
      
      // 订阅所有设备事件（用于监听设备上报）
      const eventTopic = MQTT_TOPICS.WILDCARD_TOPICS.ALL_EVENTS(this.productKey, this.deviceName)
      this.subscribe(eventTopic, this.handleDeviceEvent.bind(this))
      
      console.log('网关主题订阅完成')
    } catch (error) {
      console.error('网关主题订阅失败:', error)
    }
  }

  /**
   * 处理控制消息
   * @param {string} topic 主题
   * @param {string} payload 消息内容
   */
  handleControlMessage(topic, payload) {
    try {
      const message = typeof payload === 'string' ? JSON.parse(payload) : payload
      console.log('收到控制消息:', message)
      
      // 验证消息格式
      const validation = MessageValidator.validateControlMessage(message)
      if (!validation.valid) {
        ErrorHandler.handleValidationError(validation.errors, '控制消息验证')
        return
      }
      
      // 验证设备指令
      if (!MessageValidator.validateDeviceAction(message.method)) {
        ErrorHandler.handleValidationError([`未知的设备指令: ${message.method}`], '指令验证')
        return
      }
      
      // 验证地址编码
      if (message.params.code && message.params.address !== undefined) {
        const addressValidation = MessageValidator.validateAddressEncoding(message.params.code, message.params.address)
        if (!addressValidation.valid) {
          ErrorHandler.handleValidationError(addressValidation.errors, '地址编码验证')
          return
        }
      }
      
      // 处理控制指令
      if (message.method && message.params) {
        this.processControlCommand(message.method, message.params, message.id)
      }
      
      this.emit('controlMessage', message)
    } catch (error) {
      ErrorHandler.handleMessageError(error, topic, payload)
    }
  }

  /**
   * 处理设备事件
   * @param {string} topic 主题
   * @param {string} payload 消息内容
   */
  handleDeviceEvent(topic, payload) {
    try {
      const message = typeof payload === 'string' ? JSON.parse(payload) : payload
      
      // 从主题中提取事件类型
      const topicParts = topic.split('/')
      const eventType = topicParts[topicParts.length - 2] // 倒数第二个部分是事件类型
      
      console.log(`收到设备事件 [${eventType}]:`, message)
      
      // 验证事件消息格式
      const validation = MessageValidator.validateEventMessage(message)
      if (!validation.valid) {
        ErrorHandler.handleValidationError(validation.errors, `${eventType}事件验证`)
        return
      }
      
      // 更新设备缓存
      this.updateDeviceCache(eventType, message)
      
      // 调用对应的事件处理器
      if (this.messageHandlers.has(eventType)) {
        this.messageHandlers.get(eventType)(message)
      }
      
      this.emit('deviceEvent', eventType, message)
    } catch (error) {
      ErrorHandler.handleMessageError(error, topic, payload)
    }
  }

  /**
   * 处理控制指令
   * @param {string} method 方法名
   * @param {object} params 参数
   * @param {string} id 消息ID
   */
  processControlCommand(method, params, id) {
    try {
      console.log(`执行控制指令: ${method}`, params)
      
      // 根据不同的控制指令执行相应操作
      switch (method) {
        case DEVICE_ACTIONS.SCAN:
          this.handleScanCommand(params)
          break
        case DEVICE_ACTIONS.GET_SETTING:
          this.handleGetSettingCommand(params)
          break
        case DEVICE_ACTIONS.LIGHT_ON:
          this.handleLightOnCommand(params)
          break
        case DEVICE_ACTIONS.LIGHT_OFF:
          this.handleLightOffCommand(params)
          break
        case DEVICE_ACTIONS.SET_LIGHT_MODE:
          this.handleSetLightModeCommand(params)
          break
        default:
          console.warn('未知的控制指令:', method)
      }
      
      // 发送响应消息
      if (id) {
        this.sendCommandResponse(method, params, id, 'success')
      }
      
    } catch (error) {
      console.error('执行控制指令失败:', error)
      if (id) {
        this.sendCommandResponse(method, params, id, 'error', error.message)
      }
    }
  }

  /**
   * 发送指令响应
   * @param {string} method 方法名
   * @param {object} params 参数
   * @param {string} id 消息ID
   * @param {string} status 状态
   * @param {string} message 消息
   */
  sendCommandResponse(method, params, id, status, message = '') {
    const responseTopic = MQTT_TOPICS.GATEWAY_PUBLISH.BEACON(this.productKey, this.deviceName)
    const response = {
      id,
      version: '1.0',
      params: {
        method,
        status,
        message,
        timestamp: Date.now()
      }
    }
    
    this.publish(responseTopic, response)
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.client) {
      // 停止连接监控
      this.stopConnectionMonitor()
      
      // 清理所有定时器
      this.stopHeartbeat()
      this.stopReconnect()
      
      // 清理报告定时器
      if (this.reportTimer) {
        clearInterval(this.reportTimer)
        this.reportTimer = null
      }
      
      // 发送离线心跳
      try {
        this.sendHeartbeat('offline')
      } catch (error) {
        console.warn('发送离线心跳失败:', error.message)
      }
      
      // 关闭连接
      this.client.close()
      this.client = null
      this.connected = false
      this.subscriptions.clear()
      
      // 清理时间记录
      this.connectTime = null
      this.lastMessageTime = null
      
      this.emit('disconnect')
    }
  }

  /**
   * 订阅主题
   * @param {string} topic 主题
   * @param {function} callback 回调函数
   * @param {object} options 订阅选项
   */
  subscribe(topic, callback, options = {}) {
    if (!this.connected) {
      console.warn('MQTT未连接，无法订阅主题:', topic)
      return false
    }

    const qos = options.qos || MQTT_CONFIG.QOS_LEVELS.AT_MOST_ONCE
    
    // 保存订阅信息
    this.subscriptions.set(topic, { callback, options })
    this.messageHandlers.set(topic, callback)

    // 发送订阅消息
    const subscribeMessage = {
      type: 'subscribe',
      topic,
      qos
    }

    this.sendMessage(subscribeMessage)
    console.log('订阅主题:', topic)
    return true
  }

  /**
   * 取消订阅
   * @param {string} topic 主题
   */
  unsubscribe(topic) {
    if (!this.connected) {
      console.warn('MQTT未连接，无法取消订阅:', topic)
      return false
    }

    this.subscriptions.delete(topic)
    this.messageHandlers.delete(topic)

    const unsubscribeMessage = {
      type: 'unsubscribe',
      topic
    }

    this.sendMessage(unsubscribeMessage)
    console.log('取消订阅主题:', topic)
    return true
  }

  /**
   * 发布消息
   * @param {string} topic 主题
   * @param {any} payload 消息内容
   * @param {object} options 发布选项
   */
  publish(topic, payload, options = {}) {
    if (!this.connected) {
      console.warn('MQTT未连接，无法发布消息')
      return false
    }

    const message = {
      type: 'publish',
      topic,
      payload: typeof payload === 'string' ? payload : JSON.stringify(payload),
      qos: options.qos || MQTT_CONFIG.QOS_LEVELS.AT_MOST_ONCE,
      retain: options.retain || false
    }

    this.sendMessage(message)
    console.log('发布消息到主题:', topic, payload)
    return true
  }

  /**
   * 发送消息
   * @param {object} message 消息对象
   */
  sendMessage(message) {
    if (this.client && this.connected) {
      const data = JSON.stringify(message)
      this.client.send({ data })
    }
  }

  /**
   * 处理接收到的消息
   * @param {string} data 消息数据
   * @param {string} topic 消息主题
   */
  handleMessage(data, topic) {
    // 更新最后消息时间
    this.lastMessageTime = Date.now()
    
    try {
      let payload
      if (typeof data === 'string') {
        try {
          payload = JSON.parse(data)
        } catch {
          payload = data
        }
      } else {
        payload = data
      }
      
      console.log('收到MQTT消息:', { topic, payload })
      
      // 查找匹配的订阅处理器
      for (const [subscribedTopic, subscription] of this.subscriptions) {
        if (this.topicMatches(subscribedTopic, topic)) {
          if (subscription.callback) {
            subscription.callback(topic, payload)
          }
        }
      }
      
      this.emit('message', topic, payload)
    } catch (error) {
      console.error('处理MQTT消息失败:', error)
    }
  }

  /**
   * 处理广播事件
   * @param {object} message 消息内容
   */
  handleBeaconEvent(message) {
    console.log('处理广播事件:', message)
    this.emit('beacon', message)
  }

  /**
   * 处理照明参数事件
   * @param {object} message 消息内容
   */
  handleSettingEvent(message) {
    console.log('处理照明参数事件:', message)
    this.emit('setting', message)
  }

  /**
   * 处理传感器配置事件
   * @param {object} message 消息内容
   */
  handleSensorEvent(message) {
    console.log('处理传感器配置事件:', message)
    this.emit('sensor', message)
  }

  /**
   * 处理调光参数事件
   * @param {object} message 消息内容
   */
  handleDimmingEvent(message) {
    console.log('处理调光参数事件:', message)
    this.emit('dimming', message)
  }

  /**
   * 处理mesh组网参数事件
   * @param {object} message 消息内容
   */
  handleNetworkEvent(message) {
    console.log('处理mesh组网参数事件:', message)
    this.emit('network', message)
  }

  /**
   * 处理红外遥控器配置事件
   * @param {object} message 消息内容
   */
  handleIrcEvent(message) {
    console.log('处理红外遥控器配置事件:', message)
    this.emit('irc', message)
  }

  /**
   * 处理能耗信息事件
   * @param {object} message 消息内容
   */
  handleConsumptionEvent(message) {
    console.log('处理能耗信息事件:', message)
    this.emit('consumption', message)
  }

  /**
   * 处理感应消息事件
   * @param {object} message 消息内容
   */
  handleTriggerEvent(message) {
    console.log('处理感应消息事件:', message)
    this.emit('trigger', message)
  }

  /**
   * 处理心跳消息事件
   * @param {object} message 消息内容
   */
  handleHeartbeatEvent(message) {
    console.log('处理心跳消息事件:', message)
    this.emit('heartbeat', message)
  }

  /**
   * 处理情景模式照明参数事件
   * @param {object} message 消息内容
   */
  handleSceneEvent(message) {
    console.log('处理情景模式照明参数事件:', message)
    this.emit('scene', message)
  }

  /**
   * 处理实时亮度、色温事件
   * @param {object} message 消息内容
   */
  handleCurrentEvent(message) {
    console.log('处理实时亮度、色温事件:', message)
    this.emit('current', message)
  }

  /**
   * 检查主题是否匹配
   * @param {string} pattern 订阅模式
   * @param {string} topic 实际主题
   * @returns {boolean} 是否匹配
   */
  topicMatches(pattern, topic) {
    // 简单的通配符匹配实现
    const patternParts = pattern.split('/')
    const topicParts = topic.split('/')
    
    if (patternParts.length !== topicParts.length) {
      return false
    }
    
    for (let i = 0; i < patternParts.length; i++) {
      const patternPart = patternParts[i]
      const topicPart = topicParts[i]
      
      if (patternPart !== '+' && patternPart !== topicPart) {
        return false
      }
    }
    
    return true
  }

  /**
   * 处理重连
   */
  handleReconnect() {
    if (this.reconnectCount < this.maxReconnectTimes) {
      this.reconnectCount++
      
      // 计算智能重连延迟（指数退避算法）
      const baseDelay = this.options.reconnectPeriod
      const backoffDelay = Math.min(baseDelay * Math.pow(2, this.reconnectCount - 1), 30000)
      
      console.log(`尝试重连MQTT (${this.reconnectCount}/${this.maxReconnectTimes})，延迟${backoffDelay}ms`)
      
      this.reconnectTimer = setTimeout(async () => {
        try {
          await this.connect()
          
          // 重连成功，重置重连计数
          this.reconnectCount = 0
          
          // 重新订阅之前的主题
          await this.resubscribeTopics()
          
          // 发送重连成功事件
          this.emit('reconnectSuccess', this.reconnectCount)
          
          console.log('MQTT重连成功')
        } catch (error) {
          console.error('MQTT重连失败:', error)
          ErrorHandler.handleConnectionError(error, '重连失败')
          
          // 继续尝试重连
          this.handleReconnect()
        }
      }, backoffDelay)
    } else {
      console.error('MQTT重连次数已达上限，停止重连')
      this.emit('reconnectFailed', this.reconnectCount)
      ErrorHandler.handleConnectionError(
        new Error('重连次数超限'),
        `重连失败，已尝试${this.reconnectCount}次`
      )
    }
  }
  
  /**
   * 重新订阅所有主题
   */
  async resubscribeTopics() {
    const subscribePromises = []
    
    for (const [topic, { callback, options }] of this.subscriptions) {
      subscribePromises.push(
        this.subscribe(topic, callback, options).catch(error => {
          console.error(`重新订阅主题失败 [${topic}]:`, error)
          return null
        })
      )
    }
    
    const results = await Promise.allSettled(subscribePromises)
    const failedCount = results.filter(r => r.status === 'rejected').length
    
    if (failedCount > 0) {
      console.warn(`${failedCount}个主题重新订阅失败`)
    }
    
    return results
  }
  
  /**
   * 停止重连
   */
  stopReconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    this.reconnectCount = 0
  }
  
  /**
   * 检查连接健康状态
   */
  checkConnectionHealth() {
    if (!this.connected) {
      return { healthy: false, reason: '未连接' }
    }
    
    const now = Date.now()
    const lastActivity = this.lastMessageTime || this.connectTime
    const inactiveTime = now - lastActivity
    
    // 如果超过2分钟没有消息活动，认为连接可能有问题
    if (inactiveTime > 120000) {
      return { 
        healthy: false, 
        reason: '连接无活动', 
        inactiveTime 
      }
    }
    
    return { healthy: true, inactiveTime }
  }
  
  /**
   * 启动连接监控
   */
  startConnectionMonitor() {
    if (this.connectionMonitor) {
      clearInterval(this.connectionMonitor)
    }
    
    this.connectionMonitor = setInterval(() => {
      const health = this.checkConnectionHealth()
      
      if (!health.healthy) {
        console.warn('连接健康检查失败:', health.reason)
        this.emit('connectionUnhealthy', health)
        
        // 如果连接不健康，尝试重连
        if (this.connected) {
          this.disconnect()
        }
        this.handleReconnect()
      }
    }, 60000) // 每分钟检查一次
  }
  
  /**
   * 停止连接监控
   */
  stopConnectionMonitor() {
    if (this.connectionMonitor) {
      clearInterval(this.connectionMonitor)
      this.connectionMonitor = null
    }
  }

  /**
   * 添加事件监听器
   * @param {string} event 事件名
   * @param {function} handler 事件处理器
   */
  on(event, handler) {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, [])
    }
    this.eventHandlers.get(event).push(handler)
  }

  /**
   * 移除事件监听器
   * @param {string} event 事件名
   * @param {function} handler 事件处理器
   */
  off(event, handler) {
    if (this.eventHandlers.has(event)) {
      const handlers = this.eventHandlers.get(event)
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   * @param {string} event 事件名
   * @param {...any} args 事件参数
   */
  emit(event, ...args) {
    if (this.eventHandlers.has(event)) {
      this.eventHandlers.get(event).forEach(handler => {
        try {
          handler(...args)
        } catch (error) {
          console.error('事件处理器执行失败:', error)
        }
      })
    }
  }

  /**
   * 获取连接状态
   * @returns {boolean} 是否已连接
   */
  isConnected() {
    return this.connected
  }

  /**
   * 获取客户端ID
   * @returns {string} 客户端ID
   */
  getClientId() {
    return this.options.clientId
  }

  /**
   * 发送心跳消息
   * @param {string} status 状态
   */
  sendHeartbeat(status = 'online') {
    const topic = MQTT_TOPICS.GATEWAY_PUBLISH.HEARTBEAT(this.productKey, this.deviceName)
    const payload = {
      id: this.generateMessageId(),
      version: '1.0',
      params: {
        status,
        timestamp: Date.now(),
        clientId: this.options.clientId
      }
    }
    
    this.publish(topic, payload)
  }

  /**
   * 开始定时心跳
   */
  startHeartbeat() {
    // 清除之前的心跳定时器
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
    }
    
    // 设置新的心跳定时器
    this.heartbeatTimer = setInterval(() => {
      if (this.connected) {
        this.sendHeartbeat()
      }
    }, MQTT_CONFIG.REPORT_INTERVALS.HEARTBEAT)
  }

  /**
   * 停止心跳
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 生成消息ID
   * @returns {string} 消息ID
   */
  generateMessageId() {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`
  }

  /**
   * 处理扫描指令
   * @param {object} params 参数
   */
  handleScanCommand(params) {
    console.log('执行设备扫描:', params)
    // 这里可以实现具体的设备扫描逻辑
    this.emit('scanCommand', params)
  }

  /**
   * 处理获取设置指令
   * @param {object} params 参数
   */
  handleGetSettingCommand(params) {
    console.log('获取设备设置:', params)
    // 这里可以实现具体的获取设置逻辑
    this.emit('getSettingCommand', params)
  }

  /**
   * 处理开灯指令
   * @param {object} params 参数
   */
  handleLightOnCommand(params) {
    console.log('执行开灯指令:', params)
    // 这里可以实现具体的开灯逻辑
    this.emit('lightOnCommand', params)
  }

  /**
   * 处理关灯指令
   * @param {object} params 参数
   */
  handleLightOffCommand(params) {
    console.log('执行关灯指令:', params)
    // 这里可以实现具体的关灯逻辑
    this.emit('lightOffCommand', params)
  }

  /**
   * 处理设置灯光模式指令
   * @param {object} params 参数
   */
  handleSetLightModeCommand(params) {
    console.log('设置灯光模式:', params)
    // 这里可以实现具体的设置灯光模式逻辑
    this.emit('setLightModeCommand', params)
  }

  /**
   * 更新设备缓存
   * @param {string} eventType 事件类型
   * @param {object} message 消息内容
   */
  updateDeviceCache(eventType, message) {
    try {
      if (!message.params) return
      
      const deviceKey = message.params.deviceName || 'unknown'
      
      if (!this.deviceCache.has(deviceKey)) {
        this.deviceCache.set(deviceKey, {})
      }
      
      const deviceData = this.deviceCache.get(deviceKey)
      deviceData[eventType] = {
        ...message.params,
        lastUpdate: Date.now()
      }
      
      // 触发缓存更新事件
      this.emit('deviceCacheUpdate', deviceKey, eventType, deviceData)
    } catch (error) {
      console.error('更新设备缓存失败:', error)
    }
  }
  
  /**
   * 获取设备缓存数据
   * @param {string} deviceName 设备名称
   * @param {string} eventType 事件类型（可选）
   * @returns {object} 缓存数据
   */
  getDeviceCache(deviceName, eventType = null) {
    const deviceData = this.deviceCache.get(deviceName)
    if (!deviceData) return null
    
    if (eventType) {
      return deviceData[eventType] || null
    }
    
    return deviceData
  }
  
  /**
   * 清除设备缓存
   * @param {string} deviceName 设备名称（可选，不传则清除所有）
   */
  clearDeviceCache(deviceName = null) {
    if (deviceName) {
      this.deviceCache.delete(deviceName)
    } else {
      this.deviceCache.clear()
    }
  }
  
  /**
   * 发布设备事件
   * @param {string} eventType 事件类型
   * @param {object} data 事件数据
   */
  publishDeviceEvent(eventType, data) {
    try {
      // 验证事件数据
      const validation = MessageValidator.validateEventMessage({
        id: 'temp',
        version: '1.0',
        params: { ...data, timestamp: Date.now() }
      })
      
      if (!validation.valid) {
        ErrorHandler.handleValidationError(validation.errors, `${eventType}事件发布验证`)
        return false
      }
      
      const topicFunction = MQTT_TOPICS.GATEWAY_PUBLISH[eventType.toUpperCase()]
      if (topicFunction) {
        const topic = topicFunction(this.productKey, this.deviceName)
        const payload = {
          id: this.generateMessageId(),
          version: '1.0',
          params: {
            ...data,
            timestamp: Date.now()
          }
        }
        
        this.publish(topic, payload)
        console.log(`发布${eventType}事件:`, payload)
        return true
      } else {
        console.warn('未知的事件类型:', eventType)
        return false
      }
    } catch (error) {
      ErrorHandler.handleMessageError(error, 'publishDeviceEvent', { eventType, data })
      return false
    }
  }
}

/**
 * 消息验证工具
 * 提供消息格式验证和数据校验功能
 */
export const MessageValidator = {
  /**
   * 验证控制消息格式
   * @param {object} message 消息对象
   * @returns {object} 验证结果
   */
  validateControlMessage(message) {
    const errors = []
    
    if (!message) {
      errors.push('消息不能为空')
      return { valid: false, errors }
    }
    
    // 验证必需字段
    if (!message.id) errors.push('缺少消息ID')
    if (!message.version) errors.push('缺少版本号')
    if (!message.method) errors.push('缺少方法名')
    if (!message.params) errors.push('缺少参数')
    
    // 验证参数格式
    if (message.params) {
      if (typeof message.params.code !== 'number') {
        errors.push('控制类型编码必须为数字')
      }
      if (!message.params.deviceName) {
        errors.push('缺少设备名称')
      }
      if (!message.params.area) {
        errors.push('缺少区域信息')
      }
      if (message.params.address === undefined) {
        errors.push('缺少地址信息')
      }
      if (!message.params.action) {
        errors.push('缺少动作指令')
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  },
  
  /**
   * 验证事件消息格式
   * @param {object} message 消息对象
   * @returns {object} 验证结果
   */
  validateEventMessage(message) {
    const errors = []
    
    if (!message) {
      errors.push('消息不能为空')
      return { valid: false, errors }
    }
    
    // 验证必需字段
    if (!message.id) errors.push('缺少消息ID')
    if (!message.version) errors.push('缺少版本号')
    if (!message.params) errors.push('缺少参数')
    
    // 验证时间戳
    if (message.params && !message.params.timestamp) {
      errors.push('缺少时间戳')
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  },
  
  /**
   * 验证地址编码
   * @param {number} code 控制类型编码
   * @param {*} address 地址值
   * @returns {object} 验证结果
   */
  validateAddressEncoding(code, address) {
    const errors = []
    
    switch (code) {
      case CONTROL_CODES.SINGLE_DEVICE:
        if (typeof address !== 'number' || address < 1 || address > 65535) {
          errors.push('单灯控制地址必须为1-65535的数字')
        }
        break
      case CONTROL_CODES.GROUP_CONTROL:
        if (typeof address !== 'number' || address < 1 || address > 255) {
          errors.push('群组控制地址必须为1-255的数字')
        }
        break
      case CONTROL_CODES.LABEL_CONTROL:
        if (typeof address !== 'string' || address.length === 0) {
          errors.push('标签控制地址必须为非空字符串')
        }
        break
      case CONTROL_CODES.AREA_CONTROL:
        if (typeof address !== 'string' || address.length === 0) {
          errors.push('全区控制地址必须为非空字符串')
        }
        break
      default:
        errors.push('未知的控制类型编码')
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  },
  
  /**
   * 验证设备指令
   * @param {string} action 指令名称
   * @returns {boolean} 是否为有效指令
   */
  validateDeviceAction(action) {
    return Object.values(DEVICE_ACTIONS).includes(action)
  },
  
  /**
   * 验证参数值范围
   * @param {string} paramName 参数名称
   * @param {*} value 参数值
   * @returns {object} 验证结果
   */
  validateParameterRange(paramName, value) {
    const errors = []
    
    switch (paramName) {
      case 'brightness':
      case 'highBright':
      case 'standbyBright':
        if (typeof value !== 'number' || value < 0 || value > 100) {
          errors.push(`${paramName}必须为0-100的数字`)
        }
        break
      case 'cct':
      case 'cctBright':
        if (typeof value !== 'number' || value < 2700 || value > 6500) {
          errors.push(`${paramName}必须为2700-6500的数字`)
        }
        break
      case 'delayTime':
      case 'delayTime2':
        if (typeof value !== 'number' || value < 0 || value > 65535) {
          errors.push(`${paramName}必须为0-65535的数字`)
        }
        break
      case 'sensorInterval':
        if (typeof value !== 'number' || value < 1 || value > 255) {
          errors.push(`${paramName}必须为1-255的数字`)
        }
        break
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
}

/**
 * 错误处理工具
 * 提供统一的错误处理和日志记录功能
 */
export const ErrorHandler = {
  /**
   * 处理MQTT连接错误
   * @param {Error} error 错误对象
   * @param {string} context 错误上下文
   */
  handleConnectionError(error, context = 'MQTT连接') {
    const errorInfo = {
      type: 'CONNECTION_ERROR',
      context,
      message: error.message,
      timestamp: Date.now(),
      stack: error.stack
    }
    
    console.error(`${context}错误:`, errorInfo)
    
    // 可以在这里添加错误上报逻辑
    this.reportError(errorInfo)
  },
  
  /**
   * 处理消息处理错误
   * @param {Error} error 错误对象
   * @param {string} topic 主题
   * @param {*} payload 消息内容
   */
  handleMessageError(error, topic, payload) {
    const errorInfo = {
      type: 'MESSAGE_ERROR',
      context: '消息处理',
      message: error.message,
      topic,
      payload,
      timestamp: Date.now(),
      stack: error.stack
    }
    
    console.error('消息处理错误:', errorInfo)
    this.reportError(errorInfo)
  },
  
  /**
   * 处理验证错误
   * @param {Array} errors 错误列表
   * @param {string} context 错误上下文
   */
  handleValidationError(errors, context = '数据验证') {
    const errorInfo = {
      type: 'VALIDATION_ERROR',
      context,
      errors,
      timestamp: Date.now()
    }
    
    console.error(`${context}错误:`, errorInfo)
    this.reportError(errorInfo)
  },
  
  /**
   * 上报错误信息
   * @param {object} errorInfo 错误信息
   */
  reportError(errorInfo) {
    // 这里可以实现错误上报到服务器的逻辑
    // 例如发送到错误监控系统或日志服务
    try {
      // 示例：存储到本地存储
      const errors = JSON.parse(uni.getStorageSync('mqtt_errors') || '[]')
      errors.push(errorInfo)
      
      // 只保留最近100条错误记录
      if (errors.length > 100) {
        errors.splice(0, errors.length - 100)
      }
      
      uni.setStorageSync('mqtt_errors', JSON.stringify(errors))
    } catch (e) {
      console.error('错误上报失败:', e)
    }
  },
  
  /**
   * 获取错误历史
   * @returns {Array} 错误历史记录
   */
  getErrorHistory() {
    try {
      return JSON.parse(uni.getStorageSync('mqtt_errors') || '[]')
    } catch (e) {
      console.error('获取错误历史失败:', e)
      return []
    }
  },
  
  /**
   * 清除错误历史
   */
  clearErrorHistory() {
    try {
      uni.removeStorageSync('mqtt_errors')
    } catch (e) {
      console.error('清除错误历史失败:', e)
    }
  }
}

/**
 * 创建全局MQTT客户端实例
 */
let globalMqttClient = null

/**
 * 获取全局MQTT客户端
 * @param {object} options 配置选项
 * @returns {MQTTClient} MQTT客户端实例
 */
export function getMqttClient(options = {}) {
  if (!globalMqttClient) {
    globalMqttClient = new MQTTClient(options)
  }
  return globalMqttClient
}

/**
 * 设备控制相关的MQTT方法
 */
export const deviceMqtt = {
  /**
   * 发送设备控制指令
   * @param {string} action 动作类型
   * @param {object} params 参数
   * @param {object} options 选项
   */
  sendDeviceCommand(action, params = {}, options = {}) {
    const client = getMqttClient()
    const topic = MQTT_TOPICS.GATEWAY_SUBSCRIBE(client.productKey, client.deviceName)
    
    const payload = {
      id: client.generateMessageId(),
      version: '1.0',
      method: action,
      params: {
        controlType: params.controlType || CONTROL_CODES.SINGLE_DEVICE,
        address: params.address || 1,
        ...params
      }
    }
    
    client.publish(topic, payload)
    console.log(`发送设备控制指令 [${action}]:`, payload)
  },

  /**
   * 控制设备开关
   * @param {number} address 设备地址
   * @param {boolean} status 开关状态
   * @param {number} controlType 控制类型
   */
  controlSwitch(address, status, controlType = CONTROL_CODES.SINGLE_DEVICE) {
    const action = status ? DEVICE_ACTIONS.LIGHT_ON : DEVICE_ACTIONS.LIGHT_OFF
    this.sendDeviceCommand(action, {
      controlType,
      address
    })
  },

  /**
   * 控制设备亮度
   * @param {number} address 设备地址
   * @param {number} brightness 亮度值(0-100)
   * @param {number} controlType 控制类型
   */
  controlBrightness(address, brightness, controlType = CONTROL_CODES.SINGLE_DEVICE) {
    this.sendDeviceCommand(DEVICE_ACTIONS.SET_HIGH_BRIGHT, {
      controlType,
      address,
      brightness: Math.max(0, Math.min(100, brightness))
    })
  },

  /**
   * 控制设备色温
   * @param {number} address 设备地址
   * @param {number} temperature 色温值
   * @param {number} controlType 控制类型
   */
  controlColorTemperature(address, temperature, controlType = CONTROL_CODES.SINGLE_DEVICE) {
    this.sendDeviceCommand(DEVICE_ACTIONS.SET_CCT_BRIGHT, {
      controlType,
      address,
      cct: temperature
    })
  },

  /**
   * 设置灯光模式
   * @param {number} address 设备地址
   * @param {string} mode 模式（中文）
   * @param {number} controlType 控制类型
   */
  setLightMode(address, mode, controlType = CONTROL_CODES.SINGLE_DEVICE) {
    const englishMode = PARAM_MAPPING.LIGHT_MODES[mode]
    if (englishMode) {
      this.sendDeviceCommand(DEVICE_ACTIONS.SET_LIGHT_MODE, {
        controlType,
        address,
        mode: englishMode
      })
    } else {
      console.warn('未知的灯光模式:', mode)
    }
  },

  /**
   * 设置延时模式
   * @param {number} address 设备地址
   * @param {string} delayMode 延时模式（中文）
   * @param {number} controlType 控制类型
   */
  setDelayMode(address, delayMode, controlType = CONTROL_CODES.SINGLE_DEVICE) {
    const englishMode = PARAM_MAPPING.DELAY_MODES[delayMode]
    if (englishMode) {
      this.sendDeviceCommand(DEVICE_ACTIONS.SET_DELAY_MODE, {
        controlType,
        address,
        delayMode: englishMode
      })
    } else {
      console.warn('未知的延时模式:', delayMode)
    }
  },

  /**
   * 设置恒照度控制模式
   * @param {number} address 设备地址
   * @param {string} alsMode 恒照度模式（中文）
   * @param {number} controlType 控制类型
   */
  setAlsMode(address, alsMode, controlType = CONTROL_CODES.SINGLE_DEVICE) {
    const englishMode = PARAM_MAPPING.ALS_MODES[alsMode]
    if (englishMode) {
      this.sendDeviceCommand(DEVICE_ACTIONS.SET_ALS_MODE, {
        controlType,
        address,
        alsMode: englishMode
      })
    } else {
      console.warn('未知的恒照度模式:', alsMode)
    }
  },

  /**
   * 设置情景模式
   * @param {Object} sceneConfig - 情景配置对象
   * @param {string} sceneConfig.deviceName - 设备节点名称（必填）
   * @param {string} sceneConfig.area - 区域编码（2字节十六进制，必填）
   * @param {string} sceneConfig.address - 设备地址编码（2字节十六进制，必填）
   * @param {Array} sceneConfig.lampGroups - 灯具组配置数组（必填）
   * @param {number} sceneConfig.sceneId - 情景唯一标识（必填）
   * @param {string} sceneConfig.sceneName - 情景名称（可选）
   * @param {string} sceneConfig.identity - 操作者身份标识（可选）
   */
  setScene(sceneConfig) {
    // 验证必填参数
    if (!sceneConfig.deviceName) {
      throw new Error('设备名称不能为空')
    }
    if (!sceneConfig.area) {
      throw new Error('区域编码不能为空')
    }
    if (!sceneConfig.address) {
      throw new Error('设备地址编码不能为空')
    }
    if (!sceneConfig.lampGroups || !Array.isArray(sceneConfig.lampGroups) || sceneConfig.lampGroups.length === 0) {
      throw new Error('灯具组配置不能为空')
    }
    if (sceneConfig.sceneId === undefined || sceneConfig.sceneId === null) {
      throw new Error('情景ID不能为空')
    }

    // 验证灯具组配置
    sceneConfig.lampGroups.forEach((group, index) => {
      if (!group.Lamps || !Array.isArray(group.Lamps) || group.Lamps.length === 0) {
        throw new Error(`第${index + 1}个灯具组的Lamps配置不能为空`)
      }
      if (!group.Params) {
        throw new Error(`第${index + 1}个灯具组的Params配置不能为空`)
      }
      
      const params = group.Params
      // 验证必填参数
      if (!params['有人亮度']) {
        throw new Error(`第${index + 1}个灯具组的有人亮度不能为空`)
      }
      if (!params['无人亮度']) {
        throw new Error(`第${index + 1}个灯具组的无人亮度不能为空`)
      }
      if (!params['一段延时']) {
        throw new Error(`第${index + 1}个灯具组的一段延时不能为空`)
      }
      if (!params['亮灯模式']) {
        throw new Error(`第${index + 1}个灯具组的亮灯模式不能为空`)
      }
      if (!params['感应模式']) {
        throw new Error(`第${index + 1}个灯具组的感应模式不能为空`)
      }

      // 验证百分比参数范围（0-100）
      const brightnessParams = ['有人亮度', '无人亮度', '色温']
      brightnessParams.forEach(param => {
        if (params[param]) {
          const value = parseInt(params[param].replace('%', ''))
          if (isNaN(value) || value < 0 || value > 100) {
            throw new Error(`第${index + 1}个灯具组的${param}必须在0-100%范围内`)
          }
        }
      })

      // 验证时间参数（≥0）
      const timeParams = ['一段延时', '二段延时']
      timeParams.forEach(param => {
        if (params[param]) {
          const value = parseInt(params[param].replace('秒', ''))
          if (isNaN(value) || value < 0) {
            throw new Error(`第${index + 1}个灯具组的${param}必须大于等于0秒`)
          }
        }
      })
    })

    // 构建SET_SCENE报文
    const message = {
      code: 100,
      deviceName: sceneConfig.deviceName,
      area: sceneConfig.area,
      address: sceneConfig.address,
      action: "setScene",
      params: {
        LampGroups: sceneConfig.lampGroups,
        SceneId: sceneConfig.sceneId,
        SceneName: sceneConfig.sceneName || '',
        identity: sceneConfig.identity || ''
      }
    }

    return this.sendDeviceCommand(DEVICE_ACTIONS.SET_SCENE, message)
  },

  /**
   * 扫描设备
   */
  scanDevices() {
    this.sendDeviceCommand(DEVICE_ACTIONS.SCAN)
  },

  /**
   * 停止扫描
   */
  stopScan() {
    this.sendDeviceCommand(DEVICE_ACTIONS.STOP_SCAN)
  },

  /**
   * 获取设备设置
   * @param {number} address 设备地址
   * @param {number} controlType 控制类型
   */
  getDeviceSetting(address, controlType = CONTROL_CODES.SINGLE_DEVICE) {
    this.sendDeviceCommand(DEVICE_ACTIONS.GET_SETTING, {
      controlType,
      address
    })
  },

  /**
   * 获取设备当前状态
   * @param {number} address 设备地址
   * @param {number} controlType 控制类型
   */
  getCurrentStatus(address, controlType = CONTROL_CODES.SINGLE_DEVICE) {
    this.sendDeviceCommand(DEVICE_ACTIONS.GET_CURRENT, {
      controlType,
      address
    })
  },

  /**
   * 设备闪烁
   * @param {number} address 设备地址
   * @param {number} controlType 控制类型
   */
  blinkDevice(address, controlType = CONTROL_CODES.SINGLE_DEVICE) {
    this.sendDeviceCommand(DEVICE_ACTIONS.BLINK, {
      controlType,
      address
    })
  },

  /**
   * 停止设备闪烁
   * @param {number} address 设备地址
   * @param {number} controlType 控制类型
   */
  stopBlink(address, controlType = CONTROL_CODES.SINGLE_DEVICE) {
    this.sendDeviceCommand(DEVICE_ACTIONS.STOP_BLINK, {
      controlType,
      address
    })
  },

  /**
   * 订阅设备事件
   * @param {string} eventType 事件类型
   * @param {function} callback 回调函数
   */
  subscribeDeviceEvent(eventType, callback) {
    const client = getMqttClient()
    client.on(eventType, callback)
  },

  /**
   * 取消订阅设备事件
   * @param {string} eventType 事件类型
   * @param {function} callback 回调函数
   */
  unsubscribeDeviceEvent(eventType, callback) {
    const client = getMqttClient()
    client.off(eventType, callback)
  }
}

/**
 * 地址编码工具类
 * 用于处理设备地址编码和解码
 */
/**
 * 地址编码器 - 处理不同控制类型的地址编码和解码
 * 支持单灯控制(number)、群组控制(cluster)、标签控制(label)、全区控制(area)等地址格式
 * 提供地址验证、编码转换、控制消息创建等功能
 */
export const AddressEncoder = {
  /**
   * 编码设备地址为标准格式
   * @param {number} controlType 控制类型代码 (100:单灯, 200:群组, 300:标签, 400:全区)
   * @param {number|string} address 地址值 (单灯:1-65535, 群组:1-255, 标签/全区:字符串)
   * @returns {object} 编码后的地址信息，包含code、address、type字段
   */
  encodeAddress(controlType, address) {
    const addressInfo = {
      code: controlType,
      address: address,
      type: this.getAddressType(controlType)
    }
    
    // 验证地址格式
    if (!this.validateAddress(controlType, address)) {
      throw new Error(`无效的地址格式: controlType=${controlType}, address=${address}`)
    }
    
    return addressInfo
  },
  
  /**
   * 根据控制类型获取对应的地址类型名称
   * @param {number} controlType 控制类型代码
   * @returns {string} 地址类型名称 ('number'|'cluster'|'label'|'area'|'unknown')
   */
  getAddressType(controlType) {
    const typeMap = {
      [CONTROL_CODES.SINGLE_DEVICE]: 'number',
      [CONTROL_CODES.GROUP_CONTROL]: 'cluster', 
      [CONTROL_CODES.LABEL_CONTROL]: 'label',
      [CONTROL_CODES.AREA_CONTROL]: 'area'
    }
    return typeMap[controlType] || 'unknown'
  },
  
  /**
   * 验证地址格式是否符合控制类型要求
   * @param {number} controlType 控制类型代码
   * @param {number|string} address 待验证的地址值
   * @returns {boolean} 验证结果，true表示格式正确
   */
  validateAddress(controlType, address) {
    switch (controlType) {
      case CONTROL_CODES.SINGLE_DEVICE:
        return typeof address === 'number' && address >= 1 && address <= 65535
      case CONTROL_CODES.GROUP_CONTROL:
        return typeof address === 'number' && address >= 1 && address <= 255
      case CONTROL_CODES.LABEL_CONTROL:
        return typeof address === 'string' && address.length > 0
      case CONTROL_CODES.AREA_CONTROL:
        return typeof address === 'string' && address.length > 0
      default:
        return false
    }
  },
  
  /**
   * 解码地址信息对象，提取控制类型和地址值
   * @param {object} addressInfo 包含code和address字段的地址信息对象
   * @returns {object} 解码结果，包含controlType、address、type、description字段
   */
  decodeAddress(addressInfo) {
    if (!addressInfo || typeof addressInfo !== 'object') {
      throw new Error('无效的地址信息对象')
    }
    
    const { code, address } = addressInfo
    return {
      controlType: code,
      address: address,
      type: this.getAddressType(code),
      description: this.getControlDescription(code)
    }
  },
  
  /**
   * 获取控制类型的中文描述
   * @param {number} controlType 控制类型代码
   * @returns {string} 控制类型的中文描述 ('单灯控制'|'群组控制'|'标签控制'|'全区控制'|'未知控制类型')
   */
  getControlDescription(controlType) {
    const descMap = {
      [CONTROL_CODES.SINGLE_DEVICE]: '单灯控制',
      [CONTROL_CODES.GROUP_CONTROL]: '群组控制',
      [CONTROL_CODES.LABEL_CONTROL]: '标签控制', 
      [CONTROL_CODES.AREA_CONTROL]: '全区控制'
    }
    return descMap[controlType] || '未知控制类型'
  },
  
  /**
   * 创建标准格式的设备控制消息
   * @param {string} action 设备动作类型 (如'lightOn', 'lightOff', 'setLightMode'等)
   * @param {number} controlType 控制类型代码
   * @param {number|string} address 目标设备地址
   * @param {object} params 额外的控制参数，默认为空对象
   * @returns {object} 标准格式的MQTT控制消息对象，包含id、version、method、params字段
   */
  createControlMessage(action, controlType, address, params = {}) {
    const addressInfo = this.encodeAddress(controlType, address)
    
    return {
      id: Date.now().toString(),
      version: '1.0',
      method: action,
      params: {
        controlType: addressInfo.code,
        address: addressInfo.address,
        ...params
      }
    }
  },
  
  /**
   * 创建批量设备控制消息数组
   * @param {string} action 设备动作类型
   * @param {Array} targets 目标设备列表，每个元素包含{controlType, address, params}结构
   * @returns {Array} 标准格式的MQTT控制消息对象数组
   */
  createBatchControlMessages(action, targets) {
    if (!Array.isArray(targets)) {
      throw new Error('targets必须是数组')
    }
    
    return targets.map(target => {
      const { controlType, address, params = {} } = target
      return this.createControlMessage(action, controlType, address, params)
    })
  }
}

export default {
  MQTT_CONFIG,
  MQTT_TOPICS,
  CONTROL_CODES,
  DEVICE_ACTIONS,
  PARAM_MAPPING,
  MQTTClient,
  MessageValidator,
  ErrorHandler,
  AddressEncoder,
  getMqttClient,
  deviceMqtt
}