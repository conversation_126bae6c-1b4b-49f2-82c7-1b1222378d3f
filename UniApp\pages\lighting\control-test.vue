<template>
  <view class="lighting-control">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">照明控制测试</text>
    </view>

    <!-- 基本信息 -->
    <view class="info-section">
      <text class="info-item">页面状态: {{ pageStatus }}</text>
      <text class="info-item">Vue版本: {{ vueVersion }}</text>
      <text class="info-item">当前时间: {{ currentTime }}</text>
    </view>

    <!-- API测试 -->
    <view class="api-section">
      <text class="section-title">API测试</text>
      <button @click="testDeviceApi" class="test-btn">测试设备API</button>
      <button @click="testGroupApi" class="test-btn">测试分组API</button>
      <button @click="testSceneApi" class="test-btn">测试场景API</button>
      <text class="api-result">{{ apiResult }}</text>
    </view>

    <!-- 导入测试 -->
    <view class="import-section">
      <text class="section-title">导入测试</text>
      <text class="import-item">设备API: {{ deviceApiStatus }}</text>
      <text class="import-item">照明API: {{ lightingApiStatus }}</text>
      <text class="import-item">Vue组合式API: {{ vueApiStatus }}</text>
    </view>
  </view>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'

// 测试导入
let deviceApiStatus = '未测试'
let lightingApiStatus = '未测试'
let vueApiStatus = 'OK'

try {
  const deviceApi = require('@/api/device')
  deviceApiStatus = deviceApi ? 'OK' : '失败'
} catch (error) {
  deviceApiStatus = `错误: ${error.message}`
}

try {
  const lightingApi = require('@/api/lighting')
  lightingApiStatus = lightingApi ? 'OK' : '失败'
} catch (error) {
  lightingApiStatus = `错误: ${error.message}`
}

export default {
  name: 'LightingControlTest',
  setup() {
    const pageStatus = ref('初始化中...')
    const vueVersion = ref('Vue 3')
    const currentTime = ref('')
    const apiResult = ref('未测试')

    // 更新时间
    const updateTime = () => {
      currentTime.value = new Date().toLocaleTimeString()
    }

    // 测试设备API
    const testDeviceApi = async () => {
      try {
        apiResult.value = '测试设备API中...'
        
        // 动态导入API
        const { getDeviceList } = await import('@/api/device')
        
        if (typeof getDeviceList === 'function') {
          apiResult.value = '设备API导入成功，尝试调用...'
          
          // 尝试调用API
          const result = await getDeviceList({ page: 1, pageSize: 10 })
          apiResult.value = `设备API调用成功: ${JSON.stringify(result).substring(0, 100)}...`
        } else {
          apiResult.value = '设备API导入失败: getDeviceList不是函数'
        }
      } catch (error) {
        apiResult.value = `设备API测试失败: ${error.message}`
        console.error('设备API测试错误:', error)
      }
    }

    // 测试分组API
    const testGroupApi = async () => {
      try {
        apiResult.value = '测试分组API中...'
        
        const { getGroups } = await import('@/api/device')
        
        if (typeof getGroups === 'function') {
          apiResult.value = '分组API导入成功，尝试调用...'
          
          const result = await getGroups()
          apiResult.value = `分组API调用成功: ${JSON.stringify(result).substring(0, 100)}...`
        } else {
          apiResult.value = '分组API导入失败: getGroups不是函数'
        }
      } catch (error) {
        apiResult.value = `分组API测试失败: ${error.message}`
        console.error('分组API测试错误:', error)
      }
    }

    // 测试场景API
    const testSceneApi = async () => {
      try {
        apiResult.value = '测试场景API中...'
        
        const { getScenes } = await import('@/api/lighting')
        
        if (typeof getScenes === 'function') {
          apiResult.value = '场景API导入成功，尝试调用...'
          
          const result = await getScenes()
          apiResult.value = `场景API调用成功: ${JSON.stringify(result).substring(0, 100)}...`
        } else {
          apiResult.value = '场景API导入失败: getScenes不是函数'
        }
      } catch (error) {
        apiResult.value = `场景API测试失败: ${error.message}`
        console.error('场景API测试错误:', error)
      }
    }

    // 页面加载时初始化
    onMounted(() => {
      console.log('照明控制测试页面加载')
      pageStatus.value = '页面加载完成'
      updateTime()
      
      // 定时更新时间
      setInterval(updateTime, 1000)
      
      // 输出调试信息
      console.log('Vue组合式API状态:', vueApiStatus)
      console.log('设备API状态:', deviceApiStatus)
      console.log('照明API状态:', lightingApiStatus)
    })

    return {
      pageStatus,
      vueVersion,
      currentTime,
      apiResult,
      deviceApiStatus,
      lightingApiStatus,
      vueApiStatus,
      testDeviceApi,
      testGroupApi,
      testSceneApi
    }
  }
}
</script>

<style scoped>
.lighting-control {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background-color: white;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.info-section,
.api-section,
.import-section {
  background-color: white;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.info-item,
.import-item {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
  padding: 10rpx;
  background-color: #f8f9fa;
  border-radius: 6rpx;
}

.test-btn {
  background-color: #1890ff;
  color: white;
  border: none;
  padding: 20rpx 30rpx;
  border-radius: 8rpx;
  margin: 0 10rpx 15rpx 0;
  font-size: 28rpx;
}

.test-btn:active {
  background-color: #0056CC;
}

.api-result {
  display: block;
  font-size: 26rpx;
  color: #333;
  background-color: #f0f0f0;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-top: 15rpx;
  word-break: break-all;
}
</style>