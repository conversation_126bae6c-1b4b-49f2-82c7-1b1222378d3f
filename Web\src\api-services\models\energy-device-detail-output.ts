/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { DeviceStatInfo } from './device-stat-info';
import { EnergyConsumptionOutput } from './energy-consumption-output';
import { EnergyControlOutput } from './energy-control-output';
import { EnergyDeviceGroupOutput } from './energy-device-group-output';
import { EnergyFaultOutput } from './energy-fault-output';
/**
 * 设备详情输出参数
 * @export
 * @interface EnergyDeviceDetailOutput
 */
export interface EnergyDeviceDetailOutput {
    /**
     * 主键ID
     * @type {number}
     * @memberof EnergyDeviceDetailOutput
     */
    id?: number;
    /**
     * 设备编码
     * @type {string}
     * @memberof EnergyDeviceDetailOutput
     */
    deviceCode?: string | null;
    /**
     * 设备名称
     * @type {string}
     * @memberof EnergyDeviceDetailOutput
     */
    deviceName?: string | null;
    /**
     * 设备类型
     * @type {string}
     * @memberof EnergyDeviceDetailOutput
     */
    deviceType?: string | null;
    /**
     * 设备型号
     * @type {string}
     * @memberof EnergyDeviceDetailOutput
     */
    deviceModel?: string | null;
    /**
     * 设备位置
     * @type {string}
     * @memberof EnergyDeviceDetailOutput
     */
    location?: string | null;
    /**
     * 分组ID
     * @type {number}
     * @memberof EnergyDeviceDetailOutput
     */
    groupId?: number | null;
    /**
     * 分组名称
     * @type {string}
     * @memberof EnergyDeviceDetailOutput
     */
    groupName?: string | null;
    /**
     * IP地址
     * @type {string}
     * @memberof EnergyDeviceDetailOutput
     */
    ipAddress?: string | null;
    /**
     * MAC地址
     * @type {string}
     * @memberof EnergyDeviceDetailOutput
     */
    macAddress?: string | null;
    /**
     * 固件版本
     * @type {string}
     * @memberof EnergyDeviceDetailOutput
     */
    firmwareVersion?: string | null;
    /**
     * 硬件版本
     * @type {string}
     * @memberof EnergyDeviceDetailOutput
     */
    hardwareVersion?: string | null;
    /**
     * 额定功率
     * @type {number}
     * @memberof EnergyDeviceDetailOutput
     */
    ratedPower?: number | null;
    /**
     * 额定电压
     * @type {number}
     * @memberof EnergyDeviceDetailOutput
     */
    ratedVoltage?: number | null;
    /**
     * 额定电流
     * @type {number}
     * @memberof EnergyDeviceDetailOutput
     */
    ratedCurrent?: number | null;
    /**
     * 安装日期
     * @type {Date}
     * @memberof EnergyDeviceDetailOutput
     */
    installDate?: Date | null;
    /**
     * 保修期至
     * @type {Date}
     * @memberof EnergyDeviceDetailOutput
     */
    warrantyDate?: Date | null;
    /**
     * 供应商
     * @type {string}
     * @memberof EnergyDeviceDetailOutput
     */
    supplier?: string | null;
    /**
     * 在线状态
     * @type {number}
     * @memberof EnergyDeviceDetailOutput
     */
    onlineStatus?: number;
    /**
     * 最后在线时间
     * @type {Date}
     * @memberof EnergyDeviceDetailOutput
     */
    lastOnlineTime?: Date | null;
    /**
     * 亮度
     * @type {number}
     * @memberof EnergyDeviceDetailOutput
     */
    brightness?: number | null;
    /**
     * 温度
     * @type {number}
     * @memberof EnergyDeviceDetailOutput
     */
    temperature?: number | null;
    /**
     * 湿度
     * @type {number}
     * @memberof EnergyDeviceDetailOutput
     */
    humidity?: number | null;
    /**
     * 功耗
     * @type {number}
     * @memberof EnergyDeviceDetailOutput
     */
    powerConsumption?: number | null;
    /**
     * 电压
     * @type {number}
     * @memberof EnergyDeviceDetailOutput
     */
    voltage?: number | null;
    /**
     * 电流
     * @type {number}
     * @memberof EnergyDeviceDetailOutput
     */
    current?: number | null;
    /**
     * 功率因数
     * @type {number}
     * @memberof EnergyDeviceDetailOutput
     */
    powerFactor?: number | null;
    /**
     * 频率
     * @type {number}
     * @memberof EnergyDeviceDetailOutput
     */
    frequency?: number | null;
    /**
     * 运行时长（小时）
     * @type {number}
     * @memberof EnergyDeviceDetailOutput
     */
    runningTime?: number | null;
    /**
     * 故障次数
     * @type {number}
     * @memberof EnergyDeviceDetailOutput
     */
    faultCount?: number | null;
    /**
     * 最后故障时间
     * @type {Date}
     * @memberof EnergyDeviceDetailOutput
     */
    lastFaultTime?: Date | null;
    /**
     * 维护日期
     * @type {Date}
     * @memberof EnergyDeviceDetailOutput
     */
    maintenanceDate?: Date | null;
    /**
     * 保修到期日期
     * @type {Date}
     * @memberof EnergyDeviceDetailOutput
     */
    warrantyExpiry?: Date | null;
    /**
     * 是否在线
     * @type {boolean}
     * @memberof EnergyDeviceDetailOutput
     */
    isOnline?: boolean | null;
    /**
     * 制造商
     * @type {string}
     * @memberof EnergyDeviceDetailOutput
     */
    manufacturer?: string | null;
    /**
     * 功率等级
     * @type {number}
     * @memberof EnergyDeviceDetailOutput
     */
    powerRating?: number | null;
    /**
     * 电压等级
     * @type {number}
     * @memberof EnergyDeviceDetailOutput
     */
    voltageRating?: number | null;
    /**
     * 电流等级
     * @type {number}
     * @memberof EnergyDeviceDetailOutput
     */
    currentRating?: number | null;
    /**
     * 租户ID
     * @type {number}
     * @memberof EnergyDeviceDetailOutput
     */
    tenantId?: number | null;
    /**
     * 备注
     * @type {string}
     * @memberof EnergyDeviceDetailOutput
     */
    remark?: string | null;
    /**
     * 排序
     * @type {number}
     * @memberof EnergyDeviceDetailOutput
     */
    sort?: number;
    /**
     * 状态
     * @type {number}
     * @memberof EnergyDeviceDetailOutput
     */
    status?: number;
    /**
     * 创建时间
     * @type {Date}
     * @memberof EnergyDeviceDetailOutput
     */
    createTime?: Date;
    /**
     * 更新时间
     * @type {Date}
     * @memberof EnergyDeviceDetailOutput
     */
    updateTime?: Date | null;
    /**
     * 
     * @type {EnergyDeviceGroupOutput}
     * @memberof EnergyDeviceDetailOutput
     */
    group?: EnergyDeviceGroupOutput;
    /**
     * 最近能耗记录
     * @type {Array<EnergyConsumptionOutput>}
     * @memberof EnergyDeviceDetailOutput
     */
    recentConsumptions?: Array<EnergyConsumptionOutput> | null;
    /**
     * 最近故障记录
     * @type {Array<EnergyFaultOutput>}
     * @memberof EnergyDeviceDetailOutput
     */
    recentFaults?: Array<EnergyFaultOutput> | null;
    /**
     * 最近控制记录
     * @type {Array<EnergyControlOutput>}
     * @memberof EnergyDeviceDetailOutput
     */
    recentControls?: Array<EnergyControlOutput> | null;
    /**
     * 
     * @type {DeviceStatInfo}
     * @memberof EnergyDeviceDetailOutput
     */
    statInfo?: DeviceStatInfo;
    /**
     * 今日运行时长(小时)
     * @type {number}
     * @memberof EnergyDeviceDetailOutput
     */
    todayRunningTime?: number;
    /**
     * 本月运行时长(小时)
     * @type {number}
     * @memberof EnergyDeviceDetailOutput
     */
    monthRunningTime?: number;
    /**
     * 累计运行时长(小时)
     * @type {number}
     * @memberof EnergyDeviceDetailOutput
     */
    totalRunningTime?: number;
}
