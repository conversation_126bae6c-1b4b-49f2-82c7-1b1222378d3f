/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 能耗统计输出参数
 * @export
 * @interface EnergyConsumptionStatOutput
 */
export interface EnergyConsumptionStatOutput {
    /**
     * 统计时间
     * @type {Date}
     * @memberof EnergyConsumptionStatOutput
     */
    statTime?: Date;
    /**
     * 设备数量
     * @type {number}
     * @memberof EnergyConsumptionStatOutput
     */
    deviceCount?: number;
    /**
     * 总功率(W)
     * @type {number}
     * @memberof EnergyConsumptionStatOutput
     */
    totalPower?: number;
    /**
     * 平均功率(W)
     * @type {number}
     * @memberof EnergyConsumptionStatOutput
     */
    averagePower?: number;
    /**
     * 最大功率(W)
     * @type {number}
     * @memberof EnergyConsumptionStatOutput
     */
    maxPower?: number;
    /**
     * 最小功率(W)
     * @type {number}
     * @memberof EnergyConsumptionStatOutput
     */
    minPower?: number;
    /**
     * 总能耗(kWh)
     * @type {number}
     * @memberof EnergyConsumptionStatOutput
     */
    totalEnergyConsumption?: number;
    /**
     * 平均能耗(kWh)
     * @type {number}
     * @memberof EnergyConsumptionStatOutput
     */
    averageEnergyConsumption?: number;
    /**
     * 节能率(%)
     * @type {number}
     * @memberof EnergyConsumptionStatOutput
     */
    energySavingRate?: number;
    /**
     * 总费用(元)
     * @type {number}
     * @memberof EnergyConsumptionStatOutput
     */
    totalCost?: number;
    /**
     * 总用电量(kWh)
     * @type {number}
     * @memberof EnergyConsumptionStatOutput
     */
    totalEnergy?: number;
    /**
     * 今日用电量(kWh)
     * @type {number}
     * @memberof EnergyConsumptionStatOutput
     */
    todayEnergy?: number;
    /**
     * 本月用电量(kWh)
     * @type {number}
     * @memberof EnergyConsumptionStatOutput
     */
    monthEnergy?: number;
    /**
     * 本年用电量(kWh)
     * @type {number}
     * @memberof EnergyConsumptionStatOutput
     */
    yearEnergy?: number;
    /**
     * 平均功率(W)
     * @type {number}
     * @memberof EnergyConsumptionStatOutput
     */
    avgPower?: number;
    /**
     * 记录数量
     * @type {number}
     * @memberof EnergyConsumptionStatOutput
     */
    recordCount?: number;
}
