/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { EnergySceneDeviceOutput } from './energy-scene-device-output';
/**
 * 场景输出参数
 * @export
 * @interface EnergySceneOutput
 */
export interface EnergySceneOutput {
    /**
     * 主键ID
     * @type {number}
     * @memberof EnergySceneOutput
     */
    id?: number;
    /**
     * 场景编码
     * @type {string}
     * @memberof EnergySceneOutput
     */
    sceneCode?: string | null;
    /**
     * 场景名称
     * @type {string}
     * @memberof EnergySceneOutput
     */
    sceneName?: string | null;
    /**
     * 场景类型：1手动场景 2定时场景 3感应场景 4联动场景
     * @type {number}
     * @memberof EnergySceneOutput
     */
    sceneType?: number;
    /**
     * 场景描述
     * @type {string}
     * @memberof EnergySceneOutput
     */
    description?: string | null;
    /**
     * 场景图标
     * @type {string}
     * @memberof EnergySceneOutput
     */
    icon?: string | null;
    /**
     * 执行次数
     * @type {number}
     * @memberof EnergySceneOutput
     */
    executeCount?: number;
    /**
     * 最后执行时间
     * @type {Date}
     * @memberof EnergySceneOutput
     */
    lastExecuteTime?: Date | null;
    /**
     * 平均执行时长(秒)
     * @type {number}
     * @memberof EnergySceneOutput
     */
    avgExecuteTime?: number | null;
    /**
     * 成功率
     * @type {number}
     * @memberof EnergySceneOutput
     */
    successRate?: number | null;
    /**
     * 排序
     * @type {number}
     * @memberof EnergySceneOutput
     */
    sort?: number;
    /**
     * 状态
     * @type {number}
     * @memberof EnergySceneOutput
     */
    status?: number;
    /**
     * 状态名称
     * @type {string}
     * @memberof EnergySceneOutput
     */
    statusName?: string | null;
    /**
     * 创建时间
     * @type {Date}
     * @memberof EnergySceneOutput
     */
    createTime?: Date;
    /**
     * 更新时间
     * @type {Date}
     * @memberof EnergySceneOutput
     */
    updateTime?: Date | null;
    /**
     * 触发条件(JSON格式)
     * @type {string}
     * @memberof EnergySceneOutput
     */
    triggerCondition?: string | null;
    /**
     * 执行动作(JSON格式)
     * @type {string}
     * @memberof EnergySceneOutput
     */
    executeActions?: string | null;
    /**
     * 定时表达式
     * @type {string}
     * @memberof EnergySceneOutput
     */
    cronExpression?: string | null;
    /**
     * 场景设备列表
     * @type {Array<EnergySceneDeviceOutput>}
     * @memberof EnergySceneOutput
     */
    devices?: Array<EnergySceneDeviceOutput> | null;
}
