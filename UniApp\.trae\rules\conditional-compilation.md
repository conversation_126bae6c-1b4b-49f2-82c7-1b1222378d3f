---
description: Uni-App X 条件编译规范
globs: *.uts,*.uvue,*.css,*.less,*.scss,*.ts,*.js,*.sass,*.json
alwaysApply: false
---
# 条件编译规范

## 基础语法
```
// 平台基础判断
#ifdef APP || MP
  // 小程序/APP 通用代码
  #ifdef APP-ANDROID
    // Android 专用逻辑
  #endif
  #ifdef APP-IOS
    // iOS 专用逻辑
  #endif
#endif

// 排除特定平台
#ifndef H5
  // 非 H5 平台代码
#endif

// 多平台组合
#ifdef APP-ANDROID || APP-IOS
  // 原生 App 平台代码
#endif
```

## 核心平台标识符

### 应用平台
- `APP` - App 平台通用
- `APP-ANDROID` - Android App 平台
- `APP-IOS` - iOS App 平台  
- `APP-HARMONY` - HarmonyOS Next 平台
- `WEB` - Web 平台 (等同于 H5)
- `H5` - H5 平台

### 小程序平台
- `MP` - 小程序平台通用
- `MP-WEIXIN` - 微信小程序
- `MP-ALIPAY` - 支付宝小程序
- `MP-BAIDU` - 百度小程序
- `MP-TOUTIAO` - 抖音小程序
- `MP-KUAISHOU` - 快手小程序
- `MP-JD` - 京东小程序
- `MP-HARMONY` - 鸿蒙原子化服务
- `MP-XHS` - 小红书小程序

### 版本标识符
- `uniVersion` - 用于区分编译器版本

## 条件编译使用场景

### 1. API 兼容性处理
```uts
#ifdef APP
  // 使用 App 专用 API
  const result = plus.device.getInfo()
#endif

#ifdef MP-WEIXIN
  // 使用微信小程序专用 API
  wx.getUserProfile()
#endif
```

### 2. 样式适配
```css
/* 通用样式 */
.container {
  padding: 20px;
}

/* #ifdef APP */
.container {
  padding-top: var(--status-bar-height);
}
/* #endif */

/* #ifdef H5 */
.container {
  max-width: 750px;
  margin: 0 auto;
}
/* #endif */
```

### 3. 组件差异化
```uvue
<template>
  <view>
    <!-- #ifdef APP -->
    <native-component></native-component>
    <!-- #endif -->
    
    <!-- #ifdef H5 -->
    <web-component></web-component>
    <!-- #endif -->
  </view>
</template>
```

## 最佳实践
- 优先使用平台通用代码，减少条件编译的使用
- 条件编译代码应该有清晰的注释说明
- 避免过度嵌套的条件编译
- 定期检查和清理不必要的条件编译代码
- 使用版本控制时注意条件编译对代码可读性的影响