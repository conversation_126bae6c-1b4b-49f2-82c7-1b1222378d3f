// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using MQTTnet;
using MQTTnet.Client;
using MQTTnet.Protocol;
using System.Text;
using Newtonsoft.Json;
using System.Text.RegularExpressions;
using System.Threading.Channels;
using System.Collections.Concurrent;
using System.Buffers;

namespace Admin.NET.Plugin.MQTT;

/// <summary>
/// MQTT消息路由器 - 高性能消息路由和处理
/// 支持并发消息处理、内存池优化、性能监控和错误恢复
/// </summary>
public class MqttMessageRouter : <PERSON><PERSON>leton, IDisposable
{
    private readonly ILogger<MqttMessageRouter> _logger;
    private readonly MqttClientManager _clientManager;
    private readonly MqttOptions _options;
    private readonly ConcurrentDictionary<string, ConcurrentBag<IMessageHandler>> _topicHandlers;
    private readonly ConcurrentDictionary<string, MessageSubscription> _subscriptions;
    private readonly Channel<MessageRoutingTask> _messageChannel;
    private readonly ChannelWriter<MessageRoutingTask> _messageWriter;
    private readonly ChannelReader<MessageRoutingTask> _messageReader;
    private readonly SemaphoreSlim _routingSemaphore;
    private readonly Timer _metricsTimer;
    private readonly ArrayPool<byte> _arrayPool;
    
    // 性能统计
    private long _messagesRouted;
    private long _routingErrors;
    private long _averageProcessingTime;
    private volatile bool _disposed;

    /// <summary>
    /// 是否已连接
    /// </summary>
    public bool IsConnected => _clientManager?.IsConnected ?? false;

    /// <summary>
    /// 构造函数 - 初始化高性能消息路由器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="clientManager">MQTT客户端管理器</param>
    /// <param name="options">MQTT配置选项</param>
    public MqttMessageRouter(ILogger<MqttMessageRouter> logger, 
                           MqttClientManager clientManager,
                           IOptions<MqttOptions> options)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _clientManager = clientManager ?? throw new ArgumentNullException(nameof(clientManager));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        
        // 初始化集合和通道
        _topicHandlers = new ConcurrentDictionary<string, ConcurrentBag<IMessageHandler>>();
        _subscriptions = new ConcurrentDictionary<string, MessageSubscription>();
        
        // 创建高性能消息处理通道
        var channelOptions = new BoundedChannelOptions(1000)
        {
            FullMode = BoundedChannelFullMode.Wait,
            SingleReader = false,
            SingleWriter = false,
            AllowSynchronousContinuations = false
        };
        _messageChannel = Channel.CreateBounded<MessageRoutingTask>(channelOptions);
        _messageWriter = _messageChannel.Writer;
        _messageReader = _messageChannel.Reader;
        
        _routingSemaphore = new SemaphoreSlim(Environment.ProcessorCount * 2, Environment.ProcessorCount * 2);
        _arrayPool = ArrayPool<byte>.Shared;
        
        // 启动消息处理任务
        _ = Task.Run(ProcessMessagesAsync);
        
        // 性能监控定时器
        _metricsTimer = new Timer(CollectMetrics, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));

        // 注册消息接收处理
        _clientManager.MessageReceived += OnMessageReceived;
        
        _logger.LogInformation("MQTT消息路由器初始化完成，并发处理能力: {Capacity}", Environment.ProcessorCount * 2);
    }

    /// <summary>
    /// 注册消息处理器 - 线程安全的处理器注册
    /// </summary>
    /// <typeparam name="T">消息类型</typeparam>
    /// <param name="topicPattern">主题模式</param>
    /// <param name="handler">消息处理器</param>
    /// <returns>注册任务</returns>
    public async Task RegisterHandlerAsync<T>(string topicPattern, IMessageHandler<T> handler) where T : class
    {
        if (string.IsNullOrWhiteSpace(topicPattern))
            throw new ArgumentException("主题模式不能为空", nameof(topicPattern));
        if (handler == null)
            throw new ArgumentNullException(nameof(handler));
        if (_disposed)
            throw new ObjectDisposedException(nameof(MqttMessageRouter));
            
        try
        {
            var handlers = _topicHandlers.GetOrAdd(topicPattern, _ => new ConcurrentBag<IMessageHandler>());
            handlers.Add(handler);

            // 自动订阅主题
            await SubscribeTopicAsync(topicPattern);

            _logger.LogInformation("注册消息处理器成功: {TopicPattern}, 处理器类型: {HandlerType}, 当前处理器数量: {Count}", 
                                 topicPattern, typeof(T).Name, handlers.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "注册消息处理器失败: {TopicPattern}, 处理器类型: {HandlerType}", 
                           topicPattern, typeof(T).Name);
            throw;
        }
    }

    /// <summary>
    /// 移除消息处理器 - 优化的处理器移除逻辑
    /// </summary>
    /// <param name="topicPattern">主题模式</param>
    /// <param name="handler">要移除的处理器</param>
    /// <returns>移除任务</returns>
    public async Task RemoveHandlerAsync(string topicPattern, IMessageHandler handler)
    {
        if (string.IsNullOrWhiteSpace(topicPattern))
            throw new ArgumentException("主题模式不能为空", nameof(topicPattern));
        if (handler == null)
            throw new ArgumentNullException(nameof(handler));
        if (_disposed)
            throw new ObjectDisposedException(nameof(MqttMessageRouter));
            
        try
        {
            if (_topicHandlers.TryGetValue(topicPattern, out var handlers))
            {
                // 创建新的处理器集合，排除要移除的处理器
                var newHandlers = new ConcurrentBag<IMessageHandler>();
                foreach (var h in handlers)
                {
                    if (!ReferenceEquals(h, handler))
                    {
                        newHandlers.Add(h);
                    }
                }
                
                // 如果没有处理器了，移除整个条目并取消订阅
                if (newHandlers.IsEmpty)
                {
                    _topicHandlers.TryRemove(topicPattern, out _);
                    await UnsubscribeTopicAsync(topicPattern);
                }
                else
                {
                    _topicHandlers.TryUpdate(topicPattern, newHandlers, handlers);
                }
            }

            _logger.LogInformation("移除消息处理器成功: {TopicPattern}, 处理器类型: {HandlerType}", 
                                 topicPattern, handler.GetType().Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移除消息处理器失败: {TopicPattern}, 处理器类型: {HandlerType}", 
                           topicPattern, handler.GetType().Name);
        }
    }

    /// <summary>
    /// 订阅主题
    /// </summary>
    private async Task SubscribeTopicAsync(string topicPattern)
    {
        try
        {
            if (!_subscriptions.ContainsKey(topicPattern))
            {
                var qos = (MqttQualityOfServiceLevel)_options.DefaultQoS;
                await _clientManager.SubscribeAsync(topicPattern, qos);
                
                _subscriptions.TryAdd(topicPattern, new MessageSubscription
                {
                    Topic = topicPattern,
                    Qos = qos,
                    SubscribedAt = DateTime.UtcNow,
                    MessageCount = 0
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "订阅主题失败: {TopicPattern}", topicPattern);
        }
    }

    /// <summary>
    /// 取消订阅主题
    /// </summary>
    private async Task UnsubscribeTopicAsync(string topicPattern)
    {
        try
        {
            await _clientManager.UnsubscribeAsync(topicPattern);
            _subscriptions.TryRemove(topicPattern, out _);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取消订阅主题失败: {TopicPattern}", topicPattern);
        }
    }

    /// <summary>
    /// 消息接收处理 - 使用Channel进行高性能异步处理
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="e">消息事件参数</param>
    private async void OnMessageReceived(object sender, MqttApplicationMessageReceivedEventArgs e)
    {
        if (_disposed || e?.ApplicationMessage == null)
            return;
            
        try
        {
            var topic = e.ApplicationMessage.Topic;
            var payloadSegment = e.ApplicationMessage.PayloadSegment;
            
            // 使用内存池优化内存分配
            var payloadArray = _arrayPool.Rent(payloadSegment.Count);
            try
            {
                payloadSegment.CopyTo(payloadArray);
                var payload = Encoding.UTF8.GetString(payloadArray, 0, payloadSegment.Count);
                
                // 创建路由任务并加入处理队列
                var routingTask = new MessageRoutingTask
                {
                    Topic = topic,
                    Payload = payload,
                    EventArgs = e,
                    ReceivedAt = DateTime.UtcNow
                };
                
                // 非阻塞写入Channel
                if (!_messageWriter.TryWrite(routingTask))
                {
                    _logger.LogWarning("消息处理队列已满，丢弃消息: {Topic}", topic);
                    Interlocked.Increment(ref _routingErrors);
                }
            }
            finally
            {
                _arrayPool.Return(payloadArray);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "消息接收处理失败, 主题: {Topic}", e.ApplicationMessage?.Topic);
            Interlocked.Increment(ref _routingErrors);
        }
    }

    /// <summary>
    /// 异步处理消息队列 - 高性能消息处理循环
    /// </summary>
    /// <returns>处理任务</returns>
    private async Task ProcessMessagesAsync()
    {
        await foreach (var routingTask in _messageReader.ReadAllAsync())
        {
            if (_disposed)
                break;
                
            _ = Task.Run(async () =>
            {
                await _routingSemaphore.WaitAsync();
                try
                {
                    var startTime = DateTime.UtcNow;
                    
                    // 更新订阅统计
                    UpdateSubscriptionStats(routingTask.Topic);
                    
                    // 路由消息到匹配的处理器
                    await RouteMessageAsync(routingTask.Topic, routingTask.Payload, routingTask.EventArgs);
                    
                    // 更新性能统计
                    Interlocked.Increment(ref _messagesRouted);
                    var processingTime = (DateTime.UtcNow - startTime).TotalMilliseconds;
                    Interlocked.Exchange(ref _averageProcessingTime, (long)processingTime);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "处理消息失败, 主题: {Topic}", routingTask.Topic);
                    Interlocked.Increment(ref _routingErrors);
                }
                finally
                {
                    _routingSemaphore.Release();
                }
            });
        }
    }
    
    /// <summary>
    /// 路由消息到处理器 - 优化的并发处理
    /// </summary>
    /// <param name="topic">主题</param>
    /// <param name="payload">消息内容</param>
    /// <param name="e">消息事件参数</param>
    /// <returns>路由任务</returns>
    private async Task RouteMessageAsync(string topic, string payload, MqttApplicationMessageReceivedEventArgs e)
    {
        var matchedHandlers = new List<IMessageHandler>();

        // 查找匹配的处理器
        foreach (var kvp in _topicHandlers)
        {
            if (IsTopicMatch(topic, kvp.Key))
            {
                matchedHandlers.AddRange(kvp.Value);
            }
        }

        if (matchedHandlers.Count == 0)
        {
            _logger.LogDebug("未找到匹配的消息处理器, 主题: {Topic}", topic);
            return;
        }

        // 并行处理消息，限制并发数
        var semaphore = new SemaphoreSlim(Math.Min(matchedHandlers.Count, Environment.ProcessorCount));
        var tasks = matchedHandlers.Select(async handler =>
        {
            await semaphore.WaitAsync();
            try
            {
                await handler.HandleAsync(topic, payload, e);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "消息处理器执行失败, 主题: {Topic}, 处理器: {HandlerType}", 
                               topic, handler.GetType().Name);
            }
            finally
            {
                semaphore.Release();
            }
        });

        await Task.WhenAll(tasks);
        semaphore.Dispose();
    }

    /// <summary>
    /// 更新订阅统计
    /// </summary>
    private void UpdateSubscriptionStats(string topic)
    {
        foreach (var subscription in _subscriptions.Values)
        {
            if (IsTopicMatch(topic, subscription.Topic))
            {
                Interlocked.Increment(ref subscription.MessageCount);
                subscription.LastMessageAt = DateTime.UtcNow;
            }
        }
    }

    /// <summary>
    /// 检查主题是否匹配
    /// </summary>
    private static bool IsTopicMatch(string topic, string pattern)
    {
        if (pattern == topic) return true;
        
        // 处理MQTT通配符
        if (pattern.Contains('+') || pattern.Contains('#'))
        {
            return MqttTopicFilterComparer.Compare(topic, pattern) == MqttTopicFilterCompareResult.IsMatch;
        }
        
        // 处理正则表达式模式
        if (pattern.StartsWith("regex:"))
        {
            var regexPattern = pattern.Substring(6);
            return Regex.IsMatch(topic, regexPattern);
        }
        
        return false;
    }

    /// <summary>
    /// 发布消息
    /// </summary>
    public async Task PublishAsync<T>(string topic, T message, MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtLeastOnce, bool retain = false) where T : class
    {
        try
        {
            await _clientManager.PublishAsync(topic, message, qos, retain);
            _logger.LogDebug("消息路由器发布消息成功, 主题: {Topic}", topic);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "消息路由器发布消息失败, 主题: {Topic}", topic);
            throw;
        }
    }

    /// <summary>
    /// 获取订阅统计信息
    /// </summary>
    public Dictionary<string, MessageSubscription> GetSubscriptionStats()
    {
        return new Dictionary<string, MessageSubscription>(_subscriptions);
    }

    /// <summary>
    /// 获取处理器统计信息 - 增强版本包含详细统计
    /// </summary>
    /// <returns>处理器统计字典</returns>
    public Dictionary<string, int> GetHandlerStats()
    {
        return _topicHandlers.ToDictionary(
            kvp => kvp.Key,
            kvp => kvp.Value.Count
        );
    }
    
    /// <summary>
    /// 获取路由器性能统计
    /// </summary>
    /// <returns>性能统计信息</returns>
    public MessageRouterStats GetPerformanceStats()
    {
        return new MessageRouterStats
        {
            MessagesRouted = Interlocked.Read(ref _messagesRouted),
            RoutingErrors = Interlocked.Read(ref _routingErrors),
            AverageProcessingTime = Interlocked.Read(ref _averageProcessingTime),
            ActiveHandlers = _topicHandlers.Count,
            ActiveSubscriptions = _subscriptions.Count,
            QueueLength = _messageChannel.Reader.CanCount ? _messageChannel.Reader.Count : -1,
            IsHealthy = !_disposed && IsConnected
        };
    }
    
    /// <summary>
    /// 收集性能指标 - 定时执行的性能监控
    /// </summary>
    /// <param name="state">定时器状态</param>
    private void CollectMetrics(object state)
    {
        if (_disposed)
            return;
            
        try
        {
            var stats = GetPerformanceStats();
            _logger.LogInformation("MQTT路由器性能统计 - 已路由消息: {MessagesRouted}, 错误: {Errors}, 平均处理时间: {AvgTime}ms, 活跃处理器: {Handlers}, 订阅: {Subscriptions}",
                stats.MessagesRouted, stats.RoutingErrors, stats.AverageProcessingTime, stats.ActiveHandlers, stats.ActiveSubscriptions);
                
            // 触发垃圾回收以优化内存使用
            if (stats.MessagesRouted % 10000 == 0 && stats.MessagesRouted > 0)
            {
                GC.Collect(0, GCCollectionMode.Optimized);
                _logger.LogDebug("执行垃圾回收优化内存使用");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "收集性能指标失败");
        }
    }
    
    /// <summary>
    /// 释放资源 - 确保正确清理所有资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;
            
        _disposed = true;
        
        try
        {
            // 停止接收新消息
            _clientManager.MessageReceived -= OnMessageReceived;
            
            // 关闭消息通道
            _messageWriter?.Complete();
            
            // 释放信号量
            _routingSemaphore?.Dispose();
            
            // 停止定时器
            _metricsTimer?.Dispose();
            
            // 清理处理器和订阅
            _topicHandlers.Clear();
            _subscriptions.Clear();
            
            _logger.LogInformation("MQTT消息路由器已释放所有资源");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "释放MQTT消息路由器资源时发生错误");
        }
    }

    /// <summary>
    /// 订阅主题（同步方法，用于测试）
    /// </summary>
    public void Subscribe(string topic, int qos)
    {
        try
        {
            var qosLevel = (MqttQualityOfServiceLevel)qos;
            
            if (!_subscriptions.ContainsKey(topic))
            {
                _subscriptions.TryAdd(topic, new MessageSubscription
                {
                    Topic = topic,
                    Qos = qosLevel,
                    SubscribedAt = DateTime.UtcNow,
                    MessageCount = 0,
                    LastMessageAt = DateTime.UtcNow
                });
                
                _logger.LogInformation("订阅主题成功: {Topic}, QoS: {Qos}", topic, qos);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "订阅主题失败: {Topic}", topic);
        }
    }

    /// <summary>
    /// 获取订阅信息（用于测试）
    /// </summary>
    public Dictionary<string, SubscriptionInfo> GetSubscriptions()
    {
        return _subscriptions.ToDictionary(
            kvp => kvp.Key,
            kvp => new SubscriptionInfo
            {
                Topic = kvp.Value.Topic,
                QosLevel = (int)kvp.Value.Qos,
                SubscribedAt = kvp.Value.SubscribedAt,
                MessageCount = kvp.Value.MessageCount,
                LastMessageAt = kvp.Value.LastMessageAt
            }
        );
    }

    /// <summary>
    /// 移除消息处理器
    /// </summary>
    /// <param name="handlerId">处理器ID</param>
    /// <param name="handler">处理器实例</param>
    public void RemoveHandler(string handlerId, IMessageHandler handler)
    {
        // 实现移除处理器的逻辑
        // 这里可以根据实际需求实现
    }

    /// <summary>
    /// 获取所有处理器
    /// </summary>
    /// <returns>处理器字典</returns>
    public Dictionary<string, IMessageHandler> GetHandlers()
    {
        // 返回处理器字典
        // 这里可以根据实际需求实现
        return new Dictionary<string, IMessageHandler>();
    }

    /// <summary>
    /// 清理过期订阅
    /// </summary>
    public async Task CleanupExpiredSubscriptionsAsync(TimeSpan expireTime)
    {
        try
        {
            var expiredTopics = _subscriptions
                .Where(kvp => DateTime.UtcNow - kvp.Value.LastMessageAt > expireTime)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var topic in expiredTopics)
            {
                if (!_topicHandlers.ContainsKey(topic))
                {
                    await UnsubscribeTopicAsync(topic);
                    _logger.LogInformation("清理过期订阅: {Topic}", topic);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期订阅失败");
        }
    }
}

/// <summary>
/// 消息路由任务 - 用于Channel队列处理的消息包装
/// </summary>
public class MessageRoutingTask
{
    /// <summary>
    /// 消息主题
    /// </summary>
    public string Topic { get; set; }
    
    /// <summary>
    /// 消息内容
    /// </summary>
    public string Payload { get; set; }
    
    /// <summary>
    /// 原始事件参数
    /// </summary>
    public MqttApplicationMessageReceivedEventArgs EventArgs { get; set; }
    
    /// <summary>
    /// 接收时间
    /// </summary>
    public DateTime ReceivedAt { get; set; }
}

/// <summary>
/// 消息路由器性能统计
/// </summary>
public class MessageRouterStats
{
    /// <summary>
    /// 已路由消息总数
    /// </summary>
    public long MessagesRouted { get; set; }
    
    /// <summary>
    /// 路由错误总数
    /// </summary>
    public long RoutingErrors { get; set; }
    
    /// <summary>
    /// 平均处理时间（毫秒）
    /// </summary>
    public long AverageProcessingTime { get; set; }
    
    /// <summary>
    /// 活跃处理器数量
    /// </summary>
    public int ActiveHandlers { get; set; }
    
    /// <summary>
    /// 活跃订阅数量
    /// </summary>
    public int ActiveSubscriptions { get; set; }
    
    /// <summary>
    /// 消息队列长度
    /// </summary>
    public int QueueLength { get; set; }
    
    /// <summary>
    /// 路由器健康状态
    /// </summary>
    public bool IsHealthy { get; set; }
}

/// <summary>
/// 消息订阅信息 - 增强版本支持更多统计
/// </summary>
public class MessageSubscription
{
    /// <summary>
    /// 主题
    /// </summary>
    public string Topic { get; set; }

    /// <summary>
    /// QoS等级
    /// </summary>
    public MqttQualityOfServiceLevel Qos { get; set; }

    /// <summary>
    /// 订阅时间
    /// </summary>
    public DateTime SubscribedAt { get; set; }

    /// <summary>
    /// 最后消息时间
    /// </summary>
    public DateTime LastMessageAt { get; set; }

    /// <summary>
    /// 消息数量（线程安全）
    /// </summary>
    public int MessageCount;
    
    /// <summary>
    /// 订阅是否活跃
    /// </summary>
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// 消息处理器接口
/// </summary>
public interface IMessageHandler
{
    /// <summary>
    /// 处理消息
    /// </summary>
    Task HandleAsync(string topic, string payload, MqttApplicationMessageReceivedEventArgs e);
}

/// <summary>
/// 泛型消息处理器接口
/// </summary>
public interface IMessageHandler<T> : IMessageHandler where T : class
{
    /// <summary>
    /// 处理强类型消息
    /// </summary>
    Task HandleAsync(string topic, T message, MqttApplicationMessageReceivedEventArgs e);
}

/// <summary>
/// 订阅信息（用于测试）
/// </summary>
public class SubscriptionInfo
{
    /// <summary>
    /// 主题
    /// </summary>
    public string Topic { get; set; }

    /// <summary>
    /// QoS等级
    /// </summary>
    public int QosLevel { get; set; }

    /// <summary>
    /// 订阅时间
    /// </summary>
    public DateTime SubscribedAt { get; set; }

    /// <summary>
    /// 最后消息时间
    /// </summary>
    public DateTime LastMessageAt { get; set; }

    /// <summary>
    /// 消息数量
    /// </summary>
    public int MessageCount { get; set; }
}

/// <summary>
/// 抽象消息处理器基类
/// </summary>
public abstract class MessageHandlerBase<T> : IMessageHandler<T> where T : class
{
    protected readonly ILogger _logger;

    protected MessageHandlerBase(ILogger logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 处理原始消息
    /// </summary>
    public async Task HandleAsync(string topic, string payload, MqttApplicationMessageReceivedEventArgs e)
    {
        try
        {
            T message = null;
            
            // 尝试反序列化消息
            if (typeof(T) == typeof(string))
            {
                message = payload as T;
            }
            else
            {
                message = JsonConvert.DeserializeObject<T>(payload);
            }

            if (message != null)
            {
                await HandleAsync(topic, message, e);
            }
            else
            {
                _logger.LogWarning("无法反序列化消息, 主题: {Topic}, 负载: {Payload}", topic, payload);
            }
        }
        catch (Newtonsoft.Json.JsonException ex)
        {
            _logger.LogError(ex, "消息反序列化失败, 主题: {Topic}, 负载: {Payload}", topic, payload);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理消息失败, 主题: {Topic}", topic);
        }
    }

    /// <summary>
    /// 处理强类型消息
    /// </summary>
    public abstract Task HandleAsync(string topic, T message, MqttApplicationMessageReceivedEventArgs e);
}