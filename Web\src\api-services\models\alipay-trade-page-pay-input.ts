/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { ExtUserInfo } from './ext-user-info';
import { ExtendParams } from './extend-params';
import { InvoiceInfo } from './invoice-info';
/**
 * 统一收单下单并支付页面接口输入参数
 * @export
 * @interface AlipayTradePagePayInput
 */
export interface AlipayTradePagePayInput {
    /**
     * 商户订单号
     * @type {string}
     * @memberof AlipayTradePagePayInput
     */
    outTradeNo: string;
    /**
     * 订单总金额
     * @type {string}
     * @memberof AlipayTradePagePayInput
     */
    totalAmount: string;
    /**
     * 订单标题
     * @type {string}
     * @memberof AlipayTradePagePayInput
     */
    subject: string;
    /**
     * 
     * @type {string}
     * @memberof AlipayTradePagePayInput
     */
    body?: string | null;
    /**
     * 超时时间
     * @type {string}
     * @memberof AlipayTradePagePayInput
     */
    timeoutExpress?: string | null;
    /**
     * 二维码宽度
     * @type {number}
     * @memberof AlipayTradePagePayInput
     */
    qrcodeWidth: number;
    /**
     * 
     * @type {ExtendParams}
     * @memberof AlipayTradePagePayInput
     */
    extendParams?: ExtendParams;
    /**
     * 商户业务数据
     * @type {{ [key: string]: any; }}
     * @memberof AlipayTradePagePayInput
     */
    businessParams?: { [key: string]: any; } | null;
    /**
     * 
     * @type {InvoiceInfo}
     * @memberof AlipayTradePagePayInput
     */
    invoiceInfo?: InvoiceInfo;
    /**
     * 
     * @type {ExtUserInfo}
     * @memberof AlipayTradePagePayInput
     */
    extUserInfo?: ExtUserInfo;
}
