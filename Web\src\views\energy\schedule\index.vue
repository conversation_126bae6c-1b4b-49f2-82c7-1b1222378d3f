<template>
  <div class="schedule-container schedule-management mobile-device-control responsive-table mobile-safe-area">
    <!-- 查询表单 -->
    <el-card class="box-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>定时任务管理</span>
        </div>
      </template>
      
      <el-form :model="state.queryParams" ref="queryFormRef" :inline="true" label-width="80px" class="mobile-form">
        <el-form-item label="任务名称" prop="taskName">
          <el-input
            v-model="state.queryParams.taskName"
            placeholder="请输入任务名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
            class="mobile-input"
          />
        </el-form-item>
        <el-form-item label="任务类型" prop="taskType">
          <el-select v-model="state.queryParams.taskType" placeholder="请选择任务类型" clearable style="width: 150px">
            <el-option label="定时开关" value="switch" />
            <el-option label="定时调光" value="brightness" />
            <el-option label="场景执行" value="scene" />
            <el-option label="批量控制" value="batch" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="isEnabled">
          <el-select v-model="state.queryParams.isEnabled" placeholder="请选择状态" clearable style="width: 120px">
            <el-option label="启用" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 任务列表 -->
    <el-card class="box-card full-table">
      <template #header>
        <div class="card-header">
          <span>定时任务列表</span>
          <div class="header-actions">
            <el-button type="primary" icon="Plus" @click="openAddTask">新增任务</el-button>
            <el-button type="info" icon="TrendCharts" @click="openEnhancedAnalysis">智能分析</el-button>
            <el-button type="success" icon="VideoPlay" @click="batchExecute" :disabled="state.selectedTasks.length === 0">批量执行</el-button>
            <el-button type="warning" icon="VideoPause" @click="batchDisable" :disabled="state.selectedTasks.length === 0">批量禁用</el-button>
          </div>
        </div>
      </template>
      
      <el-table
        v-loading="state.loading"
        :data="state.taskData"
        @selection-change="handleSelectionChange"
        row-key="id"
        border
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="taskName" label="任务名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="taskType" label="任务类型" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getTaskTypeColor(row.taskType)">{{ getTaskTypeLabel(row.taskType) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="cronExpression" label="执行时间" width="150" align="center">
          <template #default="{ row }">
            <span>{{ formatCronExpression(row.cronExpression) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="targetDevices" label="目标设备" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <el-tag v-if="row.targetType === 'all'" type="info">全部设备</el-tag>
            <el-tag v-else-if="row.targetType === 'group'" type="warning">{{ row.targetName }}</el-tag>
            <span v-else>{{ row.targetDevices?.join(', ') || '未设置' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="isEnabled" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.isEnabled"
              @change="changeTaskStatus(row)"
              :loading="row.statusLoading"
            />
          </template>
        </el-table-column>
        <el-table-column prop="lastExecuteTime" label="最后执行" width="180" align="center">
          <template #default="{ row }">
            {{ row.lastExecuteTime ? formatDateTime(row.lastExecuteTime) : '未执行' }}
          </template>
        </el-table-column>
        <el-table-column prop="nextExecuteTime" label="下次执行" width="180" align="center">
          <template #default="{ row }">
            {{ row.nextExecuteTime ? formatDateTime(row.nextExecuteTime) : '已停止' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" align="center" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" size="small" @click="executeTask(row)" :disabled="!row.isEnabled">
              立即执行
            </el-button>
            <el-button link type="success" size="small" @click="openEditTask(row)">
              编辑
            </el-button>
            <el-button link type="warning" size="small" @click="viewTaskLog(row)">
              日志
            </el-button>
            <el-button link type="info" size="small" @click="copyTask(row)">
              复制
            </el-button>
            <el-button link type="danger" size="small" @click="deleteTask(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <el-pagination 
        v-model:currentPage="state.tableParams.page" 
        v-model:page-size="state.tableParams.pageSize"
        @current-change="handleCurrentChange" 
        @size-change="handleSizeChange"
        :total="state.tableParams.total"
        layout="total, sizes, prev, pager, next, jumper" 
      />
    </el-card>

    <!-- 新增/编辑任务弹窗 -->
    <el-dialog v-model="state.taskDialogVisible" :title="state.taskDialogTitle" width="800px" destroy-on-close>
      <el-form ref="taskFormRef" :model="state.taskForm" :rules="state.taskRules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="任务名称" prop="taskName">
              <el-input v-model="state.taskForm.taskName" placeholder="请输入任务名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务类型" prop="taskType">
              <el-select v-model="state.taskForm.taskType" placeholder="请选择任务类型" style="width: 100%" @change="handleTaskTypeChange">
                <el-option label="定时开关" value="switch" />
                <el-option label="定时调光" value="brightness" />
                <el-option label="场景执行" value="scene" />
                <el-option label="批量控制" value="batch" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="执行时间" prop="cronExpression">
              <el-select v-model="state.taskForm.cronType" placeholder="选择时间类型" style="width: 100%" @change="handleCronTypeChange">
                <el-option label="每天" value="daily" />
                <el-option label="每周" value="weekly" />
                <el-option label="每月" value="monthly" />
                <el-option label="自定义" value="custom" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="具体时间" prop="executeTime">
              <el-time-picker
                v-if="state.taskForm.cronType !== 'custom'"
                v-model="state.taskForm.executeTime"
                placeholder="选择执行时间"
                format="HH:mm"
                value-format="HH:mm"
                style="width: 100%"
              />
              <el-input
                v-else
                v-model="state.taskForm.cronExpression"
                placeholder="请输入Cron表达式"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="目标设备" prop="targetType">
          <el-radio-group v-model="state.taskForm.targetType" @change="handleTargetTypeChange">
            <el-radio label="all">全部设备</el-radio>
            <el-radio label="group">设备分组</el-radio>
            <el-radio label="devices">指定设备</el-radio>
            <el-radio label="scene">执行场景</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item v-if="state.taskForm.targetType === 'group'" label="选择分组" prop="targetGroupId">
          <el-select v-model="state.taskForm.targetGroupId" placeholder="请选择设备分组" style="width: 100%">
            <el-option
              v-for="group in state.deviceGroups"
              :key="group.id"
              :label="group.groupName"
              :value="group.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item v-if="state.taskForm.targetType === 'devices'" label="选择设备" prop="targetDeviceIds">
          <el-select
            v-model="state.taskForm.targetDeviceIds"
            multiple
            filterable
            placeholder="请选择目标设备"
            style="width: 100%"
          >
            <el-option
              v-for="device in state.availableDevices"
              :key="device.id"
              :label="`${device.deviceName} (${device.deviceCode})`"
              :value="device.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item v-if="state.taskForm.targetType === 'scene'" label="选择场景" prop="targetSceneId">
          <el-select v-model="state.taskForm.targetSceneId" placeholder="请选择执行场景" style="width: 100%">
            <el-option
              v-for="scene in state.availableScenes"
              :key="scene.id"
              :label="scene.sceneName"
              :value="scene.id"
            />
          </el-select>
        </el-form-item>
        
        <!-- 控制参数 -->
        <div v-if="state.taskForm.taskType === 'switch'">
          <el-form-item label="开关动作" prop="switchAction">
            <el-radio-group v-model="state.taskForm.switchAction">
              <el-radio label="on">开启</el-radio>
              <el-radio label="off">关闭</el-radio>
              <el-radio label="toggle">切换</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        
        <div v-if="state.taskForm.taskType === 'brightness'">
          <el-form-item label="亮度设置" prop="brightness">
            <el-slider v-model="state.taskForm.brightness" :min="0" :max="100" :step="1" show-input />
          </el-form-item>
        </div>
        
        <div v-if="state.taskForm.taskType === 'batch'">
          <el-form-item label="开关控制">
            <el-radio-group v-model="state.taskForm.batchSwitchAction">
              <el-radio label="on">全部开启</el-radio>
              <el-radio label="off">全部关闭</el-radio>
              <el-radio label="none">不改变</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="亮度设置" v-if="state.taskForm.batchSwitchAction !== 'off'">
            <el-checkbox v-model="state.taskForm.setBrightness" label="设置亮度" />
            <el-slider 
              v-if="state.taskForm.setBrightness"
              v-model="state.taskForm.batchBrightness" 
              :min="0" 
              :max="100" 
              :step="5" 
              show-input 
              style="margin-top: 10px"
            />
          </el-form-item>
        </div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="任务状态" prop="isEnabled">
              <el-switch v-model="state.taskForm.isEnabled" active-text="启用" inactive-text="禁用" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="重复执行" prop="isRepeatable">
              <el-switch v-model="state.taskForm.isRepeatable" active-text="是" inactive-text="否" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="任务描述" prop="description">
          <el-input v-model="state.taskForm.description" type="textarea" :rows="3" placeholder="请输入任务描述" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="state.taskDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitTask" :loading="state.submitLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 任务日志弹窗 -->
    <el-dialog v-model="state.logDialogVisible" title="任务执行日志" width="900px" destroy-on-close>
      <el-table
        v-loading="state.logLoading"
        :data="state.taskLogs"
        border
        stripe
        style="width: 100%"
        max-height="400"
      >
        <el-table-column prop="executeTime" label="执行时间" width="180" align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.executeTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="执行状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === 'success' ? 'success' : 'danger'">
              {{ row.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="deviceCount" label="设备数量" width="100" align="center" />
        <el-table-column prop="successCount" label="成功数量" width="100" align="center" />
        <el-table-column prop="failCount" label="失败数量" width="100" align="center" />
        <el-table-column prop="duration" label="执行耗时" width="120" align="center">
          <template #default="{ row }">
            {{ row.duration }}ms
          </template>
        </el-table-column>
        <el-table-column prop="message" label="执行结果" min-width="200" show-overflow-tooltip />
      </el-table>
    </el-dialog>

    <!-- 增强分析组件 -->
    <ScheduleEnhanced ref="scheduleEnhancedRef" v-model="state.enhancedVisible" @refresh="handleQuery" />
  </div>
</template>

<script setup lang="ts" name="schedule">
import { onMounted, reactive, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getAPI } from '/@/utils/axios-utils';
import { useDeviceApi } from '/@/api-services/device';
import { useSceneApi } from '/@/api-services/scene';
import { formatDateTime } from '/@/utils/formatTime';
import ScheduleEnhanced from './component/scheduleEnhanced.vue';

const taskFormRef = ref();
const scheduleEnhancedRef = ref();

const state = reactive({
  loading: false,
  taskData: [] as Array<any>,
  selectedTasks: [] as Array<any>,
  queryParams: {
    taskName: undefined,
    taskType: undefined,
    isEnabled: undefined
  },
  tableParams: {
    page: 1,
    pageSize: 20,
    total: 0 as any,
  },
  taskDialogVisible: false,
  taskDialogTitle: '',
  submitLoading: false,
  taskForm: {
    id: undefined,
    taskName: '',
    taskType: '',
    cronType: 'daily',
    cronExpression: '',
    executeTime: '',
    targetType: 'all',
    targetGroupId: undefined,
    targetDeviceIds: [],
    targetSceneId: undefined,
    switchAction: 'on',
    brightness: 80,
    batchSwitchAction: 'on',
    setBrightness: false,
    batchBrightness: 80,
    isEnabled: true,
    isRepeatable: true,
    description: ''
  },
  taskRules: {
    taskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
    taskType: [{ required: true, message: '请选择任务类型', trigger: 'change' }],
    executeTime: [{ required: true, message: '请选择执行时间', trigger: 'change' }]
  },
  availableDevices: [] as Array<any>,
  deviceGroups: [] as Array<any>,
  availableScenes: [] as Array<any>,
  logDialogVisible: false,
  logLoading: false,
  taskLogs: [] as Array<any>,
  enhancedVisible: false
});

// 模拟数据
const mockTasks = [
  {
    id: '1',
    taskName: '办公区域晚间关灯',
    taskType: 'switch',
    cronExpression: '0 0 22 * * ?',
    targetType: 'group',
    targetName: '办公区域',
    switchAction: 'off',
    isEnabled: true,
    lastExecuteTime: '2024-01-15 22:00:00',
    nextExecuteTime: '2024-01-16 22:00:00',
    description: '每天晚上10点自动关闭办公区域所有灯具'
  },
  {
    id: '2',
    taskName: '会议室智能调光',
    taskType: 'brightness',
    cronExpression: '0 0 9 * * MON-FRI',
    targetType: 'devices',
    targetDevices: ['会议室LED-001', '会议室LED-002'],
    brightness: 70,
    isEnabled: true,
    lastExecuteTime: '2024-01-15 09:00:00',
    nextExecuteTime: '2024-01-16 09:00:00',
    description: '工作日早上9点将会议室灯光调至70%亮度'
  },
  {
    id: '3',
    taskName: '节能模式场景',
    taskType: 'scene',
    cronExpression: '0 0 18 * * ?',
    targetType: 'scene',
    targetName: '节能模式',
    isEnabled: false,
    lastExecuteTime: null,
    nextExecuteTime: null,
    description: '每天下午6点执行节能模式场景'
  }
];

const mockDevices = [
  { id: '1', deviceName: '办公室LED-001', deviceCode: 'LED001' },
  { id: '2', deviceName: '会议室LED-001', deviceCode: 'LED002' },
  { id: '3', deviceName: '走廊LED-001', deviceCode: 'LED003' }
];

const mockGroups = [
  { id: '1', groupName: '办公区域' },
  { id: '2', groupName: '会议区域' },
  { id: '3', groupName: '公共区域' }
];

const mockScenes = [
  { id: '1', sceneName: '节能模式' },
  { id: '2', sceneName: '工作模式' },
  { id: '3', sceneName: '会议模式' }
];

const mockLogs = [
  {
    executeTime: '2024-01-15 22:00:00',
    status: 'success',
    deviceCount: 15,
    successCount: 15,
    failCount: 0,
    duration: 1250,
    message: '任务执行成功，所有设备已关闭'
  },
  {
    executeTime: '2024-01-14 22:00:00',
    status: 'success',
    deviceCount: 15,
    successCount: 14,
    failCount: 1,
    duration: 1580,
    message: '任务执行完成，1台设备离线未响应'
  }
];

onMounted(async () => {
  await loadMockData();
  await handleQuery();
});

// 加载模拟数据
const loadMockData = async () => {
  state.availableDevices = mockDevices;
  state.deviceGroups = mockGroups;
  state.availableScenes = mockScenes;
};

// 查询操作
const handleQuery = async () => {
  state.loading = true;
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    
    let filteredTasks = [...mockTasks];
    
    if (state.queryParams.taskName) {
      filteredTasks = filteredTasks.filter(task => 
        task.taskName.includes(state.queryParams.taskName)
      );
    }
    
    if (state.queryParams.taskType) {
      filteredTasks = filteredTasks.filter(task => 
        task.taskType === state.queryParams.taskType
      );
    }
    
    if (state.queryParams.isEnabled !== undefined) {
      filteredTasks = filteredTasks.filter(task => 
        task.isEnabled === state.queryParams.isEnabled
      );
    }
    
    state.taskData = filteredTasks;
    state.tableParams.total = filteredTasks.length;
  } catch (error) {
    console.error('查询定时任务失败:', error);
    ElMessage.error('查询定时任务失败');
  } finally {
    state.loading = false;
  }
};

// 重置操作
const resetQuery = async () => {
  state.queryParams.taskName = undefined;
  state.queryParams.taskType = undefined;
  state.queryParams.isEnabled = undefined;
  await handleQuery();
};

// 获取任务类型标签
const getTaskTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    'switch': '定时开关',
    'brightness': '定时调光',
    'scene': '场景执行',
    'batch': '批量控制'
  };
  return typeMap[type] || type;
};

// 获取任务类型颜色
const getTaskTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'switch': 'primary',
    'brightness': 'success',
    'scene': 'warning',
    'batch': 'info'
  };
  return colorMap[type] || '';
};

// 格式化Cron表达式
const formatCronExpression = (cron: string) => {
  // 简单的Cron表达式解析，实际项目中可以使用专门的库
  if (cron === '0 0 22 * * ?') return '每天 22:00';
  if (cron === '0 0 9 * * MON-FRI') return '工作日 09:00';
  if (cron === '0 0 18 * * ?') return '每天 18:00';
  return cron;
};

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  state.selectedTasks = selection;
};

// 改变页面容量
const handleSizeChange = (val: number) => {
  state.tableParams.pageSize = val;
  handleQuery();
};

// 改变页码序号
const handleCurrentChange = async (val: number) => {
  state.tableParams.page = val;
  await handleQuery();
};

// 打开新增任务
const openAddTask = () => {
  state.taskDialogTitle = '新增定时任务';
  state.taskForm = {
    id: undefined,
    taskName: '',
    taskType: '',
    cronType: 'daily',
    cronExpression: '',
    executeTime: '',
    targetType: 'all',
    targetGroupId: undefined,
    targetDeviceIds: [],
    targetSceneId: undefined,
    switchAction: 'on',
    brightness: 80,
    batchSwitchAction: 'on',
    setBrightness: false,
    batchBrightness: 80,
    isEnabled: true,
    isRepeatable: true,
    description: ''
  };
  state.taskDialogVisible = true;
};

// 打开编辑任务
const openEditTask = (row: any) => {
  state.taskDialogTitle = '编辑定时任务';
  state.taskForm = { ...row };
  state.taskDialogVisible = true;
};

// 任务类型变化
const handleTaskTypeChange = (type: string) => {
  // 根据任务类型重置相关字段
  if (type === 'scene') {
    state.taskForm.targetType = 'scene';
  }
};

// Cron类型变化
const handleCronTypeChange = (type: string) => {
  if (type !== 'custom') {
    state.taskForm.cronExpression = '';
  }
};

// 目标类型变化
const handleTargetTypeChange = (type: string) => {
  // 清空相关字段
  state.taskForm.targetGroupId = undefined;
  state.taskForm.targetDeviceIds = [];
  state.taskForm.targetSceneId = undefined;
};

// 提交任务
const submitTask = async () => {
  try {
    await taskFormRef.value?.validate();
    
    state.submitLoading = true;
    
    // 构建Cron表达式
    if (state.taskForm.cronType !== 'custom' && state.taskForm.executeTime) {
      const [hour, minute] = state.taskForm.executeTime.split(':');
      if (state.taskForm.cronType === 'daily') {
        state.taskForm.cronExpression = `0 ${minute} ${hour} * * ?`;
      } else if (state.taskForm.cronType === 'weekly') {
        state.taskForm.cronExpression = `0 ${minute} ${hour} * * MON-FRI`;
      } else if (state.taskForm.cronType === 'monthly') {
        state.taskForm.cronExpression = `0 ${minute} ${hour} 1 * ?`;
      }
    }
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    ElMessage.success(state.taskForm.id ? '更新任务成功' : '创建任务成功');
    state.taskDialogVisible = false;
    await handleQuery();
  } catch (error) {
    console.error('提交任务失败:', error);
  } finally {
    state.submitLoading = false;
  }
};

// 修改任务状态
const changeTaskStatus = async (row: any) => {
  try {
    row.statusLoading = true;
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    
    ElMessage.success(`任务${row.isEnabled ? '启用' : '禁用'}成功`);
  } catch (error) {
    console.error('修改任务状态失败:', error);
    ElMessage.error('修改任务状态失败');
    // 恢复原状态
    row.isEnabled = !row.isEnabled;
  } finally {
    row.statusLoading = false;
  }
};

// 执行任务
const executeTask = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定立即执行任务：【${row.taskName}】?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    ElMessage.success('任务执行成功');
    await handleQuery();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('执行任务失败:', error);
      ElMessage.error('执行任务失败');
    }
  }
};

// 批量执行
const batchExecute = async () => {
  try {
    await ElMessageBox.confirm(`确定批量执行选中的 ${state.selectedTasks.length} 个任务?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    ElMessage.success(`批量执行 ${state.selectedTasks.length} 个任务成功`);
    await handleQuery();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量执行失败:', error);
      ElMessage.error('批量执行失败');
    }
  }
};

// 批量禁用
const batchDisable = async () => {
  try {
    await ElMessageBox.confirm(`确定批量禁用选中的 ${state.selectedTasks.length} 个任务?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    ElMessage.success(`批量禁用 ${state.selectedTasks.length} 个任务成功`);
    await handleQuery();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量禁用失败:', error);
      ElMessage.error('批量禁用失败');
    }
  }
};

// 复制任务
const copyTask = (row: any) => {
  state.taskDialogTitle = '复制定时任务';
  state.taskForm = {
    ...row,
    id: undefined,
    taskName: `${row.taskName}_副本`,
    isEnabled: false
  };
  state.taskDialogVisible = true;
};

// 删除任务
const deleteTask = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定删除任务：【${row.taskName}】?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    
    ElMessage.success('删除任务成功');
    await handleQuery();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除任务失败:', error);
      ElMessage.error('删除任务失败');
    }
  }
};

// 查看任务日志
const viewTaskLog = async (row: any) => {
  state.logDialogVisible = true;
  state.logLoading = true;
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    state.taskLogs = mockLogs;
  } catch (error) {
    console.error('获取任务日志失败:', error);
    ElMessage.error('获取任务日志失败');
  } finally {
    state.logLoading = false;
  }
};

// 打开增强分析
const openEnhancedAnalysis = () => {
  state.enhancedVisible = true;
};
</script>

<style scoped lang="scss">
.schedule-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.box-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .header-actions {
      display: flex;
      gap: 8px;
    }
  }
}

.full-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  
  :deep(.el-card__body) {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  
  :deep(.el-table) {
    flex: 1;
  }
}

.el-form--inline .el-form-item,
.el-form-item:last-of-type {
  margin: 5px 15px;
}

.dialog-footer {
  text-align: right;
}
</style>