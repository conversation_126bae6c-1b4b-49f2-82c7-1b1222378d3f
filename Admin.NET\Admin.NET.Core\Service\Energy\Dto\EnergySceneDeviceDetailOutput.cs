// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core.Service;

/// <summary>
/// 场景设备详情输出参数
/// </summary>
public class EnergySceneDeviceDetailOutput : EnergySceneDeviceOutput
{
    /// <summary>
    /// 场景详细信息
    /// </summary>
    public EnergySceneOutput? Scene { get; set; }

    /// <summary>
    /// 设备详细信息
    /// </summary>
    public EnergyDeviceOutput? Device { get; set; }

    /// <summary>
    /// 设备位置
    /// </summary>
    public string? DeviceLocation { get; set; }

    /// <summary>
    /// 设备类型
    /// </summary>
    public string? DeviceType { get; set; }

    /// <summary>
    /// 设备型号
    /// </summary>
    public string? DeviceModel { get; set; }

    /// <summary>
    /// 执行历史记录
    /// </summary>
    public List<SceneDeviceExecuteHistoryItem> ExecuteHistory { get; set; } = new();

    /// <summary>
    /// 最近执行记录
    /// </summary>
    public List<SceneDeviceExecuteRecord> RecentExecuteRecords { get; set; } = new();

    /// <summary>
    /// 统计信息
    /// </summary>
    public SceneDeviceStatInfo StatInfo { get; set; } = new();

    /// <summary>
    /// 今日执行次数
    /// </summary>
    public int TodayExecuteCount { get; set; }

    /// <summary>
    /// 本月执行次数
    /// </summary>
    public int ThisMonthExecuteCount { get; set; }

    /// <summary>
    /// 今日成功次数
    /// </summary>
    public int TodaySuccessCount { get; set; }

    /// <summary>
    /// 本月成功次数
    /// </summary>
    public int ThisMonthSuccessCount { get; set; }
}

/// <summary>
/// 场景设备执行历史项
/// </summary>
public class SceneDeviceExecuteHistoryItem
{
    /// <summary>
    /// 执行时间
    /// </summary>
    public DateTime ExecuteTime { get; set; }

    /// <summary>
    /// 执行结果
    /// </summary>
    public int ExecuteResult { get; set; }

    /// <summary>
    /// 响应时间(毫秒)
    /// </summary>
    public int? ResponseTime { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 控制参数
    /// </summary>
    public string? ControlParams { get; set; }
}

/// <summary>
/// 场景设备统计信息
/// </summary>
public class SceneDeviceStatInfo
{
    /// <summary>
    /// 今日执行次数
    /// </summary>
    public int TodayExecuteCount { get; set; }

    /// <summary>
    /// 本周执行次数
    /// </summary>
    public int WeekExecuteCount { get; set; }

    /// <summary>
    /// 本月执行次数
    /// </summary>
    public int MonthExecuteCount { get; set; }

    /// <summary>
    /// 今日成功次数
    /// </summary>
    public int TodaySuccessCount { get; set; }

    /// <summary>
    /// 本周成功次数
    /// </summary>
    public int WeekSuccessCount { get; set; }

    /// <summary>
    /// 本月成功次数
    /// </summary>
    public int MonthSuccessCount { get; set; }

    /// <summary>
    /// 最短响应时间(毫秒)
    /// </summary>
    public int? MinResponseTime { get; set; }

    /// <summary>
    /// 最长响应时间(毫秒)
    /// </summary>
    public int? MaxResponseTime { get; set; }

    /// <summary>
    /// 最后执行状态
    /// </summary>
    public int LastExecuteStatus { get; set; }
}

/// <summary>
/// 场景设备执行记录
/// </summary>
public class SceneDeviceExecuteRecord
{
    /// <summary>
    /// 控制记录ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 控制类型
    /// </summary>
    public int ControlType { get; set; }

    /// <summary>
    /// 控制命令
    /// </summary>
    public string ControlCommand { get; set; }

    /// <summary>
    /// 控制时间
    /// </summary>
    public DateTime ControlTime { get; set; }

    /// <summary>
    /// 执行状态
    /// </summary>
    public int ExecuteStatus { get; set; }

    /// <summary>
    /// 执行状态名称
    /// </summary>
    public string ExecuteStatusName { get; set; }

    /// <summary>
    /// 执行持续时间(毫秒)
    /// </summary>
    public int ExecuteDuration { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; }
}