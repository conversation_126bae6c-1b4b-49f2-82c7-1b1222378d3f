# MQTT插件服务器技术架构文档

## 1. 架构设计

### 1.1 整体架构图

```mermaid
graph TD
    A[客户端应用] --> B[MQTT插件服务器]
    C[网关设备] --> B
    D[智能设备] --> C
    
    B --> E[消息处理引擎]
    B --> F[安全认证模块]
    B --> G[数据持久化层]
    B --> H[监控管理模块]
    
    E --> I[主题订阅管理器]
    E --> J[消息转发器]
    E --> K[消息解析器]
    
    G --> L[(MySQL数据库)]
    G --> M[(Redis缓存)]
    
    H --> N[状态监控]
    H --> O[流量统计]
    H --> P[客户端管理]
    
    subgraph "高可用性层"
        Q[负载均衡器]
        R[故障转移]
        S[集群管理]
    end
    
    Q --> B
    R --> B
    S --> B
```

### 1.2 插件架构设计

基于Admin.NET插件架构模式，MQTT服务器采用模块化插件设计：

```mermaid
graph TD
    A[Admin.NET.Plugin.MQTT] --> B[Startup.cs]
    A --> C[Configuration/]
    A --> D[Service/]
    A --> E[Entity/]
    A --> F[Option/]
    A --> G[Middleware/]
    
    C --> C1[MQTT.json]
    D --> D1[MQTTService.cs]
    D --> D2[MessageProcessor.cs]
    D --> D3[TopicManager.cs]
    E --> E1[MQTTMessage.cs]
    E --> E2[ClientConnection.cs]
    F --> F1[MQTTOptions.cs]
    G --> G1[MQTTMiddleware.cs]
```

## 2. 技术描述

### 2.1 核心技术栈

* **后端框架**: ASP.NET Core 8.0 + Admin.NET插件架构

* **MQTT协议**: MQTTnet 4.x (支持MQTT 3.1.1/5.0)

* **数据库**: MySQL 8.0 + Redis 7.0

* **安全协议**: TLS 1.3 + X.509证书认证

* **监控**: SignalR + 自定义监控面板

* **负载均衡**: Nginx + Consul服务发现

### 2.2 依赖包清单

```xml
<PackageReference Include="MQTTnet" Version="4.3.1" />
<PackageReference Include="MQTTnet.AspNetCore" Version="4.3.1" />
<PackageReference Include="Microsoft.AspNetCore.SignalR" Version="8.0.0" />
<PackageReference Include="StackExchange.Redis" Version="2.7.4" />
<PackageReference Include="MySql.EntityFrameworkCore" Version="8.0.0" />
<PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
<PackageReference Include="Consul" Version="1.6.10.9" />
```

## 3. 路由定义

### 3.1 Web管理界面路由

| 路由              | 用途          |
| --------------- | ----------- |
| /mqtt/dashboard | MQTT服务器监控面板 |
| /mqtt/clients   | 客户端连接管理     |
| /mqtt/topics    | 主题订阅管理      |
| /mqtt/messages  | 消息流量统计      |
| /mqtt/security  | 安全配置管理      |
| /mqtt/settings  | 服务器配置       |

### 3.2 MQTT主题路由

基于网关主题订阅规则的标准主题结构：

| 主题类型 | 主题模式                                                      | 说明          |
| ---- | --------------------------------------------------------- | ----------- |
| 下行控制 | `/productKey/deviceName/user/get`                         | 云端向网关发送控制指令 |
| 上行事件 | `/sys/productKey/deviceName/thing/event/{eventType}/post` | 网关向云端上报设备事件 |
| 系统管理 | `/sys/mqtt/server/{action}`                               | 服务器管理指令     |
| 监控统计 | `/sys/mqtt/monitor/{metric}`                              | 监控数据上报      |

## 4. API定义

### 4.1 核心API接口

#### 4.1.1 MQTT服务管理

**启动MQTT服务**

```
POST /api/mqtt/server/start
```

请求参数：

| 参数名            | 类型      | 必填    | 说明       |
| -------------- | ------- | ----- | -------- |
| port           | int     | true  | MQTT服务端口 |
| enableTLS      | boolean | false | 是否启用TLS  |
| maxConnections | int     | false | 最大连接数    |

响应：

```json
{
  "success": true,
  "message": "MQTT服务启动成功",
  "data": {
    "serverId": "mqtt-server-001",
    "port": 1883,
    "status": "running"
  }
}
```

**停止MQTT服务**

```
POST /api/mqtt/server/stop
```

#### 4.1.2 客户端管理

**获取客户端列表**

```
GET /api/mqtt/clients
```

响应：

```json
{
  "success": true,
  "data": {
    "total": 150,
    "clients": [
      {
        "clientId": "gateway_001",
        "ipAddress": "*************",
        "connectTime": "2024-01-15T10:30:00Z",
        "status": "connected",
        "subscriptions": ["/sys/+/+/thing/event/+/post"]
      }
    ]
  }
}
```

**断开客户端连接**

```
POST /api/mqtt/clients/{clientId}/disconnect
```

#### 4.1.3 消息处理

**发布消息**

```
POST /api/mqtt/messages/publish
```

请求：

```json
{
  "topic": "/productKey001/gateway001/user/get",
  "payload": {
    "code": 200,
    "deviceName": "gateway001",
    "area": "00 01",
    "address": "00 03",
    "action": "lightOn",
    "params": "",
    "identity": ""
  },
  "qos": 1,
  "retain": false
}
```

**获取消息统计**

```
GET /api/mqtt/messages/statistics
```

### 4.2 网关指令API

基于网关主题订阅规则的标准指令接口：

#### 4.2.1 设备控制指令

**设备扫描**

```
POST /api/mqtt/gateway/scan
```

**灯光控制**

```
POST /api/mqtt/gateway/light/control
```

请求：

```json
{
  "productKey": "productKey001",
  "deviceName": "gateway001",
  "code": 200,
  "area": "00 01",
  "address": "00 03",
  "action": "lightOn"
}
```

#### 4.2.2 设备状态查询

**获取设备状态**

```
GET /api/mqtt/gateway/device/status
```

**获取能耗信息**

```
GET /api/mqtt/gateway/device/consumption
```

## 5. 服务器架构图

### 5.1 分层架构

```mermaid
graph TD
    A[表现层 - Web管理界面] --> B[应用层 - MQTT服务]
    B --> C[业务层 - 消息处理]
    C --> D[数据层 - 持久化存储]
    
    subgraph "应用层组件"
        B1[MQTT Broker]
        B2[WebSocket Gateway]
        B3[REST API]
    end
    
    subgraph "业务层组件"
        C1[主题管理器]
        C2[消息解析器]
        C3[转发引擎]
        C4[安全认证]
    end
    
    subgraph "数据层组件"
        D1[MySQL - 配置数据]
        D2[Redis - 缓存/会话]
        D3[文件系统 - 日志]
    end
    
    B --> B1
    B --> B2
    B --> B3
    
    C --> C1
    C --> C2
    C --> C3
    C --> C4
    
    D --> D1
    D --> D2
    D --> D3
```

### 5.2 高可用架构

```mermaid
graph TD
    A[负载均衡器 Nginx] --> B[MQTT服务器集群]
    
    subgraph "MQTT集群"
        B1[MQTT Server 1]
        B2[MQTT Server 2]
        B3[MQTT Server 3]
    end
    
    B --> B1
    B --> B2
    B --> B3
    
    B1 --> C[Redis集群]
    B2 --> C
    B3 --> C
    
    B1 --> D[MySQL主从]
    B2 --> D
    B3 --> D
    
    E[Consul服务发现] --> B1
    E --> B2
    E --> B3
    
    F[监控系统] --> B1
    F --> B2
    F --> B3
```

## 6. 数据模型

### 6.1 数据模型定义

```mermaid
erDiagram
    MQTT_SERVER ||--o{ CLIENT_CONNECTION : manages
    CLIENT_CONNECTION ||--o{ TOPIC_SUBSCRIPTION : has
    TOPIC_SUBSCRIPTION ||--o{ MESSAGE_LOG : generates
    CLIENT_CONNECTION ||--o{ MESSAGE_LOG : sends
    
    MQTT_SERVER {
        string id PK
        string name
        string host
        int port
        boolean tls_enabled
        datetime created_at
        datetime updated_at
        string status
    }
    
    CLIENT_CONNECTION {
        string id PK
        string client_id
        string ip_address
        datetime connect_time
        datetime last_seen
        string status
        json auth_info
    }
    
    TOPIC_SUBSCRIPTION {
        string id PK
        string client_id FK
        string topic_pattern
        int qos_level
        datetime subscribed_at
        boolean active
    }
    
    MESSAGE_LOG {
        string id PK
        string topic
        text payload
        string sender_client_id FK
        datetime timestamp
        int qos_level
        boolean retained
        string message_type
    }
    
    DEVICE_COMMAND {
        string id PK
        string product_key
        string device_name
        int code
        string area
        string address
        string action
        string params
        datetime created_at
        string status
    }
    
    DEVICE_EVENT {
        string id PK
        string product_key
        string device_name
        string event_type
        json event_data
        datetime timestamp
        boolean processed
    }
```

### 6.2 数据定义语言

#### MQTT服务器表

```sql
CREATE TABLE mqtt_servers (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(100) NOT NULL,
    host VARCHAR(255) NOT NULL,
    port INT NOT NULL DEFAULT 1883,
    tls_enabled BOOLEAN DEFAULT FALSE,
    max_connections INT DEFAULT 1000,
    status ENUM('running', 'stopped', 'error') DEFAULT 'stopped',
    config JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_mqtt_servers_status ON mqtt_servers(status);
CREATE INDEX idx_mqtt_servers_created_at ON mqtt_servers(created_at DESC);
```

#### 客户端连接表

```sql
CREATE TABLE client_connections (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    client_id VARCHAR(255) NOT NULL UNIQUE,
    ip_address VARCHAR(45) NOT NULL,
    connect_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('connected', 'disconnected', 'banned') DEFAULT 'connected',
    auth_info JSON,
    user_agent VARCHAR(500),
    protocol_version VARCHAR(10)
);

-- 创建索引
CREATE INDEX idx_client_connections_client_id ON client_connections(client_id);
CREATE INDEX idx_client_connections_status ON client_connections(status);
CREATE INDEX idx_client_connections_ip ON client_connections(ip_address);
```

#### 主题订阅表

```sql
CREATE TABLE topic_subscriptions (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    client_id VARCHAR(255) NOT NULL,
    topic_pattern VARCHAR(500) NOT NULL,
    qos_level TINYINT DEFAULT 0 CHECK (qos_level IN (0, 1, 2)),
    subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (client_id) REFERENCES client_connections(client_id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_topic_subscriptions_client_id ON topic_subscriptions(client_id);
CREATE INDEX idx_topic_subscriptions_topic ON topic_subscriptions(topic_pattern);
CREATE INDEX idx_topic_subscriptions_active ON topic_subscriptions(active);
```

#### 消息日志表

```sql
CREATE TABLE message_logs (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    topic VARCHAR(500) NOT NULL,
    payload LONGTEXT,
    sender_client_id VARCHAR(255),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    qos_level TINYINT DEFAULT 0,
    retained BOOLEAN DEFAULT FALSE,
    message_type ENUM('publish', 'subscribe', 'unsubscribe', 'connect', 'disconnect') DEFAULT 'publish',
    size_bytes INT DEFAULT 0,
    INDEX idx_message_logs_topic (topic),
    INDEX idx_message_logs_timestamp (timestamp DESC),
    INDEX idx_message_logs_client (sender_client_id)
) PARTITION BY RANGE (UNIX_TIMESTAMP(timestamp)) (
    PARTITION p202401 VALUES LESS THAN (UNIX_TIMESTAMP('2024-02-01')),
    PARTITION p202402 VALUES LESS THAN (UNIX_TIMESTAMP('2024-03-01')),
    PARTITION p202403 VALUES LESS THAN (UNIX_TIMESTAMP('2024-04-01')),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

#### 设备指令表

```sql
CREATE TABLE device_commands (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    product_key VARCHAR(100) NOT NULL,
    device_name VARCHAR(100) NOT NULL,
    code INT NOT NULL COMMENT '控制类型：100单灯，200群组，300标签，400全区',
    area VARCHAR(10) NOT NULL COMMENT '区域地址',
    address VARCHAR(10) NOT NULL COMMENT '设备地址',
    action VARCHAR(50) NOT NULL COMMENT '动作指令',
    params TEXT COMMENT '指令参数',
    identity VARCHAR(100) COMMENT '身份标识',
    status ENUM('pending', 'sent', 'acked', 'failed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sent_at TIMESTAMP NULL,
    acked_at TIMESTAMP NULL
);

-- 创建索引
CREATE INDEX idx_device_commands_product_device ON device_commands(product_key, device_name);
CREATE INDEX idx_device_commands_status ON device_commands(status);
CREATE INDEX idx_device_commands_created_at ON device_commands(created_at DESC);
```

#### 设备事件表

```sql
CREATE TABLE device_events (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    product_key VARCHAR(100) NOT NULL,
    device_name VARCHAR(100) NOT NULL,
    event_type ENUM('beacon', 'setting', 'sensor', 'dimming', 'network', 'irc', 'consumption', 'trigger', 'heartbeat', 'scene', 'current') NOT NULL,
    event_data JSON NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed BOOLEAN DEFAULT FALSE,
    process_time TIMESTAMP NULL
);

-- 创建索引
CREATE INDEX idx_device_events_product_device ON device_events(product_key, device_name);
CREATE INDEX idx_device_events_type ON device_events(event_type);
CREATE INDEX idx_device_events_timestamp ON device_events(timestamp DESC);
CREATE INDEX idx_device_events_processed ON device_events(processed);
```

#### 初始化数据

```sql
-- 插入默认MQTT服务器配置
INSERT INTO mqtt_servers (name, host, port, tls_enabled, max_connections, config) VALUES 
('默认MQTT服务器', '0.0.0.0', 1883, FALSE, 1000, JSON_OBJECT(
    'keepAlive', 60,
    'cleanSession', TRUE,
    'maxMessageSize', 1048576,
    'enableRetainedMessages', TRUE
));

-- 插入系统主题订阅规则
INSERT INTO topic_subscriptions (client_id, topic_pattern, qos_level) VALUES 
('system', '/sys/+/+/thing/event/+/post', 1),
('system', '/productKey/+/user/get', 1),
('system', '/sys/mqtt/server/+', 1);
```

## 7. 安全机制设计

### 7.1 TLS/SSL加密

* **证书管理**: 支持自签名证书和CA签发证书

* **加密算法**: TLS 1.3，支持ECDHE-RSA-AES256-GCM-SHA384

* **证书轮换**: 自动证书更新和热重载

### 7.2 客户端认证

* **用户名/密码认证**: 支持数据库和LDAP认证

* **证书认证**: X.509客户端证书双向认证

* **Token认证**: JWT令牌认证机制

### 7.3 访问控制

* **主题权限**: 基于客户端ID的主题发布/订阅权限控制

* **IP白名单**: 支持IP地址和网段白名单

* **速率限制**: 连接频率和消息发布频率限制

## 8. 监控与管理

### 8.1 实时监控指标

* **连接统计**: 当前连接数、历史峰值、连接成功率

* **消息统计**: 消息吞吐量、主题分布、QoS分布

* **性能指标**: CPU使用率、内存使用率、网络IO

* **错误统计**: 连接失败、认证失败、消息丢失

### 8.2 告警机制

* **阈值告警**: 连接数、消息量、错误率超阈值告警

* **异常告警**: 服务异常、网络异常、存储异常

* **通知方式**: 邮件、短信、钉钉、企业微信

## 9. 高可用性设计

### 9.1 集群部署

* **负载均衡**: Nginx TCP负载均衡，支持轮询、最少连接

* **服务发现**: Consul服务注册与发现

* **健康检查**: 定期健康检查和自动故障转移

### 9.2 数据一致性

* **Redis集群**: 主从复制 + 哨兵模式

* **MySQL集群**: 主从复制 + 读写分离

* **消息持久化**: 支持消息持久化和离线消息存储

### 9.3 容灾备份

* **数据备份**: 定期数据库备份和增量备份

* **配置备份**: 配置文件版本控制和自动备份

* **灾难恢复**: 快速恢复机制和数据回滚

## 10. 部署与运维

### 10.1 Docker部署

```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0
WORKDIR /app
COPY . .
EXPOSE 1883 8883 8080
ENTRYPOINT ["dotnet", "Admin.NET.Plugin.MQTT.dll"]
```

### 10.2 配置管理

```json
{
  "MQTT": {
    "ServerOptions": {
      "DefaultEndpoint": {
        "Port": 1883,
        "IsEnabled": true
      },
      "TlsEndpoint": {
        "Port": 8883,
        "IsEnabled": true,
        "CertificatePath": "/certs/server.pfx",
        "CertificatePassword": "password"
      },
      "MaxPendingMessagesPerClient": 1000,
      "DefaultCommunicationTimeout": "00:01:00"
    },
    "Security": {
      "EnableAuthentication": true,
      "EnableAuthorization": true,
      "AllowAnonymous": false
    },
    "Persistence": {
      "EnableMessagePersistence": true,
      "EnableRetainedMessages": true,
      "MaxRetainedMessages": 10000
    }
  }
}
```

### 10.3 性能优化

* **连接池**: 数据库连接池优化

* **缓存策略**: Redis缓存热点数据

* **异步处理**: 消息异步处理和批量处理

* **资源限制**: 内存和CPU资源限制配置

