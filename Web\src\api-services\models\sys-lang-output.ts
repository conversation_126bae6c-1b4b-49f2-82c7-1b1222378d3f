/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { DirectionEnum } from './direction-enum';
import { WeekEnum } from './week-enum';
/**
 * 语言输出参数
 * @export
 * @interface SysLangOutput
 */
export interface SysLangOutput {
    /**
     * 主键Id
     * @type {number}
     * @memberof SysLangOutput
     */
    id?: number;
    /**
     * 语言名称
     * @type {string}
     * @memberof SysLangOutput
     */
    name?: string | null;
    /**
     * 语言代码
     * @type {string}
     * @memberof SysLangOutput
     */
    code?: string | null;
    /**
     * ISO 语言代码
     * @type {string}
     * @memberof SysLangOutput
     */
    isoCode?: string | null;
    /**
     * URL 语言代码
     * @type {string}
     * @memberof SysLangOutput
     */
    urlCode?: string | null;
    /**
     * 
     * @type {DirectionEnum}
     * @memberof SysLangOutput
     */
    direction?: DirectionEnum;
    /**
     * 日期格式
     * @type {string}
     * @memberof SysLangOutput
     */
    dateFormat?: string | null;
    /**
     * 时间格式
     * @type {string}
     * @memberof SysLangOutput
     */
    timeFormat?: string | null;
    /**
     * 
     * @type {WeekEnum}
     * @memberof SysLangOutput
     */
    weekStart?: WeekEnum;
    /**
     * 分组符号
     * @type {string}
     * @memberof SysLangOutput
     */
    grouping?: string | null;
    /**
     * 小数点符号
     * @type {string}
     * @memberof SysLangOutput
     */
    decimalPoint?: string | null;
    /**
     * 千分位分隔符
     * @type {string}
     * @memberof SysLangOutput
     */
    thousandsSep?: string | null;
    /**
     * 是否启用
     * @type {boolean}
     * @memberof SysLangOutput
     */
    active?: boolean;
    /**
     * 创建时间
     * @type {Date}
     * @memberof SysLangOutput
     */
    createTime?: Date | null;
    /**
     * 更新时间
     * @type {Date}
     * @memberof SysLangOutput
     */
    updateTime?: Date | null;
    /**
     * 创建者Id
     * @type {number}
     * @memberof SysLangOutput
     */
    createUserId?: number | null;
    /**
     * 创建者姓名
     * @type {string}
     * @memberof SysLangOutput
     */
    createUserName?: string | null;
    /**
     * 修改者Id
     * @type {number}
     * @memberof SysLangOutput
     */
    updateUserId?: number | null;
    /**
     * 修改者姓名
     * @type {string}
     * @memberof SysLangOutput
     */
    updateUserName?: string | null;
}
