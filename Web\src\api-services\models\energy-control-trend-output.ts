/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 控制趋势输出参数
 * @export
 * @interface EnergyControlTrendOutput
 */
export interface EnergyControlTrendOutput {
    /**
     * 时间点
     * @type {Date}
     * @memberof EnergyControlTrendOutput
     */
    time?: Date;
    /**
     * 时间点
     * @type {Date}
     * @memberof EnergyControlTrendOutput
     */
    timePoint?: Date;
    /**
     * 总控制次数
     * @type {number}
     * @memberof EnergyControlTrendOutput
     */
    totalCount?: number;
    /**
     * 控制次数
     * @type {number}
     * @memberof EnergyControlTrendOutput
     */
    controlCount?: number;
    /**
     * 成功次数
     * @type {number}
     * @memberof EnergyControlTrendOutput
     */
    successCount?: number;
    /**
     * 失败次数
     * @type {number}
     * @memberof EnergyControlTrendOutput
     */
    failedCount?: number;
    /**
     * 失败次数
     * @type {number}
     * @memberof EnergyControlTrendOutput
     */
    failCount?: number;
    /**
     * 手动控制次数
     * @type {number}
     * @memberof EnergyControlTrendOutput
     */
    manualCount?: number;
    /**
     * 自动控制次数
     * @type {number}
     * @memberof EnergyControlTrendOutput
     */
    autoCount?: number;
    /**
     * 成功率(%)
     * @type {number}
     * @memberof EnergyControlTrendOutput
     */
    successRate?: number;
    /**
     * 平均响应时间(毫秒)
     * @type {number}
     * @memberof EnergyControlTrendOutput
     */
    avgResponseTime?: number | null;
    /**
     * 平均执行时长(毫秒)
     * @type {number}
     * @memberof EnergyControlTrendOutput
     */
    avgExecuteDuration?: number;
    /**
     * 开灯次数
     * @type {number}
     * @memberof EnergyControlTrendOutput
     */
    turnOnCount?: number;
    /**
     * 关灯次数
     * @type {number}
     * @memberof EnergyControlTrendOutput
     */
    turnOffCount?: number;
    /**
     * 调光次数
     * @type {number}
     * @memberof EnergyControlTrendOutput
     */
    dimCount?: number;
    /**
     * 场景执行次数
     * @type {number}
     * @memberof EnergyControlTrendOutput
     */
    sceneCount?: number;
    /**
     * 设备数量
     * @type {number}
     * @memberof EnergyControlTrendOutput
     */
    deviceCount?: number;
    /**
     * 活跃设备数量
     * @type {number}
     * @memberof EnergyControlTrendOutput
     */
    activeDeviceCount?: number;
}
