/**
 * 加密工具类
 * 提供SM2加密功能，用于密码传输加密
 */

import { sm2 } from 'sm-crypto'

/**
 * SM2公钥 - 需要与后端配置保持一致
 * 从后端配置文件 App.json 中的 Cryptogram.PublicKey 获取
 */
const SM2_PUBLIC_KEY = '0484C7466D950E120E5ECE5DD85D0C90EAA85081A3A2BD7C57AE6DC822EFCCBD66620C67B0103FC8DD280E36C3B282977B722AAEC3C56518EDCEBAFB72C5A05312'

/**
 * SM2加密函数
 * @param {string} plainText - 需要加密的明文
 * @returns {string} 加密后的密文
 */
export function sm2Encrypt(plainText) {
  try {
    if (!plainText) {
      throw new Error('明文不能为空')
    }
    
    // 使用SM2加密
    const encrypted = sm2.doEncrypt(plainText, SM2_PUBLIC_KEY, 1)
    return encrypted
  } catch (error) {
    console.error('SM2加密失败:', error)
    throw new Error('密码加密失败')
  }
}

/**
 * 加密密码 - 专门用于密码加密的函数
 * @param {string} password - 原始密码
 * @returns {string} 加密后的密码
 */
export function encryptPassword(password) {
  return sm2Encrypt(password)
}

/**
 * 验证SM2公钥格式
 * @param {string} publicKey - 公钥字符串
 * @returns {boolean} 是否为有效的公钥格式
 */
export function validatePublicKey(publicKey) {
  if (!publicKey || typeof publicKey !== 'string') {
    return false
  }
  
  // SM2公钥应该是130位的十六进制字符串（包含04前缀）
  const hexPattern = /^04[0-9a-fA-F]{128}$/
  return hexPattern.test(publicKey)
}

/**
 * 获取当前使用的SM2公钥
 * @returns {string} SM2公钥
 */
export function getPublicKey() {
  return SM2_PUBLIC_KEY
}

export default {
  sm2Encrypt,
  encryptPassword,
  validatePublicKey,
  getPublicKey
}