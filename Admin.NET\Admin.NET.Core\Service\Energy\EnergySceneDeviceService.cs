// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core.Service;

/// <summary>
/// 场景设备关联服务 🧩
/// </summary>
[ApiDescriptionSettings(Order = 506)]
public class EnergySceneDeviceService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<EnergySceneDevice> _energySceneDeviceRep;
    private readonly SqlSugarRepository<EnergyScene> _energySceneRep;
    private readonly SqlSugarRepository<EnergyDevice> _energyDeviceRep;
    private readonly SqlSugarRepository<EnergyControl> _energyControlRep;
    private readonly SysCacheService _sysCacheService;
    private readonly UserManager _userManager;

    public EnergySceneDeviceService(
        SqlSugarRepository<EnergySceneDevice> energySceneDeviceRep,
        SqlSugarRepository<EnergyScene> energySceneRep,
        SqlSugarRepository<EnergyDevice> energyDeviceRep,
        SqlSugarRepository<EnergyControl> energyControlRep,
        SysCacheService sysCacheService,
        UserManager userManager)
    {
        _energySceneDeviceRep = energySceneDeviceRep;
        _energySceneRep = energySceneRep;
        _energyDeviceRep = energyDeviceRep;
        _energyControlRep = energyControlRep;
        _sysCacheService = sysCacheService;
        _userManager = userManager;
    }

    /// <summary>
    /// 获取场景设备分页列表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取场景设备分页列表")]
    public async Task<SqlSugarPagedList<EnergySceneDeviceOutput>> GetPage([FromQuery] EnergySceneDeviceInput input)
    {
        var query = _energySceneDeviceRep.AsQueryable()
            .LeftJoin<EnergyScene>((sd, s) => sd.SceneId == s.Id)
            .LeftJoin<EnergyDevice>((sd, s, d) => sd.DeviceId == d.Id)
            .WhereIF(input.SceneId > 0, (sd, s, d) => sd.SceneId == input.SceneId)
            .WhereIF(input.DeviceId > 0, (sd, s, d) => sd.DeviceId == input.DeviceId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeviceCode), (sd, s, d) => d.DeviceCode.Contains(input.DeviceCode))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeviceName), (sd, s, d) => d.DeviceName.Contains(input.DeviceName))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ControlType), (sd, s, d) => sd.ControlType.Contains(input.ControlType))
            .WhereIF(input.Status.HasValue, (sd, s, d) => sd.Status == input.Status)
            .Select((sd, s, d) => new EnergySceneDeviceOutput
            {
                Id = sd.Id,
                SceneId = sd.SceneId,
                SceneName = s.SceneName,
                DeviceId = sd.DeviceId,
                DeviceCode = d.DeviceCode,
                DeviceName = d.DeviceName,
                DeviceLocation = d.Location,
                DeviceType = d.DeviceType,
                DeviceModel = d.DeviceModel,
                ControlType = sd.ControlType,
                ControlCommand = sd.ControlCommand,
                DelayTime = sd.DelayTime,
                Sort = sd.Sort,
                Status = sd.Status,
                StatusName = SqlFunc.IIF(sd.Status == 1, "启用", "禁用"),
                LastExecuteTime = sd.LastExecuteTime,
                ExecuteCount = sd.ExecuteCount,
                SuccessRate = sd.SuccessRate,
                AvgExecuteDuration = sd.AvgExecuteDuration,
                CreateTime = sd.CreateTime,
                TenantId = sd.TenantId
            })
            .OrderBy(sd => sd.SceneId)
            .OrderBy(sd => sd.Sort)
            .OrderBy(sd => sd.CreateTime, OrderByType.Desc);

        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取场景设备列表 🔖
    /// </summary>
    /// <param name="sceneId">场景ID</param>
    /// <returns></returns>
    [DisplayName("获取场景设备列表")]
    public async Task<List<EnergySceneDeviceOutput>> GetListBySceneId(long sceneId)
    {
        var query = _energySceneDeviceRep.AsQueryable()
            .LeftJoin<EnergyDevice>((sd, d) => sd.DeviceId == d.Id)
            .Where((sd, d) => sd.SceneId == sceneId)
            .Select((sd, d) => new EnergySceneDeviceOutput
            {
                Id = sd.Id,
                SceneId = sd.SceneId,
                DeviceId = sd.DeviceId,
                DeviceCode = d.DeviceCode,
                DeviceName = d.DeviceName,
                DeviceLocation = d.Location,
                DeviceType = d.DeviceType,
                DeviceModel = d.DeviceModel,
                ControlType = sd.ControlType,
                ControlCommand = sd.ControlCommand,
                DelayTime = sd.DelayTime,
                Sort = sd.Sort,
                Status = sd.Status,
                StatusName = SqlFunc.IIF(sd.Status == 1, "启用", "禁用"),
                LastExecuteTime = sd.LastExecuteTime,
                ExecuteCount = sd.ExecuteCount,
                SuccessRate = sd.SuccessRate,
                AvgExecuteDuration = sd.AvgExecuteDuration
            })
            .OrderBy(sd => sd.Sort)
            .OrderBy(sd => sd.CreateTime, OrderByType.Desc);

        return await query.ToListAsync();
    }

    /// <summary>
    /// 获取场景设备详情 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取场景设备详情")]
    public async Task<EnergySceneDeviceDetailOutput> GetDetail([FromQuery] QueryByIdEnergySceneDeviceInput input)
    {
        var sceneDevice = await _energySceneDeviceRep.AsQueryable()
            .LeftJoin<EnergyScene>((sd, s) => sd.SceneId == s.Id)
            .LeftJoin<EnergyDevice>((sd, s, d) => sd.DeviceId == d.Id)
            .Where((sd, s, d) => sd.Id == input.Id)
            .Select((sd, s, d) => new EnergySceneDeviceDetailOutput
            {
                Id = sd.Id,
                SceneId = sd.SceneId,
                SceneName = s.SceneName,
                DeviceId = sd.DeviceId,
                DeviceCode = d.DeviceCode,
                DeviceName = d.DeviceName,
                DeviceLocation = d.Location,
                DeviceType = d.DeviceType,
                DeviceModel = d.DeviceModel,
                ControlType = sd.ControlType,
                ControlParams = sd.ControlCommand,
                DelaySeconds = sd.DelayTime,
                Sort = sd.Sort,
                Status = sd.Status,
                LastExecuteTime = sd.LastExecuteTime,
                ExecuteCount = sd.ExecuteCount,
                SuccessCount = sd.SuccessCount,
                SuccessRate = sd.SuccessRate,
                AvgExecuteTime = sd.AvgExecuteDuration,
                CreateTime = sd.CreateTime,
                UpdateTime = sd.UpdateTime
            })
            .FirstAsync();

        if (sceneDevice == null)
            throw Oops.Oh("场景设备不存在");

        // 获取最近执行记录
        sceneDevice.RecentExecuteRecords = await _energyControlRep.AsQueryable()
            .Where(u => u.SceneId == sceneDevice.SceneId && u.DeviceId == sceneDevice.DeviceId)
            .Select(u => new SceneDeviceExecuteRecord
            {
                Id = u.Id,
                ControlType = u.ControlType,
                ControlCommand = u.ControlCommand,
                ControlTime = u.ControlTime,
                ExecuteStatus = u.ExecuteStatus,
                ExecuteStatusName = SqlFunc.IIF(u.ExecuteStatus == 0, "执行中", SqlFunc.IIF(u.ExecuteStatus == 1, "成功", SqlFunc.IIF(u.ExecuteStatus == 2, "失败", "超时"))),
                ExecuteDuration = u.ExecuteDuration,
                ErrorMessage = u.ErrorMessage
            })
            .OrderBy(u => u.ControlTime, OrderByType.Desc)
            .Take(10)
            .ToListAsync();

        // 统计今日和本月执行次数
        var today = DateTime.Today;
        var thisMonth = new DateTime(today.Year, today.Month, 1);

        sceneDevice.TodayExecuteCount = await _energyControlRep.CountAsync(u => u.SceneId == sceneDevice.SceneId && u.DeviceId == sceneDevice.DeviceId && u.ControlTime >= today);
        sceneDevice.ThisMonthExecuteCount = await _energyControlRep.CountAsync(u => u.SceneId == sceneDevice.SceneId && u.DeviceId == sceneDevice.DeviceId && u.ControlTime >= thisMonth);
        
        sceneDevice.TodaySuccessCount = await _energyControlRep.CountAsync(u => u.SceneId == sceneDevice.SceneId && u.DeviceId == sceneDevice.DeviceId && u.ControlTime >= today && u.ExecuteStatus == 1);
        sceneDevice.ThisMonthSuccessCount = await _energyControlRep.CountAsync(u => u.SceneId == sceneDevice.SceneId && u.DeviceId == sceneDevice.DeviceId && u.ControlTime >= thisMonth && u.ExecuteStatus == 1);

        return sceneDevice;
    }

    /// <summary>
    /// 增加场景设备 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [DisplayName("增加场景设备")]
    public async Task<long> AddSceneDevice(AddEnergySceneDeviceInput input)
    {
        // 检查场景是否存在
        var sceneExist = await _energySceneRep.IsAnyAsync(u => u.Id == input.SceneId);
        if (!sceneExist)
            throw Oops.Oh("场景不存在");

        // 检查设备是否存在
        var deviceExist = await _energyDeviceRep.IsAnyAsync(u => u.Id == input.DeviceId);
        if (!deviceExist)
            throw Oops.Oh("设备不存在");

        // 检查场景设备是否已存在
        var isExist = await _energySceneDeviceRep.IsAnyAsync(u => u.SceneId == input.SceneId && u.DeviceId == input.DeviceId);
        if (isExist)
            throw Oops.Oh("该设备已添加到场景中");

        var sceneDevice = input.Adapt<EnergySceneDevice>();
        sceneDevice.TenantId = _userManager.TenantId;
        sceneDevice.CreateUserId = _userManager.UserId;
        sceneDevice.CreateTime = DateTime.Now;
        sceneDevice.ExecuteCount = 0;
        sceneDevice.SuccessRate = 0;
        sceneDevice.AvgExecuteDuration = 0;

        var newSceneDevice = await _energySceneDeviceRep.AsInsertable(sceneDevice).ExecuteReturnEntityAsync();
        return newSceneDevice.Id;
    }

    /// <summary>
    /// 批量添加场景设备 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "BatchAdd"), HttpPost]
    [DisplayName("批量添加场景设备")]
    public async Task<int> BatchAddSceneDevice(BatchAddSceneDeviceInput input)
    {
        // 检查场景是否存在
        var sceneExist = await _energySceneRep.IsAnyAsync(u => u.Id == input.SceneId);
        if (!sceneExist)
            throw Oops.Oh("场景不存在");

        if (!input.SceneDevices.Any())
            throw Oops.Oh("设备列表不能为空");

        var deviceIds = input.SceneDevices.Select(x => x.DeviceId).ToList();
        
        // 检查设备是否都存在
        var existDeviceIds = await _energyDeviceRep.AsQueryable()
            .Where(u => deviceIds.Contains(u.Id))
            .Select(u => u.Id)
            .ToListAsync();
        
        var notExistDeviceIds = deviceIds.Except(existDeviceIds).ToList();
        if (notExistDeviceIds.Any())
            throw Oops.Oh($"设备ID {string.Join(",", notExistDeviceIds)} 不存在");

        // 检查是否有重复的场景设备
        var existSceneDeviceIds = await _energySceneDeviceRep.AsQueryable()
            .Where(u => u.SceneId == input.SceneId && deviceIds.Contains(u.DeviceId))
            .Select(u => u.DeviceId)
            .ToListAsync();
        
        if (existSceneDeviceIds.Any())
        {
            var deviceNames = await _energyDeviceRep.AsQueryable()
                .Where(u => existSceneDeviceIds.Contains(u.Id))
                .Select(u => u.DeviceName)
                .ToListAsync();
            throw Oops.Oh($"设备 {string.Join(",", deviceNames)} 已添加到场景中");
        }

        var sceneDevices = input.SceneDevices.Select(item => new EnergySceneDevice
        {
            SceneId = input.SceneId,
            DeviceId = item.DeviceId,
            ControlType = item.ControlType,
            ControlCommand = item.ControlCommand,
            DelayTime = item.ExecuteDelay,
            Sort = item.Sort,
            Status = 1, // 默认启用
            ExecuteCount = 0,
            SuccessRate = 0,
            AvgExecuteDuration = 0,
            TenantId = _userManager.TenantId,
            CreateUserId = _userManager.UserId,
            CreateTime = DateTime.Now
        }).ToList();

        return await _energySceneDeviceRep.AsInsertable(sceneDevices).ExecuteCommandAsync();
    }

    /// <summary>
    /// 更新场景设备 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [DisplayName("更新场景设备")]
    public async Task UpdateSceneDevice(UpdateEnergySceneDeviceInput input)
    {
        // 检查记录是否存在
        var sceneDevice = await _energySceneDeviceRep.GetByIdAsync(input.Id);
        if (sceneDevice == null)
            throw Oops.Oh("场景设备不存在");

        // 检查场景是否存在
        var sceneExist = await _energySceneRep.IsAnyAsync(u => u.Id == input.SceneId);
        if (!sceneExist)
            throw Oops.Oh("场景不存在");

        // 检查设备是否存在
        var deviceExist = await _energyDeviceRep.IsAnyAsync(u => u.Id == input.DeviceId);
        if (!deviceExist)
            throw Oops.Oh("设备不存在");

        // 检查场景设备是否已存在（排除自己）
        var isExist = await _energySceneDeviceRep.IsAnyAsync(u => u.SceneId == input.SceneId && u.DeviceId == input.DeviceId && u.Id != input.Id);
        if (isExist)
            throw Oops.Oh("该设备已添加到场景中");

        var updateSceneDevice = input.Adapt<EnergySceneDevice>();
        updateSceneDevice.UpdateUserId = _userManager.UserId;
        updateSceneDevice.UpdateTime = DateTime.Now;

        await _energySceneDeviceRep.AsUpdateable(updateSceneDevice).ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除场景设备 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    [DisplayName("删除场景设备")]
    public async Task DeleteSceneDevice(DeleteEnergySceneDeviceInput input)
    {
        var sceneDevice = await _energySceneDeviceRep.GetByIdAsync(input.Id);
        if (sceneDevice == null)
            throw Oops.Oh("场景设备不存在");

        await _energySceneDeviceRep.DeleteAsync(sceneDevice);
    }

    /// <summary>
    /// 批量删除场景设备 🔖
    /// </summary>
    /// <param name="sceneId">场景ID</param>
    /// <param name="deviceIds">设备ID列表</param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    [DisplayName("批量删除场景设备")]
    public async Task<int> BatchDeleteSceneDevice(long sceneId, List<long> deviceIds)
    {
        if (!deviceIds.Any())
            throw Oops.Oh("设备ID列表不能为空");

        return await _energySceneDeviceRep.AsDeleteable()
            .Where(u => u.SceneId == sceneId && deviceIds.Contains(u.DeviceId))
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 设置场景设备状态 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "SetStatus"), HttpPost]
    [DisplayName("设置场景设备状态")]
    public async Task SetStatus(EnergySceneDeviceStatusInput input)
    {
        var sceneDevice = await _energySceneDeviceRep.GetByIdAsync(input.Id);
        if (sceneDevice == null)
            throw Oops.Oh("场景设备不存在");

        sceneDevice.Status = input.Status;
        sceneDevice.UpdateUserId = _userManager.UserId;
        sceneDevice.UpdateTime = DateTime.Now;

        await _energySceneDeviceRep.AsUpdateable(sceneDevice)
            .UpdateColumns(u => new { u.Status, u.UpdateUserId, u.UpdateTime })
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 调整场景设备排序 🔖
    /// </summary>
    /// <param name="sceneId">场景ID</param>
    /// <param name="deviceId">设备ID</param>
    /// <param name="sort">新排序</param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "UpdateSort"), HttpPost]
    [DisplayName("调整场景设备排序")]
    public async Task UpdateSort(long sceneId, long deviceId, int sort)
    {
        var sceneDevice = await _energySceneDeviceRep.GetFirstAsync(u => u.SceneId == sceneId && u.DeviceId == deviceId);
        if (sceneDevice == null)
            throw Oops.Oh("场景设备不存在");

        sceneDevice.Sort = sort;
        sceneDevice.UpdateUserId = _userManager.UserId;
        sceneDevice.UpdateTime = DateTime.Now;

        await _energySceneDeviceRep.AsUpdateable(sceneDevice)
            .UpdateColumns(u => new { u.Sort, u.UpdateUserId, u.UpdateTime })
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 复制场景设备到其他场景 🔖
    /// </summary>
    /// <param name="sourceSceneId">源场景ID</param>
    /// <param name="targetSceneId">目标场景ID</param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "CopyToScene"), HttpPost]
    [DisplayName("复制场景设备到其他场景")]
    public async Task<int> CopyToScene(long sourceSceneId, long targetSceneId)
    {
        // 检查源场景和目标场景是否存在
        var sourceSceneExist = await _energySceneRep.IsAnyAsync(u => u.Id == sourceSceneId);
        if (!sourceSceneExist)
            throw Oops.Oh("源场景不存在");

        var targetSceneExist = await _energySceneRep.IsAnyAsync(u => u.Id == targetSceneId);
        if (!targetSceneExist)
            throw Oops.Oh("目标场景不存在");

        // 获取源场景的设备列表
        var sourceSceneDevices = await _energySceneDeviceRep.AsQueryable()
            .Where(u => u.SceneId == sourceSceneId)
            .ToListAsync();

        if (!sourceSceneDevices.Any())
            throw Oops.Oh("源场景没有设备");

        // 获取目标场景已有的设备ID
        var targetDeviceIds = await _energySceneDeviceRep.AsQueryable()
            .Where(u => u.SceneId == targetSceneId)
            .Select(u => u.DeviceId)
            .ToListAsync();

        // 过滤掉已存在的设备
        var newSceneDevices = sourceSceneDevices
            .Where(u => !targetDeviceIds.Contains(u.DeviceId))
            .Select(u => new EnergySceneDevice
            {
                SceneId = targetSceneId,
                DeviceId = u.DeviceId,
                ControlType = u.ControlType,
                ControlCommand = u.ControlCommand,
                DelayTime = u.DelayTime,
                Sort = u.Sort,
                Status = u.Status,
                ExecuteCount = 0,
                SuccessRate = 0,
                AvgExecuteDuration = 0,
                TenantId = _userManager.TenantId,
                CreateUserId = _userManager.UserId,
                CreateTime = DateTime.Now
            })
            .ToList();

        if (!newSceneDevices.Any())
            return 0;

        return await _energySceneDeviceRep.AsInsertable(newSceneDevices).ExecuteCommandAsync();
    }

    /// <summary>
    /// 更新场景设备执行统计 🔖
    /// </summary>
    /// <param name="sceneId">场景ID</param>
    /// <param name="deviceId">设备ID</param>
    /// <param name="isSuccess">是否成功</param>
    /// <param name="duration">执行时长</param>
    /// <returns></returns>
    [DisplayName("更新场景设备执行统计")]
    public async Task UpdateExecuteStats(long sceneId, long deviceId, bool isSuccess, int duration)
    {
        var sceneDevice = await _energySceneDeviceRep.GetFirstAsync(u => u.SceneId == sceneId && u.DeviceId == deviceId);
        if (sceneDevice == null)
            return;

        sceneDevice.ExecuteCount += 1;
        sceneDevice.LastExecuteTime = DateTime.Now;
        
        // 重新计算成功率
        var totalExecuteCount = await _energyControlRep.CountAsync(u => u.SceneId == sceneId && u.DeviceId == deviceId);
        var successExecuteCount = await _energyControlRep.CountAsync(u => u.SceneId == sceneId && u.DeviceId == deviceId && u.ExecuteStatus == 1);
        sceneDevice.SuccessRate = totalExecuteCount > 0 ? (decimal)successExecuteCount / totalExecuteCount * 100 : 0;
        
        // 重新计算平均执行时长
        var avgDuration = await _energyControlRep.AsQueryable()
            .Where(u => u.SceneId == sceneId && u.DeviceId == deviceId && u.ExecuteDuration > 0)
            .Select(u => u.ExecuteDuration)
            .ToListAsync();
        sceneDevice.AvgExecuteDuration = avgDuration.Any() ? (int)avgDuration.Average() : 0;
        
        sceneDevice.UpdateTime = DateTime.Now;

        await _energySceneDeviceRep.AsUpdateable(sceneDevice)
            .UpdateColumns(u => new { u.ExecuteCount, u.LastExecuteTime, u.SuccessRate, u.AvgExecuteDuration, u.UpdateTime })
            .ExecuteCommandAsync();
    }
}