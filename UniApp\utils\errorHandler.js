/**
 * 统一错误处理工具
 */

// 错误类型枚举
export const ERROR_TYPES = {
  NETWORK: 'NETWORK_ERROR',
  API: 'API_ERROR',
  TIMEOUT: 'TIMEOUT_ERROR',
  AUTH: 'AUTH_ERROR',
  VALIDATION: 'VALIDATION_ERROR',
  UNKNOWN: 'UNKNOWN_ERROR'
}

// 错误级别枚举
export const ERROR_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
}

/**
 * 错误处理器类
 */
class ErrorHandler {
  constructor() {
    this.errorQueue = []
    this.maxQueueSize = 50
    this.retryAttempts = new Map()
    this.maxRetryAttempts = 3
    this.retryDelay = 1000
    this.initialized = false
  }

  /**
   * 初始化错误处理器
   * 设置全局错误监听器和配置
   */
  initialize() {
    if (this.initialized) {
      console.warn('ErrorHandler already initialized')
      return
    }

    try {
      // 设置全局错误监听器
      if (typeof uni !== 'undefined') {
        // UniApp环境下的错误监听
        uni.onError && uni.onError((error) => {
          this.handleError(new Error(error), { source: 'uni.onError' })
        })
      }

      // 设置Promise未捕获错误监听
      if (typeof window !== 'undefined' && window.addEventListener) {
        window.addEventListener('unhandledrejection', (event) => {
          this.handleError(event.reason, { source: 'unhandledrejection' })
        })
      }

      this.initialized = true
      console.log('ErrorHandler initialized successfully at', new Date().toLocaleString())
    } catch (error) {
      console.error('Failed to initialize ErrorHandler:', error)
    }
  }

  /**
   * 处理错误
   * @param {Error} error 错误对象
   * @param {Object} context 错误上下文
   * @param {Object} options 处理选项
   */
  handleError(error, context = {}, options = {}) {
    const errorInfo = this.parseError(error, context)
    
    // 记录错误
    this.logError(errorInfo)
    
    // 显示用户友好的错误提示
    if (options.showToast !== false) {
      this.showErrorToast(errorInfo)
    }
    
    // 根据错误类型执行特定处理
    this.executeErrorHandling(errorInfo, options)
    
    return errorInfo
  }

  /**
   * 解析错误信息
   * @param {Error} error 错误对象
   * @param {Object} context 错误上下文
   */
  parseError(error, context) {
    const errorInfo = {
      id: this.generateErrorId(),
      timestamp: new Date().toISOString(),
      message: error.message || '未知错误',
      stack: error.stack,
      type: this.determineErrorType(error),
      level: this.determineErrorLevel(error),
      context: context,
      userAgent: this.getUserAgent(),
      url: context.url || '',
      method: context.method || '',
      params: context.params || {}
    }

    return errorInfo
  }

  /**
   * 确定错误类型
   * @param {Error} error 错误对象
   */
  determineErrorType(error) {
    const message = error.message?.toLowerCase() || ''
    
    if (message.includes('network') || message.includes('连接') || message.includes('timeout')) {
      return ERROR_TYPES.NETWORK
    }
    
    if (error.code === 401 || message.includes('unauthorized') || message.includes('登录')) {
      return ERROR_TYPES.AUTH
    }
    
    if (error.code >= 400 && error.code < 500) {
      return ERROR_TYPES.VALIDATION
    }
    
    if (error.code >= 500) {
      return ERROR_TYPES.API
    }
    
    if (message.includes('timeout')) {
      return ERROR_TYPES.TIMEOUT
    }
    
    return ERROR_TYPES.UNKNOWN
  }

  /**
   * 确定错误级别
   * @param {Error} error 错误对象
   */
  determineErrorLevel(error) {
    const type = this.determineErrorType(error)
    
    switch (type) {
      case ERROR_TYPES.AUTH:
      case ERROR_TYPES.CRITICAL:
        return ERROR_LEVELS.CRITICAL
      case ERROR_TYPES.API:
        return ERROR_LEVELS.HIGH
      case ERROR_TYPES.NETWORK:
      case ERROR_TYPES.TIMEOUT:
        return ERROR_LEVELS.MEDIUM
      default:
        return ERROR_LEVELS.LOW
    }
  }

  /**
   * 记录错误
   * @param {Error} error 错误对象
   * @param {Object} context 错误上下文
   * @param {string} level 错误级别
   */
  recordError(error, context = {}, level = ERROR_LEVELS.MEDIUM) {
    const errorInfo = this.parseError(error, context)
    const errorRecord = {
      id: this.generateErrorId(),
      ...errorInfo,
      context,
      level,
      timestamp: Date.now(),
      userAgent: this.getUserAgent()
    }
    
    // 添加到错误队列
    this.errorQueue.push(errorRecord)
    
    // 控制台输出
    console.error('错误记录:', errorRecord)
    
    // 存储到本地
    this.saveErrorToStorage(errorRecord)
    
    return errorRecord
  }

  /**
   * 记录错误
   * @param {Object} errorInfo 错误信息
   */
  logError(errorInfo) {
    // 添加到错误队列
    this.errorQueue.push(errorInfo)
    
    // 限制队列大小
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift()
    }
    
    // 控制台输出
    console.error('ErrorHandler:', errorInfo)
    
    // 存储到本地（用于离线分析）
    try {
      const storedErrors = uni.getStorageSync('error_logs') || []
      storedErrors.push(errorInfo)
      
      // 只保留最近100条错误
      if (storedErrors.length > 100) {
        storedErrors.splice(0, storedErrors.length - 100)
      }
      
      uni.setStorageSync('error_logs', storedErrors)
    } catch (e) {
      console.error('存储错误日志失败:', e)
    }
  }

  /**
   * 保存错误到本地存储
   * @param {Object} errorRecord 错误记录
   */
  saveErrorToStorage(errorRecord) {
    try {
      const storedErrors = uni.getStorageSync('error_logs') || []
      storedErrors.push(errorRecord)
      
      // 只保留最近100条错误
      if (storedErrors.length > 100) {
        storedErrors.splice(0, storedErrors.length - 100)
      }
      
      uni.setStorageSync('error_logs', storedErrors)
    } catch (e) {
      console.error('存储错误日志失败:', e)
    }
  }

  /**
   * 显示错误提示
   * @param {Object} errorInfo 错误信息
   */
  showErrorToast(errorInfo) {
    const userFriendlyMessage = this.getUserFriendlyMessage(errorInfo)
    
    uni.showToast({
      title: userFriendlyMessage,
      icon: 'none',
      duration: 3000,
      mask: false
    })
  }

  /**
   * 获取用户友好的错误消息
   * @param {Object} errorInfo 错误信息
   */
  getUserFriendlyMessage(errorInfo) {
    switch (errorInfo.type) {
      case ERROR_TYPES.NETWORK:
        return '网络连接异常，请检查网络设置'
      case ERROR_TYPES.TIMEOUT:
        return '请求超时，请稍后重试'
      case ERROR_TYPES.AUTH:
        return '登录已过期，请重新登录'
      case ERROR_TYPES.API:
        return '服务器异常，请稍后重试'
      case ERROR_TYPES.VALIDATION:
        return errorInfo.message || '请求参数错误'
      default:
        return '操作失败，请稍后重试'
    }
  }

  /**
   * 执行错误处理
   * @param {Object} errorInfo 错误信息
   * @param {Object} options 处理选项
   */
  executeErrorHandling(errorInfo, options) {
    switch (errorInfo.type) {
      case ERROR_TYPES.AUTH:
        this.handleAuthError(errorInfo)
        break
      case ERROR_TYPES.NETWORK:
      case ERROR_TYPES.TIMEOUT:
        this.handleNetworkError(errorInfo, options)
        break
      case ERROR_TYPES.API:
        this.handleApiError(errorInfo, options)
        break
    }
  }

  /**
   * 处理认证错误
   * @param {Object} errorInfo 错误信息
   */
  handleAuthError(errorInfo) {
    console.log('处理认证错误:', errorInfo.message)
    // 清除用户信息
    try {
      uni.removeStorageSync('token')
      uni.removeStorageSync('userInfo')
      
      // 跳转到登录页
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/user/login'
        })
      }, 1500)
    } catch (e) {
      console.error('处理认证错误失败:', e)
    }
  }

  /**
   * 处理网络错误
   * @param {Object} errorInfo 错误信息
   * @param {Object} options 处理选项
   */
  handleNetworkError(errorInfo, options = {}) {
    console.log('处理网络错误:', errorInfo.message)
    if (options.enableRetry !== false) {
      this.scheduleRetry(errorInfo, options)
    }
  }

  /**
   * 处理API错误
   * @param {Object} errorInfo 错误信息
   * @param {Object} options 处理选项
   */
  handleApiError(errorInfo, options = {}) {
    console.log('处理API错误:', errorInfo.message, '状态码:', errorInfo.context?.status)
    // 对于5xx错误，可以尝试重试
    if (errorInfo.context && errorInfo.context.status >= 500 && options.enableRetry !== false) {
      this.scheduleRetry(errorInfo, options)
    }
  }

  /**
   * 处理业务错误
   * @param {Object} errorInfo 错误信息
   * @param {Object} options 处理选项
   */
  handleBusinessError(errorInfo, options = {}) {
    // 对于业务错误，通常不需要重试，只记录日志
    console.warn('业务错误:', errorInfo.message, '位置:', errorInfo.context?.url || '未知')
    
    // 如果是认证错误，执行特殊处理
    if (errorInfo.type === ERROR_TYPES.AUTH) {
      this.handleAuthError(errorInfo)
    }
    
    // 使用options中的配置
    if (options.showToast !== false) {
      console.log('显示业务错误提示')
    }
  }

  /**
   * 安排重试
   * @param {Object} errorInfo 错误信息
   * @param {Object} options 处理选项
   */
  scheduleRetry(errorInfo, options) {
    // 确保 context 存在且包含必要的信息
    if (!errorInfo.context || !errorInfo.context.url) {
      console.warn('无法安排重试：缺少必要的上下文信息')
      return
    }
    
    const retryKey = `${errorInfo.context.url}_${errorInfo.context.method || 'GET'}`
    const currentAttempts = this.retryAttempts.get(retryKey) || 0
    
    if (currentAttempts < this.maxRetryAttempts && options.retryCallback) {
      this.retryAttempts.set(retryKey, currentAttempts + 1)
      
      const delay = this.retryDelay * Math.pow(2, currentAttempts) // 指数退避
      
      setTimeout(() => {
        console.log(`重试第${currentAttempts + 1}次:`, retryKey)
        options.retryCallback()
      }, delay)
    } else {
      // 达到最大重试次数，清除重试记录
      this.retryAttempts.delete(retryKey)
    }
  }

  /**
   * 生成错误ID
   */
  generateErrorId() {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 获取用户代理信息
   */
  getUserAgent() {
    try {
      // 在UniApp环境下，navigator可能不存在
      // #ifdef H5
      if (typeof navigator !== 'undefined' && navigator && navigator.userAgent) {
        return navigator.userAgent
      }
      // #endif
      
      // #ifndef H5
      // 非H5环境下，navigator不存在，直接使用UniApp系统信息
      // #endif
      
      // UniApp环境下获取系统信息
      const systemInfo = uni.getSystemInfoSync()
      return `${systemInfo.platform} ${systemInfo.system} ${systemInfo.version}`
    } catch (err) {
      return 'Unknown'
    }
  }

  /**
   * 获取错误统计
   */
  getErrorStats() {
    const stats = {
      total: this.errorQueue.length,
      byType: {},
      byLevel: {},
      recent: this.errorQueue.slice(-10)
    }

    this.errorQueue.forEach(error => {
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1
      stats.byLevel[error.level] = (stats.byLevel[error.level] || 0) + 1
    })

    return stats
  }

  /**
   * 清除错误记录
   */
  clearErrors() {
    this.errorQueue = []
    this.retryAttempts.clear()
    
    try {
      uni.removeStorageSync('error_logs')
    } catch (e) {
      console.error('清除错误日志失败:', e)
    }
  }

  /**
   * 导出错误日志
   */
  exportErrorLogs() {
    try {
      const storedErrors = uni.getStorageSync('error_logs') || []
      const exportData = {
        timestamp: new Date().toISOString(),
        errors: storedErrors,
        stats: this.getErrorStats()
      }
      
      return JSON.stringify(exportData, null, 2)
    } catch (e) {
      console.error('导出错误日志失败:', e)
      return null
    }
  }
}

// 创建全局错误处理器实例
const errorHandler = new ErrorHandler()

// 导出错误处理器实例
export default errorHandler

// 导出便捷函数
export const handleError = (error, context, options) => errorHandler.handleError(error, context, options)
export const handleNetworkError = (errorInfo, options) => errorHandler.handleNetworkError(errorInfo, options)
export const handleBusinessError = (errorInfo, options) => errorHandler.handleBusinessError(errorInfo, options)
export const handleCriticalError = (error, context, options = {}) => {
  // 关键错误处理，强制显示错误提示且不允许重试
  return errorHandler.handleError(error, context, {
    ...options,
    showToast: true,
    enableRetry: false,
    level: ERROR_LEVELS.CRITICAL
  })
}
export const clearErrorDeduplicationCache = () => {
  errorHandler.errorQueue = []
  errorHandler.retryAttempts.clear()
  console.log('Error deduplication cache cleared')
}
export const withRetry = (apiCall, context = {}, options = {}) => {
  return new Promise((resolve, reject) => {
    const executeCall = async () => {
      try {
        const result = await apiCall()
        
        // 成功后清除重试记录
        const retryKey = `${context.url}_${context.method}`
        errorHandler.retryAttempts.delete(retryKey)
        
        resolve(result)
      } catch (error) {
        const errorInfo = handleError(error, context, {
          ...options,
          retryCallback: executeCall,
          showToast: false // 第一次失败不显示toast
        })
        
        // 如果没有重试或重试失败，则reject
        const retryKey = `${context.url}_${context.method}`
        const attempts = errorHandler.retryAttempts.get(retryKey) || 0
        
        if (attempts >= errorHandler.maxRetryAttempts) {
          // 显示最终错误提示
          errorHandler.showErrorToast(errorInfo)
          reject(error)
        }
      }
    }
    
    executeCall()
  })
}

console.log('ErrorHandler 初始化于 ' + new Date().toLocaleTimeString() + ' GMT+0800 (CST)，位置：utils/errorHandler.js:425')
