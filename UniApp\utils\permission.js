/**
 * 权限验证工具
 * 提供用户权限检查和路由守卫功能
 */

import { userStorage } from './storage.js'

/**
 * 权限常量定义
 */
export const PERMISSIONS = {
  // 设备管理权限
  DEVICE_VIEW: 'device:view',
  DEVICE_ADD: 'device:add',
  DEVICE_EDIT: 'device:edit',
  DEVICE_DELETE: 'device:delete',
  DEVICE_CONTROL: 'device:control',
  
  // 照明控制权限
  LIGHTING_VIEW: 'lighting:view',
  LIGHTING_CONTROL: 'lighting:control',
  LIGHTING_SCENE: 'lighting:scene',
  LIGHTING_SCHEDULE: 'lighting:schedule',
  
  // 能耗监控权限
  ENERGY_VIEW: 'energy:view',
  ENERGY_EXPORT: 'energy:export',
  ENERGY_REPORT: 'energy:report',
  
  // 故障管理权限
  FAULT_VIEW: 'fault:view',
  FAULT_REPORT: 'fault:report',
  FAULT_HANDLE: 'fault:handle',
  FAULT_DELETE: 'fault:delete',
  
  // 用户管理权限
  USER_VIEW: 'user:view',
  USER_ADD: 'user:add',
  USER_EDIT: 'user:edit',
  USER_DELETE: 'user:delete',
  
  // 系统管理权限
  SYSTEM_CONFIG: 'system:config',
  SYSTEM_LOG: 'system:log',
  SYSTEM_BACKUP: 'system:backup'
}

/**
 * 角色常量定义
 */
export const ROLES = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin',
  MANAGER: 'manager',
  OPERATOR: 'operator',
  VIEWER: 'viewer'
}

/**
 * 角色权限映射
 */
export const ROLE_PERMISSIONS = {
  [ROLES.SUPER_ADMIN]: Object.values(PERMISSIONS),
  [ROLES.ADMIN]: [
    PERMISSIONS.DEVICE_VIEW,
    PERMISSIONS.DEVICE_ADD,
    PERMISSIONS.DEVICE_EDIT,
    PERMISSIONS.DEVICE_DELETE,
    PERMISSIONS.DEVICE_CONTROL,
    PERMISSIONS.LIGHTING_VIEW,
    PERMISSIONS.LIGHTING_CONTROL,
    PERMISSIONS.LIGHTING_SCENE,
    PERMISSIONS.LIGHTING_SCHEDULE,
    PERMISSIONS.ENERGY_VIEW,
    PERMISSIONS.ENERGY_EXPORT,
    PERMISSIONS.ENERGY_REPORT,
    PERMISSIONS.FAULT_VIEW,
    PERMISSIONS.FAULT_REPORT,
    PERMISSIONS.FAULT_HANDLE,
    PERMISSIONS.USER_VIEW,
    PERMISSIONS.USER_ADD,
    PERMISSIONS.USER_EDIT
  ],
  [ROLES.MANAGER]: [
    PERMISSIONS.DEVICE_VIEW,
    PERMISSIONS.DEVICE_EDIT,
    PERMISSIONS.DEVICE_CONTROL,
    PERMISSIONS.LIGHTING_VIEW,
    PERMISSIONS.LIGHTING_CONTROL,
    PERMISSIONS.LIGHTING_SCENE,
    PERMISSIONS.LIGHTING_SCHEDULE,
    PERMISSIONS.ENERGY_VIEW,
    PERMISSIONS.ENERGY_EXPORT,
    PERMISSIONS.FAULT_VIEW,
    PERMISSIONS.FAULT_REPORT,
    PERMISSIONS.FAULT_HANDLE,
    PERMISSIONS.USER_VIEW
  ],
  [ROLES.OPERATOR]: [
    PERMISSIONS.DEVICE_VIEW,
    PERMISSIONS.DEVICE_CONTROL,
    PERMISSIONS.LIGHTING_VIEW,
    PERMISSIONS.LIGHTING_CONTROL,
    PERMISSIONS.ENERGY_VIEW,
    PERMISSIONS.FAULT_VIEW,
    PERMISSIONS.FAULT_REPORT
  ],
  [ROLES.VIEWER]: [
    PERMISSIONS.DEVICE_VIEW,
    PERMISSIONS.LIGHTING_VIEW,
    PERMISSIONS.ENERGY_VIEW,
    PERMISSIONS.FAULT_VIEW
  ]
}

/**
 * 权限验证类
 */
export class PermissionChecker {
  constructor() {
    this.userInfo = null
    this.userPermissions = []
    this.userRoles = []
    this.init()
  }

  /**
   * 初始化权限信息
   */
  init() {
    this.userInfo = userStorage.getUserInfo()
    if (this.userInfo) {
      this.userRoles = this.userInfo.roles || []
      this.userPermissions = this.getUserPermissions()
    }
  }

  /**
   * 获取用户权限列表
   * @returns {array} 权限列表
   */
  getUserPermissions() {
    const permissions = new Set()
    
    // 从角色获取权限
    this.userRoles.forEach(role => {
      const rolePermissions = ROLE_PERMISSIONS[role] || []
      rolePermissions.forEach(permission => permissions.add(permission))
    })
    
    // 添加用户特定权限
    if (this.userInfo.permissions) {
      this.userInfo.permissions.forEach(permission => permissions.add(permission))
    }
    
    return Array.from(permissions)
  }

  /**
   * 检查是否有指定权限
   * @param {string|array} permission 权限或权限数组
   * @returns {boolean} 是否有权限
   */
  hasPermission(permission) {
    if (!this.userInfo) return false
    
    // 超级管理员拥有所有权限
    if (this.userRoles.includes(ROLES.SUPER_ADMIN)) return true
    
    if (Array.isArray(permission)) {
      // 检查是否拥有数组中的任一权限
      return permission.some(p => this.userPermissions.includes(p))
    } else {
      // 检查单个权限
      return this.userPermissions.includes(permission)
    }
  }

  /**
   * 检查是否拥有所有指定权限
   * @param {array} permissions 权限数组
   * @returns {boolean} 是否拥有所有权限
   */
  hasAllPermissions(permissions) {
    if (!this.userInfo || !Array.isArray(permissions)) return false
    
    // 超级管理员拥有所有权限
    if (this.userRoles.includes(ROLES.SUPER_ADMIN)) return true
    
    return permissions.every(permission => this.userPermissions.includes(permission))
  }

  /**
   * 检查是否有指定角色
   * @param {string|array} role 角色或角色数组
   * @returns {boolean} 是否有角色
   */
  hasRole(role) {
    if (!this.userInfo) return false
    
    if (Array.isArray(role)) {
      return role.some(r => this.userRoles.includes(r))
    } else {
      return this.userRoles.includes(role)
    }
  }

  /**
   * 检查是否为管理员
   * @returns {boolean} 是否为管理员
   */
  isAdmin() {
    return this.hasRole([ROLES.SUPER_ADMIN, ROLES.ADMIN])
  }

  /**
   * 检查是否已登录
   * @returns {boolean} 是否已登录
   */
  isLoggedIn() {
    return !!this.userInfo && !!userStorage.getToken()
  }

  /**
   * 更新权限信息
   */
  refresh() {
    this.init()
  }

  /**
   * 清除权限信息
   */
  clear() {
    this.userInfo = null
    this.userPermissions = []
    this.userRoles = []
  }
}

// 创建全局权限检查器实例
export const permissionChecker = new PermissionChecker()

/**
 * 权限指令
 * 用于在模板中进行权限控制
 */
export const permissionDirective = {
  /**
   * 检查权限
   * @param {string|array} permission 权限
   * @returns {boolean} 是否有权限
   */
  check(permission) {
    return permissionChecker.hasPermission(permission)
  },

  /**
   * 检查角色
   * @param {string|array} role 角色
   * @returns {boolean} 是否有角色
   */
  checkRole(role) {
    return permissionChecker.hasRole(role)
  }
}

/**
 * 路由守卫
 */
export const routeGuard = {
  /**
   * 需要登录的页面
   */
  authRequired: [
    '/pages/device/list',
    '/pages/device/detail',
    '/pages/lighting/control',
    '/pages/lighting/schedule',
    '/pages/lighting/scene',
    '/pages/energy/monitor',
    '/pages/energy/statistics',
    '/pages/fault/list',
    '/pages/fault/detail',
    '/pages/user/profile'
  ],

  /**
   * 页面权限配置
   */
  pagePermissions: {
    '/pages/device/list': [PERMISSIONS.DEVICE_VIEW],
    '/pages/device/detail': [PERMISSIONS.DEVICE_VIEW],
    '/pages/lighting/control': [PERMISSIONS.LIGHTING_CONTROL],
    '/pages/lighting/schedule': [PERMISSIONS.LIGHTING_SCHEDULE],
    '/pages/lighting/scene': [PERMISSIONS.LIGHTING_SCENE],
    '/pages/energy/monitor': [PERMISSIONS.ENERGY_VIEW],
    '/pages/energy/statistics': [PERMISSIONS.ENERGY_VIEW],
    '/pages/fault/list': [PERMISSIONS.FAULT_VIEW],
    '/pages/fault/detail': [PERMISSIONS.FAULT_VIEW]
  },

  /**
   * 检查页面访问权限
   * @param {string} path 页面路径
   * @returns {object} 检查结果
   */
  checkPageAccess(path) {
    // 检查是否需要登录
    if (this.authRequired.includes(path)) {
      if (!permissionChecker.isLoggedIn()) {
        return {
          allowed: false,
          reason: 'login_required',
          redirectTo: '/pages/user/login'
        }
      }
    }

    // 检查页面权限
    const requiredPermissions = this.pagePermissions[path]
    if (requiredPermissions && requiredPermissions.length > 0) {
      if (!permissionChecker.hasPermission(requiredPermissions)) {
        return {
          allowed: false,
          reason: 'permission_denied',
          redirectTo: '/pages/index/index'
        }
      }
    }

    return { allowed: true }
  }
}

/**
 * 便捷的权限检查方法
 */
export const checkPermission = (permission) => {
  return permissionChecker.hasPermission(permission)
}

export const checkRole = (role) => {
  return permissionChecker.hasRole(role)
}

export const isLoggedIn = () => {
  return permissionChecker.isLoggedIn()
}

export const isAdmin = () => {
  return permissionChecker.isAdmin()
}

/**
 * 权限装饰器
 * 用于方法级别的权限控制
 */
export function requirePermission(permission) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = function(...args) {
      if (!checkPermission(permission)) {
        console.warn(`权限不足，需要权限: ${permission}`)
        uni.showToast({
          title: '权限不足',
          icon: 'none'
        })
        return
      }
      return originalMethod.apply(this, args)
    }
    
    return descriptor
  }
}

/**
 * 角色装饰器
 * 用于方法级别的角色控制
 */
export function requireRole(role) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = function(...args) {
      if (!checkRole(role)) {
        console.warn(`角色不足，需要角色: ${role}`)
        uni.showToast({
          title: '权限不足',
          icon: 'none'
        })
        return
      }
      return originalMethod.apply(this, args)
    }
    
    return descriptor
  }
}

export default {
  PERMISSIONS,
  ROLES,
  ROLE_PERMISSIONS,
  PermissionChecker,
  permissionChecker,
  permissionDirective,
  routeGuard,
  checkPermission,
  checkRole,
  isLoggedIn,
  isAdmin,
  requirePermission,
  requireRole
}