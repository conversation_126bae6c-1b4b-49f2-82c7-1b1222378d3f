/**
 * 表单验证工具
 * 提供常用的验证规则和验证方法
 */

/**
 * 正则表达式常量
 */
export const REGEX = {
  // 手机号
  MOBILE: /^1[3-9]\d{9}$/,
  // 电话号码
  phone: /^1[3-9]\d{9}$/,
  // 邮箱
  EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  // 身份证号
  ID_CARD: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
  // 密码(8-20位，包含字母和数字)
  PASSWORD: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,20}$/,
  // 用户名(4-20位字母数字下划线)
  USERNAME: /^[a-zA-Z0-9_]{4,20}$/,
  // IP地址
  IP: /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
  // URL
  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)$/,
  // 中文
  CHINESE: /^[\u4e00-\u9fa5]+$/,
  // 数字
  NUMBER: /^\d+$/,
  // 小数
  DECIMAL: /^\d+(\.\d+)?$/,
  // 正整数
  POSITIVE_INTEGER: /^[1-9]\d*$/,
  // 非负整数
  NON_NEGATIVE_INTEGER: /^\d+$/
}

/**
 * 基础验证方法
 */
export const validators = {
  /**
   * 验证是否为空
   * @param {any} value 待验证值
   * @returns {boolean} 验证结果
   */
  isEmpty(value) {
    if (value === null || value === undefined) return true
    if (typeof value === 'string') return value.trim() === ''
    if (Array.isArray(value)) return value.length === 0
    if (typeof value === 'object') return Object.keys(value).length === 0
    return false
  },

  /**
   * 验证是否非空
   * @param {any} value 待验证值
   * @returns {boolean} 验证结果
   */
  isNotEmpty(value) {
    return !this.isEmpty(value)
  },

  /**
   * 验证字符串长度
   * @param {string} value 待验证值
   * @param {number} min 最小长度
   * @param {number} max 最大长度
   * @returns {boolean} 验证结果
   */
  isLength(value, min = 0, max = Infinity) {
    if (typeof value !== 'string') return false
    const length = value.length
    return length >= min && length <= max
  },

  /**
   * 验证手机号
   * @param {string} value 待验证值
   * @returns {boolean} 验证结果
   */
  isMobile(value) {
    return REGEX.MOBILE.test(value)
  },

  /**
   * 验证邮箱
   * @param {string} value 待验证值
   * @returns {boolean} 验证结果
   */
  isEmail(value) {
    return REGEX.EMAIL.test(value)
  },

  /**
   * 验证身份证号
   * @param {string} value 待验证值
   * @returns {boolean} 验证结果
   */
  isIdCard(value) {
    return REGEX.ID_CARD.test(value)
  },

  /**
   * 验证密码强度
   * @param {string} value 待验证值
   * @returns {boolean} 验证结果
   */
  isPassword(value) {
    return REGEX.PASSWORD.test(value)
  },

  /**
   * 验证用户名
   * @param {string} value 待验证值
   * @returns {boolean} 验证结果
   */
  isUsername(value) {
    return REGEX.USERNAME.test(value)
  },

  /**
   * 验证IP地址
   * @param {string} value 待验证值
   * @returns {boolean} 验证结果
   */
  isIP(value) {
    return REGEX.IP.test(value)
  },

  /**
   * 验证URL
   * @param {string} value 待验证值
   * @returns {boolean} 验证结果
   */
  isURL(value) {
    return REGEX.URL.test(value)
  },

  /**
   * 验证数字
   * @param {any} value 待验证值
   * @returns {boolean} 验证结果
   */
  isNumber(value) {
    return !isNaN(Number(value)) && isFinite(Number(value))
  },

  /**
   * 验证正整数
   * @param {any} value 待验证值
   * @returns {boolean} 验证结果
   */
  isPositiveInteger(value) {
    return REGEX.POSITIVE_INTEGER.test(String(value))
  },

  /**
   * 验证数值范围
   * @param {number} value 待验证值
   * @param {number} min 最小值
   * @param {number} max 最大值
   * @returns {boolean} 验证结果
   */
  isInRange(value, min = -Infinity, max = Infinity) {
    const num = Number(value)
    return !isNaN(num) && num >= min && num <= max
  }
}

/**
 * 验证规则配置
 */
export const rules = {
  // 必填
  required: {
    validator: validators.isNotEmpty,
    message: '此字段为必填项'
  },

  // 手机号
  mobile: {
    validator: validators.isMobile,
    message: '请输入正确的手机号码'
  },

  // 邮箱
  email: {
    validator: validators.isEmail,
    message: '请输入正确的邮箱地址'
  },

  // 密码
  password: {
    validator: validators.isPassword,
    message: '密码必须包含字母和数字，长度8-20位'
  },

  // 用户名
  username: {
    validator: validators.isUsername,
    message: '用户名只能包含字母、数字和下划线，长度4-20位'
  },

  // IP地址
  ip: {
    validator: validators.isIP,
    message: '请输入正确的IP地址'
  },

  // URL
  url: {
    validator: validators.isURL,
    message: '请输入正确的URL地址'
  }
}

/**
 * 表单验证器类
 */
export class FormValidator {
  constructor() {
    this.rules = {}
    this.errors = {}
  }

  /**
   * 添加验证规则
   * @param {string} field 字段名
   * @param {array} fieldRules 验证规则数组
   */
  addRule(field, fieldRules) {
    this.rules[field] = fieldRules
  }

  /**
   * 验证单个字段
   * @param {string} field 字段名
   * @param {any} value 字段值
   * @returns {boolean} 验证结果
   */
  validateField(field, value) {
    const fieldRules = this.rules[field]
    if (!fieldRules) return true

    delete this.errors[field]

    for (const rule of fieldRules) {
      let isValid = false
      let message = '验证失败'

      if (typeof rule === 'function') {
        isValid = rule(value)
      } else if (typeof rule === 'object') {
        if (rule.validator) {
          isValid = rule.validator(value)
          message = rule.message || message
        }
      }

      if (!isValid) {
        this.errors[field] = message
        return false
      }
    }

    return true
  }

  /**
   * 验证所有字段
   * @param {object} data 表单数据
   * @returns {boolean} 验证结果
   */
  validate(data) {
    this.errors = {}
    let isValid = true

    for (const field in this.rules) {
      const fieldValue = data[field]
      if (!this.validateField(field, fieldValue)) {
        isValid = false
      }
    }

    return isValid
  }

  /**
   * 获取验证错误
   * @returns {object} 错误信息对象
   */
  getErrors() {
    return this.errors
  }

  /**
   * 获取字段错误
   * @param {string} field 字段名
   * @returns {string|null} 错误信息
   */
  getFieldError(field) {
    return this.errors[field] || null
  }

  /**
   * 清除错误
   * @param {string} field 字段名，不传则清除所有错误
   */
  clearErrors(field) {
    if (field) {
      delete this.errors[field]
    } else {
      this.errors = {}
    }
  }
}

/**
 * 快速验证方法
 * @param {object} data 表单数据
 * @param {object} ruleConfig 验证规则配置
 * @returns {object} 验证结果 { isValid, errors }
 */
export function validate(data, ruleConfig) {
  const validator = new FormValidator()
  
  // 添加验证规则
  for (const field in ruleConfig) {
    validator.addRule(field, ruleConfig[field])
  }
  
  // 执行验证
  const isValid = validator.validate(data)
  const errors = validator.getErrors()
  
  return { isValid, errors }
}

/**
 * 常用验证规则组合
 */
export const commonRules = {
  // 登录表单
  loginForm: {
    username: [rules.required, rules.username],
    password: [rules.required]
  },

  // 注册表单
  registerForm: {
    username: [rules.required, rules.username],
    password: [rules.required, rules.password],
    email: [rules.required, rules.email],
    mobile: [rules.required, rules.mobile]
  },

  // 设备添加表单
  deviceForm: {
    name: [rules.required, {
      validator: (value) => validators.isLength(value, 2, 50),
      message: '设备名称长度应在2-50个字符之间'
    }],
    ip: [rules.required, rules.ip],
    location: [rules.required]
  },

  // 用户信息表单
  userInfoForm: {
    realName: [rules.required, {
      validator: (value) => validators.isLength(value, 2, 20),
      message: '真实姓名长度应在2-20个字符之间'
    }],
    email: [rules.email],
    mobile: [rules.mobile]
  }
}

export default {
  REGEX,
  validators,
  rules,
  FormValidator,
  validate,
  commonRules
}