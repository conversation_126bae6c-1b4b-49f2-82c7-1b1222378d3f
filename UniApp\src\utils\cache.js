/**
 * 数据缓存工具类
 * 提供本地存储、内存缓存和离线数据处理功能
 */

class CacheManager {
  constructor() {
    this.memoryCache = new Map()
    this.cachePrefix = 'energy_light_'
    this.defaultExpireTime = 5 * 60 * 1000 // 5分钟默认过期时间
  }

  /**
   * 生成缓存键
   * @param {string} key 原始键
   * @returns {string} 带前缀的缓存键
   */
  getCacheKey(key) {
    return `${this.cachePrefix}${key}`
  }

  /**
   * 设置内存缓存
   * @param {string} key 缓存键
   * @param {any} data 缓存数据
   * @param {number} expireTime 过期时间（毫秒）
   */
  setMemoryCache(key, data, expireTime = this.defaultExpireTime) {
    const cacheData = {
      data,
      timestamp: Date.now(),
      expireTime
    }
    this.memoryCache.set(key, cacheData)
  }

  /**
   * 获取内存缓存
   * @param {string} key 缓存键
   * @returns {any|null} 缓存数据或null
   */
  getMemoryCache(key) {
    const cacheData = this.memoryCache.get(key)
    if (!cacheData) return null

    const { data, timestamp, expireTime } = cacheData
    const now = Date.now()

    // 检查是否过期
    if (now - timestamp > expireTime) {
      this.memoryCache.delete(key)
      return null
    }

    return data
  }

  /**
   * 设置本地存储缓存
   * @param {string} key 缓存键
   * @param {any} data 缓存数据
   * @param {number} expireTime 过期时间（毫秒）
   */
  setLocalCache(key, data, expireTime = this.defaultExpireTime) {
    try {
      const cacheData = {
        data,
        timestamp: Date.now(),
        expireTime
      }
      const cacheKey = this.getCacheKey(key)
      uni.setStorageSync(cacheKey, JSON.stringify(cacheData))
    } catch (error) {
      console.error('设置本地缓存失败:', error)
    }
  }

  /**
   * 获取本地存储缓存
   * @param {string} key 缓存键
   * @returns {any|null} 缓存数据或null
   */
  getLocalCache(key) {
    try {
      const cacheKey = this.getCacheKey(key)
      const cacheStr = uni.getStorageSync(cacheKey)
      if (!cacheStr) return null

      const cacheData = JSON.parse(cacheStr)
      const { data, timestamp, expireTime } = cacheData
      const now = Date.now()

      // 检查是否过期
      if (now - timestamp > expireTime) {
        this.removeLocalCache(key)
        return null
      }

      return data
    } catch (error) {
      console.error('获取本地缓存失败:', error)
      return null
    }
  }

  /**
   * 删除本地存储缓存
   * @param {string} key 缓存键
   */
  removeLocalCache(key) {
    try {
      const cacheKey = this.getCacheKey(key)
      uni.removeStorageSync(cacheKey)
    } catch (error) {
      console.error('删除本地缓存失败:', error)
    }
  }

  /**
   * 清空所有缓存
   */
  clearAllCache() {
    // 清空内存缓存
    this.memoryCache.clear()

    // 清空本地存储缓存
    try {
      const storageInfo = uni.getStorageInfoSync()
      const keys = storageInfo.keys || []
      
      keys.forEach(key => {
        if (key.startsWith(this.cachePrefix)) {
          uni.removeStorageSync(key)
        }
      })
    } catch (error) {
      console.error('清空本地缓存失败:', error)
    }
  }

  /**
   * 获取缓存（优先内存，其次本地存储）
   * @param {string} key 缓存键
   * @returns {any|null} 缓存数据或null
   */
  getCache(key) {
    // 优先从内存缓存获取
    let data = this.getMemoryCache(key)
    if (data !== null) {
      return data
    }

    // 从本地存储获取
    data = this.getLocalCache(key)
    if (data !== null) {
      // 同步到内存缓存
      this.setMemoryCache(key, data)
      return data
    }

    return null
  }

  /**
   * 设置缓存（同时设置内存和本地存储）
   * @param {string} key 缓存键
   * @param {any} data 缓存数据
   * @param {number} expireTime 过期时间（毫秒）
   */
  setCache(key, data, expireTime = this.defaultExpireTime) {
    this.setMemoryCache(key, data, expireTime)
    this.setLocalCache(key, data, expireTime)
  }

  /**
   * 删除缓存（同时删除内存和本地存储）
   * @param {string} key 缓存键
   */
  removeCache(key) {
    this.memoryCache.delete(key)
    this.removeLocalCache(key)
  }

  /**
   * 检查网络状态
   * @returns {Promise<boolean>} 是否有网络连接
   */
  async checkNetworkStatus() {
    return new Promise((resolve) => {
      uni.getNetworkType({
        success: (res) => {
          resolve(res.networkType !== 'none')
        },
        fail: () => {
          resolve(false)
        }
      })
    })
  }

  /**
   * 获取缓存统计信息
   * @returns {object} 缓存统计信息
   */
  getCacheStats() {
    const memorySize = this.memoryCache.size
    let localSize = 0

    try {
      const storageInfo = uni.getStorageInfoSync()
      const keys = storageInfo.keys || []
      localSize = keys.filter(key => key.startsWith(this.cachePrefix)).length
    } catch (error) {
      console.error('获取缓存统计失败:', error)
    }

    return {
      memorySize,
      localSize,
      totalSize: memorySize + localSize
    }
  }
}

// 创建全局缓存管理器实例
const cacheManager = new CacheManager()

export default cacheManager

// 导出常用方法
export const {
  getCache,
  setCache,
  removeCache,
  clearAllCache,
  checkNetworkStatus,
  getCacheStats
} = cacheManager