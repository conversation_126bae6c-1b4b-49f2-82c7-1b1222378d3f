// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core.Service;

/// <summary>
/// 场景管理服务 🧩
/// </summary>
[ApiDescriptionSettings(Order = 505)]
public class EnergySceneService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<EnergyScene> _energySceneRep;
    private readonly SqlSugarRepository<EnergySceneDevice> _energySceneDeviceRep;
    private readonly SqlSugarRepository<EnergyDevice> _energyDeviceRep;
    private readonly SqlSugarRepository<EnergyControl> _energyControlRep;
    private readonly SysCacheService _sysCacheService;
    private readonly UserManager _userManager;

    public EnergySceneService(
        SqlSugarRepository<EnergyScene> energySceneRep,
        SqlSugarRepository<EnergySceneDevice> energySceneDeviceRep,
        SqlSugarRepository<EnergyDevice> energyDeviceRep,
        SqlSugarRepository<EnergyControl> energyControlRep,
        SysCacheService sysCacheService,
        UserManager userManager)
    {
        _energySceneRep = energySceneRep;
        _energySceneDeviceRep = energySceneDeviceRep;
        _energyDeviceRep = energyDeviceRep;
        _energyControlRep = energyControlRep;
        _sysCacheService = sysCacheService;
        _userManager = userManager;
    }

    /// <summary>
    /// 获取场景分页列表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取场景分页列表")]
    public async Task<SqlSugarPagedList<EnergySceneOutput>> GetPage([FromQuery] EnergySceneInput input)
    {
        var query = _energySceneRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.SceneName), u => u.SceneName.Contains(input.SceneName))
            .WhereIF(!string.IsNullOrWhiteSpace(input.SceneType), u => u.SceneType == int.Parse(input.SceneType))
            .WhereIF(input.Status.HasValue, u => u.Status == input.Status.Value)
            .WhereIF(!string.IsNullOrWhiteSpace(input.TriggerCondition), u => u.TriggerCondition.Contains(input.TriggerCondition))
            .Select(u => new EnergySceneOutput
            {
                Id = u.Id,
                SceneName = u.SceneName,
                Description = u.Description,
                TriggerCondition = u.TriggerCondition,
                ExecuteActions = u.ExecuteAction,
                CronExpression = u.TimerConfig,
                Status = u.Status,
                Sort = u.OrderNo,
                LastExecuteTime = u.LastExecuteTime,
                ExecuteCount = u.ExecuteCount,
                SuccessRate = u.SuccessRate,
                CreateTime = u.CreateTime
            })
            .OrderBy(u => u.Sort)
            .OrderBy(u => u.CreateTime, OrderByType.Desc);

        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取场景列表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取场景列表")]
    public async Task<List<EnergySceneOutput>> GetList([FromQuery] EnergySceneInput input)
    {
        var query = _energySceneRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.SceneName), u => u.SceneName.Contains(input.SceneName))
            .WhereIF(!string.IsNullOrWhiteSpace(input.SceneType), u => u.SceneType == int.Parse(input.SceneType))
            .WhereIF(input.Status.HasValue, u => u.Status == input.Status)
            .Select(u => new EnergySceneOutput
            {
                Id = u.Id,
                SceneName = u.SceneName,
                Description = u.Description,
                Status = u.Status,
                Sort = u.OrderNo,
                CreateTime = u.CreateTime
            })
            .OrderBy(u => u.Sort)
            .OrderBy(u => u.CreateTime, OrderByType.Desc);

        return await query.ToListAsync();
    }

    /// <summary>
    /// 获取场景详情 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取场景详情")]
    public async Task<EnergySceneDetailOutput> GetDetail([FromQuery] QueryByIdEnergySceneInput input)
    {
        var scene = await _energySceneRep.GetByIdAsync(input.Id);
        if (scene == null)
            throw Oops.Oh("场景不存在");

        var sceneOutput = scene.Adapt<EnergySceneDetailOutput>();
        sceneOutput.StatusName = scene.Status == 1 ? "启用" : "禁用";

        // 获取关联设备列表
        sceneOutput.DeviceList = await _energySceneDeviceRep.AsQueryable()
            .LeftJoin<EnergyDevice>((sd, d) => sd.DeviceId == d.Id)
            .Where((sd, d) => sd.SceneId == input.Id)
            .Select((sd, d) => new EnergySceneDeviceOutput
            {
                Id = sd.Id,
                SceneId = sd.SceneId,
                DeviceId = sd.DeviceId,
                DeviceCode = d.DeviceCode,
                DeviceName = d.DeviceName,
                DeviceLocation = d.Location,
                ControlType = sd.ControlType,
                ControlCommand = sd.ControlCommand,
                DelayTime = sd.DelayTime,
                Sort = sd.Sort,
                Status = sd.Status,
                StatusName = SqlFunc.IIF(sd.Status == 1, "启用", "禁用"),
                LastExecuteTime = sd.LastExecuteTime,
                ExecuteCount = sd.ExecuteCount,
                SuccessRate = sd.SuccessRate,
                AvgExecuteDuration = sd.AvgExecuteDuration
            })
            .OrderBy(sd => sd.Sort)
            .ToListAsync();

        // 获取最近执行记录
        var recentRecords = await _energyControlRep.AsQueryable()
            .LeftJoin<EnergyDevice>((c, d) => c.DeviceId == d.Id)
            .Where((c, d) => c.SceneId == input.Id)
            .Select((c, d) => new
            {
                c.Id,
                c.DeviceId,
                DeviceCode = d.DeviceCode,
                DeviceName = d.DeviceName,
                c.ControlType,
                c.ControlCommand,
                c.ControlTime,
                c.ExecuteStatus,
                ExecuteStatusName = SqlFunc.IIF(c.ExecuteStatus == 0, "执行中", SqlFunc.IIF(c.ExecuteStatus == 1, "成功", SqlFunc.IIF(c.ExecuteStatus == 2, "失败", "超时"))),
                c.ExecuteDuration,
                c.ErrorMessage
            })
            .OrderBy(c => c.ControlTime, OrderByType.Desc)
            .Take(10)
            .ToListAsync();
        
        sceneOutput.RecentExecuteRecords = recentRecords.Cast<object>().ToList();

        // 统计今日和本月执行次数
        var today = DateTime.Today;
        var thisMonth = new DateTime(today.Year, today.Month, 1);

        sceneOutput.TodayExecuteCount = await _energyControlRep.CountAsync(u => u.SceneId == input.Id && u.ControlTime >= today);
        sceneOutput.ThisMonthExecuteCount = await _energyControlRep.CountAsync(u => u.SceneId == input.Id && u.ControlTime >= thisMonth);

        // 计算平均执行时长
        var avgDuration = await _energyControlRep.AsQueryable()
            .Where(u => u.SceneId == input.Id && u.ExecuteDuration > 0)
            .Select(u => u.ExecuteDuration)
            .ToListAsync();
        sceneOutput.AvgExecuteTime = avgDuration.Any() ? (decimal)avgDuration.Average() : 0;

        return sceneOutput;
    }

    /// <summary>
    /// 增加场景 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [DisplayName("增加场景")]
    public async Task<long> AddScene(AddEnergySceneInput input)
    {
        // 检查场景名称是否重复
        var isExist = await _energySceneRep.IsAnyAsync(u => u.SceneName == input.SceneName);
        if (isExist)
            throw Oops.Oh("场景名称已存在");

        var scene = input.Adapt<EnergyScene>();
        scene.TenantId = _userManager.TenantId;
        scene.CreateUserId = _userManager.UserId;
        scene.CreateTime = DateTime.Now;
        scene.ExecuteCount = 0;
        scene.SuccessRate = 0;

        var newScene = await _energySceneRep.AsInsertable(scene).ExecuteReturnEntityAsync();
        return newScene.Id;
    }

    /// <summary>
    /// 更新场景 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [DisplayName("更新场景")]
    public async Task UpdateScene(UpdateEnergySceneInput input)
    {
        // 检查场景是否存在
        var scene = await _energySceneRep.GetByIdAsync(input.Id);
        if (scene == null)
            throw Oops.Oh("场景不存在");

        // 检查场景名称是否重复（排除自己）
        var isExist = await _energySceneRep.IsAnyAsync(u => u.SceneName == input.SceneName && u.Id != input.Id);
        if (isExist)
            throw Oops.Oh("场景名称已存在");

        var updateScene = input.Adapt<EnergyScene>();
        updateScene.UpdateUserId = _userManager.UserId;
        updateScene.UpdateTime = DateTime.Now;

        await _energySceneRep.AsUpdateable(updateScene).ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除场景 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    [DisplayName("删除场景")]
    public async Task DeleteScene(DeleteEnergySceneInput input)
    {
        var scene = await _energySceneRep.GetByIdAsync(input.Id);
        if (scene == null)
            throw Oops.Oh("场景不存在");

        // 检查是否有关联的设备
        var hasDevices = await _energySceneDeviceRep.IsAnyAsync(u => u.SceneId == input.Id);
        if (hasDevices)
            throw Oops.Oh("场景下存在关联设备，请先删除关联设备");

        await _energySceneRep.DeleteAsync(scene);
    }

    /// <summary>
    /// 设置场景状态 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "SetStatus"), HttpPost]
    [DisplayName("设置场景状态")]
    public async Task SetStatus(EnergySceneStatusInput input)
    {
        var scene = await _energySceneRep.GetByIdAsync(input.Id);
        if (scene == null)
            throw Oops.Oh("场景不存在");

        scene.Status = input.Status;
        scene.UpdateUserId = _userManager.UserId;
        scene.UpdateTime = DateTime.Now;

        await _energySceneRep.AsUpdateable(scene)
            .UpdateColumns(u => new { u.Status, u.UpdateUserId, u.UpdateTime })
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 执行场景 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Execute"), HttpPost]
    [DisplayName("执行场景")]
    public async Task<bool> ExecuteScene(ExecuteSceneInput input)
    {
        var scene = await _energySceneRep.GetByIdAsync(input.Id);
        if (scene == null)
            throw Oops.Oh("场景不存在");

        if (scene.Status != 1)
            throw Oops.Oh("场景已禁用，无法执行");

        // 获取场景关联的设备列表
        var sceneDevices = await _energySceneDeviceRep.AsQueryable()
            .LeftJoin<EnergyDevice>((sd, d) => sd.DeviceId == d.Id)
            .Where((sd, d) => sd.SceneId == input.Id && sd.Status == 1 && d.Status == 1)
            .Select((sd, d) => new
            {
                sd.Id,
                sd.DeviceId,
                sd.ControlType,
                sd.ControlCommand,
                sd.DelayTime,
                sd.Sort,
                DeviceCode = d.DeviceCode,
                DeviceName = d.DeviceName
            })
            .OrderBy(sd => sd.Sort)
            .ToListAsync();

        if (!sceneDevices.Any())
            throw Oops.Oh("场景下没有可执行的设备");

        var executeStartTime = DateTime.Now;
        var successCount = 0;
        var totalCount = sceneDevices.Count;

        // 按顺序执行设备控制
        foreach (var device in sceneDevices)
        {
            try
            {
                // 创建控制记录
                var control = new EnergyControl
                {
                    DeviceId = device.DeviceId,
                    SceneId = input.Id,
                    ControlType = int.TryParse(device.ControlType, out var controlType) ? controlType : 1,
                    ControlCommand = device.ControlCommand,
                    ControlTime = DateTime.Now,
                    ExecuteStatus = 0, // 执行中
                    ControlSource = 3, // 场景控制
                    TenantId = _userManager.TenantId,
                    CreateUserId = _userManager.UserId,
                    CreateTime = DateTime.Now
                };

                var newControl = await _energyControlRep.AsInsertable(control).ExecuteReturnEntityAsync();

                // TODO: 发送MQTT控制指令到设备
                // var success = await SendSceneControlCommandAsync(device, control);
                // if (success) successCount++;

                // 模拟执行成功
                successCount++;
                await UpdateControlResult(newControl.Id, 1, "执行成功", null, 100);

                // 延迟执行下一个设备
                if (device.DelayTime > 0)
                    await Task.Delay(device.DelayTime * 1000);
            }
            catch (Exception ex)
            {
                // 记录执行失败
                Console.WriteLine($"场景设备执行失败: {device.DeviceName}, 错误: {ex.Message}");
            }
        }

        // 更新场景执行统计
        var executeDuration = (int)(DateTime.Now - executeStartTime).TotalMilliseconds;
        await UpdateSceneExecuteStats(input.Id, successCount, totalCount, executeDuration);

        return successCount > 0;
    }

    /// <summary>
    /// 获取场景统计数据 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取场景统计数据")]
    public async Task<EnergySceneStatOutput> GetStat([FromQuery] EnergySceneStatInput input)
    {
        var query = _energySceneRep.AsQueryable()
            .WhereIF(input.StartTime.HasValue, u => u.CreateTime >= input.StartTime)
            .WhereIF(input.EndTime.HasValue, u => u.CreateTime <= input.EndTime);

        var totalCount = await query.CountAsync();
        var enabledCount = await query.Where(u => u.Status == 1).CountAsync();
        var disabledCount = await query.Where(u => u.Status == 0).CountAsync();

        // 统计执行次数
        var controlQuery = _energyControlRep.AsQueryable()
            .Where(u => u.SceneId.HasValue)
            .WhereIF(input.StartTime.HasValue, u => u.ControlTime >= input.StartTime)
            .WhereIF(input.EndTime.HasValue, u => u.ControlTime <= input.EndTime);

        var totalExecuteCount = await controlQuery.CountAsync();
        var successExecuteCount = await controlQuery.Where(u => u.ExecuteStatus == 1).CountAsync();
        var failedExecuteCount = await controlQuery.Where(u => u.ExecuteStatus == 2).CountAsync();

        // 计算平均成功率和执行时长
        var avgSuccessRate = await query.Where(u => u.ExecuteCount > 0).Select(u => u.SuccessRate ?? 0).ToListAsync();
        var avgDuration = await controlQuery.Where(u => u.ExecuteDuration > 0).Select(u => u.ExecuteDuration).ToListAsync();

        return new EnergySceneStatOutput
        {
            StatTime = DateTime.Now,
            TotalSceneCount = totalCount,
            EnabledSceneCount = enabledCount,
            DisabledSceneCount = disabledCount,
            TotalExecuteCount = totalExecuteCount,
            SuccessExecuteCount = successExecuteCount,
            FailedExecuteCount = failedExecuteCount,
            AvgSuccessRate = avgSuccessRate.Any() ? (decimal)avgSuccessRate.Average() : 0,
            AvgExecuteDuration = avgDuration.Any() ? (decimal)avgDuration.Average() : 0
        };
    }

    /// <summary>
    /// 更新控制结果
    /// </summary>
    private async Task UpdateControlResult(long controlId, int status, string result, string errorMessage, int duration)
    {
        var control = await _energyControlRep.GetByIdAsync(controlId);
        if (control == null) return;

        control.ExecuteStatus = status;
        control.ExecuteResult = result;
        control.ErrorMessage = errorMessage;
        control.ExecuteDuration = duration;
        control.UpdateTime = DateTime.Now;

        await _energyControlRep.AsUpdateable(control)
            .UpdateColumns(u => new { u.ExecuteStatus, u.ExecuteResult, u.ErrorMessage, u.ExecuteDuration, u.UpdateTime })
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 更新场景执行统计
    /// </summary>
    private async Task UpdateSceneExecuteStats(long sceneId, int successCount, int totalCount, int duration)
    {
        var scene = await _energySceneRep.GetByIdAsync(sceneId);
        if (scene == null) return;

        scene.ExecuteCount += 1;
        scene.LastExecuteTime = DateTime.Now;
        
        // 重新计算成功率
        var totalExecuteCount = await _energyControlRep.CountAsync(u => u.SceneId == sceneId);
        var successExecuteCount = await _energyControlRep.CountAsync(u => u.SceneId == sceneId && u.ExecuteStatus == 1);
        scene.SuccessRate = totalExecuteCount > 0 ? (decimal)successExecuteCount / totalExecuteCount * 100 : 0;
        
        scene.UpdateTime = DateTime.Now;

        await _energySceneRep.AsUpdateable(scene)
            .UpdateColumns(u => new { u.ExecuteCount, u.LastExecuteTime, u.SuccessRate, u.UpdateTime })
            .ExecuteCommandAsync();
    }
}