namespace Admin.NET.Core.Service;

/// <summary>
/// 告警列表输出
/// </summary>
public class AlarmListOutput
{
    /// <summary>
    /// 告警ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string DeviceName { get; set; }

    /// <summary>
    /// 故障类型
    /// </summary>
    public int FaultType { get; set; }

    /// <summary>
    /// 故障级别
    /// </summary>
    public int FaultLevel { get; set; }

    /// <summary>
    /// 故障描述
    /// </summary>
    public string FaultDescription { get; set; }

    /// <summary>
    /// 处理状态
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 处理时间
    /// </summary>
    public DateTime? HandleTime { get; set; }

    /// <summary>
    /// 处理备注
    /// </summary>
    public string? Remark { get; set; }
}