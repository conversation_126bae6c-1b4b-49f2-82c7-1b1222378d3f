// Loading状态管理模块
const state = {
  // 全局loading状态
  globalLoading: false,
  globalLoadingText: '加载中...',
  
  // 页面loading状态
  pageLoading: false,
  pageLoadingText: '页面加载中...',
  
  // 局部loading状态
  localLoading: {},
  
  // loading队列
  loadingQueue: [],
  
  // 网络状态
  networkStatus: 'good',
  
  // loading配置
  loadingConfig: {
    timeout: 30000, // 默认超时时间
    showTimeout: 500, // 显示延迟时间
    minShowTime: 1000 // 最小显示时间
  }
}

const getters = {
  // 是否有任何loading在显示
  hasAnyLoading: (state) => {
    return state.globalLoading || state.pageLoading || Object.keys(state.localLoading).length > 0
  },
  
  // 获取当前loading数量
  loadingCount: (state) => {
    let count = 0
    if (state.globalLoading) count++
    if (state.pageLoading) count++
    count += Object.keys(state.localLoading).length
    return count
  },
  
  // 获取网络状态
  networkStatus: (state) => state.networkStatus,
  
  // 获取loading配置
  loadingConfig: (state) => state.loadingConfig
}

const mutations = {
  // 设置全局loading
  SET_GLOBAL_LOADING(state, { loading, text = '加载中...' }) {
    state.globalLoading = loading
    state.globalLoadingText = text
  },
  
  // 设置页面loading
  SET_PAGE_LOADING(state, { loading, text = '页面加载中...' }) {
    state.pageLoading = loading
    state.pageLoadingText = text
  },
  
  // 设置局部loading
  SET_LOCAL_LOADING(state, { id, loading, text = '处理中...', options = {} }) {
    if (loading) {
      state.localLoading = {
        ...state.localLoading,
        [id]: {
          text,
          options,
          startTime: Date.now()
        }
      }
    } else {
      const { [id]: _, ...rest } = state.localLoading // eslint-disable-line no-unused-vars
      state.localLoading = rest
    }
  },
  
  // 添加到loading队列
  ADD_TO_QUEUE(state, item) {
    state.loadingQueue.push({
      ...item,
      id: item.id || Date.now().toString(),
      startTime: Date.now()
    })
  },
  
  // 从loading队列移除
  REMOVE_FROM_QUEUE(state, id) {
    state.loadingQueue = state.loadingQueue.filter(item => item.id !== id)
  },
  
  // 清空loading队列
  CLEAR_QUEUE(state) {
    state.loadingQueue = []
  },
  
  // 设置网络状态
  SET_NETWORK_STATUS(state, status) {
    state.networkStatus = status
  },
  
  // 更新loading配置
  UPDATE_LOADING_CONFIG(state, config) {
    state.loadingConfig = {
      ...state.loadingConfig,
      ...config
    }
  },
  
  // 重置所有loading状态
  RESET_ALL_LOADING(state) {
    state.globalLoading = false
    state.pageLoading = false
    state.localLoading = {}
    state.loadingQueue = []
  }
}

const actions = {
  // 显示全局loading
  showGlobalLoading({ commit, state }, { text = '加载中...', timeout = null } = {}) {
    return new Promise((resolve) => {
      // 延迟显示，避免闪烁
      setTimeout(() => {
        commit('SET_GLOBAL_LOADING', { loading: true, text })
        resolve()
      }, state.loadingConfig.showTimeout)
      
      // 自动超时隐藏
      if (timeout || state.loadingConfig.timeout) {
        setTimeout(() => {
          commit('SET_GLOBAL_LOADING', { loading: false })
        }, timeout || state.loadingConfig.timeout)
      }
    })
  },
  
  // 隐藏全局loading
  hideGlobalLoading({ commit, state }) {
    return new Promise((resolve) => {
      // 确保最小显示时间
      const minShowTime = state.loadingConfig.minShowTime
      const showTime = Date.now() - (state.globalLoadingStartTime || 0)
      
      if (showTime < minShowTime) {
        setTimeout(() => {
          commit('SET_GLOBAL_LOADING', { loading: false })
          resolve()
        }, minShowTime - showTime)
      } else {
        commit('SET_GLOBAL_LOADING', { loading: false })
        resolve()
      }
    })
  },
  
  // 显示页面loading
  showPageLoading({ commit }, { text = '页面加载中...' } = {}) {
    commit('SET_PAGE_LOADING', { loading: true, text })
  },
  
  // 隐藏页面loading
  hidePageLoading({ commit }) {
    commit('SET_PAGE_LOADING', { loading: false })
  },
  
  // 显示局部loading
  showLocalLoading({ commit }, { id, text = '处理中...', options = {} }) {
    if (!id) {
      id = 'local_' + Date.now()
    }
    commit('SET_LOCAL_LOADING', { id, loading: true, text, options })
    return id
  },
  
  // 隐藏局部loading
  hideLocalLoading({ commit }, id) {
    commit('SET_LOCAL_LOADING', { id, loading: false })
  },
  
  // 智能loading - 根据网络状况和数据大小选择合适的loading方式
  showSmartLoading({ state, dispatch }, options = {}) {
    const {
      type = 'auto',
      dataSize = 'small',
      text = '加载中...',
      timeout = null,
      id = null
    } = options
    
    let loadingType = type
    let loadingId = id
    
    if (type === 'auto') {
      // 根据网络状况和数据大小自动选择
      if (state.networkStatus === 'slow' || dataSize === 'large') {
        loadingType = 'page'
      } else if (dataSize === 'medium') {
        loadingType = 'local'
      } else {
        loadingType = 'global'
      }
    }
    
    switch (loadingType) {
      case 'global':
        dispatch('showGlobalLoading', { text, timeout })
        break
      case 'page':
        dispatch('showPageLoading', { text })
        break
      case 'local':
        loadingId = dispatch('showLocalLoading', { id: loadingId, text, options })
        break
    }
    
    return { type: loadingType, id: loadingId }
  },
  
  // 隐藏智能loading
  hideSmartLoading({ dispatch }, { type, id }) {
    switch (type) {
      case 'global':
        dispatch('hideGlobalLoading')
        break
      case 'page':
        dispatch('hidePageLoading')
        break
      case 'local':
        dispatch('hideLocalLoading', id)
        break
    }
  },
  
  // 管理loading队列
  manageLoadingQueue({ commit, state, dispatch }, { action, item }) {
    switch (action) {
      case 'add':
        commit('ADD_TO_QUEUE', item)
        // 处理队列中的loading
        if (state.loadingQueue.length === 1) {
          const latestItem = state.loadingQueue[0]
          dispatch('showSmartLoading', latestItem.options)
        }
        break
        
      case 'remove':
        commit('REMOVE_FROM_QUEUE', item.id)
        // 如果队列为空，隐藏所有loading
        if (state.loadingQueue.length === 0) {
          dispatch('hideAllLoading')
        } else {
          // 显示队列中下一个loading
          const nextItem = state.loadingQueue[state.loadingQueue.length - 1]
          dispatch('showSmartLoading', nextItem.options)
        }
        break
        
      case 'clear':
        commit('CLEAR_QUEUE')
        dispatch('hideAllLoading')
        break
    }
  },
  
  // 隐藏所有loading
  hideAllLoading({ commit }) {
    commit('RESET_ALL_LOADING')
  },
  
  // 更新网络状态
  updateNetworkStatus({ commit }, status) {
    commit('SET_NETWORK_STATUS', status)
  },
  
  // 初始化网络监控
  initNetworkMonitor({ commit }) {
    // 监听网络状态变化
    uni.onNetworkStatusChange((res) => {
      let status = 'good'
      
      if (res.networkType === 'none') {
        status = 'none'
      } else if (res.networkType === '2g') {
        status = 'slow'
      } else if (res.networkType === '3g') {
        status = 'medium'
      } else {
        status = 'good'
      }
      
      commit('SET_NETWORK_STATUS', status)
    })
    
    // 获取初始网络状态
    uni.getNetworkType({
      success: (res) => {
        let status = 'good'
        
        if (res.networkType === 'none') {
          status = 'none'
        } else if (res.networkType === '2g') {
          status = 'slow'
        } else if (res.networkType === '3g') {
          status = 'medium'
        } else {
          status = 'good'
        }
        
        commit('SET_NETWORK_STATUS', status)
      }
    })
  },
  
  // 更新loading配置
  updateLoadingConfig({ commit }, config) {
    commit('UPDATE_LOADING_CONFIG', config)
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}