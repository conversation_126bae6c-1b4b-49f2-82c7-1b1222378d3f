using System;
using System.Threading;
using System.Threading.Tasks;

namespace Admin.NET.Plugin.MQTT.Core.Interfaces
{
    /// <summary>
    /// MQTT服务工厂接口
    /// 定义创建和管理MQTT相关服务实例的工厂方法
    /// </summary>
    public interface IMqttServiceFactory
    {
        /// <summary>
        /// 创建MQTT客户端管理器
        /// </summary>
        /// <param name="configuration">客户端配置</param>
        /// <returns>MQTT客户端管理器实例</returns>
        IMqttClientManager CreateClientManager(MqttClientConfiguration configuration);
        
        /// <summary>
        /// 创建消息路由器
        /// </summary>
        /// <param name="clientManager">客户端管理器</param>
        /// <param name="configuration">路由器配置</param>
        /// <returns>消息路由器实例</returns>
        IMqttMessageRouter CreateMessageRouter(IMqttClientManager clientManager, MessageRouterConfiguration configuration);
        
        /// <summary>
        /// 创建设备控制服务
        /// </summary>
        /// <param name="messageRouter">消息路由器</param>
        /// <param name="configuration">设备控制配置</param>
        /// <returns>设备控制服务实例</returns>
        IDeviceControlService CreateDeviceControlService(IMqttMessageRouter messageRouter, DeviceControlConfiguration configuration);
        
        /// <summary>
        /// 创建配置管理器
        /// </summary>
        /// <returns>配置管理器实例</returns>
        IMqttConfigurationManager CreateConfigurationManager();
        
        /// <summary>
        /// 创建异常处理器
        /// </summary>
        /// <param name="configuration">配置信息</param>
        /// <returns>异常处理器实例</returns>
        IMqttExceptionHandler CreateExceptionHandler(MqttPluginConfiguration configuration);
        
        /// <summary>
        /// 创建消息处理器
        /// </summary>
        /// <typeparam name="T">处理器类型</typeparam>
        /// <param name="parameters">构造参数</param>
        /// <returns>消息处理器实例</returns>
        T CreateMessageHandler<T>(params object[] parameters) where T : class, IMessageHandler;
        
        /// <summary>
        /// 创建泛型消息处理器
        /// </summary>
        /// <typeparam name="THandler">处理器类型</typeparam>
        /// <typeparam name="TMessage">消息类型</typeparam>
        /// <param name="parameters">构造参数</param>
        /// <returns>泛型消息处理器实例</returns>
        THandler CreateMessageHandler<THandler, TMessage>(params object[] parameters) 
            where THandler : class, IMessageHandler<TMessage> 
            where TMessage : class;
        
        /// <summary>
        /// 注册服务类型
        /// </summary>
        /// <typeparam name="TInterface">接口类型</typeparam>
        /// <typeparam name="TImplementation">实现类型</typeparam>
        /// <param name="lifetime">服务生命周期</param>
        void RegisterService<TInterface, TImplementation>(ServiceLifetime lifetime = ServiceLifetime.Transient)
            where TInterface : class
            where TImplementation : class, TInterface;
        
        /// <summary>
        /// 注册服务实例
        /// </summary>
        /// <typeparam name="TInterface">接口类型</typeparam>
        /// <param name="instance">服务实例</param>
        void RegisterInstance<TInterface>(TInterface instance) where TInterface : class;
        
        /// <summary>
        /// 注册服务工厂方法
        /// </summary>
        /// <typeparam name="TInterface">接口类型</typeparam>
        /// <param name="factory">工厂方法</param>
        /// <param name="lifetime">服务生命周期</param>
        void RegisterFactory<TInterface>(Func<IMqttServiceFactory, TInterface> factory, ServiceLifetime lifetime = ServiceLifetime.Transient)
            where TInterface : class;
        
        /// <summary>
        /// 获取服务实例
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <returns>服务实例</returns>
        T GetService<T>() where T : class;
        
        /// <summary>
        /// 获取服务实例
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>服务实例</returns>
        object GetService(Type serviceType);
        
        /// <summary>
        /// 尝试获取服务实例
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <param name="service">服务实例</param>
        /// <returns>是否成功获取</returns>
        bool TryGetService<T>(out T service) where T : class;
        
        /// <summary>
        /// 检查服务是否已注册
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <returns>是否已注册</returns>
        bool IsServiceRegistered<T>() where T : class;
        
        /// <summary>
        /// 检查服务是否已注册
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>是否已注册</returns>
        bool IsServiceRegistered(Type serviceType);
        
        /// <summary>
        /// 创建子作用域
        /// </summary>
        /// <returns>子作用域工厂</returns>
        IMqttServiceScope CreateScope();
        
        /// <summary>
        /// 释放资源
        /// </summary>
        void Dispose();
    }
    
    /// <summary>
    /// MQTT服务作用域接口
    /// </summary>
    public interface IMqttServiceScope : IDisposable
    {
        /// <summary>
        /// 服务工厂
        /// </summary>
        IMqttServiceFactory ServiceFactory { get; }
        
        /// <summary>
        /// 获取服务实例
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <returns>服务实例</returns>
        T GetService<T>() where T : class;
        
        /// <summary>
        /// 获取服务实例
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>服务实例</returns>
        object GetService(Type serviceType);
    }
    
    /// <summary>
    /// 服务生命周期枚举
    /// </summary>
    public enum ServiceLifetime
    {
        /// <summary>
        /// 瞬时服务 - 每次请求都创建新实例
        /// </summary>
        Transient,
        
        /// <summary>
        /// 作用域服务 - 在同一作用域内共享实例
        /// </summary>
        Scoped,
        
        /// <summary>
        /// 单例服务 - 全局共享单一实例
        /// </summary>
        Singleton
    }
    
    /// <summary>
    /// 服务注册信息
    /// </summary>
    public class ServiceRegistration
    {
        /// <summary>
        /// 服务类型
        /// </summary>
        public Type ServiceType { get; set; }
        
        /// <summary>
        /// 实现类型
        /// </summary>
        public Type ImplementationType { get; set; }
        
        /// <summary>
        /// 服务实例
        /// </summary>
        public object Instance { get; set; }
        
        /// <summary>
        /// 工厂方法
        /// </summary>
        public Func<IMqttServiceFactory, object> Factory { get; set; }
        
        /// <summary>
        /// 服务生命周期
        /// </summary>
        public ServiceLifetime Lifetime { get; set; }
        
        /// <summary>
        /// 注册时间
        /// </summary>
        public DateTime RegisteredAt { get; set; } = DateTime.UtcNow;
    }
    
    /// <summary>
    /// 服务工厂异常
    /// </summary>
    public class ServiceFactoryException : Exception
    {
        /// <summary>
        /// 服务类型
        /// </summary>
        public Type ServiceType { get; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <param name="message">错误消息</param>
        public ServiceFactoryException(Type serviceType, string message) : base(message)
        {
            ServiceType = serviceType;
        }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <param name="message">错误消息</param>
        /// <param name="innerException">内部异常</param>
        public ServiceFactoryException(Type serviceType, string message, Exception innerException) : base(message, innerException)
        {
            ServiceType = serviceType;
        }
    }
    
    /// <summary>
    /// 循环依赖异常
    /// </summary>
    public class CircularDependencyException : ServiceFactoryException
    {
        /// <summary>
        /// 依赖链
        /// </summary>
        public string[] DependencyChain { get; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <param name="dependencyChain">依赖链</param>
        public CircularDependencyException(Type serviceType, string[] dependencyChain) 
            : base(serviceType, $"检测到循环依赖: {string.Join(" -> ", dependencyChain)} -> {serviceType.Name}")
        {
            DependencyChain = dependencyChain;
        }
    }
    
    /// <summary>
    /// 服务未注册异常
    /// </summary>
    public class ServiceNotRegisteredException : ServiceFactoryException
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        public ServiceNotRegisteredException(Type serviceType) 
            : base(serviceType, $"服务类型 '{serviceType.FullName}' 未注册")
        {
        }
    }
}