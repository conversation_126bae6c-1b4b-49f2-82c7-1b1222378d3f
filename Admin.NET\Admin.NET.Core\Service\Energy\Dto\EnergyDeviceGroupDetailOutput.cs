// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core.Service;

/// <summary>
/// 设备分组详情输出参数
/// </summary>
public class EnergyDeviceGroupDetailOutput : EnergyDeviceGroupOutput
{
    /// <summary>
    /// 父级分组详细信息
    /// </summary>
    public EnergyDeviceGroupOutput? Parent { get; set; }

    /// <summary>
    /// 分组下的设备列表
    /// </summary>
    public List<EnergyDeviceOutput> Devices { get; set; } = new();

    /// <summary>
    /// 分组统计信息
    /// </summary>
    public GroupStatInfo StatInfo { get; set; } = new();
}

/// <summary>
/// 分组统计信息
/// </summary>
public class GroupStatInfo
{
    /// <summary>
    /// 设备总数
    /// </summary>
    public int DeviceCount { get; set; }

    /// <summary>
    /// 在线设备数
    /// </summary>
    public int OnlineDeviceCount { get; set; }

    /// <summary>
    /// 离线设备数
    /// </summary>
    public int OfflineDeviceCount { get; set; }

    /// <summary>
    /// 故障设备数
    /// </summary>
    public int FaultDeviceCount { get; set; }

    /// <summary>
    /// 在线率
    /// </summary>
    public decimal OnlineRate { get; set; }

    /// <summary>
    /// 今日总用电量(kWh)
    /// </summary>
    public decimal TodayTotalEnergy { get; set; }

    /// <summary>
    /// 本月总用电量(kWh)
    /// </summary>
    public decimal MonthTotalEnergy { get; set; }

    /// <summary>
    /// 累计总用电量(kWh)
    /// </summary>
    public decimal TotalEnergy { get; set; }

    /// <summary>
    /// 今日控制次数
    /// </summary>
    public int TodayControlCount { get; set; }

    /// <summary>
    /// 本月控制次数
    /// </summary>
    public int MonthControlCount { get; set; }

    /// <summary>
    /// 累计控制次数
    /// </summary>
    public int TotalControlCount { get; set; }

    /// <summary>
    /// 子分组数量
    /// </summary>
    public int ChildGroupCount { get; set; }
}