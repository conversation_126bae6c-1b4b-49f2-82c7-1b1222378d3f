import { service as request } from '/@/utils/request';
import { useBaseApi } from '../base';

/**
 * 职位管理API接口集合
 * @method getPage 获取职位分页列表
 * @method getDetail 获取职位详情
 * @method add 添加职位
 * @method update 更新职位
 * @method delete 删除职位
 * @method getList 获取职位列表
 * @method setStatus 设置状态
 */
export function usePosApi() {
	const baseApi = useBaseApi('sysPos');
	
	return {
		...baseApi,
		// 获取职位列表
		getList: (name?: string, code?: string, status?: number) => {
			return request({
				url: '/api/sysPos/list',
				method: 'get',
				params: { name, code, status },
			});
		},
		// 设置状态
		setStatus: (data: any) => {
			return request({
				url: '/api/sysPos/setStatus',
				method: 'post',
				data,
			});
		},
	};
}