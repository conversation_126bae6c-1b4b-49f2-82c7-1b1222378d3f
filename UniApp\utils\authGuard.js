/**
 * 路由守卫工具
 * 用于检查用户登录状态，未登录时跳转到登录页面
 */

import store from '@/store'

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
export function isUserLoggedIn() {
  return store.getters['user/isLoggedIn']
}

/**
 * 路由守卫函数
 * 在页面加载时调用，检查登录状态
 * @param {Object} options 配置选项
 * @param {boolean} options.requireAuth 是否需要登录，默认true
 * @param {string} options.redirectUrl 未登录时跳转的页面，默认登录页
 * @param {string} options.message 提示消息
 * @returns {Promise<boolean>} 是否允许访问
 */
export async function authGuard(options = {}) {
  const {
    requireAuth = true,
    redirectUrl = '/pages/user/login',
    message = '此功能需要登录后才能使用，是否立即登录？'
  } = options

  // 如果不需要登录验证，直接允许访问
  if (!requireAuth) {
    return true
  }

  // 检查登录状态
  const isLoggedIn = isUserLoggedIn()
  
  if (!isLoggedIn) {
    // 显示更友好的登录提示
    uni.showModal({
      title: '需要登录',
      content: message,
      showCancel: true,
      cancelText: '稍后再说',
      confirmText: '立即登录',
      confirmColor: '#007AFF',
      success: (res) => {
        if (res.confirm) {
          // 保存当前页面路径，登录成功后可以返回
          const currentPages = getCurrentPages()
          const currentPage = currentPages[currentPages.length - 1]?.route
          if (currentPage && currentPage !== 'pages/user/login') {
            uni.setStorageSync('redirectAfterLogin', currentPage)
          }
          
          uni.reLaunch({
            url: redirectUrl
          })
        } else {
          // 用户取消，返回首页
          uni.reLaunch({
            url: '/pages/index/index'
          })
        }
      }
    })
    return false
  }

  // 已登录，检查token是否有效
  try {
    const isValid = await store.dispatch('user/checkLoginStatus')
    if (!isValid) {
      // Token无效，提示重新登录
      uni.showModal({
        title: '登录已过期',
        content: '您的登录状态已过期，为了保护您的账户安全，请重新登录',
        showCancel: false,
        confirmText: '重新登录',
        confirmColor: '#007AFF',
        success: () => {
          // 清除过期的登录状态
          store.dispatch('user/logout', { redirectTo: 'login' })
        }
      })
      return false
    }
  } catch (error) {
    console.error('登录状态检查失败:', error)
    // 显示错误提示并跳转到登录页
    uni.showToast({
      title: '登录状态检查失败',
      icon: 'none',
      duration: 2000
    })
    
    setTimeout(() => {
      uni.reLaunch({
        url: redirectUrl
      })
    }, 2000)
    return false
  }

  return true
}

/**
 * 页面混入对象
 * 可以在需要登录的页面中使用
 */
export const authMixin = {
  async onLoad() {
    // 检查登录状态
    const canAccess = await authGuard({
      requireAuth: true,
      message: '此页面需要登录后才能访问'
    })
    
    if (!canAccess) {
      // 如果不能访问，阻止页面继续加载
      return
    }
    
    // 如果页面有自定义的onLoad方法，调用它
    if (this.$options.methods && this.$options.methods.onLoadAfterAuth) {
      this.onLoadAfterAuth()
    }
  }
}

/**
 * 装饰器函数，用于保护需要登录的方法
 * @param {Function} fn 需要保护的方法
 * @param {Object} options 配置选项
 * @returns {Function} 包装后的方法
 */
export function requireAuth(fn, options = {}) {
  return async function(...args) {
    const canAccess = await authGuard(options)
    if (canAccess) {
      return fn.apply(this, args)
    }
  }
}

/**
 * 检查特定权限
 * @param {string|Array} permissions 需要的权限
 * @returns {boolean} 是否有权限
 */
export function hasPermission(permissions) {
  if (!isUserLoggedIn()) {
    return false
  }
  
  const userPermissions = store.getters['user/permissions'] || []
  
  if (typeof permissions === 'string') {
    return userPermissions.includes(permissions)
  }
  
  if (Array.isArray(permissions)) {
    return permissions.some(permission => userPermissions.includes(permission))
  }
  
  return false
}

/**
 * 检查用户角色
 * @param {string|Array} roles 需要的角色
 * @returns {boolean} 是否有角色
 */
export function hasRole(roles) {
  if (!isUserLoggedIn()) {
    return false
  }
  
  const userRoles = store.getters['user/roles'] || []
  
  if (typeof roles === 'string') {
    return userRoles.includes(roles)
  }
  
  if (Array.isArray(roles)) {
    return roles.some(role => userRoles.includes(role))
  }
  
  return false
}

export default {
  isUserLoggedIn,
  authGuard,
  authMixin,
  requireAuth,
  hasPermission,
  hasRole
}