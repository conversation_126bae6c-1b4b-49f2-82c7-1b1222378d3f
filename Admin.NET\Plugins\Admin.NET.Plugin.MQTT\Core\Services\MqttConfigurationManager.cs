using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Admin.NET.Plugin.MQTT.Core.Models;
using Admin.NET.Plugin.MQTT.Core.Interfaces;
using Admin.NET.Plugin.MQTT.Interfaces;
using static Admin.NET.Plugin.MQTT.Interfaces.IMqttConfigurationManager;
using ConfigurationChangedEventArgs = Admin.NET.Plugin.MQTT.Interfaces.ConfigurationChangedEventArgs;
using Admin.NET.Plugin.MQTT.Core.Configuration;
using static Admin.NET.Plugin.MQTT.Core.Constants;

namespace Admin.NET.Plugin.MQTT.Core.Services
{
    /// <summary>
    /// MQTT配置管理器实现
    /// 负责配置加载、验证、更新、热重载等功能
    /// </summary>
    public class MqttConfigurationManager : IMqttConfigurationManager, IDisposable
    {
        #region 私有字段
        
        private readonly ILogger<MqttConfigurationManager> _logger;
        private readonly IConfiguration _configuration;
        private readonly IOptionsMonitor<Admin.NET.Plugin.MQTT.Core.Models.MqttPluginConfiguration> _optionsMonitor;
        private readonly FileSystemWatcher _configFileWatcher;
        
        private Admin.NET.Plugin.MQTT.Core.Models.MqttPluginConfiguration _currentConfiguration;
        private readonly ReaderWriterLockSlim _configLock;
        private readonly Timer _validationTimer;
        
        private volatile bool _isDisposed;
        private readonly string _configFilePath;
        private readonly SemaphoreSlim _reloadSemaphore;
        
        #endregion
        
        #region 事件
        
        /// <summary>
        /// 配置变更事件
        /// </summary>
        public event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged;
        
        /// <summary>
        /// 配置验证失败事件
        /// </summary>
        public event EventHandler<ConfigurationValidationFailedEventArgs> ValidationFailed;
        
        /// <summary>
        /// 配置重载事件
        /// </summary>
        public event EventHandler<ConfigurationReloadedEventArgs> ConfigurationReloaded;
        
        #endregion
        
        #region 属性
        
        /// <summary>
        /// 当前配置
        /// </summary>
        public Admin.NET.Plugin.MQTT.Core.Models.MqttPluginConfiguration CurrentConfiguration
        {
            get
            {
                _configLock.EnterReadLock();
                try
                {
                    return _currentConfiguration;
                }
                finally
                {
                    _configLock.ExitReadLock();
                }
            }
        }
        
        /// <summary>
        /// 配置文件路径
        /// </summary>
        public string ConfigurationFilePath => _configFilePath;
        
        #endregion
        
        #region 构造函数
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="configuration">配置提供者</param>
        /// <param name="optionsMonitor">配置监视器</param>
        public MqttConfigurationManager(
            ILogger<MqttConfigurationManager> logger,
            IConfiguration configuration,
            IOptionsMonitor<Admin.NET.Plugin.MQTT.Core.Models.MqttPluginConfiguration> optionsMonitor)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _optionsMonitor = optionsMonitor ?? throw new ArgumentNullException(nameof(optionsMonitor));
            
            _configLock = new ReaderWriterLockSlim();
            _reloadSemaphore = new SemaphoreSlim(1, 1);
            
            // 获取配置文件路径
            _configFilePath = GetConfigurationFilePath();
            
            // 初始化当前配置
            _currentConfiguration = _optionsMonitor.CurrentValue ?? CreateDefaultConfiguration();
            
            // 订阅配置变更
            _optionsMonitor.OnChange(OnConfigurationChanged);
            
            // 初始化文件监视器
            InitializeFileWatcher();
            
            // 初始化验证定时器
            _validationTimer = new Timer(ValidateConfigurationCallback, null, 
                TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
            
            _logger.LogInformation("MQTT配置管理器已初始化，配置文件: {ConfigFile}", _configFilePath);
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 获取客户端配置
        /// </summary>
        /// <returns>客户端配置</returns>
        public Admin.NET.Plugin.MQTT.Interfaces.MqttClientConfiguration GetClientConfiguration()
        {
            var config = CurrentConfiguration.Client ?? new MqttClientConfiguration();
            return new Admin.NET.Plugin.MQTT.Interfaces.MqttClientConfiguration
            {
                Server = config.Server,
                Port = config.Port,
                ClientId = config.ClientId,
                Username = config.Username,
                Password = config.Password,
                UseTls = config.UseTls,
                CleanSession = config.CleanSession,
                KeepAliveInterval = config.KeepAliveInterval,
                ConnectTimeout = config.ConnectTimeout,
                AutoReconnect = config.AutoReconnect,
                ReconnectInterval = config.ReconnectInterval,
                MaxReconnectAttempts = config.MaxReconnectAttempts
            };
        }
        
        /// <summary>
        /// 获取消息路由器配置
        /// </summary>
        /// <returns>消息路由器配置</returns>
        public Admin.NET.Plugin.MQTT.Interfaces.MessageRouterConfiguration GetRouterConfiguration()
        {
            var config = CurrentConfiguration.Router ?? new MessageRouterConfiguration();
            return new Admin.NET.Plugin.MQTT.Interfaces.MessageRouterConfiguration
            {
                QueueCapacity = config.QueueCapacity,
                MaxConcurrentHandlers = config.MaxConcurrentHandlers,
                HandlerTimeoutMs = config.HandlerTimeoutMs,
                EnablePerformanceMonitoring = config.EnablePerformanceMonitoring,
                PerformanceMonitoringInterval = config.PerformanceMonitoringInterval,
                EnableMemoryPoolOptimization = config.EnableMemoryPoolOptimization,
                MemoryPoolMaxArrayLength = config.MemoryPoolMaxArrayLength
            };
        }
        
        /// <summary>
        /// 获取设备控制配置
        /// </summary>
        /// <returns>设备控制配置</returns>
        public Admin.NET.Plugin.MQTT.Interfaces.DeviceControlConfiguration GetDeviceControlConfiguration()
        {
            var config = CurrentConfiguration.DeviceControl ?? new DeviceControlConfiguration();
            return new Admin.NET.Plugin.MQTT.Interfaces.DeviceControlConfiguration
            {
                DefaultTimeoutSeconds = config.DefaultTimeoutSeconds,
                ScanTimeoutSeconds = config.ScanTimeoutSeconds,
                MaxConcurrentScans = config.MaxConcurrentScans,
                DeviceCacheExpirationMinutes = config.DeviceCacheExpirationMinutes,
                EnableDeviceStatusCache = config.EnableDeviceStatusCache,
                HeartbeatInterval = config.HeartbeatInterval,
                GatewayName = config.GatewayName,
                InstanceId = config.InstanceId
            };
        }
        
        /// <summary>
        /// 获取性能监控配置
        /// </summary>
        /// <returns>性能监控配置</returns>
        public Admin.NET.Plugin.MQTT.Interfaces.PerformanceMonitoringConfiguration GetPerformanceConfiguration()
        {
            var config = CurrentConfiguration.Performance ?? new PerformanceMonitoringConfiguration();
            return new Admin.NET.Plugin.MQTT.Interfaces.PerformanceMonitoringConfiguration
            {
                Enabled = config.Enabled,
                MonitoringInterval = config.MonitoringInterval,
                EnableMemoryMonitoring = config.EnableMemoryMonitoring,
                MemoryThresholdMB = config.MemoryThresholdMB,
                EnableGarbageCollection = config.EnableGarbageCollection,
                GarbageCollectionInterval = config.GarbageCollectionInterval,
                EnableDetailedStats = config.EnableDetailedStats
            };
        }
        
        /// <summary>
        /// 获取日志配置
        /// </summary>
        /// <returns>日志配置</returns>
        public LoggingConfiguration GetLoggingConfiguration()
        {
            return CurrentConfiguration.Logging ?? new LoggingConfiguration();
        }
        
        /// <summary>
        /// 获取安全配置
        /// </summary>
        /// <returns>安全配置</returns>
        public SecurityConfiguration GetSecurityConfiguration()
        {
            return CurrentConfiguration.Security ?? new SecurityConfiguration();
        }
        
        /// <summary>
        /// 获取重试策略配置
        /// </summary>
        /// <returns>重试策略配置</returns>
        public RetryPolicyConfiguration GetRetryPolicyConfiguration()
        {
            return CurrentConfiguration.RetryPolicy ?? new RetryPolicyConfiguration();
        }
        
        /// <summary>
        /// 获取缓存配置
        /// </summary>
        /// <returns>缓存配置</returns>
        public CacheConfiguration GetCacheConfiguration()
        {
            return CurrentConfiguration.Cache ?? new CacheConfiguration();
        }
        
        /// <summary>
        /// 更新配置（接口实现）
        /// </summary>
        /// <param name="configuration">新配置</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>更新任务</returns>
        public async Task UpdateConfigurationAsync(Admin.NET.Plugin.MQTT.Interfaces.MqttPluginConfiguration configuration, CancellationToken cancellationToken = default)
        {
            if (configuration == null)
                throw new ArgumentNullException(nameof(configuration));

            // 转换为内部配置类型
            var internalConfig = ConvertToInternalConfiguration(configuration);
            var result = await UpdateConfigurationInternalAsync(internalConfig, true, cancellationToken);
            if (!result)
            {
                throw new InvalidOperationException("配置更新失败");
            }
        }

        /// <summary>
        /// 更新配置（内部实现）
        /// </summary>
        /// <param name="configuration">新配置</param>
        /// <param name="saveToFile">是否保存到文件</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>更新任务</returns>
        public async Task<bool> UpdateConfigurationInternalAsync(MqttPluginConfiguration configuration, bool saveToFile = true, CancellationToken cancellationToken = default)
        {
            if (configuration == null)
                throw new ArgumentNullException(nameof(configuration));
            
            try
            {
                // 验证新配置
                var validationResult = await ValidateConfigurationAsync(configuration, cancellationToken);
                if (!validationResult.IsValid)
                {
                    _logger.LogWarning("配置验证失败: {Errors}", string.Join(", ", validationResult.Errors));
                    
                    ValidationFailed?.Invoke(this, new ConfigurationValidationFailedEventArgs
                    {
                        Configuration = configuration,
                        ValidationResult = validationResult,
                        Timestamp = DateTime.UtcNow
                    });
                    
                    return false;
                }
                
                var oldConfiguration = CurrentConfiguration;
                
                // 更新配置
                _configLock.EnterWriteLock();
                try
                {
                    _currentConfiguration = configuration;
                }
                finally
                {
                    _configLock.ExitWriteLock();
                }
                
                // 保存到文件
                if (saveToFile)
                {
                    await SaveConfigurationToFileAsync(configuration, cancellationToken);
                }
                
                // 触发配置变更事件
                ConfigurationChanged?.Invoke(this, new ConfigurationChangedEventArgs
                {
                    OldConfiguration = ConvertToInterfaceConfiguration(oldConfiguration),
                    NewConfiguration = ConvertToInterfaceConfiguration(configuration),
                    ChangedAt = DateTime.UtcNow
                });
                
                _logger.LogInformation("配置已更新");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新配置时发生错误");
                return false;
            }
        }
        
        /// <summary>
        /// 验证配置（同步版本）
        /// </summary>
        /// <param name="configuration">待验证的配置</param>
        /// <returns>验证结果</returns>
        public Admin.NET.Plugin.MQTT.Interfaces.ConfigurationValidationResult ValidateConfiguration(Admin.NET.Plugin.MQTT.Interfaces.MqttPluginConfiguration configuration)
        {
            if (configuration == null)
            {
                return new Admin.NET.Plugin.MQTT.Interfaces.ConfigurationValidationResult
                {
                    IsValid = false,
                    Errors = new List<Admin.NET.Plugin.MQTT.Interfaces.ConfigurationValidationError> 
                    { 
                        new Admin.NET.Plugin.MQTT.Interfaces.ConfigurationValidationError { Message = "配置不能为空" } 
                    }
                };
            }

            // 转换为内部配置类型进行验证
            var internalConfig = ConvertToInternalConfiguration(configuration);
            var internalResult = ValidateConfigurationAsync(internalConfig).GetAwaiter().GetResult();
            
            // 转换验证结果
            return new Admin.NET.Plugin.MQTT.Interfaces.ConfigurationValidationResult
            {
                IsValid = internalResult.IsValid,
                Errors = internalResult.Errors?.Select(e => new Admin.NET.Plugin.MQTT.Interfaces.ConfigurationValidationError
                {
                    Path = e.Path,
                    Message = e.Message,
                    ErrorCode = e.ErrorCode
                }).ToList() ?? new List<Admin.NET.Plugin.MQTT.Interfaces.ConfigurationValidationError>(),
                Warnings = internalResult.Warnings?.Select(w => new Admin.NET.Plugin.MQTT.Interfaces.ConfigurationValidationWarning
                {
                    Path = w.Path,
                    Message = w.Message,
                    WarningCode = w.WarningCode
                }).ToList() ?? new List<Admin.NET.Plugin.MQTT.Interfaces.ConfigurationValidationWarning>()
            };
        }
        
        /// <summary>
        /// 验证配置（异步版本）
        /// </summary>
        /// <param name="configuration">要验证的配置</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>验证结果</returns>
        public async Task<ConfigurationValidationResult> ValidateConfigurationAsync(MqttPluginConfiguration configuration, CancellationToken cancellationToken = default)
        {
            if (configuration == null)
            {
                return new ConfigurationValidationResult
                {
                    IsValid = false,
                    Errors = new List<ConfigurationValidationError> { new ConfigurationValidationError { Message = "配置不能为空" } }
                };
            }
            
            var errors = new List<string>();
            var warnings = new List<string>();
            
            try
            {
                // 验证客户端配置
                ValidateClientConfiguration(configuration.Client, errors, warnings);
                
                // 验证路由器配置
                ValidateRouterConfiguration(configuration.Router, errors, warnings);
                
                // 验证设备控制配置
                ValidateDeviceControlConfiguration(configuration.DeviceControl, errors, warnings);
                
                // 验证性能监控配置
                ValidatePerformanceConfiguration(configuration.Performance, errors, warnings);
                
                // 验证日志配置
                ValidateLoggingConfiguration(configuration.Logging, errors, warnings);
                
                // 验证安全配置
                ValidateSecurityConfiguration(configuration.Security, errors, warnings);
                
                // 验证重试策略配置
                ValidateRetryPolicyConfiguration(configuration.RetryPolicy, errors, warnings);
                
                // 验证缓存配置
                ValidateCacheConfiguration(configuration.Cache, errors, warnings);
                
                // 使用数据注解验证
                var validationContext = new ValidationContext(configuration);
                var validationResults = new List<ValidationResult>();
                Validator.TryValidateObject(configuration, validationContext, validationResults, true);
                
                errors.AddRange(validationResults.Select(r => r.ErrorMessage));
                
                return new ConfigurationValidationResult
                {
                    IsValid = !errors.Any(),
                    Errors = errors.Select(e => new ConfigurationValidationError { Message = e }).ToList(),
                    Warnings = warnings.Select(w => new ConfigurationValidationWarning { Message = w }).ToList()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证配置时发生错误");
                return new ConfigurationValidationResult
                {
                    IsValid = false,
                    Errors = new List<ConfigurationValidationError> { new ConfigurationValidationError { Message = $"验证过程中发生错误: {ex.Message}" } }
                };
            }
        }
        
        /// <summary>
        /// 获取完整配置
        /// </summary>
        /// <returns>完整的MQTT插件配置</returns>
        public Admin.NET.Plugin.MQTT.Interfaces.MqttPluginConfiguration GetConfiguration()
        {
            _configLock.EnterReadLock();
            try
            {
                var currentConfig = _currentConfiguration;
                return new Admin.NET.Plugin.MQTT.Interfaces.MqttPluginConfiguration
                {
                    Client = new Admin.NET.Plugin.MQTT.Interfaces.MqttClientConfiguration
                    {
                        Server = currentConfig.Client.Server,
                        Port = currentConfig.Client.Port,
                        ClientId = currentConfig.Client.ClientId,
                        Username = currentConfig.Client.Username,
                        Password = currentConfig.Client.Password,
                        UseTls = currentConfig.Client.UseTls,
                        CleanSession = currentConfig.Client.CleanSession,
                        KeepAliveInterval = currentConfig.Client.KeepAliveInterval,
                        ConnectTimeout = currentConfig.Client.ConnectTimeout,
                        AutoReconnect = currentConfig.Client.AutoReconnect,
                        ReconnectInterval = currentConfig.Client.ReconnectInterval,
                        MaxReconnectAttempts = currentConfig.Client.MaxReconnectAttempts,
                        DefaultSubscriptions = currentConfig.Client.DefaultSubscriptions?.ToList() ?? new List<string>()
                    },
                    Router = new Admin.NET.Plugin.MQTT.Interfaces.MessageRouterConfiguration
                    {
                        QueueCapacity = currentConfig.Router.QueueCapacity,
                        MaxConcurrentHandlers = currentConfig.Router.MaxConcurrentHandlers,
                        HandlerTimeoutMs = currentConfig.Router.HandlerTimeoutMs,
                        EnablePerformanceMonitoring = currentConfig.Router.EnablePerformanceMonitoring,
                        PerformanceMonitoringInterval = currentConfig.Router.PerformanceMonitoringInterval,
                        EnableMemoryPoolOptimization = currentConfig.Router.EnableMemoryPoolOptimization,
                        MemoryPoolMaxArrayLength = currentConfig.Router.MemoryPoolMaxArrayLength
                    },
                    DeviceControl = new Admin.NET.Plugin.MQTT.Interfaces.DeviceControlConfiguration
                    {
                        DefaultTimeoutSeconds = currentConfig.DeviceControl.DefaultTimeoutSeconds,
                        ScanTimeoutSeconds = currentConfig.DeviceControl.ScanTimeoutSeconds,
                        MaxConcurrentScans = currentConfig.DeviceControl.MaxConcurrentScans,
                        DeviceCacheExpirationMinutes = currentConfig.DeviceControl.DeviceCacheExpirationMinutes,
                        EnableDeviceStatusCache = currentConfig.DeviceControl.EnableDeviceStatusCache,
                        HeartbeatInterval = currentConfig.DeviceControl.HeartbeatInterval,
                        GatewayName = currentConfig.DeviceControl.GatewayName,
                        InstanceId = currentConfig.DeviceControl.InstanceId
                    },
                    Performance = new Admin.NET.Plugin.MQTT.Interfaces.PerformanceMonitoringConfiguration
                    {
                        Enabled = currentConfig.Performance.Enabled,
                        MonitoringInterval = currentConfig.Performance.MonitoringInterval,
                        EnableMemoryMonitoring = currentConfig.Performance.EnableMemoryMonitoring,
                        MemoryThresholdMB = currentConfig.Performance.MemoryThresholdMB,
                        EnableGarbageCollection = currentConfig.Performance.EnableGarbageCollection,
                        GarbageCollectionInterval = currentConfig.Performance.GarbageCollectionInterval,
                        EnableDetailedStats = currentConfig.Performance.EnableDetailedStats
                    }
                };
            }
            finally
            {
                _configLock.ExitReadLock();
            }
        }
        
        /// <summary>
        /// 重载配置
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>重载任务</returns>
        public async Task ReloadConfigurationAsync(CancellationToken cancellationToken = default)
        {
            await _reloadSemaphore.WaitAsync(cancellationToken);
            try
            {
                _logger.LogInformation("开始重载配置...");
                
                var newConfiguration = await LoadConfigurationFromFileAsync(cancellationToken);
                if (newConfiguration == null)
                {
                    _logger.LogWarning("无法从文件加载配置，使用默认配置");
                    newConfiguration = CreateDefaultConfiguration();
                }
                
                var success = await UpdateConfigurationAsync(newConfiguration, false, cancellationToken);
                
                if (success)
                {
                    ConfigurationReloaded?.Invoke(this, new ConfigurationReloadedEventArgs
                    {
                        Configuration = newConfiguration,
                        Timestamp = DateTime.UtcNow
                    });
                    
                    _logger.LogInformation("配置重载成功");
                }
                else
                {
                    _logger.LogWarning("配置重载失败");
                }
                
                // 不返回值，符合接口定义
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重载配置时发生错误");
                // 不返回值，符合接口定义
            }
            finally
            {
                _reloadSemaphore.Release();
            }
        }
        
        /// <summary>
        /// 重置为默认配置
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>重置任务</returns>
        public async Task<bool> ResetToDefaultAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var defaultConfiguration = CreateDefaultConfiguration();
                return await UpdateConfigurationAsync(defaultConfiguration, true, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置为默认配置时发生错误");
                return false;
            }
        }
        
        /// <summary>
        /// 导出配置到文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>导出任务</returns>
        public async Task<bool> ExportConfigurationAsync(string filePath, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));
            
            try
            {
                var configuration = CurrentConfiguration;
                await SaveConfigurationToFileAsync(configuration, filePath, cancellationToken);
                
                _logger.LogInformation("配置已导出到: {FilePath}", filePath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出配置时发生错误: {FilePath}", filePath);
                return false;
            }
        }
        
        /// <summary>
        /// 从文件导入配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>导入任务</returns>
        public async Task<bool> ImportConfigurationAsync(string filePath, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));
            
            if (!File.Exists(filePath))
                throw new FileNotFoundException($"配置文件不存在: {filePath}");
            
            try
            {
                var configuration = await LoadConfigurationFromFileAsync(filePath, cancellationToken);
                if (configuration == null)
                {
                    _logger.LogWarning("无法从文件加载配置: {FilePath}", filePath);
                    return false;
                }
                
                var success = await UpdateConfigurationAsync(configuration, true, cancellationToken);
                
                if (success)
                {
                    _logger.LogInformation("配置已从文件导入: {FilePath}", filePath);
                }
                
                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导入配置时发生错误: {FilePath}", filePath);
                return false;
            }
        }
        
        #endregion
        
        #region 配置转换方法
        
        /// <summary>
        /// 将内部配置转换为接口配置
        /// </summary>
        /// <param name="internalConfig">内部配置</param>
        /// <returns>接口配置</returns>
        private Admin.NET.Plugin.MQTT.Interfaces.MqttPluginConfiguration ConvertToInterfaceConfiguration(Admin.NET.Plugin.MQTT.Core.Models.MqttPluginConfiguration internalConfig)
        {
            if (internalConfig == null)
                return new Admin.NET.Plugin.MQTT.Interfaces.MqttPluginConfiguration();

            return new Admin.NET.Plugin.MQTT.Interfaces.MqttPluginConfiguration
            {
                Client = new Admin.NET.Plugin.MQTT.Interfaces.MqttClientConfiguration
                {
                    Server = internalConfig.Client?.Server ?? "localhost",
                    Port = internalConfig.Client?.Port ?? 1883,
                    ClientId = internalConfig.Client?.ClientId ?? Guid.NewGuid().ToString(),
                    Username = internalConfig.Client?.Username,
                    Password = internalConfig.Client?.Password,
                    UseTls = internalConfig.Client?.UseTls ?? false,
                    CleanSession = internalConfig.Client?.CleanSession ?? true,
                    KeepAliveInterval = internalConfig.Client?.KeepAliveInterval ?? 60,
                    ConnectTimeout = internalConfig.Client?.ConnectTimeout ?? 30,
                    AutoReconnect = internalConfig.Client?.AutoReconnect ?? true,
                    ReconnectInterval = internalConfig.Client?.ReconnectInterval ?? 5,
                    MaxReconnectAttempts = internalConfig.Client?.MaxReconnectAttempts ?? 10
                },
                Router = new Admin.NET.Plugin.MQTT.Interfaces.MessageRouterConfiguration
                {
                    QueueCapacity = internalConfig.Router?.QueueCapacity ?? 10000,
                    MaxConcurrentHandlers = internalConfig.Router?.MaxConcurrentHandlers ?? 10,
                    HandlerTimeoutMs = internalConfig.Router?.HandlerTimeoutMs ?? 30000,
                    EnablePerformanceMonitoring = internalConfig.Router?.EnablePerformanceMonitoring ?? true,
                    PerformanceMonitoringInterval = internalConfig.Router?.PerformanceMonitoringInterval ?? 60,
                    EnableMemoryPoolOptimization = internalConfig.Router?.EnableMemoryPoolOptimization ?? true,
                    MemoryPoolMaxArrayLength = internalConfig.Router?.MemoryPoolMaxArrayLength ?? 1024 * 1024
                },
                DeviceControl = new Admin.NET.Plugin.MQTT.Interfaces.DeviceControlConfiguration
                {
                    DefaultTimeoutSeconds = internalConfig.DeviceControl?.DefaultTimeoutSeconds ?? 30,
                    ScanTimeoutSeconds = internalConfig.DeviceControl?.ScanTimeoutSeconds ?? 60,
                    MaxConcurrentScans = internalConfig.DeviceControl?.MaxConcurrentScans ?? 5,
                    DeviceCacheExpirationMinutes = internalConfig.DeviceControl?.DeviceCacheExpirationMinutes ?? 30,
                    EnableDeviceStatusCache = internalConfig.DeviceControl?.EnableDeviceStatusCache ?? true
                },
                Performance = new Admin.NET.Plugin.MQTT.Interfaces.PerformanceMonitoringConfiguration
                {
                    EnableMonitoring = internalConfig.Performance?.EnableMonitoring ?? true,
                    MetricsCollectionInterval = internalConfig.Performance?.MetricsCollectionInterval ?? 60,
                    MaxMetricsHistorySize = internalConfig.Performance?.MaxMetricsHistorySize ?? 1000,
                    EnableMemoryMonitoring = internalConfig.Performance?.EnableMemoryMonitoring ?? true,
                    EnableCpuMonitoring = internalConfig.Performance?.EnableCpuMonitoring ?? true,
                    EnableNetworkMonitoring = internalConfig.Performance?.EnableNetworkMonitoring ?? true,
                    PerformanceThresholds = internalConfig.Performance?.PerformanceThresholds ?? new Dictionary<string, double>()
                }
            };
        }
        
        /// <summary>
        /// 将接口配置转换为内部配置
        /// </summary>
        /// <param name="interfaceConfig">接口配置</param>
        /// <returns>内部配置</returns>
        private MqttPluginConfiguration ConvertToInternalConfiguration(Admin.NET.Plugin.MQTT.Interfaces.MqttPluginConfiguration interfaceConfig)
        {
            if (interfaceConfig == null)
                return new MqttPluginConfiguration();

            return new MqttPluginConfiguration
            {
                Client = new MqttClientConfiguration
                {
                    Server = interfaceConfig.Client?.Server ?? "localhost",
                    Port = interfaceConfig.Client?.Port ?? 1883,
                    ClientId = interfaceConfig.Client?.ClientId ?? Guid.NewGuid().ToString(),
                    Username = interfaceConfig.Client?.Username,
                    Password = interfaceConfig.Client?.Password,
                    UseTls = interfaceConfig.Client?.UseTls ?? false,
                    CleanSession = interfaceConfig.Client?.CleanSession ?? true,
                    KeepAliveInterval = interfaceConfig.Client?.KeepAliveInterval ?? 60,
                    ConnectTimeout = interfaceConfig.Client?.ConnectTimeout ?? 30,
                    AutoReconnect = interfaceConfig.Client?.AutoReconnect ?? true,
                    ReconnectInterval = interfaceConfig.Client?.ReconnectInterval ?? 5,
                    MaxReconnectAttempts = interfaceConfig.Client?.MaxReconnectAttempts ?? 10
                },
                Router = new MessageRouterConfiguration
                {
                    QueueCapacity = interfaceConfig.Router?.QueueCapacity ?? 10000,
                    MaxConcurrentHandlers = interfaceConfig.Router?.MaxConcurrentHandlers ?? 10,
                    HandlerTimeoutMs = interfaceConfig.Router?.HandlerTimeoutMs ?? 30000,
                    EnablePerformanceMonitoring = interfaceConfig.Router?.EnablePerformanceMonitoring ?? true,
                    PerformanceMonitoringInterval = interfaceConfig.Router?.PerformanceMonitoringInterval ?? 60,
                    EnableMemoryPoolOptimization = interfaceConfig.Router?.EnableMemoryPoolOptimization ?? true,
                    MemoryPoolMaxArrayLength = interfaceConfig.Router?.MemoryPoolMaxArrayLength ?? 1024 * 1024
                },
                DeviceControl = new DeviceControlConfiguration
                {
                    DefaultTimeoutSeconds = interfaceConfig.DeviceControl?.DefaultTimeoutSeconds ?? 30,
                    ScanTimeoutSeconds = interfaceConfig.DeviceControl?.ScanTimeoutSeconds ?? 60,
                    MaxConcurrentScans = interfaceConfig.DeviceControl?.MaxConcurrentScans ?? 5,
                    DeviceCacheExpirationMinutes = interfaceConfig.DeviceControl?.DeviceCacheExpirationMinutes ?? 30,
                    EnableDeviceStatusCache = interfaceConfig.DeviceControl?.EnableDeviceStatusCache ?? true,
                    HeartbeatInterval = interfaceConfig.DeviceControl?.HeartbeatInterval ?? 30,
                    GatewayName = interfaceConfig.DeviceControl?.GatewayName ?? "Gateway",
                    InstanceId = interfaceConfig.DeviceControl?.InstanceId
                },
                Performance = new PerformanceMonitoringConfiguration
                {
                    Enabled = interfaceConfig.Performance?.Enabled ?? true,
                    MonitoringInterval = interfaceConfig.Performance?.MonitoringInterval ?? 60,
                    EnableMemoryMonitoring = interfaceConfig.Performance?.EnableMemoryMonitoring ?? true,
                    MemoryThresholdMB = interfaceConfig.Performance?.MemoryThresholdMB ?? 500,
                    EnableGarbageCollection = interfaceConfig.Performance?.EnableGarbageCollection ?? true,
                    GarbageCollectionInterval = interfaceConfig.Performance?.GarbageCollectionInterval ?? 300,
                    EnableDetailedStats = interfaceConfig.Performance?.EnableDetailedStats ?? false
                }
            };
        }
        
        #endregion
        
        #region 私有方法
        
        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        /// <returns>配置文件路径</returns>
        private string GetConfigurationFilePath()
        {
            var configPath = _configuration["MqttPlugin:ConfigurationFile"];
            if (string.IsNullOrWhiteSpace(configPath))
            {
                configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Plugins", "Admin.NET.Plugin.MQTT", "mqtt-config.json");
            }
            
            // 确保目录存在
            var directory = Path.GetDirectoryName(configPath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
            
            return configPath;
        }
        
        /// <summary>
        /// 初始化文件监视器
        /// </summary>
        private void InitializeFileWatcher()
        {
            try
            {
                if (!File.Exists(_configFilePath))
                {
                    // 创建默认配置文件
                    var defaultConfig = CreateDefaultConfiguration();
                    SaveConfigurationToFileAsync(defaultConfig).GetAwaiter().GetResult();
                }
                
                var directory = Path.GetDirectoryName(_configFilePath);
                var fileName = Path.GetFileName(_configFilePath);
                
                _configFileWatcher = new FileSystemWatcher(directory, fileName)
                {
                    NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.Size,
                    EnableRaisingEvents = true
                };
                
                _configFileWatcher.Changed += OnConfigFileChanged;
                
                _logger.LogDebug("配置文件监视器已启动: {ConfigFile}", _configFilePath);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "初始化配置文件监视器失败: {ConfigFile}", _configFilePath);
            }
        }
        
        /// <summary>
        /// 配置文件变更事件处理
        /// </summary>
        /// <param name="sender">发送者</param>
        /// <param name="e">事件参数</param>
        private async void OnConfigFileChanged(object sender, FileSystemEventArgs e)
        {
            try
            {
                // 延迟一段时间，避免文件正在写入
                await Task.Delay(1000);
                
                _logger.LogInformation("检测到配置文件变更，开始重载配置: {ConfigFile}", e.FullPath);
                
                await ReloadConfigurationAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理配置文件变更时发生错误: {ConfigFile}", e.FullPath);
            }
        }
        
        /// <summary>
        /// 配置变更事件处理
        /// </summary>
        /// <param name="configuration">新配置</param>
        private void OnConfigurationChanged(MqttPluginConfiguration configuration)
        {
            try
            {
                if (configuration != null)
                {
                    var oldConfiguration = CurrentConfiguration;
                    
                    _configLock.EnterWriteLock();
                    try
                    {
                        _currentConfiguration = configuration;
                    }
                    finally
                    {
                        _configLock.ExitWriteLock();
                    }
                    
                    ConfigurationChanged?.Invoke(this, new ConfigurationChangedEventArgs
                    {
                        OldConfiguration = ConvertToInterfaceConfiguration(oldConfiguration),
                        NewConfiguration = ConvertToInterfaceConfiguration(configuration),
                        ChangedAt = DateTime.UtcNow
                    });
                    
                    _logger.LogInformation("配置已通过选项监视器更新");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理配置变更时发生错误");
            }
        }
        
        /// <summary>
        /// 从文件加载配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>配置对象</returns>
        private async Task<MqttPluginConfiguration> LoadConfigurationFromFileAsync(string filePath = null, CancellationToken cancellationToken = default)
        {
            filePath ??= _configFilePath;
            
            if (!File.Exists(filePath))
            {
                _logger.LogWarning("配置文件不存在: {FilePath}", filePath);
                return null;
            }
            
            try
            {
                var json = await File.ReadAllTextAsync(filePath, cancellationToken);
                if (string.IsNullOrWhiteSpace(json))
                {
                    _logger.LogWarning("配置文件为空: {FilePath}", filePath);
                    return null;
                }
                
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    ReadCommentHandling = JsonCommentHandling.Skip,
                    AllowTrailingCommas = true
                };
                
                var configuration = JsonSerializer.Deserialize<MqttPluginConfiguration>(json, options);
                
                _logger.LogDebug("配置已从文件加载: {FilePath}", filePath);
                return configuration;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从文件加载配置时发生错误: {FilePath}", filePath);
                return null;
            }
        }
        
        /// <summary>
        /// 保存配置到文件
        /// </summary>
        /// <param name="configuration">配置对象</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>保存任务</returns>
        private async Task SaveConfigurationToFileAsync(MqttPluginConfiguration configuration, string filePath = null, CancellationToken cancellationToken = default)
        {
            filePath ??= _configFilePath;
            
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };
                
                var json = JsonSerializer.Serialize(configuration, options);
                
                // 确保目录存在
                var directory = Path.GetDirectoryName(filePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
                
                await File.WriteAllTextAsync(filePath, json, cancellationToken);
                
                _logger.LogDebug("配置已保存到文件: {FilePath}", filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存配置到文件时发生错误: {FilePath}", filePath);
                throw;
            }
        }
        
        /// <summary>
        /// 创建默认配置
        /// </summary>
        /// <returns>默认配置</returns>
        private MqttPluginConfiguration CreateDefaultConfiguration()
        {
            return new MqttPluginConfiguration
            {
                Client = new MqttClientConfiguration
                {
                    Server = "localhost",
                    Port = 1883,
                    ClientId = $"mqtt-plugin-{Environment.MachineName}-{Guid.NewGuid():N}"[..32],
                    Username = "",
                    Password = "",
                    CleanSession = true,
                    KeepAliveInterval = 60,
                    ConnectionTimeout = 30,
                    ReconnectInterval = 5,
                    UseTls = false,
                    ValidateServerCertificate = true,
                    EnableHeartbeat = true,
                    HeartbeatInterval = 30,
                    HeartbeatTopic = "heartbeat",
                    WillTopic = "",
                    WillMessage = "offline",
                    WillQos = 0,
                    WillRetain = false
                },
                Router = new MessageRouterConfiguration
                {
                    MaxQueueSize = 10000,
                    MaxConcurrentProcessing = Environment.ProcessorCount * 2,
                    ProcessingThreadCount = Environment.ProcessorCount,
                    ProcessingTimeoutMs = 30000,
                    EnablePriorityQueue = false,
                    PerformanceCollectionInterval = 60,
                    CleanupInterval = 60,
                    DataRetentionHours = 24
                },
                DeviceControl = new DeviceControlConfiguration
                {
                    ScanTimeoutSeconds = 30,
                    CommandTimeoutSeconds = 10,
                    MaxRetryAttempts = 3,
                    RetryDelaySeconds = 2,
                    EnableBatchOperations = true,
                    MaxBatchSize = 100,
                    CacheExpirationMinutes = 30,
                    EnableStatusMonitoring = true,
                    StatusUpdateIntervalSeconds = 60
                },
                Performance = new PerformanceMonitoringConfiguration
                {
                    EnableMetricsCollection = true,
                    MetricsCollectionInterval = 60,
                    MemoryThreshold = 512,
                    CpuThreshold = 80,
                    QueueSizeThreshold = 8000,
                    ResponseTimeThreshold = 5000,
                    EnableAlerting = true,
                    AlertCooldownMinutes = 5,
                    GcTriggerThreshold = 256
                },
                Logging = new LoggingConfiguration
                {
                    LogLevel = LogLevel.Information,
                    EnableFileLogging = true,
                    LogFilePath = "logs/mqtt-plugin.log",
                    MaxLogFileSize = 10,
                    MaxLogFiles = 10,
                    EnableStructuredLogging = true,
                    EnablePerformanceLogging = false,
                    LogMessagePayload = false
                },
                Security = new SecurityConfiguration
                {
                    EnableEncryption = false,
                    EncryptionKey = "",
                    EnableAuthentication = false,
                    AllowedTopics = new List<string> { "*" },
                    DeniedTopics = new List<string>(),
                    AccessControlEnabled = false,
                    AccessRules = new List<AccessRule>()
                },
                RetryPolicy = new RetryPolicyConfiguration
                {
                    MaxRetryAttempts = 3,
                    BaseDelayMs = 1000,
                    MaxDelayMs = 30000,
                    BackoffMultiplier = 2.0,
                    EnableJitter = true,
                    JitterMaxMs = 1000
                },
                Cache = new CacheConfiguration
                {
                    Provider = CacheProviderType.Memory,
                    ConnectionString = "",
                    DefaultExpirationMinutes = 30,
                    MaxCacheSize = 1000,
                    EnableCompression = false,
                    KeyPrefix = "mqtt-plugin"
                },
                Metadata = new PluginMetadata
                {
                    Name = "MQTT Plugin",
                    Version = "2.0.0",
                    Description = "Modern MQTT plugin with enhanced features",
                    Author = "Admin.NET Team",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }
            };
        }
        
        /// <summary>
        /// 验证客户端配置
        /// </summary>
        /// <param name="config">客户端配置</param>
        /// <param name="errors">错误列表</param>
        /// <param name="warnings">警告列表</param>
        private void ValidateClientConfiguration(MqttClientConfiguration config, List<string> errors, List<string> warnings)
        {
            if (config == null)
            {
                errors.Add("客户端配置不能为空");
                return;
            }
            
            if (string.IsNullOrWhiteSpace(config.Server))
                errors.Add("MQTT服务器地址不能为空");
            
            if (config.Port <= 0 || config.Port > 65535)
                errors.Add("MQTT服务器端口必须在1-65535之间");
            
            if (string.IsNullOrWhiteSpace(config.ClientId))
                errors.Add("客户端ID不能为空");
            
            if (config.ClientId?.Length > 23)
                warnings.Add("客户端ID长度超过23个字符，可能不被某些MQTT服务器支持");
            
            if (config.KeepAliveInterval < 10)
                warnings.Add("保持连接间隔过短，建议至少10秒");
            
            if (config.ConnectionTimeout < 5)
                warnings.Add("连接超时时间过短，建议至少5秒");
        }
        
        /// <summary>
        /// 验证路由器配置
        /// </summary>
        /// <param name="config">路由器配置</param>
        /// <param name="errors">错误列表</param>
        /// <param name="warnings">警告列表</param>
        private void ValidateRouterConfiguration(Admin.NET.Plugin.MQTT.Core.Models.MessageRouterConfiguration config, List<string> errors, List<string> warnings)
        {
            if (config == null)
            {
                errors.Add("消息路由器配置不能为空");
                return;
            }
            
            if (config.MaxQueueSize <= 0)
                errors.Add("最大队列大小必须大于0");
            
            if (config.MaxConcurrentProcessing <= 0)
                errors.Add("最大并发处理数必须大于0");
            
            if (config.ProcessingThreadCount <= 0)
                errors.Add("处理线程数必须大于0");
            
            if (config.ProcessingTimeoutMs <= 0)
                errors.Add("处理超时时间必须大于0");
            
            if (config.MaxQueueSize > 100000)
                warnings.Add("队列大小过大，可能导致内存使用过多");
            
            if (config.ProcessingThreadCount > Environment.ProcessorCount * 4)
                warnings.Add("处理线程数过多，可能导致上下文切换开销增大");
        }
        
        /// <summary>
        /// 验证设备控制配置
        /// </summary>
        /// <param name="config">设备控制配置</param>
        /// <param name="errors">错误列表</param>
        /// <param name="warnings">警告列表</param>
        private void ValidateDeviceControlConfiguration(Admin.NET.Plugin.MQTT.Core.Models.DeviceControlConfiguration config, List<string> errors, List<string> warnings)
        {
            if (config == null)
            {
                warnings.Add("设备控制配置为空，将使用默认值");
                return;
            }
            
            if (config.ScanTimeoutSeconds <= 0)
                errors.Add("扫描超时时间必须大于0");
            
            if (config.CommandTimeoutSeconds <= 0)
                errors.Add("命令超时时间必须大于0");
            
            if (config.MaxRetryAttempts < 0)
                errors.Add("最大重试次数不能为负数");
            
            if (config.RetryDelaySeconds <= 0)
                errors.Add("重试延迟时间必须大于0");
            
            if (config.MaxBatchSize <= 0)
                errors.Add("最大批处理大小必须大于0");
        }
        
        /// <summary>
        /// 验证性能监控配置
        /// </summary>
        /// <param name="config">性能监控配置</param>
        /// <param name="errors">错误列表</param>
        /// <param name="warnings">警告列表</param>
        private void ValidatePerformanceConfiguration(Admin.NET.Plugin.MQTT.Core.Models.PerformanceMonitoringConfiguration config, List<string> errors, List<string> warnings)
        {
            if (config == null)
            {
                warnings.Add("性能监控配置为空，将使用默认值");
                return;
            }
            
            if (config.MetricsCollectionInterval <= 0)
                errors.Add("指标收集间隔必须大于0");
            
            if (config.MemoryThreshold <= 0)
                errors.Add("内存阈值必须大于0");
            
            if (config.CpuThreshold <= 0 || config.CpuThreshold > 100)
                errors.Add("CPU阈值必须在0-100之间");
            
            if (config.QueueSizeThreshold <= 0)
                errors.Add("队列大小阈值必须大于0");
            
            if (config.ResponseTimeThreshold <= 0)
                errors.Add("响应时间阈值必须大于0");
        }
        
        /// <summary>
        /// 验证日志配置
        /// </summary>
        /// <param name="config">日志配置</param>
        /// <param name="errors">错误列表</param>
        /// <param name="warnings">警告列表</param>
        private void ValidateLoggingConfiguration(Admin.NET.Plugin.MQTT.Core.Models.LoggingConfiguration config, List<string> errors, List<string> warnings)
        {
            if (config == null)
            {
                warnings.Add("日志配置为空，将使用默认值");
                return;
            }
            
            if (config.EnableFileLogging && string.IsNullOrWhiteSpace(config.LogFilePath))
                errors.Add("启用文件日志时，日志文件路径不能为空");
            
            if (config.MaxLogFileSize <= 0)
                errors.Add("最大日志文件大小必须大于0");
            
            if (config.MaxLogFiles <= 0)
                errors.Add("最大日志文件数量必须大于0");
        }
        
        /// <summary>
        /// 验证安全配置
        /// </summary>
        /// <param name="config">安全配置</param>
        /// <param name="errors">错误列表</param>
        /// <param name="warnings">警告列表</param>
        private void ValidateSecurityConfiguration(SecurityConfiguration config, List<string> errors, List<string> warnings)
        {
            if (config == null)
            {
                warnings.Add("安全配置为空，将使用默认值");
                return;
            }
            
            if (config.EnableEncryption && string.IsNullOrWhiteSpace(config.EncryptionKey))
                errors.Add("启用加密时，加密密钥不能为空");
            
            if (!string.IsNullOrWhiteSpace(config.EncryptionKey) && config.EncryptionKey.Length < 16)
                warnings.Add("加密密钥长度过短，建议至少16个字符");
        }
        
        /// <summary>
        /// 验证重试策略配置
        /// </summary>
        /// <param name="config">重试策略配置</param>
        /// <param name="errors">错误列表</param>
        /// <param name="warnings">警告列表</param>
        private void ValidateRetryPolicyConfiguration(RetryPolicyConfiguration config, List<string> errors, List<string> warnings)
        {
            if (config == null)
            {
                warnings.Add("重试策略配置为空，将使用默认值");
                return;
            }
            
            if (config.MaxRetryAttempts < 0)
                errors.Add("最大重试次数不能为负数");
            
            if (config.BaseDelayMs <= 0)
                errors.Add("基础延迟时间必须大于0");
            
            if (config.MaxDelayMs <= 0)
                errors.Add("最大延迟时间必须大于0");
            
            if (config.MaxDelayMs < config.BaseDelayMs)
                errors.Add("最大延迟时间不能小于基础延迟时间");
            
            if (config.BackoffMultiplier <= 1.0)
                errors.Add("退避倍数必须大于1.0");
        }
        
        /// <summary>
        /// 验证缓存配置
        /// </summary>
        /// <param name="config">缓存配置</param>
        /// <param name="errors">错误列表</param>
        /// <param name="warnings">警告列表</param>
        private void ValidateCacheConfiguration(CacheConfiguration config, List<string> errors, List<string> warnings)
        {
            if (config == null)
            {
                warnings.Add("缓存配置为空，将使用默认值");
                return;
            }
            
            if (config.Provider == CacheProviderType.Redis && string.IsNullOrWhiteSpace(config.ConnectionString))
                errors.Add("使用Redis缓存时，连接字符串不能为空");
            
            if (config.DefaultExpirationMinutes <= 0)
                errors.Add("默认过期时间必须大于0");
            
            if (config.MaxCacheSize <= 0)
                errors.Add("最大缓存大小必须大于0");
            
            if (string.IsNullOrWhiteSpace(config.KeyPrefix))
                warnings.Add("缓存键前缀为空，可能导致键冲突");
        }
        
        /// <summary>
        /// 验证配置回调
        /// </summary>
        /// <param name="state">状态对象</param>
        private async void ValidateConfigurationCallback(object state)
        {
            try
            {
                var validationResult = await ValidateConfigurationAsync(CurrentConfiguration);
                if (!validationResult.IsValid)
                {
                    _logger.LogWarning("定期配置验证失败: {Errors}", string.Join(", ", validationResult.Errors));
                    
                    ValidationFailed?.Invoke(this, new ConfigurationValidationFailedEventArgs
                    {
                        Configuration = CurrentConfiguration,
                        ValidationResult = validationResult,
                        Timestamp = DateTime.UtcNow
                    });
                }
                else if (validationResult.Warnings.Any())
                {
                    _logger.LogInformation("配置验证通过，但有警告: {Warnings}", string.Join(", ", validationResult.Warnings));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "定期配置验证时发生错误");
            }
        }
        
        #endregion
        
        #region IDisposable实现
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed)
                return;
            
            _isDisposed = true;
            
            try
            {
                // 停止定时器
                _validationTimer?.Dispose();
                
                // 停止文件监视器
                if (_configFileWatcher != null)
                {
                    _configFileWatcher.EnableRaisingEvents = false;
                    _configFileWatcher.Changed -= OnConfigFileChanged;
                    _configFileWatcher.Dispose();
                }
                
                // 释放锁
                _configLock?.Dispose();
                _reloadSemaphore?.Dispose();
                
                _logger.LogInformation("MQTT配置管理器已释放资源");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放MQTT配置管理器资源时发生错误");
            }
        }
        
        #endregion
    }
    
    #region 事件参数类
    
    /// <summary>
    /// 配置验证失败事件参数
    /// </summary>
    public class ConfigurationValidationFailedEventArgs : EventArgs
    {
        /// <summary>
        /// 配置
        /// </summary>
        public Admin.NET.Plugin.MQTT.Core.Models.MqttPluginConfiguration Configuration { get; set; }
        
        /// <summary>
        /// 验证结果
        /// </summary>
        public ConfigurationValidationResult ValidationResult { get; set; }
        
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
    
    /// <summary>
    /// 配置重载事件参数
    /// </summary>
    public class ConfigurationReloadedEventArgs : EventArgs
    {
        /// <summary>
        /// 配置
        /// </summary>
        public Admin.NET.Plugin.MQTT.Core.Models.MqttPluginConfiguration Configuration { get; set; }
        
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
    
    #endregion
}