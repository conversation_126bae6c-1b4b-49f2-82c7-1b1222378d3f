using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Admin.NET.Plugin.MQTT.Interfaces;
using Admin.NET.Plugin.MQTT.Models;

namespace Admin.NET.Plugin.MQTT.Services
{
    /// <summary>
    /// MQTT配置管理器实现
    /// 负责配置加载、验证、更新、热重载等功能
    /// </summary>
    public class MqttConfigurationManager : IMqttConfigurationManager, IDisposable
    {
        #region 私有字段
        
        private readonly ILogger<MqttConfigurationManager> _logger;
        private readonly IConfiguration _configuration;
        private readonly IOptionsMonitor<MqttPluginConfiguration> _optionsMonitor;
        private readonly FileSystemWatcher _configFileWatcher;
        
        private MqttPluginConfiguration _currentConfiguration;
        private readonly ReaderWriterLockSlim _configLock;
        private readonly Timer _validationTimer;
        
        private volatile bool _isDisposed;
        private readonly string _configFilePath;
        private readonly SemaphoreSlim _reloadSemaphore;
        
        #endregion
        
        #region 事件
        
        /// <summary>
        /// 配置变更事件
        /// </summary>
        public event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged;
        
        /// <summary>
        /// 配置验证失败事件
        /// </summary>
        public event EventHandler<ConfigurationValidationFailedEventArgs> ValidationFailed;
        
        /// <summary>
        /// 配置重载事件
        /// </summary>
        public event EventHandler<ConfigurationReloadedEventArgs> ConfigurationReloaded;
        
        #endregion
        
        #region 属性
        
        /// <summary>
        /// 当前配置
        /// </summary>
        public MqttPluginConfiguration CurrentConfiguration
        {
            get
            {
                _configLock.EnterReadLock();
                try
                {
                    return _currentConfiguration;
                }
                finally
                {
                    _configLock.ExitReadLock();
                }
            }
        }
        
        /// <summary>
        /// 配置文件路径
        /// </summary>
        public string ConfigurationFilePath => _configFilePath;
        
        #endregion
        
        #region 构造函数
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="configuration">配置提供者</param>
        /// <param name="optionsMonitor">配置监视器</param>
        public MqttConfigurationManager(
            ILogger<MqttConfigurationManager> logger,
            IConfiguration configuration,
            IOptionsMonitor<MqttPluginConfiguration> optionsMonitor)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _optionsMonitor = optionsMonitor ?? throw new ArgumentNullException(nameof(optionsMonitor));
            
            _configLock = new ReaderWriterLockSlim();
            _reloadSemaphore = new SemaphoreSlim(1, 1);
            
            // 获取配置文件路径
            _configFilePath = GetConfigurationFilePath();
            
            // 初始化当前配置
            _currentConfiguration = _optionsMonitor.CurrentValue ?? CreateDefaultConfiguration();
            
            // 订阅配置变更
            _optionsMonitor.OnChange(OnConfigurationChanged);
            
            // 初始化文件监视器
            InitializeFileWatcher();
            
            // 初始化验证定时器
            _validationTimer = new Timer(ValidateConfigurationCallback, null, 
                TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
            
            _logger.LogInformation("MQTT配置管理器已初始化，配置文件: {ConfigFile}", _configFilePath);
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 获取客户端配置
        /// </summary>
        /// <returns>客户端配置</returns>
        public MqttClientConfiguration GetClientConfiguration()
        {
            return CurrentConfiguration.Client ?? new MqttClientConfiguration();
        }
        
        /// <summary>
        /// 获取消息路由器配置
        /// </summary>
        /// <returns>消息路由器配置</returns>
        public MessageRouterConfiguration GetRouterConfiguration()
        {
            return CurrentConfiguration.Router ?? new MessageRouterConfiguration();
        }
        
        /// <summary>
        /// 获取设备控制配置
        /// </summary>
        /// <returns>设备控制配置</returns>
        public DeviceControlConfiguration GetDeviceControlConfiguration()
        {
            return CurrentConfiguration.DeviceControl ?? new DeviceControlConfiguration();
        }
        
        /// <summary>
        /// 获取性能监控配置
        /// </summary>
        /// <returns>性能监控配置</returns>
        public PerformanceMonitoringConfiguration GetPerformanceConfiguration()
        {
            return CurrentConfiguration.Performance ?? new PerformanceMonitoringConfiguration();
        }
        
        /// <summary>
        /// 获取日志配置
        /// </summary>
        /// <returns>日志配置</returns>
        public LoggingConfiguration GetLoggingConfiguration()
        {
            return CurrentConfiguration.Logging ?? new LoggingConfiguration();
        }
        
        /// <summary>
        /// 获取安全配置
        /// </summary>
        /// <returns>安全配置</returns>
        public SecurityConfiguration GetSecurityConfiguration()
        {
            return CurrentConfiguration.Security ?? new SecurityConfiguration();
        }
        
        /// <summary>
        /// 获取重试策略配置
        /// </summary>
        /// <returns>重试策略配置</returns>
        public RetryPolicyConfiguration GetRetryPolicyConfiguration()
        {
            return CurrentConfiguration.RetryPolicy ?? new RetryPolicyConfiguration();
        }
        
        /// <summary>
        /// 获取缓存配置
        /// </summary>
        /// <returns>缓存配置</returns>
        public CacheConfiguration GetCacheConfiguration()
        {
            return CurrentConfiguration.Cache ?? new CacheConfiguration();
        }
        
        /// <summary>
        /// 更新配置
        /// </summary>
        /// <param name="configuration">新配置</param>
        /// <param name="saveToFile">是否保存到文件</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>更新任务</returns>
        public async Task<bool> UpdateConfigurationAsync(MqttPluginConfiguration configuration, bool saveToFile = true, CancellationToken cancellationToken = default)
        {
            if (configuration == null)
                throw new ArgumentNullException(nameof(configuration));
            
            try
            {
                // 验证新配置
                var validationResult = await ValidateConfigurationAsync(configuration, cancellationToken);
                if (!validationResult.IsValid)
                {
                    _logger.LogWarning("配置验证失败: {Errors}", string.Join(", ", validationResult.Errors));
                    
                    ValidationFailed?.Invoke(this, new ConfigurationValidationFailedEventArgs
                    {
                        Configuration = configuration,
                        ValidationResult = validationResult,
                        Timestamp = DateTime.UtcNow
                    });
                    
                    return false;
                }
                
                var oldConfiguration = CurrentConfiguration;
                
                // 更新配置
                _configLock.EnterWriteLock();
                try
                {
                    _currentConfiguration = configuration;
                }
                finally
                {
                    _configLock.ExitWriteLock();
                }
                
                // 保存到文件
                if (saveToFile)
                {
                    await SaveConfigurationToFileAsync(configuration, cancellationToken);
                }
                
                // 触发配置变更事件
                ConfigurationChanged?.Invoke(this, new ConfigurationChangedEventArgs
                {
                    OldConfiguration = oldConfiguration,
                    NewConfiguration = configuration,
                    Timestamp = DateTime.UtcNow
                });
                
                _logger.LogInformation("配置已更新");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新配置时发生错误");
                return false;
            }
        }
        
        /// <summary>
        /// 验证配置
        /// </summary>
        /// <param name="configuration">要验证的配置</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>验证结果</returns>
        public async Task<ConfigurationValidationResult> ValidateConfigurationAsync(MqttPluginConfiguration configuration, CancellationToken cancellationToken = default)
        {
            if (configuration == null)
            {
                return new ConfigurationValidationResult
                {
                    IsValid = false,
                    Errors = new List<string> { "配置不能为空" }
                };
            }
            
            var errors = new List<string>();
            var warnings = new List<string>();
            
            try
            {
                // 验证客户端配置
                ValidateClientConfiguration(configuration.Client, errors, warnings);
                
                // 验证路由器配置
                ValidateRouterConfiguration(configuration.Router, errors, warnings);
                
                // 验证设备控制配置
                ValidateDeviceControlConfiguration(configuration.DeviceControl, errors, warnings);
                
                // 验证性能监控配置
                ValidatePerformanceConfiguration(configuration.Performance, errors, warnings);
                
                // 验证日志配置
                ValidateLoggingConfiguration(configuration.Logging, errors, warnings);
                
                // 验证安全配置
                ValidateSecurityConfiguration(configuration.Security, errors, warnings);
                
                // 验证重试策略配置
                ValidateRetryPolicyConfiguration(configuration.RetryPolicy, errors, warnings);
                
                // 验证缓存配置
                ValidateCacheConfiguration(configuration.Cache, errors, warnings);
                
                // 使用数据注解验证
                var validationContext = new ValidationContext(configuration);
                var validationResults = new List<ValidationResult>();
                Validator.TryValidateObject(configuration, validationContext, validationResults, true);
                
                errors.AddRange(validationResults.Select(r => r.ErrorMessage));
                
                return new ConfigurationValidationResult
                {
                    IsValid = !errors.Any(),
                    Errors = errors,
                    Warnings = warnings
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证配置时发生错误");
                return new ConfigurationValidationResult
                {
                    IsValid = false,
                    Errors = new List<string> { $"验证过程中发生错误: {ex.Message}" }
                };
            }
        }
        
        /// <summary>
        /// 重载配置
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>重载任务</returns>
        public async Task<bool> ReloadConfigurationAsync(CancellationToken cancellationToken = default)
        {
            await _reloadSemaphore.WaitAsync(cancellationToken);
            try
            {
                _logger.LogInformation("开始重载配置...");
                
                var newConfiguration = await LoadConfigurationFromFileAsync(cancellationToken);
                if (newConfiguration == null)
                {
                    _logger.LogWarning("无法从文件加载配置，使用默认配置");
                    newConfiguration = CreateDefaultConfiguration();
                }
                
                var success = await UpdateConfigurationAsync(newConfiguration, false, cancellationToken);
                
                if (success)
                {
                    ConfigurationReloaded?.Invoke(this, new ConfigurationReloadedEventArgs
                    {
                        Configuration = newConfiguration,
                        Timestamp = DateTime.UtcNow
                    });
                    
                    _logger.LogInformation("配置重载成功");
                }
                else
                {
                    _logger.LogWarning("配置重载失败");
                }
                
                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重载配置时发生错误");
                return false;
            }
            finally
            {
                _reloadSemaphore.Release();
            }
        }
        
        /// <summary>
        /// 重置为默认配置
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>重置任务</returns>
        public async Task<bool> ResetToDefaultAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var defaultConfiguration = CreateDefaultConfiguration();
                return await UpdateConfigurationAsync(defaultConfiguration, true, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置为默认配置时发生错误");
                return false;
            }
        }
        
        /// <summary>
        /// 导出配置到文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>导出任务</returns>
        public async Task<bool> ExportConfigurationAsync(string filePath, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));
            
            try
            {
                var configuration = CurrentConfiguration;
                await SaveConfigurationToFileAsync(configuration, filePath, cancellationToken);
                
                _logger.LogInformation("配置已导出到: {FilePath}", filePath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出配置时发生错误: {FilePath}", filePath);
                return false;
            }
        }
        
        /// <summary>
        /// 从文件导入配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>导入任务</returns>
        public async Task<bool> ImportConfigurationAsync(string filePath, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));
            
            if (!File.Exists(filePath))
                throw new FileNotFoundException($"配置文件不存在: {filePath}");
            
            try
            {
                var configuration = await LoadConfigurationFromFileAsync(filePath, cancellationToken);
                if (configuration == null)
                {
                    _logger.LogWarning("无法从文件加载配置: {FilePath}", filePath);
                    return false;
                }
                
                var success = await UpdateConfigurationAsync(configuration, true, cancellationToken);
                
                if (success)
                {
                    _logger.LogInformation("配置已从文件导入: {FilePath}", filePath);
                }
                
                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导入配置时发生错误: {FilePath}", filePath);
                return false;
            }
        }
        
        #endregion
        
        #region 私有方法
        
        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        /// <returns>配置文件路径</returns>
        private string GetConfigurationFilePath()
        {
            var configPath = _configuration["MqttPlugin:ConfigurationFile"];
            if (string.IsNullOrWhiteSpace(configPath))
            {
                configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Plugins", "Admin.NET.Plugin.MQTT", "mqtt-config.json");
            }
            
            // 确保目录存在
            var directory = Path.GetDirectoryName(configPath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
            
            return configPath;
        }
        
        /// <summary>
        /// 初始化文件监视器
        /// </summary>
        private void InitializeFileWatcher()
        {
            try
            {
                if (!File.Exists(_configFilePath))
                {
                    // 创建默认配置文件
                    var defaultConfig = CreateDefaultConfiguration();
                    SaveConfigurationToFileAsync(defaultConfig).GetAwaiter().GetResult();
                }
                
                var directory = Path.GetDirectoryName(_configFilePath);
                var fileName = Path.GetFileName(_configFilePath);
                
                _configFileWatcher = new FileSystemWatcher(directory, fileName)
                {
                    NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.Size,
                    EnableRaisingEvents = true
                };
                
                _configFileWatcher.Changed += OnConfigFileChanged;
                
                _logger.LogDebug("配置文件监视器已启动: {ConfigFile}", _configFilePath);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "初始化配置文件监视器失败: {ConfigFile}", _configFilePath);
            }
        }
        
        /// <summary>
        /// 配置文件变更事件处理
        /// </summary>
        /// <param name="sender">发送者</param>
        /// <param name="e">事件参数</param>
        private async void OnConfigFileChanged(object sender, FileSystemEventArgs e)
        {
            try
            {
                // 延迟一段时间，避免文件正在写入
                await Task.Delay(1000);
                
                _logger.LogInformation("检测到配置文件变更，开始重载配置: {ConfigFile}", e.FullPath);
                
                await ReloadConfigurationAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理配置文件变更时发生错误: {ConfigFile}", e.FullPath);
            }
        }
        
        /// <summary>
        /// 配置变更事件处理
        /// </summary>
        /// <param name="configuration">新配置</param>
        private void OnConfigurationChanged(MqttPluginConfiguration configuration)
        {
            try
            {
                if (configuration != null)
                {
                    var oldConfiguration = CurrentConfiguration;
                    
                    _configLock.EnterWriteLock();
                    try
                    {
                        _currentConfiguration = configuration;
                    }
                    finally
                    {
                        _configLock.ExitWriteLock();
                    }
                    
                    ConfigurationChanged?.Invoke(this, new ConfigurationChangedEventArgs
                    {
                        OldConfiguration = oldConfiguration,
                        NewConfiguration = configuration,
                        Timestamp = DateTime.UtcNow
                    });
                    
                    _logger.LogInformation("配置已通过选项监视器更新");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理配置变更时发生错误");
            }
        }
        
        /// <summary>
        /// 从文件加载配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>配置对象</returns>
        private async Task<MqttPluginConfiguration> LoadConfigurationFromFileAsync(string filePath = null, CancellationToken cancellationToken = default)
        {
            filePath ??= _configFilePath;
            
            if (!File.Exists(filePath))
            {
                _logger.LogWarning("配置文件不存在: {FilePath}", filePath);
                return null;
            }
            
            try
            {
                var json = await File.ReadAllTextAsync(filePath, cancellationToken);
                if (string.IsNullOrWhiteSpace(json))
                {
                    _logger.LogWarning("配置文件为空: {FilePath}", filePath);
                    return null;
                }
                
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    ReadCommentHandling = JsonCommentHandling.Skip,
                    AllowTrailingCommas = true
                };
                
                var configuration = JsonSerializer.Deserialize<MqttPluginConfiguration>(json, options);
                
                _logger.LogDebug("配置已从文件加载: {FilePath}", filePath);
                return configuration;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从文件加载配置时发生错误: {FilePath}", filePath);
                return null;
            }
        }
        
        /// <summary>
        /// 保存配置到文件
        /// </summary>
        /// <param name="configuration">配置对象</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>保存任务</returns>
        private async Task SaveConfigurationToFileAsync(MqttPluginConfiguration configuration, string filePath = null, CancellationToken cancellationToken = default)
        {
            filePath ??= _configFilePath;
            
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };
                
                var json = JsonSerializer.Serialize(configuration, options);
                
                // 确保目录存在
                var directory = Path.GetDirectoryName(filePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
                
                await File.WriteAllTextAsync(filePath, json, cancellationToken);
                
                _logger.LogDebug("配置已保存到文件: {FilePath}", filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存配置到文件时发生错误: {FilePath}", filePath);
                throw;
            }
        }
        
        /// <summary>
        /// 创建默认配置
        /// </summary>
        /// <returns>默认配置</returns>
        private MqttPluginConfiguration CreateDefaultConfiguration()
        {
            return new MqttPluginConfiguration
            {
                Client = new MqttClientConfiguration
                {
                    Server = "localhost",
                    Port = 1883,
                    ClientId = $"mqtt-plugin-{Environment.MachineName}-{Guid.NewGuid():N}"[..32],
                    Username = "",
                    Password = "",
                    CleanSession = true,
                    KeepAliveInterval = 60,
                    ConnectionTimeout = 30,
                    ReconnectInterval = 5,
                    UseTls = false,
                    ValidateServerCertificate = true,
                    EnableHeartbeat = true,
                    HeartbeatInterval = 30,
                    HeartbeatTopic = "heartbeat",
                    WillTopic = "",
                    WillMessage = "offline",
                    WillQos = 0,
                    WillRetain = false
                },
                Router = new MessageRouterConfiguration
                {
                    MaxQueueSize = 10000,
                    MaxConcurrentProcessing = Environment.ProcessorCount * 2,
                    ProcessingThreadCount = Environment.ProcessorCount,
                    ProcessingTimeoutMs = 30000,
                    EnablePriorityQueue = false,
                    PerformanceCollectionInterval = 60,
                    CleanupInterval = 60,
                    DataRetentionHours = 24
                },
                DeviceControl = new DeviceControlConfiguration
                {
                    ScanTimeoutSeconds = 30,
                    CommandTimeoutSeconds = 10,
                    MaxRetryAttempts = 3,
                    RetryDelaySeconds = 2,
                    EnableBatchOperations = true,
                    MaxBatchSize = 100,
                    CacheExpirationMinutes = 30,
                    EnableStatusMonitoring = true,
                    StatusUpdateIntervalSeconds = 60
                },
                Performance = new PerformanceMonitoringConfiguration
                {
                    EnableMetricsCollection = true,
                    MetricsCollectionInterval = 60,
                    MemoryThreshold = 512,
                    CpuThreshold = 80,
                    QueueSizeThreshold = 8000,
                    ResponseTimeThreshold = 5000,
                    EnableAlerting = true,
                    AlertCooldownMinutes = 5,
                    GcTriggerThreshold = 256
                },
                Logging = new LoggingConfiguration
                {
                    LogLevel = LogLevel.Information,
                    EnableFileLogging = true,
                    LogFilePath = "logs/mqtt-plugin.log",
                    MaxLogFileSize = 10,
                    MaxLogFiles = 10,
                    EnableStructuredLogging = true,
                    EnablePerformanceLogging = false,
                    LogMessagePayload = false
                },
                Security = new SecurityConfiguration
                {
                    EnableEncryption = false,
                    EncryptionKey = "",
                    EnableAuthentication = false,
                    AllowedTopics = new List<string> { "*" },
                    DeniedTopics = new List<string>(),
                    AccessControlEnabled = false,
                    AccessRules = new List<AccessRule>()
                },
                RetryPolicy = new RetryPolicyConfiguration
                {
                    MaxRetryAttempts = 3,
                    BaseDelayMs = 1000,
                    MaxDelayMs = 30000,
                    BackoffMultiplier = 2.0,
                    EnableJitter = true,
                    JitterMaxMs = 1000
                },
                Cache = new CacheConfiguration
                {
                    Provider = CacheProviderType.Memory,
                    ConnectionString = "",
                    DefaultExpirationMinutes = 30,
                    MaxCacheSize = 1000,
                    EnableCompression = false,
                    KeyPrefix = "mqtt-plugin"
                },
                Metadata = new PluginMetadata
                {
                    Name = "MQTT Plugin",
                    Version = "2.0.0",
                    Description = "Modern MQTT plugin with enhanced features",
                    Author = "Admin.NET Team",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }
            };
        }
        
        /// <summary>
        /// 验证客户端配置
        /// </summary>
        /// <param name="config">客户端配置</param>
        /// <param name="errors">错误列表</param>
        /// <param name="warnings">警告列表</param>
        private void ValidateClientConfiguration(MqttClientConfiguration config, List<string> errors, List<string> warnings)
        {
            if (config == null)
            {
                errors.Add("客户端配置不能为空");
                return;
            }
            
            if (string.IsNullOrWhiteSpace(config.Server))
                errors.Add("MQTT服务器地址不能为空");
            
            if (config.Port <= 0 || config.Port > 65535)
                errors.Add("MQTT服务器端口必须在1-65535之间");
            
            if (string.IsNullOrWhiteSpace(config.ClientId))
                errors.Add("客户端ID不能为空");
            
            if (config.ClientId?.Length > 23)
                warnings.Add("客户端ID长度超过23个字符，可能不被某些MQTT服务器支持");
            
            if (config.KeepAliveInterval < 10)
                warnings.Add("保持连接间隔过短，建议至少10秒");
            
            if (config.ConnectionTimeout < 5)
                warnings.Add("连接超时时间过短，建议至少5秒");
        }
        
        /// <summary>
        /// 验证路由器配置
        /// </summary>
        /// <param name="config">路由器配置</param>
        /// <param name="errors">错误列表</param>
        /// <param name="warnings">警告列表</param>
        private void ValidateRouterConfiguration(MessageRouterConfiguration config, List<string> errors, List<string> warnings)
        {
            if (config == null)
            {
                errors.Add("消息路由器配置不能为空");
                return;
            }
            
            if (config.MaxQueueSize <= 0)
                errors.Add("最大队列大小必须大于0");
            
            if (config.MaxConcurrentProcessing <= 0)
                errors.Add("最大并发处理数必须大于0");
            
            if (config.ProcessingThreadCount <= 0)
                errors.Add("处理线程数必须大于0");
            
            if (config.ProcessingTimeoutMs <= 0)
                errors.Add("处理超时时间必须大于0");
            
            if (config.MaxQueueSize > 100000)
                warnings.Add("队列大小过大，可能导致内存使用过多");
            
            if (config.ProcessingThreadCount > Environment.ProcessorCount * 4)
                warnings.Add("处理线程数过多，可能导致上下文切换开销增大");
        }
        
        /// <summary>
        /// 验证设备控制配置
        /// </summary>
        /// <param name="config">设备控制配置</param>
        /// <param name="errors">错误列表</param>
        /// <param name="warnings">警告列表</param>
        private void ValidateDeviceControlConfiguration(DeviceControlConfiguration config, List<string> errors, List<string> warnings)
        {
            if (config == null)
            {
                warnings.Add("设备控制配置为空，将使用默认值");
                return;
            }
            
            if (config.ScanTimeoutSeconds <= 0)
                errors.Add("扫描超时时间必须大于0");
            
            if (config.CommandTimeoutSeconds <= 0)
                errors.Add("命令超时时间必须大于0");
            
            if (config.MaxRetryAttempts < 0)
                errors.Add("最大重试次数不能为负数");
            
            if (config.RetryDelaySeconds <= 0)
                errors.Add("重试延迟时间必须大于0");
            
            if (config.MaxBatchSize <= 0)
                errors.Add("最大批处理大小必须大于0");
        }
        
        /// <summary>
        /// 验证性能监控配置
        /// </summary>
        /// <param name="config">性能监控配置</param>
        /// <param name="errors">错误列表</param>
        /// <param name="warnings">警告列表</param>
        private void ValidatePerformanceConfiguration(PerformanceMonitoringConfiguration config, List<string> errors, List<string> warnings)
        {
            if (config == null)
            {
                warnings.Add("性能监控配置为空，将使用默认值");
                return;
            }
            
            if (config.MetricsCollectionInterval <= 0)
                errors.Add("指标收集间隔必须大于0");
            
            if (config.MemoryThreshold <= 0)
                errors.Add("内存阈值必须大于0");
            
            if (config.CpuThreshold <= 0 || config.CpuThreshold > 100)
                errors.Add("CPU阈值必须在0-100之间");
            
            if (config.QueueSizeThreshold <= 0)
                errors.Add("队列大小阈值必须大于0");
            
            if (config.ResponseTimeThreshold <= 0)
                errors.Add("响应时间阈值必须大于0");
        }
        
        /// <summary>
        /// 验证日志配置
        /// </summary>
        /// <param name="config">日志配置</param>
        /// <param name="errors">错误列表</param>
        /// <param name="warnings">警告列表</param>
        private void ValidateLoggingConfiguration(LoggingConfiguration config, List<string> errors, List<string> warnings)
        {
            if (config == null)
            {
                warnings.Add("日志配置为空，将使用默认值");
                return;
            }
            
            if (config.EnableFileLogging && string.IsNullOrWhiteSpace(config.LogFilePath))
                errors.Add("启用文件日志时，日志文件路径不能为空");
            
            if (config.MaxLogFileSize <= 0)
                errors.Add("最大日志文件大小必须大于0");
            
            if (config.MaxLogFiles <= 0)
                errors.Add("最大日志文件数量必须大于0");
        }
        
        /// <summary>
        /// 验证安全配置
        /// </summary>
        /// <param name="config">安全配置</param>
        /// <param name="errors">错误列表</param>
        /// <param name="warnings">警告列表</param>
        private void ValidateSecurityConfiguration(SecurityConfiguration config, List<string> errors, List<string> warnings)
        {
            if (config == null)
            {
                warnings.Add("安全配置为空，将使用默认值");
                return;
            }
            
            if (config.EnableEncryption && string.IsNullOrWhiteSpace(config.EncryptionKey))
                errors.Add("启用加密时，加密密钥不能为空");
            
            if (!string.IsNullOrWhiteSpace(config.EncryptionKey) && config.EncryptionKey.Length < 16)
                warnings.Add("加密密钥长度过短，建议至少16个字符");
        }
        
        /// <summary>
        /// 验证重试策略配置
        /// </summary>
        /// <param name="config">重试策略配置</param>
        /// <param name="errors">错误列表</param>
        /// <param name="warnings">警告列表</param>
        private void ValidateRetryPolicyConfiguration(RetryPolicyConfiguration config, List<string> errors, List<string> warnings)
        {
            if (config == null)
            {
                warnings.Add("重试策略配置为空，将使用默认值");
                return;
            }
            
            if (config.MaxRetryAttempts < 0)
                errors.Add("最大重试次数不能为负数");
            
            if (config.BaseDelayMs <= 0)
                errors.Add("基础延迟时间必须大于0");
            
            if (config.MaxDelayMs <= 0)
                errors.Add("最大延迟时间必须大于0");
            
            if (config.MaxDelayMs < config.BaseDelayMs)
                errors.Add("最大延迟时间不能小于基础延迟时间");
            
            if (config.BackoffMultiplier <= 1.0)
                errors.Add("退避倍数必须大于1.0");
        }
        
        /// <summary>
        /// 验证缓存配置
        /// </summary>
        /// <param name="config">缓存配置</param>
        /// <param name="errors">错误列表</param>
        /// <param name="warnings">警告列表</param>
        private void ValidateCacheConfiguration(CacheConfiguration config, List<string> errors, List<string> warnings)
        {
            if (config == null)
            {
                warnings.Add("缓存配置为空，将使用默认值");
                return;
            }
            
            if (config.Provider == CacheProviderType.Redis && string.IsNullOrWhiteSpace(config.ConnectionString))
                errors.Add("使用Redis缓存时，连接字符串不能为空");
            
            if (config.DefaultExpirationMinutes <= 0)
                errors.Add("默认过期时间必须大于0");
            
            if (config.MaxCacheSize <= 0)
                errors.Add("最大缓存大小必须大于0");
            
            if (string.IsNullOrWhiteSpace(config.KeyPrefix))
                warnings.Add("缓存键前缀为空，可能导致键冲突");
        }
        
        /// <summary>
        /// 验证配置回调
        /// </summary>
        /// <param name="state">状态对象</param>
        private async void ValidateConfigurationCallback(object state)
        {
            try
            {
                var validationResult = await ValidateConfigurationAsync(CurrentConfiguration);
                if (!validationResult.IsValid)
                {
                    _logger.LogWarning("定期配置验证失败: {Errors}", string.Join(", ", validationResult.Errors));
                    
                    ValidationFailed?.Invoke(this, new ConfigurationValidationFailedEventArgs
                    {
                        Configuration = CurrentConfiguration,
                        ValidationResult = validationResult,
                        Timestamp = DateTime.UtcNow
                    });
                }
                else if (validationResult.Warnings.Any())
                {
                    _logger.LogInformation("配置验证通过，但有警告: {Warnings}", string.Join(", ", validationResult.Warnings));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "定期配置验证时发生错误");
            }
        }
        
        #endregion
        
        #region IDisposable实现
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed)
                return;
            
            _isDisposed = true;
            
            try
            {
                // 停止定时器
                _validationTimer?.Dispose();
                
                // 停止文件监视器
                if (_configFileWatcher != null)
                {
                    _configFileWatcher.EnableRaisingEvents = false;
                    _configFileWatcher.Changed -= OnConfigFileChanged;
                    _configFileWatcher.Dispose();
                }
                
                // 释放锁
                _configLock?.Dispose();
                _reloadSemaphore?.Dispose();
                
                _logger.LogInformation("MQTT配置管理器已释放资源");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放MQTT配置管理器资源时发生错误");
            }
        }
        
        #endregion
    }
    
    #region 事件参数类
    
    /// <summary>
    /// 配置变更事件参数
    /// </summary>
    public class ConfigurationChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 旧配置
        /// </summary>
        public MqttPluginConfiguration OldConfiguration { get; set; }
        
        /// <summary>
        /// 新配置
        /// </summary>
        public MqttPluginConfiguration NewConfiguration { get; set; }
        
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
    
    /// <summary>
    /// 配置验证失败事件参数
    /// </summary>
    public class ConfigurationValidationFailedEventArgs : EventArgs
    {
        /// <summary>
        /// 配置
        /// </summary>
        public MqttPluginConfiguration Configuration { get; set; }
        
        /// <summary>
        /// 验证结果
        /// </summary>
        public ConfigurationValidationResult ValidationResult { get; set; }
        
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
    
    /// <summary>
    /// 配置重载事件参数
    /// </summary>
    public class ConfigurationReloadedEventArgs : EventArgs
    {
        /// <summary>
        /// 配置
        /// </summary>
        public MqttPluginConfiguration Configuration { get; set; }
        
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
    
    #endregion
}