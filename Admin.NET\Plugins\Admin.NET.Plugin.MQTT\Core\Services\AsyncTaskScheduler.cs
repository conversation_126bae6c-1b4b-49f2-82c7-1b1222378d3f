// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Channels;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Admin.NET.Plugin.MQTT.Core.Services;

namespace Admin.NET.Plugin.MQTT.Services;

/// <summary>
/// 异步任务调度器实现
/// 提供高效的任务调度和执行机制，支持优先级队列和并发控制
/// </summary>
public class AsyncTaskScheduler : IAsyncTaskScheduler, IDisposable
{
    #region 私有字段
    
    private readonly ILogger<AsyncTaskScheduler> _logger;
    private readonly PerformanceConfiguration _configuration;
    
    // 任务队列
    private readonly Channel<TaskItem> _highPriorityQueue;
    private readonly Channel<TaskItem> _normalPriorityQueue;
    private readonly Channel<TaskItem> _lowPriorityQueue;
    
    // 并发控制
    private readonly SemaphoreSlim _concurrencyLimiter;
    private readonly CancellationTokenSource _cancellationTokenSource;
    
    // 统计信息
    private readonly TaskSchedulerStatistics _statistics;
    
    // 工作线程
    private readonly Task[] _workerTasks;
    
    // 任务跟踪
    private readonly ConcurrentDictionary<string, TaskItem> _runningTasks;
    private readonly ConcurrentDictionary<string, TaskCompletionSource<object>> _taskCompletions;
    
    // 性能监控
    private readonly Timer _performanceTimer;
    
    private volatile bool _disposed;
    
    #endregion
    
    #region 构造函数
    
    /// <summary>
    /// 构造函数 - 初始化异步任务调度器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="configuration">性能配置</param>
    public AsyncTaskScheduler(ILogger<AsyncTaskScheduler> logger, PerformanceConfiguration configuration)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        
        // 初始化队列
        var highPriorityOptions = new BoundedChannelOptions(_configuration.HighPriorityQueueCapacity)
        {
            FullMode = BoundedChannelFullMode.Wait,
            SingleReader = false,
            SingleWriter = false
        };
        
        var normalPriorityOptions = new BoundedChannelOptions(_configuration.NormalPriorityQueueCapacity)
        {
            FullMode = BoundedChannelFullMode.Wait,
            SingleReader = false,
            SingleWriter = false
        };
        
        var lowPriorityOptions = new BoundedChannelOptions(_configuration.LowPriorityQueueCapacity)
        {
            FullMode = BoundedChannelFullMode.Wait,
            SingleReader = false,
            SingleWriter = false
        };
        
        _highPriorityQueue = Channel.CreateBounded<TaskItem>(highPriorityOptions);
        _normalPriorityQueue = Channel.CreateBounded<TaskItem>(normalPriorityOptions);
        _lowPriorityQueue = Channel.CreateBounded<TaskItem>(lowPriorityOptions);
        
        // 初始化并发控制
        _concurrencyLimiter = new SemaphoreSlim(_configuration.MaxConcurrentTasks, _configuration.MaxConcurrentTasks);
        _cancellationTokenSource = new CancellationTokenSource();
        
        // 初始化统计信息
        _statistics = new TaskSchedulerStatistics();
        
        // 初始化任务跟踪
        _runningTasks = new ConcurrentDictionary<string, TaskItem>();
        _taskCompletions = new ConcurrentDictionary<string, TaskCompletionSource<object>>();
        
        // 启动工作线程
        _workerTasks = new Task[_configuration.MaxConcurrentTasks];
        for (int i = 0; i < _workerTasks.Length; i++)
        {
            _workerTasks[i] = Task.Run(WorkerLoop, _cancellationTokenSource.Token);
        }
        
        // 启动性能监控定时器
        var performanceInterval = TimeSpan.FromMinutes(_configuration.PerformanceReportInterval);
        _performanceTimer = new Timer(PerformanceCallback, null, performanceInterval, performanceInterval);
        
        _logger.LogInformation("异步任务调度器已初始化，最大并发数: {MaxConcurrency}，工作线程数: {WorkerCount}", 
            _configuration.MaxConcurrentTasks, _workerTasks.Length);
    }
    
    #endregion
    
    #region 公共方法
    
    /// <summary>
    /// 调度任务
    /// </summary>
    /// <param name="taskFunc">任务函数</param>
    /// <param name="priority">任务优先级</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务ID</returns>
    public async Task<string> ScheduleAsync(Func<CancellationToken, Task> taskFunc, 
        Admin.NET.Plugin.MQTT.Core.Services.TaskPriority priority = Admin.NET.Plugin.MQTT.Core.Services.TaskPriority.Normal, 
        CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(AsyncTaskScheduler));
        
        if (taskFunc == null)
            throw new ArgumentNullException(nameof(taskFunc));
        
        var taskId = Guid.NewGuid().ToString("N");
        var taskItem = new TaskItem
        {
            Id = taskId,
            TaskFunc = taskFunc,
            Priority = priority,
            CreatedAt = DateTime.UtcNow,
            CancellationToken = CancellationTokenSource.CreateLinkedTokenSource(
                cancellationToken, _cancellationTokenSource.Token).Token
        };
        
        // 选择合适的队列
        var queue = GetQueueByPriority(priority);
        
        try
        {
            await queue.Writer.WriteAsync(taskItem, cancellationToken).ConfigureAwait(false);
            
            // 更新统计信息
            Interlocked.Increment(ref _statistics._totalScheduled);
            UpdateQueueLength(priority, 1);
            
            _logger.LogTrace("任务已调度: {TaskId}，优先级: {Priority}", taskId, priority);
            
            return taskId;
        }
        catch (Exception ex)
        {
            Interlocked.Increment(ref _statistics._totalFailed);
            _logger.LogError(ex, "调度任务失败: {TaskId}，优先级: {Priority}", taskId, priority);
            throw;
        }
    }
    
    /// <summary>
    /// 调度带返回值的任务
    /// </summary>
    /// <typeparam name="T">返回值类型</typeparam>
    /// <param name="taskFunc">任务函数</param>
    /// <param name="priority">任务优先级</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务结果</returns>
    public async Task<T> ScheduleAsync<T>(Func<CancellationToken, Task<T>> taskFunc, 
        Admin.NET.Plugin.MQTT.Core.Services.TaskPriority priority = Admin.NET.Plugin.MQTT.Core.Services.TaskPriority.Normal, 
        CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(AsyncTaskScheduler));
        
        if (taskFunc == null)
            throw new ArgumentNullException(nameof(taskFunc));
        
        var taskId = Guid.NewGuid().ToString("N");
        var tcs = new TaskCompletionSource<object>();
        _taskCompletions.TryAdd(taskId, tcs);
        
        var taskItem = new TaskItem
        {
            Id = taskId,
            TaskFunc = async ct =>
            {
                try
                {
                    var result = await taskFunc(ct).ConfigureAwait(false);
                    if (_taskCompletions.TryRemove(taskId, out var completion))
                    {
                        completion.SetResult(result);
                    }
                }
                catch (Exception ex)
                {
                    if (_taskCompletions.TryRemove(taskId, out var completion))
                    {
                        completion.SetException(ex);
                    }
                }
            },
            Priority = priority,
            CreatedAt = DateTime.UtcNow,
            CancellationToken = CancellationTokenSource.CreateLinkedTokenSource(
                cancellationToken, _cancellationTokenSource.Token).Token
        };
        
        // 选择合适的队列
        var queue = GetQueueByPriority(priority);
        
        try
        {
            await queue.Writer.WriteAsync(taskItem, cancellationToken).ConfigureAwait(false);
            
            // 更新统计信息
            Interlocked.Increment(ref _statistics._totalScheduled);
            UpdateQueueLength(priority, 1);
            
            _logger.LogTrace("带返回值任务已调度: {TaskId}，优先级: {Priority}", taskId, priority);
            
            // 等待任务完成
            var result = await tcs.Task.ConfigureAwait(false);
            return (T)result;
        }
        catch (Exception ex)
        {
            Interlocked.Increment(ref _statistics._totalFailed);
            _taskCompletions.TryRemove(taskId, out _);
            _logger.LogError(ex, "调度带返回值任务失败: {TaskId}，优先级: {Priority}", taskId, priority);
            throw;
        }
    }
    
    /// <summary>
    /// 取消任务
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <returns>是否成功取消</returns>
    public bool CancelTask(string taskId)
    {
        if (string.IsNullOrEmpty(taskId))
            return false;
        
        if (_runningTasks.TryGetValue(taskId, out var taskItem))
        {
            try
            {
                taskItem.CancellationTokenSource?.Cancel();
                _runningTasks.TryRemove(taskId, out _);
                
                Interlocked.Increment(ref _statistics._totalCancelled);
                
                _logger.LogDebug("任务已取消: {TaskId}", taskId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取消任务失败: {TaskId}", taskId);
                return false;
            }
        }
        
        return false;
    }
    
    /// <summary>
    /// 获取任务状态
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <returns>任务状态</returns>
    public TaskStatus GetTaskStatus(string taskId)
    {
        if (string.IsNullOrEmpty(taskId))
            return TaskStatus.Unknown;
        
        if (_runningTasks.ContainsKey(taskId))
            return TaskStatus.Running;
        
        if (_taskCompletions.ContainsKey(taskId))
            return TaskStatus.Pending;
        
        return TaskStatus.Unknown;
    }
    
    /// <summary>
    /// 获取统计信息
    /// </summary>
    /// <returns>任务调度器统计信息</returns>
    public TaskSchedulerStatistics GetStatistics()
    {
        return new TaskSchedulerStatistics
        {
            TotalScheduled = _statistics.TotalScheduled,
            TotalCompleted = _statistics.TotalCompleted,
            TotalFailed = _statistics.TotalFailed,
            TotalCancelled = _statistics.TotalCancelled,
            CurrentRunning = _statistics.CurrentRunning,
            HighPriorityQueueLength = _statistics.HighPriorityQueueLength,
            NormalPriorityQueueLength = _statistics.NormalPriorityQueueLength,
            LowPriorityQueueLength = _statistics.LowPriorityQueueLength,
            AverageExecutionTime = _statistics.AverageExecutionTime
        };
    }
    
    /// <summary>
    /// 等待所有任务完成
    /// </summary>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否所有任务都完成</returns>
    public async Task<bool> WaitForAllTasksAsync(TimeSpan timeout, CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        
        while (DateTime.UtcNow - startTime < timeout && !cancellationToken.IsCancellationRequested)
        {
            if (_statistics.CurrentRunning == 0 && 
                _statistics.HighPriorityQueueLength == 0 && 
                _statistics.NormalPriorityQueueLength == 0 && 
                _statistics.LowPriorityQueueLength == 0)
            {
                return true;
            }
            
            await Task.Delay(100, cancellationToken).ConfigureAwait(false);
        }
        
        return false;
    }
    
    #endregion
    
    #region 私有方法
    
    /// <summary>
    /// 根据优先级获取队列
    /// </summary>
    /// <param name="priority">任务优先级</param>
    /// <returns>对应的队列</returns>
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    private Channel<TaskItem> GetQueueByPriority(Admin.NET.Plugin.MQTT.Core.Services.TaskPriority priority)
    {
        return priority switch
        {
            Admin.NET.Plugin.MQTT.Core.Services.TaskPriority.High => _highPriorityQueue,
            Admin.NET.Plugin.MQTT.Core.Services.TaskPriority.Normal => _normalPriorityQueue,
            Admin.NET.Plugin.MQTT.Core.Services.TaskPriority.Low => _lowPriorityQueue,
            _ => _normalPriorityQueue
        };
    }
    
    /// <summary>
    /// 更新队列长度统计
    /// </summary>
    /// <param name="priority">任务优先级</param>
    /// <param name="delta">变化量</param>
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    private void UpdateQueueLength(Admin.NET.Plugin.MQTT.Core.Services.TaskPriority priority, int delta)
    {
        switch (priority)
        {
            case Admin.NET.Plugin.MQTT.Core.Services.TaskPriority.High:
                Interlocked.Add(ref _statistics._highPriorityQueueLength, delta);
                break;
            case Admin.NET.Plugin.MQTT.Core.Services.TaskPriority.Normal:
                Interlocked.Add(ref _statistics._normalPriorityQueueLength, delta);
                break;
            case Admin.NET.Plugin.MQTT.Core.Services.TaskPriority.Low:
                Interlocked.Add(ref _statistics._lowPriorityQueueLength, delta);
                break;
        }
    }
    
    /// <summary>
    /// 工作线程循环
    /// </summary>
    private async Task WorkerLoop()
    {
        var cancellationToken = _cancellationTokenSource.Token;
        
        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                // 按优先级顺序尝试获取任务
                var taskItem = await GetNextTaskAsync(cancellationToken).ConfigureAwait(false);
                
                if (taskItem != null)
                {
                    await ExecuteTaskAsync(taskItem).ConfigureAwait(false);
                }
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "工作线程异常");
                await Task.Delay(1000, cancellationToken).ConfigureAwait(false);
            }
        }
    }
    
    /// <summary>
    /// 获取下一个任务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务项</returns>
    private async Task<TaskItem> GetNextTaskAsync(CancellationToken cancellationToken)
    {
        // 优先处理高优先级任务
        if (_highPriorityQueue.Reader.TryRead(out var highPriorityTask))
        {
            UpdateQueueLength(Admin.NET.Plugin.MQTT.Core.Services.TaskPriority.High, -1);
            return highPriorityTask;
        }
        
        // 然后处理普通优先级任务
        if (_normalPriorityQueue.Reader.TryRead(out var normalPriorityTask))
        {
            UpdateQueueLength(Admin.NET.Plugin.MQTT.Core.Services.TaskPriority.Normal, -1);
            return normalPriorityTask;
        }
        
        // 最后处理低优先级任务
        if (_lowPriorityQueue.Reader.TryRead(out var lowPriorityTask))
        {
            UpdateQueueLength(Admin.NET.Plugin.MQTT.Core.Services.TaskPriority.Low, -1);
            return lowPriorityTask;
        }
        
        // 如果没有任务，等待任何队列有新任务
        var readTasks = new Task<bool>[]
        {
            _highPriorityQueue.Reader.WaitToReadAsync(cancellationToken).AsTask(),
            _normalPriorityQueue.Reader.WaitToReadAsync(cancellationToken).AsTask(),
            _lowPriorityQueue.Reader.WaitToReadAsync(cancellationToken).AsTask()
        };
        
        await Task.WhenAny(readTasks).ConfigureAwait(false);
        
        // 再次尝试获取任务
        return await GetNextTaskAsync(cancellationToken).ConfigureAwait(false);
    }
    
    /// <summary>
    /// 执行任务
    /// </summary>
    /// <param name="taskItem">任务项</param>
    private async Task ExecuteTaskAsync(TaskItem taskItem)
    {
        await _concurrencyLimiter.WaitAsync(taskItem.CancellationToken).ConfigureAwait(false);
        
        try
        {
            // 添加到运行任务列表
            _runningTasks.TryAdd(taskItem.Id, taskItem);
            Interlocked.Increment(ref _statistics._currentRunning);
            
            var startTime = DateTime.UtcNow;
            
            _logger.LogTrace("开始执行任务: {TaskId}，优先级: {Priority}", taskItem.Id, taskItem.Priority);
            
            // 执行任务
            await taskItem.TaskFunc(taskItem.CancellationToken).ConfigureAwait(false);
            
            var executionTime = DateTime.UtcNow - startTime;
            
            // 更新统计信息
            Interlocked.Increment(ref _statistics._totalCompleted);
            UpdateAverageExecutionTime(executionTime);
            
            _logger.LogTrace("任务执行完成: {TaskId}，耗时: {Duration}ms", taskItem.Id, executionTime.TotalMilliseconds);
        }
        catch (OperationCanceledException)
        {
            Interlocked.Increment(ref _statistics._totalCancelled);
            _logger.LogDebug("任务被取消: {TaskId}", taskItem.Id);
        }
        catch (Exception ex)
        {
            Interlocked.Increment(ref _statistics._totalFailed);
            _logger.LogError(ex, "任务执行失败: {TaskId}", taskItem.Id);
        }
        finally
        {
            // 从运行任务列表中移除
            _runningTasks.TryRemove(taskItem.Id, out _);
            Interlocked.Decrement(ref _statistics._currentRunning);
            
            _concurrencyLimiter.Release();
        }
    }
    
    /// <summary>
    /// 更新平均执行时间
    /// </summary>
    /// <param name="executionTime">执行时间</param>
    private void UpdateAverageExecutionTime(TimeSpan executionTime)
    {
        var currentAverage = _statistics.AverageExecutionTime;
        var totalCompleted = _statistics.TotalCompleted;
        
        if (totalCompleted > 1)
        {
            var newAverage = TimeSpan.FromTicks(
                (currentAverage.Ticks * (totalCompleted - 1) + executionTime.Ticks) / totalCompleted);
            
            Interlocked.Exchange(ref _statistics._averageExecutionTimeTicks, newAverage.Ticks);
        }
        else
        {
            Interlocked.Exchange(ref _statistics._averageExecutionTimeTicks, executionTime.Ticks);
        }
    }
    
    /// <summary>
    /// 性能监控回调
    /// </summary>
    /// <param name="state">状态</param>
    private void PerformanceCallback(object state)
    {
        if (_disposed)
            return;
        
        try
        {
            var stats = GetStatistics();
            
            // 检查队列长度是否超过阈值
            var totalQueueLength = stats.HighPriorityQueueLength + 
                                 stats.NormalPriorityQueueLength + 
                                 stats.LowPriorityQueueLength;
            
            if (totalQueueLength > _configuration.TaskQueueLengthThreshold)
            {
                _logger.LogWarning("任务队列长度超过阈值: {CurrentLength}/{Threshold}，" +
                    "高优先级: {High}，普通: {Normal}，低优先级: {Low}", 
                    totalQueueLength, _configuration.TaskQueueLengthThreshold,
                    stats.HighPriorityQueueLength, stats.NormalPriorityQueueLength, stats.LowPriorityQueueLength);
            }
            
            // 记录性能指标
            _logger.LogDebug("任务调度器性能报告 - 总调度: {TotalScheduled}，已完成: {TotalCompleted}，" +
                "失败: {TotalFailed}，取消: {TotalCancelled}，当前运行: {CurrentRunning}，" +
                "平均执行时间: {AverageTime}ms", 
                stats.TotalScheduled, stats.TotalCompleted, stats.TotalFailed, 
                stats.TotalCancelled, stats.CurrentRunning, stats.AverageExecutionTime.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "性能监控回调失败");
        }
    }
    
    #endregion
    
    #region IDisposable实现
    
    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;
            
            // 停止性能监控定时器
            _performanceTimer?.Dispose();
            
            // 取消所有任务
            _cancellationTokenSource.Cancel();
            
            // 关闭队列
            _highPriorityQueue.Writer.Complete();
            _normalPriorityQueue.Writer.Complete();
            _lowPriorityQueue.Writer.Complete();
            
            // 等待工作线程完成
            try
            {
                Task.WaitAll(_workerTasks, TimeSpan.FromSeconds(10));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "等待工作线程完成失败");
            }
            
            // 释放资源
            _concurrencyLimiter?.Dispose();
            _cancellationTokenSource?.Dispose();
            
            // 清理任务跟踪
            _runningTasks.Clear();
            _taskCompletions.Clear();
            
            var stats = GetStatistics();
            _logger.LogInformation("异步任务调度器已释放资源，总调度: {TotalScheduled}，已完成: {TotalCompleted}，" +
                "失败: {TotalFailed}，取消: {TotalCancelled}", 
                stats.TotalScheduled, stats.TotalCompleted, stats.TotalFailed, stats.TotalCancelled);
        }
    }
    
    #endregion
}

/// <summary>
/// 任务项
/// 表示一个待执行的任务
/// </summary>
internal class TaskItem
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string Id { get; set; } = string.Empty;
    
    /// <summary>
    /// 任务函数
    /// </summary>
    public Func<CancellationToken, Task> TaskFunc { get; set; } = null!;
    
    /// <summary>
    /// 任务优先级
    /// </summary>
    public Admin.NET.Plugin.MQTT.Core.Services.TaskPriority Priority { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
    
    /// <summary>
    /// 取消令牌
    /// </summary>
    public CancellationToken CancellationToken { get; set; }
    
    /// <summary>
    /// 取消令牌源
    /// </summary>
    public CancellationTokenSource CancellationTokenSource { get; set; } = null!;
}

/// <summary>
/// 任务状态枚举
/// 定义任务的各种状态
/// </summary>
public enum TaskStatus
{
    /// <summary>
    /// 未知状态
    /// </summary>
    Unknown,
    
    /// <summary>
    /// 等待中
    /// </summary>
    Pending,
    
    /// <summary>
    /// 运行中
    /// </summary>
    Running,
    
    /// <summary>
    /// 已完成
    /// </summary>
    Completed,
    
    /// <summary>
    /// 已失败
    /// </summary>
    Failed,
    
    /// <summary>
    /// 已取消
    /// </summary>
    Cancelled
}

/// <summary>
/// 任务调度器统计信息
/// 记录任务调度器的运行状态和性能指标
/// </summary>
public class TaskSchedulerStatistics
{
    internal long _totalScheduled;
    internal long _totalCompleted;
    internal long _totalFailed;
    internal long _totalCancelled;
    internal long _currentRunning;
    internal long _highPriorityQueueLength;
    internal long _normalPriorityQueueLength;
    internal long _lowPriorityQueueLength;
    internal long _averageExecutionTimeTicks;
    
    /// <summary>
    /// 总调度任务数
    /// </summary>
    public long TotalScheduled => Interlocked.Read(ref _totalScheduled);
    
    /// <summary>
    /// 总完成任务数
    /// </summary>
    public long TotalCompleted => Interlocked.Read(ref _totalCompleted);
    
    /// <summary>
    /// 总失败任务数
    /// </summary>
    public long TotalFailed => Interlocked.Read(ref _totalFailed);
    
    /// <summary>
    /// 总取消任务数
    /// </summary>
    public long TotalCancelled => Interlocked.Read(ref _totalCancelled);
    
    /// <summary>
    /// 当前运行任务数
    /// </summary>
    public long CurrentRunning => Interlocked.Read(ref _currentRunning);
    
    /// <summary>
    /// 高优先级队列长度
    /// </summary>
    public long HighPriorityQueueLength => Interlocked.Read(ref _highPriorityQueueLength);
    
    /// <summary>
    /// 普通优先级队列长度
    /// </summary>
    public long NormalPriorityQueueLength => Interlocked.Read(ref _normalPriorityQueueLength);
    
    /// <summary>
    /// 低优先级队列长度
    /// </summary>
    public long LowPriorityQueueLength => Interlocked.Read(ref _lowPriorityQueueLength);
    
    /// <summary>
    /// 平均执行时间
    /// </summary>
    public TimeSpan AverageExecutionTime => TimeSpan.FromTicks(Interlocked.Read(ref _averageExecutionTimeTicks));
    
    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate
    {
        get
        {
            var total = TotalCompleted + TotalFailed;
            return total > 0 ? (double)TotalCompleted / total : 0;
        }
    }
    
    /// <summary>
    /// 吞吐量（每秒完成任务数）
    /// </summary>
    public double Throughput { get; set; }
}