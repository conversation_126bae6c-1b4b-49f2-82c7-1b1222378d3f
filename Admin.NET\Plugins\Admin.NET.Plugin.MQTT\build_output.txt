﻿  姝ｅ湪纭畾瑕佽繕鍘熺殑椤圭洰鈥?
  鎵€鏈夐」鐩潎鏄渶鏂扮殑锛屾棤娉曡繕鍘熴€?
  Admin.NET.Core -> D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Admin.NET.Core\bin\Debug\net9.0\Admin.NET.Core.dll
  Admin.NET.Core -> D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Admin.NET.Core\bin\Debug\net8.0\Admin.NET.Core.dll
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Controller\MqttController.cs(112,48): error CS1061: 鈥淒eviceControlService鈥濇湭鍖呭惈鈥淕etOnlineDevicesAsync鈥濈殑瀹氫箟锛屽苟涓旀壘涓嶅埌鍙帴鍙楃涓€涓€淒eviceControlService鈥濈被鍨嬪弬鏁扮殑鍙闂墿灞曟柟娉曗€淕etOnlineDevicesAsync鈥?鏄惁缂哄皯 using 鎸囦护鎴栫▼搴忛泦寮曠敤?) [D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\MqttClientManager.cs(97,59): warning CS0618: 鈥淢qttClientOptionsBuilderTlsParameters鈥濆凡杩囨椂:鈥淯se methods from MqttClientOptionsBuilder instead.鈥?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\MqttClientManager.cs(97,33): warning CS0618: 鈥淢qttClientOptionsBuilder.WithTls(MqttClientOptionsBuilderTlsParameters)鈥濆凡杩囨椂:鈥淯se WithTlsOptions(... configure) instead.鈥?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(129,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(160,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Startup.cs(169,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(191,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(222,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(253,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(287,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(318,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(349,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\MqttClientManager.cs(377,55): warning CS0618: 鈥淢qttApplicationMessage.Payload鈥濆凡杩囨椂:鈥淯se PayloadSegment instead. This property will be removed in a future release.鈥?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(367,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(386,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(404,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\MqttMessageRouter.cs(172,51): warning CS0618: 鈥淢qttApplicationMessage.Payload鈥濆凡杩囨椂:鈥淯se PayloadSegment instead. This property will be removed in a future release.鈥?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Controller\MqttController.cs(112,48): error CS1061: 鈥淒eviceControlService鈥濇湭鍖呭惈鈥淕etOnlineDevicesAsync鈥濈殑瀹氫箟锛屽苟涓旀壘涓嶅埌鍙帴鍙楃涓€涓€淒eviceControlService鈥濈被鍨嬪弬鏁扮殑鍙闂墿灞曟柟娉曗€淕etOnlineDevicesAsync鈥?鏄惁缂哄皯 using 鎸囦护鎴栫▼搴忛泦寮曠敤?) [D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(129,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(160,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(191,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Startup.cs(169,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(222,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\MqttMessageRouter.cs(172,51): warning CS0618: 鈥淢qttApplicationMessage.Payload鈥濆凡杩囨椂:鈥淯se PayloadSegment instead. This property will be removed in a future release.鈥?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(253,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(287,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(318,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(349,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(367,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(386,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\MqttClientManager.cs(97,59): warning CS0618: 鈥淢qttClientOptionsBuilderTlsParameters鈥濆凡杩囨椂:鈥淯se methods from MqttClientOptionsBuilder instead.鈥?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\MqttClientManager.cs(97,33): warning CS0618: 鈥淢qttClientOptionsBuilder.WithTls(MqttClientOptionsBuilderTlsParameters)鈥濆凡杩囨椂:鈥淯se WithTlsOptions(... configure) instead.鈥?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(404,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\MqttClientManager.cs(377,55): warning CS0618: 鈥淢qttApplicationMessage.Payload鈥濆凡杩囨椂:鈥淯se PayloadSegment instead. This property will be removed in a future release.鈥?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]

鐢熸垚澶辫触銆?

D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\MqttClientManager.cs(97,59): warning CS0618: 鈥淢qttClientOptionsBuilderTlsParameters鈥濆凡杩囨椂:鈥淯se methods from MqttClientOptionsBuilder instead.鈥?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\MqttClientManager.cs(97,33): warning CS0618: 鈥淢qttClientOptionsBuilder.WithTls(MqttClientOptionsBuilderTlsParameters)鈥濆凡杩囨椂:鈥淯se WithTlsOptions(... configure) instead.鈥?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(129,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(160,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Startup.cs(169,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(191,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(222,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(253,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(287,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(318,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(349,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\MqttClientManager.cs(377,55): warning CS0618: 鈥淢qttApplicationMessage.Payload鈥濆凡杩囨椂:鈥淯se PayloadSegment instead. This property will be removed in a future release.鈥?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(367,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(386,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(404,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\MqttMessageRouter.cs(172,51): warning CS0618: 鈥淢qttApplicationMessage.Payload鈥濆凡杩囨椂:鈥淯se PayloadSegment instead. This property will be removed in a future release.鈥?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(129,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(160,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(191,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Startup.cs(169,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(222,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\MqttMessageRouter.cs(172,51): warning CS0618: 鈥淢qttApplicationMessage.Payload鈥濆凡杩囨椂:鈥淯se PayloadSegment instead. This property will be removed in a future release.鈥?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(253,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(287,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(318,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(349,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(367,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(386,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\MqttClientManager.cs(97,59): warning CS0618: 鈥淢qttClientOptionsBuilderTlsParameters鈥濆凡杩囨椂:鈥淯se methods from MqttClientOptionsBuilder instead.鈥?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\MqttClientManager.cs(97,33): warning CS0618: 鈥淢qttClientOptionsBuilder.WithTls(MqttClientOptionsBuilderTlsParameters)鈥濆凡杩囨椂:鈥淯se WithTlsOptions(... configure) instead.鈥?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\DeviceEventService.cs(404,24): warning CS1998: 姝ゅ紓姝ユ柟娉曠己灏?"await" 杩愮畻绗︼紝灏嗕互鍚屾鏂瑰紡杩愯銆傝鑰冭檻浣跨敤 "await" 杩愮畻绗︾瓑寰呴潪闃绘鐨?API 璋冪敤锛屾垨鑰呬娇鐢?"await Task.Run(...)" 鍦ㄥ悗鍙扮嚎绋嬩笂鎵ц鍗犵敤澶ч噺 CPU 鐨勫伐浣溿€?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Service\MqttClientManager.cs(377,55): warning CS0618: 鈥淢qttApplicationMessage.Payload鈥濆凡杩囨椂:鈥淯se PayloadSegment instead. This property will be removed in a future release.鈥?[D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Controller\MqttController.cs(112,48): error CS1061: 鈥淒eviceControlService鈥濇湭鍖呭惈鈥淕etOnlineDevicesAsync鈥濈殑瀹氫箟锛屽苟涓旀壘涓嶅埌鍙帴鍙楃涓€涓€淒eviceControlService鈥濈被鍨嬪弬鏁扮殑鍙闂墿灞曟柟娉曗€淕etOnlineDevicesAsync鈥?鏄惁缂哄皯 using 鎸囦护鎴栫▼搴忛泦寮曠敤?) [D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net9.0]
D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Controller\MqttController.cs(112,48): error CS1061: 鈥淒eviceControlService鈥濇湭鍖呭惈鈥淕etOnlineDevicesAsync鈥濈殑瀹氫箟锛屽苟涓旀壘涓嶅埌鍙帴鍙楃涓€涓€淒eviceControlService鈥濈被鍨嬪弬鏁扮殑鍙闂墿灞曟柟娉曗€淕etOnlineDevicesAsync鈥?鏄惁缂哄皯 using 鎸囦护鎴栫▼搴忛泦寮曠敤?) [D:\Project\鑺傝兘鐏痋Admin.NET.energy_light\Admin.NET\Plugins\Admin.NET.Plugin.MQTT\Admin.NET.Plugin.MQTT.csproj::TargetFramework=net8.0]
    32 涓鍛?
    2 涓敊璇?

宸茬敤鏃堕棿 00:00:01.87

鏈夊彲鐢ㄧ殑宸ヤ綔璐熻浇鏇存柊銆傛湁鍏宠缁嗕俊鎭紝璇疯繍琛?`dotnet workload list`銆?
