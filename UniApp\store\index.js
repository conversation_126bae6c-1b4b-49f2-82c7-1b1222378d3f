import { createStore } from 'vuex'
import createPersistedState from 'vuex-persistedstate'
import user from './modules/user'
import device from './modules/device'
import energy from './modules/energy'
import fault from './modules/fault'
import system from './modules/system'
import loading from './modules/loading'

// 持久化配置
const persistedState = createPersistedState({
  key: 'energy-light-store',
  paths: [
    'user.token',
    'user.userInfo',
    'user.permissions',
    'user.roles',
    'system.theme',
    'system.language',
    'system.settings',
    'device.favoriteDevices',
    'device.deviceGroups',
    'energy.energyStats',
    'fault.recentAlerts',
    'loading.loadingConfig',
    'loading.networkStatus'
  ],
  storage: {
    getItem: (key) => {
      try {
        return uni.getStorageSync(key)
      } catch (error) {
        console.error('获取存储数据失败:', error)
        return null
      }
    },
    setItem: (key, value) => {
      try {
        uni.setStorageSync(key, value)
      } catch (error) {
        console.error('设置存储数据失败:', error)
      }
    },
    removeItem: (key) => {
      try {
        uni.removeStorageSync(key)
      } catch (error) {
        console.error('删除存储数据失败:', error)
      }
    }
  }
})

// 创建store实例
const store = createStore({
  modules: {
    user,
    device,
    energy,
    fault,
    system,
    loading
  },
  plugins: [persistedState],
  strict: true // uni-app 环境下默认开启严格模式
})

// 全局状态管理器
export default store



// 状态重置函数
export const resetStore = () => {
  store.dispatch('user/logout')
  store.dispatch('device/clearDevices')
  store.dispatch('system/resetSettings')
}

// 状态同步函数
export const syncStore = async () => {
  try {
    // 同步用户信息
    if (store.getters['user/isLoggedIn']) {
      await store.dispatch('user/getUserInfo')
      await store.dispatch('user/getPermissions')
    }
    
    // 同步设备信息
    await store.dispatch('device/getDeviceList')
    await store.dispatch('device/getDeviceGroups')
    await store.dispatch('device/getDeviceStats')
    
    // 同步能耗信息
    await store.dispatch('energy/getEnergyStats')
    
    // 同步故障信息
    await store.dispatch('fault/getFaultList', { pageSize: 3, status: 'pending' })
    await store.dispatch('fault/getFaultStats')
    
    // 同步系统设置
    await store.dispatch('system/getSystemInfo')
    
    console.log('状态同步完成')
  } catch (error) {
    console.error('状态同步失败:', error)
  }
}

// 状态监听器
store.subscribe((mutation) => {
  // 监听特定mutations，执行相应操作
  switch (mutation.type) {
    case 'user/SET_TOKEN':
      // Token变化时，同步到请求头
      if (mutation.payload) {
        // 设置请求头Authorization
        uni.setStorageSync('token', mutation.payload)
      } else {
        uni.removeStorageSync('token')
      }
      break
      
    case 'system/SET_THEME':
      // 主题变化时，更新页面样式
      try {
        const pages = getCurrentPages()
        if (pages.length > 0) {
          const currentPage = pages[pages.length - 1]
          if (currentPage && currentPage.$vm) {
            currentPage.$vm.$nextTick(() => {
              // 触发主题更新
              uni.$emit('theme-change', mutation.payload)
            })
          }
        }
      } catch (error) {
        console.error('主题更新失败:', error)
      }
      break
      
    case 'device/SET_DEVICE_STATUS': {
      // 设备状态变化时，发送通知
      const { deviceId, status } = mutation.payload
      uni.$emit('device-status-change', { deviceId, status })
      break
    }
  }
})

// 状态变化监听器
store.subscribeAction({
  before: (action) => {
    // Action执行前的处理
    if (action.type.includes('login') || action.type.includes('logout')) {
      console.log('认证操作:', action.type)
    }
	//console.log(action)
  },
  after: (action) => {
    // Action执行后的处理
    if (action.type === 'user/login') {
      // 登录成功后，同步数据
      syncStore()
    } else if (action.type === 'user/logout') {
      // 登出后，清理数据
      uni.clearStorageSync()
    }
  },
  error: (action, _, error) => {
    // Action执行错误的处理
    console.error('Action执行错误:', action.type, error)
    
    // 特定错误处理
    if (error.code === 401) {
      // Token过期，自动登出
      store.dispatch('user/logout')
      uni.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none'
      })
      uni.reLaunch({
        url: '/pages/user/login'
      })
    }
  }
})

// #ifdef H5
// 开发环境下将store挂载到全局
if (typeof window !== 'undefined') {
  window.$store = store
  
  // 开发环境下的调试信息
  store.subscribe((mutation) => {
    console.log('Vuex Mutation:', mutation)
  })
  
  store.subscribeAction((action) => {
    console.log('Vuex Action:', action)
  })
  
  // 全局错误处理
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled Rejection:', event.reason)
    // 这里可以添加错误上报逻辑
  })
  
  window.addEventListener('error', (event) => {
    console.error('Uncaught Exception:', event.error)
    // 这里可以添加错误上报逻辑
  })
}
// #endif

// #ifndef H5
// 非H5环境下将store挂载到uni对象
uni.$store = store
// #endif