/**
 * SVG到PNG批量转换脚本
 * 使用sharp库进行高质量图像转换
 * 使用方法: node scripts/svg-to-png.js
 */

const fs = require('fs')
const path = require('path')

// 检查是否安装了sharp
let sharp
try {
  sharp = require('sharp')
} catch (error) {
  console.error('错误: 未安装sharp库')
  console.log('请运行以下命令安装:')
  console.log('npm install sharp')
  process.exit(1)
}

// 转换配置
const CONFIG = {
  // 输入目录
  inputDirs: [
    path.join(__dirname, '../static/icons'),
    path.join(__dirname, '../static/images')
  ],
  // 输出尺寸
  sizes: [24, 48, 96],
  // 默认尺寸
  defaultSize: 48,
  // 输出质量
  quality: 90,
  // 背景色（透明）
  background: { r: 0, g: 0, b: 0, alpha: 0 }
}

/**
 * 转换单个SVG文件为PNG
 * @param {string} svgPath SVG文件路径
 * @param {string} outputDir 输出目录
 * @param {number} size 输出尺寸
 */
async function convertSvgToPng(svgPath, outputDir, size = CONFIG.defaultSize) {
  try {
    const fileName = path.basename(svgPath, '.svg')
    const outputPath = path.join(outputDir, `${fileName}.png`)
    
    // 读取SVG内容
    const svgBuffer = fs.readFileSync(svgPath)
    
    // 使用sharp转换
    await sharp(svgBuffer)
      .resize(size, size, {
        fit: 'contain',
        background: CONFIG.background
      })
      .png({
        quality: CONFIG.quality,
        compressionLevel: 9
      })
      .toFile(outputPath)
    
    console.log(`✓ 转换成功: ${fileName}.svg → ${fileName}.png (${size}x${size})`)
    return true
    
  } catch (error) {
    console.error(`✗ 转换失败: ${path.basename(svgPath)}`, error.message)
    return false
  }
}

/**
 * 批量转换目录中的所有SVG文件
 * @param {string} inputDir 输入目录
 */
async function convertDirectory(inputDir) {
  if (!fs.existsSync(inputDir)) {
    console.warn(`目录不存在: ${inputDir}`)
    return { success: 0, failed: 0 }
  }
  
  const files = fs.readdirSync(inputDir)
  const svgFiles = files.filter(file => file.endsWith('.svg'))
  
  if (svgFiles.length === 0) {
    console.log(`目录中没有SVG文件: ${inputDir}`)
    return { success: 0, failed: 0 }
  }
  
  console.log(`\n处理目录: ${inputDir}`)
  console.log(`找到 ${svgFiles.length} 个SVG文件`)
  
  let successCount = 0
  let failedCount = 0
  
  for (const file of svgFiles) {
    const svgPath = path.join(inputDir, file)
    const success = await convertSvgToPng(svgPath, inputDir, CONFIG.defaultSize)
    
    if (success) {
      successCount++
    } else {
      failedCount++
    }
  }
  
  return { success: successCount, failed: failedCount }
}

/**
 * 更新icon-config.json文件，标记PNG文件为可用
 */
function updateIconConfig() {
  const configPath = path.join(__dirname, '../static/icons/icon-config.json')
  
  if (!fs.existsSync(configPath)) {
    console.warn('icon-config.json文件不存在，跳过更新')
    return
  }
  
  try {
    const config = JSON.parse(fs.readFileSync(configPath, 'utf8'))
    
    // 检查PNG文件是否存在并更新配置
    Object.keys(config.icons || {}).forEach(iconName => {
      const pngPath = path.join(__dirname, '../static/icons', `${iconName}.png`)
      if (fs.existsSync(pngPath)) {
        config.icons[iconName].png.available = true
        console.log(`✓ 更新配置: ${iconName}.png 标记为可用`)
      }
    })
    
    // 更新时间戳
    config.updatedAt = new Date().toISOString()
    
    // 保存配置
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2), 'utf8')
    console.log('\n✓ icon-config.json 更新完成')
    
  } catch (error) {
    console.error('更新icon-config.json失败:', error.message)
  }
}

/**
 * 生成多种尺寸的PNG文件
 * @param {string} inputDir 输入目录
 */
async function generateMultipleSizes(inputDir) {
  if (!fs.existsSync(inputDir)) {
    return
  }
  
  const files = fs.readdirSync(inputDir)
  const svgFiles = files.filter(file => file.endsWith('.svg'))
  
  console.log(`\n生成多尺寸PNG文件...`)
  
  for (const file of svgFiles) {
    const svgPath = path.join(inputDir, file)
    const fileName = path.basename(file, '.svg')
    
    for (const size of CONFIG.sizes) {
      if (size === CONFIG.defaultSize) continue // 默认尺寸已经生成
      
      try {
        const outputPath = path.join(inputDir, `${fileName}_${size}.png`)
        const svgBuffer = fs.readFileSync(svgPath)
        
        await sharp(svgBuffer)
          .resize(size, size, {
            fit: 'contain',
            background: CONFIG.background
          })
          .png({
            quality: CONFIG.quality,
            compressionLevel: 9
          })
          .toFile(outputPath)
        
        console.log(`✓ 生成: ${fileName}_${size}.png`)
        
      } catch (error) {
        console.error(`✗ 生成失败: ${fileName}_${size}.png`, error.message)
      }
    }
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('开始SVG到PNG批量转换...')
  console.log('使用sharp库进行高质量转换\n')
  
  let totalSuccess = 0
  let totalFailed = 0
  
  // 转换所有目录
  for (const inputDir of CONFIG.inputDirs) {
    const result = await convertDirectory(inputDir)
    totalSuccess += result.success
    totalFailed += result.failed
  }
  
  // 更新配置文件
  updateIconConfig()
  
  // 生成多尺寸版本（可选）
  const generateMultiple = process.argv.includes('--multiple-sizes')
  if (generateMultiple) {
    for (const inputDir of CONFIG.inputDirs) {
      await generateMultipleSizes(inputDir)
    }
  }
  
  // 输出统计信息
  console.log('\n=== 转换完成 ===')
  console.log(`成功转换: ${totalSuccess} 个文件`)
  console.log(`转换失败: ${totalFailed} 个文件`)
  
  if (totalSuccess > 0) {
    console.log('\n后续步骤:')
    console.log('1. 检查生成的PNG文件质量')
    console.log('2. 测试图标在应用中的显示效果')
    console.log('3. 如需生成多尺寸版本，请运行: node scripts/svg-to-png.js --multiple-sizes')
  }
  
  if (totalFailed > 0) {
    console.log('\n注意: 部分文件转换失败，请检查SVG文件格式')
    process.exit(1)
  }
}

// 执行脚本
if (require.main === module) {
  main().catch(error => {
    console.error('脚本执行失败:', error)
    process.exit(1)
  })
}

module.exports = {
  convertSvgToPng,
  convertDirectory,
  updateIconConfig,
  CONFIG
}