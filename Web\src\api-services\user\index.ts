import { service as request } from '/@/utils/request';
import { useBaseApi } from '../base';

/**
 * 用户管理API接口集合
 * @method getPage 获取用户分页列表
 * @method getDetail 获取用户详情
 * @method add 添加用户
 * @method update 更新用户
 * @method delete 删除用户
 * @method setStatus 设置用户状态
 * @method resetPassword 重置用户密码
 * @method changePassword 修改密码
 * @method setNickName 设置昵称
 * @method getUserRoles 获取用户角色
 * @method setUserRole 设置用户角色
 * @method getUserOrgs 获取用户机构
 * @method setUserOrg 设置用户机构
 */
export function useUserApi() {
	const baseApi = useBaseApi('sysUser');
	
	return {
		...baseApi,
		// 重置用户密码
		resetPassword: (data: any) => {
			return request({
				url: '/api/sysUser/resetPwd',
				method: 'post',
				data,
			});
		},
		// 修改密码
		changePassword: (data: any) => {
			return request({
				url: '/api/sysUser/changePwd',
				method: 'post',
				data,
			});
		},
		// 设置昵称
		setNickName: (data: any) => {
			return request({
				url: '/api/sysUser/setNickName',
				method: 'post',
				data,
			});
		},
		// 获取用户角色
		getUserRoles: (userId: any) => {
			return request({
				url: '/api/sysUser/ownRole',
				method: 'get',
				params: { userId },
			});
		},
		// 设置用户角色
		setUserRole: (data: any) => {
			return request({
				url: '/api/sysUser/setRole',
				method: 'post',
				data,
			});
		},
		// 获取用户机构
		getUserOrgs: (userId: any) => {
			return request({
				url: '/api/sysUser/ownOrg',
				method: 'get',
				params: { userId },
			});
		},
		// 设置用户机构
		setUserOrg: (data: any) => {
			return request({
				url: '/api/sysUser/setOrg',
				method: 'post',
				data,
			});
		},
		// 解除登录锁定
		unlockLogin: (data: any) => {
			return request({
				url: '/api/sysUser/unlockLogin',
				method: 'post',
				data,
			});
		},
		// 获取用户基本信息
		getBaseInfo: () => {
			return request({
				url: '/api/sysUser/baseInfo',
				method: 'get',
			});
		},
		// 更新用户基本信息
		updateBaseInfo: (data: any) => {
			return request({
				url: '/api/sysUser/baseInfo',
				method: 'post',
				data,
			});
		},
		// 获取用户拥有的角色列表
		getOwnRoleList: (userId: any) => {
			return request({
				url: '/api/sysUser/ownRoleList',
				method: 'get',
				params: { userId },
			});
		},
		// 获取用户拥有的扩展机构列表
		getOwnExtOrgList: (userId: any) => {
			return request({
				url: '/api/sysUser/ownExtOrgList',
				method: 'get',
				params: { userId },
			});
		},
		// 获取用户机构信息
		getOrgInfo: () => {
			return request({
				url: '/api/sysUser/orgInfo',
				method: 'get',
			});
		},
		// 设置语言代码
		setLangCode: (langCode: string) => {
    return request({
      url: `/api/sysUser/setLangCode/${langCode}`,
      method: 'post',
    });
  },
  update: (data: any) => {
    return request({
      url: '/api/sysUser/update',
      method: 'post',
      data,
    });
  },
  add: (data: any) => {
    return request({
      url: '/api/sysUser/add',
      method: 'post',
      data,
    });
  },
};
}