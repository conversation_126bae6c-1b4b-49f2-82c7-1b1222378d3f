// Vuex状态类型定义

// 用户模块状态类型
export const USER_TYPES = {
  // Mutations
  SET_TOKEN: 'user/SET_TOKEN',
  SET_USER_INFO: 'user/SET_USER_INFO',
  SET_PERMISSIONS: 'user/SET_PERMISSIONS',
  SET_ROLES: 'user/SET_ROLES',
  SET_LOGIN_STATUS: 'user/SET_LOGIN_STATUS',
  SET_LOADING: 'user/SET_LOADING',
  CLEAR_USER_DATA: 'user/CLEAR_USER_DATA',
  
  // Actions
  LOGIN: 'user/login',
  LOGOUT: 'user/logout',
  GET_USER_INFO: 'user/getUserInfo',
  UPDATE_USER_INFO: 'user/updateUserInfo',
  GET_PERMISSIONS: 'user/getPermissions',
  REFRESH_TOKEN: 'user/refreshToken',
  CHANGE_PASSWORD: 'user/changePassword'
}

// 设备模块状态类型
export const DEVICE_TYPES = {
  // Mutations
  SET_DEVICE_LIST: 'device/SET_DEVICE_LIST',
  SET_DEVICE_GROUPS: 'device/SET_DEVICE_GROUPS',
  SET_CURRENT_DEVICE: 'device/SET_CURRENT_DEVICE',
  SET_DEVICE_STATUS: 'device/SET_DEVICE_STATUS',
  SET_FAVORITE_DEVICES: 'device/SET_FAVORITE_DEVICES',
  SET_LOADING: 'device/SET_LOADING',
  SET_ERROR: 'device/SET_ERROR',
  ADD_DEVICE: 'device/ADD_DEVICE',
  UPDATE_DEVICE: 'device/UPDATE_DEVICE',
  REMOVE_DEVICE: 'device/REMOVE_DEVICE',
  CLEAR_DEVICES: 'device/CLEAR_DEVICES',
  
  // Actions
  GET_DEVICE_LIST: 'device/getDeviceList',
  GET_DEVICE_GROUPS: 'device/getDeviceGroups',
  GET_DEVICE_DETAIL: 'device/getDeviceDetail',
  CONTROL_DEVICE: 'device/controlDevice',
  DELETE_DEVICE: 'device/deleteDevice',
  TOGGLE_FAVORITE: 'device/toggleFavorite',
  BATCH_CONTROL: 'device/batchControl',
  SYNC_DEVICE_STATUS: 'device/syncDeviceStatus'
}

// 系统模块状态类型
export const SYSTEM_TYPES = {
  // Mutations
  SET_THEME: 'system/SET_THEME',
  SET_LANGUAGE: 'system/SET_LANGUAGE',
  SET_SETTINGS: 'system/SET_SETTINGS',
  SET_SYSTEM_INFO: 'system/SET_SYSTEM_INFO',
  SET_NETWORK_STATUS: 'system/SET_NETWORK_STATUS',
  SET_APP_VERSION: 'system/SET_APP_VERSION',
  SET_LOADING: 'system/SET_LOADING',
  RESET_SETTINGS: 'system/RESET_SETTINGS',
  
  // Actions
  INIT_SYSTEM: 'system/initSystem',
  GET_SYSTEM_INFO: 'system/getSystemInfo',
  UPDATE_SETTINGS: 'system/updateSettings',
  CHANGE_THEME: 'system/changeTheme',
  CHANGE_LANGUAGE: 'system/changeLanguage',
  CHECK_UPDATE: 'system/checkUpdate',
  CLEAR_CACHE: 'system/clearCache'
}

// 设备状态枚举
export const DEVICE_STATUS = {
  ONLINE: 'online',
  OFFLINE: 'offline',
  FAULT: 'fault',
  MAINTENANCE: 'maintenance',
  UNKNOWN: 'unknown'
}

// 设备类型枚举
export const DEVICE_TYPE = {
  LED_LIGHT: 'led_light',
  SMART_SWITCH: 'smart_switch',
  SENSOR: 'sensor',
  CONTROLLER: 'controller',
  GATEWAY: 'gateway'
}

// 用户角色枚举
export const USER_ROLE = {
  ADMIN: 'admin',
  MANAGER: 'manager',
  OPERATOR: 'operator',
  VIEWER: 'viewer'
}

// 权限枚举
export const PERMISSION = {
  // 设备管理
  DEVICE_VIEW: 'device:view',
  DEVICE_ADD: 'device:add',
  DEVICE_EDIT: 'device:edit',
  DEVICE_DELETE: 'device:delete',
  DEVICE_CONTROL: 'device:control',
  
  // 照明控制
  LIGHTING_VIEW: 'lighting:view',
  LIGHTING_CONTROL: 'lighting:control',
  LIGHTING_SCENE: 'lighting:scene',
  LIGHTING_SCHEDULE: 'lighting:schedule',
  
  // 能耗监控
  ENERGY_VIEW: 'energy:view',
  ENERGY_EXPORT: 'energy:export',
  ENERGY_REPORT: 'energy:report',
  
  // 故障管理
  FAULT_VIEW: 'fault:view',
  FAULT_HANDLE: 'fault:handle',
  FAULT_REPORT: 'fault:report',
  
  // 用户管理
  USER_VIEW: 'user:view',
  USER_ADD: 'user:add',
  USER_EDIT: 'user:edit',
  USER_DELETE: 'user:delete',
  
  // 系统管理
  SYSTEM_VIEW: 'system:view',
  SYSTEM_CONFIG: 'system:config',
  SYSTEM_LOG: 'system:log'
}

// 主题类型
export const THEME_TYPE = {
  LIGHT: 'light',
  DARK: 'dark',
  AUTO: 'auto'
}

// 语言类型
export const LANGUAGE_TYPE = {
  ZH_CN: 'zh-CN',
  EN_US: 'en-US'
}

// 网络状态
export const NETWORK_STATUS = {
  ONLINE: 'online',
  OFFLINE: 'offline',
  WEAK: 'weak'
}

// 加载状态
export const LOADING_STATUS = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error'
}

// 错误类型
export const ERROR_TYPE = {
  NETWORK_ERROR: 'network_error',
  AUTH_ERROR: 'auth_error',
  PERMISSION_ERROR: 'permission_error',
  VALIDATION_ERROR: 'validation_error',
  BUSINESS_ERROR: 'business_error',
  UNKNOWN_ERROR: 'unknown_error'
}

// 状态接口定义（用于TypeScript）
export const StateInterfaces = {
  // 用户状态接口
  UserState: {
    token: '',
    userInfo: null,
    permissions: [],
    roles: [],
    isLoggedIn: false,
    loading: false,
    error: null
  },
  
  // 设备状态接口
  DeviceState: {
    deviceList: [],
    deviceGroups: [],
    currentDevice: null,
    favoriteDevices: [],
    loading: false,
    error: null,
    lastUpdateTime: null
  },
  
  // 系统状态接口
  SystemState: {
    theme: THEME_TYPE.LIGHT,
    language: LANGUAGE_TYPE.ZH_CN,
    settings: {},
    systemInfo: null,
    networkStatus: NETWORK_STATUS.ONLINE,
    appVersion: '',
    loading: false,
    error: null
  },
  
  // 根状态接口
  RootState: {
    user: null,
    device: null,
    system: null
  }
}

// 用户信息接口
export const UserInfo = {
  id: '',
  username: '',
  nickname: '',
  email: '',
  phone: '',
  avatar: '',
  department: '',
  position: '',
  status: '',
  createTime: '',
  lastLoginTime: ''
}

// 设备信息接口
export const DeviceInfo = {
  id: '',
  name: '',
  type: '',
  model: '',
  location: '',
  status: '',
  online: false,
  brightness: 0,
  colorTemp: 0,
  color: '',
  power: 0,
  energy: 0,
  groupId: '',
  createTime: '',
  updateTime: ''
}

// 设备组信息接口
export const DeviceGroup = {
  id: '',
  name: '',
  description: '',
  deviceCount: 0,
  onlineCount: 0,
  location: '',
  createTime: ''
}

// 系统信息接口
export const SystemInfo = {
  version: '',
  buildTime: '',
  environment: '',
  serverTime: '',
  timezone: '',
  features: []
}

// API响应接口
export const ApiResponse = {
  code: 0,
  message: '',
  data: null,
  timestamp: 0
}

// 分页信息接口
export const PageInfo = {
  current: 1,
  size: 10,
  total: 0,
  pages: 0
}

// 导出所有类型
export default {
  USER_TYPES,
  DEVICE_TYPES,
  SYSTEM_TYPES,
  DEVICE_STATUS,
  DEVICE_TYPE,
  USER_ROLE,
  PERMISSION,
  THEME_TYPE,
  LANGUAGE_TYPE,
  NETWORK_STATUS,
  LOADING_STATUS,
  ERROR_TYPE,
  StateInterfaces,
  UserInfo,
  DeviceInfo,
  DeviceGroup,
  SystemInfo,
  ApiResponse,
  PageInfo
}