// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System;
using System.Buffers;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Runtime;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Channels;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Admin.NET.Plugin.MQTT.Core.Configuration;
using Admin.NET.Plugin.MQTT.Interfaces;

namespace Admin.NET.Plugin.MQTT.Core.Services;

/// <summary>
/// 性能优化服务接口
/// 提供内存管理、对象池、异步任务调度和性能监控功能
/// </summary>
public interface IPerformanceOptimizationService : IDisposable
{
    /// <summary>
    /// 内存池管理器
    /// </summary>
    IMemoryPoolManager MemoryPoolManager { get; }
    
    /// <summary>
    /// 对象池管理器
    /// </summary>
    IObjectPoolManager ObjectPoolManager { get; }
    
    /// <summary>
    /// 异步任务调度器
    /// </summary>
    IAsyncTaskScheduler AsyncTaskScheduler { get; }
    
    /// <summary>
    /// 性能监控器
    /// </summary>
    IPerformanceMonitor PerformanceMonitor { get; }
    
    /// <summary>
    /// 获取性能统计信息
    /// </summary>
    /// <returns>性能统计信息</returns>
    Task<PerformanceStatistics> GetPerformanceStatisticsAsync();
    
    /// <summary>
    /// 执行垃圾回收优化
    /// </summary>
    /// <param name="generation">垃圾回收代数，-1表示全部</param>
    /// <returns>优化任务</returns>
    Task OptimizeGarbageCollectionAsync(int generation = -1);
    
    /// <summary>
    /// 压缩内存使用
    /// </summary>
    /// <returns>压缩任务</returns>
    Task CompactMemoryAsync();
    
    /// <summary>
    /// 预热性能关键路径
    /// </summary>
    /// <returns>预热任务</returns>
    Task WarmupCriticalPathsAsync();
}

/// <summary>
/// 内存池管理器接口
/// 提供高效的内存分配和回收机制
/// </summary>
public interface IMemoryPoolManager
{
    /// <summary>
    /// 租用字节数组
    /// </summary>
    /// <param name="minimumLength">最小长度</param>
    /// <returns>租用的字节数组</returns>
    byte[] RentByteArray(int minimumLength);
    
    /// <summary>
    /// 归还字节数组
    /// </summary>
    /// <param name="array">要归还的数组</param>
    /// <param name="clearArray">是否清空数组</param>
    void ReturnByteArray(byte[] array, bool clearArray = false);
    
    /// <summary>
    /// 租用字符数组
    /// </summary>
    /// <param name="minimumLength">最小长度</param>
    /// <returns>租用的字符数组</returns>
    char[] RentCharArray(int minimumLength);
    
    /// <summary>
    /// 归还字符数组
    /// </summary>
    /// <param name="array">要归还的数组</param>
    /// <param name="clearArray">是否清空数组</param>
    void ReturnCharArray(char[] array, bool clearArray = false);
    
    /// <summary>
    /// 租用内存块
    /// </summary>
    /// <param name="minimumLength">最小长度</param>
    /// <returns>租用的内存块</returns>
    IMemoryOwner<byte> RentMemory(int minimumLength);
    
    /// <summary>
    /// 获取内存池统计信息
    /// </summary>
    /// <returns>内存池统计信息</returns>
    MemoryPoolStatistics GetStatistics();
}

/// <summary>
/// 对象池管理器接口
/// 提供对象的重用机制，减少GC压力
/// </summary>
public interface IObjectPoolManager
{
    /// <summary>
    /// 获取对象池
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <returns>对象池</returns>
    IObjectPool<T> GetPool<T>() where T : class, new();
    
    /// <summary>
    /// 注册对象池
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="factory">对象工厂</param>
    /// <param name="maxSize">最大池大小</param>
    void RegisterPool<T>(Func<T> factory, int maxSize = 100) where T : class;
    
    /// <summary>
    /// 获取对象池统计信息
    /// </summary>
    /// <returns>对象池统计信息</returns>
    ObjectPoolStatistics GetStatistics();
}

/// <summary>
/// 异步任务调度器接口
/// 提供高效的异步任务调度和执行
/// </summary>
public interface IAsyncTaskScheduler
{
    /// <summary>
    /// 调度高优先级任务
    /// </summary>
    /// <param name="task">要调度的任务</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>调度任务</returns>
    Task ScheduleHighPriorityAsync(Func<CancellationToken, Task> task, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 调度普通优先级任务
    /// </summary>
    /// <param name="task">要调度的任务</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>调度任务</returns>
    Task ScheduleNormalPriorityAsync(Func<CancellationToken, Task> task, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 调度低优先级任务
    /// </summary>
    /// <param name="task">要调度的任务</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>调度任务</returns>
    Task ScheduleLowPriorityAsync(Func<CancellationToken, Task> task, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量调度任务
    /// </summary>
    /// <param name="tasks">要调度的任务集合</param>
    /// <param name="priority">任务优先级</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>调度任务</returns>
    Task ScheduleBatchAsync(IEnumerable<Func<CancellationToken, Task>> tasks, TaskPriority priority = TaskPriority.Normal, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取调度器统计信息
    /// </summary>
    /// <returns>调度器统计信息</returns>
    TaskSchedulerStatistics GetStatistics();
}

/// <summary>
/// 性能监控器接口
/// 提供实时性能监控和分析
/// </summary>
public interface IPerformanceMonitor
{
    /// <summary>
    /// 开始性能计时
    /// </summary>
    /// <param name="operationName">操作名称</param>
    /// <returns>性能计时器</returns>
    IPerformanceTimer StartTimer(string operationName);
    
    /// <summary>
    /// 记录性能指标
    /// </summary>
    /// <param name="metricName">指标名称</param>
    /// <param name="value">指标值</param>
    /// <param name="tags">标签</param>
    void RecordMetric(string metricName, double value, IDictionary<string, string>? tags = null);
    
    /// <summary>
    /// 记录内存使用情况
    /// </summary>
    void RecordMemoryUsage();
    
    /// <summary>
    /// 记录CPU使用情况
    /// </summary>
    void RecordCpuUsage();
    
    /// <summary>
    /// 获取性能报告
    /// </summary>
    /// <param name="timeRange">时间范围</param>
    /// <returns>性能报告</returns>
    Task<PerformanceReport> GetPerformanceReportAsync(TimeSpan timeRange);
    
    /// <summary>
    /// 性能警告事件
    /// </summary>
    event EventHandler<PerformanceWarningEventArgs> PerformanceWarning;
}

/// <summary>
/// 性能优化服务实现
/// 集成内存管理、对象池、异步调度和性能监控功能
/// </summary>
public class PerformanceOptimizationService : IPerformanceOptimizationService
{
    #region 私有字段
    
    private readonly ILogger<PerformanceOptimizationService> _logger;
    private readonly IMqttConfigurationManager _configurationManager;
    private readonly IMqttExceptionHandler _exceptionHandler;
    private readonly PerformanceMonitoringConfiguration _configuration;
    
    private readonly IMemoryPoolManager _memoryPoolManager;
    private readonly IObjectPoolManager _objectPoolManager;
    private readonly IAsyncTaskScheduler _asyncTaskScheduler;
    private readonly IPerformanceMonitor _performanceMonitor;
    
    private readonly Timer _gcOptimizationTimer;
    private readonly Timer _memoryCompactionTimer;
    private readonly Timer _performanceReportTimer;
    
    private readonly CancellationTokenSource _cancellationTokenSource;
    private volatile bool _disposed;
    
    #endregion
    
    #region 构造函数
    
    /// <summary>
    /// 构造函数 - 初始化性能优化服务
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="configurationManager">配置管理器</param>
    /// <param name="exceptionHandler">异常处理器</param>
    public PerformanceOptimizationService(
        ILogger<PerformanceOptimizationService> logger,
        IMqttConfigurationManager configurationManager,
        IMqttExceptionHandler exceptionHandler)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configurationManager = configurationManager ?? throw new ArgumentNullException(nameof(configurationManager));
        _exceptionHandler = exceptionHandler ?? throw new ArgumentNullException(nameof(exceptionHandler));
        
        _configuration = _configurationManager.GetPerformanceConfiguration();
        _cancellationTokenSource = new CancellationTokenSource();
        
        // 初始化各个管理器
        _memoryPoolManager = new MemoryPoolManager(_logger, _configuration);
        _objectPoolManager = new ObjectPoolManager(_logger, _configuration);
        _asyncTaskScheduler = new AsyncTaskScheduler(_logger, _configuration);
        _performanceMonitor = new PerformanceMonitor(_logger, _configuration);
        
        // 初始化定时器
        InitializeTimers();
        
        // 预热关键路径
        _ = Task.Run(WarmupCriticalPathsAsync, _cancellationTokenSource.Token);
        
        _logger.LogInformation("性能优化服务已初始化，内存阈值: {MemoryThreshold}MB，GC优化间隔: {GcInterval}分钟", 
            _configuration.MemoryThreshold, _configuration.GcOptimizationInterval);
    }
    
    #endregion
    
    #region 公共属性
    
    /// <summary>
    /// 内存池管理器
    /// </summary>
    public IMemoryPoolManager MemoryPoolManager => _memoryPoolManager;
    
    /// <summary>
    /// 对象池管理器
    /// </summary>
    public IObjectPoolManager ObjectPoolManager => _objectPoolManager;
    
    /// <summary>
    /// 异步任务调度器
    /// </summary>
    public IAsyncTaskScheduler AsyncTaskScheduler => _asyncTaskScheduler;
    
    /// <summary>
    /// 性能监控器
    /// </summary>
    public IPerformanceMonitor PerformanceMonitor => _performanceMonitor;
    
    #endregion
    
    #region 公共方法
    
    /// <summary>
    /// 获取性能统计信息
    /// </summary>
    /// <returns>性能统计信息</returns>
    public async Task<PerformanceStatistics> GetPerformanceStatisticsAsync()
    {
        try
        {
            var memoryStats = _memoryPoolManager.GetStatistics();
            var objectPoolStats = _objectPoolManager.GetStatistics();
            var schedulerStats = _asyncTaskScheduler.GetStatistics();
            var performanceReport = await _performanceMonitor.GetPerformanceReportAsync(TimeSpan.FromMinutes(5));
            
            return new PerformanceStatistics
            {
                MemoryPool = memoryStats,
                ObjectPool = objectPoolStats,
                TaskScheduler = schedulerStats,
                PerformanceReport = performanceReport,
                Timestamp = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            await _exceptionHandler.HandleExceptionAsync(ex, "获取性能统计信息失败");
            throw;
        }
    }
    
    /// <summary>
    /// 执行垃圾回收优化
    /// </summary>
    /// <param name="generation">垃圾回收代数，-1表示全部</param>
    /// <returns>优化任务</returns>
    public async Task OptimizeGarbageCollectionAsync(int generation = -1)
    {
        try
        {
            await _asyncTaskScheduler.ScheduleLowPriorityAsync(async cancellationToken =>
            {
                var stopwatch = Stopwatch.StartNew();
                
                // 记录GC前的内存使用情况
                var beforeMemory = GC.GetTotalMemory(false);
                
                // 执行垃圾回收
                if (generation == -1)
                {
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    GC.Collect();
                }
                else
                {
                    GC.Collect(generation, GCCollectionMode.Optimized);
                }
                
                // 记录GC后的内存使用情况
                var afterMemory = GC.GetTotalMemory(false);
                var freedMemory = beforeMemory - afterMemory;
                
                stopwatch.Stop();
                
                _performanceMonitor.RecordMetric("gc_optimization_duration_ms", stopwatch.ElapsedMilliseconds);
                _performanceMonitor.RecordMetric("gc_freed_memory_bytes", freedMemory);
                
                _logger.LogDebug("垃圾回收优化完成，代数: {Generation}，释放内存: {FreedMemory:N0} 字节，耗时: {Duration}ms", 
                    generation, freedMemory, stopwatch.ElapsedMilliseconds);
                
            }, _cancellationTokenSource.Token);
        }
        catch (Exception ex)
        {
            await _exceptionHandler.HandleExceptionAsync(ex, "垃圾回收优化失败");
            throw;
        }
    }
    
    /// <summary>
    /// 压缩内存使用
    /// </summary>
    /// <returns>压缩任务</returns>
    public async Task CompactMemoryAsync()
    {
        try
        {
            await _asyncTaskScheduler.ScheduleLowPriorityAsync(async cancellationToken =>
            {
                var stopwatch = Stopwatch.StartNew();
                
                // 记录压缩前的内存使用情况
                var beforeMemory = GC.GetTotalMemory(false);
                
                // 执行内存压缩
                GCSettings.LargeObjectHeapCompactionMode = GCLargeObjectHeapCompactionMode.CompactOnce;
                GC.Collect(2, GCCollectionMode.Forced, true, true);
                GC.WaitForPendingFinalizers();
                
                // 记录压缩后的内存使用情况
                var afterMemory = GC.GetTotalMemory(false);
                var compactedMemory = beforeMemory - afterMemory;
                
                stopwatch.Stop();
                
                _performanceMonitor.RecordMetric("memory_compaction_duration_ms", stopwatch.ElapsedMilliseconds);
                _performanceMonitor.RecordMetric("memory_compacted_bytes", compactedMemory);
                
                _logger.LogDebug("内存压缩完成，压缩内存: {CompactedMemory:N0} 字节，耗时: {Duration}ms", 
                    compactedMemory, stopwatch.ElapsedMilliseconds);
                
            }, _cancellationTokenSource.Token);
        }
        catch (Exception ex)
        {
            await _exceptionHandler.HandleExceptionAsync(ex, "内存压缩失败");
            throw;
        }
    }
    
    /// <summary>
    /// 预热性能关键路径
    /// </summary>
    /// <returns>预热任务</returns>
    public async Task WarmupCriticalPathsAsync()
    {
        try
        {
            await _asyncTaskScheduler.ScheduleNormalPriorityAsync(async cancellationToken =>
            {
                var stopwatch = Stopwatch.StartNew();
                
                // 预热内存池
                WarmupMemoryPools();
                
                // 预热对象池
                WarmupObjectPools();
                
                // 预热JIT编译
                WarmupJitCompilation();
                
                stopwatch.Stop();
                
                _performanceMonitor.RecordMetric("warmup_duration_ms", stopwatch.ElapsedMilliseconds);
                
                _logger.LogInformation("性能关键路径预热完成，耗时: {Duration}ms", stopwatch.ElapsedMilliseconds);
                
            }, _cancellationTokenSource.Token);
        }
        catch (Exception ex)
        {
            await _exceptionHandler.HandleExceptionAsync(ex, "预热性能关键路径失败");
            throw;
        }
    }
    
    #endregion
    
    #region 私有方法
    
    /// <summary>
    /// 初始化定时器
    /// </summary>
    private void InitializeTimers()
    {
        // GC优化定时器
        var gcInterval = TimeSpan.FromMinutes(_configuration.GcOptimizationInterval);
        _gcOptimizationTimer = new Timer(async _ => await OptimizeGarbageCollectionAsync(), 
            null, gcInterval, gcInterval);
        
        // 内存压缩定时器
        var compactionInterval = TimeSpan.FromMinutes(_configuration.MemoryCompactionInterval);
        _memoryCompactionTimer = new Timer(async _ => await CompactMemoryAsync(), 
            null, compactionInterval, compactionInterval);
        
        // 性能报告定时器
        var reportInterval = TimeSpan.FromMinutes(_configuration.PerformanceReportInterval);
        _performanceReportTimer = new Timer(async _ => await GeneratePerformanceReportAsync(), 
            null, reportInterval, reportInterval);
    }
    
    /// <summary>
    /// 预热内存池
    /// </summary>
    private void WarmupMemoryPools()
    {
        // 预分配常用大小的内存块
        var commonSizes = new[] { 256, 512, 1024, 2048, 4096, 8192 };
        
        foreach (var size in commonSizes)
        {
            // 预热字节数组池
            var byteArray = _memoryPoolManager.RentByteArray(size);
            _memoryPoolManager.ReturnByteArray(byteArray);
            
            // 预热字符数组池
            var charArray = _memoryPoolManager.RentCharArray(size);
            _memoryPoolManager.ReturnCharArray(charArray);
            
            // 预热内存块池
            using var memory = _memoryPoolManager.RentMemory(size);
        }
    }
    
    /// <summary>
    /// 预热对象池
    /// </summary>
    private void WarmupObjectPools()
    {
        // 预热常用对象池
        var stringBuilderPool = _objectPoolManager.GetPool<StringBuilder>();
        var stringBuilder = stringBuilderPool.Get();
        stringBuilderPool.Return(stringBuilder);
        
        // 可以根据需要添加更多对象池的预热
    }
    
    /// <summary>
    /// 预热JIT编译
    /// </summary>
    private void WarmupJitCompilation()
    {
        // 触发关键方法的JIT编译
        RuntimeHelpers.PrepareMethod(typeof(PerformanceOptimizationService).GetMethod(nameof(GetPerformanceStatisticsAsync))!.MethodHandle);
        RuntimeHelpers.PrepareMethod(typeof(PerformanceOptimizationService).GetMethod(nameof(OptimizeGarbageCollectionAsync))!.MethodHandle);
        RuntimeHelpers.PrepareMethod(typeof(PerformanceOptimizationService).GetMethod(nameof(CompactMemoryAsync))!.MethodHandle);
    }
    
    /// <summary>
    /// 生成性能报告
    /// </summary>
    /// <returns>生成任务</returns>
    private async Task GeneratePerformanceReportAsync()
    {
        try
        {
            var statistics = await GetPerformanceStatisticsAsync();
            
            // 检查性能警告
            CheckPerformanceWarnings(statistics);
            
            _logger.LogDebug("性能报告已生成，内存使用: {MemoryUsage:N0} 字节，对象池命中率: {HitRate:P2}", 
                statistics.MemoryPool.TotalAllocatedBytes, statistics.ObjectPool.HitRate);
        }
        catch (Exception ex)
        {
            await _exceptionHandler.HandleExceptionAsync(ex, "生成性能报告失败");
        }
    }
    
    /// <summary>
    /// 检查性能警告
    /// </summary>
    /// <param name="statistics">性能统计信息</param>
    private void CheckPerformanceWarnings(PerformanceStatistics statistics)
    {
        var warnings = new List<string>();
        
        // 检查内存使用
        var memoryUsageMB = statistics.MemoryPool.TotalAllocatedBytes / 1024.0 / 1024.0;
        if (memoryUsageMB > _configuration.MemoryThreshold)
        {
            warnings.Add($"内存使用过高: {memoryUsageMB:F2}MB > {_configuration.MemoryThreshold}MB");
        }
        
        // 检查对象池命中率
        if (statistics.ObjectPool.HitRate < _configuration.ObjectPoolHitRateThreshold)
        {
            warnings.Add($"对象池命中率过低: {statistics.ObjectPool.HitRate:P2} < {_configuration.ObjectPoolHitRateThreshold:P2}");
        }
        
        // 检查任务调度器队列长度
        if (statistics.TaskScheduler.QueueLength > _configuration.TaskQueueLengthThreshold)
        {
            warnings.Add($"任务队列长度过高: {statistics.TaskScheduler.QueueLength} > {_configuration.TaskQueueLengthThreshold}");
        }
        
        // 触发性能警告事件
        if (warnings.Any())
        {
            _performanceMonitor.PerformanceWarning?.Invoke(this, new PerformanceWarningEventArgs
            {
                Warnings = warnings,
                Statistics = statistics,
                Timestamp = DateTime.UtcNow
            });
        }
    }
    
    #endregion
    
    #region IDisposable实现
    
    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;
            
            // 取消所有正在进行的操作
            _cancellationTokenSource?.Cancel();
            
            // 释放定时器
            _gcOptimizationTimer?.Dispose();
            _memoryCompactionTimer?.Dispose();
            _performanceReportTimer?.Dispose();
            
            // 释放管理器
            (_memoryPoolManager as IDisposable)?.Dispose();
            (_objectPoolManager as IDisposable)?.Dispose();
            (_asyncTaskScheduler as IDisposable)?.Dispose();
            (_performanceMonitor as IDisposable)?.Dispose();
            
            // 释放取消令牌源
            _cancellationTokenSource?.Dispose();
            
            _logger.LogInformation("性能优化服务已释放资源");
        }
    }
    
    #endregion
}

/// <summary>
/// 任务优先级枚举
/// 定义异步任务的优先级级别
/// </summary>
public enum TaskPriority
{
    /// <summary>低优先级</summary>
    Low = 0,
    /// <summary>普通优先级</summary>
    Normal = 1,
    /// <summary>高优先级</summary>
    High = 2,
    /// <summary>紧急优先级</summary>
    Critical = 3
}

/// <summary>
/// 性能统计信息
/// 包含各个组件的性能数据
/// </summary>
public class PerformanceStatistics
{
    /// <summary>
    /// 内存池统计信息
    /// </summary>
    public MemoryPoolStatistics MemoryPool { get; set; } = new();
    
    /// <summary>
    /// 对象池统计信息
    /// </summary>
    public ObjectPoolStatistics ObjectPool { get; set; } = new();
    
    /// <summary>
    /// 任务调度器统计信息
    /// </summary>
    public TaskSchedulerStatistics TaskScheduler { get; set; } = new();
    
    /// <summary>
    /// 性能报告
    /// </summary>
    public PerformanceReport PerformanceReport { get; set; } = new();
    
    /// <summary>
    /// 统计时间戳
    /// </summary>
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// 内存池统计信息
/// 记录内存池的使用情况
/// </summary>
public class MemoryPoolStatistics
{
    /// <summary>
    /// 总分配字节数
    /// </summary>
    public long TotalAllocatedBytes { get; set; }
    
    /// <summary>
    /// 当前使用字节数
    /// </summary>
    public long CurrentUsedBytes { get; set; }
    
    /// <summary>
    /// 池命中次数
    /// </summary>
    public long PoolHits { get; set; }
    
    /// <summary>
    /// 池未命中次数
    /// </summary>
    public long PoolMisses { get; set; }
    
    /// <summary>
    /// 池命中率
    /// </summary>
    public double HitRate => PoolHits + PoolMisses > 0 ? (double)PoolHits / (PoolHits + PoolMisses) : 0;
}

/// <summary>
/// 对象池统计信息
/// 记录对象池的使用情况
/// </summary>
public class ObjectPoolStatistics
{
    /// <summary>
    /// 总获取次数
    /// </summary>
    public long TotalGets { get; set; }
    
    /// <summary>
    /// 总归还次数
    /// </summary>
    public long TotalReturns { get; set; }
    
    /// <summary>
    /// 池命中次数
    /// </summary>
    public long PoolHits { get; set; }
    
    /// <summary>
    /// 池未命中次数
    /// </summary>
    public long PoolMisses { get; set; }
    
    /// <summary>
    /// 当前池中对象数量
    /// </summary>
    public int CurrentPooledObjects { get; set; }
    
    /// <summary>
    /// 池命中率
    /// </summary>
    public double HitRate => TotalGets > 0 ? (double)PoolHits / TotalGets : 0;
}

/// <summary>
/// 任务调度器统计信息
/// 记录任务调度器的使用情况
/// </summary>
public class TaskSchedulerStatistics
{
    /// <summary>
    /// 总调度任务数
    /// </summary>
    public long TotalScheduledTasks { get; set; }
    
    /// <summary>
    /// 已完成任务数
    /// </summary>
    public long CompletedTasks { get; set; }
    
    /// <summary>
    /// 失败任务数
    /// </summary>
    public long FailedTasks { get; set; }
    
    /// <summary>
    /// 当前队列长度
    /// </summary>
    public int QueueLength { get; set; }
    
    /// <summary>
    /// 平均执行时间（毫秒）
    /// </summary>
    public double AverageExecutionTimeMs { get; set; }
    
    /// <summary>
    /// 任务成功率
    /// </summary>
    public double SuccessRate => TotalScheduledTasks > 0 ? (double)CompletedTasks / TotalScheduledTasks : 0;
}

/// <summary>
/// 性能报告
/// 包含详细的性能分析数据
/// </summary>
public class PerformanceReport
{
    /// <summary>
    /// 报告生成时间
    /// </summary>
    public DateTime GeneratedAt { get; set; }
    
    /// <summary>
    /// 报告时间范围
    /// </summary>
    public TimeSpan TimeRange { get; set; }
    
    /// <summary>
    /// CPU使用率（百分比）
    /// </summary>
    public double CpuUsagePercent { get; set; }
    
    /// <summary>
    /// 内存使用量（字节）
    /// </summary>
    public long MemoryUsageBytes { get; set; }
    
    /// <summary>
    /// GC回收次数
    /// </summary>
    public int GcCollectionCount { get; set; }
    
    /// <summary>
    /// 平均响应时间（毫秒）
    /// </summary>
    public double AverageResponseTimeMs { get; set; }
    
    /// <summary>
    /// 吞吐量（每秒操作数）
    /// </summary>
    public double ThroughputOps { get; set; }
    
    /// <summary>
    /// 性能指标集合
    /// </summary>
    public Dictionary<string, double> Metrics { get; set; } = new();
}

/// <summary>
/// 性能警告事件参数
/// 包含性能警告的详细信息
/// </summary>
public class PerformanceWarningEventArgs : EventArgs
{
    /// <summary>
    /// 警告信息列表
    /// </summary>
    public List<string> Warnings { get; set; } = new();
    
    /// <summary>
    /// 性能统计信息
    /// </summary>
    public PerformanceStatistics Statistics { get; set; } = new();
    
    /// <summary>
    /// 警告时间戳
    /// </summary>
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// 性能计时器接口
/// 用于测量操作执行时间
/// </summary>
public interface IPerformanceTimer : IDisposable
{
    /// <summary>
    /// 操作名称
    /// </summary>
    string OperationName { get; }
    
    /// <summary>
    /// 开始时间
    /// </summary>
    DateTime StartTime { get; }
    
    /// <summary>
    /// 已经过时间
    /// </summary>
    TimeSpan Elapsed { get; }
    
    /// <summary>
    /// 停止计时并记录结果
    /// </summary>
    void Stop();
}

/// <summary>
/// 对象池接口
/// 提供对象的获取和归还功能
/// </summary>
/// <typeparam name="T">对象类型</typeparam>
public interface IObjectPool<T> where T : class
{
    /// <summary>
    /// 从池中获取对象
    /// </summary>
    /// <returns>对象实例</returns>
    T Get();
    
    /// <summary>
    /// 将对象归还到池中
    /// </summary>
    /// <param name="obj">要归还的对象</param>
    void Return(T obj);
    
    /// <summary>
    /// 获取池统计信息
    /// </summary>
    /// <returns>池统计信息</returns>
    ObjectPoolStatistics GetStatistics();
}