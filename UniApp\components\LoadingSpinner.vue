<template>
  <view 
    class="loading-spinner"
    :class="[
      `size-${size}`,
      `type-${type}`,
      {
        'with-text': showText,
        'overlay': overlay,
        'fullscreen': fullscreen
      },
      customClass
    ]"
    v-if="visible"
  >
    <!-- 遮罩层 -->
    <view class="spinner-overlay" v-if="overlay || fullscreen"></view>
    
    <!-- 加载容器 -->
    <view class="spinner-container">
      <!-- 旋转圆圈 -->
      <view class="spinner-circle" v-if="type === 'circle'">
        <view class="circle-path"></view>
      </view>
      
      <!-- 点点加载 -->
      <view class="spinner-dots" v-else-if="type === 'dots'">
        <view class="dot" v-for="i in 3" :key="i"></view>
      </view>
      
      <!-- 脉冲加载 -->
      <view class="spinner-pulse" v-else-if="type === 'pulse'">
        <view class="pulse-ring" v-for="i in 3" :key="i"></view>
      </view>
      
      <!-- 波浪加载 -->
      <view class="spinner-wave" v-else-if="type === 'wave'">
        <view class="wave-bar" v-for="i in 5" :key="i"></view>
      </view>
      
      <!-- 方块加载 -->
      <view class="spinner-cube" v-else-if="type === 'cube'">
        <view class="cube" v-for="i in 9" :key="i"></view>
      </view>
      
      <!-- 弹跳球 -->
      <view class="spinner-bounce" v-else-if="type === 'bounce'">
        <view class="bounce-ball" v-for="i in 3" :key="i"></view>
      </view>
      
      <!-- 旋转方块 -->
      <view class="spinner-rotate" v-else-if="type === 'rotate'">
        <view class="rotate-cube"></view>
      </view>
      
      <!-- 进度环 -->
      <view class="spinner-progress" v-else-if="type === 'progress'">
        <svg class="progress-svg" viewBox="0 0 50 50">
          <circle 
            class="progress-bg" 
            cx="25" 
            cy="25" 
            r="20" 
            fill="none" 
            stroke="#f0f0f0" 
            stroke-width="4"
          />
          <circle 
            class="progress-bar" 
            cx="25" 
            cy="25" 
            r="20" 
            fill="none" 
            stroke="currentColor" 
            stroke-width="4"
            stroke-linecap="round"
            :stroke-dasharray="circumference"
            :stroke-dashoffset="progressOffset"
          />
        </svg>
        <text class="progress-text" v-if="showProgress">{{ progress }}%</text>
      </view>
      
      <!-- 默认旋转加载 -->
      <view class="spinner-default" v-else>
        <view class="default-ring"></view>
      </view>
      
      <!-- 加载文本 -->
      <view class="spinner-text" v-if="showText && text">
        <text class="text-content">{{ text }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed, ref, watch } from 'vue'

// Props定义
const props = defineProps({
  // 是否显示
  visible: {
    type: Boolean,
    default: true
  },
  // 加载类型
  type: {
    type: String,
    default: 'circle',
    validator: (value) => {
      return [
        'circle', 'dots', 'pulse', 'wave', 'cube', 
        'bounce', 'rotate', 'progress', 'default'
      ].includes(value)
    }
  },
  // 尺寸
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large', 'xlarge'].includes(value)
  },
  // 加载文本
  text: {
    type: String,
    default: '加载中...'
  },
  // 是否显示文本
  showText: {
    type: Boolean,
    default: false
  },
  // 是否显示遮罩
  overlay: {
    type: Boolean,
    default: false
  },
  // 是否全屏显示
  fullscreen: {
    type: Boolean,
    default: false
  },
  // 进度值（仅progress类型有效）
  progress: {
    type: Number,
    default: 0,
    validator: (value) => value >= 0 && value <= 100
  },
  // 是否显示进度文本
  showProgress: {
    type: Boolean,
    default: false
  },
  // 主题色
  color: {
    type: String,
    default: '#007AFF'
  },
  // 自定义样式类
  customClass: {
    type: [String, Array, Object],
    default: ''
  },
  // 延迟显示时间（毫秒）
  delay: {
    type: Number,
    default: 0
  },
  // 最小显示时间（毫秒）
  minDuration: {
    type: Number,
    default: 0
  }
})

// Events定义
const emit = defineEmits(['show', 'hide'])

// 响应式数据
const actualVisible = ref(props.visible)
const showTimer = ref(null)
const hideTimer = ref(null)
const startTime = ref(0)

// 计算属性
const circumference = computed(() => {
  return 2 * Math.PI * 20 // r=20
})

const progressOffset = computed(() => {
  const progress = Math.max(0, Math.min(100, props.progress))
  return circumference.value - (progress / 100) * circumference.value
})

// 方法
const show = () => {
  if (showTimer.value) {
    clearTimeout(showTimer.value)
    showTimer.value = null
  }
  
  if (hideTimer.value) {
    clearTimeout(hideTimer.value)
    hideTimer.value = null
  }
  
  if (props.delay > 0) {
    showTimer.value = setTimeout(() => {
      actualVisible.value = true
      startTime.value = Date.now()
      emit('show')
    }, props.delay)
  } else {
    actualVisible.value = true
    startTime.value = Date.now()
    emit('show')
  }
}

const hide = () => {
  if (showTimer.value) {
    clearTimeout(showTimer.value)
    showTimer.value = null
    return
  }
  
  if (hideTimer.value) {
    clearTimeout(hideTimer.value)
    hideTimer.value = null
  }
  
  const elapsed = Date.now() - startTime.value
  const remaining = Math.max(0, props.minDuration - elapsed)
  
  if (remaining > 0) {
    hideTimer.value = setTimeout(() => {
      actualVisible.value = false
      emit('hide')
    }, remaining)
  } else {
    actualVisible.value = false
    emit('hide')
  }
}

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      show()
    } else {
      hide()
    }
  },
  { immediate: true }
)

// 组件卸载时清理定时器
const cleanup = () => {
  if (showTimer.value) {
    clearTimeout(showTimer.value)
    showTimer.value = null
  }
  if (hideTimer.value) {
    clearTimeout(hideTimer.value)
    hideTimer.value = null
  }
}

// 暴露方法
defineExpose({
  show,
  hide,
  cleanup
})
</script>

<style lang="scss" scoped>
.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
  }
  
  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
  }
}

.spinner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
}

.spinner-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

// 尺寸样式
.size-small {
  .spinner-circle,
  .spinner-default {
    width: 32rpx;
    height: 32rpx;
  }
  
  .spinner-dots {
    .dot {
      width: 8rpx;
      height: 8rpx;
    }
  }
  
  .spinner-pulse {
    width: 32rpx;
    height: 32rpx;
  }
  
  .spinner-wave {
    .wave-bar {
      width: 4rpx;
      height: 24rpx;
    }
  }
  
  .spinner-text {
    margin-top: 12rpx;
    
    .text-content {
      font-size: 22rpx;
    }
  }
}

.size-medium {
  .spinner-circle,
  .spinner-default {
    width: 48rpx;
    height: 48rpx;
  }
  
  .spinner-dots {
    .dot {
      width: 12rpx;
      height: 12rpx;
    }
  }
  
  .spinner-pulse {
    width: 48rpx;
    height: 48rpx;
  }
  
  .spinner-wave {
    .wave-bar {
      width: 6rpx;
      height: 36rpx;
    }
  }
  
  .spinner-text {
    margin-top: 16rpx;
    
    .text-content {
      font-size: 26rpx;
    }
  }
}

.size-large {
  .spinner-circle,
  .spinner-default {
    width: 64rpx;
    height: 64rpx;
  }
  
  .spinner-dots {
    .dot {
      width: 16rpx;
      height: 16rpx;
    }
  }
  
  .spinner-pulse {
    width: 64rpx;
    height: 64rpx;
  }
  
  .spinner-wave {
    .wave-bar {
      width: 8rpx;
      height: 48rpx;
    }
  }
  
  .spinner-text {
    margin-top: 20rpx;
    
    .text-content {
      font-size: 30rpx;
    }
  }
}

.size-xlarge {
  .spinner-circle,
  .spinner-default {
    width: 80rpx;
    height: 80rpx;
  }
  
  .spinner-dots {
    .dot {
      width: 20rpx;
      height: 20rpx;
    }
  }
  
  .spinner-pulse {
    width: 80rpx;
    height: 80rpx;
  }
  
  .spinner-wave {
    .wave-bar {
      width: 10rpx;
      height: 60rpx;
    }
  }
  
  .spinner-text {
    margin-top: 24rpx;
    
    .text-content {
      font-size: 34rpx;
    }
  }
}

// 旋转圆圈
.spinner-circle {
  position: relative;
  
  .circle-path {
    width: 100%;
    height: 100%;
    border: 4rpx solid #f0f0f0;
    border-top: 4rpx solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

// 默认旋转
.spinner-default {
  position: relative;
  
  .default-ring {
    width: 100%;
    height: 100%;
    border: 4rpx solid rgba(0, 122, 255, 0.2);
    border-top: 4rpx solid #007AFF;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

// 点点加载
.spinner-dots {
  display: flex;
  gap: 8rpx;
  
  .dot {
    background: currentColor;
    border-radius: 50%;
    animation: dot-bounce 1.4s ease-in-out infinite both;
    
    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
    &:nth-child(3) { animation-delay: 0s; }
  }
}

// 脉冲加载
.spinner-pulse {
  position: relative;
  
  .pulse-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 4rpx solid currentColor;
    border-radius: 50%;
    animation: pulse-scale 1.5s ease-in-out infinite;
    
    &:nth-child(1) { animation-delay: 0s; }
    &:nth-child(2) { animation-delay: -0.5s; }
    &:nth-child(3) { animation-delay: -1s; }
  }
}

// 波浪加载
.spinner-wave {
  display: flex;
  align-items: flex-end;
  gap: 4rpx;
  
  .wave-bar {
    background: currentColor;
    animation: wave-stretch 1.2s ease-in-out infinite;
    
    &:nth-child(1) { animation-delay: -1.2s; }
    &:nth-child(2) { animation-delay: -1.1s; }
    &:nth-child(3) { animation-delay: -1.0s; }
    &:nth-child(4) { animation-delay: -0.9s; }
    &:nth-child(5) { animation-delay: -0.8s; }
  }
}

// 方块加载
.spinner-cube {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 4rpx;
  width: 48rpx;
  height: 48rpx;
  
  .cube {
    background: currentColor;
    animation: cube-fade 1.5s ease-in-out infinite;
    
    &:nth-child(1) { animation-delay: 0.0s; }
    &:nth-child(2) { animation-delay: 0.1s; }
    &:nth-child(3) { animation-delay: 0.2s; }
    &:nth-child(4) { animation-delay: 0.1s; }
    &:nth-child(5) { animation-delay: 0.2s; }
    &:nth-child(6) { animation-delay: 0.3s; }
    &:nth-child(7) { animation-delay: 0.2s; }
    &:nth-child(8) { animation-delay: 0.3s; }
    &:nth-child(9) { animation-delay: 0.4s; }
  }
}

// 弹跳球
.spinner-bounce {
  display: flex;
  gap: 8rpx;
  
  .bounce-ball {
    width: 16rpx;
    height: 16rpx;
    background: currentColor;
    border-radius: 50%;
    animation: bounce-up 1.4s ease-in-out infinite both;
    
    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
    &:nth-child(3) { animation-delay: 0s; }
  }
}

// 旋转方块
.spinner-rotate {
  .rotate-cube {
    width: 48rpx;
    height: 48rpx;
    background: currentColor;
    animation: rotate-cube 2s ease-in-out infinite;
  }
}

// 进度环
.spinner-progress {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  
  .progress-svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
  }
  
  .progress-bar {
    transition: stroke-dashoffset 0.3s ease;
    animation: progress-rotate 2s linear infinite;
  }
  
  .progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 20rpx;
    font-weight: 600;
    color: currentColor;
  }
}

// 加载文本
.spinner-text {
  text-align: center;
  
  .text-content {
    color: #666;
    line-height: 1.2;
  }
}

// 动画定义
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes dot-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes pulse-scale {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

@keyframes wave-stretch {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

@keyframes cube-fade {
  0%, 70%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  35% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes bounce-up {
  0%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20rpx);
  }
}

@keyframes rotate-cube {
  0% {
    transform: perspective(120rpx) rotateX(0deg) rotateY(0deg);
  }
  50% {
    transform: perspective(120rpx) rotateX(-180.1deg) rotateY(0deg);
  }
  100% {
    transform: perspective(120rpx) rotateX(-180deg) rotateY(-179.9deg);
  }
}

@keyframes progress-rotate {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -124;
  }
}
</style>