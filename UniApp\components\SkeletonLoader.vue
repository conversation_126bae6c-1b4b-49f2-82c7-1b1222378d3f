<template>
  <view class="skeleton-loader" :class="{'skeleton-animated': animated}">
    <!-- 列表骨架屏 -->
    <view v-if="type === 'list'" class="skeleton-list">
      <view v-for="i in rows" :key="i" class="skeleton-list-item">
        <view class="skeleton-avatar" v-if="showAvatar"></view>
        <view class="skeleton-content">
          <view class="skeleton-title" :style="{width: getTitleWidth(i)}"></view>
          <view class="skeleton-text" v-for="j in textLines" :key="j" :style="{width: getTextWidth(j)}"></view>
        </view>
        <view class="skeleton-action" v-if="showAction"></view>
      </view>
    </view>
    
    <!-- 卡片骨架屏 -->
    <view v-else-if="type === 'card'" class="skeleton-card">
      <view class="skeleton-card-header" v-if="showHeader">
        <view class="skeleton-avatar small"></view>
        <view class="skeleton-card-info">
          <view class="skeleton-title small"></view>
          <view class="skeleton-text small"></view>
        </view>
      </view>
      <view class="skeleton-card-image" v-if="showImage"></view>
      <view class="skeleton-card-content">
        <view class="skeleton-title"></view>
        <view class="skeleton-text" v-for="i in 3" :key="i" :style="{width: getTextWidth(i)}"></view>
      </view>
      <view class="skeleton-card-footer" v-if="showFooter">
        <view class="skeleton-button" v-for="i in 2" :key="i"></view>
      </view>
    </view>
    
    <!-- 表格骨架屏 -->
    <view v-else-if="type === 'table'" class="skeleton-table">
      <view class="skeleton-table-header">
        <view v-for="col in columns" :key="col" class="skeleton-table-cell header"></view>
      </view>
      <view v-for="i in rows" :key="i" class="skeleton-table-row">
        <view v-for="col in columns" :key="col" class="skeleton-table-cell"></view>
      </view>
    </view>
    
    <!-- 文章骨架屏 -->
    <view v-else-if="type === 'article'" class="skeleton-article">
      <view class="skeleton-article-header">
        <view class="skeleton-title large"></view>
        <view class="skeleton-meta">
          <view class="skeleton-avatar mini"></view>
          <view class="skeleton-text small"></view>
          <view class="skeleton-text small"></view>
        </view>
      </view>
      <view class="skeleton-article-image" v-if="showImage"></view>
      <view class="skeleton-article-content">
        <view v-for="i in 8" :key="i" class="skeleton-text" :style="{width: getArticleTextWidth(i)}"></view>
      </view>
    </view>
    
    <!-- 表单骨架屏 -->
    <view v-else-if="type === 'form'" class="skeleton-form">
      <view v-for="i in formFields" :key="i" class="skeleton-form-field">
        <view class="skeleton-label"></view>
        <view class="skeleton-input"></view>
      </view>
      <view class="skeleton-form-actions">
        <view class="skeleton-button primary"></view>
        <view class="skeleton-button secondary"></view>
      </view>
    </view>
    
    <!-- 网格骨架屏 -->
    <view v-else-if="type === 'grid'" class="skeleton-grid">
      <view v-for="i in gridItems" :key="i" class="skeleton-grid-item">
        <view class="skeleton-grid-image"></view>
        <view class="skeleton-grid-title"></view>
        <view class="skeleton-grid-text"></view>
      </view>
    </view>
    
    <!-- 自定义骨架屏 -->
    <view v-else-if="type === 'custom'" class="skeleton-custom">
      <slot></slot>
    </view>
    
    <!-- 默认骨架屏 -->
    <view v-else class="skeleton-default">
      <view class="skeleton-title"></view>
      <view class="skeleton-text" v-for="i in 3" :key="i" :style="{width: getTextWidth(i)}"></view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'SkeletonLoader',
  props: {
    // 骨架屏类型
    type: {
      type: String,
      default: 'default',
      validator: (value) => {
        return ['list', 'card', 'table', 'article', 'form', 'grid', 'custom', 'default'].includes(value)
      }
    },
    // 行数
    rows: {
      type: Number,
      default: 3
    },
    // 文本行数
    textLines: {
      type: Number,
      default: 2
    },
    // 表格列数
    columns: {
      type: Number,
      default: 4
    },
    // 表单字段数
    formFields: {
      type: Number,
      default: 4
    },
    // 网格项目数
    gridItems: {
      type: Number,
      default: 6
    },
    // 是否显示头像
    showAvatar: {
      type: Boolean,
      default: true
    },
    // 是否显示操作按钮
    showAction: {
      type: Boolean,
      default: false
    },
    // 是否显示头部
    showHeader: {
      type: Boolean,
      default: true
    },
    // 是否显示图片
    showImage: {
      type: Boolean,
      default: true
    },
    // 是否显示底部
    showFooter: {
      type: Boolean,
      default: true
    },
    // 是否启用动画
    animated: {
      type: Boolean,
      default: true
    },
    // 自定义样式
    customStyle: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    // 获取标题宽度
    getTitleWidth(index) {
      const widths = ['85%', '75%', '90%', '80%', '70%']
      return widths[index % widths.length]
    },
    
    // 获取文本宽度
    getTextWidth(index) {
      const widths = ['100%', '85%', '95%', '75%', '90%']
      return widths[index % widths.length]
    },
    
    // 获取文章文本宽度
    getArticleTextWidth(index) {
      if (index === 1) return '100%'
      if (index === 8) return '60%'
      const widths = ['95%', '100%', '90%', '100%', '85%', '100%', '95%']
      return widths[index % widths.length]
    }
  }
}
</script>

<style scoped>
.skeleton-loader {
  padding: 20rpx;
}

/* 动画效果 */
.skeleton-animated .skeleton-title,
.skeleton-animated .skeleton-text,
.skeleton-animated .skeleton-avatar,
.skeleton-animated .skeleton-input,
.skeleton-animated .skeleton-button,
.skeleton-animated .skeleton-card-image,
.skeleton-animated .skeleton-article-image,
.skeleton-animated .skeleton-grid-image,
.skeleton-animated .skeleton-table-cell {
  position: relative;
  overflow: hidden;
}

.skeleton-animated .skeleton-title::after,
.skeleton-animated .skeleton-text::after,
.skeleton-animated .skeleton-avatar::after,
.skeleton-animated .skeleton-input::after,
.skeleton-animated .skeleton-button::after,
.skeleton-animated .skeleton-card-image::after,
.skeleton-animated .skeleton-article-image::after,
.skeleton-animated .skeleton-grid-image::after,
.skeleton-animated .skeleton-table-cell::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

@keyframes skeleton-loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 基础骨架元素 */
.skeleton-title {
  height: 32rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  margin-bottom: 16rpx;
}

.skeleton-title.small {
  height: 24rpx;
}

.skeleton-title.large {
  height: 40rpx;
}

.skeleton-text {
  height: 24rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  margin-bottom: 12rpx;
}

.skeleton-text.small {
  height: 20rpx;
}

.skeleton-avatar {
  width: 80rpx;
  height: 80rpx;
  background-color: #f0f0f0;
  border-radius: 50%;
  flex-shrink: 0;
}

.skeleton-avatar.small {
  width: 60rpx;
  height: 60rpx;
}

.skeleton-avatar.mini {
  width: 40rpx;
  height: 40rpx;
}

.skeleton-button {
  height: 60rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  margin: 0 10rpx;
  flex: 1;
}

.skeleton-button.primary {
  background-color: #e6f3ff;
}

.skeleton-button.secondary {
  background-color: #f5f5f5;
}

.skeleton-input {
  height: 80rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.skeleton-label {
  height: 28rpx;
  width: 120rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  margin-bottom: 12rpx;
}

/* 列表骨架屏 */
.skeleton-list-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.skeleton-content {
  flex: 1;
  margin-left: 20rpx;
}

.skeleton-action {
  width: 60rpx;
  height: 40rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  margin-left: 20rpx;
}

/* 卡片骨架屏 */
.skeleton-card {
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.skeleton-card-header {
  display: flex;
  align-items: center;
  padding: 20rpx;
}

.skeleton-card-info {
  flex: 1;
  margin-left: 20rpx;
}

.skeleton-card-image {
  height: 300rpx;
  background-color: #f0f0f0;
}

.skeleton-card-content {
  padding: 20rpx;
}

.skeleton-card-footer {
  display: flex;
  padding: 20rpx;
  border-top: 1rpx solid #f5f5f5;
}

/* 表格骨架屏 */
.skeleton-table {
  border: 1rpx solid #f0f0f0;
  border-radius: 8rpx;
  overflow: hidden;
}

.skeleton-table-header {
  display: flex;
  background-color: #f8f9fa;
}

.skeleton-table-row {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.skeleton-table-cell {
  flex: 1;
  height: 80rpx;
  background-color: #f0f0f0;
  margin: 10rpx;
  border-radius: 4rpx;
}

.skeleton-table-cell.header {
  background-color: #e9ecef;
}

/* 文章骨架屏 */
.skeleton-article-header {
  margin-bottom: 30rpx;
}

.skeleton-meta {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
}

.skeleton-meta .skeleton-text {
  width: 100rpx;
  margin-left: 20rpx;
}

.skeleton-article-image {
  height: 400rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  margin-bottom: 30rpx;
}

.skeleton-article-content .skeleton-text {
  margin-bottom: 16rpx;
}

/* 表单骨架屏 */
.skeleton-form-field {
  margin-bottom: 30rpx;
}

.skeleton-form-actions {
  display: flex;
  margin-top: 40rpx;
}

/* 网格骨架屏 */
.skeleton-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.skeleton-grid-item {
  background-color: #ffffff;
  border-radius: 8rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.skeleton-grid-image {
  height: 200rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
}

.skeleton-grid-title {
  height: 28rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  margin-bottom: 12rpx;
}

.skeleton-grid-text {
  height: 20rpx;
  width: 80%;
  background-color: #f0f0f0;
  border-radius: 4rpx;
}

/* 默认骨架屏 */
.skeleton-default {
  padding: 20rpx;
}
</style>