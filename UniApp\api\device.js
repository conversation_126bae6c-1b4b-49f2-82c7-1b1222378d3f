/**
 * 设备管理相关API
 */

import request from '../utils/request.js'
import { createCachedApi } from '../src/utils/apiCache.js' 

const BASE_URL = '/api/energyDevice'

/**
 * 获取设备列表
 * @param {Object} params 查询参数
 * @param {number} params.page 页码
 * @param {number} params.pageSize 每页数量
 * @param {string} params.keyword 关键词搜索
 * @param {string} params.status 设备状态
 * @param {string} params.type 设备类型
 * @param {string} params.groupId 设备分组ID
 * @returns {Promise} 设备列表
 */
export const getDeviceList = (params) => {
  return request({
    url: `${BASE_URL}/page`,
    method: 'GET',
    params
  })
}

/**
 * 获取设备分页列表
 * @param {Object} params 查询参数
 * @returns {Promise} 设备分页列表
 */
export const getDevicePage = (params = {}) => {
  return request({
    url: `${BASE_URL}/page`,
    method: 'GET',
    params
  })
}

/**
 * 获取设备详情
 * @param {string} deviceId 设备ID
 * @returns {Promise} 设备详情
 */
export const getDeviceDetail = (deviceId) => {
  return request({
    url: `${BASE_URL}/detail`,
    method: 'GET',
    params: { id: deviceId }
  })
}

/**
 * 添加设备
 * @param {Object} deviceData 设备数据
 * @param {string} deviceData.name 设备名称
 * @param {string} deviceData.code 设备编码
 * @param {string} deviceData.type 设备类型
 * @param {string} deviceData.model 设备型号
 * @param {string} deviceData.location 设备位置
 * @param {string} deviceData.groupId 设备分组ID
 * @param {string} deviceData.description 设备描述
 * @returns {Promise} 添加结果
 */
export const addDevice = (data) => {
  return request({
    url: `${BASE_URL}/add`,
    method: 'POST',
    data
  })
}

/**
 * 获取设备分组列表（别名函数）
 * @returns {Promise} 分组列表
 */
export const getGroups = () => {
  return getDeviceGroups()
}

/**
 * 批量切换设备开关状态
 * @param {Array} deviceIds 设备ID数组
 * @param {boolean} status 目标状态
 * @returns {Promise} 控制结果
 */
export const batchToggle = (deviceIds, status) => {
  return request({
    url: `${BASE_URL}/batch-toggle`,
    method: 'POST',
    data: {
      deviceIds,
      status
    }
  })
}

/**
 * 切换单个设备开关状态
 * @param {string} deviceId 设备ID
 * @param {boolean} status 目标状态
 * @returns {Promise} 控制结果
 */
export const toggleDeviceSwitch = (deviceId, status) => {
  return request({
    url: `${BASE_URL}/${deviceId}/toggle`,
    method: 'POST',
    data: { status }
  })
}

/**
 * 设置设备亮度
 * @param {string} deviceId 设备ID
 * @param {number} brightness 亮度值 0-100
 * @returns {Promise} 控制结果
 */
export const setDeviceBrightness = (deviceId, brightness) => {
  return request({
    url: `${BASE_URL}/${deviceId}/brightness`,
    method: 'POST',
    data: { brightness }
  })
}

/**
 * 批量设置设备亮度
 * @param {Array} deviceIds 设备ID数组
 * @param {number} brightness 亮度值 0-100
 * @returns {Promise} 控制结果
 */
export const batchSetBrightness = (deviceIds, brightness) => {
  return request({
    url: `${BASE_URL}/batch-brightness`,
    method: 'POST',
    data: {
      deviceIds,
      brightness
    }
  })
}

/**
 * 设置设备色温
 * @param {string} deviceId 设备ID
 * @param {number} colorTemp 色温值
 * @returns {Promise} 控制结果
 */
export const setDeviceColorTemp = (deviceId, colorTemp) => {
  return request({
    url: `${BASE_URL}/${deviceId}/color-temp`,
    method: 'POST',
    data: { colorTemp }
  })
}

/**
 * 切换设备定时器
 * @param {string} deviceId 设备ID
 * @param {Object} timerData 定时器数据
 * @returns {Promise} 控制结果
 */
export const toggleDeviceTimer = (deviceId, timerData) => {
  return request({
    url: `${BASE_URL}/${deviceId}/timer`,
    method: 'POST',
    data: timerData
  })
}

/**
 * 更新设备信息
 * @param {string} deviceId 设备ID
 * @param {Object} deviceData 设备数据
 * @returns {Promise} 更新结果
 */
export const updateDevice = (data) => {
  return request({
    url: `${BASE_URL}/update`,
    method: 'POST',
    data
  })
}

/**
 * 删除设备
 * @param {string} deviceId 设备ID
 * @returns {Promise} 删除结果
 */
export const deleteDevice = (id) => {
  return request({
    url: `${BASE_URL}/delete`,
    method: 'POST',
    data: { id }
  })
}

/**
 * 设置设备状态
 * @param {string} deviceId 设备ID
 * @param {number} status 设备状态
 * @returns {Promise} 设置结果
 */
export const setDeviceStatus = (deviceId, status) => {
  return request({
    url: `${BASE_URL}/setStatus`,
    method: 'POST',
    data: { deviceId, status }
  })
}

/**
 * 批量删除设备
 * @param {Array} deviceIds 设备ID数组
 * @returns {Promise} 删除结果
 */
export const batchDeleteDevice = (deviceIds) => {
  return request({
    url: `${BASE_URL}/batch-delete`,
    method: 'DELETE',
    data: { ids: deviceIds }
  })
}

/**
 * 控制设备开关
 * @param {string} deviceId 设备ID
 * @param {boolean} status 开关状态 true-开启 false-关闭
 * @returns {Promise} 控制结果
 */
export const controlDeviceSwitch = (deviceId, status) => {
  return request({
    url: '/api/energyControl/deviceControl',
    method: 'POST',
    data: { deviceId, status }
  })
}

/**
 * 调节设备亮度
 * @param {string} deviceId 设备ID
 * @param {number} brightness 亮度值 0-100
 * @returns {Promise} 控制结果
 */
export const controlDeviceBrightness = (deviceId, brightness) => {
  return request({
    url: '/api/energyControl/deviceControl',
    method: 'POST',
    data: { deviceId, brightness }
  })
}

/**
 * 调节设备色温
 * @param {string} deviceId 设备ID
 * @param {number} colorTemp 色温值 2700-6500K
 * @returns {Promise} 控制结果
 */
export const controlDeviceColorTemp = (deviceId, colorTemp) => {
  return request({
    url: '/api/energyControl/deviceControl',
    method: 'POST',
    data: { deviceId, colorTemp }
  })
}

/**
 * 设置设备颜色
 * @param {string} deviceId 设备ID
 * @param {Object} color 颜色值
 * @param {number} color.r 红色值 0-255
 * @param {number} color.g 绿色值 0-255
 * @param {number} color.b 蓝色值 0-255
 * @returns {Promise} 控制结果
 */
export const controlDeviceColor = (deviceId, color) => {
  return request({
    url: '/api/energyControl/deviceControl',
    method: 'POST',
    data: { deviceId, color }
  })
}

/**
 * 获取设备实时状态
 * @param {string} deviceId 设备ID
 * @returns {Promise} 设备状态
 */
export const getDeviceStatus = (deviceId) => {
  return request({
    url: `${BASE_URL}/status/${deviceId}`,
    method: 'GET'
  })
}

/**
 * 获取设备历史数据
 * @param {string} deviceId 设备ID
 * @param {Object} params 查询参数
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @param {string} params.dataType 数据类型
 * @returns {Promise} 历史数据
 */
export const getDeviceHistory = (deviceId, params = {}) => {
  return request({
    url: `${BASE_URL}/${deviceId}/history`,
    method: 'GET',
    params
  })
}

/**
 * 获取设备分组列表
 * @returns {Promise} 分组列表
 */
export const getDeviceGroups = () => {
  return request({
    url: `${BASE_URL}/groups`,
    method: 'GET'
  })
}

/**
 * 添加设备分组
 * @param {Object} groupData 分组数据
 * @param {string} groupData.name 分组名称
 * @param {string} groupData.description 分组描述
 * @param {string} groupData.parentId 父分组ID
 * @returns {Promise} 添加结果
 */
export const addDeviceGroup = (groupData) => {
  return request({
    url: `${BASE_URL}/groups/add`,
    method: 'POST',
    data: groupData
  })
}

/**
 * 更新设备分组
 * @param {string} groupId 分组ID
 * @param {Object} groupData 分组数据
 * @returns {Promise} 更新结果
 */
export const updateDeviceGroup = (groupId, groupData) => {
  return request({
    url: `${BASE_URL}/groups/${groupId}`,
    method: 'PUT',
    data: groupData
  })
}

/**
 * 删除设备分组
 * @param {string} groupId 分组ID
 * @returns {Promise} 删除结果
 */
export const deleteDeviceGroup = (groupId) => {
  return request({
    url: `${BASE_URL}/groups/${groupId}`,
    method: 'DELETE'
  })
}

/**
 * 移动设备到分组
 * @param {string} deviceId 设备ID
 * @param {string} groupId 目标分组ID
 * @returns {Promise} 移动结果
 */
export const moveDeviceToGroup = (deviceId, groupId) => {
  return request({
    url: `${BASE_URL}/move/${deviceId}`,
    method: 'PUT',
    data: { groupId }
  })
}

/**
 * 批量控制设备
 * @param {Array} deviceIds 设备ID数组
 * @param {Object} controlData 控制数据
 * @param {boolean} controlData.status 开关状态
 * @param {number} controlData.brightness 亮度值
 * @param {number} controlData.colorTemp 色温值
 * @returns {Promise} 控制结果
 */
export const batchControlDevices = (deviceIds, controlData) => {
  return request({
    url: '/api/energyControl/deviceControl',
    method: 'POST',
    data: { deviceIds, controlData }
  })
}

/**
 * 获取设备类型分布
 * @returns {Promise} 设备类型列表
 */
export const getDeviceTypes = () => {
  return request({
    url: `${BASE_URL}/typeDistribution`,
    method: 'GET'
  })
}
 
/**
 * 获取设备统计信息
 * @param {Object} params 查询参数
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @param {string} params.timeRange 时间范围
 * @returns {Promise} 统计信息
 */
export const getDeviceStatistics = (params = {}) => {
  return request({
    url: `${BASE_URL}/stats`,
    method: 'GET',
    params
  })
}

/**
 * 获取设备统计信息（别名函数）
 * @param {Object} params 查询参数
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @param {string} params.timeRange 时间范围
 * @returns {Promise} 统计信息
 */
export const getDeviceStats = (params = {}) => {
  return request({
    url: `${BASE_URL}/stats`,
    method: 'GET',
    params
  })
}

/**
 * 重启设备
 * @param {string} deviceId 设备ID
 * @returns {Promise} 重启结果
 */
export const rebootDevice = (deviceId) => {
  return request({
    url: `${BASE_URL}/reboot/${deviceId}`,
    method: 'POST'
  })
}

/**
 * 重置设备
 * @param {string} deviceId 设备ID
 * @returns {Promise} 重置结果
 */
export const resetDevice = (deviceId) => {
  return request({
    url: `${BASE_URL}/reset/${deviceId}`,
    method: 'POST'
  })
}

/**
 * 获取设备固件版本
 * @param {string} deviceId 设备ID
 * @returns {Promise} 固件版本信息
 */
export const getDeviceFirmware = (deviceId) => {
  return request({
    url: `${BASE_URL}/firmware/${deviceId}`,
    method: 'GET'
  })
}

/**
 * 升级设备固件
 * @param {string} deviceId 设备ID
 * @param {string} firmwareVersion 固件版本
 * @returns {Promise} 升级结果
 */
export const upgradeDeviceFirmware = (deviceId, firmwareVersion) => {
  return request({
    url: `${BASE_URL}/firmware/upgrade/${deviceId}`,
    method: 'POST',
    data: { firmwareVersion }
  })
}

// 创建缓存版本的API函数
const cachedDeviceApi = {
  getDeviceList: createCachedApi(getDeviceList, 'device_list', 2 * 60 * 1000),
  getDeviceDetail: createCachedApi(getDeviceDetail, 'device_detail', 5 * 60 * 1000),
  getDeviceStatus: createCachedApi(getDeviceStatus, 'device_status', 30 * 1000),
  getDeviceHistory: createCachedApi(getDeviceHistory, 'device_history', 5 * 60 * 1000),
  getDeviceGroups: createCachedApi(getDeviceGroups, 'device_groups', 5 * 60 * 1000),
  getDeviceTypes: createCachedApi(getDeviceTypes, 'device_types', 10 * 60 * 1000),
  getDeviceStatistics: createCachedApi(getDeviceStatistics, 'device_statistics', 2 * 60 * 1000),
  getDeviceFirmware: createCachedApi(getDeviceFirmware, 'device_firmware', 10 * 60 * 1000)
}

/**
 * 导出设备数据
 * @param {Object} params 导出参数
 * @returns {Promise} 导出结果
 */
export const exportDeviceData = (params = {}) => {
  return request({
    url: `${BASE_URL}/exportData`,
    method: 'POST',
    data: params,
    responseType: 'blob'
  })
}

/**
 * 导出设备数据到Excel
 * @param {Object} params 导出参数
 * @returns {Promise} Excel文件
 */
export const exportDeviceExcel = (params = {}) => {
  return request({
    url: `${BASE_URL}/exportExcel`,
    method: 'POST',
    data: params,
    responseType: 'blob'
  })
}

// 默认导出所有设备管理相关API
export default {
  // 基础CRUD操作
  getDeviceList,
  getDevicePage,
  getDeviceDetail,
  addDevice,
  updateDevice,
  deleteDevice,
  batchDeleteDevice,
  setDeviceStatus,
  
  // 设备控制操作
  controlDeviceSwitch,
  controlDeviceBrightness,
  controlDeviceColorTemp,
  controlDeviceColor,
  batchControlDevices,
  
  // 设备状态和信息
  getDeviceStatus,
  getDeviceHistory,
  getDeviceTypes,
  getDeviceStatistics,
  getDeviceStats,
  
  // 设备分组管理
  getDeviceGroups,
  getGroups,
  addDeviceGroup,
  updateDeviceGroup,
  deleteDeviceGroup,
  moveDeviceToGroup,
  
  // 设备控制和操作
  batchToggle,
  toggleDeviceSwitch,
  setDeviceBrightness,
  batchSetBrightness,
  setDeviceColorTemp,
  toggleDeviceTimer,
  rebootDevice,
  resetDevice,
  
  // 固件管理
  getDeviceFirmware,
  upgradeDeviceFirmware,
  
  // 数据导出
  exportDeviceData,
  exportDeviceExcel,
  
  // 缓存版本的API函数
  ...cachedDeviceApi
}