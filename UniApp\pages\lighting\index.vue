<template>
	<view class="lighting-container">
		<!-- 顶部统计 -->
		<view class="stats-section">
			<view class="stats-card" v-for="(stat, index) in statsData" :key="index">
				<view class="stat-icon" :class="stat.iconClass">
					<text>{{ stat.icon }}</text>
				</view>
				<view class="stat-content">
					<view class="stat-value">{{ stat.value }}</view>
					<view class="stat-label">{{ stat.label }}</view>
				</view>
			</view>
		</view>
		
		<!-- 快速控制 -->
		<view class="quick-control-card">
			<view class="card-header">
				<view class="card-title">快速控制</view>
				<view class="control-mode">
					<text :class="controlMode === 'single' ? 'mode-active' : ''" @tap="setControlMode('single')">单控</text>
					<text :class="controlMode === 'group' ? 'mode-active' : ''" @tap="setControlMode('group')">群控</text>
				</view>
			</view>
			
			<!-- 全局控制 -->
			<view class="global-controls">
				<view class="control-row">
					<view class="control-item">
						<text class="control-label">全部开关</text>
						<switch 
							:checked="globalSwitch" 
							@change="handleGlobalSwitch"
							color="#1890ff"
						/>
					</view>
				</view>
				
				<view class="control-row" v-if="globalSwitch">
					<view class="control-item">
						<text class="control-label">全局亮度</text>
						<text class="brightness-value">{{ globalBrightness }}%</text>
					</view>
				</view>
				
				<view class="brightness-slider-container" v-if="globalSwitch">
					<view class="brightness-icons">
						<text class="brightness-icon">🔅</text>
						<slider 
							class="global-brightness-slider"
							v-model="globalBrightness" 
							:min="0" 
							:max="100" 
							:step="1"
							@change="handleGlobalBrightness"
							active-color="#1890ff"
							background-color="#f0f0f0"
						/>
						<text class="brightness-icon">🔆</text>
					</view>
				</view>
				
				<!-- 快捷操作 -->
				<view class="quick-actions">
					<view 
						class="quick-btn" 
						v-for="action in quickActions" 
						:key="action.key"
						@tap="executeQuickAction(action)"
					>
						<text class="quick-icon">{{ action.icon }}</text>
						<text class="quick-text">{{ action.label }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 情景模式 -->
		<view class="scene-card">
			<view class="card-header">
				<view class="card-title">情景模式</view>
				<view class="add-scene-btn" @tap="addScene">
					<text>+ 自定义</text>
				</view>
			</view>
			
			<view class="scene-grid">
				<view 
					class="scene-item" 
					v-for="scene in sceneList" 
					:key="scene.id"
					:class="{ active: currentScene === scene.id }"
					@tap="applyScene(scene)"
				>
					<view class="scene-icon" :style="{ background: scene.gradient }">
						<text>{{ scene.icon }}</text>
					</view>
					<view class="scene-name">{{ scene.name }}</view>
					<view class="scene-desc">{{ scene.description }}</view>
					<view class="scene-status" v-if="currentScene === scene.id">
						<text>已应用</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 设备列表 -->
		<view class="device-list-card">
			<view class="card-header">
				<view class="card-title">设备控制</view>
				<view class="filter-tabs">
					<text 
						v-for="filter in deviceFilters" 
						:key="filter.key"
						:class="{ active: currentFilter === filter.key }"
						@tap="setDeviceFilter(filter.key)"
					>
						{{ filter.label }}
					</text>
				</view>
			</view>
			
			<view class="device-list">
				<view 
					class="device-item" 
					v-for="device in filteredDevices" 
					:key="device.id"
				>
					<view class="device-info">
						<view class="device-avatar">
							<text class="device-icon">💡</text>
							<view class="device-status-dot" :class="device.statusClass"></view>
						</view>
						<view class="device-details">
							<view class="device-name">{{ device.name }}</view>
							<view class="device-location">{{ device.location }}</view>
							<view class="device-power" v-if="device.isOn">
								{{ device.currentPower }}W · {{ device.brightness }}%
							</view>
						</view>
					</view>
					
					<view class="device-controls">
						<view class="control-switch">
							<switch 
								:checked="device.isOn" 
								@change="toggleDevice(device)"
								color="#1890ff"
							/>
						</view>
						
						<view class="brightness-control" v-if="device.isOn">
							<slider 
							class="device-brightness-slider"
							:value="device.brightness" 
							:min="0" 
							:max="100" 
							:step="1"
							@change="adjustDeviceBrightness(device, $event)"
							active-color="#1890ff"
							background-color="#f0f0f0"
						/>
						</view>
						
						<view class="device-actions">
							<view class="action-btn" @tap="viewDeviceDetail(device)">
								<text>详情</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 定时任务 -->
		<view class="timer-card">
			<view class="card-header">
				<view class="card-title">定时任务</view>
				<view class="view-all-btn" @tap="viewAllTimers">
					<text>查看全部</text>
				</view>
			</view>
			
			<view class="timer-list">
				<view class="timer-item" v-for="timer in recentTimers" :key="timer.id">
					<view class="timer-icon" :class="timer.iconClass">
						<text>{{ timer.icon }}</text>
					</view>
					<view class="timer-info">
						<view class="timer-name">{{ timer.name }}</view>
						<view class="timer-time">{{ timer.time }} · {{ timer.repeatText }}</view>
						<view class="timer-devices">{{ timer.deviceCount }}个设备</view>
					</view>
					<view class="timer-status">
						<switch 
							:checked="timer.enabled" 
							@change="toggleTimer(timer)"
							color="#1890ff"
						/>
					</view>
				</view>
				
				<view v-if="recentTimers.length === 0" class="empty">
					<view class="empty-icon">⏰</view>
					<view class="empty-text">暂无定时任务</view>
					<view class="empty-action" @tap="addTimer">
						<text>创建定时任务</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { 
	getLightingOverview, 
	getLightingDeviceList, 
	getLightingTimerList,
	globalLightingControl,
	updateGlobalBrightness,
	executeQuickAction,
	applyLightingScene,
	toggleLightingDevice,
	updateDeviceBrightness,
	toggleLightingTimer
} from '@/api/lighting.js'

export default {
	data() {
		return {
			loading: false,
			controlMode: 'single', // single: 单控, group: 群控
			globalSwitch: false,
			globalBrightness: 80,
			currentScene: null,
			currentFilter: 'all',
			
			// 统计数据
			statsData: [
				{
					icon: '💡',
					iconClass: 'stat-devices',
					value: '0',
					label: '在线设备'
				},
				{
					icon: '🔆',
					iconClass: 'stat-brightness',
					value: '0%',
					label: '平均亮度'
				},
				{
					icon: '⚡',
					iconClass: 'stat-power',
					value: '0W',
					label: '总功耗'
				},
				{
					icon: '⏰',
					iconClass: 'stat-timers',
					value: '0',
					label: '定时任务'
				}
			],
			
			// 快捷操作
			quickActions: [
				{ key: 'all_on', icon: '🌞', label: '全部开启' },
				{ key: 'all_off', icon: '🌙', label: '全部关闭' },
				{ key: 'energy_save', icon: '🍃', label: '节能模式' },
				{ key: 'max_bright', icon: '☀️', label: '最大亮度' }
			],
			
			// 情景模式
			sceneList: [
				{
					id: 1,
					name: '工作模式',
					description: '高亮度，适合办公',
					icon: '💼',
					gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
					brightness: 90,
					colorTemp: 5000
				},
				{
					id: 2,
					name: '休息模式',
					description: '柔和光线，放松心情',
					icon: '🛋️',
					gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
					brightness: 40,
					colorTemp: 3000
				},
				{
					id: 3,
					name: '会议模式',
					description: '均匀照明，专业会议',
					icon: '👥',
					gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
					brightness: 85,
					colorTemp: 4500
				},
				{
					id: 4,
					name: '节能模式',
					description: '低功耗，环保节能',
					icon: '🍃',
					gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
					brightness: 30,
					colorTemp: 3500
				},
				{
					id: 5,
					name: '夜间模式',
					description: '微弱光线，不影响睡眠',
					icon: '🌙',
					gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
					brightness: 10,
					colorTemp: 2700
				},
				{
					id: 6,
					name: '阅读模式',
					description: '护眼光线，舒适阅读',
					icon: '📖',
					gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
					brightness: 70,
					colorTemp: 4000
				}
			],
			
			// 设备筛选
			deviceFilters: [
				{ key: 'all', label: '全部' },
				{ key: 'online', label: '在线' },
				{ key: 'offline', label: '离线' },
				{ key: 'on', label: '开启' },
				{ key: 'off', label: '关闭' }
			],
			
			// 设备列表
			deviceList: [],
			
			// 定时任务
			recentTimers: []
		}
	},
	
	computed: {
		// 过滤后的设备列表
		filteredDevices() {
			if (this.currentFilter === 'all') {
				return this.deviceList
			}
			
			return this.deviceList.filter(device => {
				switch (this.currentFilter) {
					case 'online':
						return device.status === 'online'
					case 'offline':
						return device.status === 'offline'
					case 'on':
						return device.isOn
					case 'off':
						return !device.isOn
					default:
						return true
				}
			})
		}
	},
	
	onLoad() {
		this.loadLightingData()
	},
	
	onShow() {
		// 页面显示时刷新数据
		this.refreshData()
	},
	
	onUnload() {
		// 清理全局亮度调节定时器
		if (this.globalBrightnessTimer) {
			clearTimeout(this.globalBrightnessTimer)
			this.globalBrightnessTimer = null
		}
		
		// 清理设备亮度调节定时器
		if (this.deviceList && this.deviceList.length > 0) {
			this.deviceList.forEach(device => {
				if (device.brightnessTimer) {
					clearTimeout(device.brightnessTimer)
					device.brightnessTimer = null
				}
			})
		}
	},
	
	methods: {
		// 加载照明数据
		async loadLightingData() {
			try {
				uni.showLoading({ title: '加载数据...' })
				
				// 并发获取数据
				const [overviewRes, deviceRes, timerRes] = await Promise.all([
					getLightingOverview(),
					getLightingDeviceList(),
					getLightingTimerList()
				])
				
				// 更新统计数据
				if (overviewRes.data) {
					const overview = overviewRes.data
					this.statsData[0].value = overview.onlineDevices?.toString() || '0'
					this.statsData[1].value = `${overview.averageBrightness || 0}%`
					this.statsData[2].value = `${overview.totalPower || 0}W`
					this.statsData[3].value = overview.timerCount?.toString() || '0'
				}
				
				// 更新设备列表
				if (deviceRes.data) {
					this.deviceList = deviceRes.data
				}
				
				// 更新定时任务
				if (timerRes.data) {
					this.recentTimers = timerRes.data
				}
				
				// 更新全局开关状态
				this.updateGlobalSwitch()
				
				uni.hideLoading()
			} catch (error) {
				console.error('加载照明数据失败:', error)
				uni.hideLoading()
				uni.showToast({
					title: error.message || '加载数据失败',
					icon: 'error'
				})
			}
		},
		
		// 刷新数据
		async refreshData() {
			try {
				await this.loadLightingData()
				uni.showToast({
					title: '刷新成功',
					icon: 'success'
				})
			} catch (error) {
				console.error('刷新数据失败:', error)
				uni.showToast({
					title: error.message || '刷新失败',
					icon: 'error'
				})
			}
		},
		
		// 更新全局开关状态
		updateGlobalSwitch() {
			const onlineDevices = this.deviceList.filter(d => d.status === 'online')
			const onDevices = onlineDevices.filter(d => d.isOn)
			
			this.globalSwitch = onDevices.length > 0
			
			if (onDevices.length > 0) {
				this.globalBrightness = Math.round(
					onDevices.reduce((sum, d) => sum + d.brightness, 0) / onDevices.length
				)
			}
		},
		
		// 设置控制模式
		setControlMode(mode) {
			this.controlMode = mode
			uni.showToast({
				title: `已切换到${mode === 'single' ? '单控' : '群控'}模式`,
				icon: 'success'
			})
		},
		
		// 全局开关处理
		async handleGlobalSwitch(e) {
			try {
				const isOn = e.detail.value
				const action = isOn ? '开启' : '关闭'
				
				uni.showLoading({ title: `全局${action}中...` })
				
				// 调用API进行全局控制
				await globalLightingControl({
					isOn: isOn,
					brightness: isOn ? this.globalBrightness : 0
				})
				
				// 刷新数据
				await this.loadLightingData()
				
				uni.hideLoading()
				uni.showToast({
					title: `全局${action}成功`,
					icon: 'success'
				})
			} catch (error) {
				console.error('全局开关操作失败:', error)
				uni.hideLoading()
				uni.showToast({
					title: error.message || '操作失败',
					icon: 'error'
				})
			}
		},
		
		// 全局亮度调节
		async handleGlobalBrightness(e) {
			try {
				const brightness = e.detail.value
				
				// 防抖处理
				clearTimeout(this.globalBrightnessTimer)
				this.globalBrightnessTimer = setTimeout(async () => {
					await this.updateGlobalBrightness(brightness)
				}, 500)
			} catch (error) {
				console.error('全局亮度调节失败:', error)
			}
		},
		
		// 更新全局亮度
		async updateGlobalBrightness(brightness) {
			try {
				// 调用API更新全局亮度
				await updateGlobalBrightness({
					brightness: brightness
				})
				
				this.globalBrightness = brightness
				
				// 重新加载数据
				await this.loadLightingData()
			} catch (error) {
				console.error('更新全局亮度失败:', error)
				uni.showToast({
					title: error.message || '亮度调节失败',
					icon: 'error'
				})
			}
		},
		
		// 执行快捷操作
		async executeQuickAction(action) {
			try {
				uni.showLoading({ title: `${action.label}中...` })
				
				// 调用API执行快速操作
				await executeQuickAction({
					type: action.key
				})
				
				// 重新加载数据
				await this.loadLightingData()
				
				uni.hideLoading()
				uni.showToast({
					title: `${action.label}完成`,
					icon: 'success'
				})
			} catch (error) {
				console.error('快捷操作失败:', error)
				uni.hideLoading()
				uni.showToast({
					title: error.message || '操作失败',
					icon: 'error'
				})
			}
		},
		
		// 应用情景模式
		async applyScene(scene) {
			try {
				uni.showLoading({ title: `切换到${scene.name}...` })
				
				// 调用API应用情景模式
				await applyLightingScene({
					sceneId: scene.id
				})
				
				// 更新当前情景
				this.currentScene = scene.id
				
				// 重新加载数据
				await this.loadLightingData()
				
				uni.hideLoading()
				uni.showToast({
					title: `${scene.name}已应用`,
					icon: 'success'
				})
			} catch (error) {
				console.error('应用情景模式失败:', error)
				uni.hideLoading()
				uni.showToast({
					title: error.message || '应用失败',
					icon: 'error'
				})
			}
		},
		
		// 添加自定义情景
		addScene() {
			uni.navigateTo({
				url: '/pages/lighting/scene'
			})
		},
		
		// 设置设备筛选
		setDeviceFilter(filter) {
			this.currentFilter = filter
		},
		
		// 切换单个设备
		async toggleDevice(device) {
			try {
				const isOn = !device.isOn
				const action = isOn ? '开启' : '关闭'
				
				// 调用API切换设备状态
				await toggleLightingDevice({
					deviceId: device.id,
					isOn: isOn
				})
				
				// 重新加载数据
				await this.loadLightingData()
				
				uni.showToast({
					title: `${device.name}已${action}`,
					icon: 'success'
				})
			} catch (error) {
				console.error('切换设备状态失败:', error)
				uni.showToast({
					title: error.message || '操作失败',
					icon: 'error'
				})
			}
		},
		
		// 调节设备亮度
		async adjustDeviceBrightness(device, e) {
			try {
				const brightness = e.detail.value
				
				// 防抖处理
				clearTimeout(device.brightnessTimer)
				device.brightnessTimer = setTimeout(async () => {
					await this.updateDeviceBrightness(device, brightness)
				}, 500)
			} catch (error) {
				console.error('调节设备亮度失败:', error)
			}
		},
		
		// 更新设备亮度
		async updateDeviceBrightness(device, brightness) {
			try {
				// 调用API更新设备亮度
				await updateDeviceBrightness({
					deviceId: device.id,
					brightness: brightness
				})
				
				// 重新加载数据
				await this.loadLightingData()
			} catch (error) {
				console.error('更新设备亮度失败:', error)
				uni.showToast({
					title: error.message || '亮度调节失败',
					icon: 'error'
				})
			}
		},
		
		// 查看设备详情
		viewDeviceDetail(device) {
			uni.navigateTo({
				url: `/pages/device/detail?id=${device.id}`
			})
		},
		
		// 切换定时任务
		async toggleTimer(timer) {
			try {
				const enabled = !timer.enabled
				
				// 调用API切换定时任务状态
				await toggleLightingTimer({
					timerId: timer.id,
					enabled: enabled
				})
				
				// 更新本地状态
				timer.enabled = enabled
				
				const message = enabled ? '定时任务已启用' : '定时任务已禁用'
				uni.showToast({
					title: message,
					icon: 'success'
				})
			} catch (error) {
				console.error('切换定时任务失败:', error)
				uni.showToast({
					title: error.message || '操作失败',
					icon: 'error'
				})
			}
		},
		
		// 查看所有定时任务
		viewAllTimers() {
			uni.navigateTo({
				url: '/pages/lighting/timer'
			})
		},
		
		// 添加定时任务
		addTimer() {
			uni.navigateTo({
				url: '/pages/lighting/timer'
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.lighting-container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

// 统计区域
.stats-section {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 16rpx;
	margin-bottom: 20rpx;
}

.stats-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 24rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.stat-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
	font-size: 24rpx;
}

.stat-devices {
	background-color: #e6f7ff;
}

.stat-brightness {
	background-color: #fff7e6;
}

.stat-power {
	background-color: #f6ffed;
}

.stat-timers {
	background-color: #fff2f0;
}

.stat-content {
	flex: 1;
}

.stat-value {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 4rpx;
}

.stat-label {
	font-size: 24rpx;
	color: #999999;
}

// 快速控制卡片
.quick-control-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.card-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.control-mode {
	display: flex;
	background-color: #f0f0f0;
	border-radius: 8rpx;
	padding: 4rpx;
	
	text {
		padding: 8rpx 16rpx;
		border-radius: 6rpx;
		font-size: 24rpx;
		color: #666666;
		transition: all 0.3s;
	}
	
	.mode-active {
		background-color: #1890ff;
		color: #ffffff;
	}
}

.global-controls {
	margin-bottom: 24rpx;
}

.control-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.control-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
}

.control-label {
	font-size: 28rpx;
	color: #333333;
}

.brightness-value {
	font-size: 24rpx;
	color: #1890ff;
	font-weight: 500;
}

.brightness-slider-container {
	margin-bottom: 24rpx;
}

.brightness-icons {
	display: flex;
	align-items: center;
}

.brightness-icon {
	font-size: 32rpx;
	margin: 0 16rpx;
}

.global-brightness-slider {
	flex: 1;
}

.quick-actions {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 16rpx;
}

.quick-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx 12rpx;
	background-color: #f8f9fa;
	border-radius: 12rpx;
	transition: all 0.3s;
}

.quick-btn:active {
	background-color: #e9ecef;
	transform: scale(0.95);
}

.quick-icon {
	font-size: 32rpx;
	margin-bottom: 8rpx;
}

.quick-text {
	font-size: 20rpx;
	color: #666666;
}

// 情景模式卡片
.scene-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.add-scene-btn {
	padding: 8rpx 16rpx;
	background-color: #1890ff;
	color: #ffffff;
	border-radius: 8rpx;
	font-size: 24rpx;
}

.scene-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 16rpx;
}

.scene-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx;
	border-radius: 12rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s;
	position: relative;
}

.scene-item.active {
	border-color: #1890ff;
	background-color: #f0f8ff;
}

.scene-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	margin-bottom: 12rpx;
}

.scene-name {
	font-size: 24rpx;
	font-weight: 500;
	color: #333333;
	margin-bottom: 4rpx;
}

.scene-desc {
	font-size: 20rpx;
	color: #999999;
	text-align: center;
	margin-bottom: 8rpx;
}

.scene-status {
	position: absolute;
	top: 8rpx;
	right: 8rpx;
	background-color: #1890ff;
	color: #ffffff;
	font-size: 18rpx;
	padding: 2rpx 8rpx;
	border-radius: 4rpx;
}

// 设备列表卡片
.device-list-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.filter-tabs {
	display: flex;
	gap: 16rpx;
	
	text {
		padding: 8rpx 16rpx;
		background-color: #f0f0f0;
		color: #666666;
		border-radius: 8rpx;
		font-size: 24rpx;
		transition: all 0.3s;
	}
	
	.active {
		background-color: #1890ff;
		color: #ffffff;
	}
}

.device-list {
	margin-top: 24rpx;
}

.device-item {
	display: flex;
	align-items: center;
	padding: 24rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.device-item:last-child {
	border-bottom: none;
}

.device-info {
	display: flex;
	align-items: center;
	flex: 1;
}

.device-avatar {
	position: relative;
	margin-right: 16rpx;
}

.device-icon {
	font-size: 40rpx;
}

.device-status-dot {
	position: absolute;
	top: -4rpx;
	right: -4rpx;
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	border: 2rpx solid #ffffff;
}

.status-online {
	background-color: #52c41a;
}

.status-offline {
	background-color: #ff4d4f;
}

.device-details {
	flex: 1;
}

.device-name {
	font-size: 28rpx;
	font-weight: 500;
	color: #333333;
	margin-bottom: 4rpx;
}

.device-location {
	font-size: 24rpx;
	color: #999999;
	margin-bottom: 4rpx;
}

.device-power {
	font-size: 22rpx;
	color: #1890ff;
}

.device-controls {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 12rpx;
}

.control-switch {
	// switch组件样式由uni-app控制
}

.brightness-control {
	width: 200rpx;
}

.device-brightness-slider {
	width: 100%;
}

.device-actions {
	display: flex;
	gap: 8rpx;
}

.action-btn {
	padding: 6rpx 12rpx;
	background-color: #f0f0f0;
	color: #666666;
	border-radius: 6rpx;
	font-size: 22rpx;
	transition: all 0.3s;
}

.action-btn:active {
	background-color: #e0e0e0;
}

// 定时任务卡片
.timer-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.view-all-btn {
	padding: 8rpx 16rpx;
	background-color: #f0f0f0;
	color: #666666;
	border-radius: 8rpx;
	font-size: 24rpx;
}

.timer-list {
	margin-top: 24rpx;
}

.timer-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.timer-item:last-child {
	border-bottom: none;
}

.timer-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
	font-size: 24rpx;
}

.timer-morning {
	background-color: #fff7e6;
}

.timer-evening {
	background-color: #f6ffed;
}

.timer-energy {
	background-color: #e6f7ff;
}

.timer-info {
	flex: 1;
}

.timer-name {
	font-size: 28rpx;
	font-weight: 500;
	color: #333333;
	margin-bottom: 4rpx;
}

.timer-time {
	font-size: 24rpx;
	color: #666666;
	margin-bottom: 4rpx;
}

.timer-devices {
	font-size: 22rpx;
	color: #999999;
}

.timer-status {
	// switch组件样式由uni-app控制
}

// 空状态
.empty {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx 0;
}

.empty-icon {
	font-size: 80rpx;
	margin-bottom: 16rpx;
	opacity: 0.5;
}

.empty-text {
	font-size: 28rpx;
	color: #999999;
	margin-bottom: 24rpx;
}

.empty-action {
	padding: 12rpx 24rpx;
	background-color: #1890ff;
	color: #ffffff;
	border-radius: 8rpx;
	font-size: 24rpx;
}

// 响应式设计
@media screen and (min-width: 768px) {
	.stats-section {
		grid-template-columns: repeat(4, 1fr);
	}
	
	.scene-grid {
		grid-template-columns: repeat(6, 1fr);
	}
	
	.quick-actions {
		grid-template-columns: repeat(8, 1fr);
	}
}
</style>#333333;