// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core.Service;

/// <summary>
/// 场景统计查询输入参数
/// </summary>
public class EnergySceneStatInput : BasePageInput
{
    /// <summary>
    /// 统计类型（1-按天，2-按周，3-按月，4-按年）
    /// </summary>
    public int StatType { get; set; } = 1;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 场景ID列表
    /// </summary>
    public List<long>? SceneIds { get; set; }

    /// <summary>
    /// 场景状态
    /// </summary>
    public int? Status { get; set; }

    /// <summary>
    /// 统计维度（1-执行次数，2-成功率，3-平均时长，4-设备数量）
    /// </summary>
    public int StatDimension { get; set; } = 1;

    /// <summary>
    /// 是否包含详细数据
    /// </summary>
    public bool IncludeDetail { get; set; } = false;

    /// <summary>
    /// 排序字段
    /// </summary>
    public string? OrderBy { get; set; }

    /// <summary>
    /// 排序方式（asc/desc）
    /// </summary>
    public string OrderDirection { get; set; } = "desc";
}