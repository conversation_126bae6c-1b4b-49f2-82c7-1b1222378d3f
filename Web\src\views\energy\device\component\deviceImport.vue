<template>
	<div class="device-import-container">
		<el-dialog
			v-model="visible"
			title="批量导入设备"
			width="800px"
			:before-close="handleClose"
			destroy-on-close
		>
			<div class="import-steps">
				<el-steps :active="currentStep" finish-status="success" align-center>
					<el-step title="下载模板" description="下载导入模板文件" />
					<el-step title="上传文件" description="上传填写好的Excel文件" />
					<el-step title="数据预览" description="预览导入数据" />
					<el-step title="导入完成" description="完成设备导入" />
				</el-steps>
			</div>
			
			<div class="step-content">
				<!-- 步骤1：下载模板 -->
				<div v-if="currentStep === 0" class="step-download">
					<div class="download-info">
						<el-alert
							title="导入说明"
							type="info"
							:closable="false"
							show-icon
						>
							<template #default>
								<div class="import-rules">
									<p><strong>导入规则：</strong></p>
									<ul>
										<li>请先下载导入模板，按照模板格式填写设备信息</li>
										<li>设备名称和设备编号为必填项，不能为空</li>
										<li>设备编号不能重复，系统会自动检查</li>
										<li>设备类型请从下拉列表中选择：LED灯、荧光灯、节能灯、智能灯</li>
										<li>功率请填写数字，单位为瓦特(W)</li>
										<li>亮度范围为0-100，默认为80</li>
										<li>设备状态：正常、故障、维护、停用</li>
										<li>开关状态：开启、关闭</li>
										<li>安装日期格式：YYYY-MM-DD</li>
										<li>IP地址格式：xxx.xxx.xxx.xxx</li>
									</ul>
								</div>
							</template>
						</el-alert>
					</div>
					
					<div class="download-action">
						<el-button type="primary" size="large" @click="downloadTemplate">
							<el-icon><ele-Download /></el-icon>
							下载导入模板
						</el-button>
					</div>
				</div>
				
				<!-- 步骤2：上传文件 -->
				<div v-if="currentStep === 1" class="step-upload">
					<el-upload
						ref="uploadRef"
						class="upload-demo"
						drag
						:action="uploadUrl"
						:headers="uploadHeaders"
						:before-upload="beforeUpload"
						:on-success="handleUploadSuccess"
						:on-error="handleUploadError"
						:file-list="fileList"
						:limit="1"
						:auto-upload="false"
						accept=".xlsx,.xls"
					>
						<el-icon class="el-icon--upload"><ele-UploadFilled /></el-icon>
						<div class="el-upload__text">
							将Excel文件拖到此处，或<em>点击上传</em>
						</div>
						<template #tip>
							<div class="el-upload__tip">
								只能上传 .xlsx/.xls 文件，且不超过 10MB
							</div>
						</template>
					</el-upload>
					
					<div class="upload-actions" v-if="fileList.length > 0">
						<el-button type="primary" @click="parseFile" :loading="parsing">
							<el-icon><ele-View /></el-icon>
							解析文件
						</el-button>
					</div>
				</div>
				
				<!-- 步骤3：数据预览 -->
				<div v-if="currentStep === 2" class="step-preview">
					<div class="preview-summary">
						<el-row :gutter="20">
							<el-col :span="6">
								<el-statistic title="总数据量" :value="previewData.length" suffix="条" />
							</el-col>
							<el-col :span="6">
								<el-statistic title="有效数据" :value="validData.length" suffix="条" />
							</el-col>
							<el-col :span="6">
								<el-statistic title="错误数据" :value="errorData.length" suffix="条" />
							</el-col>
							<el-col :span="6">
								<el-statistic title="重复数据" :value="duplicateData.length" suffix="条" />
							</el-col>
						</el-row>
					</div>
					
					<div class="preview-tabs">
						<el-tabs v-model="activeTab" type="card">
							<el-tab-pane label="有效数据" name="valid">
								<el-table
									:data="validData"
									border
									stripe
									max-height="400"
									style="width: 100%"
								>
									<el-table-column prop="deviceName" label="设备名称" min-width="120" show-overflow-tooltip />
									<el-table-column prop="deviceCode" label="设备编号" min-width="120" show-overflow-tooltip />
									<el-table-column prop="deviceType" label="设备类型" width="100" align="center" />
									<el-table-column prop="location" label="安装位置" min-width="120" show-overflow-tooltip />
									<el-table-column prop="power" label="功率(W)" width="100" align="center" />
									<el-table-column prop="brightness" label="亮度" width="80" align="center" />
									<el-table-column prop="status" label="状态" width="80" align="center" />
									<el-table-column prop="isOn" label="开关" width="80" align="center" />
								</el-table>
							</el-tab-pane>
							
							<el-tab-pane :label="`错误数据(${errorData.length})`" name="error" v-if="errorData.length > 0">
								<el-table
									:data="errorData"
									border
									stripe
									max-height="400"
									style="width: 100%"
								>
									<el-table-column prop="rowIndex" label="行号" width="80" align="center" />
									<el-table-column prop="deviceName" label="设备名称" min-width="120" show-overflow-tooltip />
									<el-table-column prop="deviceCode" label="设备编号" min-width="120" show-overflow-tooltip />
									<el-table-column prop="errorMessage" label="错误信息" min-width="200" show-overflow-tooltip>
										<template #default="{ row }">
											<el-text type="danger">{{ row.errorMessage }}</el-text>
										</template>
									</el-table-column>
								</el-table>
							</el-tab-pane>
							
							<el-tab-pane :label="`重复数据(${duplicateData.length})`" name="duplicate" v-if="duplicateData.length > 0">
								<el-table
									:data="duplicateData"
									border
									stripe
									max-height="400"
									style="width: 100%"
								>
									<el-table-column prop="rowIndex" label="行号" width="80" align="center" />
									<el-table-column prop="deviceName" label="设备名称" min-width="120" show-overflow-tooltip />
									<el-table-column prop="deviceCode" label="设备编号" min-width="120" show-overflow-tooltip />
									<el-table-column prop="duplicateReason" label="重复原因" min-width="200" show-overflow-tooltip>
										<template #default="{ row }">
											<el-text type="warning">{{ row.duplicateReason }}</el-text>
										</template>
									</el-table-column>
								</el-table>
							</el-tab-pane>
						</el-tabs>
					</div>
				</div>
				
				<!-- 步骤4：导入完成 -->
				<div v-if="currentStep === 3" class="step-complete">
					<div class="complete-result">
						<el-result
							:icon="importResult.success ? 'success' : 'warning'"
							:title="importResult.title"
							:sub-title="importResult.message"
						>
							<template #extra>
								<div class="result-stats">
									<el-row :gutter="20">
										<el-col :span="8">
											<el-statistic title="成功导入" :value="importResult.successCount" suffix="条" />
										</el-col>
										<el-col :span="8">
											<el-statistic title="失败数量" :value="importResult.failCount" suffix="条" />
										</el-col>
										<el-col :span="8">
											<el-statistic title="总耗时" :value="importResult.duration" suffix="秒" />
										</el-col>
									</el-row>
								</div>
							</template>
						</el-result>
					</div>
				</div>
			</div>
			
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="handleClose">取消</el-button>
					<el-button v-if="currentStep === 0" type="primary" @click="nextStep" :disabled="!templateDownloaded">
						下一步
					</el-button>
					<el-button v-if="currentStep === 1" @click="prevStep">上一步</el-button>
					<el-button v-if="currentStep === 1" type="primary" @click="nextStep" :disabled="fileList.length === 0 || !fileParsed">
						下一步
					</el-button>
					<el-button v-if="currentStep === 2" @click="prevStep">上一步</el-button>
					<el-button v-if="currentStep === 2" type="primary" @click="importDevices" :loading="importing" :disabled="validData.length === 0">
						开始导入 ({{ validData.length }}条)
					</el-button>
					<el-button v-if="currentStep === 3" type="primary" @click="handleClose">
						完成
					</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getAPI } from '/@/utils/axios-utils';
import { useDeviceApi } from '/@/api-services/device';
import * as XLSX from 'xlsx';

// 组件属性
interface Props {
	title?: string;
}

const props = withDefaults(defineProps<Props>(), {
	title: '批量导入设备'
});

// 组件事件
const emit = defineEmits(['refresh', 'success']);

// 响应式数据
const visible = ref(false);
const currentStep = ref(0);
const templateDownloaded = ref(false);
const fileParsed = ref(false);
const parsing = ref(false);
const importing = ref(false);

// 上传相关
const uploadRef = ref();
const fileList = ref<any[]>([]);
const uploadUrl = ref('/api/upload'); // 实际的上传地址
const uploadHeaders = ref({
	'Authorization': `Bearer ${localStorage.getItem('token')}`
});

// 数据预览
const previewData = ref<any[]>([]);
const activeTab = ref('valid');

// 导入结果
const importResult = reactive({
	success: true,
	title: '导入完成',
	message: '设备数据导入成功',
	successCount: 0,
	failCount: 0,
	duration: 0
});

// 计算属性
const validData = computed(() => {
	return previewData.value.filter(item => !item.hasError && !item.isDuplicate);
});

const errorData = computed(() => {
	return previewData.value.filter(item => item.hasError);
});

const duplicateData = computed(() => {
	return previewData.value.filter(item => item.isDuplicate);
});

// 设备类型映射
const deviceTypeMap = {
	'LED灯': 'LED',
	'荧光灯': 'Fluorescent',
	'节能灯': 'EnergySaving',
	'智能灯': 'Smart'
};

// 状态映射
const statusMap = {
	'正常': 1,
	'故障': 2,
	'维护': 3,
	'停用': 4
};

// 开关状态映射
const switchMap = {
	'开启': true,
	'关闭': false
};

// 打开对话框
const openDialog = () => {
	visible.value = true;
	resetData();
};

// 关闭对话框
const handleClose = () => {
	visible.value = false;
	resetData();
};

// 重置数据
const resetData = () => {
	currentStep.value = 0;
	templateDownloaded.value = false;
	fileParsed.value = false;
	fileList.value = [];
	previewData.value = [];
	activeTab.value = 'valid';
	Object.assign(importResult, {
		success: true,
		title: '导入完成',
		message: '设备数据导入成功',
		successCount: 0,
		failCount: 0,
		duration: 0
	});
};

// 下载模板
const downloadTemplate = () => {
	// 创建模板数据
	const templateData = [
		{
			'设备名称': 'LED-001',
			'设备编号': 'LED001',
			'设备类型': 'LED灯',
			'安装位置': '办公室A-001',
			'功率(W)': 20,
			'亮度': 80,
			'设备状态': '正常',
			'开关状态': '开启',
			'安装日期': '2024-01-01',
			'IP地址': '*************',
			'备注': '示例设备'
		},
		{
			'设备名称': 'LED-002',
			'设备编号': 'LED002',
			'设备类型': '节能灯',
			'安装位置': '办公室A-002',
			'功率(W)': 15,
			'亮度': 60,
			'设备状态': '正常',
			'开关状态': '关闭',
			'安装日期': '2024-01-02',
			'IP地址': '*************',
			'备注': '示例设备2'
		}
	];
	
	// 创建工作簿
	const wb = XLSX.utils.book_new();
	const ws = XLSX.utils.json_to_sheet(templateData);
	
	// 设置列宽
	const colWidths = [
		{ wch: 15 }, // 设备名称
		{ wch: 15 }, // 设备编号
		{ wch: 12 }, // 设备类型
		{ wch: 20 }, // 安装位置
		{ wch: 10 }, // 功率
		{ wch: 8 },  // 亮度
		{ wch: 10 }, // 设备状态
		{ wch: 10 }, // 开关状态
		{ wch: 12 }, // 安装日期
		{ wch: 15 }, // IP地址
		{ wch: 20 }  // 备注
	];
	ws['!cols'] = colWidths;
	
	// 添加工作表
	XLSX.utils.book_append_sheet(wb, ws, '设备导入模板');
	
	// 下载文件
	XLSX.writeFile(wb, '设备导入模板.xlsx');
	
	templateDownloaded.value = true;
	ElMessage.success('模板下载成功');
};

// 上传前检查
const beforeUpload = (file: File) => {
	const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
					 file.type === 'application/vnd.ms-excel';
	const isLt10M = file.size / 1024 / 1024 < 10;
	
	if (!isExcel) {
		ElMessage.error('只能上传Excel文件!');
		return false;
	}
	if (!isLt10M) {
		ElMessage.error('文件大小不能超过10MB!');
		return false;
	}
	return true;
};

// 上传成功
const handleUploadSuccess = (response: any, file: any) => {
	ElMessage.success('文件上传成功');
};

// 上传失败
const handleUploadError = (error: any) => {
	ElMessage.error('文件上传失败');
	console.error('Upload error:', error);
};

// 解析文件
const parseFile = async () => {
	if (fileList.value.length === 0) {
		ElMessage.warning('请先选择文件');
		return;
	}
	
	parsing.value = true;
	try {
		const file = fileList.value[0].raw;
		const data = await readExcelFile(file);
		previewData.value = validateData(data);
		fileParsed.value = true;
		ElMessage.success(`文件解析完成，共${previewData.value.length}条数据`);
	} catch (error) {
		console.error('Parse file error:', error);
		ElMessage.error('文件解析失败');
	} finally {
		parsing.value = false;
	}
};

// 读取Excel文件
const readExcelFile = (file: File): Promise<any[]> => {
	return new Promise((resolve, reject) => {
		const reader = new FileReader();
		reader.onload = (e) => {
			try {
				const data = new Uint8Array(e.target?.result as ArrayBuffer);
				const workbook = XLSX.read(data, { type: 'array' });
				const sheetName = workbook.SheetNames[0];
				const worksheet = workbook.Sheets[sheetName];
				const jsonData = XLSX.utils.sheet_to_json(worksheet);
				resolve(jsonData);
			} catch (error) {
				reject(error);
			}
		};
		reader.onerror = reject;
		reader.readAsArrayBuffer(file);
	});
};

// 验证数据
const validateData = (data: any[]): any[] => {
	const result: any[] = [];
	const existingCodes = new Set<string>();
	
	data.forEach((row, index) => {
		const item: any = {
			rowIndex: index + 2, // Excel行号从2开始（第1行是标题）
			deviceName: row['设备名称'],
			deviceCode: row['设备编号'],
			deviceType: deviceTypeMap[row['设备类型'] as keyof typeof deviceTypeMap] || row['设备类型'],
			location: row['安装位置'],
			power: row['功率(W)'],
			brightness: row['亮度'] || 80,
			status: statusMap[row['设备状态'] as keyof typeof statusMap] || 1,
			isOn: switchMap[row['开关状态'] as keyof typeof switchMap] ?? true,
			installDate: row['安装日期'],
			ipAddress: row['IP地址'],
			remark: row['备注'],
			hasError: false,
			isDuplicate: false,
			errorMessage: '',
			duplicateReason: ''
		};
		
		// 验证必填字段
		const errors: string[] = [];
		if (!item.deviceName) errors.push('设备名称不能为空');
		if (!item.deviceCode) errors.push('设备编号不能为空');
		if (!item.deviceType) errors.push('设备类型无效');
		if (!item.location) errors.push('安装位置不能为空');
		if (!item.power || isNaN(item.power)) errors.push('功率必须为数字');
		
		// 验证数据格式
		if (item.brightness && (item.brightness < 0 || item.brightness > 100)) {
			errors.push('亮度范围为0-100');
		}
		
		if (item.ipAddress && !/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/.test(item.ipAddress)) {
			errors.push('IP地址格式不正确');
		}
		
		if (item.installDate && !/^\d{4}-\d{2}-\d{2}$/.test(item.installDate)) {
			errors.push('安装日期格式应为YYYY-MM-DD');
		}
		
		// 检查重复
		if (item.deviceCode && existingCodes.has(item.deviceCode)) {
			item.isDuplicate = true;
			item.duplicateReason = '设备编号重复';
		} else if (item.deviceCode) {
			existingCodes.add(item.deviceCode);
		}
		
		if (errors.length > 0) {
			item.hasError = true;
			item.errorMessage = errors.join('; ');
		}
		
		result.push(item);
	});
	
	return result;
};

// 导入设备
const importDevices = async () => {
	if (validData.value.length === 0) {
		ElMessage.warning('没有有效数据可导入');
		return;
	}
	
	importing.value = true;
	const startTime = Date.now();
	
	try {
		// 准备导入数据
		const importData = validData.value.map(item => ({
			deviceName: item.deviceName,
			deviceCode: item.deviceCode,
			deviceType: item.deviceType,
			location: item.location,
			power: item.power,
			brightness: item.brightness,
			status: item.status,
			isOn: item.isOn,
			installDate: item.installDate,
			ipAddress: item.ipAddress,
			remark: item.remark
		}));
		
		// 这里应该调用实际的API
		// 使用新的模块化API批量导入设备
		
		// 模拟导入过程
		await new Promise(resolve => setTimeout(resolve, 2000));
		
		const endTime = Date.now();
		const duration = Math.round((endTime - startTime) / 1000);
		
		// 更新导入结果
		Object.assign(importResult, {
			success: true,
			title: '导入完成',
			message: `成功导入${validData.value.length}台设备`,
			successCount: validData.value.length,
			failCount: errorData.value.length + duplicateData.value.length,
			duration
		});
		
		currentStep.value = 3;
		emit('success', validData.value.length);
		ElMessage.success('设备导入成功');
	} catch (error) {
		console.error('Import devices error:', error);
		ElMessage.error('设备导入失败');
		
		Object.assign(importResult, {
			success: false,
			title: '导入失败',
			message: '设备导入过程中发生错误',
			successCount: 0,
			failCount: validData.value.length,
			duration: Math.round((Date.now() - startTime) / 1000)
		});
		
		currentStep.value = 3;
	} finally {
		importing.value = false;
	}
};

// 下一步
const nextStep = () => {
	if (currentStep.value < 3) {
		currentStep.value++;
	}
};

// 上一步
const prevStep = () => {
	if (currentStep.value > 0) {
		currentStep.value--;
	}
};

// 暴露方法
defineExpose({
	openDialog
});
</script>

<style scoped lang="scss">
.device-import-container {
	:deep(.el-dialog__body) {
		padding: 20px;
	}
}

.import-steps {
	margin-bottom: 30px;
}

.step-content {
	min-height: 400px;
}

.step-download {
	.download-info {
		margin-bottom: 30px;
		
		.import-rules {
			ul {
				margin: 10px 0;
				padding-left: 20px;
				
				li {
					margin-bottom: 8px;
					line-height: 1.5;
				}
			}
		}
	}
	
	.download-action {
		text-align: center;
		padding: 40px 0;
	}
}

.step-upload {
	.upload-demo {
		margin-bottom: 20px;
	}
	
	.upload-actions {
		text-align: center;
		padding: 20px 0;
	}
}

.step-preview {
	.preview-summary {
		margin-bottom: 20px;
		padding: 20px;
		background-color: #f8f9fa;
		border-radius: 6px;
	}
	
	.preview-tabs {
		:deep(.el-tabs__content) {
			padding: 20px 0;
		}
	}
}

.step-complete {
	.complete-result {
		text-align: center;
		padding: 40px 0;
		
		.result-stats {
			margin-top: 30px;
			padding: 20px;
			background-color: #f8f9fa;
			border-radius: 6px;
		}
	}
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 8px;
}

@media (max-width: 768px) {
	.step-content {
		min-height: 300px;
	}
	
	.preview-summary {
		:deep(.el-col) {
			margin-bottom: 10px;
		}
	}
	
	.result-stats {
		:deep(.el-col) {
			margin-bottom: 10px;
		}
	}
}
</style>