<template>
  <view 
    class="status-badge"
    :class="[
      `status-${status}`,
      `type-${type}`,
      `size-${size}`,
      {
        'with-icon': showIcon,
        'clickable': clickable,
        'animated': animated,
        'outlined': outlined,
        'rounded': rounded
      },
      customClass
    ]"
    @click="handleClick"
  >
    <view v-if="showIcon" class="badge-icon">
      <text class="icon">{{ iconText }}</text>
    </view>
    <text class="badge-text">{{ text }}</text>
    <view v-if="showDot" class="badge-dot"></view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

// Props定义
const props = defineProps({
  // 状态类型
  status: {
    type: String,
    default: 'default',
    validator: (value) => {
      return ['default', 'success', 'warning', 'error', 'info', 'online', 'offline', 'fault'].includes(value)
    }
  },
  // 显示文本
  text: {
    type: String,
    default: ''
  },
  // 徽章类型
  type: {
    type: String,
    default: 'filled',
    validator: (value) => {
      return ['filled', 'outlined', 'text'].includes(value)
    }
  },
  // 尺寸大小
  size: {
    type: String,
    default: 'medium',
    validator: (value) => {
      return ['small', 'medium', 'large'].includes(value)
    }
  },
  // 是否显示图标
  showIcon: {
    type: Boolean,
    default: false
  },
  // 是否显示圆点
  showDot: {
    type: Boolean,
    default: false
  },
  // 是否可点击
  clickable: {
    type: Boolean,
    default: false
  },
  // 是否有动画
  animated: {
    type: Boolean,
    default: false
  },
  // 是否为轮廓样式
  outlined: {
    type: Boolean,
    default: false
  },
  // 是否为圆角样式
  rounded: {
    type: Boolean,
    default: false
  },
  // 自定义类名
  customClass: {
    type: String,
    default: ''
  }
})

// Emits定义
const emit = defineEmits(['click'])

// 计算属性
const iconText = computed(() => {
  const iconMap = {
    success: '✓',
    warning: '⚠',
    error: '✗',
    info: 'ℹ',
    online: '●',
    offline: '○',
    fault: '!'
  }
  return iconMap[props.status] || '●'
})

// 方法
const handleClick = () => {
  if (props.clickable) {
    emit('click')
  }
}
</script>

<style lang="scss" scoped>
.status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s ease;
  
  // 尺寸样式
  &.size-small {
    padding: 2px 6px;
    font-size: 12px;
    line-height: 16px;
    
    .badge-icon {
      margin-right: 2px;
      .icon {
        font-size: 10px;
      }
    }
  }
  
  &.size-medium {
    padding: 4px 8px;
    font-size: 14px;
    line-height: 20px;
    
    .badge-icon {
      margin-right: 4px;
      .icon {
        font-size: 12px;
      }
    }
  }
  
  &.size-large {
    padding: 6px 12px;
    font-size: 16px;
    line-height: 24px;
    
    .badge-icon {
      margin-right: 6px;
      .icon {
        font-size: 14px;
      }
    }
  }
  
  // 状态颜色
  &.status-default {
    background-color: #f5f5f5;
    color: #666;
    border: 1px solid #d9d9d9;
  }
  
  &.status-success {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
  }
  
  &.status-warning {
    background-color: #fffbe6;
    color: #faad14;
    border: 1px solid #ffe58f;
  }
  
  &.status-error {
    background-color: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
  }
  
  &.status-info {
    background-color: #f0f9ff;
    color: #1890ff;
    border: 1px solid #91d5ff;
  }
  
  &.status-online {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
  }
  
  &.status-offline {
    background-color: #f5f5f5;
    color: #8c8c8c;
    border: 1px solid #d9d9d9;
  }
  
  &.status-fault {
    background-color: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
  }
  
  // 类型样式
  &.type-outlined {
    background-color: transparent;
  }
  
  &.type-text {
    background-color: transparent;
    border: none;
    padding-left: 0;
    padding-right: 0;
  }
  
  // 交互样式
  &.clickable {
    cursor: pointer;
    
    &:hover {
      opacity: 0.8;
      transform: translateY(-1px);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
  
  // 动画样式
  &.animated {
    animation: pulse 2s infinite;
  }
  
  // 圆角样式
  &.rounded {
    border-radius: 12px;
  }
  
  // 图标样式
  .badge-icon {
    display: flex;
    align-items: center;
    
    .icon {
      font-weight: bold;
    }
  }
  
  // 文本样式
  .badge-text {
    white-space: nowrap;
  }
  
  // 圆点样式
  .badge-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: currentColor;
    margin-left: 4px;
  }
}

// 动画定义
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}
</style>