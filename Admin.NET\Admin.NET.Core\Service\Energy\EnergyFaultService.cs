// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System.Linq;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.Drawing;

namespace Admin.NET.Core.Service;

/// <summary>
/// 设备故障服务 🧩
/// </summary>
[ApiDescriptionSettings(Order = 503)]
public class EnergyFaultService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<EnergyFault> _energyFaultRep;
    private readonly SqlSugarRepository<EnergyDevice> _energyDeviceRep;
    private readonly SysCacheService _sysCacheService;
    private readonly UserManager _userManager;

    public EnergyFaultService(
        SqlSugarRepository<EnergyFault> energyFaultRep,
        SqlSugarRepository<EnergyDevice> energyDeviceRep,
        SysCacheService sysCacheService,
        UserManager userManager)
    {
        _energyFaultRep = energyFaultRep;
        _energyDeviceRep = energyDeviceRep;
        _sysCacheService = sysCacheService;
        _userManager = userManager;
    }

    /// <summary>
    /// 获取故障记录分页列表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取故障记录分页列表")]
    public async Task<SqlSugarPagedList<EnergyFaultOutput>> GetPage([FromQuery] EnergyFaultInput input)
    {
        var query = _energyFaultRep.AsQueryable()
            .LeftJoin<EnergyDevice>((f, d) => f.DeviceId == d.Id)
            .WhereIF(input.DeviceId > 0, (f, d) => f.DeviceId == input.DeviceId)
            // .WhereIF(!string.IsNullOrWhiteSpace(input.DeviceCode), (f, d) => d.DeviceCode.Contains(input.DeviceCode)) // EnergyFaultInput中不存在DeviceCode字段
            .WhereIF(!string.IsNullOrWhiteSpace(input.FaultType), (f, d) => f.FaultType.ToString().Contains(input.FaultType))
            .WhereIF(input.FaultLevel.HasValue, (f, d) => f.FaultLevel == input.FaultLevel)
            .WhereIF(input.FaultStatus.HasValue, (f, d) => f.Status == input.FaultStatus)
            .WhereIF(input.StartTime.HasValue, (f, d) => f.FaultTime >= input.StartTime)
            .WhereIF(input.EndTime.HasValue, (f, d) => f.FaultTime <= input.EndTime)
            .Select<EnergyFaultOutput>((f, d) => new EnergyFaultOutput
            {
                Id = f.Id,
                DeviceId = f.DeviceId,
                DeviceCode = d.DeviceCode,
                DeviceName = d.DeviceName,
                FaultCode = f.FaultCode,
                FaultType = f.FaultType.ToString(),
                FaultLevel = f.FaultLevel,
                // FaultLevelName = SqlFunc.IIF(f.FaultLevel == 1, "低", SqlFunc.IIF(f.FaultLevel == 2, "中", SqlFunc.IIF(f.FaultLevel == 3, "高", "紧急"))), // EnergyFaultOutput中不存在此字段
                FaultDescription = f.FaultDescription,
                FaultTime = f.FaultTime,
                RepairTime = f.RepairTime,
                RepairDescription = f.RepairDescription,
                // RepairUserId = f.RepairUserId, // EnergyFaultOutput中不存在此字段
                FaultStatus = f.Status, // 使用FaultStatus字段
                // StatusName = SqlFunc.IIF(f.Status == 0, "待处理", SqlFunc.IIF(f.Status == 1, "处理中", SqlFunc.IIF(f.Status == 2, "已修复", "已忽略"))), // EnergyFaultOutput中不存在此字段
                // Duration = SqlFunc.DateDiff(DateType.Minute, f.FaultTime, SqlFunc.IsNull(f.RepairTime, DateTime.Now)), // EnergyFaultOutput中不存在此字段
                CreateTime = f.CreateTime
            })
            .OrderBy(f => f.FaultTime, OrderByType.Desc);

        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取故障详情 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取故障详情")]
    public async Task<EnergyFaultDetailOutput> GetDetail([FromQuery] QueryByIdEnergyFaultInput input)
    {
        var fault = await _energyFaultRep.AsQueryable()
            .LeftJoin<EnergyDevice>((f, d) => f.DeviceId == d.Id)
            .Where((f, d) => f.Id == input.Id)
            .Select((f, d) => new EnergyFaultDetailOutput
            {
                Id = f.Id,
                DeviceId = f.DeviceId,
                DeviceCode = d.DeviceCode,
                DeviceName = d.DeviceName,
                FaultCode = f.FaultCode,
                FaultType = f.FaultType.ToString(),
                FaultLevel = f.FaultLevel,
                FaultDescription = f.FaultDescription,
                FaultTime = f.FaultTime,
                RepairTime = f.RepairTime,
                RepairDescription = f.RepairDescription,
                FaultStatus = f.Status,
                StatusName = SqlFunc.IIF(f.Status == 0, "待处理", SqlFunc.IIF(f.Status == 1, "处理中", SqlFunc.IIF(f.Status == 2, "已修复", "已忽略"))),
                Duration = SqlFunc.DateDiff(DateType.Minute, f.FaultTime, f.RepairTime ?? DateTime.Now) / 60.0m,
                CreateTime = f.CreateTime,
                UpdateTime = f.UpdateTime,
                TenantId = f.TenantId
            })
            .FirstAsync();

        if (fault == null)
            throw Oops.Oh("故障记录不存在");

        // 获取同类型故障历史记录
        fault.HistoryFaults = await _energyFaultRep.AsQueryable()
            .LeftJoin<EnergyDevice>((f, d) => f.DeviceId == d.Id)
            .Where((f, d) => f.DeviceId == fault.DeviceId && f.FaultType.ToString() == fault.FaultType && f.Id != fault.Id)
            .Select((f, d) => new EnergyFaultOutput
            {
                Id = f.Id,
                FaultCode = f.FaultCode,
                FaultDescription = f.FaultDescription,
                FaultTime = f.FaultTime,
                RepairTime = f.RepairTime,
                FaultStatus = f.Status
            })
            .OrderBy(f => f.FaultTime, OrderByType.Desc)
            .Take(10)
            .ToListAsync();

        return fault;
    }

    /// <summary>
    /// 增加故障记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [DisplayName("增加故障记录")]
    public async Task<long> AddFault(AddEnergyFaultInput input)
    {
        // 检查设备是否存在
        var deviceExist = await _energyDeviceRep.IsAnyAsync(u => u.Id == input.DeviceId);
        if (!deviceExist)
            throw Oops.Oh("设备不存在");

        // 检查故障编码是否重复
        if (!string.IsNullOrWhiteSpace(input.FaultCode))
        {
            var codeExist = await _energyFaultRep.IsAnyAsync(u => u.FaultCode == input.FaultCode);
            if (codeExist)
                throw Oops.Oh("故障编码已存在");
        }

        var fault = input.Adapt<EnergyFault>();
        fault.TenantId = _userManager.TenantId;
        fault.CreateUserId = _userManager.UserId;
        fault.CreateTime = DateTime.Now;
        fault.Status = 0; // 默认待处理状态

        // 如果未指定故障时间，使用当前时间
        if (fault.FaultTime == default)
            fault.FaultTime = DateTime.Now;

        var newFault = await _energyFaultRep.AsInsertable(fault).ExecuteReturnEntityAsync();

        // TODO: 发送故障告警通知
        // await SendFaultAlertAsync(newFault);

        return newFault.Id;
    }

    /// <summary>
    /// 更新故障记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [DisplayName("更新故障记录")]
    public async Task UpdateFault(UpdateEnergyFaultInput input)
    {
        // 检查记录是否存在
        var fault = await _energyFaultRep.GetByIdAsync(input.Id);
        if (fault == null)
            throw Oops.Oh("故障记录不存在");

        // 检查设备是否存在
        var deviceExist = await _energyDeviceRep.IsAnyAsync(u => u.Id == input.DeviceId);
        if (!deviceExist)
            throw Oops.Oh("设备不存在");

        // 检查故障编码是否重复（排除自己）
        if (!string.IsNullOrWhiteSpace(input.FaultCode))
        {
            var codeExist = await _energyFaultRep.IsAnyAsync(u => u.FaultCode == input.FaultCode && u.Id != input.Id);
            if (codeExist)
                throw Oops.Oh("故障编码已存在");
        }

        var updateFault = input.Adapt<EnergyFault>();
        updateFault.UpdateUserId = _userManager.UserId;
        updateFault.UpdateTime = DateTime.Now;

        await _energyFaultRep.AsUpdateable(updateFault).ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除故障记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    [DisplayName("删除故障记录")]
    public async Task DeleteFault(DeleteEnergyFaultInput input)
    {
        var fault = await _energyFaultRep.GetByIdAsync(input.Id);
        if (fault == null)
            throw Oops.Oh("故障记录不存在");

        await _energyFaultRep.DeleteAsync(fault);
    }

    /// <summary>
    /// 设置故障状态 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "SetStatus"), HttpPost]
    [DisplayName("设置故障状态")]
    public async Task SetStatus(EnergyFaultStatusInput input)
    {
        var fault = await _energyFaultRep.GetByIdAsync(input.Id);
        if (fault == null)
            throw Oops.Oh("故障记录不存在");

        fault.Status = input.FaultStatus;
        fault.UpdateUserId = _userManager.UserId;
        fault.UpdateTime = DateTime.Now;

        // 如果状态为已修复，记录修复时间和修复人员
        if (input.FaultStatus == 2)
        {
            fault.RepairTime = DateTime.Now;
            fault.RepairUserId = _userManager.UserId;
        }

        await _energyFaultRep.AsUpdateable(fault)
            .UpdateColumns(u => new { u.Status, u.RepairTime, u.RepairUserId, u.UpdateUserId, u.UpdateTime })
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 故障维修 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Repair"), HttpPost]
    [DisplayName("故障维修")]
    public async Task RepairFault(RepairFaultInput input)
    {
        var fault = await _energyFaultRep.GetByIdAsync(input.Id);
        if (fault == null)
            throw Oops.Oh("故障记录不存在");

        if (fault.Status == 2)
            throw Oops.Oh("故障已修复，无需重复操作");

        fault.Status = 2; // 已修复
        fault.RepairTime = DateTime.Now;
        fault.RepairDescription = input.RepairDescription;
        fault.RepairUserId = _userManager.UserId;
        fault.UpdateUserId = _userManager.UserId;
        fault.UpdateTime = DateTime.Now;

        await _energyFaultRep.AsUpdateable(fault)
            .UpdateColumns(u => new { u.Status, u.RepairTime, u.RepairDescription, u.RepairUserId, u.UpdateUserId, u.UpdateTime })
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取故障统计数据 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取故障统计数据")]
    public async Task<EnergyFaultStatOutput> GetStat([FromQuery] EnergyFaultStatInput input)
    {
        var query = _energyFaultRep.AsQueryable()
            .LeftJoin<EnergyDevice>((f, d) => f.DeviceId == d.Id)
            .WhereIF(input.DeviceId > 0, (f, d) => f.DeviceId == input.DeviceId)
            .WhereIF(input.GroupId > 0, (f, d) => d.GroupId == input.GroupId)
            .WhereIF(input.StartTime.HasValue, (f, d) => f.FaultTime >= input.StartTime)
            .WhereIF(input.EndTime.HasValue, (f, d) => f.FaultTime <= input.EndTime);

        var totalCount = await query.CountAsync();
        var pendingCount = await query.Where((f, d) => f.Status == 0).CountAsync();
        var processingCount = await query.Where((f, d) => f.Status == 1).CountAsync();
        var repairedCount = await query.Where((f, d) => f.Status == 2).CountAsync();
        var ignoredCount = await query.Where((f, d) => f.Status == 3).CountAsync();

        var levelStats = await query
            .GroupBy((f, d) => f.FaultLevel)
            .Select((f, d) => new { Level = f.FaultLevel, Count = SqlFunc.AggregateCount(f.Id) })
            .ToListAsync();

        var typeStats = await query
            .GroupBy((f, d) => f.FaultType)
            .Select((f, d) => new { Type = f.FaultType, Count = SqlFunc.AggregateCount(f.Id) })
            .OrderBy(x => x.Count, OrderByType.Desc)
            .Take(10)
            .ToListAsync();

        // 计算平均修复时长（分钟）
        var avgRepairTime = await query
            .Where((f, d) => f.Status == 2 && f.RepairTime.HasValue)
            .Select((f, d) => SqlFunc.DateDiff(DateType.Minute, f.FaultTime, f.RepairTime.Value))
            .ToListAsync();

        return new EnergyFaultStatOutput
        {
            StatTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            TotalCount = totalCount,
            PendingCount = pendingCount,
            ProcessingCount = processingCount,
            RepairedCount = repairedCount,
            IgnoredCount = ignoredCount,
            RepairRate = totalCount > 0 ? (decimal)repairedCount / totalCount * 100 : 0,
            AvgRepairTime = avgRepairTime.Any() ? (decimal)avgRepairTime.Average() : 0,
            LowLevelCount = levelStats.FirstOrDefault(x => x.Level == 1)?.Count ?? 0,
            MediumLevelCount = levelStats.FirstOrDefault(x => x.Level == 2)?.Count ?? 0,
            HighLevelCount = levelStats.FirstOrDefault(x => x.Level == 3)?.Count ?? 0,
            CriticalLevelCount = levelStats.FirstOrDefault(x => x.Level == 4)?.Count ?? 0,
            TopFaultTypes = typeStats.Select(x => new FaultTypeStatItem
            {
                FaultType = x.Type.ToString(),
                Count = x.Count,
                Percentage = totalCount > 0 ? (decimal)x.Count / totalCount * 100 : 0
            }).ToList()
        };
    }

    /// <summary>
    /// 获取故障趋势数据 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取故障趋势数据")]
    public async Task<List<EnergyFaultTrendOutput>> GetTrend([FromQuery] EnergyFaultStatInput input)
    {
        var query = _energyFaultRep.AsQueryable()
            .LeftJoin<EnergyDevice>((f, d) => f.DeviceId == d.Id)
            .WhereIF(input.DeviceId > 0, (f, d) => f.DeviceId == input.DeviceId)
            .WhereIF(input.GroupId > 0, (f, d) => d.GroupId == input.GroupId)
            .WhereIF(input.StartTime.HasValue, (f, d) => f.FaultTime >= input.StartTime)
            .WhereIF(input.EndTime.HasValue, (f, d) => f.FaultTime <= input.EndTime);

        // 按天分组统计
        var trendData = await query
            .GroupBy((f, d) => new { Date = SqlFunc.DateValue(f.FaultTime, DateType.Day) })
            .Select((f, d) => new EnergyFaultTrendOutput
            {
                Time = f.FaultTime,
                TotalCount = SqlFunc.AggregateCount(f.Id),
                PendingCount = SqlFunc.AggregateSum(SqlFunc.IIF(f.Status == 0, 1, 0)),
                ProcessingCount = SqlFunc.AggregateSum(SqlFunc.IIF(f.Status == 1, 1, 0)),
                RepairedCount = SqlFunc.AggregateSum(SqlFunc.IIF(f.Status == 2, 1, 0)),
                LowLevelCount = SqlFunc.AggregateSum(SqlFunc.IIF(f.FaultLevel == 1, 1, 0)),
                MediumLevelCount = SqlFunc.AggregateSum(SqlFunc.IIF(f.FaultLevel == 2, 1, 0)),
                HighLevelCount = SqlFunc.AggregateSum(SqlFunc.IIF(f.FaultLevel == 3, 1, 0)),
                CriticalLevelCount = SqlFunc.AggregateSum(SqlFunc.IIF(f.FaultLevel == 4, 1, 0))
            })
            .OrderBy(x => x.Time)
            .ToListAsync();

        return trendData;
    }

    /// <summary>
    /// 获取设备故障排行 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取设备故障排行")]
    public async Task<List<EnergyFaultRankOutput>> GetRank([FromQuery] EnergyFaultStatInput input)
    {
        var query = _energyFaultRep.AsQueryable()
            .LeftJoin<EnergyDevice>((f, d) => f.DeviceId == d.Id)
            .WhereIF(input.GroupId > 0, (f, d) => d.GroupId == input.GroupId)
            .WhereIF(input.StartTime.HasValue, (f, d) => f.FaultTime >= input.StartTime)
            .WhereIF(input.EndTime.HasValue, (f, d) => f.FaultTime <= input.EndTime);

        var rankData = await query
            .GroupBy((f, d) => new { f.DeviceId, d.DeviceCode, d.DeviceName, d.Location })
            .Select((f, d) => new EnergyFaultRankOutput
            {
                DeviceId = f.DeviceId,
                DeviceCode = d.DeviceCode,
                DeviceName = d.DeviceName,
                DeviceLocation = d.Location,
                TotalFaultCount = SqlFunc.AggregateCount(f.Id),
                PendingCount = SqlFunc.AggregateSum(SqlFunc.IIF(f.Status == 0, 1, 0)),
                RepairedCount = SqlFunc.AggregateSum(SqlFunc.IIF(f.Status == 2, 1, 0)),
                AvgRepairTime = SqlFunc.AggregateAvg(SqlFunc.DateDiff(DateType.Minute, f.FaultTime, f.RepairTime ?? f.FaultTime)),
                LastFaultTime = SqlFunc.AggregateMax(f.FaultTime)
            })
            .OrderBy(x => x.TotalFaultCount, OrderByType.Desc)
            .ToListAsync();

        // 添加排名和故障率
        for (int i = 0; i < rankData.Count; i++)
        {
            rankData[i].Rank = i + 1;
            rankData[i].FaultRate = rankData[i].TotalFaultCount > 0 && rankData[i].RepairedCount > 0
                ? (decimal)rankData[i].RepairedCount / rankData[i].TotalFaultCount * 100
                : 0;
        }

        return rankData;
    }

    /// <summary>
    /// 导出故障数据 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出故障数据")]
    public async Task<FileStreamResult> ExportData([FromQuery] EnergyFaultInput input)
    {
        var data = await _energyFaultRep.AsQueryable()
            .LeftJoin<EnergyDevice>((f, d) => f.DeviceId == d.Id)
            .WhereIF(input.DeviceId > 0, (f, d) => f.DeviceId == input.DeviceId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.FaultType), (f, d) => f.FaultType.ToString().Contains(input.FaultType))
            .WhereIF(input.FaultLevel.HasValue, (f, d) => f.FaultLevel == input.FaultLevel)
            .WhereIF(input.FaultStatus.HasValue, (f, d) => f.Status == input.FaultStatus)
            .WhereIF(input.StartTime.HasValue, (f, d) => f.FaultTime >= input.StartTime)
            .WhereIF(input.EndTime.HasValue, (f, d) => f.FaultTime <= input.EndTime)
            .OrderBy((f, d) => f.FaultTime, OrderByType.Desc)
            .Select((f, d) => new
            {
                故障编码 = f.FaultCode,
                设备编码 = d.DeviceCode,
                设备名称 = d.DeviceName,
                设备位置 = d.Location,
                故障类型 = f.FaultType.ToString(),
                故障等级 = f.FaultLevel == 1 ? "低" : f.FaultLevel == 2 ? "中" : f.FaultLevel == 3 ? "高" : "紧急",
                故障描述 = f.FaultDescription,
                故障时间 = f.FaultTime,
                故障状态 = f.Status == 0 ? "待处理" : f.Status == 1 ? "处理中" : f.Status == 2 ? "已修复" : "已忽略",
                维修人员 = f.HandlerName,
                维修时间 = f.RepairTime,
                维修描述 = f.RepairDescription,
                持续时长_分钟 = SqlFunc.DateDiff(DateType.Minute, f.FaultTime, f.RepairTime ?? DateTime.Now),
                创建时间 = f.CreateTime
            })
            .ToListAsync();

        // 实现Excel导出功能
        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add("故障数据");
        
        // 设置表头
        var headers = new string[]
        {
            "故障编码", "设备编码", "设备名称", "设备位置", "故障类型", "故障等级", "故障描述", 
            "故障时间", "故障状态", "维修人员", "维修时间", "维修描述", "持续时长(分钟)", "创建时间"
        };
        
        for (int i = 0; i < headers.Length; i++)
        {
            worksheet.Cells[1, i + 1].Value = headers[i];
            worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            worksheet.Cells[1, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
            worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(211, 211, 211, 255);
        }
        
        // 填充数据
        for (int row = 0; row < data.Count; row++)
        {
            var item = data[row];
            worksheet.Cells[row + 2, 1].Value = item.故障编码;
            worksheet.Cells[row + 2, 2].Value = item.设备编码;
            worksheet.Cells[row + 2, 3].Value = item.设备名称;
            worksheet.Cells[row + 2, 4].Value = item.设备位置;
            worksheet.Cells[row + 2, 5].Value = item.故障类型;
            worksheet.Cells[row + 2, 6].Value = item.故障等级;
            worksheet.Cells[row + 2, 7].Value = item.故障描述;
            worksheet.Cells[row + 2, 8].Value = item.故障时间.ToString("yyyy-MM-dd HH:mm:ss");
            worksheet.Cells[row + 2, 9].Value = item.故障状态;
            worksheet.Cells[row + 2, 10].Value = item.维修人员;
            worksheet.Cells[row + 2, 11].Value = item.维修时间?.ToString("yyyy-MM-dd HH:mm:ss") ?? "";
            worksheet.Cells[row + 2, 12].Value = item.维修描述;
            worksheet.Cells[row + 2, 13].Value = item.持续时长_分钟;
            worksheet.Cells[row + 2, 14].Value = item.创建时间.ToString("yyyy-MM-dd HH:mm:ss");
        }
        
        // 自动调整列宽
        worksheet.Cells.AutoFitColumns();
        
        var stream = new MemoryStream();
        package.SaveAs(stream);
        stream.Position = 0;
        
        return new FileStreamResult(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        {
            FileDownloadName = $"故障数据_{DateTime.Now:yyyyMMddHHmmss}.xlsx"
        };
    }

    /// <summary>
    /// 导出故障数据到Excel 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "ExportExcel"), HttpPost]
    [DisplayName("导出故障数据到Excel")]
    public async Task<IActionResult> ExportExcel(EnergyFaultInput input)
    {
        var excelData = await ExportData(input);
        var fileName = $"故障数据_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
        
        return new FileContentResult(((MemoryStream)excelData.FileStream).ToArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        {
            FileDownloadName = fileName
        };
    }
}