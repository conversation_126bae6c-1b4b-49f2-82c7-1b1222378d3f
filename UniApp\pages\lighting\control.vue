<template>
  <view class="lighting-control">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">照明控制</text>
      <view class="header-actions">
        <button class="btn-refresh" @click="refreshData">
          <text class="icon">🔄</text>
          <text>刷新</text>
        </button>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-container">
      <view class="stat-card">
        <text class="stat-value">{{ stats.totalDevices }}</text>
        <text class="stat-label">设备总数</text>
      </view>
      <view class="stat-card">
        <text class="stat-value">{{ stats.onlineDevices }}</text>
        <text class="stat-label">在线设备</text>
      </view>
      <view class="stat-card">
        <text class="stat-value">{{ stats.onDevices }}</text>
        <text class="stat-label">开启设备</text>
      </view>
      <view class="stat-card">
        <text class="stat-value">{{ stats.totalPower }}W</text>
        <text class="stat-label">总功耗</text>
      </view>
    </view>

    <!-- 快捷控制 -->
    <view class="quick-control">
      <view class="control-header">
        <text class="section-title">快捷控制</text>
      </view>
      <view class="control-actions">
        <button class="control-btn" :class="{ active: allDevicesOn }" @click="toggleAllDevices">
          <text class="btn-icon">💡</text>
          <text>{{ allDevicesOn ? '全部关闭' : '全部开启' }}</text>
        </button>
        <button class="control-btn" @click="showBrightnessModal">
          <text class="btn-icon">🔆</text>
          <text>批量调光</text>
        </button>
        <button class="control-btn" @click="showSceneModal">
          <text class="btn-icon">🎭</text>
          <text>场景模式</text>
        </button>
      </view>
    </view>

    <!-- 设备分组 -->
    <view class="device-groups">
      <view class="group-tabs">
        <view 
          v-for="group in deviceGroups" 
          :key="group.id"
          class="group-tab"
          :class="{ active: activeGroupId === group.id }"
          @click="switchGroup(group.id)"
        >
          <text>{{ group.name }}</text>
          <text class="group-count">({{ group.deviceCount }})</text>
        </view>
      </view>
    </view>

    <!-- 设备列表 -->
    <view class="device-list">
      <view class="list-header">
        <text class="section-title">设备列表</text>
        <view class="list-actions">
          <button class="btn-filter" @click="showFilterModal">
            <text class="icon">🔍</text>
            <text>筛选</text>
          </button>
        </view>
      </view>
      
      <view class="device-items">
        <view 
          v-for="device in filteredDevices" 
          :key="device.id"
          class="device-item"
          @click="selectDevice(device)"
        >
          <view class="device-info">
            <view class="device-header">
              <text class="device-name">{{ device.name }}</text>
              <view class="device-status" :class="getStatusClass(device.status)">
                <text>{{ getStatusText(device.status) }}</text>
              </view>
            </view>
            <view class="device-details">
              <text class="device-location">📍 {{ device.location }}</text>
              <text class="device-power">⚡ {{ device.power }}W</text>
            </view>
          </view>
          
          <view class="device-controls">
            <!-- 开关控制 -->
            <view class="switch-control">
              <switch 
                :checked="device.isOn" 
                @change="toggleDevice(device)"
                :disabled="device.status !== 'online'"
              />
            </view>
            
            <!-- 亮度控制 -->
            <view class="brightness-control" v-if="device.isOn && device.status === 'online'">
              <text class="brightness-label">亮度</text>
              <slider 
                :value="device.brightness" 
                @change="adjustBrightness(device, $event)"
                min="10" 
                max="100" 
                step="10"
                activeColor="#1890ff"
                backgroundColor="#f0f0f0"
              />
              <text class="brightness-value">{{ device.brightness }}%</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 批量调光弹窗 -->
    <uni-popup ref="brightnessPopup" type="center">
      <view class="brightness-modal">
        <view class="modal-header">
          <text class="modal-title">批量调光</text>
          <button class="btn-close" @click="closeBrightnessModal">✕</button>
        </view>
        <view class="modal-content">
          <view class="brightness-setting">
            <text class="setting-label">目标亮度：{{ batchBrightness }}%</text>
            <slider 
              :value="batchBrightness" 
              min="10" 
              max="100" 
              step="10"
              activeColor="#1890ff"
              @change="updateBatchBrightness"
            />
          </view>
          <view class="device-selection">
            <text class="selection-title">选择设备：</text>
            <view class="device-checkboxes">
              <label 
                v-for="device in onlineDevices" 
                :key="device.id"
                class="device-checkbox"
              >
                <checkbox 
                  :value="device.id" 
                  :checked="selectedDevices.includes(device.id)"
                  @change="toggleDeviceSelection(device.id)"
                />
                <text>{{ device.name }}</text>
              </label>
            </view>
          </view>
        </view>
        <view class="modal-actions">
          <button class="btn-cancel" @click="closeBrightnessModal">取消</button>
          <button class="btn-confirm" @click="applyBatchBrightness">应用</button>
        </view>
      </view>
    </uni-popup>

    <!-- 场景模式弹窗 -->
    <uni-popup ref="scenePopup" type="center">
      <view class="scene-modal">
        <view class="modal-header">
          <text class="modal-title">场景模式</text>
          <button class="btn-close" @click="closeSceneModal">✕</button>
        </view>
        <view class="modal-content">
          <view class="scene-list">
            <view 
              v-for="scene in scenes" 
              :key="scene.id"
              class="scene-item"
              @click="applyScene(scene)"
            >
              <text class="scene-icon">{{ scene.icon }}</text>
              <view class="scene-info">
                <text class="scene-name">{{ scene.name }}</text>
                <text class="scene-desc">{{ scene.description }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 筛选弹窗 -->
    <uni-popup ref="filterPopup" type="bottom">
      <view class="filter-modal">
        <view class="modal-header">
          <text class="modal-title">设备筛选</text>
          <button class="btn-close" @click="closeFilterModal">✕</button>
        </view>
        <view class="modal-content">
          <view class="filter-item">
            <text class="filter-label">设备状态：</text>
            <picker 
              :value="filterStatus" 
              :range="statusOptions" 
              range-key="label"
              @change="updateFilterStatus"
            >
              <view class="picker-text">{{ getStatusLabel(filterStatus) }}</view>
            </picker>
          </view>
          <view class="filter-item">
            <text class="filter-label">设备类型：</text>
            <picker 
              :value="filterType" 
              :range="typeOptions" 
              range-key="label"
              @change="updateFilterType"
            >
              <view class="picker-text">{{ getTypeLabel(filterType) }}</view>
            </picker>
          </view>
        </view>
        <view class="modal-actions">
          <button class="btn-reset" @click="resetFilter">重置</button>
          <button class="btn-confirm" @click="applyFilter">确定</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnload } from 'vue'
import { getDeviceList, getGroups, batchToggle, toggleDeviceSwitch, setDeviceBrightness, batchSetBrightness } from '@/api/device'
import { getScenes } from '@/api/lighting'
import { authGuard } from '@/utils/authGuard'
// import { applyScene } from '@/api/lighting' // 暂时注释，未使用

export default {
  name: 'LightingControl',
  setup() {
    // 响应式数据
    const stats = reactive({
      totalDevices: 0,
      onlineDevices: 0,
      onDevices: 0,
      totalPower: 0
    })

    const devices = ref([])
    const deviceGroups = ref([])
    const activeGroupId = ref('all')
    const scenes = ref([])
    
    // 批量调光相关
    const batchBrightness = ref(80)
    const selectedDevices = ref([])
    
    // 筛选相关
    const filterStatus = ref('all')
    const filterType = ref('all')
    const statusOptions = [
      { value: 'all', label: '全部状态' },
      { value: 'online', label: '在线' },
      { value: 'offline', label: '离线' },
      { value: 'fault', label: '故障' }
    ]
    const typeOptions = [
      { value: 'all', label: '全部类型' },
      { value: 'led', label: 'LED灯' },
      { value: 'fluorescent', label: '荧光灯' },
      { value: 'halogen', label: '卤素灯' }
    ]

    // 计算属性
    const allDevicesOn = computed(() => {
      const onlineDevices = devices.value.filter(d => d.status === 'online')
      return onlineDevices.length > 0 && onlineDevices.every(d => d.isOn)
    })

    const onlineDevices = computed(() => {
      return devices.value.filter(d => d.status === 'online')
    })

    const filteredDevices = computed(() => {
      let result = devices.value
      
      // 按分组筛选
      if (activeGroupId.value !== 'all') {
        result = result.filter(d => d.groupId === activeGroupId.value)
      }
      
      // 按状态筛选
      if (filterStatus.value !== 'all') {
        result = result.filter(d => d.status === filterStatus.value)
      }
      
      // 按类型筛选
      if (filterType.value !== 'all') {
        result = result.filter(d => d.type === filterType.value)
      }
      
      return result
    })

    // 方法
    const initData = async () => {
      try {
        console.log('开始加载照明控制数据...')
        uni.showLoading({ title: '加载数据...' })
        
        // 并行加载设备、分组和场景数据
        console.log('调用API获取数据...')
        const [devicesRes, groupsRes, scenesRes] = await Promise.all([
          getDeviceList({ page: 1, pageSize: 100 }),
          getGroups(),
          getScenes()
        ])
        
        console.log('API响应结果:', { devicesRes, groupsRes, scenesRes })
        
        if (devicesRes && devicesRes.success) {
          devices.value = devicesRes.data?.list || []
          console.log('设备数据加载成功:', devices.value.length, '个设备')
        } else {
          console.warn('设备数据加载失败:', devicesRes)
          devices.value = []
        }
        
        if (groupsRes && groupsRes.success) {
          const groups = groupsRes.data || []
          deviceGroups.value = [
            { id: 'all', name: '全部设备', deviceCount: devices.value.length },
            ...groups.map(group => ({
              ...group,
              deviceCount: devices.value.filter(d => d.groupId === group.id).length
            }))
          ]
          console.log('分组数据加载成功:', deviceGroups.value.length, '个分组')
        } else {
          console.warn('分组数据加载失败:', groupsRes)
          deviceGroups.value = [{ id: 'all', name: '全部设备', deviceCount: 0 }]
        }
        
        if (scenesRes && scenesRes.success) {
          scenes.value = scenesRes.data || []
          console.log('场景数据加载成功:', scenes.value.length, '个场景')
        } else {
          console.warn('场景数据加载失败:', scenesRes)
          scenes.value = []
        }
        
        updateStats()
        console.log('数据加载完成，统计信息:', stats.value)
        uni.hideLoading()
      } catch (error) {
        console.error('加载数据失败:', error)
        console.error('错误详情:', error.stack || error.toString())
        uni.hideLoading()
        uni.showToast({ title: error.message || '数据加载失败', icon: 'error' })
        
        // 设置默认数据以防止页面完全空白
        console.log('设置默认模拟数据...')
        devices.value = [
          {
            id: 'demo-1',
            name: '演示灯具1',
            status: 'online',
            isOn: true,
            brightness: 80,
            power: 12,
            type: 'led',
            groupId: 'demo-group'
          },
          {
            id: 'demo-2', 
            name: '演示灯具2',
            status: 'online',
            isOn: false,
            brightness: 0,
            power: 0,
            type: 'led',
            groupId: 'demo-group'
          }
        ]
        deviceGroups.value = [
          { id: 'all', name: '全部设备', deviceCount: 2 },
          { id: 'demo-group', name: '演示分组', deviceCount: 2 }
        ]
        scenes.value = [
          { id: 'demo-scene-1', name: '演示场景1', description: '测试场景' },
          { id: 'demo-scene-2', name: '演示场景2', description: '测试场景' }
        ]
        updateStats()
        console.log('默认数据设置完成')
      }
    }

    const updateStats = () => {
      stats.totalDevices = devices.value.length
      stats.onlineDevices = devices.value.filter(d => d.status === 'online').length
      stats.onDevices = devices.value.filter(d => d.isOn).length
      stats.totalPower = devices.value
        .filter(d => d.isOn)
        .reduce((sum, d) => sum + d.power, 0)
    }

    const refreshData = async () => {
      try {
        await initData()
        uni.showToast({ title: '刷新成功', icon: 'success' })
      } catch (error) {
        console.error('刷新失败:', error)
        uni.showToast({ title: error.message || '刷新失败', icon: 'error' })
      }
    }

    const toggleAllDevices = async () => {
      try {
        const targetState = !allDevicesOn.value
        const onlineDeviceIds = devices.value
          .filter(d => d.status === 'online')
          .map(d => d.id)
        
        if (onlineDeviceIds.length === 0) {
          uni.showToast({ title: '没有在线设备', icon: 'error' })
          return
        }
        
        uni.showLoading({ title: targetState ? '开启中...' : '关闭中...' })
        
        const response = await batchToggle(onlineDeviceIds, targetState)
        
        if (response.success) {
          devices.value.forEach(device => {
            if (device.status === 'online') {
              device.isOn = targetState
            }
          })
          updateStats()
          uni.showToast({ 
            title: targetState ? '已全部开启' : '已全部关闭', 
            icon: 'success' 
          })
        } else {
          throw new Error(response.message || '批量操作失败')
        }
        
        uni.hideLoading()
      } catch (error) {
        console.error('批量操作失败:', error)
        uni.hideLoading()
        uni.showToast({ title: error.message || '操作失败', icon: 'error' })
      }
    }

    const toggleDevice = async (device) => {
      if (device.status !== 'online') {
        uni.showToast({ title: '设备离线，无法控制', icon: 'error' })
        return
      }
      
      try {
        const targetState = !device.isOn
        uni.showLoading({ title: targetState ? '开启中...' : '关闭中...' })
        
        const response = await toggleDeviceSwitch(device.id, targetState)
        
        if (response.success) {
          device.isOn = targetState
          updateStats()
          uni.showToast({ 
            title: `${device.name} 已${device.isOn ? '开启' : '关闭'}`, 
            icon: 'success' 
          })
        } else {
          throw new Error(response.message || '操作失败')
        }
        
        uni.hideLoading()
      } catch (error) {
        console.error('设备操作失败:', error)
        uni.hideLoading()
        uni.showToast({ title: error.message || '操作失败', icon: 'error' })
      }
    }

    const adjustBrightness = async (device, event) => {
      try {
        const brightness = event.detail.value
        
        const response = await setDeviceBrightness(device.id, brightness)
        
        if (response.success) {
          device.brightness = brightness
          uni.showToast({ 
            title: `${device.name} 亮度调至 ${brightness}%`, 
            icon: 'success' 
          })
        } else {
          throw new Error(response.message || '亮度调节失败')
        }
      } catch (error) {
        console.error('亮度调节失败:', error)
        uni.showToast({ title: error.message || '调节失败', icon: 'error' })
      }
    }

    const switchGroup = (groupId) => {
      activeGroupId.value = groupId
    }

    const selectDevice = (device) => {
      uni.navigateTo({
        url: `/pages/device/detail?id=${device.id}`
      })
    }

    const getStatusClass = (status) => {
      return {
        'status-online': status === 'online',
        'status-offline': status === 'offline',
        'status-fault': status === 'fault'
      }
    }

    const getStatusText = (status) => {
      const statusMap = {
        online: '在线',
        offline: '离线',
        fault: '故障'
      }
      return statusMap[status] || '未知'
    }

    // 弹窗相关方法
    const showBrightnessModal = () => {
      selectedDevices.value = []
      uni.$refs.brightnessPopup.open()
    }

    const closeBrightnessModal = () => {
      uni.$refs.brightnessPopup.close()
    }

    const updateBatchBrightness = (event) => {
      batchBrightness.value = event.detail.value
    }

    const toggleDeviceSelection = (deviceId) => {
      const index = selectedDevices.value.indexOf(deviceId)
      if (index > -1) {
        selectedDevices.value.splice(index, 1)
      } else {
        selectedDevices.value.push(deviceId)
      }
    }

    const applyBatchBrightness = async () => {
      if (selectedDevices.value.length === 0) {
        uni.showToast({ title: '请选择设备', icon: 'error' })
        return
      }
      
      try {
        uni.showLoading({ title: '调节中...' })
        
        const response = await batchSetBrightness(selectedDevices.value, batchBrightness.value)
        
        if (response.success) {
          selectedDevices.value.forEach(deviceId => {
            const device = devices.value.find(d => d.id === deviceId)
            if (device && device.status === 'online') {
              device.brightness = batchBrightness.value
              if (!device.isOn) {
                device.isOn = true
              }
            }
          })
          
          updateStats()
          closeBrightnessModal()
          uni.showToast({ title: '批量调光成功', icon: 'success' })
        } else {
          throw new Error(response.message || '批量调节失败')
        }
        
        uni.hideLoading()
      } catch (error) {
        console.error('批量调节失败:', error)
        uni.hideLoading()
        uni.showToast({ title: error.message || '调节失败', icon: 'error' })
      }
    }

    const showSceneModal = () => {
      uni.$refs.scenePopup.open()
    }

    const closeSceneModal = () => {
      uni.$refs.scenePopup.close()
    }

    const applyScene = async (scene) => {
      try {
        uni.showLoading({ title: '应用场景中...' })
        
        const onlineDeviceIds = devices.value
          .filter(d => d.status === 'online')
          .map(d => d.id)
        
        if (onlineDeviceIds.length === 0) {
          uni.showToast({ title: '没有在线设备', icon: 'error' })
          return
        }
        
        const response = await applyScene(scene.id, onlineDeviceIds)
        
        if (response.success) {
          devices.value.forEach(device => {
            if (device.status === 'online') {
              device.isOn = true
              device.brightness = scene.brightness
            }
          })
          updateStats()
          closeSceneModal()
          uni.showToast({ title: `已应用${scene.name}`, icon: 'success' })
        } else {
          throw new Error(response.message || '场景应用失败')
        }
        
        uni.hideLoading()
      } catch (error) {
        console.error('场景应用失败:', error)
        uni.hideLoading()
        uni.showToast({ title: error.message || '应用失败', icon: 'error' })
      }
    }

    const showFilterModal = () => {
      uni.$refs.filterPopup.open()
    }

    const closeFilterModal = () => {
      uni.$refs.filterPopup.close()
    }

    const updateFilterStatus = (event) => {
      filterStatus.value = statusOptions[event.detail.value].value
    }

    const updateFilterType = (event) => {
      filterType.value = typeOptions[event.detail.value].value
    }

    const getStatusLabel = (value) => {
      return statusOptions.find(option => option.value === value)?.label || '全部状态'
    }

    const getTypeLabel = (value) => {
      return typeOptions.find(option => option.value === value)?.label || '全部类型'
    }

    const resetFilter = () => {
      filterStatus.value = 'all'
      filterType.value = 'all'
    }

    const applyFilter = () => {
      closeFilterModal()
      uni.showToast({ title: '筛选已应用', icon: 'success' })
    }

    // 生命周期
    onMounted(async () => {
      // 暂时跳过登录验证，直接加载数据
      // TODO: 修复登录状态检查问题后恢复验证
      /*
      const canAccess = await authGuard({
        requireAuth: true,
        message: '照明控制页面需要登录后才能访问'
      })
      
      if (!canAccess) {
        return
      }
      */
      
      initData()
    })

    onUnload(() => {
      // 页面卸载时清理资源
      // 清空设备数据数组，释放内存
      devices.value = []
      deviceGroups.value = []
      scenes.value = []
      selectedDevices.value = []
      
      // 重置统计数据
      stats.value = {
        total: 0,
        online: 0,
        offline: 0,
        brightness: 0
      }
      
      // 重置筛选条件
      filterStatus.value = 'all'
      filterType.value = 'all'
      
      // 重置批量操作状态
      batchBrightness.value = 50
      activeGroupId.value = 'all'
      
      // 隐藏loading状态
      uni.hideLoading()
    })

    return {
      stats,
      devices,
      deviceGroups,
      activeGroupId,
      scenes,
      batchBrightness,
      selectedDevices,
      filterStatus,
      filterType,
      statusOptions,
      typeOptions,
      allDevicesOn,
      onlineDevices,
      filteredDevices,
      refreshData,
      toggleAllDevices,
      toggleDevice,
      adjustBrightness,
      switchGroup,
      selectDevice,
      getStatusClass,
      getStatusText,
      showBrightnessModal,
      closeBrightnessModal,
      updateBatchBrightness,
      toggleDeviceSelection,
      applyBatchBrightness,
      showSceneModal,
      closeSceneModal,
      applyScene,
      showFilterModal,
      closeFilterModal,
      updateFilterStatus,
      updateFilterType,
      getStatusLabel,
      getTypeLabel,
      resetFilter,
      applyFilter
    }
  }
}
</script>

<style scoped>
.lighting-control {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

.btn-refresh {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 15rpx 25rpx;
  background-color: #1890ff;
  color: white;
  border-radius: 8rpx;
  border: none;
  font-size: 28rpx;
}

/* 统计信息 */
.stats-container {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.stat-card {
  flex: 1;
  background-color: white;
  padding: 30rpx 20rpx;
  border-radius: 12rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.stat-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 快捷控制 */
.quick-control {
  background-color: white;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.control-header {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.control-actions {
  display: flex;
  gap: 20rpx;
}

.control-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
  padding: 25rpx 15rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #495057;
}

.control-btn.active {
  background-color: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
}

.btn-icon {
  font-size: 32rpx;
}

/* 设备分组 */
.device-groups {
  margin-bottom: 30rpx;
}

.group-tabs {
  display: flex;
  background-color: white;
  border-radius: 12rpx;
  padding: 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.group-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 15rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
  cursor: pointer;
}

.group-tab.active {
  background-color: #1890ff;
  color: white;
}

.group-count {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 设备列表 */
.device-list {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.list-actions {
  display: flex;
  gap: 15rpx;
}

.btn-filter {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #dee2e6;
  border-radius: 6rpx;
  font-size: 26rpx;
  color: #495057;
}

.device-items {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.device-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e9ecef;
}

.device-info {
  flex: 1;
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.device-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.device-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.status-online {
  background-color: #d4edda;
  color: #155724;
}

.status-offline {
  background-color: #f8d7da;
  color: #721c24;
}

.status-fault {
  background-color: #fff3cd;
  color: #856404;
}

.device-details {
  display: flex;
  gap: 30rpx;
  font-size: 26rpx;
  color: #666;
}

.device-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15rpx;
  min-width: 200rpx;
}

.switch-control {
  display: flex;
  align-items: center;
}

.brightness-control {
  display: flex;
  align-items: center;
  gap: 15rpx;
  width: 100%;
}

.brightness-label {
  font-size: 24rpx;
  color: #666;
  white-space: nowrap;
}

.brightness-value {
  font-size: 24rpx;
  color: #1890ff;
  font-weight: bold;
  min-width: 60rpx;
  text-align: center;
}

/* 弹窗样式 */
.brightness-modal,
.scene-modal,
.filter-modal {
  background-color: white;
  border-radius: 12rpx;
  width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.btn-close {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f8f9fa;
  border: none;
  font-size: 28rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  padding: 30rpx;
  max-height: 500rpx;
  overflow-y: auto;
}

.brightness-setting {
  margin-bottom: 30rpx;
}

.setting-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.device-selection {
  margin-bottom: 20rpx;
}

.selection-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.device-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  max-height: 200rpx;
  overflow-y: auto;
}

.device-checkbox {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 10rpx;
  font-size: 26rpx;
}

.scene-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.scene-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 25rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  cursor: pointer;
}

.scene-item:hover {
  background-color: #e9ecef;
}

.scene-icon {
  font-size: 40rpx;
}

.scene-info {
  flex: 1;
}

.scene-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.scene-desc {
  font-size: 24rpx;
  color: #666;
}

.filter-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
}

.filter-label {
  font-size: 28rpx;
  color: #333;
}

.picker-text {
  padding: 15rpx 20rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #dee2e6;
  border-radius: 6rpx;
  font-size: 26rpx;
  color: #495057;
  min-width: 200rpx;
  text-align: center;
}

.modal-actions {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #e9ecef;
}

.btn-cancel,
.btn-reset {
  flex: 1;
  padding: 25rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #dee2e6;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #495057;
}

.btn-confirm {
  flex: 1;
  padding: 25rpx;
  background-color: #1890ff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: white;
}

.icon {
  font-size: 24rpx;
}
</style>