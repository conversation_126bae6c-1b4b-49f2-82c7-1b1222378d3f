<template>
  <view class="login-container">
    <!-- 背景装饰 -->
    <view class="bg-decoration">
      <view class="bg-circle circle-1"></view>
      <view class="bg-circle circle-2"></view>
      <view class="bg-circle circle-3"></view>
    </view>

    <!-- 登录表单 -->
    <view class="login-form">
      <!-- Logo和标题 -->
      <view class="header-section">
        <view class="logo">
          <image 
            src="https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=smart%20lighting%20system%20logo%20modern%20minimalist&image_size=square" 
            class="logo-image" 
            mode="aspectFit"
          />
        </view>
        <text class="app-title">节能灯管理系统</text>
        <text class="app-subtitle">智能照明，节能环保</text>
      </view>

      <!-- 登录表单 -->
      <view class="form-section">
        <!-- 用户名输入 -->
        <view class="input-group">
          <view class="input-label">
            <text class="label-icon">👤</text>
            <text class="label-text">用户名</text>
          </view>
          <input 
            class="form-input"
            type="text"
            v-model="loginForm.username"
            placeholder="请输入用户名"
            :class="{ error: errors.username }"
            @blur="validateUsername"
          />
          <text class="error-text" v-if="errors.username">{{ errors.username }}</text>
        </view>

        <!-- 密码输入 -->
        <view class="input-group">
          <view class="input-label">
            <text class="label-icon">🔒</text>
            <text class="label-text">密码</text>
          </view>
          <view class="password-input">
            <input 
              class="form-input"
              :type="showPassword ? 'text' : 'password'"
              v-model="loginForm.password"
              placeholder="请输入密码"
              :class="{ error: errors.password }"
              @blur="validatePassword"
            />
            <text 
              class="password-toggle"
              @click="togglePassword"
            >
              {{ showPassword ? '👁️' : '👁️‍🗨️' }}
            </text>
          </view>
          <text class="error-text" v-if="errors.password">{{ errors.password }}</text>
        </view>

        <!-- 验证码输入 -->
        <view class="input-group" v-if="needCaptcha">
          <view class="input-label">
            <text class="label-icon">🔢</text>
            <text class="label-text">验证码</text>
          </view>
          <view class="captcha-input">
            <input 
              class="form-input captcha-field"
              type="text"
              v-model="loginForm.captcha"
              placeholder="请输入验证码"
              :class="{ error: errors.captcha }"
              @blur="validateCaptcha"
            />
            <view class="captcha-image" @click="refreshCaptcha">
              <image 
                :src="captchaUrl"
                class="captcha-img"
                mode="aspectFit"
              />
              <text class="refresh-text">点击刷新</text>
            </view>
          </view>
          <text class="error-text" v-if="errors.captcha">{{ errors.captcha }}</text>
        </view>

        <!-- 记住密码和忘记密码 -->
        <view class="form-options">
          <label class="remember-checkbox">
            <input 
              type="checkbox"
              v-model="loginForm.remember"
              class="checkbox"
            />
            <text class="checkbox-text">记住密码</text>
          </label>
          <text class="forgot-password" @click="forgotPassword">忘记密码？</text>
        </view>

        <!-- 登录按钮 -->
        <button 
          class="login-btn"
          :class="{ loading: loading || isLoading }"
          :disabled="loading || isLoading"
          @click="handleLogin"
        >
          <text v-if="!loading && !isLoading">登录</text>
          <text v-else>登录中...</text>
        </button>

        <!-- 其他登录方式 -->
        <view class="other-login">
          <view class="divider">
            <view class="divider-line"></view>
            <text class="divider-text">其他登录方式</text>
            <view class="divider-line"></view>
          </view>
          
          <view class="social-login">
            <view class="social-btn wechat" @click="wechatLogin">
              <text class="social-icon">💬</text>
              <text class="social-text">微信登录</text>
            </view>
            <view class="social-btn phone" @click="phoneLogin">
              <text class="social-icon">📱</text>
              <text class="social-text">手机登录</text>
            </view>
          </view>
        </view>

        <!-- 注册链接 -->
        <view class="register-link">
          <text class="register-text">还没有账号？</text>
          <text class="register-btn" @click="goRegister">立即注册</text>
        </view>
      </view>
    </view>

    <!-- 忘记密码弹窗 -->
    <view class="modal-overlay" v-if="showForgotModal" @click="closeForgotModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">找回密码</text>
          <text class="modal-close" @click="closeForgotModal">✕</text>
        </view>
        
        <view class="modal-body">
          <view class="input-group">
            <text class="input-label">手机号码</text>
            <input 
              class="form-input"
              type="number"
              v-model="forgotForm.phone"
              placeholder="请输入注册手机号"
            />
          </view>
          
          <view class="input-group">
            <text class="input-label">验证码</text>
            <view class="sms-input">
              <input 
                class="form-input sms-field"
                type="number"
                v-model="forgotForm.smsCode"
                placeholder="请输入验证码"
              />
              <button 
                class="sms-btn"
                :disabled="smsCountdown > 0"
                @click="sendSmsCode"
              >
                {{ smsCountdown > 0 ? `${smsCountdown}s` : '发送验证码' }}
              </button>
            </view>
          </view>
          
          <view class="input-group">
            <text class="input-label">新密码</text>
            <input 
              class="form-input"
              type="password"
              v-model="forgotForm.newPassword"
              placeholder="请输入新密码"
            />
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="modal-btn cancel" @click="closeForgotModal">取消</button>
          <button class="modal-btn confirm" @click="resetPassword">确认重置</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { onUnload } from '@dcloudio/uni-app'
import { encryptPassword } from '@/utils/crypto'
import { getCaptcha } from '../../api/auth.js'

// Vuex store
const store = useStore()

// 计算属性
const isLoggedIn = computed(() => store.getters['user/isLoggedIn'])
const loading = computed(() => store.state.user.loading)

// 响应式数据
const loginForm = reactive({
  username: '',
  password: '',
  captcha: '',
  codeId: '',
  remember: false
})

const forgotForm = reactive({
  phone: '',
  smsCode: '',
  newPassword: ''
})

const errors = reactive({
  username: '',
  password: '',
  captcha: ''
})

const isLoading = ref(false)
const showPassword = ref(false)
const needCaptcha = ref(false)
const captchaUrl = ref('')
const showForgotModal = ref(false)
const smsCountdown = ref(0)
const smsTimer = ref(null)
const navigationTimer = ref(null)

// 方法
const generateCaptcha = async () => {
  try {
    const response = await getCaptcha()
	console.log(response.data);
    if (response && response.data) {
      captchaUrl.value = `data:image/png;base64,${response.data.result.img}`
      loginForm.codeId = response.data.result.id
    }
  } catch (error) {
    console.error('获取验证码失败:', error)
    uni.showToast({
      title: '获取验证码失败',
      icon: 'error'
    })
  }
}

const refreshCaptcha = async () => {
  await generateCaptcha()
  loginForm.captcha = ''
}

const togglePassword = () => {
  showPassword.value = !showPassword.value
}

const validateUsername = () => {
  if (!loginForm.username) {
    errors.username = '请输入用户名'
    return false
  }
  if (loginForm.username.length < 3) {
    errors.username = '用户名至少3个字符'
    return false
  }
  errors.username = ''
  return true
}

const validatePassword = () => {
  if (!loginForm.password) {
    errors.password = '请输入密码'
    return false
  }
  if (loginForm.password.length < 6) {
    errors.password = '密码至少6个字符'
    return false
  }
  errors.password = ''
  return true
}

const validateCaptcha = () => {
  if (needCaptcha.value && !loginForm.captcha) {
    errors.captcha = '请输入验证码'
    return false
  }
  errors.captcha = ''
  return true
}

const validateForm = () => {
  const isUsernameValid = validateUsername()
  const isPasswordValid = validatePassword()
  const isCaptchaValid = validateCaptcha()
  
  return isUsernameValid && isPasswordValid && isCaptchaValid
}

const handleLogin = async () => {
  if (!validateForm()) {
    return
  }
  
  isLoading.value = true
  
  try {
    // SM2加密密码
    const encryptedPassword = encryptPassword(loginForm.password)
    
    // 构建登录数据
    const loginData = {
      account: loginForm.username,
      password: encryptedPassword,
      code: needCaptcha.value ? loginForm.captcha : undefined,
      codeId: needCaptcha.value ? parseInt(loginForm.codeId) : undefined,
      tenantId: *************//暂时默认
    }
    
    // 调用Vuex登录action
    await store.dispatch('user/login', loginData)
    
    // 处理记住密码
    if (loginForm.remember) {
      uni.setStorageSync('rememberedUser', {
        username: loginForm.username,
        password: loginForm.password
      })
    } else {
      uni.removeStorageSync('rememberedUser')
    }
    
    uni.showToast({
      title: '登录成功',
      icon: 'success'
    })
    
    // 跳转到首页
    navigationTimer.value = setTimeout(() => {
      uni.switchTab({
        url: '/pages/index/index'
      })
      navigationTimer.value = null
    }, 1500)
    
  } catch (error) {
    console.error('登录失败:', error)
    
    // 根据错误类型处理
    if (error.message && error.message.includes('验证码')) {
      needCaptcha.value = true
      generateCaptcha()
      errors.captcha = '验证码错误，请重新输入'
    } else if (error.message && error.message.includes('用户名')) {
      errors.username = '用户名不存在'
    } else if (error.message && error.message.includes('密码')) {
      errors.password = '密码错误'
      needCaptcha.value = true
      generateCaptcha()
    } else {
      uni.showToast({
        title: error.message || '登录失败，请重试',
        icon: 'error'
      })
    }
  } finally {
    isLoading.value = false
  }
}

const wechatLogin = () => {
  uni.showToast({
    title: '微信登录功能开发中',
    icon: 'none'
  })
}

const phoneLogin = () => {
  uni.showToast({
    title: '手机登录功能开发中',
    icon: 'none'
  })
}

const goRegister = () => {
  uni.navigateTo({
    url: '/pages/user/register'
  })
}

const forgotPassword = () => {
  showForgotModal.value = true
}

const closeForgotModal = () => {
  showForgotModal.value = false
  forgotForm.phone = ''
  forgotForm.smsCode = ''
  forgotForm.newPassword = ''
}

const sendSmsCode = () => {
  if (!forgotForm.phone) {
    uni.showToast({
      title: '请输入手机号',
      icon: 'error'
    })
    return
  }
  
  // 开始倒计时
  smsCountdown.value = 60
  smsTimer.value = setInterval(() => {
    smsCountdown.value--
    if (smsCountdown.value <= 0) {
      clearInterval(smsTimer.value)
    }
  }, 1000)
  
  uni.showToast({
    title: '验证码已发送',
    icon: 'success'
  })
}

const resetPassword = async () => {
  if (!forgotForm.phone || !forgotForm.smsCode || !forgotForm.newPassword) {
    uni.showToast({
      title: '请填写完整信息',
      icon: 'error'
    })
    return
  }
  
  try {
    // 模拟重置密码API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    uni.showToast({
      title: '密码重置成功',
      icon: 'success'
    })
    
    closeForgotModal()
  } catch (err) {
    console.error('密码重置失败:', err)
    uni.showToast({
      title: '重置失败，请重试',
      icon: 'error'
    })
  }
}

const loadRememberedUser = () => {
  const remembered = uni.getStorageSync('rememberedUser')
  if (remembered && typeof remembered === 'object' && remembered !== null) {
    loginForm.username = remembered.username || ''
    loginForm.password = remembered.password || ''
    loginForm.remember = true
  }
}

// 生命周期
onMounted(async () => {
  // 检查是否已登录
  if (isLoggedIn.value) {
    uni.switchTab({
      url: '/pages/index/index'
    })
    return
  }
  
  // 检查登录状态
  try {
    await store.dispatch('user/checkLoginStatus')
    if (isLoggedIn.value) {
      uni.switchTab({
        url: '/pages/index/index'
      })
      return
    }
  } catch (err) {
    console.log('未登录或token已过期:', err.message || err)
  }
  
  loadRememberedUser()
  await generateCaptcha()
  needCaptcha.value = true
})

// 页面卸载时清理定时器
onUnload(() => {
  if (smsTimer.value) {
    clearInterval(smsTimer.value)
    smsTimer.value = null
  }
  if (navigationTimer.value) {
    clearTimeout(navigationTimer.value)
    navigationTimer.value = null
  }
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.circle-1 {
  width: 300rpx;
  height: 300rpx;
  top: -150rpx;
  right: -150rpx;
}

.circle-2 {
  width: 200rpx;
  height: 200rpx;
  bottom: 200rpx;
  left: -100rpx;
}

.circle-3 {
  width: 150rpx;
  height: 150rpx;
  top: 300rpx;
  left: 50rpx;
}

/* 登录表单 */
.login-form {
  position: relative;
  z-index: 1;
  padding: 100rpx 40rpx 40rpx;
}

/* 头部区域 */
.header-section {
  text-align: center;
  margin-bottom: 80rpx;
}

.logo {
  margin-bottom: 30rpx;
}

.logo-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
}

.app-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 10rpx;
}

.app-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 表单区域 */
.form-section {
  background: white;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}

.input-group {
  margin-bottom: 40rpx;
}

.input-label {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.label-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.label-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 90rpx;
  padding: 0 30rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #f8f8f8;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #667eea;
  background: white;
}

.form-input.error {
  border-color: #ff3b30;
}

.error-text {
  font-size: 24rpx;
  color: #ff3b30;
  margin-top: 10rpx;
}

/* 密码输入 */
.password-input {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #999;
}

/* 验证码输入 */
.captcha-input {
  display: flex;
  gap: 20rpx;
}

.captcha-field {
  flex: 1;
}

.captcha-image {
  width: 200rpx;
  height: 90rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
  position: relative;
  overflow: hidden;
}

.captcha-img {
  width: 100%;
  height: 100%;
}

.refresh-text {
  position: absolute;
  bottom: 5rpx;
  right: 10rpx;
  font-size: 20rpx;
  color: #666;
  background: rgba(255, 255, 255, 0.7);
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
}

/* 表单选项 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 50rpx;
}

.remember-checkbox {
  display: flex;
  align-items: center;
}

.checkbox {
  margin-right: 15rpx;
}

.checkbox-text {
  font-size: 26rpx;
  color: #666;
}

.forgot-password {
  font-size: 26rpx;
  color: #667eea;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 90rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 40rpx;
}

.login-btn.loading {
  opacity: 0.7;
}

/* 其他登录方式 */
.other-login {
  margin-bottom: 40rpx;
}

.divider {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.divider-line {
  flex: 1;
  height: 1rpx;
  background: #e0e0e0;
}

.divider-text {
  padding: 0 30rpx;
  font-size: 24rpx;
  color: #999;
}

.social-login {
  display: flex;
  gap: 30rpx;
}

.social-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  border: 2rpx solid #e0e0e0;
}

.social-btn.wechat {
  background: #07c160;
  color: white;
  border-color: #07c160;
}

.social-btn.phone {
  background: #ff9500;
  color: white;
  border-color: #ff9500;
}

.social-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

.social-text {
  font-size: 26rpx;
}

/* 注册链接 */
.register-link {
  text-align: center;
}

.register-text {
  font-size: 26rpx;
  color: #666;
}

.register-btn {
  font-size: 26rpx;
  color: #667eea;
  margin-left: 10rpx;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 600rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
}

.modal-body {
  padding: 40rpx;
}

.sms-input {
  display: flex;
  gap: 20rpx;
}

.sms-field {
  flex: 1;
}

.sms-btn {
  width: 200rpx;
  height: 90rpx;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 24rpx;
}

.sms-btn:disabled {
  background: #ccc;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  height: 100rpx;
  border: none;
  font-size: 28rpx;
}

.modal-btn.cancel {
  background: #f0f0f0;
  color: #333;
}

.modal-btn.confirm {
  background: #667eea;
  color: white;
}
</style>