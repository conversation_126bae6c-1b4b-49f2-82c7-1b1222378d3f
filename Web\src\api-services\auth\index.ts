import { service as request } from '/@/utils/request';

/**
 * 认证相关API接口集合
 * @method login 用户登录
 * @method loginByPhone 手机号登录
 * @method logout 用户退出登录
 * @method refreshToken 刷新Token
 * @method getCaptcha 获取验证码
 * @method getUserInfo 获取用户信息
 * @method unlockScreen 解锁屏幕
 * @method userRegistration 用户注册
 */
export function useAuthApi() {
	return {
		// 用户登录
		login: (data: any) => {
			return request({
				url: '/api/sysAuth/login',
				method: 'post',
				data,
			});
		},
		// 手机号登录
		loginByPhone: (data: any) => {
			return request({
				url: '/api/sysAuth/loginPhone',
				method: 'post',
				data,
			});
		},
		// 用户退出登录
		logout: () => {
			return request({
				url: '/api/sysAuth/logout',
				method: 'post',
			});
		},
		// 刷新Token
		refreshToken: (accessToken?: string) => {
			return request({
				url: '/api/sysAuth/refreshToken',
				method: 'get',
				params: { accessToken },
			});
		},
		// 获取验证码
		getCaptcha: () => {
			return request({
				url: '/api/sysAuth/captcha',
				method: 'get',
			});
		},
		// 获取用户信息
		getUserInfo: () => {
			return request({
				url: '/api/sysAuth/userInfo',
				method: 'get',
			});
		},
		// 解锁屏幕
		unlockScreen: (password: string) => {
			return request({
				url: '/api/sysAuth/unLockScreen',
				method: 'post',
				data: { password },
			});
		},
		// 用户注册
		userRegistration: (data: any) => {
			return request({
				url: '/api/sysAuth/userRegistration',
				method: 'post',
				data,
			});
		},
	};
}