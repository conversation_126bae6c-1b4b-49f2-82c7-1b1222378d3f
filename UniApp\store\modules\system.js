import { SYSTEM_TYPES, THEME_TYPE, LANGUAGE_TYPE, NETWORK_STATUS } from '../types'
import { storage } from '../../utils'

// 初始状态
const state = {
  // 主题设置
  theme: storage.get('theme') || THEME_TYPE.LIGHT,
  // 语言设置
  language: storage.get('language') || LANGUAGE_TYPE.ZH_CN,
  // 网络状态
  networkStatus: NETWORK_STATUS.UNKNOWN,
  // 系统信息
  systemInfo: {
    platform: '',
    system: '',
    version: '',
    model: '',
    brand: '',
    screenWidth: 0,
    screenHeight: 0,
    windowWidth: 0,
    windowHeight: 0,
    statusBarHeight: 0,
    navigationBarHeight: 0,
    safeArea: {},
    safeAreaInsets: {}
  },
  // 应用设置
  appSettings: storage.get('appSettings') || {
    // 通知设置
    notifications: {
      enabled: true,
      deviceAlert: true,
      energyAlert: true,
      faultAlert: true,
      systemNotice: true
    },
    // 显示设置
    display: {
      autoRefresh: true,
      refreshInterval: 30, // 秒
      showAnimation: true,
      compactMode: false
    },
    // 数据设置
    data: {
      cacheEnabled: true,
      cacheExpiry: 300, // 秒
      autoSync: true,
      syncInterval: 60 // 秒
    },
    // 安全设置
    security: {
      autoLock: false,
      lockTimeout: 300, // 秒
      biometricAuth: false,
      gestureAuth: false
    }
  },
  // 加载状态
  loading: false,
  // 错误信息
  error: null,
  // 应用版本信息
  appVersion: {
    version: '1.0.0',
    buildNumber: '1',
    updateTime: '',
    hasUpdate: false,
    updateInfo: null
  },
  // 页面栈信息
  pageStack: [],
  // 当前页面信息
  currentPage: {
    route: '',
    title: '',
    params: {},
    timestamp: null
  },
  // 全局状态
  globalState: {
    isFirstLaunch: storage.get('isFirstLaunch') !== false,
    hasShownGuide: storage.get('hasShownGuide') || false,
    lastActiveTime: storage.get('lastActiveTime') || null,
    sessionId: null,
    deviceId: storage.get('deviceId') || null
  }
}

// Getters
const getters = {
  // 是否为暗色主题
  isDarkTheme: (state) => {
    return state.theme === THEME_TYPE.DARK
  },
  
  // 是否为中文
  isChineseLanguage: (state) => {
    return state.language === LANGUAGE_TYPE.ZH_CN
  },
  
  // 网络是否连接
  isNetworkConnected: (state) => {
    return state.networkStatus === NETWORK_STATUS.WIFI || 
           state.networkStatus === NETWORK_STATUS.CELLULAR
  },
  
  // 是否为移动网络
  isCellularNetwork: (state) => {
    return state.networkStatus === NETWORK_STATUS.CELLULAR
  },
  
  // 是否为WiFi网络
  isWifiNetwork: (state) => {
    return state.networkStatus === NETWORK_STATUS.WIFI
  },
  
  // 获取状态栏高度
  statusBarHeight: (state) => {
    return state.systemInfo.statusBarHeight || 0
  },
  
  // 获取导航栏高度
  navigationBarHeight: (state) => {
    return state.systemInfo.navigationBarHeight || 44
  },
  
  // 获取安全区域
  safeAreaInsets: (state) => {
    return state.systemInfo.safeAreaInsets || { top: 0, right: 0, bottom: 0, left: 0 }
  },
  
  // 是否为首次启动
  isFirstLaunch: (state) => {
    return state.globalState.isFirstLaunch
  },
  
  // 是否已显示引导
  hasShownGuide: (state) => {
    return state.globalState.hasShownGuide
  },
  
  // 通知是否启用
  isNotificationEnabled: (state) => {
    return state.appSettings.notifications.enabled
  },
  
  // 自动刷新是否启用
  isAutoRefreshEnabled: (state) => {
    return state.appSettings.display.autoRefresh
  },
  
  // 缓存是否启用
  isCacheEnabled: (state) => {
    return state.appSettings.data.cacheEnabled
  },
  
  // 自动同步是否启用
  isAutoSyncEnabled: (state) => {
    return state.appSettings.data.autoSync
  },
  
  // 是否有应用更新
  hasAppUpdate: (state) => {
    return state.appVersion.hasUpdate
  },
  
  // 获取当前页面路由
  currentRoute: (state) => {
    return state.currentPage.route
  },
  
  // 获取页面栈深度
  pageStackDepth: (state) => {
    return state.pageStack.length
  }
}

// Mutations
const mutations = {
  // 设置主题
  [SYSTEM_TYPES.SET_THEME](state, theme) {
    state.theme = theme
    storage.set('theme', theme)
  },
  
  // 设置语言
  [SYSTEM_TYPES.SET_LANGUAGE](state, language) {
    state.language = language
    storage.set('language', language)
  },
  
  // 设置网络状态
  [SYSTEM_TYPES.SET_NETWORK_STATUS](state, status) {
    state.networkStatus = status
  },
  
  // 设置系统信息
  [SYSTEM_TYPES.SET_SYSTEM_INFO](state, systemInfo) {
    state.systemInfo = { ...state.systemInfo, ...systemInfo }
  },
  
  // 设置应用设置
  [SYSTEM_TYPES.SET_APP_SETTINGS](state, settings) {
    state.appSettings = { ...state.appSettings, ...settings }
    storage.set('appSettings', state.appSettings)
  },
  
  // 更新通知设置
  [SYSTEM_TYPES.UPDATE_NOTIFICATION_SETTINGS](state, notifications) {
    state.appSettings.notifications = { ...state.appSettings.notifications, ...notifications }
    storage.set('appSettings', state.appSettings)
  },
  
  // 更新显示设置
  [SYSTEM_TYPES.UPDATE_DISPLAY_SETTINGS](state, display) {
    state.appSettings.display = { ...state.appSettings.display, ...display }
    storage.set('appSettings', state.appSettings)
  },
  
  // 更新数据设置
  [SYSTEM_TYPES.UPDATE_DATA_SETTINGS](state, data) {
    state.appSettings.data = { ...state.appSettings.data, ...data }
    storage.set('appSettings', state.appSettings)
  },
  
  // 更新安全设置
  [SYSTEM_TYPES.UPDATE_SECURITY_SETTINGS](state, security) {
    state.appSettings.security = { ...state.appSettings.security, ...security }
    storage.set('appSettings', state.appSettings)
  },
  
  // 设置加载状态
  [SYSTEM_TYPES.SET_LOADING](state, loading) {
    state.loading = loading
  },
  
  // 设置错误信息
  [SYSTEM_TYPES.SET_ERROR](state, error) {
    state.error = error
  },
  
  // 设置应用版本信息
  [SYSTEM_TYPES.SET_APP_VERSION](state, versionInfo) {
    state.appVersion = { ...state.appVersion, ...versionInfo }
  },
  
  // 推入页面栈
  [SYSTEM_TYPES.PUSH_PAGE](state, pageInfo) {
    state.pageStack.push(pageInfo)
    state.currentPage = pageInfo
  },
  
  // 弹出页面栈
  [SYSTEM_TYPES.POP_PAGE](state) {
    if (state.pageStack.length > 0) {
      state.pageStack.pop()
      state.currentPage = state.pageStack[state.pageStack.length - 1] || {
        route: '',
        title: '',
        params: {},
        timestamp: null
      }
    }
  },
  
  // 清空页面栈
  [SYSTEM_TYPES.CLEAR_PAGE_STACK](state) {
    state.pageStack = []
    state.currentPage = {
      route: '',
      title: '',
      params: {},
      timestamp: null
    }
  },
  
  // 设置当前页面
  [SYSTEM_TYPES.SET_CURRENT_PAGE](state, pageInfo) {
    state.currentPage = pageInfo
  },
  
  // 设置首次启动标记
  [SYSTEM_TYPES.SET_FIRST_LAUNCH](state, isFirst) {
    state.globalState.isFirstLaunch = isFirst
    storage.set('isFirstLaunch', isFirst)
  },
  
  // 设置引导显示标记
  [SYSTEM_TYPES.SET_GUIDE_SHOWN](state, hasShown) {
    state.globalState.hasShownGuide = hasShown
    storage.set('hasShownGuide', hasShown)
  },
  
  // 更新最后活跃时间
  [SYSTEM_TYPES.UPDATE_LAST_ACTIVE_TIME](state) {
    const now = new Date().toISOString()
    state.globalState.lastActiveTime = now
    storage.set('lastActiveTime', now)
  },
  
  // 设置会话ID
  [SYSTEM_TYPES.SET_SESSION_ID](state, sessionId) {
    state.globalState.sessionId = sessionId
  },
  
  // 设置设备ID
  [SYSTEM_TYPES.SET_DEVICE_ID](state, deviceId) {
    state.globalState.deviceId = deviceId
    storage.set('deviceId', deviceId)
  }
}

// Actions
const actions = {
  // 初始化系统信息
  async [SYSTEM_TYPES.INIT_SYSTEM]({ commit, dispatch }) {
    try {
      commit(SYSTEM_TYPES.SET_LOADING, true)
      
      // 获取系统信息
      const systemInfo = uni.getSystemInfoSync()
      commit(SYSTEM_TYPES.SET_SYSTEM_INFO, systemInfo)
      
      // 获取网络状态
      await dispatch(SYSTEM_TYPES.GET_NETWORK_STATUS)
      
      // 生成会话ID
      const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      commit(SYSTEM_TYPES.SET_SESSION_ID, sessionId)
      
      // 生成设备ID（如果不存在）
      if (!state.globalState.deviceId) {
        const deviceId = `device_${systemInfo.platform}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        commit(SYSTEM_TYPES.SET_DEVICE_ID, deviceId)
      }
      
      // 更新最后活跃时间
      commit(SYSTEM_TYPES.UPDATE_LAST_ACTIVE_TIME)
      
      return systemInfo
    } catch (error) {
      commit(SYSTEM_TYPES.SET_ERROR, error.message)
      throw error
    } finally {
      commit(SYSTEM_TYPES.SET_LOADING, false)
    }
  },
  
  // 获取网络状态
  async [SYSTEM_TYPES.GET_NETWORK_STATUS]({ commit }) {
    try {
      const networkInfo = await new Promise((resolve, reject) => {
        uni.getNetworkType({
          success: resolve,
          fail: reject
        })
      })
      
      let status = NETWORK_STATUS.UNKNOWN
      switch (networkInfo.networkType) {
        case 'wifi':
          status = NETWORK_STATUS.WIFI
          break
        case '2g':
        case '3g':
        case '4g':
        case '5g':
          status = NETWORK_STATUS.CELLULAR
          break
        case 'none':
          status = NETWORK_STATUS.NONE
          break
        default:
          status = NETWORK_STATUS.UNKNOWN
      }
      
      commit(SYSTEM_TYPES.SET_NETWORK_STATUS, status)
      return status
    } catch (error) {
      commit(SYSTEM_TYPES.SET_NETWORK_STATUS, NETWORK_STATUS.UNKNOWN)
      throw error
    }
  },
  
  // 切换主题
  [SYSTEM_TYPES.TOGGLE_THEME]({ commit, state }) {
    const newTheme = state.theme === THEME_TYPE.LIGHT ? THEME_TYPE.DARK : THEME_TYPE.LIGHT
    commit(SYSTEM_TYPES.SET_THEME, newTheme)
    
    uni.showToast({
      title: `已切换到${newTheme === THEME_TYPE.DARK ? '暗色' : '亮色'}主题`,
      icon: 'none'
    })
  },
  
  // 切换语言
  [SYSTEM_TYPES.SWITCH_LANGUAGE]({ commit }, language) {
    commit(SYSTEM_TYPES.SET_LANGUAGE, language)
    
    uni.showToast({
      title: `语言已切换为${language === LANGUAGE_TYPE.ZH_CN ? '中文' : 'English'}`,
      icon: 'none'
    })
  },
  
  // 更新应用设置
  [SYSTEM_TYPES.UPDATE_SETTINGS]({ commit }, { type, settings }) {
    switch (type) {
      case 'notifications':
        commit(SYSTEM_TYPES.UPDATE_NOTIFICATION_SETTINGS, settings)
        break
      case 'display':
        commit(SYSTEM_TYPES.UPDATE_DISPLAY_SETTINGS, settings)
        break
      case 'data':
        commit(SYSTEM_TYPES.UPDATE_DATA_SETTINGS, settings)
        break
      case 'security':
        commit(SYSTEM_TYPES.UPDATE_SECURITY_SETTINGS, settings)
        break
      default:
        commit(SYSTEM_TYPES.SET_APP_SETTINGS, settings)
    }
    
    uni.showToast({
      title: '设置已保存',
      icon: 'success'
    })
  },
  
  // 检查应用更新
  async [SYSTEM_TYPES.CHECK_APP_UPDATE]({ commit }) {
    try {
      // 这里应该调用实际的更新检查API
      // const response = await updateApi.checkUpdate()
      
      // 模拟检查更新
      const hasUpdate = false // response.data.hasUpdate
      const updateInfo = null // response.data.updateInfo
      
      commit(SYSTEM_TYPES.SET_APP_VERSION, {
        hasUpdate,
        updateInfo
      })
      
      if (hasUpdate) {
        uni.showModal({
          title: '发现新版本',
          content: updateInfo?.description || '有新版本可用，是否立即更新？',
          confirmText: '立即更新',
          cancelText: '稍后提醒',
          success: (res) => {
            if (res.confirm) {
              // 执行更新逻辑
              // dispatch(SYSTEM_TYPES.DOWNLOAD_UPDATE)
            }
          }
        })
      }
      
      return { hasUpdate, updateInfo }
    } catch (error) {
      commit(SYSTEM_TYPES.SET_ERROR, error.message)
      throw error
    }
  },
  
  // 页面导航
  [SYSTEM_TYPES.NAVIGATE_TO]({ commit }, { route, title, params = {} }) {
    const pageInfo = {
      route,
      title,
      params,
      timestamp: new Date().toISOString()
    }
    
    commit(SYSTEM_TYPES.PUSH_PAGE, pageInfo)
    
    uni.navigateTo({
      url: route,
      success: () => {
        console.log('导航成功:', route)
      },
      fail: (error) => {
        console.error('导航失败:', error)
        commit(SYSTEM_TYPES.POP_PAGE)
      }
    })
  },
  
  // 页面返回
  [SYSTEM_TYPES.NAVIGATE_BACK]({ commit }, delta = 1) {
    for (let i = 0; i < delta; i++) {
      commit(SYSTEM_TYPES.POP_PAGE)
    }
    
    uni.navigateBack({
      delta,
      success: () => {
        console.log('返回成功')
      },
      fail: (error) => {
        console.error('返回失败:', error)
      }
    })
  },
  
  // 重定向页面
  [SYSTEM_TYPES.REDIRECT_TO]({ commit }, { route, title, params = {} }) {
    const pageInfo = {
      route,
      title,
      params,
      timestamp: new Date().toISOString()
    }
    
    // 替换当前页面
    commit(SYSTEM_TYPES.SET_CURRENT_PAGE, pageInfo)
    
    uni.redirectTo({
      url: route,
      success: () => {
        console.log('重定向成功:', route)
      },
      fail: (error) => {
        console.error('重定向失败:', error)
      }
    })
  },
  
  // 重新启动应用
  [SYSTEM_TYPES.RELAUNCH_APP]({ commit }, { route = '/pages/index/index' } = {}) {
    commit(SYSTEM_TYPES.CLEAR_PAGE_STACK)
    
    uni.reLaunch({
      url: route,
      success: () => {
        console.log('应用重启成功')
      },
      fail: (error) => {
        console.error('应用重启失败:', error)
      }
    })
  },
  
  // 完成引导
  [SYSTEM_TYPES.COMPLETE_GUIDE]({ commit }) {
    commit(SYSTEM_TYPES.SET_FIRST_LAUNCH, false)
    commit(SYSTEM_TYPES.SET_GUIDE_SHOWN, true)
  },
  
  // 重置应用数据
  [SYSTEM_TYPES.RESET_APP_DATA]({ commit }) {
    // 清除所有本地存储
    storage.clear()
    
    // 重置状态
    commit(SYSTEM_TYPES.SET_THEME, THEME_TYPE.LIGHT)
    commit(SYSTEM_TYPES.SET_LANGUAGE, LANGUAGE_TYPE.ZH_CN)
    commit(SYSTEM_TYPES.SET_APP_SETTINGS, {
      notifications: {
        enabled: true,
        deviceAlert: true,
        energyAlert: true,
        faultAlert: true,
        systemNotice: true
      },
      display: {
        autoRefresh: true,
        refreshInterval: 30,
        showAnimation: true,
        compactMode: false
      },
      data: {
        cacheEnabled: true,
        cacheExpiry: 300,
        autoSync: true,
        syncInterval: 60
      },
      security: {
        autoLock: false,
        lockTimeout: 300,
        biometricAuth: false,
        gestureAuth: false
      }
    })
    commit(SYSTEM_TYPES.SET_FIRST_LAUNCH, true)
    commit(SYSTEM_TYPES.SET_GUIDE_SHOWN, false)
    commit(SYSTEM_TYPES.CLEAR_PAGE_STACK)
    
    uni.showToast({
      title: '应用数据已重置',
      icon: 'success'
    })
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}