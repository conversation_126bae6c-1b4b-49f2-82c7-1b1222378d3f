@font-face {
  font-family: 'iconfont';
  src: url('./iconfont.woff2') format('woff2'),
       url('./iconfont.woff') format('woff'),
       url('./iconfont.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

.iconfont {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 底部导航图标 */
.icon-home:before { content: "\e001"; }
.icon-device:before { content: "\e002"; }
.icon-lighting:before { content: "\e003"; }
.icon-energy:before { content: "\e004"; }
.icon-user:before { content: "\e005"; }

/* 功能操作图标 */
.icon-add:before { content: "\e101"; }
.icon-edit:before { content: "\e102"; }
.icon-delete:before { content: "\e103"; }
.icon-search:before { content: "\e104"; }
.icon-filter:before { content: "\e105"; }
.icon-settings:before { content: "\e106"; }

/* 设备类型图标 */
.icon-led:before { content: "\e201"; }
.icon-sensor:before { content: "\e202"; }
.icon-controller:before { content: "\e203"; }

/* 状态指示图标 */
.icon-online:before { content: "\e301"; }
.icon-offline:before { content: "\e302"; }
.icon-warning:before { content: "\e303"; }

/* 常用系统图标 */
.icon-arrow-left:before { content: "\e401"; }
.icon-arrow-right:before { content: "\e402"; }
.icon-arrow-up:before { content: "\e403"; }
.icon-arrow-down:before { content: "\e404"; }
.icon-close:before { content: "\e405"; }
.icon-check:before { content: "\e406"; }
.icon-info:before { content: "\e407"; }
.icon-refresh:before { content: "\e408"; }
.icon-more:before { content: "\e409"; }
.icon-menu:before { content: "\e410"; }

/* 图标尺寸类 */
.icon-xs { font-size: 12px; }
.icon-sm { font-size: 14px; }
.icon-md { font-size: 16px; }
.icon-lg { font-size: 18px; }
.icon-xl { font-size: 20px; }
.icon-xxl { font-size: 24px; }

/* 图标颜色类 */
.icon-primary { color: #007aff; }
.icon-success { color: #4cd964; }
.icon-warning { color: #f0ad4e; }
.icon-danger { color: #dd524d; }
.icon-info { color: #909399; }
.icon-white { color: #ffffff; }
.icon-black { color: #000000; }
.icon-gray { color: #c0c4cc; }