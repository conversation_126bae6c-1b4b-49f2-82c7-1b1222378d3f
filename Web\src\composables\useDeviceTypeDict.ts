/**
 * 设备类型字典数据管理组合函数
 * 用于统一管理设备类型字典数据的获取和缓存
 */
import { ref, onMounted } from 'vue'
import { getAPI } from '/@/utils/axios-utils'
import { SysDictDataApi } from '/@/api-services/api'
import { ElMessage } from 'element-plus'

/**
 * 设备类型字典项接口定义
 */
export interface DeviceTypeDictItem {
  /** 字典值 */
  value: string
  /** 字典标签 */
  label: string
  /** 字典名称 */
  name: string
  /** 排序 */
  sort: number
  /** 状态 */
  status: number
  /** 备注 */
  remark?: string
}

/**
 * 设备类型字典数据管理组合函数
 * @returns 返回设备类型字典相关的响应式数据和方法
 */
export function useDeviceTypeDict() {
  /** 设备类型字典数据列表 */
  const deviceTypeOptions = ref<DeviceTypeDictItem[]>([])
  
  /** 数据加载状态 */
  const loading = ref(false)
  
  /** 是否已加载数据 */
  const loaded = ref(false)

  /**
   * 获取设备类型字典数据
   * 从sysdicttype表中查询code为'deviceType'的记录，联动查询sysdictdata表中对应数据
   */
  const fetchDeviceTypeDict = async () => {
    if (loaded.value) return // 避免重复加载
    
    loading.value = true
    try {
      // 根据字典类型编码获取字典值集合
      const dictDataApi = getAPI(SysDictDataApi)
      const response = await dictDataApi.apiSysDictDataDataListCodeGet('deviceType')
      
      if (response.data.code === 200 && response.data.result) {
        // 转换数据格式，确保包含所需字段
        deviceTypeOptions.value = response.data.result.map((item: any) => ({
          value: item.value || item.code, // 字典值
          label: item.label || item.name, // 字典标签
          name: item.name || item.label,  // 字典名称
          sort: item.sort || 0,           // 排序
          status: item.status || 1,       // 状态
          remark: item.remark || ''       // 备注
        }))
        
        // 按排序字段排序
        deviceTypeOptions.value.sort((a, b) => a.sort - b.sort)
        loaded.value = true
      } else {
        console.warn('获取设备类型字典数据失败:', response.data.message)
        ElMessage.warning('获取设备类型选项失败，请检查字典配置')
      }
    } catch (error) {
      console.error('获取设备类型字典数据异常:', error)
      ElMessage.error('获取设备类型选项异常，请稍后重试')
      
      // 发生错误时使用默认数据作为备选方案
      // 注意：这里的value值应该与后端返回的deviceType值保持一致
      deviceTypeOptions.value = [
        { value: '1', label: 'LED灯', name: 'LED灯', sort: 1, status: 1 },
        { value: '2', label: '网关', name: '网关', sort: 2, status: 1 },
        { value: '3', label: '荧光灯', name: '荧光灯', sort: 3, status: 1 },
        { value: '4', label: '卤素灯', name: '卤素灯', sort: 4, status: 1 },
        { value: '智能灯', label: '智能灯', name: '智能灯', sort: 5, status: 1 }
      ]
    } finally {
      loading.value = false
    }
  }

  /**
   * 根据设备类型值获取对应的标签
   * @param value 设备类型值
   * @returns 对应的标签，如果未找到则返回原值
   */
  const getDeviceTypeLabel = (value: string): string => {
    const item = deviceTypeOptions.value.find(option => option.value === value)
    return item ? item.label : value
  }

  /**
   * 根据设备类型值获取对应的字典项
   * @param value 设备类型值
   * @returns 对应的字典项，如果未找到则返回null
   */
  const getDeviceTypeItem = (value: string): DeviceTypeDictItem | null => {
    return deviceTypeOptions.value.find(option => option.value === value) || null
  }

  /**
   * 刷新字典数据
   * 强制重新加载字典数据
   */
  const refreshDeviceTypeDict = async () => {
    loaded.value = false
    await fetchDeviceTypeDict()
  }

  // 组件挂载时自动加载数据
  onMounted(() => {
    fetchDeviceTypeDict()
  })

  return {
    /** 设备类型字典选项列表 */
    deviceTypeOptions,
    /** 数据加载状态 */
    loading,
    /** 是否已加载数据 */
    loaded,
    /** 获取设备类型字典数据 */
    fetchDeviceTypeDict,
    /** 根据值获取标签 */
    getDeviceTypeLabel,
    /** 根据值获取字典项 */
    getDeviceTypeItem,
    /** 刷新字典数据 */
    refreshDeviceTypeDict
  }
}