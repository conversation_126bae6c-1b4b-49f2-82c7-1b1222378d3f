using System;
using System.Threading;
using System.Threading.Tasks;

namespace Admin.NET.Plugin.MQTT.Interfaces
{
    /// <summary>
    /// MQTT异常处理器接口
    /// 定义统一的异常处理、重试机制和错误恢复策略
    /// </summary>
    public interface IMqttExceptionHandler
    {
        /// <summary>
        /// 异常发生事件
        /// </summary>
        event EventHandler<ExceptionOccurredEventArgs> ExceptionOccurred;
        
        /// <summary>
        /// 错误恢复事件
        /// </summary>
        event EventHandler<ErrorRecoveredEventArgs> ErrorRecovered;
        
        /// <summary>
        /// 处理异常
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <param name="context">异常上下文</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异常处理结果</returns>
        Task<ExceptionHandlingResult> HandleExceptionAsync(Exception exception, ExceptionContext context, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 执行带重试的操作
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="operation">要执行的操作</param>
        /// <param name="retryPolicy">重试策略</param>
        /// <param name="context">操作上下文</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>操作结果</returns>
        Task<T> ExecuteWithRetryAsync<T>(Func<CancellationToken, Task<T>> operation, RetryPolicy retryPolicy, OperationContext context, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 执行带重试的操作（无返回值）
        /// </summary>
        /// <param name="operation">要执行的操作</param>
        /// <param name="retryPolicy">重试策略</param>
        /// <param name="context">操作上下文</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task ExecuteWithRetryAsync(Func<CancellationToken, Task> operation, RetryPolicy retryPolicy, OperationContext context, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 注册异常处理策略
        /// </summary>
        /// <param name="exceptionType">异常类型</param>
        /// <param name="strategy">处理策略</param>
        void RegisterExceptionStrategy(Type exceptionType, IExceptionHandlingStrategy strategy);
        
        /// <summary>
        /// 移除异常处理策略
        /// </summary>
        /// <param name="exceptionType">异常类型</param>
        /// <returns>是否成功移除</returns>
        bool RemoveExceptionStrategy(Type exceptionType);
        
        /// <summary>
        /// 获取异常统计信息
        /// </summary>
        /// <returns>异常统计信息</returns>
        ExceptionStatistics GetExceptionStatistics();
        
        /// <summary>
        /// 重置异常统计
        /// </summary>
        void ResetStatistics();
    }
    
    /// <summary>
    /// 异常处理策略接口
    /// </summary>
    public interface IExceptionHandlingStrategy
    {
        /// <summary>
        /// 策略名称
        /// </summary>
        string Name { get; }
        
        /// <summary>
        /// 是否可以处理指定异常
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <param name="context">异常上下文</param>
        /// <returns>是否可以处理</returns>
        bool CanHandle(Exception exception, ExceptionContext context);
        
        /// <summary>
        /// 处理异常
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <param name="context">异常上下文</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>处理结果</returns>
        Task<ExceptionHandlingResult> HandleAsync(Exception exception, ExceptionContext context, CancellationToken cancellationToken = default);
    }
    
    /// <summary>
    /// 异常发生事件参数
    /// </summary>
    public class ExceptionOccurredEventArgs : EventArgs
    {
        /// <summary>
        /// 异常对象
        /// </summary>
        public Exception Exception { get; set; }
        
        /// <summary>
        /// 异常上下文
        /// </summary>
        public ExceptionContext Context { get; set; }
        
        /// <summary>
        /// 发生时间
        /// </summary>
        public DateTime OccurredAt { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// 是否已处理
        /// </summary>
        public bool IsHandled { get; set; }
    }
    
    /// <summary>
    /// 错误恢复事件参数
    /// </summary>
    public class ErrorRecoveredEventArgs : EventArgs
    {
        /// <summary>
        /// 原始异常
        /// </summary>
        public Exception OriginalException { get; set; }
        
        /// <summary>
        /// 异常上下文
        /// </summary>
        public ExceptionContext Context { get; set; }
        
        /// <summary>
        /// 恢复时间
        /// </summary>
        public DateTime RecoveredAt { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; }
        
        /// <summary>
        /// 恢复策略
        /// </summary>
        public string RecoveryStrategy { get; set; }
    }
    
    /// <summary>
    /// 异常上下文
    /// </summary>
    public class ExceptionContext
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        public string OperationName { get; set; }
        
        /// <summary>
        /// 组件名称
        /// </summary>
        public string ComponentName { get; set; }
        
        /// <summary>
        /// 相关数据
        /// </summary>
        public object Data { get; set; }
        
        /// <summary>
        /// 异常发生时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// 用户ID（如果适用）
        /// </summary>
        public string UserId { get; set; }
        
        /// <summary>
        /// 会话ID（如果适用）
        /// </summary>
        public string SessionId { get; set; }
        
        /// <summary>
        /// 请求ID（如果适用）
        /// </summary>
        public string RequestId { get; set; }
        
        /// <summary>
        /// 扩展属性
        /// </summary>
        public System.Collections.Generic.Dictionary<string, object> Properties { get; set; } = new System.Collections.Generic.Dictionary<string, object>();
    }
    
    /// <summary>
    /// 操作上下文
    /// </summary>
    public class OperationContext
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        public string OperationName { get; set; }
        
        /// <summary>
        /// 操作描述
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// 操作参数
        /// </summary>
        public object Parameters { get; set; }
        
        /// <summary>
        /// 操作开始时间
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// 扩展属性
        /// </summary>
        public System.Collections.Generic.Dictionary<string, object> Properties { get; set; } = new System.Collections.Generic.Dictionary<string, object>();
    }
    
    /// <summary>
    /// 异常处理结果
    /// </summary>
    public class ExceptionHandlingResult
    {
        /// <summary>
        /// 是否已处理
        /// </summary>
        public bool IsHandled { get; set; }
        
        /// <summary>
        /// 是否应该重试
        /// </summary>
        public bool ShouldRetry { get; set; }
        
        /// <summary>
        /// 重试延迟（毫秒）
        /// </summary>
        public int RetryDelayMs { get; set; }
        
        /// <summary>
        /// 处理策略名称
        /// </summary>
        public string StrategyName { get; set; }
        
        /// <summary>
        /// 处理消息
        /// </summary>
        public string Message { get; set; }
        
        /// <summary>
        /// 恢复操作
        /// </summary>
        public Func<CancellationToken, Task> RecoveryAction { get; set; }
        
        /// <summary>
        /// 是否需要断路器
        /// </summary>
        public bool RequiresCircuitBreaker { get; set; }
    }
    
    /// <summary>
    /// 重试策略
    /// </summary>
    public class RetryPolicy
    {
        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetryCount { get; set; } = 3;
        
        /// <summary>
        /// 重试间隔（毫秒）
        /// </summary>
        public int RetryIntervalMs { get; set; } = 1000;
        
        /// <summary>
        /// 重试间隔增长因子
        /// </summary>
        public double BackoffMultiplier { get; set; } = 2.0;
        
        /// <summary>
        /// 最大重试间隔（毫秒）
        /// </summary>
        public int MaxRetryIntervalMs { get; set; } = 30000;
        
        /// <summary>
        /// 是否启用抖动
        /// </summary>
        public bool EnableJitter { get; set; } = true;
        
        /// <summary>
        /// 重试条件
        /// </summary>
        public Func<Exception, bool> RetryCondition { get; set; }
        
        /// <summary>
        /// 重试策略类型
        /// </summary>
        public RetryPolicyType PolicyType { get; set; } = RetryPolicyType.ExponentialBackoff;
    }
    
    /// <summary>
    /// 重试策略类型
    /// </summary>
    public enum RetryPolicyType
    {
        /// <summary>
        /// 固定间隔
        /// </summary>
        FixedInterval,
        
        /// <summary>
        /// 指数退避
        /// </summary>
        ExponentialBackoff,
        
        /// <summary>
        /// 线性退避
        /// </summary>
        LinearBackoff,
        
        /// <summary>
        /// 自定义
        /// </summary>
        Custom
    }
    
    /// <summary>
    /// 异常统计信息
    /// </summary>
    public class ExceptionStatistics
    {
        /// <summary>
        /// 总异常数
        /// </summary>
        public long TotalExceptions { get; set; }
        
        /// <summary>
        /// 已处理异常数
        /// </summary>
        public long HandledException { get; set; }
        
        /// <summary>
        /// 未处理异常数
        /// </summary>
        public long UnhandledException { get; set; }
        
        /// <summary>
        /// 重试成功数
        /// </summary>
        public long RetrySuccessCount { get; set; }
        
        /// <summary>
        /// 重试失败数
        /// </summary>
        public long RetryFailureCount { get; set; }
        
        /// <summary>
        /// 按异常类型分组的统计
        /// </summary>
        public System.Collections.Generic.Dictionary<string, ExceptionTypeStatistics> ExceptionTypeStats { get; set; } = new System.Collections.Generic.Dictionary<string, ExceptionTypeStatistics>();
        
        /// <summary>
        /// 统计开始时间
        /// </summary>
        public DateTime StatisticsStartTime { get; set; }
        
        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdatedAt { get; set; }
    }
    
    /// <summary>
    /// 异常类型统计
    /// </summary>
    public class ExceptionTypeStatistics
    {
        /// <summary>
        /// 异常类型名称
        /// </summary>
        public string ExceptionType { get; set; }
        
        /// <summary>
        /// 发生次数
        /// </summary>
        public long Count { get; set; }
        
        /// <summary>
        /// 最后发生时间
        /// </summary>
        public DateTime LastOccurredAt { get; set; }
        
        /// <summary>
        /// 平均处理时间（毫秒）
        /// </summary>
        public double AverageHandlingTimeMs { get; set; }
        
        /// <summary>
        /// 成功恢复次数
        /// </summary>
        public long RecoverySuccessCount { get; set; }
        
        /// <summary>
        /// 恢复失败次数
        /// </summary>
        public long RecoveryFailureCount { get; set; }
    }
}