<template>
  <view class="virtual-scroll-container" :style="containerStyle">
    <!-- 滚动区域 -->
    <scroll-view 
      class="virtual-scroll-view"
      :scroll-y="true"
      :scroll-top="scrollTop"
      :style="scrollViewStyle"
      @scroll="onScroll"
      @scrolltoupper="onScrollToUpper"
      @scrolltolower="onScrollToLower"
    >
      <!-- 占位容器，用于撑开滚动高度 -->
      <view class="virtual-placeholder" :style="{ height: totalHeight + 'px' }">
        <!-- 可见区域容器 -->
        <view class="virtual-visible-area" :style="visibleAreaStyle">
          <!-- 渲染可见项目 -->
          <view 
            class="virtual-item"
            :class="getItemClass(item, index)"
            :style="getItemStyle(item, index)"
            v-for="(item, index) in visibleItems" 
            :key="getItemKey(item, index)"
            @tap="onItemTap(item, index)"
          >
            <!-- 使用插槽渲染项目内容 -->
            <slot 
              name="item" 
              :item="item" 
              :index="getRealIndex(index)" 
              :isVisible="true"
            >
              <!-- 默认渲染 -->
              <view class="default-item">
                <text>{{ item.name || item.title || JSON.stringify(item) }}</text>
              </view>
            </slot>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 加载更多指示器 -->
    <view v-if="showLoadMore" class="load-more-indicator">
      <view v-if="loadingMore" class="loading">
        <view class="loading-spinner"></view>
        <text class="loading-text">{{ loadMoreText }}</text>
      </view>
      <view v-else-if="noMoreData" class="no-more">
        <text class="no-more-text">{{ noMoreText }}</text>
      </view>
    </view>
    
    <!-- 回到顶部按钮 -->
    <view 
      v-if="showBackTop && scrollTop > backTopThreshold" 
      class="back-top-btn"
      :style="backTopStyle"
      @tap="scrollToTop"
    >
      <text class="back-top-icon">↑</text>
    </view>
  </view>
</template>

<script>
import { VirtualScrollManager } from '@/utils/performance'

export default {
  name: 'VirtualScroll',
  props: {
    // 数据列表
    items: {
      type: Array,
      default: () => []
    },
    // 每项高度
    itemHeight: {
      type: Number,
      default: 100
    },
    // 容器高度
    height: {
      type: [String, Number],
      default: '100%'
    },
    // 缓冲区大小（额外渲染的项目数量）
    buffer: {
      type: Number,
      default: 5
    },
    // 项目唯一键字段
    keyField: {
      type: String,
      default: 'id'
    },
    // 是否启用下拉刷新
    enableRefresh: {
      type: Boolean,
      default: false
    },
    // 是否启用加载更多
    enableLoadMore: {
      type: Boolean,
      default: false
    },
    // 是否正在加载更多
    loadingMore: {
      type: Boolean,
      default: false
    },
    // 是否没有更多数据
    noMoreData: {
      type: Boolean,
      default: false
    },
    // 加载更多文本
    loadMoreText: {
      type: String,
      default: '加载中...'
    },
    // 没有更多数据文本
    noMoreText: {
      type: String,
      default: '没有更多数据了'
    },
    // 是否显示回到顶部按钮
    showBackTop: {
      type: Boolean,
      default: true
    },
    // 回到顶部按钮显示阈值
    backTopThreshold: {
      type: Number,
      default: 500
    },
    // 滚动节流延迟
    scrollThrottle: {
      type: Number,
      default: 16
    },
    // 自定义样式类
    customClass: {
      type: String,
      default: ''
    }
  },
  
  data() {
    return {
      scrollTop: 0,
      virtualManager: null,
      visibleItems: [],
      startIndex: 0,
      endIndex: 0,
      offsetY: 0,
      totalHeight: 0,
      throttledScroll: null
    }
  },
  
  computed: {
    containerStyle() {
      return {
        height: this.formatSize(this.height),
        position: 'relative'
      }
    },
    
    scrollViewStyle() {
      return {
        height: '100%'
      }
    },
    
    visibleAreaStyle() {
      return {
        transform: `translateY(${this.offsetY}px)`,
        position: 'relative'
      }
    },
    
    showLoadMore() {
      return this.enableLoadMore && (this.loadingMore || this.noMoreData)
    },
    
    backTopStyle() {
      return {
        position: 'fixed',
        right: '30rpx',
        bottom: '100rpx',
        zIndex: 999
      }
    }
  },
  
  watch: {
    items: {
      handler(newItems) {
        this.updateData(newItems)
      },
      immediate: true
    },
    
    itemHeight() {
      this.initVirtualManager()
      this.updateData(this.items)
    }
  },
  
  mounted() {
    this.initVirtualManager()
    this.initThrottledScroll()
  },
  
  beforeUnmount() {
    this.cleanup()
  },
  
  methods: {
    /**
     * 初始化虚拟滚动管理器
     */
    initVirtualManager() {
      const containerHeight = this.getContainerHeight()
      
      this.virtualManager = new VirtualScrollManager({
        itemHeight: this.itemHeight,
        containerHeight,
        buffer: this.buffer
      })
    },
    
    /**
     * 初始化节流滚动
     */
    initThrottledScroll() {
      // 简单的节流实现，约60fps
      let lastTime = 0
      this.throttledScroll = (scrollTop) => {
        const now = Date.now()
        if (now - lastTime >= 16) {
          lastTime = now
          this.handleScroll(scrollTop)
        }
      }
    },
    
    /**
     * 获取容器高度
     * @returns {number} 容器高度
     */
    getContainerHeight() {
      if (typeof this.height === 'number') {
        return this.height
      }
      
      // 默认使用屏幕高度
      const systemInfo = uni.getSystemInfoSync()
      return systemInfo.windowHeight
    },
    
    /**
     * 更新数据
     * @param {Array} items 数据数组
     */
    updateData(items) {
      if (!this.virtualManager) return
      
      this.virtualManager.setData(items || [])
      this.updateVisibleData()
    },
    
    /**
     * 更新可见数据
     */
    updateVisibleData() {
      if (!this.virtualManager) return
      
      const result = this.virtualManager.getVisibleData()
      
      this.visibleItems = result.data
      this.startIndex = result.startIndex
      this.endIndex = result.endIndex
      this.offsetY = result.offsetY
      this.totalHeight = result.totalHeight
    },
    
    /**
     * 滚动事件处理
     * @param {Object} e 事件对象
     */
    onScroll(e) {
      const scrollTop = e.detail.scrollTop
      this.throttledScroll(scrollTop)
    },
    
    /**
     * 处理滚动
     * @param {number} scrollTop 滚动位置
     */
    handleScroll(scrollTop) {
      if (!this.virtualManager) return
      
      this.scrollTop = scrollTop
      this.virtualManager.updateScrollTop(scrollTop)
      this.updateVisibleData()
      
      // 触发滚动事件
      this.$emit('scroll', {
        scrollTop,
        startIndex: this.startIndex,
        endIndex: this.endIndex
      })
    },
    
    /**
     * 滚动到顶部
     */
    onScrollToUpper() {
      if (this.enableRefresh) {
        this.$emit('refresh')
      }
    },
    
    /**
     * 滚动到底部
     */
    onScrollToLower() {
      if (this.enableLoadMore && !this.loadingMore && !this.noMoreData) {
        this.$emit('loadMore')
      }
    },
    
    /**
     * 项目点击事件
     * @param {Object} item 项目数据
     * @param {number} index 项目索引
     */
    onItemTap(item, index) {
      this.$emit('itemTap', {
        item,
        index: this.getRealIndex(index),
        realIndex: this.startIndex + index
      })
    },
    
    /**
     * 获取项目唯一键
     * @param {Object} item 项目数据
     * @param {number} index 项目索引
     * @returns {string} 唯一键
     */
    getItemKey(item, index) {
      if (item && item[this.keyField]) {
        return String(item[this.keyField])
      }
      return `virtual-item-${this.startIndex + index}`
    },
    
    /**
     * 获取项目样式类
     * @param {Object} item 项目数据
     * @param {number} index 项目索引
     * @returns {string} 样式类字符串
     */
    getItemClass(item, index) {
      const classes = [
        'virtual-item',
        this.customClass
      ]
      
      if ((this.startIndex + index) % 2 === 0) {
        classes.push('item-even')
      } else {
        classes.push('item-odd')
      }
      
      return classes.filter(Boolean).join(' ')
    },
    
    /**
     * 获取项目样式
     * @returns {Object} 样式对象
     */
    getItemStyle() {
      return {
        height: `${this.itemHeight}px`,
        minHeight: `${this.itemHeight}px`,
        boxSizing: 'border-box'
      }
    },
    
    /**
     * 获取真实索引
     * @param {number} index 相对索引
     * @returns {number} 真实索引
     */
    getRealIndex(index) {
      return this.startIndex + index
    },
    
    /**
     * 滚动到顶部
     */
    scrollToTop() {
      this.scrollTop = 0
      this.$emit('scrollToTop')
    },
    
    /**
     * 滚动到指定位置
     * @param {number} index 项目索引
     */
    scrollToIndex(index) {
      const scrollTop = index * this.itemHeight
      this.scrollTop = scrollTop
      this.$emit('scrollToIndex', { index, scrollTop })
    },
    
    /**
     * 刷新虚拟滚动
     */
    refresh() {
      this.updateData(this.items)
    },
    
    /**
     * 格式化尺寸
     * @param {string|number} size 尺寸值
     * @returns {string} 格式化后的尺寸
     */
    formatSize(size) {
      if (typeof size === 'number') {
        return `${size}px`
      }
      return size
    },
    
    /**
     * 清理资源
     */
    cleanup() {
      this.virtualManager = null
      this.throttledScroll = null
    }
  }
}
</script>

<style lang="scss" scoped>
.virtual-scroll-container {
  position: relative;
  overflow: hidden;
}

.virtual-scroll-view {
  width: 100%;
  height: 100%;
}

.virtual-placeholder {
  position: relative;
  width: 100%;
}

.virtual-visible-area {
  width: 100%;
}

.virtual-item {
  width: 100%;
  box-sizing: border-box;
  
  &.item-even {
    background-color: #fafafa;
  }
  
  &.item-odd {
    background-color: #ffffff;
  }
}

.default-item {
  padding: 20rpx;
  display: flex;
  align-items: center;
  
  text {
    font-size: 28rpx;
    color: #333333;
  }
}

.load-more-indicator {
  padding: 20rpx;
  text-align: center;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
}

.no-more {
  padding: 20rpx;
}

.no-more-text {
  font-size: 28rpx;
  color: #999999;
}

.back-top-btn {
  width: 80rpx;
  height: 80rpx;
  background-color: #1890ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
}

.back-top-icon {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: bold;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>