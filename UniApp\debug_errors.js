/**
 * 错误调试脚本
 * 用于检查和分析应用中的错误日志
 */

// 检查本地存储中的错误日志
function checkErrorLogs() {
  try {
    const logs = uni.getStorageSync('error_logs')
    if (logs) {
      const parsedLogs = JSON.parse(logs)
      console.log('=== 错误日志分析 ===');
      console.log('总错误数量:', parsedLogs.length);
      
      // 按错误级别分组
      const levelCounts = {};
      const typeCounts = {};
      
      parsedLogs.forEach(log => {
        const level = log.error?.level || 'UNKNOWN';
        const type = log.error?.type || 'UNKNOWN';
        
        levelCounts[level] = (levelCounts[level] || 0) + 1;
        typeCounts[type] = (typeCounts[type] || 0) + 1;
      });
      
      console.log('错误级别统计:', levelCounts);
      console.log('错误类型统计:', typeCounts);
      
      // 显示最近的严重错误
      const criticalErrors = parsedLogs.filter(log => log.error?.level === 'CRITICAL');
      if (criticalErrors.length > 0) {
        console.log('=== 严重错误详情 ===');
        criticalErrors.forEach((error, index) => {
          console.log(`严重错误 ${index + 1}:`, {
            message: error.error?.message,
            timestamp: error.timestamp,
            context: error.context,
            stack: error.error?.stack
          });
        });
      }
      
      // 显示最近的错误
      console.log('=== 最近5个错误 ===');
      parsedLogs.slice(-5).forEach((error, index) => {
        console.log(`错误 ${index + 1}:`, {
          level: error.error?.level,
          type: error.error?.type,
          message: error.error?.message,
          timestamp: error.timestamp
        });
      });
      
      return parsedLogs;
    } else {
      console.log('没有找到错误日志');
      return [];
    }
  } catch (e) {
    console.error('检查错误日志失败:', e);
    return [];
  }
}

// 清除错误日志
function clearErrorLogs() {
  try {
    uni.removeStorageSync('error_logs');
    console.log('错误日志已清除');
  } catch (e) {
    console.error('清除错误日志失败:', e);
  }
}

// 模拟严重错误
function simulateCriticalError() {
  const { handleError, ERROR_LEVELS, ERROR_TYPES } = require('./utils/errorHandler.js');
  
  const criticalError = new Error('模拟的严重错误');
  criticalError.level = ERROR_LEVELS.CRITICAL;
  criticalError.type = ERROR_TYPES.SYSTEM;
  
  handleError(criticalError, { source: 'debug_script' });
}

// 导出调试函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    checkErrorLogs,
    clearErrorLogs,
    simulateCriticalError
  };
}

// 在浏览器环境中添加到全局对象
if (typeof window !== 'undefined') {
  window.debugErrors = {
    checkErrorLogs,
    clearErrorLogs,
    simulateCriticalError
  };
}

console.log('错误调试工具已加载。使用方法:');
console.log('- checkErrorLogs(): 检查错误日志');
console.log('- clearErrorLogs(): 清除错误日志');
console.log('- simulateCriticalError(): 模拟严重错误');