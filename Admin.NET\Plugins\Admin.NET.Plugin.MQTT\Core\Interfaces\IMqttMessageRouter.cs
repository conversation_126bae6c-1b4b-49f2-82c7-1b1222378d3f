using MQTTnet;
using MQTTnet.Protocol;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Admin.NET.Plugin.MQTT.Interfaces
{
    /// <summary>
    /// MQTT消息路由器接口
    /// 定义消息路由、处理器管理和性能监控的核心功能
    /// </summary>
    public interface IMqttMessageRouter : IDisposable
    {
        /// <summary>
        /// 注册消息处理器
        /// </summary>
        /// <param name="topicPattern">主题模式（支持通配符）</param>
        /// <param name="handler">消息处理器</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>处理器ID</returns>
        Task<string> RegisterHandlerAsync(string topicPattern, IMessageHandler handler, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 注册泛型消息处理器
        /// </summary>
        /// <typeparam name="T">消息类型</typeparam>
        /// <param name="topicPattern">主题模式</param>
        /// <param name="handler">泛型消息处理器</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>处理器ID</returns>
        Task<string> RegisterHandlerAsync<T>(string topicPattern, IMessageHandler<T> handler, CancellationToken cancellationToken = default) where T : class;
        
        /// <summary>
        /// 移除消息处理器
        /// </summary>
        /// <param name="handlerId">处理器ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否成功移除</returns>
        Task<bool> RemoveHandlerAsync(string handlerId, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 订阅主题
        /// </summary>
        /// <param name="topic">主题</param>
        /// <param name="qos">服务质量等级</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task SubscribeTopicAsync(string topic, MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtMostOnce, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 取消订阅主题
        /// </summary>
        /// <param name="topic">主题</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task UnsubscribeTopicAsync(string topic, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 发布消息
        /// </summary>
        /// <param name="topic">主题</param>
        /// <param name="payload">消息内容</param>
        /// <param name="qos">服务质量等级</param>
        /// <param name="retain">是否保留消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task PublishAsync(string topic, object payload, MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtMostOnce, bool retain = false, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 获取订阅统计信息
        /// </summary>
        /// <returns>订阅统计信息列表</returns>
        List<SubscriptionStats> GetSubscriptionStats();
        
        /// <summary>
        /// 获取处理器统计信息
        /// </summary>
        /// <returns>处理器统计信息列表</returns>
        List<HandlerStats> GetHandlerStats();
        
        /// <summary>
        /// 获取路由器性能统计
        /// </summary>
        /// <returns>性能统计信息</returns>
        RouterPerformanceStats GetPerformanceStats();
        
        /// <summary>
        /// 启动消息路由器
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task StartAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 停止消息路由器
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task StopAsync(CancellationToken cancellationToken = default);
    }
    
    /// <summary>
    /// 消息处理器接口
    /// </summary>
    public interface IMessageHandler
    {
        /// <summary>
        /// 处理器名称
        /// </summary>
        string Name { get; }
        
        /// <summary>
        /// 处理消息
        /// </summary>
        /// <param name="message">MQTT消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task HandleAsync(MqttApplicationMessage message, CancellationToken cancellationToken = default);
    }
    
    /// <summary>
    /// 泛型消息处理器接口
    /// </summary>
    /// <typeparam name="T">消息类型</typeparam>
    public interface IMessageHandler<T> where T : class
    {
        /// <summary>
        /// 处理器名称
        /// </summary>
        string Name { get; }
        
        /// <summary>
        /// 处理强类型消息
        /// </summary>
        /// <param name="message">强类型消息</param>
        /// <param name="originalMessage">原始MQTT消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task HandleAsync(T message, MqttApplicationMessage originalMessage, CancellationToken cancellationToken = default);
    }
    
    /// <summary>
    /// 订阅统计信息
    /// </summary>
    public class SubscriptionStats
    {
        /// <summary>
        /// 主题
        /// </summary>
        public string Topic { get; set; }
        
        /// <summary>
        /// 服务质量等级
        /// </summary>
        public MqttQualityOfServiceLevel QoS { get; set; }
        
        /// <summary>
        /// 订阅时间
        /// </summary>
        public DateTime SubscribedAt { get; set; }
        
        /// <summary>
        /// 接收消息数
        /// </summary>
        public long MessagesReceived { get; set; }
        
        /// <summary>
        /// 最后接收消息时间
        /// </summary>
        public DateTime? LastMessageAt { get; set; }
        
        /// <summary>
        /// 关联的处理器数量
        /// </summary>
        public int HandlerCount { get; set; }
    }
    
    /// <summary>
    /// 处理器统计信息
    /// </summary>
    public class HandlerStats
    {
        /// <summary>
        /// 处理器ID
        /// </summary>
        public string HandlerId { get; set; }
        
        /// <summary>
        /// 处理器名称
        /// </summary>
        public string HandlerName { get; set; }
        
        /// <summary>
        /// 主题模式
        /// </summary>
        public string TopicPattern { get; set; }
        
        /// <summary>
        /// 注册时间
        /// </summary>
        public DateTime RegisteredAt { get; set; }
        
        /// <summary>
        /// 处理消息数
        /// </summary>
        public long MessagesProcessed { get; set; }
        
        /// <summary>
        /// 处理错误数
        /// </summary>
        public long ProcessingErrors { get; set; }
        
        /// <summary>
        /// 平均处理时间（毫秒）
        /// </summary>
        public double AverageProcessingTimeMs { get; set; }
        
        /// <summary>
        /// 最后处理时间
        /// </summary>
        public DateTime? LastProcessedAt { get; set; }
    }
    
    /// <summary>
    /// 路由器性能统计
    /// </summary>
    public class RouterPerformanceStats
    {
        /// <summary>
        /// 启动时间
        /// </summary>
        public DateTime StartedAt { get; set; }
        
        /// <summary>
        /// 总处理消息数
        /// </summary>
        public long TotalMessagesProcessed { get; set; }
        
        /// <summary>
        /// 总处理错误数
        /// </summary>
        public long TotalProcessingErrors { get; set; }
        
        /// <summary>
        /// 当前队列长度
        /// </summary>
        public int CurrentQueueLength { get; set; }
        
        /// <summary>
        /// 最大队列长度
        /// </summary>
        public int MaxQueueLength { get; set; }
        
        /// <summary>
        /// 平均处理时间（毫秒）
        /// </summary>
        public double AverageProcessingTimeMs { get; set; }
        
        /// <summary>
        /// 活跃处理器数
        /// </summary>
        public int ActiveHandlers { get; set; }
        
        /// <summary>
        /// 活跃订阅数
        /// </summary>
        public int ActiveSubscriptions { get; set; }
        
        /// <summary>
        /// 内存使用量（字节）
        /// </summary>
        public long MemoryUsageBytes { get; set; }
    }
}