// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Plugin.MQTT.Core;

/// <summary>
/// MQTT插件常量定义
/// 集中管理插件中使用的所有常量值
/// </summary>
public static class MqttConstants
{
    #region 插件信息常量
    
    /// <summary>
    /// 插件名称
    /// </summary>
    public const string PluginName = "Admin.NET.Plugin.MQTT";
    
    /// <summary>
    /// 插件版本
    /// </summary>
    public const string PluginVersion = "2.0.0";
    
    /// <summary>
    /// 插件描述
    /// </summary>
    public const string PluginDescription = "Admin.NET MQTT插件 - 提供设备控制和消息路由功能";
    
    /// <summary>
    /// 插件作者
    /// </summary>
    public const string PluginAuthor = "Admin.NET Team";
    
    /// <summary>
    /// API分组名称
    /// </summary>
    public const string GroupName = "MQTT";
    
    #endregion
    
    #region MQTT协议常量
    
    /// <summary>
    /// 默认MQTT端口
    /// </summary>
    public const int DefaultPort = 1883;
    
    /// <summary>
    /// 默认MQTT SSL端口
    /// </summary>
    public const int DefaultSslPort = 8883;
    
    /// <summary>
    /// 默认WebSocket端口
    /// </summary>
    public const int DefaultWebSocketPort = 8080;
    
    /// <summary>
    /// 默认WebSocket SSL端口
    /// </summary>
    public const int DefaultWebSocketSslPort = 8443;
    
    /// <summary>
    /// 默认保持连接间隔（秒）
    /// </summary>
    public const int DefaultKeepAliveInterval = 60;
    
    /// <summary>
    /// 默认连接超时时间（秒）
    /// </summary>
    public const int DefaultConnectionTimeout = 30;
    
    /// <summary>
    /// 默认重连间隔（秒）
    /// </summary>
    public const int DefaultReconnectInterval = 5;
    
    /// <summary>
    /// 最大重连次数
    /// </summary>
    public const int MaxReconnectAttempts = 10;
    
    #endregion
    
    #region 主题常量
    
    /// <summary>
    /// 设备控制主题前缀
    /// </summary>
    public const string DeviceControlTopicPrefix = "device/control";
    
    /// <summary>
    /// 设备状态主题前缀
    /// </summary>
    public const string DeviceStatusTopicPrefix = "device/status";
    
    /// <summary>
    /// 设备事件主题前缀
    /// </summary>
    public const string DeviceEventTopicPrefix = "device/event";
    
    /// <summary>
    /// 系统消息主题前缀
    /// </summary>
    public const string SystemMessageTopicPrefix = "system/message";
    
    /// <summary>
    /// 心跳主题
    /// </summary>
    public const string HeartbeatTopic = "system/heartbeat";
    
    /// <summary>
    /// 主题分隔符
    /// </summary>
    public const string TopicSeparator = "/";
    
    /// <summary>
    /// 通配符 - 单级
    /// </summary>
    public const string SingleLevelWildcard = "+";
    
    /// <summary>
    /// 通配符 - 多级
    /// </summary>
    public const string MultiLevelWildcard = "#";
    
    #endregion
    
    #region 消息常量
    
    /// <summary>
    /// 默认消息QoS等级
    /// </summary>
    public const int DefaultQosLevel = 1;
    
    /// <summary>
    /// 最大消息大小（字节）
    /// </summary>
    public const int MaxMessageSize = 1024 * 1024; // 1MB
    
    /// <summary>
    /// 默认消息保留时间（小时）
    /// </summary>
    public const int DefaultMessageRetentionHours = 24;
    
    /// <summary>
    /// 消息ID长度
    /// </summary>
    public const int MessageIdLength = 32;
    
    #endregion
    
    #region 性能常量
    
    /// <summary>
    /// 默认队列容量
    /// </summary>
    public const int DefaultQueueCapacity = 10000;
    
    /// <summary>
    /// 默认最大并发处理数
    /// </summary>
    public const int DefaultMaxConcurrentHandlers = 100;
    
    /// <summary>
    /// 默认处理超时时间（毫秒）
    /// </summary>
    public const int DefaultHandlerTimeoutMs = 30000;
    
    /// <summary>
    /// 默认内存池最大数组长度
    /// </summary>
    public const int DefaultMemoryPoolMaxArrayLength = 1024 * 1024; // 1MB
    
    /// <summary>
    /// 默认对象池最大大小
    /// </summary>
    public const int DefaultObjectPoolMaxSize = 1000;
    
    /// <summary>
    /// 性能监控间隔（秒）
    /// </summary>
    public const int PerformanceMonitoringIntervalSeconds = 5;
    
    /// <summary>
    /// 性能报告间隔（分钟）
    /// </summary>
    public const int PerformanceReportIntervalMinutes = 15;
    
    #endregion
    
    #region 缓存常量
    
    /// <summary>
    /// 默认缓存过期时间（分钟）
    /// </summary>
    public const int DefaultCacheExpirationMinutes = 30;
    
    /// <summary>
    /// 设备缓存键前缀
    /// </summary>
    public const string DeviceCacheKeyPrefix = "mqtt:device:";
    
    /// <summary>
    /// 消息缓存键前缀
    /// </summary>
    public const string MessageCacheKeyPrefix = "mqtt:message:";
    
    /// <summary>
    /// 配置缓存键前缀
    /// </summary>
    public const string ConfigCacheKeyPrefix = "mqtt:config:";
    
    #endregion
    
    #region 日志常量
    
    /// <summary>
    /// 默认日志级别
    /// </summary>
    public const string DefaultLogLevel = "Information";
    
    /// <summary>
    /// 日志文件最大大小（MB）
    /// </summary>
    public const int LogFileMaxSizeMB = 100;
    
    /// <summary>
    /// 日志文件保留天数
    /// </summary>
    public const int LogRetentionDays = 30;
    
    /// <summary>
    /// 日志文件名模板
    /// </summary>
    public const string LogFileNameTemplate = "mqtt-{Date}.log";
    
    #endregion
    
    #region 错误代码常量
    
    /// <summary>
    /// 连接失败错误代码
    /// </summary>
    public const string ErrorCodeConnectionFailed = "MQTT_CONNECTION_FAILED";
    
    /// <summary>
    /// 消息发送失败错误代码
    /// </summary>
    public const string ErrorCodeMessageSendFailed = "MQTT_MESSAGE_SEND_FAILED";
    
    /// <summary>
    /// 订阅失败错误代码
    /// </summary>
    public const string ErrorCodeSubscriptionFailed = "MQTT_SUBSCRIPTION_FAILED";
    
    /// <summary>
    /// 配置无效错误代码
    /// </summary>
    public const string ErrorCodeInvalidConfiguration = "MQTT_INVALID_CONFIGURATION";
    
    /// <summary>
    /// 设备不存在错误代码
    /// </summary>
    public const string ErrorCodeDeviceNotFound = "MQTT_DEVICE_NOT_FOUND";
    
    /// <summary>
    /// 权限不足错误代码
    /// </summary>
    public const string ErrorCodeInsufficientPermissions = "MQTT_INSUFFICIENT_PERMISSIONS";
    
    /// <summary>
    /// 超时错误代码
    /// </summary>
    public const string ErrorCodeTimeout = "MQTT_TIMEOUT";
    
    /// <summary>
    /// 内部错误代码
    /// </summary>
    public const string ErrorCodeInternalError = "MQTT_INTERNAL_ERROR";
    
    #endregion
    
    #region HTTP状态码常量
    
    /// <summary>
    /// 成功状态码
    /// </summary>
    public const int StatusCodeSuccess = 200;
    
    /// <summary>
    /// 创建成功状态码
    /// </summary>
    public const int StatusCodeCreated = 201;
    
    /// <summary>
    /// 无内容状态码
    /// </summary>
    public const int StatusCodeNoContent = 204;
    
    /// <summary>
    /// 请求错误状态码
    /// </summary>
    public const int StatusCodeBadRequest = 400;
    
    /// <summary>
    /// 未授权状态码
    /// </summary>
    public const int StatusCodeUnauthorized = 401;
    
    /// <summary>
    /// 禁止访问状态码
    /// </summary>
    public const int StatusCodeForbidden = 403;
    
    /// <summary>
    /// 未找到状态码
    /// </summary>
    public const int StatusCodeNotFound = 404;
    
    /// <summary>
    /// 请求超时状态码
    /// </summary>
    public const int StatusCodeRequestTimeout = 408;
    
    /// <summary>
    /// 内部服务器错误状态码
    /// </summary>
    public const int StatusCodeInternalServerError = 500;
    
    /// <summary>
    /// 服务不可用状态码
    /// </summary>
    public const int StatusCodeServiceUnavailable = 503;
    
    #endregion
    
    #region 配置键常量
    
    /// <summary>
    /// MQTT配置节名称
    /// </summary>
    public const string ConfigSectionName = "MQTT";
    
    /// <summary>
    /// 客户端配置键
    /// </summary>
    public const string ClientConfigKey = "Client";
    
    /// <summary>
    /// 路由器配置键
    /// </summary>
    public const string RouterConfigKey = "Router";
    
    /// <summary>
    /// 设备控制配置键
    /// </summary>
    public const string DeviceControlConfigKey = "DeviceControl";
    
    /// <summary>
    /// 性能配置键
    /// </summary>
    public const string PerformanceConfigKey = "Performance";
    
    /// <summary>
    /// 日志配置键
    /// </summary>
    public const string LoggingConfigKey = "Logging";
    
    #endregion
    
    #region 正则表达式常量
    
    /// <summary>
    /// 设备ID验证正则表达式
    /// </summary>
    public const string DeviceIdPattern = @"^[a-zA-Z0-9_-]{1,64}$";
    
    /// <summary>
    /// 主题验证正则表达式
    /// </summary>
    public const string TopicPattern = @"^[a-zA-Z0-9_/+-]+$";
    
    /// <summary>
    /// IP地址验证正则表达式
    /// </summary>
    public const string IpAddressPattern = @"^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
    
    /// <summary>
    /// 域名验证正则表达式
    /// </summary>
    public const string DomainNamePattern = @"^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?([\.]([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?))*$";
    
    #endregion
    
    #region 连接相关常量
    
    /// <summary>
    /// 默认连接超时时间（毫秒）
    /// </summary>
    public const int DefaultConnectionTimeoutMs = 30000;
    
    /// <summary>
    /// 默认保持连接时间（秒）
    /// </summary>
    public const int DefaultKeepAliveSeconds = 60;
    
    /// <summary>
    /// 默认消息重试次数
    /// </summary>
    public const int DefaultRetryCount = 3;
    
    /// <summary>
    /// 默认消息超时时间（毫秒）
    /// </summary>
    public const int DefaultMessageTimeoutMs = 5000;
    
    #endregion
    
    #region MQTT主题常量
    
    /// <summary>
    /// MQTT主题模式定义
    /// 定义了系统中使用的各种MQTT主题模板和通配符
    /// </summary>
    public static class Topics
    {
        /// <summary>
        /// 网关订阅主题模式 - 接收云端控制指令
        /// </summary>
        /// <remarks>
        /// 参数说明：
        /// - {0}: 产品密钥或网关ID
        /// - {1}: 设备名称或设备ID
        /// </remarks>
        public const string GatewaySubscribe = "/{0}/{1}/user/get";

        /// <summary>
        /// 网关发布主题模式 - 发布设备事件
        /// </summary>
        /// <remarks>
        /// 参数说明：
        /// - {0}: 产品密钥
        /// - {1}: 设备名称
        /// - {2}: 事件类型
        /// </remarks>
        public const string GatewayPublish = "/sys/{0}/{1}/thing/event/{2}/post";

        /// <summary>
        /// 通配符订阅 - 监听所有网关控制指令
        /// 使用MQTT通配符'+'匹配单级主题
        /// </summary>
        public const string AllGatewayCommands = "/+/+/user/get";

        /// <summary>
        /// 通配符订阅 - 监听所有设备事件
        /// 使用MQTT通配符'+'匹配单级主题
        /// </summary>
        public const string AllDeviceEvents = "/sys/+/+/thing/event/+/post";
        
        /// <summary>
        /// 心跳发布主题模式
        /// 用于设备定期发送心跳消息
        /// </summary>
        public const string HeartbeatPublish = "/sys/{0}/{1}/thing/event/heartbeat/post";
        
        /// <summary>
        /// 事件通配符主题
        /// 用于订阅所有网关的事件消息
        /// </summary>
        public const string EventWildcard = "gateway/+/event/+";
        
        /// <summary>
        /// 响应通配符主题
        /// 用于订阅所有网关的响应消息
        /// </summary>
        public const string ResponseWildcard = "gateway/+/response/+";
        
        /// <summary>
        /// 控制通配符主题
        /// 用于订阅所有网关的控制消息
        /// </summary>
        public const string ControlWildcard = "gateway/+/control/+";
        
        /// <summary>
        /// 设备状态上报主题模式
        /// </summary>
        public const string DeviceStatusReport = "/sys/{0}/{1}/thing/event/status/post";
        
        /// <summary>
        /// 设备错误上报主题模式
        /// </summary>
        public const string DeviceErrorReport = "/sys/{0}/{1}/thing/event/error/post";
        
        /// <summary>
        /// 批量设备控制主题模式
        /// </summary>
        public const string BatchDeviceControl = "/sys/{0}/batch/thing/service/property/set";
        
        /// <summary>
        /// 设备配置下发主题模式
        /// </summary>
        public const string DeviceConfigSet = "/sys/{0}/{1}/thing/config/set";
        
        /// <summary>
        /// 设备配置获取主题模式
        /// </summary>
        public const string DeviceConfigGet = "/sys/{0}/{1}/thing/config/get";
        
        /// <summary>
        /// 设备固件升级主题模式
        /// </summary>
        public const string DeviceUpgrade = "/sys/{0}/{1}/thing/ota/firmware/push";
        
        /// <summary>
        /// 设备日志上报主题模式
        /// </summary>
        public const string DeviceLogReport = "/sys/{0}/{1}/thing/log/post";
    }
    
    #endregion
    
    #region 事件类型常量
    
    /// <summary>
    /// MQTT事件类型常量
    /// 定义了系统中使用的各种事件类型标识
    /// </summary>
    public static class EventTypes
    {
        /// <summary>
        /// 广播消息事件
        /// 用于设备位置和状态广播
        /// </summary>
        public const string Beacon = "beacon";

        /// <summary>
        /// 照明参数设置事件
        /// 用于设备照明配置和参数设置
        /// </summary>
        public const string Setting = "setting";

        /// <summary>
        /// 传感器配置事件
        /// 用于传感器数据上报和配置
        /// </summary>
        public const string Sensor = "sensor";

        /// <summary>
        /// 调光参数事件
        /// 用于设备亮度调节相关事件
        /// </summary>
        public const string Dimming = "dimming";

        /// <summary>
        /// Mesh组网参数事件
        /// 用于设备网络拓扑和组网配置
        /// </summary>
        public const string Network = "network";

        /// <summary>
        /// 红外遥控器配置事件
        /// 用于红外设备控制和配置
        /// </summary>
        public const string Irc = "irc";

        /// <summary>
        /// 能耗信息事件
        /// 用于设备功耗监控和统计
        /// </summary>
        public const string Consumption = "consumption";

        /// <summary>
        /// 感应触发事件
        /// 用于传感器触发和检测事件
        /// </summary>
        public const string Trigger = "trigger";

        /// <summary>
        /// 心跳消息事件
        /// 用于设备在线状态检测
        /// </summary>
        public const string Heartbeat = "heartbeat";

        /// <summary>
        /// 情景模式照明参数事件
        /// 用于场景模式配置和切换
        /// </summary>
        public const string Scene = "scene";

        /// <summary>
        /// 实时亮度、色温事件
        /// 用于设备当前状态实时上报
        /// </summary>
        public const string Current = "current";
        
        /// <summary>
        /// 告警事件
        /// 用于设备异常和故障告警
        /// </summary>
        public const string Alarm = "alarm";
        
        /// <summary>
        /// 日志事件
        /// 用于设备运行日志上报
        /// </summary>
        public const string Log = "log";
        
        /// <summary>
        /// 升级事件
        /// 用于设备固件升级相关事件
        /// </summary>
        public const string Upgrade = "upgrade";
        
        /// <summary>
        /// 配置事件
        /// 用于设备配置变更通知
        /// </summary>
        public const string Config = "config";
        
        /// <summary>
        /// 状态事件
        /// 用于设备状态变化通知
        /// </summary>
        public const string Status = "status";
    }
    
    #endregion
    
    #region 控制代码常量
    
    /// <summary>
    /// MQTT控制代码常量
    /// 定义了设备控制命令的类型标识
    /// </summary>
    public static class ControlCodes
    {
        /// <summary>
        /// 单灯控制代码
        /// 用于控制单个照明设备
        /// </summary>
        public const string SingleLight = "100";
        
        /// <summary>
        /// 群组控制代码
        /// 用于控制设备组
        /// </summary>
        public const string GroupControl = "200";
        
        /// <summary>
        /// 标签控制代码
        /// 用于控制具有特定标签的设备
        /// </summary>
        public const string TagControl = "300";
        
        /// <summary>
        /// 区域控制代码
        /// 用于控制指定区域内的所有设备
        /// </summary>
        public const string AreaControl = "400";
        
        /// <summary>
        /// 场景控制代码
        /// 用于执行预设场景
        /// </summary>
        public const string SceneControl = "500";
        
        /// <summary>
        /// 定时控制代码
        /// 用于定时任务控制
        /// </summary>
        public const string TimerControl = "600";
        
        /// <summary>
        /// 批量控制代码
        /// 用于批量设备操作
        /// </summary>
        public const string BatchControl = "700";
        
        /// <summary>
        /// 系统控制代码
        /// 用于系统级别的控制操作
        /// </summary>
        public const string SystemControl = "800";
    }
    
    #endregion
    
    #region 动作类型常量
    
    /// <summary>
    /// MQTT设备动作指令常量
    /// 定义了设备可执行的各种动作类型
    /// </summary>
    public static class Actions
    {
        /// <summary>
        /// 扫描设备动作
        /// 用于发现和识别网络中的设备
        /// </summary>
        public const string Scan = "scan";

        /// <summary>
        /// 停止扫描动作
        /// 用于停止设备扫描过程
        /// </summary>
        public const string StopScan = "stopScan";

        /// <summary>
        /// 获取照明参数动作
        /// 用于查询设备当前照明配置
        /// </summary>
        public const string GetSetting = "getSetting";

        /// <summary>
        /// 设置照明参数动作
        /// 用于配置设备照明参数
        /// </summary>
        public const string SetSetting = "setSetting";

        /// <summary>
        /// 开灯动作
        /// 用于打开照明设备
        /// </summary>
        public const string TurnOn = "turnOn";

        /// <summary>
        /// 关灯动作
        /// 用于关闭照明设备
        /// </summary>
        public const string TurnOff = "turnOff";

        /// <summary>
        /// 调光动作
        /// 用于调节设备亮度
        /// </summary>
        public const string Dimming = "dimming";

        /// <summary>
        /// 调色温动作
        /// 用于调节设备色温
        /// </summary>
        public const string ColorTemperature = "colorTemperature";

        /// <summary>
        /// 获取传感器配置动作
        /// 用于查询传感器当前配置
        /// </summary>
        public const string GetSensor = "getSensor";

        /// <summary>
        /// 设置传感器配置动作
        /// 用于配置传感器参数
        /// </summary>
        public const string SetSensor = "setSensor";

        /// <summary>
        /// 调光动作（简化版）
        /// 用于快速调节设备亮度
        /// </summary>
        public const string DimLight = "dim_light";
        
        /// <summary>
        /// 开关灯动作
        /// 用于切换设备开关状态
        /// </summary>
        public const string SwitchLight = "switch_light";
        
        /// <summary>
        /// 获取状态动作
        /// 用于查询设备当前状态
        /// </summary>
        public const string GetStatus = "get_status";
        
        /// <summary>
        /// 设置照明动作
        /// 用于配置设备照明参数
        /// </summary>
        public const string SetLighting = "set_lighting";
        
        /// <summary>
        /// 重启设备动作
        /// 用于重启设备
        /// </summary>
        public const string Restart = "restart";
        
        /// <summary>
        /// 重置设备动作
        /// 用于恢复设备出厂设置
        /// </summary>
        public const string Reset = "reset";
        
        /// <summary>
        /// 升级固件动作
        /// 用于设备固件升级
        /// </summary>
        public const string Upgrade = "upgrade";
        
        /// <summary>
        /// 设置场景动作
        /// 用于配置和切换场景模式
        /// </summary>
        public const string SetScene = "setScene";
        
        /// <summary>
        /// 设置定时器动作
        /// 用于配置定时任务
        /// </summary>
        public const string SetTimer = "setTimer";
        
        /// <summary>
        /// 删除定时器动作
        /// 用于删除定时任务
        /// </summary>
        public const string DeleteTimer = "deleteTimer";
        
        /// <summary>
        /// 设置组动作
        /// 用于设备分组操作
        /// </summary>
        public const string SetGroup = "setGroup";
        
        /// <summary>
        /// 移除组动作
        /// 用于从组中移除设备
        /// </summary>
        public const string RemoveGroup = "removeGroup";
        
        /// <summary>
        /// 同步时间动作
        /// 用于同步设备时间
        /// </summary>
        public const string SyncTime = "syncTime";
        
        /// <summary>
        /// 设置配置动作
        /// 用于更新设备配置
        /// </summary>
        public const string SetConfig = "setConfig";
        
        /// <summary>
        /// 获取配置动作
        /// 用于查询设备配置
        /// </summary>
        public const string GetConfig = "getConfig";
        
        /// <summary>
        /// 设置标签动作
        /// 用于设备标签管理
        /// </summary>
        public const string SetTag = "setTag";
        
        /// <summary>
        /// 移除标签动作
        /// 用于移除设备标签
        /// </summary>
        public const string RemoveTag = "removeTag";
    }
    
    #endregion
    
    #region QoS级别常量
    
    /// <summary>
    /// MQTT QoS（服务质量）级别常量
    /// 定义了消息传递的可靠性级别
    /// </summary>
    public static class QoSLevels
    {
        /// <summary>
        /// QoS 0 - 最多一次传递
        /// 消息发送后不保证到达，性能最高但可靠性最低
        /// 适用于对消息丢失不敏感的场景
        /// </summary>
        public const int AtMostOnce = 0;

        /// <summary>
        /// QoS 1 - 至少一次传递
        /// 保证消息至少到达一次，可能出现重复
        /// 适用于需要保证消息到达的场景
        /// </summary>
        public const int AtLeastOnce = 1;

        /// <summary>
        /// QoS 2 - 恰好一次传递
        /// 保证消息恰好到达一次，不重复不丢失
        /// 可靠性最高但性能开销最大
        /// </summary>
        public const int ExactlyOnce = 2;
        
        /// <summary>
        /// 默认QoS级别
        /// 系统默认使用的QoS级别
        /// </summary>
        public const int Default = AtLeastOnce;
        
        /// <summary>
        /// 心跳消息QoS级别
        /// 心跳消息推荐使用的QoS级别
        /// </summary>
        public const int Heartbeat = AtMostOnce;
        
        /// <summary>
        /// 控制命令QoS级别
        /// 控制命令推荐使用的QoS级别
        /// </summary>
        public const int Control = AtLeastOnce;
        
        /// <summary>
        /// 重要事件QoS级别
        /// 重要事件推荐使用的QoS级别
        /// </summary>
        public const int ImportantEvent = ExactlyOnce;
    }
    
    #endregion
    
    #region 消息状态常量
    
    /// <summary>
    /// MQTT消息状态常量
    /// 定义了消息处理的各种状态
    /// </summary>
    public static class MessageStatus
    {
        /// <summary>
        /// 成功状态
        /// 消息处理成功完成
        /// </summary>
        public const string Success = "success";

        /// <summary>
        /// 失败状态
        /// 消息处理失败
        /// </summary>
        public const string Failed = "failed";

        /// <summary>
        /// 超时状态
        /// 消息处理超时
        /// </summary>
        public const string Timeout = "timeout";

        /// <summary>
        /// 处理中状态
        /// 消息正在处理中
        /// </summary>
        public const string Processing = "processing";
        
        /// <summary>
        /// 等待状态
        /// 消息等待处理
        /// </summary>
        public const string Pending = "pending";
        
        /// <summary>
        /// 已取消状态
        /// 消息处理已取消
        /// </summary>
        public const string Cancelled = "cancelled";
        
        /// <summary>
        /// 重试状态
        /// 消息正在重试
        /// </summary>
        public const string Retrying = "retrying";
        
        /// <summary>
        /// 已忽略状态
        /// 消息被忽略处理
        /// </summary>
        public const string Ignored = "ignored";
        
        /// <summary>
        /// 部分成功状态
        /// 批量消息部分成功
        /// </summary>
        public const string PartialSuccess = "partialSuccess";
        
        /// <summary>
        /// 已过期状态
        /// 消息已过期
        /// </summary>
        public const string Expired = "expired";
    }
    
    #endregion
    
    #region 设备类型常量
    
    /// <summary>
    /// 设备类型常量
    /// 定义了系统支持的各种设备类型
    /// </summary>
    public static class DeviceTypes
    {
        /// <summary>
        /// 智能灯具
        /// 可调光调色的智能照明设备
        /// </summary>
        public const string SmartLight = "smartLight";
        
        /// <summary>
        /// 传感器
        /// 各类环境传感器设备
        /// </summary>
        public const string Sensor = "sensor";
        
        /// <summary>
        /// 网关设备
        /// MQTT网关或边缘计算设备
        /// </summary>
        public const string Gateway = "gateway";
        
        /// <summary>
        /// 开关设备
        /// 智能开关或继电器设备
        /// </summary>
        public const string Switch = "switch";
        
        /// <summary>
        /// 调光器
        /// 专用调光控制设备
        /// </summary>
        public const string Dimmer = "dimmer";
        
        /// <summary>
        /// 红外设备
        /// 红外遥控或红外传感设备
        /// </summary>
        public const string InfraredDevice = "infraredDevice";
        
        /// <summary>
        /// 能耗监测设备
        /// 电能计量和监测设备
        /// </summary>
        public const string PowerMeter = "powerMeter";
        
        /// <summary>
        /// 控制面板
        /// 智能控制面板设备
        /// </summary>
        public const string ControlPanel = "controlPanel";
        
        /// <summary>
        /// 未知设备
        /// 未识别或未分类的设备
        /// </summary>
        public const string Unknown = "unknown";
    }
    
    #endregion
    
    #region 时间常量
    
    /// <summary>
    /// 一秒的毫秒数
    /// </summary>
    public const int MillisecondsPerSecond = 1000;
    
    /// <summary>
    /// 一分钟的毫秒数
    /// </summary>
    public const int MillisecondsPerMinute = 60 * MillisecondsPerSecond;
    
    /// <summary>
    /// 一小时的毫秒数
    /// </summary>
    public const int MillisecondsPerHour = 60 * MillisecondsPerMinute;
    
    /// <summary>
    /// 一天的毫秒数
    /// </summary>
    public const int MillisecondsPerDay = 24 * MillisecondsPerHour;
    
    #endregion
}

/// <summary>
/// MQTT插件枚举定义
/// 集中管理插件中使用的所有枚举类型
/// </summary>
public static class MqttEnums
{
    /// <summary>
    /// 插件状态枚举
    /// </summary>
    public enum PluginStatus
    {
        /// <summary>
        /// 未初始化
        /// </summary>
        Uninitialized = 0,
        
        /// <summary>
        /// 初始化中
        /// </summary>
        Initializing = 1,
        
        /// <summary>
        /// 已初始化
        /// </summary>
        Initialized = 2,
        
        /// <summary>
        /// 启动中
        /// </summary>
        Starting = 3,
        
        /// <summary>
        /// 运行中
        /// </summary>
        Running = 4,
        
        /// <summary>
        /// 停止中
        /// </summary>
        Stopping = 5,
        
        /// <summary>
        /// 已停止
        /// </summary>
        Stopped = 6,
        
        /// <summary>
        /// 错误状态
        /// </summary>
        Error = 7,
        
        /// <summary>
        /// 已释放
        /// </summary>
        Disposed = 8
    }
    
    /// <summary>
    /// 连接状态枚举
    /// </summary>
    public enum ConnectionStatus
    {
        /// <summary>
        /// 已断开
        /// </summary>
        Disconnected = 0,
        
        /// <summary>
        /// 连接中
        /// </summary>
        Connecting = 1,
        
        /// <summary>
        /// 已连接
        /// </summary>
        Connected = 2,
        
        /// <summary>
        /// 断开中
        /// </summary>
        Disconnecting = 3,
        
        /// <summary>
        /// 重连中
        /// </summary>
        Reconnecting = 4
    }
    
    /// <summary>
    /// 消息类型枚举
    /// </summary>
    public enum MessageType
    {
        /// <summary>
        /// 设备控制消息
        /// </summary>
        DeviceControl = 1,
        
        /// <summary>
        /// 设备状态消息
        /// </summary>
        DeviceStatus = 2,
        
        /// <summary>
        /// 设备事件消息
        /// </summary>
        DeviceEvent = 3,
        
        /// <summary>
        /// 系统消息
        /// </summary>
        SystemMessage = 4,
        
        /// <summary>
        /// 心跳消息
        /// </summary>
        Heartbeat = 5
    }
    
    /// <summary>
    /// 设备状态枚举
    /// </summary>
    public enum DeviceStatus
    {
        /// <summary>
        /// 未知状态
        /// </summary>
        Unknown = 0,
        
        /// <summary>
        /// 在线
        /// </summary>
        Online = 1,
        
        /// <summary>
        /// 离线
        /// </summary>
        Offline = 2,
        
        /// <summary>
        /// 故障
        /// </summary>
        Fault = 3,
        
        /// <summary>
        /// 维护中
        /// </summary>
        Maintenance = 4
    }
    
    /// <summary>
    /// 日志级别枚举
    /// </summary>
    public enum LogLevel
    {
        /// <summary>
        /// 跟踪
        /// </summary>
        Trace = 0,
        
        /// <summary>
        /// 调试
        /// </summary>
        Debug = 1,
        
        /// <summary>
        /// 信息
        /// </summary>
        Information = 2,
        
        /// <summary>
        /// 警告
        /// </summary>
        Warning = 3,
        
        /// <summary>
        /// 错误
        /// </summary>
        Error = 4,
        
        /// <summary>
        /// 严重错误
        /// </summary>
        Critical = 5
    }
}