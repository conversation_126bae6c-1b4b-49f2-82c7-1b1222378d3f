# 网关数据处理与MQTT通信架构文档 v2.0

## 文档信息

* **文档版本**: v2.0

* **创建日期**: 2025-01-29

* **文档类型**: 网关数据处理与MQTT通信架构文档

* **适用范围**: 网关设备通信、MQTT消息处理、数据采集系统

* **维护团队**: 架构与通信团队

***

## 1. 架构概述

### 1.1 系统简介

网关数据处理与MQTT通信系统是基于Admin.NET插件架构的智能照明数据通信平台。系统通过MQTT协议实现设备与云端的双向通信，支持设备数据采集、远程控制、状态监控等核心功能。采用分布式架构设计，确保系统的高可用性、可扩展性和实时性。

### 1.2 核心价值

* **实时通信**: 基于MQTT协议的低延迟消息传输

* **可靠传输**: 支持QoS等级，确保消息可靠送达

* **海量连接**: 支持大规模设备并发连接

* **灵活扩展**: 插件化架构，支持功能模块动态扩展

* **统一管理**: 集中化的设备管理和数据处理

### 1.3 应用场景

* **智能照明**: LED灯具的远程控制和状态监控

* **能耗管理**: 实时能耗数据采集和分析

* **故障诊断**: 设备故障自动检测和报警

* **场景控制**: 批量设备的场景化控制

* **数据分析**: 历史数据统计和趋势分析

***

## 2. 整体架构设计

### 2.1 系统架构图

```mermaid
graph TB
    subgraph "设备层 Device Layer"
        A[智能照明设备]
        B[传感器设备]
        C[控制器设备]
    end
    
    subgraph "网关层 Gateway Layer"
        D[网关设备]
        E[协议转换]
        F[本地缓存]
    end
    
    subgraph "消息中间件层 Message Middleware"
        G[EMQX集群]
        H[负载均衡]
        I[消息路由]
    end
    
    subgraph "应用服务层 Application Layer"
        J[Admin.NET应用服务]
        K[MQTT插件服务器]
        L[数据处理引擎]
        M[消息处理器]
    end
    
    subgraph "数据存储层 Data Storage"
        N[MySQL主库]
        O[MySQL从库]
        P[Redis缓存]
        Q[时序数据库]
    end
    
    subgraph "管理界面层 Management UI"
        R[Web管理端]
        S[移动端APP]
        T[监控面板]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    D --> G
    G --> H
    H --> I
    G --> K
    K --> J
    J --> L
    L --> M
    J --> N
    L --> O
    K --> P
    M --> Q
    J --> R
    J --> S
    K --> T
```

### 2.2 数据流向架构

```mermaid
sequenceDiagram
    participant D as 智能设备
    participant G as 网关设备
    participant E as EMQX集群
    participant M as MQTT插件服务器
    participant A as Admin.NET应用
    participant DB as 数据库
    participant U as 用户界面
    
    Note over D,U: 设备上报数据流程
    D->>G: 发送设备数据
    G->>E: 发布MQTT消息
    E->>M: 转发消息到插件
    M->>A: 处理业务逻辑
    A->>DB: 存储设备数据
    A->>U: 推送状态更新
    
    Note over D,U: 设备控制流程
    U->>A: 发送控制指令
    A->>M: 调用MQTT服务
    M->>E: 发布控制消息
    E->>G: 转发到网关
    G->>D: 执行设备控制
    D->>G: 返回执行结果
    G->>E: 上报执行状态
    E->>M: 接收状态反馈
    M->>A: 更新设备状态
    A->>U: 显示控制结果
```

***

## 3. MQTT插件服务器架构

### 3.1 插件架构设计

#### 3.1.1 Admin.NET插件集成

```mermaid
graph LR
    subgraph "Admin.NET Core"
        A[应用主框架]
        B[插件管理器]
        C[服务容器]
    end
    
    subgraph "MQTT插件模块"
        D[MQTT服务插件]
        E[消息处理插件]
        F[设备管理插件]
        G[数据采集插件]
    end
    
    subgraph "外部服务"
        H[EMQX服务器]
        I[设备网关]
        J[数据库]
    end
    
    A --> B
    B --> D
    B --> E
    B --> F
    B --> G
    C --> D
    D --> H
    E --> H
    F --> I
    G --> J
```

#### 3.1.2 核心组件说明

| 组件名称       | 功能描述     | 主要职责            |
| ---------- | -------- | --------------- |
| MQTT客户端管理器 | 管理MQTT连接 | 连接建立、心跳检测、重连机制  |
| 消息路由器      | 消息分发处理   | 主题订阅、消息路由、QoS管理 |
| 设备状态管理器    | 设备状态维护   | 在线状态、属性缓存、状态同步  |
| 数据处理引擎     | 数据解析转换   | 协议解析、数据验证、格式转换  |
| 安全认证模块     | 连接安全控制   | 客户端认证、权限验证、加密通信 |
| 监控统计模块     | 系统监控统计   | 连接统计、消息统计、性能监控  |

### 3.2 技术栈与依赖

#### 3.2.1 核心技术栈

* **框架基础**: Admin.NET (基于.NET 8.0)

* **MQTT客户端**: MQTTnet 4.x

* **数据库**: MySQL 8.0 + Redis 7.0

* **消息队列**: RabbitMQ 3.12

* **监控**: Prometheus + Grafana

* **日志**: Serilog + ELK Stack

#### 3.2.2 关键依赖包

```xml
<PackageReference Include="MQTTnet" Version="4.3.1.873" />
<PackageReference Include="MQTTnet.Extensions.ManagedClient" Version="4.3.1.873" />
<PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
<PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
<PackageReference Include="StackExchange.Redis" Version="2.7.10" />
<PackageReference Include="MySql.EntityFrameworkCore" Version="8.0.0" />
```

### 3.3 服务接口设计

#### 3.3.1 Web管理界面路由

| 路由路径               | 功能描述       | 权限要求  |
| ------------------ | ---------- | ----- |
| `/mqtt/dashboard`  | MQTT服务监控面板 | 管理员   |
| `/mqtt/clients`    | 客户端连接管理    | 管理员   |
| `/mqtt/topics`     | 主题订阅管理     | 管理员   |
| `/mqtt/messages`   | 消息日志查看     | 操作员   |
| `/mqtt/settings`   | MQTT服务配置   | 系统管理员 |
| `/mqtt/statistics` | 统计报表查看     | 管理员   |

#### 3.3.2 MQTT主题路由设计

##### 下行控制主题 (服务器 → 设备)

| 主题模式                       | 功能描述   | QoS等级 |
| -------------------------- | ------ | ----- |
| `cmd/{gateway_id}/control` | 设备控制指令 | 1     |
| `cmd/{gateway_id}/config`  | 设备配置下发 | 1     |
| `cmd/{gateway_id}/upgrade` | 固件升级指令 | 1     |
| `cmd/{gateway_id}/query`   | 设备状态查询 | 0     |

##### 上行事件主题 (设备 → 服务器)

| 主题模式                          | 功能描述   | QoS等级 |
| ----------------------------- | ------ | ----- |
| `data/{gateway_id}/status`    | 设备状态上报 | 1     |
| `data/{gateway_id}/energy`    | 能耗数据上报 | 1     |
| `data/{gateway_id}/alarm`     | 告警事件上报 | 2     |
| `data/{gateway_id}/heartbeat` | 心跳保活消息 | 0     |

##### 系统管理主题

| 主题模式                       | 功能描述   | QoS等级 |
| -------------------------- | ------ | ----- |
| `sys/{gateway_id}/online`  | 网关上线通知 | 1     |
| `sys/{gateway_id}/offline` | 网关离线通知 | 1     |
| `sys/broadcast/notice`     | 系统广播通知 | 0     |

***

## 4. 网关数据处理架构

### 4.1 网关设备架构

#### 4.1.1 网关功能模块

```mermaid
graph TD
    subgraph "网关设备内部架构"
        A[设备接入层]
        B[协议转换层]
        C[数据处理层]
        D[通信管理层]
        E[本地存储层]
        F[配置管理层]
    end
    
    subgraph "外部接口"
        G[智能设备]
        H[MQTT服务器]
        I[本地网络]
    end
    
    G --> A
    A --> B
    B --> C
    C --> D
    D --> H
    C --> E
    F --> A
    F --> B
    F --> C
    I --> F
```

#### 4.1.2 数据处理流程

1. **数据采集**: 从

