/**
 * 数据缓存管理工具
 * 提供数据缓存、离线存储、缓存过期等功能
 */

class CacheManager {
  constructor() {
    this.cachePrefix = 'energy_light_'
    this.defaultExpireTime = 30 * 60 * 1000 // 30分钟默认过期时间
    
    // 不同类型数据的缓存时间配置
    this.cacheTimeConfig = {
      'user': 60 * 60 * 1000,        // 用户信息：1小时
      'device': 10 * 60 * 1000,      // 设备信息：10分钟
      'energy': 5 * 60 * 1000,       // 能耗数据：5分钟
      'lighting': 15 * 60 * 1000,    // 照明数据：15分钟
      'fault': 2 * 60 * 1000,        // 故障数据：2分钟
      'static': 24 * 60 * 60 * 1000, // 静态数据：24小时
      'config': 60 * 60 * 1000       // 配置数据：1小时
    }
    
    // 自动清理过期缓存的定时器
    this.startAutoCleanup()
  }

  /**
   * 获取智能缓存时间
   * @param {string} key 缓存键
   * @param {number} customTime 自定义时间
   * @returns {number} 缓存时间
   */
  getSmartCacheTime(key, customTime) {
    if (customTime) return customTime
    
    // 根据键名判断数据类型
    for (const [type, time] of Object.entries(this.cacheTimeConfig)) {
      if (key.toLowerCase().includes(type)) {
        return time
      }
    }
    
    return this.defaultExpireTime
  }
  
  /**
   * 启动自动清理过期缓存
   */
  startAutoCleanup() {
    // 每30分钟清理一次过期缓存
    setInterval(() => {
      const cleaned = this.clearExpired()
      if (cleaned > 0) {
        console.log(`自动清理了 ${cleaned} 个过期缓存`)
      }
    }, 30 * 60 * 1000)
  }
  
  /**
   * 设置缓存
   * @param {string} key 缓存键
   * @param {any} data 缓存数据
   * @param {number} expireTime 过期时间（毫秒），默认根据数据类型智能判断
   */
  set(key, data, expireTime) {
    const cacheKey = this.cachePrefix + key
    const smartExpireTime = this.getSmartCacheTime(key, expireTime)
    
    const cacheData = {
      data,
      timestamp: Date.now(),
      expireTime: smartExpireTime,
      dataType: this.getDataType(key)
    }
    
    try {
      uni.setStorageSync(cacheKey, JSON.stringify(cacheData))
      console.log(`缓存已设置: ${key}, 过期时间: ${smartExpireTime}ms`)
      return true
    } catch (error) {
      console.error('缓存设置失败:', error)
      return false
    }
  }
  
  /**
   * 获取数据类型
   * @param {string} key 缓存键
   * @returns {string} 数据类型
   */
  getDataType(key) {
    const lowerKey = key.toLowerCase()
    for (const type of Object.keys(this.cacheTimeConfig)) {
      if (lowerKey.includes(type)) {
        return type
      }
    }
    return 'default'
  }

  /**
   * 获取缓存
   * @param {string} key 缓存键
   * @param {any} defaultValue 默认值
   * @returns {any} 缓存数据或默认值
   */
  get(key, defaultValue = null) {
    const cacheKey = this.cachePrefix + key
    
    try {
      const cacheStr = uni.getStorageSync(cacheKey)
      if (!cacheStr) {
        return defaultValue
      }
      
      const cacheData = JSON.parse(cacheStr)
      const now = Date.now()
      
      // 检查是否过期
      if (now - cacheData.timestamp > cacheData.expireTime) {
        this.remove(key)
        return defaultValue
      }
      
      return cacheData.data
    } catch (error) {
      console.error('缓存获取失败:', error)
      return defaultValue
    }
  }

  /**
   * 删除缓存
   * @param {string} key 缓存键
   */
  remove(key) {
    const cacheKey = this.cachePrefix + key
    try {
      uni.removeStorageSync(cacheKey)
      return true
    } catch (error) {
      console.error('缓存删除失败:', error)
      return false
    }
  }

  /**
   * 清空所有缓存
   */
  clear() {
    try {
      const info = uni.getStorageInfoSync()
      const keys = info.keys.filter(key => key.startsWith(this.cachePrefix))
      
      keys.forEach(key => {
        uni.removeStorageSync(key)
      })
      
      return true
    } catch (error) {
      console.error('缓存清空失败:', error)
      return false
    }
  }

  /**
   * 检查缓存是否存在且未过期
   * @param {string} key 缓存键
   * @returns {boolean}
   */
  has(key) {
    const cacheKey = this.cachePrefix + key
    
    try {
      const cacheStr = uni.getStorageSync(cacheKey)
      if (!cacheStr) {
        return false
      }
      
      const cacheData = JSON.parse(cacheStr)
      const now = Date.now()
      
      return now - cacheData.timestamp <= cacheData.expireTime
    } catch (error) {
      return false
    }
  }

  /**
   * 获取缓存大小信息
   * @returns {object} 缓存信息
   */
  getInfo() {
    try {
      const info = uni.getStorageInfoSync()
      const cacheKeys = info.keys.filter(key => key.startsWith(this.cachePrefix))
      
      return {
        totalKeys: cacheKeys.length,
        totalSize: info.currentSize,
        limitSize: info.limitSize,
        cacheKeys
      }
    } catch (err) {
      console.error('获取缓存信息失败:', err)
      return {
        totalSize: 0,
        itemCount: 0,
        items: []
      }
    }
  }

  /**
   * 清理过期缓存
   */
  clearExpired() {
    try {
      const info = uni.getStorageInfoSync()
      const cacheKeys = info.keys.filter(key => key.startsWith(this.cachePrefix))
      let clearedCount = 0
      
      cacheKeys.forEach(cacheKey => {
        try {
          const cacheStr = uni.getStorageSync(cacheKey)
          if (cacheStr) {
            const cacheData = JSON.parse(cacheStr)
            const now = Date.now()
            
            if (now - cacheData.timestamp > cacheData.expireTime) {
              uni.removeStorageSync(cacheKey)
              clearedCount++
            }
          }
        } catch (err) {
          // 如果解析失败，直接删除
          uni.removeStorageSync(cacheKey)
          clearedCount++
        }
      })
      
      return clearedCount
    } catch (err) {
      console.error('清理过期缓存失败:', err)
      return 0
    }
  }
}

// 离线数据管理
class OfflineManager {
  constructor() {
    this.offlinePrefix = 'offline_'
    this.syncQueue = 'sync_queue'
    this.isOnline = true
    this.syncInProgress = false
    
    // 监听网络状态变化
    this.initNetworkListener()
    
    // 定期检查并同步离线数据
    this.startAutoSync()
  }
  
  /**
   * 初始化网络状态监听
   */
  initNetworkListener() {
    uni.onNetworkStatusChange((res) => {
      const wasOffline = !this.isOnline
      this.isOnline = res.isConnected
      
      console.log('网络状态变化:', res.networkType, '连接状态:', res.isConnected)
      
      // 从离线恢复到在线时，自动同步数据
      if (wasOffline && this.isOnline) {
        console.log('网络已恢复，开始同步离线数据')
        this.syncOfflineData()
      }
    })
    
    // 初始化网络状态
    this.checkNetworkStatus().then(isOnline => {
      this.isOnline = isOnline
    })
  }
  
  /**
   * 启动自动同步
   */
  startAutoSync() {
    // 每5分钟检查一次是否需要同步
    setInterval(() => {
      if (this.isOnline && !this.syncInProgress) {
        const queue = this.getSyncQueue()
        if (queue.length > 0) {
          console.log(`发现 ${queue.length} 个待同步项，开始同步`)
          this.syncOfflineData()
        }
      }
    }, 5 * 60 * 1000)
  }

  /**
   * 保存离线数据
   * @param {string} key 数据键
   * @param {any} data 数据
   */
  saveOfflineData(key, data) {
    const offlineKey = this.offlinePrefix + key
    const offlineData = {
      data,
      timestamp: Date.now(),
      synced: false
    }
    
    try {
      uni.setStorageSync(offlineKey, JSON.stringify(offlineData))
      return true
    } catch (err) {
      console.error('离线数据保存失败:', err)
      return false
    }
  }

  /**
   * 获取离线数据
   * @param {string} key 数据键
   * @returns {any} 离线数据
   */
  getOfflineData(key) {
    const offlineKey = this.offlinePrefix + key
    
    try {
      const dataStr = uni.getStorageSync(offlineKey)
      if (!dataStr) {
        return null
      }
      
      const offlineData = JSON.parse(dataStr)
      return offlineData.data
    } catch (err) {
      console.error('离线数据获取失败:', err)
      return null
    }
  }

  /**
   * 添加到同步队列
   * @param {Object} requestData 请求数据
   */
  addToSyncQueue(requestData) {
    try {
      const queue = this.getSyncQueue()
      const syncItem = {
        ...requestData,
        timestamp: Date.now(),
        retryCount: 0,
        maxRetries: 3,
        id: this.generateSyncId()
      }
      
      queue.push(syncItem)
      uni.setStorageSync(this.syncQueue, JSON.stringify(queue))
      console.log(`已添加到同步队列: ${syncItem.id}`)
      return true
    } catch (err) {
      console.error('添加同步队列失败:', err)
      return false
    }
  }

  /**
   * 生成同步项ID
   * @returns {string} 同步项ID
   */
  generateSyncId() {
    return `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 获取同步队列
   * @returns {array} 同步队列
   */
  getSyncQueue() {
    try {
      const queueStr = uni.getStorageSync(this.syncQueue) || '[]'
      return JSON.parse(queueStr)
    } catch (err) {
      console.error('获取同步队列失败:', err)
      return []
    }
  }

  /**
   * 清空同步队列
   */
  clearSyncQueue() {
    try {
      uni.removeStorageSync(this.syncQueue)
      return true
    } catch (err) {
      console.error('清空同步队列失败:', err)
      return false
    }
  }

  /**
   * 同步离线数据
   * @returns {Promise<Object>} 同步结果
   */
  async syncOfflineData() {
    if (this.syncInProgress) {
      console.log('同步正在进行中，跳过本次同步')
      return { success: false, message: '同步正在进行中' }
    }
    
    this.syncInProgress = true
    const startTime = Date.now()
    let successCount = 0
    let failCount = 0
    
    try {
      const queue = this.getSyncQueue()
      if (queue.length === 0) {
        console.log('同步队列为空')
        return { success: true, message: '无数据需要同步' }
      }
      
      console.log(`开始同步 ${queue.length} 个离线数据项`)
      
      // 按时间戳排序，优先同步较早的数据
      queue.sort((a, b) => a.timestamp - b.timestamp)
      
      const remainingQueue = []
      
      for (const item of queue) {
        try {
          // 检查是否超过最大重试次数
          if (item.retryCount >= item.maxRetries) {
            console.warn(`同步项 ${item.id} 已达到最大重试次数，跳过`)
            failCount++
            continue
          }
          
          // 执行同步
          const result = await this.executeSyncItem(item)
          
          if (result.success) {
            console.log(`同步成功: ${item.id}`)
            successCount++
          } else {
            // 增加重试次数并重新加入队列
            item.retryCount++
            item.lastError = result.error
            item.lastRetryTime = Date.now()
            remainingQueue.push(item)
            failCount++
            console.warn(`同步失败: ${item.id}, 重试次数: ${item.retryCount}/${item.maxRetries}`)
          }
        } catch (err) {
          console.error(`同步项 ${item.id} 执行异常:`, err)
          item.retryCount++
          item.lastError = err.message
          item.lastRetryTime = Date.now()
          remainingQueue.push(item)
          failCount++
        }
      }
      
      // 更新同步队列
      uni.setStorageSync(this.syncQueue, JSON.stringify(remainingQueue))
      
      const duration = Date.now() - startTime
      const result = {
        success: true,
        successCount,
        failCount,
        duration,
        remaining: remainingQueue.length
      }
      
      console.log(`同步完成: 成功 ${successCount}, 失败 ${failCount}, 耗时 ${duration}ms, 剩余 ${remainingQueue.length}`)
      return result
      
    } catch (err) {
      console.error('同步过程异常:', err)
      return { success: false, error: err.message }
    } finally {
      this.syncInProgress = false
    }
  }
  
  /**
   * 执行单个同步项
   * @param {Object} item 同步项
   * @returns {Promise<Object>} 执行结果
   */
  async executeSyncItem(item) {
    try {
      // 这里需要根据实际的请求管理器来执行同步
      // 由于request.js中的requestManager可能还没有导入，这里先返回模拟结果
      // 实际使用时需要调用真实的API请求
      
      // 模拟API调用
      const response = await new Promise((resolve, reject) => {
        uni.request({
          url: item.url,
          method: item.method || 'POST',
          data: item.data,
          header: item.header,
          success: resolve,
          fail: reject
        })
      })
      
      if (response.statusCode === 200) {
        return { success: true, data: response.data }
      } else {
        return { success: false, error: `HTTP ${response.statusCode}` }
      }
    } catch (err) {
      return { success: false, error: err.message }
    }
  }
  
  /**
   * 检查网络状态
   * @returns {Promise<boolean>} 网络是否可用
   */
  async checkNetworkStatus() {
    return new Promise((resolve) => {
      uni.getNetworkType({
        success: (res) => {
          const isOnline = res.networkType !== 'none'
          this.isOnline = isOnline
          resolve(isOnline)
        },
        fail: () => {
          this.isOnline = false
          resolve(false)
        }
      })
    })
  }
}

// 创建实例
const cacheManager = new CacheManager()
const offlineManager = new OfflineManager()

export {
  cacheManager,
  offlineManager,
  CacheManager,
  OfflineManager
}

export default {
  cache: cacheManager,
  offline: offlineManager
}