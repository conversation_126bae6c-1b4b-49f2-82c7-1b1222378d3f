<template>
	<view class="device-list-container">
		<!-- 搜索栏 -->
		<view class="search-bar">
			<view class="search-input-wrapper">
				<text class="search-icon">🔍</text>
				<input 
					class="search-input" 
					v-model="searchKeyword" 
					placeholder="搜索设备名称或编号"
					@input="handleSearchInput"
					@confirm="handleSearchConfirm"
				/>
				<text v-if="searchKeyword" class="clear-icon" @tap="clearSearch">✕</text>
			</view>
			<view class="filter-btn" @tap="showFilterModal">
				<text class="filter-icon">⚙️</text>
				<text class="filter-text">筛选</text>
				<view v-if="hasActiveFilter" class="filter-dot"></view>
			</view>
		</view>
		
		<!-- 统计信息 -->
		<view class="stats-bar">
			<view class="stats-item" v-for="(item, index) in deviceStats" :key="index">
				<view class="stats-value" :class="item.valueClass">{{ item.value }}</view>
				<view class="stats-label">{{ item.label }}</view>
			</view>
		</view>
		
		<!-- 虚拟滚动设备列表 -->
		<view class="device-list-wrapper">
			<!-- 骨架屏 -->
			<view v-if="loading && filteredDeviceList.length === 0" class="skeleton-container">
				<SkeletonScreen
					type="device-card"
					:count="6"
					:animated="true"
				/>
			</view>
			
			<!-- 设备列表 -->
			<VirtualScroll
				v-else
				:items="filteredDeviceList"
				:item-height="200"
				height="calc(100vh - 300rpx)"
				:buffer="3"
				key-field="id"
				:enable-refresh="true"
				:enable-load-more="enableLoadMore"
				:loading-more="loadingMore"
				:no-more-data="noMoreData"
				@refresh="refreshDeviceList"
				@loadMore="loadMoreDevices"
				@itemTap="viewDeviceDetail"
			>
				<template #item="{ item: device }">
					<view class="device-item" @tap="viewDeviceDetail(device)">
				<view class="device-header">
					<view class="device-info">
						<view class="device-name">{{ device.name }}</view>
						<view class="device-code">{{ device.code }}</view>
					</view>
					<view class="device-status" :class="device.statusClass">
						<view class="status-dot"></view>
						<text class="status-text">{{ device.statusText }}</text>
					</view>
				</view>
				
				<view class="device-details">
					<view class="detail-item">
						<text class="detail-label">位置：</text>
						<text class="detail-value">{{ device.location }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">功率：</text>
						<text class="detail-value">{{ device.power }}W</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">亮度：</text>
						<text class="detail-value">{{ device.brightness }}%</text>
					</view>
				</view>
				
				<view class="device-actions">
					<view class="action-btn" :class="device.isOn ? 'btn-danger' : 'btn-primary'" @tap.stop="toggleDevice(device)">
						<text>{{ device.isOn ? '关闭' : '开启' }}</text>
					</view>
					<view class="action-btn btn-secondary" @tap.stop="adjustBrightness(device)">
						<text>调光</text>
					</view>
					<view class="action-btn btn-info" @tap.stop="viewDeviceDetail(device)">
						<text>详情</text>
					</view>
				</view>
				
						<view v-if="device.lastUpdate" class="device-footer">
							<text class="update-time">最后更新：{{ formatTime(device.lastUpdate) }}</text>
						</view>
					</view>
				</template>
			</VirtualScroll>
			
			<!-- 空状态 -->
			<view v-if="filteredDeviceList.length === 0" class="empty">
				<view class="empty-icon">📱</view>
				<view class="empty-text">{{ searchKeyword ? '未找到相关设备' : '暂无设备数据' }}</view>
				<view v-if="!searchKeyword" class="empty-action" @tap="refreshDeviceList">
					<text>刷新数据</text>
				</view>
			</view>
		</view>
		
		<!-- 筛选弹窗 -->
		<view v-if="showFilter" class="filter-modal" @tap="hideFilterModal">
			<view class="filter-content" @tap.stop>
				<view class="filter-header">
					<text class="filter-title">设备筛选</text>
					<text class="filter-close" @tap="hideFilterModal">✕</text>
				</view>
				
				<view class="filter-section">
					<view class="filter-label">设备状态</view>
					<view class="filter-options">
						<view 
							class="filter-option" 
							:class="{ active: filterOptions.status === item.value }"
							v-for="item in statusOptions" 
							:key="item.value"
							@tap="selectFilter('status', item.value)"
						>
							{{ item.label }}
						</view>
					</view>
				</view>
				
				<view class="filter-section">
					<view class="filter-label">设备类型</view>
					<view class="filter-options">
						<view 
							class="filter-option" 
							:class="{ active: filterOptions.type === item.value }"
							v-for="item in typeOptions" 
							:key="item.value"
							@tap="selectFilter('type', item.value)"
						>
							{{ item.label }}
						</view>
					</view>
				</view>
				
				<view class="filter-section">
					<view class="filter-label">所在区域</view>
					<view class="filter-options">
						<view 
							class="filter-option" 
							:class="{ active: filterOptions.area === item.value }"
							v-for="item in areaOptions" 
							:key="item.value"
							@tap="selectFilter('area', item.value)"
						>
							{{ item.label }}
						</view>
					</view>
				</view>
				
				<view class="filter-actions">
					<view class="filter-btn-reset" @tap="resetFilter">
						<text>重置</text>
					</view>
					<view class="filter-btn-confirm" @tap="applyFilter">
						<text>确定</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 调光弹窗 -->
		<view v-if="showBrightnessModal" class="brightness-modal" @tap="hideBrightnessModal">
			<view class="brightness-content" @tap.stop>
				<view class="brightness-header">
					<text class="brightness-title">调节亮度</text>
					<text class="brightness-close" @tap="hideBrightnessModal">✕</text>
				</view>
				
				<view class="brightness-info">
					<text class="device-name">{{ currentDevice?.name }}</text>
					<text class="current-brightness">当前亮度：{{ brightnessValue }}%</text>
				</view>
				
				<view class="brightness-slider">
					<slider 
						:value="brightnessValue" 
						:min="0" 
						:max="100" 
						:step="1"
						@change="handleBrightnessChange"
						active-color="#1890ff"
						background-color="#f0f0f0"
					/>
				</view>
				
				<view class="brightness-presets">
					<view 
						class="preset-btn" 
						v-for="preset in brightnessPresets" 
						:key="preset.value"
						@tap="setBrightness(preset.value)"
					>
						{{ preset.label }}
					</view>
				</view>
				
				<view class="brightness-actions">
					<view class="brightness-btn-cancel" @tap="hideBrightnessModal">
						<text>取消</text>
					</view>
					<view class="brightness-btn-confirm" @tap="confirmBrightness">
						<text>确定</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { errorMixin } from '@/mixins/errorMixin.js'
import { performanceMixin } from '@/mixins/performanceMixin.js'
import deviceApi from '@/api/device.js'
import { toggleDeviceSwitch, setDeviceBrightness } from '@/api/device.js'
import { authGuard } from '@/utils/authGuard'
import VirtualScroll from '@/components/VirtualScroll.vue'
import SkeletonScreen from '@/components/SkeletonScreen.vue'
import { MemoryLeakGuard, performanceUtils } from '@/utils/performance.js'

export default {
	name: 'DeviceList',
	mixins: [errorMixin, performanceMixin],
	components: {
		VirtualScroll,
		SkeletonScreen
	},
	data() {
		return {
			searchKeyword: '',
			loading: false,
			refreshing: false,
			loadingMore: false,
			noMoreData: false,
			enableLoadMore: true,
			currentPage: 1,
			pageSize: 20,
			deviceList: [],
			
			// 筛选相关
			showFilter: false,
			filterOptions: {
				status: '',
				type: '',
				area: ''
			},
			
			// 亮度调节相关
			showBrightnessModal: false,
			currentDevice: null,
			brightnessValue: 50,
			
			// 防抖搜索
			debouncedSearch: null,
			
			// 性能优化相关
			memoryGuard: null,
			performanceMonitor: null,
			
			// 选项数据
			statusOptions: [
				{ label: '全部', value: '' },
				{ label: '在线', value: 1 },
				{ label: '离线', value: 0 },
				{ label: '故障', value: 2 }
			],
			typeOptions: [
				{ label: '全部', value: '' },
				{ label: 'LED灯', value: 'led' },
				{ label: '节能灯', value: 'energy' },
				{ label: '智能灯', value: 'smart' }
			],
			areaOptions: [
				{ label: '全部', value: '' },
				{ label: '办公区A', value: 'office_a' },
				{ label: '办公区B', value: 'office_b' },
				{ label: '会议室', value: 'meeting' },
				{ label: '走廊', value: 'corridor' }
			],
			brightnessPresets: [
				{ label: '低亮度', value: 25 },
				{ label: '中亮度', value: 50 },
				{ label: '高亮度', value: 75 },
				{ label: '最大亮度', value: 100 }
			]
		}
	},
	
	computed: {
		// 设备统计
		deviceStats() {
			const total = this.deviceList.length
			const online = this.deviceList.filter(d => d.status === 'online').length
			const offline = this.deviceList.filter(d => d.status === 'offline').length
			const fault = this.deviceList.filter(d => d.status === 'fault').length
			
			return [
				{ label: '总数', value: total, valueClass: 'value-total' },
				{ label: '在线', value: online, valueClass: 'value-online' },
				{ label: '离线', value: offline, valueClass: 'value-offline' },
				{ label: '故障', value: fault, valueClass: 'value-fault' }
			]
		},
		
		// 过滤后的设备列表
		filteredDeviceList() {
			let list = this.deviceList
			
			// 搜索过滤
			if (this.searchKeyword) {
				const keyword = this.searchKeyword.toLowerCase()
				list = list.filter(device => 
					device.name.toLowerCase().includes(keyword) ||
					device.code.toLowerCase().includes(keyword) ||
					device.location.toLowerCase().includes(keyword)
				)
			}
			
			// 状态过滤
			if (this.filterOptions.status) {
				list = list.filter(device => device.status === this.filterOptions.status)
			}
			
			// 类型过滤
			if (this.filterOptions.type) {
				list = list.filter(device => device.type === this.filterOptions.type)
			}
			
			// 区域过滤
			if (this.filterOptions.area) {
				list = list.filter(device => device.area === this.filterOptions.area)
			}
			
			return list
		},
		
		// 是否有激活的筛选条件
		hasActiveFilter() {
			return this.filterOptions.status || this.filterOptions.type || this.filterOptions.area
		}
	},
	
	async onLoad() {
		// 检查登录状态
		const canAccess = await authGuard({
			requireAuth: true,
			message: '设备管理页面需要登录后才能访问'
		})
		
		if (!canAccess) {
			return
		}
		
		// 初始化性能优化工具
		this.memoryGuard = new MemoryLeakGuard()
		this.performanceMonitor = performanceUtils.monitor
		
		// 标记页面加载开始
		this.performanceMonitor.mark('device-list-load-start')
		
		// 初始化防抖搜索（使用性能混入提供的方法）
		this.debouncedSearch = this.createDebounced(this.performSearch, 500, 'search')
		
		this.loadDeviceList()
		this.getUserInfo()
		
		// 标记页面加载完成
		this.performanceMonitor.mark('device-list-load-end')
		this.performanceMonitor.measure('device-list-load-time', 'device-list-load-start', 'device-list-load-end')
	},
	
	onShow() {
		// 页面显示时刷新数据
		this.refreshDeviceList()
	},
	
	onUnload() {
		// 清理防抖定时器
		if (this.debouncedSearch) {
			this.debouncedSearch = null
		}
		
		// 清理搜索定时器
		if (this.searchTimer) {
			clearTimeout(this.searchTimer)
			this.searchTimer = null
		}
		
		// 清理内存防护资源
		if (this.memoryGuard) {
			this.memoryGuard.cleanup()
			this.memoryGuard = null
		}
		
		// 清理性能监控数据
		if (this.performanceMonitor) {
			this.performanceMonitor.clear()
		}
		
		// 清理设备列表数据，释放内存
		this.deviceList = []
	},
	
	methods: {
		// 加载设备列表
		async loadDeviceList(reset = false) {
			await this.$safeExecute(async () => {
				// 性能监控：标记数据加载开始
				const loadMarkName = `device-load-${Date.now()}`
				this.performanceMonitor.mark(loadMarkName)
				
				if (reset) {
					this.loading = true
					// 首次加载显示骨架屏，不显示全局loading
					if (this.deviceList.length > 0) {
						this.$showLoading('加载中...')
					}
				} else {
					this.loadingMore = true
				}
				
				const params = {
					deviceName: this.searchKeyword, // 使用设备名称进行搜索
					deviceCode: this.searchKeyword, // 同时使用设备编码进行搜索
					status: this.filterOptions.status,
					deviceType: this.filterOptions.type,
					location: this.filterOptions.area,
					page: this.currentPage,
					pageSize: this.pageSize
				}
				
				const data = await deviceApi.getDeviceList(params)
				
				// 处理设备数据
				const newItems = (data.list || data || []).map(device => ({
					...device,
					statusText: this.getStatusText(device.status),
					statusClass: `status-${device.status}`,
					lastUpdate: new Date(device.lastUpdate || device.updatedAt || Date.now())
				}))
				
				if (reset || this.currentPage === 1) {
					this.deviceList = newItems
				} else {
					this.deviceList = [...this.deviceList, ...newItems]
				}
				
				// 内存管理：当设备列表过大时进行清理
				if (this.deviceList.length > 500) {
					console.warn('设备列表过大，建议优化分页策略')
					// 可以在这里实现数据分片或虚拟化策略
				}
				
				// 检查是否还有更多数据
				this.noMoreData = newItems.length < this.pageSize
				this.enableLoadMore = !this.noMoreData
				
				// 性能监控：测量数据加载时间
				const loadEndMarkName = `${loadMarkName}-end`
				this.performanceMonitor.mark(loadEndMarkName)
				const loadTime = this.performanceMonitor.measure(`device-load-time-${Date.now()}`, loadMarkName, loadEndMarkName)
				
				if (loadTime > 2000) {
					console.warn(`设备列表加载时间过长: ${loadTime}ms`)
				}
				
				this.loading = false
				this.loadingMore = false
				uni.hideLoading()
			}, {
				context: '加载设备列表',
				showRetry: true,
				retryAction: () => this.loadDeviceList(reset),
				finally: () => {
					this.loading = false
					this.loadingMore = false
					uni.hideLoading()
				}
			})
		},
		
		// 刷新设备列表
		async refreshDeviceList() {
			await this.$safeExecute(async () => {
				this.refreshing = true
				uni.showLoading({
					title: '刷新中...',
					mask: true
				})
				await this.loadDeviceList()
				uni.hideLoading()
				uni.showToast({
					title: '刷新成功',
					icon: 'success',
					duration: 1500
				})
			}, {
				context: '刷新设备列表',
				finally: () => {
					this.refreshing = false
					uni.hideLoading()
				}
			})
		},
		
		// 处理搜索输入（使用防抖）
		handleSearchInput(e) {
			// 性能监控：标记搜索输入
			this.performanceMonitor.mark('search-input')
			
			if (e && e.detail) {
				this.searchKeyword = e.detail.value
			}
			
			// 使用防抖搜索，避免频繁请求
			if (this.debouncedSearch) {
				this.debouncedSearch()
			}
			
			// 记录搜索关键词长度，用于性能分析
			if (this.searchKeyword && this.searchKeyword.length > 20) {
				console.warn('搜索关键词过长，可能影响搜索性能')
			}
		},
		
		// 执行搜索
		async performSearch() {
			// 性能监控：标记搜索开始
			const searchMarkName = `search-${Date.now()}`
			this.performanceMonitor.mark(searchMarkName)
			
			// 重置分页状态
			this.currentPage = 1
			this.noMoreData = false
			this.enableLoadMore = true
			
			// 清理之前的搜索结果，释放内存
			this.deviceList = []
			
			await this.loadDeviceList(true)
			
			// 性能监控：测量搜索时间
			const searchEndMarkName = `${searchMarkName}-end`
			this.performanceMonitor.mark(searchEndMarkName)
			const searchTime = this.performanceMonitor.measure(`search-time-${Date.now()}`, searchMarkName, searchEndMarkName)
			
			if (searchTime > 1500) {
				console.warn(`搜索响应时间过长: ${searchTime}ms，关键词: ${this.searchKeyword}`)
			}
			
			console.log(`搜索完成，关键词: "${this.searchKeyword}"，结果数量: ${this.deviceList.length}，耗时: ${searchTime}ms`)
		},
		
		// 搜索确认
		handleSearchConfirm() {
			this.performSearch()
		},
		
		// 加载更多设备
		loadMoreDevices() {
			if (this.loadingMore || this.noMoreData) {
				return
			}
			
			this.currentPage++
			this.loadDeviceList(false)
		},
		
		// 清理缓存
		clearCache() {
			// 性能监控：标记缓存清理开始
			this.performanceMonitor.mark('cache-clear-start')
			
			// 清理设备列表数据
			const oldListLength = this.deviceList.length
			this.deviceList = []
			this.currentPage = 1
			this.noMoreData = false
			this.enableLoadMore = true
			
			// 清理内存防护中的资源
			if (this.memoryGuard) {
				this.memoryGuard.cleanup()
			}
			
			// 触发垃圾回收（仅在微信小程序中有效）
			if (typeof wx !== 'undefined' && wx.triggerGC) {
				wx.triggerGC()
			}
			
			// 性能监控：测量缓存清理时间
			this.performanceMonitor.mark('cache-clear-end')
			this.performanceMonitor.measure('cache-clear-time', 'cache-clear-start', 'cache-clear-end')
			
			console.log(`缓存清理完成，释放了 ${oldListLength} 个设备项的内存`)
		},
		
		// 清除搜索
		clearSearch() {
			this.searchKeyword = ''
		},
		
		// 显示筛选弹窗
		showFilterModal() {
			this.showFilter = true
		},
		
		// 隐藏筛选弹窗
		hideFilterModal() {
			this.showFilter = false
		},
		
		// 选择筛选条件
		selectFilter(type, value) {
			this.filterOptions[type] = this.filterOptions[type] === value ? '' : value
		},
		
		// 重置筛选
		resetFilter() {
			this.filterOptions = {
				status: '',
				type: '',
				area: ''
			}
		},
		
		// 应用筛选
		applyFilter() {
			this.hideFilterModal()
			uni.showToast({
				title: '筛选条件已应用',
				icon: 'success'
			})
		},
		
		// 切换设备开关
		async toggleDevice(device) {
			await this.$safeExecute(async () => {
				this.$showLoading('操作中...')
				
				const newStatus = device.status === 1 ? 0 : 1
				await toggleDeviceSwitch(device.id, newStatus)
				
				// 更新本地状态
				device.status = newStatus
				device.statusText = this.getStatusText(newStatus)
				device.statusClass = `status-${newStatus}`
				device.lastUpdate = new Date()
				
				this.$showSuccess(newStatus === 1 ? '设备已开启' : '设备已关闭')
			}, {
				context: `切换设备${device.name}状态`,
				showRetry: true,
				retryAction: () => this.toggleDevice(device),
				finally: () => {
					uni.hideLoading()
				}
			})
		},
		
		// 调节亮度
		adjustBrightness(device) {
			this.currentDevice = device
			this.brightnessValue = device.brightness
			this.showBrightnessModal = true
		},
		
		// 隐藏亮度弹窗
		hideBrightnessModal() {
			this.showBrightnessModal = false
			this.currentDevice = null
		},
		
		// 亮度变化处理
		handleBrightnessChange(e) {
			this.brightnessValue = e.detail.value
		},
		
		// 设置预设亮度
		setBrightness(value) {
			this.brightnessValue = value
		},
		
		// 确认亮度调节
		async confirmBrightness() {
			await this.$safeExecute(async () => {
				this.$showLoading('设置中...')
				
				await setDeviceBrightness(this.currentDevice.id, this.brightnessValue)
				
				// 更新本地状态
				this.currentDevice.brightness = this.brightnessValue
				this.currentDevice.lastUpdate = new Date()
				
				this.hideBrightnessModal()
				this.$showSuccess('亮度设置成功')
			}, {
				context: `设置设备${this.currentDevice?.name}亮度`,
				showRetry: true,
				retryAction: () => this.confirmBrightness(),
				finally: () => {
					uni.hideLoading()
				}
			})
		},
		
		// 查看设备详情
		viewDeviceDetail(device) {
			uni.navigateTo({
				url: `/pages/device/detail?id=${device.id}`
			})
		},
		
		// 格式化时间
		formatTime(date) {
			const now = new Date()
			const diff = now - date
			const minutes = Math.floor(diff / (1000 * 60))
			const hours = Math.floor(diff / (1000 * 60 * 60))
			
			if (minutes < 1) {
				return '刚刚'
			} else if (minutes < 60) {
				return `${minutes}分钟前`
			} else if (hours < 24) {
				return `${hours}小时前`
			} else {
				return this.formatDate(date, 'MM-DD HH:mm')
			}
		},
		
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				online: '在线',
				offline: '离线',
				fault: '故障'
			}
			return statusMap[status] || '未知'
		},
		
		// 初始化防抖搜索
		initDebounceSearch() {
			this.searchTimer = null
		},
		
		// 防抖搜索
		debounceSearch() {
			clearTimeout(this.searchTimer)
			this.searchTimer = setTimeout(() => {
				this.loadDeviceList()
			}, 500)
		},
		
		// 格式化日期
		formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
			if (!date) return ''
			
			const d = new Date(date)
			const year = d.getFullYear()
			const month = String(d.getMonth() + 1).padStart(2, '0')
			const day = String(d.getDate()).padStart(2, '0')
			const hours = String(d.getHours()).padStart(2, '0')
			const minutes = String(d.getMinutes()).padStart(2, '0')
			const seconds = String(d.getSeconds()).padStart(2, '0')
			
			return format
				.replace('YYYY', year)
				.replace('MM', month)
				.replace('DD', day)
				.replace('HH', hours)
				.replace('mm', minutes)
				.replace('ss', seconds)
		}
	}
}
</script>

<style lang="scss" scoped>
.device-list-container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

// 搜索栏
.search-bar {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
	gap: 16rpx;
}

.search-input-wrapper {
	flex: 1;
	position: relative;
	background-color: #ffffff;
	border-radius: 24rpx;
	padding: 0 20rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.search-icon {
	font-size: 28rpx;
	color: #999999;
	margin-right: 12rpx;
}

.search-input {
	flex: 1;
	height: 80rpx;
	font-size: 28rpx;
	color: #333333;
	border: none;
	outline: none;
}

.clear-icon {
	font-size: 24rpx;
	color: #999999;
	padding: 8rpx;
}

.filter-btn {
	position: relative;
	background-color: #ffffff;
	border-radius: 24rpx;
	padding: 0 24rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.filter-icon {
	font-size: 24rpx;
	margin-right: 8rpx;
}

.filter-text {
	font-size: 28rpx;
	color: #333333;
}

.filter-dot {
	position: absolute;
	top: 12rpx;
	right: 12rpx;
	width: 12rpx;
	height: 12rpx;
	background-color: #f5222d;
	border-radius: 50%;
}

// 统计栏
.stats-bar {
	display: flex;
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.stats-item {
	flex: 1;
	text-align: center;
}

.stats-value {
	font-size: 36rpx;
	font-weight: 600;
	margin-bottom: 8rpx;
}

.value-total {
	color: #333333;
}

.value-online {
	color: #52c41a;
}

.value-offline {
	color: #999999;
}

.value-fault {
	color: #f5222d;
}

.stats-label {
	font-size: 24rpx;
	color: #999999;
}

// 设备列表
.device-list-wrapper {
	padding: 0 30rpx;
	padding-bottom: 100rpx;
	flex: 1;
	height: calc(100vh - 300rpx);
	overflow: hidden;
}

.skeleton-container {
	padding: 20rpx;
}

.device-list {
	gap: 20rpx;
}

.device-item {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.device-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 16rpx;
}

.device-info {
	flex: 1;
}

.device-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.device-code {
	font-size: 24rpx;
	color: #999999;
}

.device-status {
	display: flex;
	align-items: center;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
}

.status-dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	margin-right: 8rpx;
}

.status-text {
	font-size: 24rpx;
}

.status-online {
	background-color: #f6ffed;
	color: #52c41a;
	
	.status-dot {
		background-color: #52c41a;
	}
}

.status-offline {
	background-color: #f5f5f5;
	color: #999999;
	
	.status-dot {
		background-color: #999999;
	}
}

.status-fault {
	background-color: #fff2f0;
	color: #f5222d;
	
	.status-dot {
		background-color: #f5222d;
	}
}

.device-details {
	display: flex;
	flex-wrap: wrap;
	gap: 24rpx;
	margin-bottom: 20rpx;
}

.detail-item {
	display: flex;
	align-items: center;
}

.detail-label {
	font-size: 24rpx;
	color: #999999;
}

.detail-value {
	font-size: 24rpx;
	color: #333333;
	font-weight: 500;
}

.device-actions {
	display: flex;
	gap: 16rpx;
	margin-bottom: 16rpx;
}

.action-btn {
	flex: 1;
	height: 64rpx;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	transition: all 0.3s;
}

.btn-primary {
	background-color: #1890ff;
	color: #ffffff;
}

.btn-danger {
	background-color: #f5222d;
	color: #ffffff;
}

.btn-secondary {
	background-color: #f0f0f0;
	color: #333333;
}

.btn-info {
	background-color: #e6f7ff;
	color: #1890ff;
}

.device-footer {
	padding-top: 16rpx;
	border-top: 2rpx solid #f0f0f0;
}

.update-time {
	font-size: 20rpx;
	color: #999999;
}

// 筛选弹窗
.filter-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: flex-end;
	z-index: 1000;
}

.filter-content {
	width: 100%;
	max-height: 80vh;
	background-color: #ffffff;
	border-radius: 24rpx 24rpx 0 0;
	padding: 32rpx;
	overflow-y: auto;
}

.filter-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 32rpx;
}

.filter-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.filter-close {
	font-size: 32rpx;
	color: #999999;
	padding: 8rpx;
}

.filter-section {
	margin-bottom: 32rpx;
}

.filter-label {
	font-size: 28rpx;
	color: #333333;
	margin-bottom: 16rpx;
}

.filter-options {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
}

.filter-option {
	padding: 12rpx 24rpx;
	border: 2rpx solid #e8e8e8;
	border-radius: 24rpx;
	font-size: 24rpx;
	color: #666666;
	transition: all 0.3s;
}

.filter-option.active {
	background-color: #1890ff;
	border-color: #1890ff;
	color: #ffffff;
}

.filter-actions {
	display: flex;
	gap: 16rpx;
	margin-top: 32rpx;
}

.filter-btn-reset,
.filter-btn-confirm {
	flex: 1;
	height: 80rpx;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
}

.filter-btn-reset {
	background-color: #f0f0f0;
	color: #333333;
}

.filter-btn-confirm {
	background-color: #1890ff;
	color: #ffffff;
}

// 亮度弹窗
.brightness-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.brightness-content {
	width: 80%;
	max-width: 600rpx;
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
}

.brightness-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.brightness-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.brightness-close {
	font-size: 32rpx;
	color: #999999;
	padding: 8rpx;
}

.brightness-info {
	text-align: center;
	margin-bottom: 32rpx;
}

.current-brightness {
	font-size: 24rpx;
	color: #999999;
	margin-top: 8rpx;
}

.brightness-slider {
	margin: 32rpx 0;
}

.brightness-presets {
	display: flex;
	justify-content: space-between;
	margin-bottom: 32rpx;
}

.preset-btn {
	padding: 12rpx 20rpx;
	border: 2rpx solid #e8e8e8;
	border-radius: 8rpx;
	font-size: 24rpx;
	color: #666666;
	transition: all 0.3s;
}

.preset-btn:active {
	background-color: #1890ff;
	border-color: #1890ff;
	color: #ffffff;
}

.brightness-actions {
	display: flex;
	gap: 16rpx;
}

.brightness-btn-cancel,
.brightness-btn-confirm {
	flex: 1;
	height: 80rpx;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
}

.brightness-btn-cancel {
	background-color: #f0f0f0;
	color: #333333;
}

.brightness-btn-confirm {
	background-color: #1890ff;
	color: #ffffff;
}
</style>