using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;

namespace Admin.NET.Plugin.MQTT.Core.Models
{
    /// <summary>
    /// 日志条目模型
    /// 表示系统中的一个日志记录，包含完整的日志信息和元数据
    /// </summary>
    public class LogEntry
    {
        /// <summary>
        /// 日志条目唯一标识符
        /// </summary>
        public string Id { get; set; }
        
        /// <summary>
        /// 日志时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
        
        /// <summary>
        /// 日志级别
        /// </summary>
        public Microsoft.Extensions.Logging.LogLevel Level { get; set; }
        
        /// <summary>
        /// 日志消息内容
        /// </summary>
        public string Message { get; set; }
        
        /// <summary>
        /// 异常信息（如果有）
        /// </summary>
        public Exception Exception { get; set; }
        
        /// <summary>
        /// 日志分类
        /// </summary>
        public string Category { get; set; }
        
        /// <summary>
        /// 线程ID
        /// </summary>
        public int ThreadId { get; set; }
        
        /// <summary>
        /// 进程ID
        /// </summary>
        public int ProcessId { get; set; }
        
        /// <summary>
        /// 机器名称
        /// </summary>
        public string MachineName { get; set; }
        
        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; }
        
        /// <summary>
        /// 附加属性字典
        /// 用于存储额外的上下文信息
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new Dictionary<string, object>();
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public LogEntry()
        {
            Id = Guid.NewGuid().ToString("N");
            Timestamp = DateTime.UtcNow;
            Properties = new Dictionary<string, object>();
        }
        
        /// <summary>
        /// 带参数的构造函数
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        /// <param name="category">日志分类</param>
        public LogEntry(Microsoft.Extensions.Logging.LogLevel level, string message, string category = "General") : this()
        {
            Level = level;
            Message = message;
            Category = category;
        }
        
        /// <summary>
        /// 重写ToString方法，提供简洁的日志条目表示
        /// </summary>
        /// <returns>格式化的日志字符串</returns>
        public override string ToString()
        {
            var exceptionInfo = Exception != null ? $" | Exception: {Exception.Message}" : string.Empty;
            return $"{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level}] [{Category}] {Message}{exceptionInfo}";
        }
    }
    
    /// <summary>
    /// 日志统计信息
    /// 用于跟踪特定分类和级别的日志统计数据
    /// </summary>
    public class LogStatistics
    {
        /// <summary>
        /// 日志分类
        /// </summary>
        public string Category { get; set; }
        
        /// <summary>
        /// 日志级别
        /// </summary>
        public Microsoft.Extensions.Logging.LogLevel Level { get; set; }
        
        /// <summary>
        /// 日志条目数量
        /// </summary>
        public long Count { get; set; }
        
        /// <summary>
        /// 首次出现时间
        /// </summary>
        public DateTime FirstOccurrence { get; set; }
        
        /// <summary>
        /// 最后出现时间
        /// </summary>
        public DateTime LastOccurrence { get; set; }
    }
    
    /// <summary>
    /// 日志统计信息汇总
    /// 提供系统整体的日志统计概览
    /// </summary>
    public class LogStatisticsInfo
    {
        /// <summary>
        /// 总日志条目数
        /// </summary>
        public long TotalEntries { get; set; }
        
        /// <summary>
        /// 总错误数
        /// </summary>
        public long TotalErrors { get; set; }
        
        /// <summary>
        /// 总警告数
        /// </summary>
        public long TotalWarnings { get; set; }
        
        /// <summary>
        /// 队列中的条目数
        /// </summary>
        public int QueuedEntries { get; set; }
        
        /// <summary>
        /// 按分类统计
        /// </summary>
        public Dictionary<string, long> Categories { get; set; } = new Dictionary<string, long>();
        
        /// <summary>
        /// 按级别统计
        /// </summary>
        public Dictionary<Microsoft.Extensions.Logging.LogLevel, long> Levels { get; set; } = new Dictionary<Microsoft.Extensions.Logging.LogLevel, long>();
        
        /// <summary>
        /// 错误率
        /// </summary>
        public double ErrorRate { get; set; }
        
        /// <summary>
        /// 警告率
        /// </summary>
        public double WarningRate { get; set; }
        
        /// <summary>
        /// 平均每分钟条目数
        /// </summary>
        public double AverageEntriesPerMinute { get; set; }
        
        /// <summary>
        /// 统计时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
    
    /// <summary>
    /// 日志搜索条件
    /// 用于过滤和搜索日志条目
    /// </summary>
    public class LogSearchCriteria
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }
        
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
        
        /// <summary>
        /// 日志级别列表
        /// </summary>
        public List<Microsoft.Extensions.Logging.LogLevel> Levels { get; set; }
        
        /// <summary>
        /// 分类
        /// </summary>
        public string Category { get; set; }
        
        /// <summary>
        /// 消息包含的文本
        /// </summary>
        public string MessageContains { get; set; }
        
        /// <summary>
        /// 异常类型
        /// </summary>
        public string ExceptionType { get; set; }
        
        /// <summary>
        /// 最大返回条目数
        /// </summary>
        public int MaxResults { get; set; } = 1000;

        /// <summary>
        /// 跳过的条目数（分页用）
        /// </summary>
        public int Skip { get; set; } = 0;

        /// <summary>
        /// 获取的条目数（分页用）
        /// </summary>
        public int Take { get; set; } = 100;

        /// <summary>
        /// 是否降序排序
        /// </summary>
        public bool SortDescending { get; set; } = true;

        /// <summary>
        /// 是否包含归档日志
        /// </summary>
        public bool IncludeArchivedLogs { get; set; } = false;
    }
    
    /// <summary>
    /// 日志导出格式枚举
    /// </summary>
    public enum LogExportFormat
    {
        /// <summary>文本格式</summary>
        Text,
        /// <summary>JSON格式</summary>
        Json,
        /// <summary>CSV格式</summary>
        Csv
    }
    
    /// <summary>
    /// 日志格式枚举
    /// </summary>
    public enum LogFormat
    {
        /// <summary>简单格式</summary>
        Simple,
        /// <summary>结构化格式</summary>
        Structured,
        /// <summary>JSON格式</summary>
        Json
    }

    /// <summary>
    /// 日志搜索结果
    /// </summary>
    public class LogSearchResult
    {
        /// <summary>
        /// 搜索到的日志条目
        /// </summary>
        public List<LogEntry> Entries { get; set; } = new List<LogEntry>();

        /// <summary>
        /// 总条目数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 搜索耗时
        /// </summary>
        public TimeSpan SearchDuration { get; set; }

        /// <summary>
        /// 搜索条件
        /// </summary>
        public LogSearchCriteria Criteria { get; set; }

        /// <summary>
        /// 错误信息（如果有）
        /// </summary>
        public string Error { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 日志导出结果
    /// </summary>
    public class LogExportResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 导出的条目数
        /// </summary>
        public int ExportedCount { get; set; }

        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// 导出耗时
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// 错误信息（如果有）
        /// </summary>
        public string Error { get; set; }
    }

    /// <summary>
    /// 日志清理结果
    /// </summary>
    public class LogCleanupResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 删除的文件列表
        /// </summary>
        public List<string> DeletedFiles { get; set; } = new List<string>();

        /// <summary>
        /// 删除的文件大小（字节）
        /// </summary>
        public long DeletedSizeBytes { get; set; }

        /// <summary>
        /// 清理耗时
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// 错误信息（如果有）
        /// </summary>
        public string Error { get; set; }
    }

    /// <summary>
    /// 性能计数器
    /// </summary>
    public class PerformanceCounter
    {
        /// <summary>
        /// 指标名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 计数
        /// </summary>
        public long Count { get; set; }

        /// <summary>
        /// 总和
        /// </summary>
        public double Sum { get; set; }

        /// <summary>
        /// 最小值
        /// </summary>
        public double Min { get; set; }

        /// <summary>
        /// 最大值
        /// </summary>
        public double Max { get; set; }

        /// <summary>
        /// 平均值
        /// </summary>
        public double Average { get; set; }

        /// <summary>
        /// 最后一次的值
        /// </summary>
        public double LastValue { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; }
    }

    /// <summary>
    /// 性能统计信息
    /// </summary>
    public class PerformanceStatisticsInfo
    {
        /// <summary>
        /// 总计数器数量
        /// </summary>
        public int TotalCounters { get; set; }

        /// <summary>
        /// 计数器字典
        /// </summary>
        public Dictionary<string, PerformanceCounter> Counters { get; set; } = new Dictionary<string, PerformanceCounter>();

        /// <summary>
        /// 按值排序的前10个计数器
        /// </summary>
        public List<PerformanceCounter> TopCountersByValue { get; set; } = new List<PerformanceCounter>();

        /// <summary>
        /// 按计数排序的前10个计数器
        /// </summary>
        public List<PerformanceCounter> TopCountersByCount { get; set; } = new List<PerformanceCounter>();

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }

    #region 事件参数类

    /// <summary>
    /// 日志条目写入事件参数
    /// </summary>
    public class LogEntryWrittenEventArgs : EventArgs
    {
        /// <summary>
        /// 日志条目
        /// </summary>
        public LogEntry LogEntry { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 日志错误事件参数
    /// </summary>
    public class LogErrorEventArgs : EventArgs
    {
        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// 原始消息
        /// </summary>
        public string OriginalMessage { get; set; }

        /// <summary>
        /// 原始日志级别
        /// </summary>
        public Microsoft.Extensions.Logging.LogLevel OriginalLevel { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 性能警告事件参数
    /// </summary>
    public class PerformanceWarningEventArgs : EventArgs
    {
        /// <summary>
        /// 指标名称
        /// </summary>
        public string MetricName { get; set; }

        /// <summary>
        /// 当前值
        /// </summary>
        public double CurrentValue { get; set; }

        /// <summary>
        /// 阈值
        /// </summary>
        public double ThresholdValue { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 性能计数器
        /// </summary>
        public PerformanceCounter Counter { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 日志统计更新事件参数
    /// </summary>
    public class LogStatisticsUpdatedEventArgs : EventArgs
    {
        /// <summary>
        /// 统计信息
        /// </summary>
        public LogStatisticsInfo Statistics { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }

    #endregion
}