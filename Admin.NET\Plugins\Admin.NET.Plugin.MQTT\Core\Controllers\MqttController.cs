// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Admin.NET.Plugin.MQTT.Core.Entity;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Microsoft.AspNetCore.Http;

namespace Admin.NET.Plugin.MQTT.Core.Controllers;

/// <summary>
/// MQTT管理控制器 - 优化版本
/// 提供设备控制、监控和管理的RESTful API接口
/// 包含统一错误处理、参数验证、性能监控和响应格式
/// </summary>
[ApiDescriptionSettings(MqttConst.GroupName, Order = 100)]
[Route("api/mqtt")]
[Authorize]
[Produces("application/json")]
[ProducesResponseType(typeof(ApiResponse), StatusCodes.Status400BadRequest)]
[ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
public class MqttController : IDynamicApiController
{
    private readonly MqttClientManager _clientManager;
    private readonly DeviceControlService _deviceControlService;
    private readonly DeviceEventService _deviceEventService;
    private readonly MqttMonitoringService _monitoringService;
    private readonly MqttLogService _logService;
    private readonly ILogger<MqttController> _logger;
    private static readonly ActivitySource _activitySource = new("Admin.NET.Plugin.MQTT.Controller");

    /// <summary>
    /// 构造函数 - 注入所需的服务依赖
    /// </summary>
    /// <param name="clientManager">MQTT客户端管理器</param>
    /// <param name="deviceControlService">设备控制服务</param>
    /// <param name="deviceEventService">设备事件服务</param>
    /// <param name="monitoringService">监控服务</param>
    /// <param name="logService">日志服务</param>
    /// <param name="logger">日志记录器</param>
    public MqttController(
        MqttClientManager clientManager,
        DeviceControlService deviceControlService,
        DeviceEventService deviceEventService,
        MqttMonitoringService monitoringService,
        MqttLogService logService,
        ILogger<MqttController> logger)
    {
        _clientManager = clientManager ?? throw new ArgumentNullException(nameof(clientManager));
        _deviceControlService = deviceControlService ?? throw new ArgumentNullException(nameof(deviceControlService));
        _deviceEventService = deviceEventService ?? throw new ArgumentNullException(nameof(deviceEventService));
        _monitoringService = monitoringService ?? throw new ArgumentNullException(nameof(monitoringService));
        _logService = logService ?? throw new ArgumentNullException(nameof(logService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 执行操作并处理异常的通用方法
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="operation">要执行的操作</param>
    /// <param name="operationName">操作名称</param>
    /// <returns>API响应</returns>
    private async Task<ApiResponse<T>> ExecuteOperationAsync<T>(Func<Task<T>> operation, string operationName)
    {
        using var activity = _activitySource.StartActivity(operationName);
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("开始执行操作: {OperationName}", operationName);
            
            var result = await operation();
            
            stopwatch.Stop();
            _logger.LogInformation("操作 {OperationName} 执行成功，耗时: {ElapsedMs}ms", 
                operationName, stopwatch.ElapsedMilliseconds);
            
            activity?.SetTag("success", true);
            activity?.SetTag("duration_ms", stopwatch.ElapsedMilliseconds);
            
            return ApiResponse<T>.Success(result, $"操作 {operationName} 执行成功");
        }
        catch (ValidationException ex)
        {
            stopwatch.Stop();
            _logger.LogWarning("操作 {OperationName} 参数验证失败: {Error}", operationName, ex.Message);
            
            activity?.SetTag("success", false);
            activity?.SetTag("error_type", "validation");
            activity?.SetTag("error_message", ex.Message);
            
            return ApiResponse<T>.Failure($"参数验证失败: {ex.Message}", "VALIDATION_ERROR");
        }
        catch (InvalidOperationException ex)
        {
            stopwatch.Stop();
            _logger.LogWarning("操作 {OperationName} 业务逻辑错误: {Error}", operationName, ex.Message);
            
            activity?.SetTag("success", false);
            activity?.SetTag("error_type", "business");
            activity?.SetTag("error_message", ex.Message);
            
            return ApiResponse<T>.Failure($"业务逻辑错误: {ex.Message}", "BUSINESS_ERROR");
        }
        catch (TimeoutException ex)
        {
            stopwatch.Stop();
            _logger.LogWarning("操作 {OperationName} 超时: {Error}", operationName, ex.Message);
            
            activity?.SetTag("success", false);
            activity?.SetTag("error_type", "timeout");
            activity?.SetTag("error_message", ex.Message);
            
            return ApiResponse<T>.Failure($"操作超时: {ex.Message}", "TIMEOUT_ERROR");
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "操作 {OperationName} 执行失败，耗时: {ElapsedMs}ms", 
                operationName, stopwatch.ElapsedMilliseconds);
            
            activity?.SetTag("success", false);
            activity?.SetTag("error_type", "system");
            activity?.SetTag("error_message", ex.Message);
            
            return ApiResponse<T>.Failure($"系统错误: {ex.Message}", "SYSTEM_ERROR");
        }
    }

    /// <summary>
    /// 执行同步操作并处理异常的通用方法
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="operation">要执行的操作</param>
    /// <param name="operationName">操作名称</param>
    /// <returns>API响应</returns>
    private ApiResponse<T> ExecuteOperation<T>(Func<T> operation, string operationName)
    {
        return ExecuteOperationAsync(() => Task.FromResult(operation()), operationName).Result;
    }

    /// <summary>
    /// 验证请求参数
    /// </summary>
    /// <param name="request">请求对象</param>
    /// <param name="parameterName">参数名称</param>
    /// <exception cref="ValidationException">参数验证失败时抛出</exception>
    private static void ValidateRequest(object request, string parameterName = "request")
    {
        if (request == null)
        {
            throw new ValidationException($"{parameterName} 不能为空");
        }

        var validationContext = new ValidationContext(request);
        var validationResults = new List<ValidationResult>();
        
        if (!Validator.TryValidateObject(request, validationContext, validationResults, true))
        {
            var errors = string.Join("; ", validationResults.Select(r => r.ErrorMessage));
            throw new ValidationException($"参数验证失败: {errors}");
        }
    }

    #region 数据库测试

    /// <summary>
    /// 测试数据库操作
    /// </summary>
    /// <returns></returns>
    [HttpGet("test/database")]
    [DisplayName("测试数据库操作")]
    public async Task<dynamic> TestDatabase()
    {
        return await _deviceControlService.TestDatabaseOperationsAsync();
    }

    #endregion

    #region MQTT连接管理

    /// <summary>
    /// 获取MQTT连接状态
    /// 返回当前MQTT客户端的连接状态信息
    /// </summary>
    /// <returns>连接状态信息</returns>
    [HttpGet("connection/status")]
    [DisplayName("获取MQTT连接状态")]
    [ProducesResponseType(typeof(ApiResponse<MqttConnectionStats>), StatusCodes.Status200OK)]
    public ApiResponse<MqttConnectionStats> GetConnectionStatus()
    {
        return ExecuteOperation(() => 
        {
            var status = _clientManager.GetConnectionStats();
            return status;
        }, "GetConnectionStatus");
    }

    /// <summary>
    /// 重新连接MQTT
    /// 断开当前连接并重新建立MQTT连接
    /// </summary>
    /// <returns>重连结果</returns>
    [HttpPost("connection/reconnect")]
    [DisplayName("重新连接MQTT")]
    [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
    public async Task<ApiResponse<bool>> ReconnectAsync()
    {
        return await ExecuteOperationAsync(async () => 
        {
            await _clientManager.StopAsync();
            await Task.Delay(1000); // 等待1秒
            await _clientManager.StartAsync();
            return true;
        }, "ReconnectMqtt");
    }

    /// <summary>
    /// 断开MQTT连接
    /// </summary>
    /// <returns></returns>
    [HttpPost("connection/disconnect")]
    [DisplayName("断开MQTT连接")]
    public async Task<bool> DisconnectAsync()
    {
        await _clientManager.StopAsync();
        return true;
    }

    #endregion

    #region 设备管理

    /// <summary>
    /// 获取设备列表
    /// 返回系统中所有已注册的设备列表
    /// </summary>
    /// <returns>设备信息列表</returns>
    [HttpGet("devices")]
    [DisplayName("获取设备列表")]
    [ProducesResponseType(typeof(ApiResponse<List<DeviceInfo>>), StatusCodes.Status200OK)]
    public ApiResponse<List<DeviceInfo>> GetDevices()
    {
        return ExecuteOperation(() => 
        {
            var devices = _deviceEventService.GetDevices();
            return devices ?? new List<DeviceInfo>();
        }, "GetDevices");
    }

    /// <summary>
    /// 获取所有在线设备列表
    /// 返回当前在线的设备列表
    /// </summary>
    /// <param name="sessionId">会话ID（可选）</param>
    /// <returns>在线设备列表</returns>
    [HttpGet("devices/online")]
    [DisplayName("获取在线设备列表")]
    [ProducesResponseType(typeof(ApiResponse<List<object>>), StatusCodes.Status200OK)]
    public async Task<ApiResponse<List<object>>> GetOnlineDevices(string sessionId = null)
    {
        return await ExecuteOperationAsync(async () => 
        {
            // 定期清理过期的扫描会话
            //_deviceControlService.CleanupExpiredSessions();
            
            // 如果没有提供sessionId，则调用不需要sessionId的方法获取所有在线设备
            if (string.IsNullOrEmpty(sessionId))
            {
                return await _deviceControlService.GetOnlineDevicesAsync();
            }
            
            //// 验证会话是否存在且有效
            //if (!_deviceControlService.IsValidScanSession(sessionId))
            //{
            //    // 会话无效，返回空列表
            //    return new List<object>();
            //}
            
            // 如果提供了sessionId，则获取特定会话的扫描结果
            return await _deviceControlService.GetScanResultsAsync(sessionId);
        }, "GetOnlineDevices");
    }

    /// <summary>
    /// 获取设备信息
    /// </summary>
    /// <param name="deviceKey">设备键值</param>
    /// <returns></returns>
    [HttpGet("devices/{deviceKey}")]
    [DisplayName("获取设备信息")]
    public DeviceInfo GetDevice(string deviceKey)
    {
        return _deviceEventService.GetDevice(deviceKey);
    }

    /// <summary>
    /// 获取设备统计信息
    /// </summary>
    /// <returns></returns>
    [HttpGet("devices/statistics")]
    [DisplayName("获取设备统计信息")]
    public DeviceStatistics GetDeviceStatistics()
    {
        return _deviceEventService.GetDeviceStatistics();
    }

    #endregion

    #region 设备控制

    /// <summary>
    /// 设备扫描
    /// 主动扫描网络中的设备并更新设备列表
    /// </summary>
    /// <param name="GatewayName">网关名称</param>
    /// <param name="area">区域</param>
    /// <param name="address">地址</param>
    /// <param name="timeoutSeconds">超时时间</param>
    /// <returns>请求ID</returns>
    [HttpPost("devices/scan")]
    [DisplayName("设备扫描")]
    [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
    public async Task<ApiResponse<string>> ScanDevicesAsync([FromQuery] string GatewayName, [FromQuery] string area = "00 00", [FromQuery] string address = "00 00", [FromQuery] int timeoutSeconds = 10)
    {
        return await ExecuteOperationAsync(async () => 
        {
            var requestId = await _deviceControlService.ScanDevicesAsync(GatewayName, area, address, timeoutSeconds);
            return requestId;
        }, "ScanDevices");
    }

    /// <summary>
    /// 获取设备状态
    /// </summary>
    /// <param name="area">区域</param>
    /// <param name="address">地址</param>
    /// <param name="timeoutSeconds">超时时间(秒)</param>
    /// <returns></returns>
    [HttpPost("devices/status")]
    [DisplayName("获取设备状态")]
    public async Task<string> GetDeviceStatusAsync([FromQuery] string area, [FromQuery] string address, [FromQuery] int timeoutSeconds = 10)
    {
        return await _deviceControlService.GetDeviceStatusAsync(area, address, timeoutSeconds);
    }

    /// <summary>
    /// 设置照明参数
    /// 控制指定设备的照明参数，包括亮度、色温、颜色等
    /// </summary>
    /// <param name="request">设置请求</param>
    /// <returns>控制请求ID</returns>
    [HttpPost("devices/lighting")]
    [DisplayName("设置照明参数")]
    [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
    public async Task<ApiResponse<string>> SetLightingAsync([FromBody] SetLightingRequest request)
    {
        return await ExecuteOperationAsync(async () => 
        {
            ValidateRequest(request, nameof(request));
            
            if (string.IsNullOrWhiteSpace(request.Area))
                throw new ValidationException("区域不能为空");
            
            if (string.IsNullOrWhiteSpace(request.Address))
                throw new ValidationException("地址不能为空");
            
            if (request.Brightness < 0 || request.Brightness > 100)
                throw new ValidationException("亮度值必须在0-100之间");
            
            if (request.ColorTemperature < 2700 || request.ColorTemperature > 6500)
                throw new ValidationException("色温值必须在2700-6500之间");
            
            var settings = new SettingEventData
            {
                Brightness = request.Brightness,
                ColorTemperature = request.ColorTemperature,
                Red = request.Red,
                Green = request.Green,
                Blue = request.Blue,
                White = request.White,
                TransitionTime = request.TransitionTime
            }; 
            var requestId = await _deviceControlService.SetLightingAsync(request.Area, request.Address, settings, request.TimeoutSeconds);
            return requestId;
        }, "SetLighting");
    }

    /// <summary>
    /// 开关灯控制
    /// 控制指定设备的开关状态
    /// </summary>
    /// <param name="request">开关请求</param>
    /// <returns>控制请求ID</returns>
    [HttpPost("devices/switch")]
    [DisplayName("开关灯控制")]
    [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
    public async Task<ApiResponse<string>> SwitchLightAsync([FromBody] SwitchLightRequest request)
    {
        return await ExecuteOperationAsync(async () => 
        {
            ValidateRequest(request, nameof(request));
            
            if (string.IsNullOrWhiteSpace(request.Area))
                throw new ValidationException("区域不能为空");
            
            if (string.IsNullOrWhiteSpace(request.Address))
                throw new ValidationException("地址不能为空");
            
            var requestId = await _deviceControlService.SwitchLightAsync(request.Area, request.Address, request.IsOn, request.TimeoutSeconds);
            return requestId;
        }, "SwitchLight");
    }

    /// <summary>
    /// 调光控制
    /// 调节指定设备的亮度
    /// </summary>
    /// <param name="request">调光请求</param>
    /// <returns>控制请求ID</returns>
    [HttpPost("devices/dim")]
    [DisplayName("调光控制")]
    [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
    public async Task<ApiResponse<string>> DimLightAsync([FromBody] DimLightRequest request)
    {
        return await ExecuteOperationAsync(async () => 
        {
            ValidateRequest(request, nameof(request));
            
            if (string.IsNullOrWhiteSpace(request.Area))
                throw new ValidationException("区域不能为空");
            
            if (string.IsNullOrWhiteSpace(request.Address))
                throw new ValidationException("地址不能为空");
            
            if (request.Brightness < 0 || request.Brightness > 100)
                throw new ValidationException("亮度值必须在0-100之间");
            
            var requestId = await _deviceControlService.DimLightAsync(request.Area, request.Address, request.Brightness, request.TransitionTime, request.TimeoutSeconds);
            return requestId;
        }, "DimLight");
    }

    /// <summary>
    /// 群组控制
    /// 对指定群组的所有设备执行批量控制操作
    /// </summary>
    /// <param name="request">群组控制请求</param>
    /// <returns>控制请求ID</returns>
    [HttpPost("devices/group")]
    [DisplayName("群组控制")]
    [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
    public async Task<ApiResponse<string>> GroupControlAsync([FromBody] GroupControlRequest request)
    {
        return await ExecuteOperationAsync(async () => 
        {
            ValidateRequest(request, nameof(request));
            
            if (string.IsNullOrWhiteSpace(request.GroupId))
                throw new ValidationException("群组ID不能为空");
            
            if (string.IsNullOrWhiteSpace(request.Action))
                throw new ValidationException("控制动作不能为空");
            
            // 验证动作类型
            var validActions = new[] { "switch", "dim", "lighting", "scene" };
            if (!validActions.Contains(request.Action.ToLower()))
                throw new ValidationException($"无效的控制动作: {request.Action}，支持的动作: {string.Join(", ", validActions)}");
            
            var requestId = await _deviceControlService.GroupControlAsync(request.GroupId, request.Action, request.Parameters, request.TimeoutSeconds);
            return requestId;
        }, "GroupControl");
    }

    /// <summary>
    /// 标签控制
    /// 对具有指定标签的所有设备执行批量控制操作
    /// </summary>
    /// <param name="request">标签控制请求</param>
    /// <returns>控制请求ID</returns>
    [HttpPost("devices/tag")]
    [DisplayName("标签控制")]
    [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
    public async Task<ApiResponse<string>> TagControlAsync([FromBody] TagControlRequest request)
    {
        return await ExecuteOperationAsync(async () => 
        {
            ValidateRequest(request, nameof(request));
            
            if (string.IsNullOrWhiteSpace(request.Tag))
                throw new ValidationException("标签不能为空");
            
            if (string.IsNullOrWhiteSpace(request.Action))
                throw new ValidationException("控制动作不能为空");
            
            // 验证动作类型
            var validActions = new[] { "switch", "dim", "lighting", "scene" };
            if (!validActions.Contains(request.Action.ToLower()))
                throw new ValidationException($"无效的控制动作: {request.Action}，支持的动作: {string.Join(", ", validActions)}");
            
            var requestId = await _deviceControlService.TagControlAsync(request.Tag, request.Action, request.Parameters, request.TimeoutSeconds);
            return requestId;
        }, "TagControl");
    }

    /// <summary>
    /// 全区控制
    /// 对整个区域内的所有设备执行批量控制操作
    /// </summary>
    /// <param name="request">全区控制请求</param>
    /// <returns>控制请求ID</returns>
    [HttpPost("devices/area")]
    [DisplayName("全区控制")]
    [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
    public async Task<ApiResponse<string>> AreaControlAsync([FromBody] AreaControlRequest request)
    {
        return await ExecuteOperationAsync(async () => 
        {
            ValidateRequest(request, nameof(request));
            
            if (string.IsNullOrWhiteSpace(request.Action))
                throw new ValidationException("控制动作不能为空");
            
            // 验证动作类型
            var validActions = new[] { "switch", "dim", "lighting", "scene" };
            if (!validActions.Contains(request.Action.ToLower()))
                throw new ValidationException($"无效的控制动作: {request.Action}，支持的动作: {string.Join(", ", validActions)}");
            
            var requestId = await _deviceControlService.AreaControlAsync(request.Action, request.Parameters, request.TimeoutSeconds);
            return requestId;
        }, "AreaControl");
    }

    /// <summary>
    /// 获取待处理请求
    /// 返回当前系统中所有待处理的设备控制请求
    /// </summary>
    /// <returns>待处理请求列表</returns>
    [HttpGet("devices/requests")]
    [DisplayName("获取待处理请求")]
    [ProducesResponseType(typeof(ApiResponse<Dictionary<string, DeviceControlRequest>>), StatusCodes.Status200OK)]
    public ApiResponse<Dictionary<string, DeviceControlRequest>> GetPendingRequests()
    {
        return ExecuteOperation(() => 
        {
            var requests = _deviceControlService.GetPendingRequests();
            return requests ?? new Dictionary<string, DeviceControlRequest>();
        }, "GetPendingRequests");
    }

    /// <summary>
    /// 取消请求
    /// </summary>
    /// <param name="requestId">请求ID</param>
    /// <returns></returns>
    [HttpDelete("devices/requests/{requestId}")]
    [DisplayName("取消请求")]
    public bool CancelRequest(string requestId)
    {
        return _deviceControlService.CancelRequest(requestId);
    }

    #endregion

    #region 监控和日志

    /// <summary>
    /// 获取性能指标
    /// 返回MQTT系统的详细性能指标数据
    /// </summary>
    /// <returns>性能指标数据字典</returns>
    [HttpGet("monitoring/metrics")]
    [DisplayName("获取性能指标")]
    [ProducesResponseType(typeof(ApiResponse<Dictionary<string, MqttMetrics>>), StatusCodes.Status200OK)]
    public ApiResponse<Dictionary<string, MqttMetrics>> GetMetrics()
    {
        return ExecuteOperation(() => 
        {
            var metrics = _monitoringService.GetMetrics();
            return metrics ?? new Dictionary<string, MqttMetrics>();
        }, "GetMetrics");
    }

    /// <summary>
    /// 获取日志条目
    /// 获取最新的系统日志条目
    /// </summary>
    /// <param name="count">条目数量（默认100，最大1000）</param>
    /// <returns>日志条目列表</returns>
    [HttpGet("logs")]
    [DisplayName("获取日志条目")]
    [ProducesResponseType(typeof(ApiResponse<List<MqttLogEntry>>), StatusCodes.Status200OK)]
    public ApiResponse<List<MqttLogEntry>> GetLogs([FromQuery] int count = 100)
    {
        return ExecuteOperation(() => 
        {
            // 参数验证
            if (count < 1 || count > 1000)
                throw new ValidationException("条目数量必须在1-1000之间");
            
            var logs = _logService.GetLogEntries(count);
            return logs ?? new List<MqttLogEntry>();
        }, "GetLogs");
    }

    /// <summary>
    /// 清空日志
    /// 清空所有系统日志记录
    /// </summary>
    /// <returns>操作结果</returns>
    [HttpDelete("logs")]
    [DisplayName("清空日志")]
    [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
    public ApiResponse<bool> ClearLogs()
    {
        return ExecuteOperation(() => 
        {
            _logService.ClearLogs();
            return true;
        }, "ClearLogs");
    }

    #endregion
}

#region 请求模型
/// <summary>
/// 设置照明参数请求
/// 用于控制设备照明参数的请求模型
/// </summary>
public class SetLightingRequest
{
    /// <summary>
    /// 区域
    /// </summary>
    [Required(ErrorMessage = "区域不能为空")]
    [StringLength(10, ErrorMessage = "区域长度不能超过10个字符")]
    public string Area { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    [Required(ErrorMessage = "地址不能为空")]
    [StringLength(10, ErrorMessage = "地址长度不能超过10个字符")]
    public string Address { get; set; }

    /// <summary>
    /// 亮度 (0-100)
    /// </summary>
    [Range(0, 100, ErrorMessage = "亮度值必须在0-100之间")]
    public int Brightness { get; set; }

    /// <summary>
    /// 色温 (2700-6500K)
    /// </summary>
    [Range(2700, 6500, ErrorMessage = "色温值必须在2700-6500之间")]
    public int ColorTemperature { get; set; }

    /// <summary>
    /// 红色分量 (0-255)
    /// </summary>
    [Range(0, 255, ErrorMessage = "红色分量必须在0-255之间")]
    public int Red { get; set; }

    /// <summary>
    /// 绿色分量 (0-255)
    /// </summary>
    [Range(0, 255, ErrorMessage = "绿色分量必须在0-255之间")]
    public int Green { get; set; }

    /// <summary>
    /// 蓝色分量 (0-255)
    /// </summary>
    [Range(0, 255, ErrorMessage = "蓝色分量必须在0-255之间")]
    public int Blue { get; set; }

    /// <summary>
    /// 白色分量 (0-255)
    /// </summary>
    [Range(0, 255, ErrorMessage = "白色分量必须在0-255之间")]
    public int White { get; set; }

    /// <summary>
    /// 过渡时间 (毫秒)
    /// </summary>
    [Range(0, 60000, ErrorMessage = "过渡时间必须在0-60000毫秒之间")]
    public int TransitionTime { get; set; }

    /// <summary>
    /// 超时时间 (秒)
    /// </summary>
    [Range(1, 300, ErrorMessage = "超时时间必须在1-300秒之间")]
    public int TimeoutSeconds { get; set; } = 10;
}

/// <summary>
/// 开关灯请求
/// 用于控制设备开关状态的请求模型
/// </summary>
public class SwitchLightRequest
{
    /// <summary>
    /// 区域
    /// </summary>
    [Required(ErrorMessage = "区域不能为空")]
    [StringLength(10, ErrorMessage = "区域长度不能超过10个字符")]
    public string Area { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    [Required(ErrorMessage = "地址不能为空")]
    [StringLength(10, ErrorMessage = "地址长度不能超过10个字符")]
    public string Address { get; set; }

    /// <summary>
    /// 是否开启
    /// </summary>
    public bool IsOn { get; set; }

    /// <summary>
    /// 超时时间 (秒)
    /// </summary>
    [Range(1, 300, ErrorMessage = "超时时间必须在1-300秒之间")]
    public int TimeoutSeconds { get; set; } = 10;
}

/// <summary>
/// 调光请求
/// 用于调节设备亮度的请求模型
/// </summary>
public class DimLightRequest
{
    /// <summary>
    /// 区域
    /// </summary>
    [Required(ErrorMessage = "区域不能为空")]
    [StringLength(10, ErrorMessage = "区域长度不能超过10个字符")]
    public string Area { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    [Required(ErrorMessage = "地址不能为空")]
    [StringLength(10, ErrorMessage = "地址长度不能超过10个字符")]
    public string Address { get; set; }

    /// <summary>
    /// 亮度 (0-100)
    /// </summary>
    [Range(0, 100, ErrorMessage = "亮度值必须在0-100之间")]
    public int Brightness { get; set; }

    /// <summary>
    /// 过渡时间 (毫秒)
    /// </summary>
    [Range(0, 60000, ErrorMessage = "过渡时间必须在0-60000毫秒之间")]
    public int TransitionTime { get; set; }

    /// <summary>
    /// 超时时间 (秒)
    /// </summary>
    [Range(1, 300, ErrorMessage = "超时时间必须在1-300秒之间")]
    public int TimeoutSeconds { get; set; } = 10;
}

/// <summary>
/// 群组控制请求
/// 用于批量控制群组设备的请求模型
/// </summary>
public class GroupControlRequest
{
    /// <summary>
    /// 群组ID
    /// </summary>
    [Required(ErrorMessage = "群组ID不能为空")]
    [StringLength(50, ErrorMessage = "群组ID长度不能超过50个字符")]
    public string GroupId { get; set; }

    /// <summary>
    /// 动作
    /// </summary>
    [Required(ErrorMessage = "控制动作不能为空")]
    public string Action { get; set; }

    /// <summary>
    /// 参数
    /// </summary>
    public string Parameters { get; set; }

    /// <summary>
    /// 超时时间 (秒)
    /// </summary>
    [Range(1, 300, ErrorMessage = "超时时间必须在1-300秒之间")]
    public int TimeoutSeconds { get; set; } = 15;
}

/// <summary>
/// 标签控制请求
/// 用于按标签批量控制设备的请求模型
/// </summary>
public class TagControlRequest
{
    /// <summary>
    /// 标签
    /// </summary>
    [Required(ErrorMessage = "标签不能为空")]
    [StringLength(50, ErrorMessage = "标签长度不能超过50个字符")]
    public string Tag { get; set; }

    /// <summary>
    /// 动作
    /// </summary>
    [Required(ErrorMessage = "控制动作不能为空")]
    public string Action { get; set; }

    /// <summary>
    /// 参数
    /// </summary>
    public string Parameters { get; set; }

    /// <summary>
    /// 超时时间 (秒)
    /// </summary>
    [Range(1, 300, ErrorMessage = "超时时间必须在1-300秒之间")]
    public int TimeoutSeconds { get; set; } = 15;
}

/// <summary>
/// 全区控制请求
/// 用于控制整个区域所有设备的请求模型
/// </summary>
public class AreaControlRequest
{
    /// <summary>
    /// 动作
    /// </summary>
    [Required(ErrorMessage = "控制动作不能为空")]
    public string Action { get; set; }

    /// <summary>
    /// 参数
    /// </summary>
    public string Parameters { get; set; }

    /// <summary>
    /// 超时时间 (秒)
    /// </summary>
    [Range(1, 300, ErrorMessage = "超时时间必须在1-300秒之间")]
    public int TimeoutSeconds { get; set; } = 20;
}

#endregion