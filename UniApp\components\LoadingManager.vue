<template>
  <view class="loading-manager">
    <!-- 全局Loading遮罩 -->
    <view v-if="globalLoading" class="global-loading-mask" @touchmove.prevent>
      <view class="loading-container">
        <view class="loading-spinner">
          <view class="spinner-dot" v-for="i in 8" :key="i" :style="{animationDelay: (i-1) * 0.1 + 's'}"></view>
        </view>
        <text class="loading-text">{{ loadingText }}</text>
      </view>
    </view>
    
    <!-- 页面级Loading -->
    <view v-if="pageLoading" class="page-loading">
      <view class="page-loading-content">
        <view class="loading-icon">
          <view class="pulse-ring"></view>
          <view class="pulse-dot"></view>
        </view>
        <text class="page-loading-text">{{ pageLoadingText }}</text>
      </view>
    </view>
    
    <!-- 局部Loading -->
    <view v-if="localLoading" class="local-loading" :style="localLoadingStyle">
      <view class="local-loading-content">
        <view class="mini-spinner"></view>
        <text class="local-loading-text">{{ localLoadingText }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'LoadingManager',
  data() {
    return {
      globalLoading: false,
      pageLoading: false,
      localLoading: false,
      loadingText: '加载中...',
      pageLoadingText: '页面加载中...',
      localLoadingText: '处理中...',
      localLoadingStyle: {},
      loadingQueue: [], // 加载队列
      networkStatus: 'good' // 网络状态
    }
  },
  mounted() {
    this.initNetworkMonitor()
  },
  methods: {
    // 显示全局Loading
    showGlobalLoading(text = '加载中...', options = {}) {
      this.loadingText = text
      this.globalLoading = true
      
      // 自动超时处理
      if (options.timeout) {
        setTimeout(() => {
          this.hideGlobalLoading()
        }, options.timeout)
      }
    },
    
    // 隐藏全局Loading
    hideGlobalLoading() {
      this.globalLoading = false
    },
    
    // 显示页面Loading
    showPageLoading(text = '页面加载中...') {
      this.pageLoadingText = text
      this.pageLoading = true
    },
    
    // 隐藏页面Loading
    hidePageLoading() {
      this.pageLoading = false
    },
    
    // 显示局部Loading
    showLocalLoading(options = {}) {
      const {
        text = '处理中...',
        position = {},
        size = 'normal'
      } = options
      
      this.localLoadingText = text
      this.localLoadingStyle = {
        ...position,
        fontSize: size === 'small' ? '24rpx' : '28rpx'
      }
      this.localLoading = true
    },
    
    // 隐藏局部Loading
    hideLocalLoading() {
      this.localLoading = false
    },
    
    // 智能Loading - 根据网络状况和数据大小选择合适的Loading方式
    showSmartLoading(options = {}) {
      const {
        type = 'auto',
        dataSize = 'small',
        text = '加载中...',
        timeout = 30000
      } = options
      
      let loadingType = type
      
      if (type === 'auto') {
        // 根据网络状况和数据大小自动选择
        if (this.networkStatus === 'slow' || dataSize === 'large') {
          loadingType = 'page'
        } else if (dataSize === 'medium') {
          loadingType = 'local'
        } else {
          loadingType = 'global'
        }
      }
      
      switch (loadingType) {
        case 'global':
          this.showGlobalLoading(text, { timeout })
          break
        case 'page':
          this.showPageLoading(text)
          break
        case 'local':
          this.showLocalLoading({ text })
          break
      }
      
      return loadingType
    },
    
    // 隐藏智能Loading
    hideSmartLoading(type) {
      switch (type) {
        case 'global':
          this.hideGlobalLoading()
          break
        case 'page':
          this.hidePageLoading()
          break
        case 'local':
          this.hideLocalLoading()
          break
      }
    },
    
    // 加载队列管理
    addToQueue(id, options) {
      this.loadingQueue.push({ id, options, startTime: Date.now() })
      this.processQueue()
    },
    
    removeFromQueue(id) {
      const index = this.loadingQueue.findIndex(item => item.id === id)
      if (index > -1) {
        this.loadingQueue.splice(index, 1)
      }
      
      if (this.loadingQueue.length === 0) {
        this.hideAllLoading()
      }
    },
    
    processQueue() {
      if (this.loadingQueue.length > 0) {
        const latest = this.loadingQueue[this.loadingQueue.length - 1]
        this.showSmartLoading(latest.options)
      }
    },
    
    // 隐藏所有Loading
    hideAllLoading() {
      this.hideGlobalLoading()
      this.hidePageLoading()
      this.hideLocalLoading()
    },
    
    // 网络监控
    initNetworkMonitor() {
      uni.onNetworkStatusChange((res) => {
        if (res.networkType === 'none') {
          this.networkStatus = 'none'
        } else if (res.networkType === '2g') {
          this.networkStatus = 'slow'
        } else if (res.networkType === '3g') {
          this.networkStatus = 'medium'
        } else {
          this.networkStatus = 'good'
        }
      })
      
      // 获取初始网络状态
      uni.getNetworkType({
        success: (res) => {
          if (res.networkType === 'none') {
            this.networkStatus = 'none'
          } else if (res.networkType === '2g') {
            this.networkStatus = 'slow'
          } else if (res.networkType === '3g') {
            this.networkStatus = 'medium'
          } else {
            this.networkStatus = 'good'
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.loading-manager {
  position: relative;
}

/* 全局Loading样式 */
.global-loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-container {
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 16rpx;
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  position: relative;
  margin-bottom: 30rpx;
}

.spinner-dot {
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  background-color: #007aff;
  border-radius: 50%;
  animation: spinner-fade 1.2s linear infinite;
}

.spinner-dot:nth-child(1) { top: 0; left: 50%; margin-left: -6rpx; }
.spinner-dot:nth-child(2) { top: 14rpx; right: 14rpx; }
.spinner-dot:nth-child(3) { top: 50%; right: 0; margin-top: -6rpx; }
.spinner-dot:nth-child(4) { bottom: 14rpx; right: 14rpx; }
.spinner-dot:nth-child(5) { bottom: 0; left: 50%; margin-left: -6rpx; }
.spinner-dot:nth-child(6) { bottom: 14rpx; left: 14rpx; }
.spinner-dot:nth-child(7) { top: 50%; left: 0; margin-top: -6rpx; }
.spinner-dot:nth-child(8) { top: 14rpx; left: 14rpx; }

@keyframes spinner-fade {
  0%, 39%, 100% { opacity: 0; }
  40% { opacity: 1; }
}

.loading-text {
  color: #ffffff;
  font-size: 28rpx;
}

/* 页面Loading样式 */
.page-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.page-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-icon {
  position: relative;
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 40rpx;
}

.pulse-ring {
  position: absolute;
  width: 100rpx;
  height: 100rpx;
  border: 4rpx solid #007aff;
  border-radius: 50%;
  animation: pulse-ring 1.5s ease-out infinite;
}

.pulse-dot {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20rpx;
  height: 20rpx;
  margin: -10rpx 0 0 -10rpx;
  background-color: #007aff;
  border-radius: 50%;
  animation: pulse-dot 1.5s ease-out infinite;
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

@keyframes pulse-dot {
  0% {
    transform: scale(0.8);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(0.8);
  }
}

.page-loading-text {
  color: #666666;
  font-size: 32rpx;
}

/* 局部Loading样式 */
.local-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 12rpx;
  padding: 30rpx 20rpx;
  z-index: 100;
}

.local-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.mini-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.local-loading-text {
  color: #ffffff;
  font-size: 24rpx;
}
</style>