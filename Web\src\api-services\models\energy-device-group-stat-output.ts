/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { GroupDeviceStatItem } from './group-device-stat-item';
import { GroupLevelStatItem } from './group-level-stat-item';
/**
 * 设备分组统计输出参数
 * @export
 * @interface EnergyDeviceGroupStatOutput
 */
export interface EnergyDeviceGroupStatOutput {
    /**
     * 分组总数
     * @type {number}
     * @memberof EnergyDeviceGroupStatOutput
     */
    totalCount?: number;
    /**
     * 启用分组数
     * @type {number}
     * @memberof EnergyDeviceGroupStatOutput
     */
    enabledCount?: number;
    /**
     * 禁用分组数
     * @type {number}
     * @memberof EnergyDeviceGroupStatOutput
     */
    disabledCount?: number;
    /**
     * 启用分组数
     * @type {number}
     * @memberof EnergyDeviceGroupStatOutput
     */
    enableCount?: number;
    /**
     * 禁用分组数
     * @type {number}
     * @memberof EnergyDeviceGroupStatOutput
     */
    disableCount?: number;
    /**
     * 根分组数量
     * @type {number}
     * @memberof EnergyDeviceGroupStatOutput
     */
    rootGroupCount?: number;
    /**
     * 子分组数量
     * @type {number}
     * @memberof EnergyDeviceGroupStatOutput
     */
    childGroupCount?: number;
    /**
     * 设备总数
     * @type {number}
     * @memberof EnergyDeviceGroupStatOutput
     */
    deviceTotalCount?: number;
    /**
     * 在线设备数
     * @type {number}
     * @memberof EnergyDeviceGroupStatOutput
     */
    deviceOnlineCount?: number;
    /**
     * 分组层级统计
     * @type {Array<GroupLevelStatItem>}
     * @memberof EnergyDeviceGroupStatOutput
     */
    levelStats?: Array<GroupLevelStatItem> | null;
    /**
     * 分组设备统计
     * @type {Array<GroupDeviceStatItem>}
     * @memberof EnergyDeviceGroupStatOutput
     */
    groupDeviceStats?: Array<GroupDeviceStatItem> | null;
}
