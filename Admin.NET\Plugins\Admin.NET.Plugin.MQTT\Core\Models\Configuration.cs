using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using static Admin.NET.Plugin.MQTT.Core.Models.DataTransferObjects;

namespace Admin.NET.Plugin.MQTT.Core.Models
{
    /// <summary>
    /// MQTT插件主配置类
    /// 包含插件运行所需的所有配置信息
    /// </summary>
    public class MqttPluginConfiguration
    {
        /// <summary>
        /// 客户端配置
        /// </summary>
        [Required]
        public MqttClientConfiguration Client { get; set; } = new MqttClientConfiguration();
        
        /// <summary>
        /// 消息路由器配置
        /// </summary>
        [Required]
        public MessageRouterConfiguration MessageRouter { get; set; } = new MessageRouterConfiguration();
        
        /// <summary>
        /// 设备控制配置
        /// </summary>
        [Required]
        public DeviceControlConfiguration DeviceControl { get; set; } = new DeviceControlConfiguration();
        
        /// <summary>
        /// 性能监控配置
        /// </summary>
        public PerformanceMonitoringConfiguration Performance { get; set; } = new PerformanceMonitoringConfiguration();
        
        /// <summary>
        /// 日志配置
        /// </summary>
        public LoggingConfiguration Logging { get; set; } = new LoggingConfiguration();
        
        /// <summary>
        /// 安全配置
        /// </summary>
        public SecurityConfiguration Security { get; set; } = new SecurityConfiguration();
        
        /// <summary>
        /// 重试策略配置
        /// </summary>
        public RetryPolicyConfiguration RetryPolicy { get; set; } = new RetryPolicyConfiguration();
        
        /// <summary>
        /// 缓存配置
        /// </summary>
        public CacheConfiguration Cache { get; set; } = new CacheConfiguration();
        
        /// <summary>
        /// 插件元数据
        /// </summary>
        public PluginMetadata Metadata { get; set; } = new PluginMetadata();
    }
    
    /// <summary>
    /// MQTT客户端配置
    /// </summary>
    public class MqttClientConfiguration
    {
        /// <summary>
        /// 服务器地址
        /// </summary>
        [Required]
        public string Server { get; set; } = "localhost";
        
        /// <summary>
        /// 服务器端口
        /// </summary>
        [Range(1, 65535)]
        public int Port { get; set; } = 1883;
        
        /// <summary>
        /// 客户端ID
        /// </summary>
        [Required]
        public string ClientId { get; set; } = Guid.NewGuid().ToString();
        
        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; }
        
        /// <summary>
        /// 密码
        /// </summary>
        public string Password { get; set; }
        
        /// <summary>
        /// 是否使用SSL/TLS
        /// </summary>
        public bool UseTls { get; set; } = false;
        
        /// <summary>
        /// 证书文件路径
        /// </summary>
        public string CertificatePath { get; set; }
        
        /// <summary>
        /// 私钥文件路径
        /// </summary>
        public string PrivateKeyPath { get; set; }
        
        /// <summary>
        /// CA证书文件路径
        /// </summary>
        public string CaCertificatePath { get; set; }
        
        /// <summary>
        /// 是否验证服务器证书
        /// </summary>
        public bool ValidateServerCertificate { get; set; } = true;
        
        /// <summary>
        /// 连接超时时间（秒）
        /// </summary>
        [Range(1, 300)]
        public int ConnectionTimeout { get; set; } = 30;
        
        /// <summary>
        /// 保活间隔（秒）
        /// </summary>
        [Range(10, 3600)]
        public int KeepAliveInterval { get; set; } = 60;
        
        /// <summary>
        /// 自动重连
        /// </summary>
        public bool AutoReconnect { get; set; } = true;
        
        /// <summary>
        /// 重连间隔（秒）
        /// </summary>
        [Range(1, 300)]
        public int ReconnectInterval { get; set; } = 5;
        
        /// <summary>
        /// 最大重连次数
        /// </summary>
        [Range(0, 100)]
        public int MaxReconnectAttempts { get; set; } = 10;
        
        /// <summary>
        /// 清理会话
        /// </summary>
        public bool CleanSession { get; set; } = true;
        
        /// <summary>
        /// 遗嘱主题
        /// </summary>
        public string WillTopic { get; set; }
        
        /// <summary>
        /// 遗嘱消息
        /// </summary>
        public string WillMessage { get; set; }
        
        /// <summary>
        /// 遗嘱QoS
        /// </summary>
        [Range(0, 2)]
        public int WillQos { get; set; } = 0;
        
        /// <summary>
        /// 遗嘱保留标志
        /// </summary>
        public bool WillRetain { get; set; } = false;
        
        /// <summary>
        /// 心跳主题
        /// </summary>
        public string HeartbeatTopic { get; set; } = "heartbeat";
        
        /// <summary>
        /// 心跳间隔（秒）
        /// </summary>
        [Range(10, 3600)]
        public int HeartbeatInterval { get; set; } = 30;
        
        /// <summary>
        /// 是否启用心跳
        /// </summary>
        public bool EnableHeartbeat { get; set; } = true;
    }
    
    /// <summary>
    /// 消息路由器配置
    /// </summary>
    public class MessageRouterConfiguration
    {
        /// <summary>
        /// 消息队列容量
        /// </summary>
        [Range(100, 100000)]
        public int QueueCapacity { get; set; } = 10000;
        
        /// <summary>
        /// 最大并发处理数
        /// </summary>
        [Range(1, 1000)]
        public int MaxConcurrentHandlers { get; set; } = 100;
        
        /// <summary>
        /// 处理超时时间（毫秒）
        /// </summary>
        [Range(100, 60000)]
        public int HandlerTimeout { get; set; } = 5000;
        
        /// <summary>
        /// 是否启用消息缓冲
        /// </summary>
        public bool EnableMessageBuffering { get; set; } = true;
        
        /// <summary>
        /// 缓冲区大小
        /// </summary>
        [Range(1024, 1048576)]
        public int BufferSize { get; set; } = 65536;
        
        /// <summary>
        /// 是否启用消息压缩
        /// </summary>
        public bool EnableCompression { get; set; } = false;
        
        /// <summary>
        /// 压缩级别
        /// </summary>
        [Range(1, 9)]
        public int CompressionLevel { get; set; } = 6;
        
        /// <summary>
        /// 消息过期时间（秒）
        /// </summary>
        [Range(60, 86400)]
        public int MessageExpirationTime { get; set; } = 3600;
        
        /// <summary>
        /// 是否启用消息去重
        /// </summary>
        public bool EnableDeduplication { get; set; } = true;
        
        /// <summary>
        /// 去重窗口大小（秒）
        /// </summary>
        [Range(10, 3600)]
        public int DeduplicationWindow { get; set; } = 300;
        
        /// <summary>
        /// 统计收集间隔（秒）
        /// </summary>
        [Range(10, 3600)]
        public int MetricsCollectionInterval { get; set; } = 60;
    }
    
    /// <summary>
    /// 设备控制配置
    /// </summary>
    public class DeviceControlConfiguration
    {
        /// <summary>
        /// 扫描超时时间（秒）
        /// </summary>
        [Range(5, 300)]
        public int ScanTimeout { get; set; } = 30;
        
        /// <summary>
        /// 控制超时时间（秒）
        /// </summary>
        [Range(1, 60)]
        public int ControlTimeout { get; set; } = 10;
        
        /// <summary>
        /// 最大并发扫描数
        /// </summary>
        [Range(1, 100)]
        public int MaxConcurrentScans { get; set; } = 10;
        
        /// <summary>
        /// 最大并发控制数
        /// </summary>
        [Range(1, 1000)]
        public int MaxConcurrentControls { get; set; } = 50;
        
        /// <summary>
        /// 设备状态缓存时间（秒）
        /// </summary>
        [Range(10, 3600)]
        public int DeviceStatusCacheTime { get; set; } = 300;
        
        /// <summary>
        /// 扫描结果缓存时间（秒）
        /// </summary>
        [Range(60, 86400)]
        public int ScanResultCacheTime { get; set; } = 1800;
        
        /// <summary>
        /// 设备信标主题模式
        /// </summary>
        public string DeviceBeaconTopicPattern { get; set; } = "device/+/beacon";
        
        /// <summary>
        /// 设备控制主题模式
        /// </summary>
        public string DeviceControlTopicPattern { get; set; } = "device/+/control";
        
        /// <summary>
        /// 设备状态主题模式
        /// </summary>
        public string DeviceStatusTopicPattern { get; set; } = "device/+/status";
        
        /// <summary>
        /// 扫描主题
        /// </summary>
        public string ScanTopic { get; set; } = "scan/request";
        
        /// <summary>
        /// 是否启用设备发现
        /// </summary>
        public bool EnableDeviceDiscovery { get; set; } = true;
        
        /// <summary>
        /// 设备发现间隔（秒）
        /// </summary>
        [Range(30, 3600)]
        public int DeviceDiscoveryInterval { get; set; } = 300;
    }
    
    /// <summary>
    /// 性能监控配置
    /// </summary>
    public class PerformanceMonitoringConfiguration
    {
        /// <summary>
        /// 是否启用性能监控
        /// </summary>
        public bool Enabled { get; set; } = true;
        
        /// <summary>
        /// 监控间隔（秒）
        /// </summary>
        [Range(10, 3600)]
        public int MonitoringInterval { get; set; } = 60;
        
        /// <summary>
        /// 是否启用内存监控
        /// </summary>
        public bool EnableMemoryMonitoring { get; set; } = true;
        
        /// <summary>
        /// 是否启用CPU监控
        /// </summary>
        public bool EnableCpuMonitoring { get; set; } = true;
        
        /// <summary>
        /// 是否启用网络监控
        /// </summary>
        public bool EnableNetworkMonitoring { get; set; } = true;
        
        /// <summary>
        /// 性能数据保留时间（小时）
        /// </summary>
        [Range(1, 168)]
        public int DataRetentionHours { get; set; } = 24;
        
        /// <summary>
        /// 内存使用警告阈值（MB）
        /// </summary>
        [Range(100, 10240)]
        public int MemoryWarningThreshold { get; set; } = 512;
        
        /// <summary>
        /// CPU使用警告阈值（百分比）
        /// </summary>
        [Range(50, 95)]
        public int CpuWarningThreshold { get; set; } = 80;
        
        /// <summary>
        /// 是否启用垃圾回收监控
        /// </summary>
        public bool EnableGcMonitoring { get; set; } = true;
        
        /// <summary>
        /// 垃圾回收触发阈值（MB）
        /// </summary>
        [Range(100, 2048)]
        public int GcTriggerThreshold { get; set; } = 256;
    }
    
    /// <summary>
    /// 日志配置
    /// </summary>
    public class LoggingConfiguration
    {
        /// <summary>
        /// 日志级别
        /// </summary>
        public LogLevel LogLevel { get; set; } = LogLevel.Information;
        
        /// <summary>
        /// 是否启用文件日志
        /// </summary>
        public bool EnableFileLogging { get; set; } = true;
        
        /// <summary>
        /// 日志文件路径
        /// </summary>
        public string LogFilePath { get; set; } = "logs/mqtt-plugin.log";
        
        /// <summary>
        /// 日志文件最大大小（MB）
        /// </summary>
        [Range(1, 1024)]
        public int MaxLogFileSize { get; set; } = 100;
        
        /// <summary>
        /// 保留的日志文件数量
        /// </summary>
        [Range(1, 100)]
        public int RetainedLogFiles { get; set; } = 10;
        
        /// <summary>
        /// 是否启用控制台日志
        /// </summary>
        public bool EnableConsoleLogging { get; set; } = true;
        
        /// <summary>
        /// 是否启用结构化日志
        /// </summary>
        public bool EnableStructuredLogging { get; set; } = true;
        
        /// <summary>
        /// 日志格式
        /// </summary>
        public string LogFormat { get; set; } = "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}";
        
        /// <summary>
        /// 是否记录敏感信息
        /// </summary>
        public bool LogSensitiveData { get; set; } = false;
    }
    
    /// <summary>
    /// 安全配置
    /// </summary>
    public class SecurityConfiguration
    {
        /// <summary>
        /// 是否启用消息加密
        /// </summary>
        public bool EnableMessageEncryption { get; set; } = false;
        
        /// <summary>
        /// 加密算法
        /// </summary>
        public string EncryptionAlgorithm { get; set; } = "AES-256-GCM";
        
        /// <summary>
        /// 加密密钥
        /// </summary>
        public string EncryptionKey { get; set; }
        
        /// <summary>
        /// 是否启用消息签名
        /// </summary>
        public bool EnableMessageSigning { get; set; } = false;
        
        /// <summary>
        /// 签名算法
        /// </summary>
        public string SigningAlgorithm { get; set; } = "HMAC-SHA256";
        
        /// <summary>
        /// 签名密钥
        /// </summary>
        public string SigningKey { get; set; }
        
        /// <summary>
        /// 允许的主题模式列表
        /// </summary>
        public List<string> AllowedTopicPatterns { get; set; } = new List<string>();
        
        /// <summary>
        /// 禁止的主题模式列表
        /// </summary>
        public List<string> DeniedTopicPatterns { get; set; } = new List<string>();
        
        /// <summary>
        /// 是否启用访问控制
        /// </summary>
        public bool EnableAccessControl { get; set; } = false;
        
        /// <summary>
        /// 访问控制规则
        /// </summary>
        public List<AccessControlRule> AccessControlRules { get; set; } = new List<AccessControlRule>();
    }
    
    /// <summary>
    /// 重试策略配置
    /// </summary>
    public class RetryPolicyConfiguration
    {
        /// <summary>
        /// 默认重试策略类型
        /// </summary>
        public RetryPolicyType DefaultPolicyType { get; set; } = RetryPolicyType.ExponentialBackoff;
        
        /// <summary>
        /// 最大重试次数
        /// </summary>
        [Range(0, 20)]
        public int MaxRetryAttempts { get; set; } = 3;
        
        /// <summary>
        /// 基础延迟时间（毫秒）
        /// </summary>
        [Range(100, 10000)]
        public int BaseDelay { get; set; } = 1000;
        
        /// <summary>
        /// 最大延迟时间（毫秒）
        /// </summary>
        [Range(1000, 60000)]
        public int MaxDelay { get; set; } = 30000;
        
        /// <summary>
        /// 指数退避倍数
        /// </summary>
        [Range(1.1, 5.0)]
        public double BackoffMultiplier { get; set; } = 2.0;
        
        /// <summary>
        /// 抖动因子
        /// </summary>
        [Range(0.0, 1.0)]
        public double JitterFactor { get; set; } = 0.1;
        
        /// <summary>
        /// 可重试的异常类型
        /// </summary>
        public List<string> RetryableExceptions { get; set; } = new List<string>
        {
            "System.Net.Sockets.SocketException",
            "System.TimeoutException",
            "MQTTnet.Exceptions.MqttCommunicationException"
        };
    }
    
    /// <summary>
    /// 缓存配置
    /// </summary>
    public class CacheConfiguration
    {
        /// <summary>
        /// 是否启用缓存
        /// </summary>
        public bool Enabled { get; set; } = true;
        
        /// <summary>
        /// 缓存提供者类型
        /// </summary>
        public CacheProviderType ProviderType { get; set; } = CacheProviderType.Memory;
        
        /// <summary>
        /// Redis连接字符串
        /// </summary>
        public string RedisConnectionString { get; set; }
        
        /// <summary>
        /// 默认过期时间（秒）
        /// </summary>
        [Range(60, 86400)]
        public int DefaultExpirationTime { get; set; } = 3600;
        
        /// <summary>
        /// 最大缓存大小（MB）
        /// </summary>
        [Range(10, 1024)]
        public int MaxCacheSize { get; set; } = 100;
        
        /// <summary>
        /// 缓存键前缀
        /// </summary>
        public string KeyPrefix { get; set; } = "mqtt:plugin:";
        
        /// <summary>
        /// 是否启用缓存压缩
        /// </summary>
        public bool EnableCompression { get; set; } = false;
    }
    
    /// <summary>
    /// 插件元数据
    /// </summary>
    public class PluginMetadata
    {
        /// <summary>
        /// 插件名称
        /// </summary>
        public string Name { get; set; } = "MQTT Plugin";
        
        /// <summary>
        /// 插件版本
        /// </summary>
        public string Version { get; set; } = "1.0.0";
        
        /// <summary>
        /// 插件描述
        /// </summary>
        public string Description { get; set; } = "高性能MQTT客户端插件";
        
        /// <summary>
        /// 插件作者
        /// </summary>
        public string Author { get; set; } = "Admin.NET Team";
        
        /// <summary>
        /// 插件主页
        /// </summary>
        public string Homepage { get; set; }
        
        /// <summary>
        /// 许可证
        /// </summary>
        public string License { get; set; } = "MIT";
        
        /// <summary>
        /// 标签列表
        /// </summary>
        public List<string> Tags { get; set; } = new List<string> { "mqtt", "iot", "messaging" };
        
        /// <summary>
        /// 最小框架版本
        /// </summary>
        public string MinFrameworkVersion { get; set; } = "6.0";
    }
    
    /// <summary>
    /// 访问控制规则
    /// </summary>
    public class AccessControlRule
    {
        /// <summary>
        /// 规则名称
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 主题模式
        /// </summary>
        public string TopicPattern { get; set; }
        
        /// <summary>
        /// 操作类型
        /// </summary>
        public AccessOperation Operation { get; set; }
        
        /// <summary>
        /// 是否允许
        /// </summary>
        public bool Allow { get; set; }
        
        /// <summary>
        /// 用户角色
        /// </summary>
        public List<string> Roles { get; set; } = new List<string>();
        
        /// <summary>
        /// 用户列表
        /// </summary>
        public List<string> Users { get; set; } = new List<string>();
    }
    
    /// <summary>
    /// 日志级别枚举
    /// </summary>
    public enum LogLevel
    {
        /// <summary>
        /// 跟踪
        /// </summary>
        Trace,
        
        /// <summary>
        /// 调试
        /// </summary>
        Debug,
        
        /// <summary>
        /// 信息
        /// </summary>
        Information,
        
        /// <summary>
        /// 警告
        /// </summary>
        Warning,
        
        /// <summary>
        /// 错误
        /// </summary>
        Error,
        
        /// <summary>
        /// 严重错误
        /// </summary>
        Critical,
        
        /// <summary>
        /// 无日志
        /// </summary>
        None
    }
    
    /// <summary>
    /// 缓存提供者类型枚举
    /// </summary>
    public enum CacheProviderType
    {
        /// <summary>
        /// 内存缓存
        /// </summary>
        Memory,
        
        /// <summary>
        /// Redis缓存
        /// </summary>
        Redis,
        
        /// <summary>
        /// 分布式缓存
        /// </summary>
        Distributed
    }
    
    /// <summary>
    /// 访问操作枚举
    /// </summary>
    public enum AccessOperation
    {
        /// <summary>
        /// 读取
        /// </summary>
        Read,
        
        /// <summary>
        /// 写入
        /// </summary>
        Write,
        
        /// <summary>
        /// 订阅
        /// </summary>
        Subscribe,
        
        /// <summary>
        /// 发布
        /// </summary>
        Publish,
        
        /// <summary>
        /// 全部
        /// </summary>
        All
    }
}