import { service as request } from '/@/utils/request';
import { useBaseApi } from '../base';

/**
 * 设备管理API接口集合
 * @method getPage 获取设备分页列表
 * @method getList 获取设备列表
 * @method getDetail 获取设备详情
 * @method add 添加设备
 * @method update 更新设备
 * @method delete 删除设备
 * @method control 控制设备
 * @method setStatus 设置设备状态
 * @method getStats 获取设备统计
 * @method getTypeDistribution 获取设备类型分布
 * @method exportData 导出设备数据
 */
export function useDeviceApi() {
	const baseApi = useBaseApi('energyDevice');
	return {
		...baseApi,
		
		// 获取设备分页列表
		getPage: (params: any) => {
			return request({
				url: '/api/energyDevice/page',
				method: 'get',
				params,
			});
		},
		
		// 获取设备列表
		getList: (params?: any) => {
			return request({
				url: '/api/energyDevice/list',
				method: 'get',
				params,
			});
		},
		
		// 获取设备详情
		getDetail: (id: string) => {
			return request({
				url: `/api/energyDevice/detail/${id}`,
				method: 'get',
			});
		},
		
		// 控制设备
		control: (data: any) => {
			return request({
				url: '/api/energyDevice/control',
				method: 'post',
				data,
			});
		},
		
		// 设置设备状态
		setStatus: (data: any) => {
			return request({
				url: '/api/energyDevice/setStatus',
				method: 'post',
				data,
			});
		},
		
		// 获取设备统计
		getStats: () => {
			return request({
				url: '/api/energyDevice/stats',
				method: 'get',
			});
		},
		
		// 获取设备类型分布
		getTypeDistribution: () => {
			return request({
				url: '/api/energyDevice/typeDistribution',
				method: 'get',
			});
		},
		
		// 导出设备数据
		exportData: (params: any) => {
			return request({
				url: '/api/energyDevice/export',
				method: 'get',
				params,
				responseType: 'blob',
			});
		},
	};
}