/**
 * 全局加载状态管理器
 * 提供统一的loading状态控制、队列管理和多种loading类型支持
 */

class LoadingManager {
  constructor() {
    // 加载队列，支持多个并发loading
    this.loadingQueue = new Map()
    // 全局loading状态
    this.globalLoading = false
    // 默认配置
    this.defaultConfig = {
      type: 'spinner', // spinner, skeleton, custom
      text: '加载中...',
      mask: true,
      delay: 0, // 延迟显示时间
      minDuration: 300, // 最小显示时间
      timeout: 30000, // 超时时间
      position: 'center' // center, top, bottom
    }
    // 当前显示的loading实例
    this.currentLoading = null
    // 超时定时器
    this.timeoutTimer = null
    // 最小显示时间定时器
    this.minDurationTimer = null
  }

  /**
   * 显示loading
   * @param {string|Object} options - loading配置或文本
   * @returns {string} loadingId
   */
  show(options = {}) {
    // 处理参数
    if (typeof options === 'string') {
      options = { text: options }
    }
    
    const config = { ...this.defaultConfig, ...options }
    const loadingId = this.generateId()
    
    // 添加到队列
    this.loadingQueue.set(loadingId, {
      ...config,
      id: loadingId,
      startTime: Date.now(),
      visible: false
    })
    
    // 延迟显示
    if (config.delay > 0) {
      setTimeout(() => {
        this.displayLoading(loadingId)
      }, config.delay)
    } else {
      this.displayLoading(loadingId)
    }
    
    return loadingId
  }

  /**
   * 隐藏loading
   * @param {string} loadingId - loading ID
   */
  hide(loadingId) {
    if (!loadingId || !this.loadingQueue.has(loadingId)) {
      return
    }
    
    const loadingItem = this.loadingQueue.get(loadingId)
    const elapsed = Date.now() - loadingItem.startTime
    const remaining = Math.max(0, loadingItem.minDuration - elapsed)
    
    if (remaining > 0) {
      // 等待最小显示时间
      setTimeout(() => {
        this.removeLoading(loadingId)
      }, remaining)
    } else {
      this.removeLoading(loadingId)
    }
  }

  /**
   * 隐藏所有loading
   */
  hideAll() {
    const loadingIds = Array.from(this.loadingQueue.keys())
    loadingIds.forEach(id => this.hide(id))
  }

  /**
   * 显示全局loading
   * @param {Object} options - 配置选项
   */
  showGlobal(options = {}) {
    const config = {
      type: 'spinner',
      text: '加载中...',
      mask: true,
      ...options
    }
    
    this.globalLoading = true
    this.currentLoading = config
    
    // 使用uni-app的loading
    if (config.type === 'spinner') {
      uni.showLoading({
        title: config.text,
        mask: config.mask
      })
    }
    
    // 设置超时
    if (config.timeout > 0) {
      this.timeoutTimer = setTimeout(() => {
        this.hideGlobal()
        console.warn('Loading timeout:', config.timeout + 'ms')
      }, config.timeout)
    }
    
    return 'global'
  }

  /**
   * 隐藏全局loading
   */
  hideGlobal() {
    if (!this.globalLoading) {
      return
    }
    
    this.globalLoading = false
    this.currentLoading = null
    
    // 清除超时定时器
    if (this.timeoutTimer) {
      clearTimeout(this.timeoutTimer)
      this.timeoutTimer = null
    }
    
    // 隐藏uni-app的loading
    uni.hideLoading()
  }

  /**
   * 显示骨架屏loading
   * @param {Object} options - 骨架屏配置
   */
  showSkeleton(options = {}) {
    const config = {
      type: 'skeleton',
      skeletonType: 'text',
      ...options
    }
    
    return this.show(config)
  }

  /**
   * 显示进度loading
   * @param {Object} options - 进度配置
   */
  showProgress(options = {}) {
    const config = {
      type: 'progress',
      progress: 0,
      showProgress: true,
      ...options
    }
    
    return this.show(config)
  }

  /**
   * 更新进度
   * @param {string} loadingId - loading ID
   * @param {number} progress - 进度值 0-100
   */
  updateProgress(loadingId, progress) {
    if (!this.loadingQueue.has(loadingId)) {
      return
    }
    
    const loadingItem = this.loadingQueue.get(loadingId)
    loadingItem.progress = Math.max(0, Math.min(100, progress))
    
    // 触发进度更新事件
    this.emitProgressUpdate(loadingId, loadingItem.progress)
  }

  /**
   * 获取loading状态
   * @param {string} loadingId - loading ID
   */
  getStatus(loadingId) {
    if (loadingId === 'global') {
      return {
        visible: this.globalLoading,
        config: this.currentLoading
      }
    }
    
    if (!this.loadingQueue.has(loadingId)) {
      return null
    }
    
    return this.loadingQueue.get(loadingId)
  }

  /**
   * 检查是否有loading在显示
   */
  hasLoading() {
    return this.globalLoading || this.loadingQueue.size > 0
  }

  /**
   * 获取所有loading状态
   */
  getAllStatus() {
    const status = {
      global: this.globalLoading,
      queue: Array.from(this.loadingQueue.values()),
      total: this.loadingQueue.size
    }
    
    return status
  }

  /**
   * 显示loading实例
   * @param {string} loadingId - loading ID
   */
  displayLoading(loadingId) {
    if (!this.loadingQueue.has(loadingId)) {
      return
    }
    
    const loadingItem = this.loadingQueue.get(loadingId)
    loadingItem.visible = true
    
    // 如果是第一个loading，显示全局loading
    if (this.loadingQueue.size === 1 && !this.globalLoading) {
      this.showGlobal({
        type: loadingItem.type,
        text: loadingItem.text,
        mask: loadingItem.mask
      })
    }
  }

  /**
   * 移除loading实例
   * @param {string} loadingId - loading ID
   */
  removeLoading(loadingId) {
    if (!this.loadingQueue.has(loadingId)) {
      return
    }
    
    this.loadingQueue.delete(loadingId)
    
    // 如果队列为空，隐藏全局loading
    if (this.loadingQueue.size === 0) {
      this.hideGlobal()
    }
  }

  /**
   * 生成唯一ID
   */
  generateId() {
    return 'loading_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }

  /**
   * 触发进度更新事件
   * @param {string} loadingId - loading ID
   * @param {number} progress - 进度值
   */
  emitProgressUpdate(loadingId, progress) {
    // 可以在这里添加事件系统
    console.log(`Loading ${loadingId} progress: ${progress}%`)
  }

  /**
   * 清理资源
   */
  destroy() {
    this.hideAll()
    this.hideGlobal()
    
    if (this.timeoutTimer) {
      clearTimeout(this.timeoutTimer)
      this.timeoutTimer = null
    }
    
    if (this.minDurationTimer) {
      clearTimeout(this.minDurationTimer)
      this.minDurationTimer = null
    }
    
    this.loadingQueue.clear()
  }
}

// 创建全局实例
const loadingManager = new LoadingManager()

// 便捷方法
export const Loading = {
  // 显示loading
  show: (options) => loadingManager.show(options),
  
  // 隐藏loading
  hide: (loadingId) => loadingManager.hide(loadingId),
  
  // 隐藏所有loading
  hideAll: () => loadingManager.hideAll(),
  
  // 全局loading
  showGlobal: (options) => loadingManager.showGlobal(options),
  hideGlobal: () => loadingManager.hideGlobal(),
  
  // 骨架屏loading
  showSkeleton: (options) => loadingManager.showSkeleton(options),
  
  // 进度loading
  showProgress: (options) => loadingManager.showProgress(options),
  updateProgress: (loadingId, progress) => loadingManager.updateProgress(loadingId, progress),
  
  // 状态查询
  getStatus: (loadingId) => loadingManager.getStatus(loadingId),
  hasLoading: () => loadingManager.hasLoading(),
  getAllStatus: () => loadingManager.getAllStatus()
}

// 导出管理器实例
export default loadingManager

// Vue 3 插件形式
export const LoadingPlugin = {
  install(app) {
    app.config.globalProperties.$loading = Loading
    app.provide('loading', Loading)
  }
}

// 装饰器形式的loading
export function withLoading(options = {}) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = async function(...args) {
      const loadingId = Loading.show(options)
      
      try {
        const result = await originalMethod.apply(this, args)
        return result
      } finally {
        Loading.hide(loadingId)
      }
    }
    
    return descriptor
  }
}

// Promise包装器
export function wrapWithLoading(promise, options = {}) {
  const loadingId = Loading.show(options)
  
  return promise
    .finally(() => {
      Loading.hide(loadingId)
    })
}

// 请求拦截器辅助函数
export const createLoadingInterceptor = (options = {}) => {
  const activeRequests = new Set()
  
  return {
    request: (config) => {
      const requestId = config.url + '_' + Date.now()
      activeRequests.add(requestId)
      
      if (activeRequests.size === 1) {
        Loading.showGlobal(options)
      }
      
      config.requestId = requestId
      return config
    },
    
    response: (response) => {
      if (response.config.requestId) {
        activeRequests.delete(response.config.requestId)
      }
      
      if (activeRequests.size === 0) {
        Loading.hideGlobal()
      }
      
      return response
    },
    
    error: (error) => {
      if (error.config && error.config.requestId) {
        activeRequests.delete(error.config.requestId)
      }
      
      if (activeRequests.size === 0) {
        Loading.hideGlobal()
      }
      
      return Promise.reject(error)
    }
  }
}