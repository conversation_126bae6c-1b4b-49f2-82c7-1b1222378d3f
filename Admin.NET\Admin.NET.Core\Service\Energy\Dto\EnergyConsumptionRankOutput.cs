// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core.Service;

/// <summary>
/// 能耗排行输出参数
/// </summary>
public class EnergyConsumptionRankOutput
{
    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string DeviceName { get; set; } = string.Empty;

    /// <summary>
    /// 设备编号
    /// </summary>
    public string DeviceCode { get; set; } = string.Empty;

    /// <summary>
    /// 设备类型
    /// </summary>
    public string DeviceType { get; set; } = string.Empty;

    /// <summary>
    /// 分组名称
    /// </summary>
    public string? GroupName { get; set; }

    /// <summary>
    /// 排名
    /// </summary>
    public int Rank { get; set; }

    /// <summary>
    /// 能耗值(kWh)
    /// </summary>
    public decimal EnergyValue { get; set; }

    /// <summary>
    /// 占比(%)
    /// </summary>
    public decimal Percentage { get; set; }

    /// <summary>
    /// 统计时间段
    /// </summary>
    public string TimePeriod { get; set; } = string.Empty;

    /// <summary>
    /// 同比增长率(%)
    /// </summary>
    public decimal? GrowthRate { get; set; }

    /// <summary>
    /// 平均功率(W)
    /// </summary>
    public decimal? AvgPower { get; set; }

    /// <summary>
    /// 运行时长(小时)
    /// </summary>
    public decimal? RunningHours { get; set; }

    /// <summary>
    /// 设备位置
    /// </summary>
    public string? DeviceLocation { get; set; }

    /// <summary>
    /// 总能耗(kWh)
    /// </summary>
    public decimal TotalEnergyConsumption { get; set; }

    /// <summary>
    /// 平均功率(W) - 别名
    /// </summary>
    public decimal? AveragePower { get; set; }

    /// <summary>
    /// 运行时长 - 别名
    /// </summary>
    public decimal? RunningTime { get; set; }

    /// <summary>
    /// 总费用(元)
    /// </summary>
    public decimal TotalCost { get; set; }
}