/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
/**
 * SysLangTextCacheApi - axios parameter creator
 * @export
 */
export const SysLangTextCacheApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 删除缓存
         * @param {string} entityName 
         * @param {string} fieldName 
         * @param {number} entityId 
         * @param {string} langCode 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSysLangTextCacheCacheEntityNameFieldNameEntityIdLangCodeDelete: async (entityName: string, fieldName: string, entityId: number, langCode: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'entityName' is not null or undefined
            if (entityName === null || entityName === undefined) {
                throw new RequiredError('entityName','Required parameter entityName was null or undefined when calling apiSysLangTextCacheCacheEntityNameFieldNameEntityIdLangCodeDelete.');
            }
            // verify required parameter 'fieldName' is not null or undefined
            if (fieldName === null || fieldName === undefined) {
                throw new RequiredError('fieldName','Required parameter fieldName was null or undefined when calling apiSysLangTextCacheCacheEntityNameFieldNameEntityIdLangCodeDelete.');
            }
            // verify required parameter 'entityId' is not null or undefined
            if (entityId === null || entityId === undefined) {
                throw new RequiredError('entityId','Required parameter entityId was null or undefined when calling apiSysLangTextCacheCacheEntityNameFieldNameEntityIdLangCodeDelete.');
            }
            // verify required parameter 'langCode' is not null or undefined
            if (langCode === null || langCode === undefined) {
                throw new RequiredError('langCode','Required parameter langCode was null or undefined when calling apiSysLangTextCacheCacheEntityNameFieldNameEntityIdLangCodeDelete.');
            }
            const localVarPath = `/api/sysLangTextCache/cache/{entityName}/{fieldName}/{entityId}/{langCode}`
                .replace(`{${"entityName"}}`, encodeURIComponent(String(entityName)))
                .replace(`{${"fieldName"}}`, encodeURIComponent(String(fieldName)))
                .replace(`{${"entityId"}}`, encodeURIComponent(String(entityId)))
                .replace(`{${"langCode"}}`, encodeURIComponent(String(langCode)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新缓存
         * @param {string} entityName 
         * @param {string} fieldName 
         * @param {number} entityId 
         * @param {string} langCode 
         * @param {string} newValue 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSysLangTextCacheCacheEntityNameFieldNameEntityIdLangCodeNewValuePut: async (entityName: string, fieldName: string, entityId: number, langCode: string, newValue: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'entityName' is not null or undefined
            if (entityName === null || entityName === undefined) {
                throw new RequiredError('entityName','Required parameter entityName was null or undefined when calling apiSysLangTextCacheCacheEntityNameFieldNameEntityIdLangCodeNewValuePut.');
            }
            // verify required parameter 'fieldName' is not null or undefined
            if (fieldName === null || fieldName === undefined) {
                throw new RequiredError('fieldName','Required parameter fieldName was null or undefined when calling apiSysLangTextCacheCacheEntityNameFieldNameEntityIdLangCodeNewValuePut.');
            }
            // verify required parameter 'entityId' is not null or undefined
            if (entityId === null || entityId === undefined) {
                throw new RequiredError('entityId','Required parameter entityId was null or undefined when calling apiSysLangTextCacheCacheEntityNameFieldNameEntityIdLangCodeNewValuePut.');
            }
            // verify required parameter 'langCode' is not null or undefined
            if (langCode === null || langCode === undefined) {
                throw new RequiredError('langCode','Required parameter langCode was null or undefined when calling apiSysLangTextCacheCacheEntityNameFieldNameEntityIdLangCodeNewValuePut.');
            }
            // verify required parameter 'newValue' is not null or undefined
            if (newValue === null || newValue === undefined) {
                throw new RequiredError('newValue','Required parameter newValue was null or undefined when calling apiSysLangTextCacheCacheEntityNameFieldNameEntityIdLangCodeNewValuePut.');
            }
            const localVarPath = `/api/sysLangTextCache/cache/{entityName}/{fieldName}/{entityId}/{langCode}/{newValue}`
                .replace(`{${"entityName"}}`, encodeURIComponent(String(entityName)))
                .replace(`{${"fieldName"}}`, encodeURIComponent(String(fieldName)))
                .replace(`{${"entityId"}}`, encodeURIComponent(String(entityId)))
                .replace(`{${"langCode"}}`, encodeURIComponent(String(langCode)))
                .replace(`{${"newValue"}}`, encodeURIComponent(String(newValue)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * SysLangTextCacheApi - functional programming interface
 * @export
 */
export const SysLangTextCacheApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 删除缓存
         * @param {string} entityName 
         * @param {string} fieldName 
         * @param {number} entityId 
         * @param {string} langCode 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysLangTextCacheCacheEntityNameFieldNameEntityIdLangCodeDelete(entityName: string, fieldName: string, entityId: number, langCode: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await SysLangTextCacheApiAxiosParamCreator(configuration).apiSysLangTextCacheCacheEntityNameFieldNameEntityIdLangCodeDelete(entityName, fieldName, entityId, langCode, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新缓存
         * @param {string} entityName 
         * @param {string} fieldName 
         * @param {number} entityId 
         * @param {string} langCode 
         * @param {string} newValue 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysLangTextCacheCacheEntityNameFieldNameEntityIdLangCodeNewValuePut(entityName: string, fieldName: string, entityId: number, langCode: string, newValue: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await SysLangTextCacheApiAxiosParamCreator(configuration).apiSysLangTextCacheCacheEntityNameFieldNameEntityIdLangCodeNewValuePut(entityName, fieldName, entityId, langCode, newValue, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * SysLangTextCacheApi - factory interface
 * @export
 */
export const SysLangTextCacheApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 删除缓存
         * @param {string} entityName 
         * @param {string} fieldName 
         * @param {number} entityId 
         * @param {string} langCode 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysLangTextCacheCacheEntityNameFieldNameEntityIdLangCodeDelete(entityName: string, fieldName: string, entityId: number, langCode: string, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return SysLangTextCacheApiFp(configuration).apiSysLangTextCacheCacheEntityNameFieldNameEntityIdLangCodeDelete(entityName, fieldName, entityId, langCode, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新缓存
         * @param {string} entityName 
         * @param {string} fieldName 
         * @param {number} entityId 
         * @param {string} langCode 
         * @param {string} newValue 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysLangTextCacheCacheEntityNameFieldNameEntityIdLangCodeNewValuePut(entityName: string, fieldName: string, entityId: number, langCode: string, newValue: string, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return SysLangTextCacheApiFp(configuration).apiSysLangTextCacheCacheEntityNameFieldNameEntityIdLangCodeNewValuePut(entityName, fieldName, entityId, langCode, newValue, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * SysLangTextCacheApi - object-oriented interface
 * @export
 * @class SysLangTextCacheApi
 * @extends {BaseAPI}
 */
export class SysLangTextCacheApi extends BaseAPI {
    /**
     * 
     * @summary 删除缓存
     * @param {string} entityName 
     * @param {string} fieldName 
     * @param {number} entityId 
     * @param {string} langCode 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysLangTextCacheApi
     */
    public async apiSysLangTextCacheCacheEntityNameFieldNameEntityIdLangCodeDelete(entityName: string, fieldName: string, entityId: number, langCode: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return SysLangTextCacheApiFp(this.configuration).apiSysLangTextCacheCacheEntityNameFieldNameEntityIdLangCodeDelete(entityName, fieldName, entityId, langCode, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新缓存
     * @param {string} entityName 
     * @param {string} fieldName 
     * @param {number} entityId 
     * @param {string} langCode 
     * @param {string} newValue 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysLangTextCacheApi
     */
    public async apiSysLangTextCacheCacheEntityNameFieldNameEntityIdLangCodeNewValuePut(entityName: string, fieldName: string, entityId: number, langCode: string, newValue: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return SysLangTextCacheApiFp(this.configuration).apiSysLangTextCacheCacheEntityNameFieldNameEntityIdLangCodeNewValuePut(entityName, fieldName, entityId, langCode, newValue, options).then((request) => request(this.axios, this.basePath));
    }
}
