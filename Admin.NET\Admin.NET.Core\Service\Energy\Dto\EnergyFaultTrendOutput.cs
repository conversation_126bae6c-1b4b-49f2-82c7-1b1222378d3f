// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core.Service;

/// <summary>
/// 故障趋势输出参数
/// </summary>
public class EnergyFaultTrendOutput
{
    /// <summary>
    /// 时间点
    /// </summary>
    public DateTime TimePoint { get; set; }

    /// <summary>
    /// 时间点
    /// </summary>
    public DateTime Time { get; set; }

    /// <summary>
    /// 故障总数
    /// </summary>
    public int TotalFaultCount { get; set; }

    /// <summary>
    /// 故障总数
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 新增故障数
    /// </summary>
    public int NewFaultCount { get; set; }

    /// <summary>
    /// 已解决故障数
    /// </summary>
    public int ResolvedFaultCount { get; set; }

    /// <summary>
    /// 待处理故障数
    /// </summary>
    public int PendingCount { get; set; }

    /// <summary>
    /// 待处理故障数
    /// </summary>
    public int PendingFaultCount { get; set; }

    /// <summary>
    /// 处理中故障数
    /// </summary>
    public int ProcessingCount { get; set; }

    /// <summary>
    /// 处理中故障数
    /// </summary>
    public int ProcessingFaultCount { get; set; }

    /// <summary>
    /// 已修复故障数
    /// </summary>
    public int RepairedCount { get; set; }

    /// <summary>
    /// 解决率(%)
    /// </summary>
    public decimal ResolveRate { get; set; }

    /// <summary>
    /// 平均处理时长(小时)
    /// </summary>
    public decimal? AvgProcessTime { get; set; }

    /// <summary>
    /// 低级故障数
    /// </summary>
    public int LowLevelCount { get; set; }

    /// <summary>
    /// 中级故障数
    /// </summary>
    public int MediumLevelCount { get; set; }

    /// <summary>
    /// 高级故障数
    /// </summary>
    public int HighLevelCount { get; set; }

    /// <summary>
    /// 严重级故障数
    /// </summary>
    public int CriticalLevelCount { get; set; }

    /// <summary>
    /// 高级故障数
    /// </summary>
    public int HighLevelFaultCount { get; set; }

    /// <summary>
    /// 中级故障数
    /// </summary>
    public int MediumLevelFaultCount { get; set; }

    /// <summary>
    /// 低级故障数
    /// </summary>
    public int LowLevelFaultCount { get; set; }

    /// <summary>
    /// 故障设备数
    /// </summary>
    public int FaultDeviceCount { get; set; }

    /// <summary>
    /// 重复故障数
    /// </summary>
    public int RepeatFaultCount { get; set; }
}