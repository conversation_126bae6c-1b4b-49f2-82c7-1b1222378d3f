/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { DirectionEnum } from './direction-enum';
import { WeekEnum } from './week-enum';
/**
 * 
 * @export
 * @interface SysLang
 */
export interface SysLang {
    /**
     * 雪花Id
     * @type {number}
     * @memberof SysLang
     */
    id?: number;
    /**
     * 创建时间
     * @type {Date}
     * @memberof SysLang
     */
    createTime?: Date;
    /**
     * 更新时间
     * @type {Date}
     * @memberof SysLang
     */
    updateTime?: Date | null;
    /**
     * 创建者Id
     * @type {number}
     * @memberof SysLang
     */
    createUserId?: number | null;
    /**
     * 创建者姓名
     * @type {string}
     * @memberof SysLang
     */
    createUserName?: string | null;
    /**
     * 修改者Id
     * @type {number}
     * @memberof SysLang
     */
    updateUserId?: number | null;
    /**
     * 修改者姓名
     * @type {string}
     * @memberof SysLang
     */
    updateUserName?: string | null;
    /**
     * 语言名称
     * @type {string}
     * @memberof SysLang
     */
    name?: string | null;
    /**
     * 语言代码（如 zh_CN）
     * @type {string}
     * @memberof SysLang
     */
    code?: string | null;
    /**
     * ISO 语言代码
     * @type {string}
     * @memberof SysLang
     */
    isoCode?: string | null;
    /**
     * URL 语言代码
     * @type {string}
     * @memberof SysLang
     */
    urlCode?: string | null;
    /**
     * 
     * @type {DirectionEnum}
     * @memberof SysLang
     */
    direction?: DirectionEnum;
    /**
     * 日期格式（如 YYYY-MM-DD）
     * @type {string}
     * @memberof SysLang
     */
    dateFormat?: string | null;
    /**
     * 时间格式（如 HH:MM:SS）
     * @type {string}
     * @memberof SysLang
     */
    timeFormat?: string | null;
    /**
     * 
     * @type {WeekEnum}
     * @memberof SysLang
     */
    weekStart?: WeekEnum;
    /**
     * 分组符号（如 ,）
     * @type {string}
     * @memberof SysLang
     */
    grouping?: string | null;
    /**
     * 小数点符号
     * @type {string}
     * @memberof SysLang
     */
    decimalPoint?: string | null;
    /**
     * 千分位分隔符
     * @type {string}
     * @memberof SysLang
     */
    thousandsSep?: string | null;
    /**
     * 是否启用
     * @type {boolean}
     * @memberof SysLang
     */
    active?: boolean;
}
