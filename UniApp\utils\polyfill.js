/**
 * 全局Polyfill文件
 * 确保关键函数在所有环境中都可用
 */

import { api } from '../api/index.js'
import deviceApi from '../api/device.js'

/**
 * 设备统计函数的全局polyfill
 * 确保在app-service.js中可以直接调用
 */
if (typeof global !== 'undefined') {
  // Node.js环境或UniApp编译环境
  global.getDeviceStatistics = function(params = {}) {
    if (deviceApi && deviceApi.getDeviceStatistics) {
      return deviceApi.getDeviceStatistics(params)
    }
    
    if (api && api.device && api.device.getDeviceStatistics) {
      return api.device.getDeviceStatistics(params)
    }
    
    // 如果API不可用，返回模拟数据
    console.warn('getDeviceStatistics API不可用，返回模拟数据')
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: {
        totalDevices: 0,
        onlineDevices: 0,
        offlineDevices: 0,
        energyConsumption: 0,
        lastUpdateTime: new Date().toISOString()
      }
    })
  }
  
  global.getDeviceStats = global.getDeviceStatistics
}

if (typeof window !== 'undefined') {
  // 浏览器环境
  window.getDeviceStatistics = function(params = {}) {
    if (api && api.device && api.device.getDeviceStatistics) {
      return api.device.getDeviceStatistics(params)
    }
    
    console.warn('getDeviceStatistics API不可用，返回模拟数据')
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: {
        totalDevices: 0,
        onlineDevices: 0,
        offlineDevices: 0,
        energyConsumption: 0,
        lastUpdateTime: new Date().toISOString()
      }
    })
  }
  
  window.getDeviceStats = window.getDeviceStatistics
}

// UniApp环境
if (typeof uni !== 'undefined') {
  // 将函数挂载到uni对象上
  uni.getDeviceStatistics = function(params = {}) {
    if (api && api.device && api.device.getDeviceStatistics) {
      return api.device.getDeviceStatistics(params)
    }
    
    console.warn('getDeviceStatistics API不可用，返回模拟数据')
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: {
        totalDevices: 0,
        onlineDevices: 0,
        offlineDevices: 0,
        energyConsumption: 0,
        lastUpdateTime: new Date().toISOString()
      }
    })
  }
  
  uni.getDeviceStats = uni.getDeviceStatistics
}

/**
 * 初始化polyfill
 * 在应用启动时调用
 */
export function initPolyfill() {
  try {
    // 确保函数在各种环境中都可用
    const deviceStatsFn = function(params = {}) {
      if (api && api.device && api.device.getDeviceStatistics) {
        return api.device.getDeviceStatistics(params)
      }
      
      console.warn('getDeviceStatistics API不可用，返回模拟数据')
      return Promise.resolve({
        code: 200,
        message: 'success',
        data: {
          totalDevices: 0,
          onlineDevices: 0,
          offlineDevices: 0,
          energyConsumption: 0,
          lastUpdateTime: new Date().toISOString()
        }
      })
    }
    
    // 在多个作用域中注册函数
    if (typeof globalThis !== 'undefined') {
      globalThis.getDeviceStatistics = deviceStatsFn
      globalThis.getDeviceStats = deviceStatsFn
    }
    
    if (typeof self !== 'undefined') {
      self.getDeviceStatistics = deviceStatsFn
      self.getDeviceStats = deviceStatsFn
    }
    
    console.log('设备统计函数polyfill初始化完成，位置：utils/polyfill.js:128')
    return true
  } catch (error) {
    console.error('Polyfill初始化失败:', error)
    return false
  }
}

/**
 * 检查函数是否可用
 */
export function checkFunctionAvailability() {
  const checks = {
    global: typeof global !== 'undefined' && typeof global.getDeviceStatistics === 'function',
    window: typeof window !== 'undefined' && typeof window.getDeviceStatistics === 'function',
    uni: typeof uni !== 'undefined' && typeof uni.getDeviceStatistics === 'function',
    api: api && api.device && typeof api.device.getDeviceStatistics === 'function'
  }
  
  console.log('函数可用性检查结果：' + JSON.stringify(checks) + '，位置：utils/polyfill.js:147')
  return checks
}

export default {
  initPolyfill,
  checkFunctionAvailability
}