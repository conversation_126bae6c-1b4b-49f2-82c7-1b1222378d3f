/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 更新故障记录输入参数
 * @export
 * @interface UpdateEnergyFaultInput
 */
export interface UpdateEnergyFaultInput {
    /**
     * 设备ID
     * @type {number}
     * @memberof UpdateEnergyFaultInput
     */
    deviceId: number;
    /**
     * 故障编码
     * @type {string}
     * @memberof UpdateEnergyFaultInput
     */
    faultCode: string;
    /**
     * 故障名称
     * @type {string}
     * @memberof UpdateEnergyFaultInput
     */
    faultName: string;
    /**
     * 故障类型
     * @type {string}
     * @memberof UpdateEnergyFaultInput
     */
    faultType: string;
    /**
     * 故障等级
     * @type {number}
     * @memberof UpdateEnergyFaultInput
     */
    faultLevel: number;
    /**
     * 故障描述
     * @type {string}
     * @memberof UpdateEnergyFaultInput
     */
    faultDescription?: string | null;
    /**
     * 故障发生时间
     * @type {Date}
     * @memberof UpdateEnergyFaultInput
     */
    faultTime: Date;
    /**
     * 故障状态
     * @type {number}
     * @memberof UpdateEnergyFaultInput
     */
    faultStatus?: number;
    /**
     * 是否已通知
     * @type {boolean}
     * @memberof UpdateEnergyFaultInput
     */
    isNotified?: boolean;
    /**
     * 通知时间
     * @type {Date}
     * @memberof UpdateEnergyFaultInput
     */
    notifyTime?: Date | null;
    /**
     * 备注
     * @type {string}
     * @memberof UpdateEnergyFaultInput
     */
    remark?: string | null;
    /**
     * 主键ID
     * @type {number}
     * @memberof UpdateEnergyFaultInput
     */
    id: number;
}
