// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core.Service;

/// <summary>
/// 场景详情输出参数
/// </summary>
public class EnergySceneDetailOutput : EnergySceneOutput
{
    /// <summary>
    /// 场景执行历史
    /// </summary>
    public List<SceneExecuteHistoryItem> ExecuteHistory { get; set; } = new();

    /// <summary>
    /// 场景统计信息
    /// </summary>
    public SceneStatInfo StatInfo { get; set; } = new();

    /// <summary>
    /// 关联设备列表
    /// </summary>
    public List<EnergySceneDeviceOutput> DeviceList { get; set; } = new();

    /// <summary>
    /// 最近执行记录
    /// </summary>
    public List<object> RecentExecuteRecords { get; set; } = new();

    /// <summary>
    /// 今日执行次数
    /// </summary>
    public int TodayExecuteCount { get; set; }

    /// <summary>
    /// 本月执行次数
    /// </summary>
    public int ThisMonthExecuteCount { get; set; }

    /// <summary>
    /// 平均执行时长(秒)
    /// </summary>
    public decimal AvgExecuteTime { get; set; }
}

/// <summary>
/// 场景执行历史项
/// </summary>
public class SceneExecuteHistoryItem
{
    /// <summary>
    /// 执行时间
    /// </summary>
    public DateTime ExecuteTime { get; set; }

    /// <summary>
    /// 执行结果
    /// </summary>
    public int ExecuteResult { get; set; }

    /// <summary>
    /// 执行时长(秒)
    /// </summary>
    public decimal ExecuteDuration { get; set; }

    /// <summary>
    /// 成功设备数
    /// </summary>
    public int SuccessDeviceCount { get; set; }

    /// <summary>
    /// 失败设备数
    /// </summary>
    public int FailDeviceCount { get; set; }

    /// <summary>
    /// 操作人员
    /// </summary>
    public string? OperatorName { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 场景统计信息
/// </summary>
public class SceneStatInfo
{
    /// <summary>
    /// 设备总数
    /// </summary>
    public int DeviceCount { get; set; }

    /// <summary>
    /// 今日执行次数
    /// </summary>
    public int TodayExecuteCount { get; set; }

    /// <summary>
    /// 本周执行次数
    /// </summary>
    public int WeekExecuteCount { get; set; }

    /// <summary>
    /// 本月执行次数
    /// </summary>
    public int MonthExecuteCount { get; set; }

    /// <summary>
    /// 成功执行次数
    /// </summary>
    public int SuccessExecuteCount { get; set; }

    /// <summary>
    /// 失败执行次数
    /// </summary>
    public int FailExecuteCount { get; set; }

    /// <summary>
    /// 最短执行时长(秒)
    /// </summary>
    public decimal? MinExecuteTime { get; set; }

    /// <summary>
    /// 最长执行时长(秒)
    /// </summary>
    public decimal? MaxExecuteTime { get; set; }

    /// <summary>
    /// 最近执行状态
    /// </summary>
    public int? LastExecuteStatus { get; set; }
}