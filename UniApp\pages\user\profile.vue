<template>
  <view v-if="isUserInfoLoaded" class="profile-container">
    <!-- 头部背景 -->
    <view class="header-bg">
      <view class="header-content">
        <!-- 头像区域 -->
        <view class="avatar-section">
          <view class="avatar-wrapper" @click="chooseAvatar">
            <image 
              :src="userInfo.avatar" 
              class="avatar-image"
              mode="aspectFill"
            />
            <view class="avatar-overlay">
              <text class="camera-icon">📷</text>
            </view>
          </view>
          <text class="username">{{ userInfo.name }}</text>
          <text class="user-role">{{ userInfo.role }}</text>
        </view>
        
        <!-- 统计信息 -->
        <view class="stats-section">
          <view class="stat-item">
            <text class="stat-number">{{ stats.deviceCount }}</text>
            <text class="stat-label">管理设备</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ stats.loginDays }}</text>
            <text class="stat-label">登录天数</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ stats.energySaved }}</text>
            <text class="stat-label">节能(kWh)</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 个人信息 -->
      <view class="section">
        <view class="section-header">
          <text class="section-title">个人信息</text>
          <text class="edit-btn" @click="toggleEdit">{{ isEditing ? '保存' : '编辑' }}</text>
        </view>
        
        <view class="info-list">
          <view class="info-item">
            <text class="info-label">用户名</text>
            <input 
              v-if="isEditing"
              class="info-input"
              v-model="editForm.username"
              placeholder="请输入用户名"
            />
            <text v-else class="info-value">{{ userInfo.username }}</text>
          </view>
          
          <view class="info-item">
            <text class="info-label">姓名</text>
            <input 
              v-if="isEditing"
              class="info-input"
              v-model="editForm.name"
              placeholder="请输入姓名"
            />
            <text v-else class="info-value">{{ userInfo.name }}</text>
          </view>
          
          <view class="info-item">
            <text class="info-label">手机号</text>
            <input 
              v-if="isEditing"
              class="info-input"
              v-model="editForm.phone"
              placeholder="请输入手机号"
            />
            <text v-else class="info-value">{{ userInfo.phone }}</text>
          </view>
          
          <view class="info-item">
            <text class="info-label">邮箱</text>
            <input 
              v-if="isEditing"
              class="info-input"
              v-model="editForm.email"
              placeholder="请输入邮箱"
            />
            <text v-else class="info-value">{{ userInfo.email }}</text>
          </view>
          
          <view class="info-item">
            <text class="info-label">部门</text>
            <picker 
              v-if="isEditing"
              :range="departments"
              :value="departmentIndex"
              @change="onDepartmentChange"
            >
              <view class="picker-view">
                <text class="picker-text">{{ editForm.department || '请选择部门' }}</text>
                <text class="picker-arrow">▼</text>
              </view>
            </picker>
            <text v-else class="info-value">{{ userInfo.department }}</text>
          </view>
        </view>
      </view>

      <!-- 安全设置 -->
      <view class="section">
        <view class="section-header">
          <text class="section-title">安全设置</text>
        </view>
        
        <view class="security-list">
          <view class="security-item" @click="changePassword">
            <view class="security-left">
              <text class="security-icon">🔒</text>
              <text class="security-label">修改密码</text>
            </view>
            <text class="security-arrow">></text>
          </view>
          
          <view class="security-item" @click="bindPhone">
            <view class="security-left">
              <text class="security-icon">📱</text>
              <text class="security-label">绑定手机</text>
            </view>
            <view class="security-right">
              <text class="security-status">{{ userInfo.phone ? '已绑定' : '未绑定' }}</text>
              <text class="security-arrow">></text>
            </view>
          </view>
          
          <view class="security-item" @click="bindEmail">
            <view class="security-left">
              <text class="security-icon">📧</text>
              <text class="security-label">绑定邮箱</text>
            </view>
            <view class="security-right">
              <text class="security-status">{{ userInfo.email ? '已绑定' : '未绑定' }}</text>
              <text class="security-arrow">></text>
            </view>
          </view>
        </view>
      </view>

      <!-- 权限信息 -->
      <view class="section">
        <view class="section-header">
          <text class="section-title">权限信息</text>
        </view>
        
        <view class="permission-list">
          <view 
            class="permission-item"
            v-for="permission in permissions"
            :key="permission.id"
          >
            <view class="permission-left">
              <text class="permission-icon">{{ permission.icon }}</text>
              <view class="permission-info">
                <text class="permission-name">{{ permission.name }}</text>
                <text class="permission-desc">{{ permission.description }}</text>
              </view>
            </view>
            <view class="permission-status" :class="{ active: permission.granted }">
              <text class="status-text">{{ permission.granted ? '已授权' : '未授权' }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 应用设置 -->
      <view class="section">
        <view class="section-header">
          <text class="section-title">应用设置</text>
        </view>
        
        <view class="setting-list">
          <view class="setting-item">
            <text class="setting-label">消息推送</text>
            <switch 
              :checked="settings.pushNotification"
              @change="onPushChange"
              color="#667eea"
            />
          </view>
          
          <view class="setting-item">
            <text class="setting-label">自动登录</text>
            <switch 
              :checked="settings.autoLogin"
              @change="onAutoLoginChange"
              color="#667eea"
            />
          </view>
          
          <view class="setting-item">
            <text class="setting-label">数据同步</text>
            <switch 
              :checked="settings.dataSync"
              @change="onDataSyncChange"
              color="#667eea"
            />
          </view>
        </view>
      </view>

      <!-- 退出登录 -->
      <view class="logout-section">
        <button class="logout-btn" @click="logout">退出登录</button>
      </view>
    </view>

    <!-- 修改密码弹窗 -->
    <view class="modal-overlay" v-if="showPasswordModal" @click="closePasswordModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">修改密码</text>
          <text class="modal-close" @click="closePasswordModal">✕</text>
        </view>
        
        <view class="modal-body">
          <view class="input-group">
            <text class="input-label">当前密码</text>
            <input 
              class="form-input"
              type="password"
              v-model="passwordForm.oldPassword"
              placeholder="请输入当前密码"
            />
          </view>
          
          <view class="input-group">
            <text class="input-label">新密码</text>
            <input 
              class="form-input"
              type="password"
              v-model="passwordForm.newPassword"
              placeholder="请输入新密码"
            />
          </view>
          
          <view class="input-group">
            <text class="input-label">确认密码</text>
            <input 
              class="form-input"
              type="password"
              v-model="passwordForm.confirmPassword"
              placeholder="请再次输入新密码"
            />
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="modal-btn cancel" @click="closePasswordModal">取消</button>
          <button class="modal-btn confirm" @click="submitPasswordChange">确认</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { authGuard } from '@/utils/authGuard'
import { encryptPassword } from '@/utils/crypto'
import { authApi } from '@/api/auth'

// 响应式数据
const userInfo = reactive({
  id: 1,
  username: 'admin',
  name: '张三',
  phone: '138****8888',
  email: '<EMAIL>',
  department: '技术部',
  role: '系统管理员',
  avatar: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20avatar%20admin&image_size=square'
})

const editForm = reactive({
  username: '',
  name: '',
  phone: '',
  email: '',
  department: ''
})

const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const stats = reactive({
  deviceCount: 156,
  loginDays: 89,
  energySaved: 1234.5
})

const settings = reactive({
  pushNotification: true,
  autoLogin: false,
  dataSync: true
})

const permissions = ref([
  {
    id: 1,
    name: '设备管理',
    description: '查看和控制照明设备',
    icon: '💡',
    granted: true
  },
  {
    id: 2,
    name: '能耗监控',
    description: '查看能耗数据和统计',
    icon: '⚡',
    granted: true
  },
  {
    id: 3,
    name: '故障处理',
    description: '处理设备故障和维修',
    icon: '🔧',
    granted: true
  },
  {
    id: 4,
    name: '用户管理',
    description: '管理系统用户和权限',
    icon: '👥',
    granted: false
  },
  {
    id: 5,
    name: '系统配置',
    description: '修改系统参数和设置',
    icon: '⚙️',
    granted: false
  }
])

const departments = ref(['技术部', '运维部', '管理部', '财务部', '人事部'])

const isEditing = ref(false)
const showPasswordModal = ref(false)
const departmentIndex = ref(0)
const isUserInfoLoaded = ref(false)

// 定时器变量
const saveTimer = ref(null)
const passwordTimer = ref(null)
const logoutTimer = ref(null)

// 方法
const chooseAvatar = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      const tempFilePath = res.tempFilePaths[0]
      // 这里应该上传到服务器，现在只是模拟
      userInfo.avatar = tempFilePath
      
      uni.showToast({
        title: '头像更新成功',
        icon: 'success'
      })
    },
    fail: () => {
      uni.showToast({
        title: '选择图片失败',
        icon: 'error'
      })
    }
  })
}

const toggleEdit = async () => {
  if (isEditing.value) {
    // 保存编辑
    try {
      // 模拟API调用
      await new Promise(resolve => {
        saveTimer.value = setTimeout(() => {
          resolve()
          saveTimer.value = null
        }, 1000)
      })
      
      // 更新用户信息
      Object.assign(userInfo, editForm)
      
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      })
      
      isEditing.value = false
    } catch (err) {
      console.error('保存用户信息失败:', err)
      uni.showToast({
        title: '保存失败',
        icon: 'error'
      })
    }
  } else {
    // 进入编辑模式
    Object.assign(editForm, userInfo)
    isEditing.value = true
  }
}

const onDepartmentChange = (e) => {
  departmentIndex.value = e.detail.value
  editForm.department = departments.value[e.detail.value]
}

const changePassword = () => {
  showPasswordModal.value = true
}

const closePasswordModal = () => {
  showPasswordModal.value = false
  passwordForm.oldPassword = ''
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
}

const submitPasswordChange = async () => {
  if (!passwordForm.oldPassword || !passwordForm.newPassword || !passwordForm.confirmPassword) {
    uni.showToast({
      title: '请填写完整信息',
      icon: 'error'
    })
    return
  }
  
  if (passwordForm.newPassword !== passwordForm.confirmPassword) {
    uni.showToast({
      title: '两次密码不一致',
      icon: 'error'
    })
    return
  }
  
  if (passwordForm.newPassword.length < 6) {
    uni.showToast({
      title: '密码至少6位',
      icon: 'error'
    })
    return
  }
  
  try {
    // 使用SM2加密密码
    const encryptedOldPassword = encryptPassword(passwordForm.oldPassword)
    const encryptedNewPassword = encryptPassword(passwordForm.newPassword)
    
    // 调用真实的密码修改API
    await authApi.changePassword({
      oldPassword: encryptedOldPassword,
      newPassword: encryptedNewPassword
    })
    
    uni.showToast({
      title: '密码修改成功',
      icon: 'success'
    })
    
    closePasswordModal()
  } catch (err) {
    console.error('密码修改失败:', err)
    uni.showToast({
      title: err.message || '密码修改失败',
      icon: 'error'
    })
  }
}

const bindPhone = () => {
  uni.showToast({
    title: '手机绑定功能开发中',
    icon: 'none'
  })
}

const bindEmail = () => {
  uni.showToast({
    title: '邮箱绑定功能开发中',
    icon: 'none'
  })
}

const onPushChange = (e) => {
  settings.pushNotification = e.detail.value
  uni.showToast({
    title: `消息推送已${e.detail.value ? '开启' : '关闭'}`,
    icon: 'none'
  })
}

const onAutoLoginChange = (e) => {
  settings.autoLogin = e.detail.value
  uni.showToast({
    title: `自动登录已${e.detail.value ? '开启' : '关闭'}`,
    icon: 'none'
  })
}

const onDataSyncChange = (e) => {
  settings.dataSync = e.detail.value
  uni.showToast({
    title: `数据同步已${e.detail.value ? '开启' : '关闭'}`,
    icon: 'none'
  })
}

const logout = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        // 清除登录状态
        uni.removeStorageSync('userToken')
        uni.removeStorageSync('userInfo')
        
        uni.showToast({
          title: '已退出登录',
          icon: 'success'
        })
        
        // 跳转到登录页
        logoutTimer.value = setTimeout(() => {
          uni.reLaunch({
            url: '/pages/user/login'
          })
          logoutTimer.value = null
        }, 1500)
      }
    }
  })
}

const loadUserInfo = () => {
  // 从本地存储加载用户信息
  const savedUserInfo = uni.getStorageSync('userInfo')
  if (savedUserInfo) {
    Object.assign(userInfo, savedUserInfo)
  }
  isUserInfoLoaded.value = true
}

// 生命周期
onMounted(async () => {
  // 检查登录状态
  const canAccess = await authGuard({
    requireAuth: true,
    message: '个人中心页面需要登录后才能访问'
  })
  
  if (!canAccess) {
    return
  }
  
  loadUserInfo()
  // 设置部门索引
  departmentIndex.value = departments.value.indexOf(userInfo.department)
})

import { onUnload } from '@dcloudio/uni-app'

// 页面卸载时清理定时器
onUnload(() => {
  if (saveTimer.value) {
    clearTimeout(saveTimer.value)
    saveTimer.value = null
  }
  if (passwordTimer.value) {
    clearTimeout(passwordTimer.value)
    passwordTimer.value = null
  }
  if (logoutTimer.value) {
    clearTimeout(logoutTimer.value)
    logoutTimer.value = null
  }
})
</script>

<style scoped>
.profile-container {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 头部背景 */
.header-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 0 60rpx;
}

.header-content {
  padding: 0 40rpx;
}

/* 头像区域 */
.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.avatar-wrapper {
  position: relative;
  margin-bottom: 20rpx;
}

.avatar-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
}

.avatar-overlay {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 50rpx;
  height: 50rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 25rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.camera-icon {
  font-size: 24rpx;
}

.username {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 10rpx;
}

.user-role {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 统计区域 */
.stats-section {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx 0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 主要内容 */
.main-content {
  padding: 0 40rpx;
  margin-top: -30rpx;
}

/* 区块样式 */
.section {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.edit-btn {
  font-size: 28rpx;
  color: #667eea;
}

/* 信息列表 */
.info-list {
  padding: 0 40rpx 20rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 150rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: right;
}

.info-input {
  flex: 1;
  height: 60rpx;
  padding: 0 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  text-align: right;
}

.picker-view {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  padding: 0 20rpx;
  height: 60rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999;
}

/* 安全设置 */
.security-list {
  padding: 0 40rpx 20rpx;
}

.security-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.security-item:last-child {
  border-bottom: none;
}

.security-left {
  display: flex;
  align-items: center;
}

.security-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.security-label {
  font-size: 28rpx;
  color: #333;
}

.security-right {
  display: flex;
  align-items: center;
}

.security-status {
  font-size: 26rpx;
  color: #999;
  margin-right: 10rpx;
}

.security-arrow {
  font-size: 28rpx;
  color: #ccc;
}

/* 权限列表 */
.permission-list {
  padding: 0 40rpx 20rpx;
}

.permission-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.permission-item:last-child {
  border-bottom: none;
}

.permission-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.permission-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.permission-info {
  flex: 1;
}

.permission-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.permission-desc {
  font-size: 24rpx;
  color: #999;
}

.permission-status {
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  background: #f0f0f0;
}

.permission-status.active {
  background: #e8f5e8;
}

.status-text {
  font-size: 24rpx;
  color: #999;
}

.permission-status.active .status-text {
  color: #52c41a;
}

/* 设置列表 */
.setting-list {
  padding: 0 40rpx 20rpx;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  font-size: 28rpx;
  color: #333;
}

/* 退出登录 */
.logout-section {
  padding: 40rpx 0;
}

.logout-btn {
  width: 100%;
  height: 90rpx;
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: bold;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 600rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
}

.modal-body {
  padding: 40rpx;
}

.input-group {
  margin-bottom: 30rpx;
}

.input-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 30rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  height: 100rpx;
  border: none;
  font-size: 28rpx;
}

.modal-btn.cancel {
  background: #f0f0f0;
  color: #333;
}

.modal-btn.confirm {
  background: #667eea;
  color: white;
}
</style>