/**
 * 性能优化工具函数集合
 * 包含图片懒加载、防抖节流、内存泄漏防护等功能
 */

/**
 * 防抖函数 - 延迟执行，在指定时间内重复调用会重置计时器
 * @param {Function} func 要执行的函数
 * @param {number} delay 延迟时间（毫秒）
 * @param {boolean} immediate 是否立即执行第一次
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay = 300, immediate = false) {
  let timeoutId = null
  let isInvoked = false

  return function debounced(...args) {
    const context = this

    // 立即执行模式
    if (immediate && !isInvoked) {
      func.apply(context, args)
      isInvoked = true
    }

    // 清除之前的定时器
    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    // 设置新的定时器
    timeoutId = setTimeout(() => {
      if (!immediate) {
        func.apply(context, args)
      }
      isInvoked = false
      timeoutId = null
    }, delay)
  }
}

/**
 * 节流函数 - 限制函数执行频率
 * @param {Function} func 要执行的函数
 * @param {number} delay 节流间隔（毫秒）
 * @param {Object} options 配置选项
 * @returns {Function} 节流后的函数
 */
export function throttle(func, delay = 300, options = {}) {
  const { leading = true, trailing = true } = options
  let lastExecTime = 0
  let timeoutId = null
  let lastArgs = null
  let lastContext = null

  return function throttled(...args) {
    const context = this
    const currentTime = Date.now()
    
    lastArgs = args
    lastContext = context

    // 首次执行
    if (leading && currentTime - lastExecTime >= delay) {
      func.apply(context, args)
      lastExecTime = currentTime
      return
    }

    // 清除之前的定时器
    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    // 设置延迟执行
    if (trailing) {
      timeoutId = setTimeout(() => {
        if (currentTime - lastExecTime >= delay) {
          func.apply(lastContext, lastArgs)
          lastExecTime = Date.now()
        }
        timeoutId = null
      }, delay - (currentTime - lastExecTime))
    }
  }
}

/**
 * 图片懒加载管理器
 */
export class LazyImageLoader {
  constructor(options = {}) {
    this.options = {
      rootMargin: '50px',
      threshold: 0.1,
      placeholder: '/static/images/placeholder.png',
      errorImage: '/static/images/error.png',
      fadeInDuration: 300,
      ...options
    }
    
    this.observer = null
    this.imageMap = new WeakMap()
    this.loadingImages = new Set()
    
    this.init()
  }

  init() {
    // 检查是否支持 IntersectionObserver
    if (typeof IntersectionObserver !== 'undefined') {
      this.observer = new IntersectionObserver(
        this.handleIntersection.bind(this),
        {
          rootMargin: this.options.rootMargin,
          threshold: this.options.threshold
        }
      )
    }
  }

  /**
   * 处理元素进入视口
   */
  handleIntersection(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        this.loadImage(entry.target)
        this.observer.unobserve(entry.target)
      }
    })
  }

  /**
   * 加载图片
   */
  async loadImage(imgElement) {
    const src = imgElement.dataset.src
    if (!src || this.loadingImages.has(src)) return

    this.loadingImages.add(src)

    try {
      // 预加载图片
      const img = new Image()
      img.src = src
      
      await new Promise((resolve, reject) => {
        img.onload = resolve
        img.onerror = reject
        
        // 设置超时
        setTimeout(() => reject(new Error('Image load timeout')), 10000)
      })

      // 设置图片源并添加淡入效果
      imgElement.src = src
      imgElement.style.opacity = '0'
      imgElement.style.transition = `opacity ${this.options.fadeInDuration}ms ease-in-out`
      
      // 触发重绘后设置透明度
      requestAnimationFrame(() => {
        imgElement.style.opacity = '1'
      })

      // 移除 data-src 属性
      delete imgElement.dataset.src
      
    } catch (error) {
      console.warn('Image load failed:', src, error)
      imgElement.src = this.options.errorImage
    } finally {
      this.loadingImages.delete(src)
    }
  }

  /**
   * 观察图片元素
   */
  observe(imgElement) {
    if (!imgElement || !imgElement.dataset.src) return

    // 设置占位图
    if (!imgElement.src) {
      imgElement.src = this.options.placeholder
    }

    if (this.observer) {
      this.observer.observe(imgElement)
    } else {
      // 降级处理：直接加载
      this.loadImage(imgElement)
    }
  }

  /**
   * 取消观察
   */
  unobserve(imgElement) {
    if (this.observer && imgElement) {
      this.observer.unobserve(imgElement)
    }
  }

  /**
   * 销毁实例
   */
  destroy() {
    if (this.observer) {
      this.observer.disconnect()
      this.observer = null
    }
    this.imageMap = null
    this.loadingImages.clear()
  }
}

/**
 * 内存泄漏防护管理器
 */
export class MemoryLeakGuard {
  constructor() {
    this.timers = new Set()
    this.intervals = new Set()
    this.observers = new Set()
    this.eventListeners = new Map()
    this.abortControllers = new Set()
  }

  /**
   * 安全的 setTimeout
   */
  setTimeout(callback, delay, ...args) {
    const timerId = setTimeout(() => {
      this.timers.delete(timerId)
      callback(...args)
    }, delay)
    
    this.timers.add(timerId)
    return timerId
  }

  /**
   * 安全的 setInterval
   */
  setInterval(callback, delay, ...args) {
    const intervalId = setInterval(callback, delay, ...args)
    this.intervals.add(intervalId)
    return intervalId
  }

  /**
   * 清除定时器
   */
  clearTimeout(timerId) {
    if (this.timers.has(timerId)) {
      clearTimeout(timerId)
      this.timers.delete(timerId)
    }
  }

  /**
   * 清除间隔器
   */
  clearInterval(intervalId) {
    if (this.intervals.has(intervalId)) {
      clearInterval(intervalId)
      this.intervals.delete(intervalId)
    }
  }

  /**
   * 添加事件监听器
   */
  addEventListener(element, event, handler, options) {
    element.addEventListener(event, handler, options)
    
    if (!this.eventListeners.has(element)) {
      this.eventListeners.set(element, [])
    }
    
    this.eventListeners.get(element).push({ event, handler, options })
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(element, event, handler) {
    element.removeEventListener(event, handler)
    
    const listeners = this.eventListeners.get(element)
    if (listeners) {
      const index = listeners.findIndex(l => l.event === event && l.handler === handler)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  /**
   * 添加观察器
   */
  addObserver(observer) {
    this.observers.add(observer)
    return observer
  }

  /**
   * 添加 AbortController
   */
  addAbortController(controller) {
    this.abortControllers.add(controller)
    return controller
  }

  /**
   * 清理所有资源
   */
  cleanup() {
    // 清理定时器
    this.timers.forEach(timerId => clearTimeout(timerId))
    this.timers.clear()

    // 清理间隔器
    this.intervals.forEach(intervalId => clearInterval(intervalId))
    this.intervals.clear()

    // 清理事件监听器
    this.eventListeners.forEach((listeners, element) => {
      listeners.forEach(({ event, handler }) => {
        element.removeEventListener(event, handler)
      })
    })
    this.eventListeners.clear()

    // 清理观察器
    this.observers.forEach(observer => {
      if (observer && typeof observer.disconnect === 'function') {
        observer.disconnect()
      }
    })
    this.observers.clear()

    // 取消请求
    this.abortControllers.forEach(controller => {
      if (controller && typeof controller.abort === 'function') {
        controller.abort()
      }
    })
    this.abortControllers.clear()
  }
}

/**
 * 性能监控工具
 */
export class PerformanceMonitor {
  constructor() {
    this.marks = new Map()
    this.measures = new Map()
    // Check for performance API availability once
    this.perf = typeof performance !== 'undefined' ? performance : null;
  }

  /**
   * 标记性能点
   */
  mark(name) {
    const timestamp = this.perf ? this.perf.now() : Date.now()
    this.marks.set(name, timestamp)
    
    if (this.perf && typeof this.perf.mark === 'function') {
      this.perf.mark(name)
    }
    
    return timestamp
  }

  /**
   * 测量性能
   */
  measure(name, startMark, endMark) {
    const startTime = this.marks.get(startMark)
    const endTime = endMark ? this.marks.get(endMark) : (this.perf ? this.perf.now() : Date.now())
    
    if (startTime === undefined) {
      console.warn(`Start mark '${startMark}' not found`)
      return null
    }
    
    const duration = endTime - startTime
    this.measures.set(name, { startTime, endTime, duration })
    
    if (this.perf && typeof this.perf.measure === 'function') {
      try {
        this.perf.measure(name, startMark, endMark)
      } catch (error) {
        console.warn('Performance measure failed:', error)
      }
    }
    
    return duration
  }

  /**
   * 获取测量结果
   */
  getMeasure(name) {
    return this.measures.get(name)
  }

  /**
   * 获取所有测量结果
   */
  getAllMeasures() {
    return Object.fromEntries(this.measures)
  }

  /**
   * 清理性能数据
   */
  clear() {
    this.marks.clear()
    this.measures.clear()
    
    if (this.perf && typeof this.perf.clearMarks === 'function') {
      this.perf.clearMarks()
    }
    
    if (this.perf && typeof this.perf.clearMeasures === 'function') {
      this.perf.clearMeasures()
    }
  }
}

/**
 * 虚拟滚动管理器
 */
export class VirtualScrollManager {
  constructor(options = {}) {
    this.options = {
      itemHeight: 100,
      containerHeight: 600,
      buffer: 5,
      ...options
    }
    
    this.data = []
    this.scrollTop = 0
    this.visibleStartIndex = 0
    this.visibleEndIndex = 0
    this.totalHeight = 0
    
    this.init()
  }

  init() {
    this.calculateVisibleRange()
  }

  /**
   * 设置数据
   */
  setData(data) {
    this.data = data || []
    this.totalHeight = this.data.length * this.options.itemHeight
    this.calculateVisibleRange()
  }

  /**
   * 更新滚动位置
   */
  updateScrollTop(scrollTop) {
    this.scrollTop = scrollTop
    this.calculateVisibleRange()
  }

  /**
   * 计算可见范围
   */
  calculateVisibleRange() {
    const { itemHeight, containerHeight, buffer } = this.options
    
    // 计算可见区域的起始和结束索引
    const visibleStart = Math.floor(this.scrollTop / itemHeight)
    const visibleEnd = Math.min(
      this.data.length - 1,
      Math.ceil((this.scrollTop + containerHeight) / itemHeight)
    )
    
    // 添加缓冲区
    this.visibleStartIndex = Math.max(0, visibleStart - buffer)
    this.visibleEndIndex = Math.min(this.data.length - 1, visibleEnd + buffer)
  }

  /**
   * 获取可见数据
   */
  getVisibleData() {
    const visibleData = this.data.slice(this.visibleStartIndex, this.visibleEndIndex + 1)
    const offsetY = this.visibleStartIndex * this.options.itemHeight
    
    return {
      data: visibleData,
      startIndex: this.visibleStartIndex,
      endIndex: this.visibleEndIndex,
      offsetY,
      totalHeight: this.totalHeight
    }
  }

  /**
   * 获取项目位置信息
   */
  getItemPosition(index) {
    return {
      top: index * this.options.itemHeight,
      height: this.options.itemHeight
    }
  }

  /**
   * 滚动到指定索引
   */
  scrollToIndex(index) {
    const scrollTop = Math.max(0, index * this.options.itemHeight)
    return scrollTop
  }

  /**
   * 更新配置
   */
  updateOptions(options) {
    this.options = { ...this.options, ...options }
    this.totalHeight = this.data.length * this.options.itemHeight
    this.calculateVisibleRange()
  }

  /**
   * 销毁实例
   */
  destroy() {
    this.data = []
    this.scrollTop = 0
    this.visibleStartIndex = 0
    this.visibleEndIndex = 0
    this.totalHeight = 0
  }
}

/**
 * 全局性能优化实例
 */
export const performanceUtils = {
  // 懒加载实例
  lazyLoader: new LazyImageLoader(),
  
  // 内存防护实例
  memoryGuard: new MemoryLeakGuard(),
  
  // 性能监控实例
  monitor: new PerformanceMonitor(),
  
  // 常用防抖函数
  searchDebounce: debounce((callback) => callback(), 500),
  inputDebounce: debounce((callback) => callback(), 300),
  
  // 常用节流函数
  scrollThrottle: throttle((callback) => callback(), 100),
  resizeThrottle: throttle((callback) => callback(), 200),
}

/**
 * Vue 3 组合式 API 钩子
 */
export function usePerformance() {
  const memoryGuard = new MemoryLeakGuard()
  
  return {
    memoryGuard,
    debounce,
    throttle,
    monitor: performanceUtils.monitor
  }
}

// 导出别名以保持向后兼容性
export const MemoryGuard = MemoryLeakGuard

export default {
  debounce,
  throttle,
  LazyImageLoader,
  MemoryLeakGuard,
  MemoryGuard,
  PerformanceMonitor,
  VirtualScrollManager,
  performanceUtils,
  usePerformance
}