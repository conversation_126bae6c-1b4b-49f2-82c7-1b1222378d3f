// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.Runtime.InteropServices;
using System.Buffers; 
using System.Diagnostics.CodeAnalysis;
using JsonIgnoreAttribute = System.Text.Json.Serialization.JsonIgnoreAttribute;

namespace Admin.NET.Plugin.MQTT.Core.Models;

/// <summary>
/// MQTT消息基类 - 优化内存使用和JSON序列化性能
/// 使用结构体布局优化、内存池和高性能JSON序列化
/// </summary>
/// <typeparam name="T">参数类型</typeparam>
[StructLayout(LayoutKind.Sequential, Pack = 8)] // 内存对齐优化
public sealed class SysMqttMessage<T> : IDisposable where T : class
{
    private static readonly ArrayPool<char> _charPool = ArrayPool<char>.Shared;
    private static readonly JsonSerializerOptions _jsonOptions = CreateJsonOptions();
    private char[]? _methodBuffer;
    private char[]? _versionBuffer;
    private char[]? _idBuffer;
    private bool _disposed;

    /// <summary>
    /// 消息ID - 使用内存池优化字符串处理
    /// </summary>
    [Required(ErrorMessage = "消息ID不能为空")]
    [StringLength(64, ErrorMessage = "消息ID长度不能超过64个字符")]
    public string Id 
    { 
        get => _idBuffer != null ? new string(_idBuffer) : string.Empty;
        set => SetStringProperty(ref _idBuffer, value);
    }
    
    /// <summary>
    /// 消息ID - JSON序列化属性名映射
    /// </summary>
    [JsonPropertyName("id")]
    [JsonIgnore(Condition = JsonIgnoreCondition.Never)]
    public string id 
    {
        get => Id;
        set => Id = value;
    }
    
    /// <summary>
    /// 方法名称 - 使用内存池优化字符串处理
    /// </summary>
    [Required(ErrorMessage = "方法名称不能为空")]
    [StringLength(64, ErrorMessage = "方法名称长度不能超过64个字符")]
    public string Method 
    { 
        get => _methodBuffer != null ? new string(_methodBuffer) : string.Empty;
        set => SetStringProperty(ref _methodBuffer, value);
    }
    
    /// <summary>
    /// 方法名称 - JSON序列化属性名映射
    /// </summary>
    [JsonPropertyName("method")]
    [JsonIgnore(Condition = JsonIgnoreCondition.Never)]
    public string method 
    {
        get => Method;
        set => Method = value;
    }
    
    /// <summary>
    /// 参数 - 优化泛型参数处理
    /// </summary>
    [Required(ErrorMessage = "参数不能为空")]
    public OptimizedParams<T> Parameters { get; set; } = new();
    
    /// <summary>
    /// 参数 - JSON序列化属性名映射
    /// </summary>
    [JsonPropertyName("params")]
    [JsonIgnore(Condition = JsonIgnoreCondition.Never)]
    public OptimizedParams<T> _params 
    {
        get => Parameters;
        set => Parameters = value;
    }
    
    /// <summary>
    /// 版本号 - 使用内存池优化字符串处理
    /// </summary>
    [StringLength(16, ErrorMessage = "版本号长度不能超过16个字符")]
    public string Version 
    { 
        get => _versionBuffer != null ? new string(_versionBuffer) : "1.0";
        set => SetStringProperty(ref _versionBuffer, value ?? "1.0");
    }
    
    /// <summary>
    /// 版本号 - JSON序列化属性名映射
    /// </summary>
    [JsonPropertyName("version")]
    [JsonIgnore(Condition = JsonIgnoreCondition.Never)]
    public string version 
    {
        get => Version;
        set => Version = value;
    }

    /// <summary>
    /// 消息创建时间 - 用于性能监控
    /// </summary>
    [JsonIgnore]
    public DateTime CreatedAt { get; } = DateTime.UtcNow;

    /// <summary>
    /// 消息大小估算 - 用于内存监控
    /// </summary>
    [JsonIgnore]
    public int EstimatedSize => 
        (Id?.Length ?? 0) * 2 + // Id (UTF-16)
        (Method?.Length ?? 0) * 2 + // Method (UTF-16)
        (Version?.Length ?? 0) * 2 + // Version (UTF-16)
        Parameters?.EstimatedSize ?? 0; // Parameters

    /// <summary>
    /// 消息是否有效
    /// </summary>
    [JsonIgnore]
    public bool IsValid => 
        !string.IsNullOrWhiteSpace(Id) && 
        !string.IsNullOrWhiteSpace(Method) && 
        Parameters != null && 
        Parameters.IsValid;

    /// <summary>
    /// 默认构造函数
    /// </summary>
    public SysMqttMessage()
    {
        Parameters = new OptimizedParams<T>();
    }

    /// <summary>
    /// 带参数的构造函数
    /// </summary>
    /// <param name="id">消息ID</param>
    /// <param name="method">方法名称</param>
    /// <param name="value">参数值</param>
    /// <param name="version">版本号</param>
    public SysMqttMessage(string id, string method, T value, string version = "1.0")
    {
        Id = id;
        Method = method;
        Version = version;
        Parameters = new OptimizedParams<T> { Value = value };
    }

    /// <summary>
    /// 设置字符串属性并使用内存池优化
    /// </summary>
    /// <param name="buffer">字符缓冲区引用</param>
    /// <param name="value">要设置的值</param>
    private void SetStringProperty(ref char[]? buffer, string? value)
    {
        // 释放旧缓冲区
        if (buffer != null)
        {
            _charPool.Return(buffer);
            buffer = null;
        }

        // 设置新值
        if (!string.IsNullOrEmpty(value))
        {
            buffer = _charPool.Rent(value.Length);
            value.AsSpan().CopyTo(buffer.AsSpan());
        }
    }

    /// <summary>
    /// 创建优化的JSON序列化选项
    /// </summary>
    /// <returns>JSON序列化选项</returns>
    private static JsonSerializerOptions CreateJsonOptions()
    {
        return new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false, // 减少输出大小
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            PropertyNameCaseInsensitive = true,
            NumberHandling = JsonNumberHandling.AllowReadingFromString,
            AllowTrailingCommas = true
        };
    }

    /// <summary>
    /// 高性能JSON序列化
    /// </summary>
    /// <returns>JSON字符串</returns>
    public string ToJson()
    {
        return JsonSerializer.Serialize(this, _jsonOptions);
    }

    /// <summary>
    /// 高性能JSON反序列化
    /// </summary>
    /// <param name="json">JSON字符串</param>
    /// <returns>消息实例</returns>
    public static SysMqttMessage<T>? FromJson(string json)
    {
        if (string.IsNullOrWhiteSpace(json))
            return null;

        try
        {
            return JsonSerializer.Deserialize<SysMqttMessage<T>>(json, _jsonOptions);
        }
        catch (JsonException)
        {
            return null;
        }
    }

    /// <summary>
    /// 创建消息的工厂方法
    /// </summary>
    /// <param name="method">方法名称</param>
    /// <param name="value">参数值</param>
    /// <param name="version">版本号</param>
    /// <returns>消息实例</returns>
    public static SysMqttMessage<T> Create(string method, T value, string version = "1.0")
    {
        return new SysMqttMessage<T>
        {
            Id = Guid.NewGuid().ToString("N"),
            Method = method,
            Version = version,
            Parameters = new OptimizedParams<T> { Value = value }
        };
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            ReturnBuffer(ref _idBuffer);
            ReturnBuffer(ref _methodBuffer);
            ReturnBuffer(ref _versionBuffer);
            
            // 如果参数实现了IDisposable，也要释放
            if (Parameters is IDisposable disposableParams)
            {
                disposableParams.Dispose();
            }
            
            _disposed = true;
        }
    }

    /// <summary>
    /// 返回缓冲区到内存池
    /// </summary>
    /// <param name="buffer">要返回的缓冲区</param>
    private void ReturnBuffer(ref char[]? buffer)
    {
        if (buffer != null)
        {
            _charPool.Return(buffer);
            buffer = null;
        }
    }
}

/// <summary>
/// 优化的MQTT消息参数类 - 减少装箱和提高性能
/// </summary>
/// <typeparam name="T">参数值类型</typeparam>
[StructLayout(LayoutKind.Sequential, Pack = 8)] // 内存对齐优化
public class OptimizedParams<T> : IDisposable where T : class
{
    private bool _disposed;

    /// <summary>
    /// 时间戳 - 使用高精度时间
    /// </summary>
    [Range(0, long.MaxValue, ErrorMessage = "时间戳必须大于等于0")]
    public long Time { get; set; } = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

    /// <summary>
    /// 时间戳 - JSON序列化属性名映射
    /// </summary>
    [JsonPropertyName("time")]
    [JsonIgnore(Condition = JsonIgnoreCondition.Never)]
    public long time 
    {
        get => Time;
        set => Time = value;
    }

    /// <summary>
    /// 参数值 - 使用泛型避免装箱
    /// </summary>
    [Required(ErrorMessage = "参数值不能为空")]
    public T? Value { get; set; }

    /// <summary>
    /// 参数值 - JSON序列化属性名映射
    /// </summary>
    [JsonPropertyName("value")]
    [JsonIgnore(Condition = JsonIgnoreCondition.Never)]
    public T? value 
    {
        get => Value;
        set => Value = value;
    }

    /// <summary>
    /// 参数创建时间 - 用于性能监控
    /// </summary>
    [JsonIgnore]
    public DateTime CreatedAt { get; } = DateTime.UtcNow;

    /// <summary>
    /// 参数大小估算 - 用于内存监控
    /// </summary>
    [JsonIgnore]
    public int EstimatedSize => 
        sizeof(long) + // Time
        (Value != null ? 64 : 0); // 估算值大小

    /// <summary>
    /// 参数是否有效
    /// </summary>
    [JsonIgnore]
    public bool IsValid => Time > 0 && Value != null;

    /// <summary>
    /// 默认构造函数
    /// </summary>
    public OptimizedParams()
    {
    }

    /// <summary>
    /// 带参数的构造函数
    /// </summary>
    /// <param name="value">参数值</param>
    /// <param name="time">时间戳（可选）</param>
    public OptimizedParams(T value, long? time = null)
    {
        Value = value;
        Time = time ?? DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
    }

    /// <summary>
    /// 创建参数的工厂方法
    /// </summary>
    /// <param name="value">参数值</param>
    /// <returns>参数实例</returns>
    public static OptimizedParams<T> Create(T value)
    {
        return new OptimizedParams<T>(value);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            // 如果值实现了IDisposable，也要释放
            if (Value is IDisposable disposableValue)
            {
                disposableValue.Dispose();
            }
            
            _disposed = true;
        }
    }
}

/// <summary>
/// 向后兼容的MQTT消息参数类
/// </summary>
/// <typeparam name="T">参数值类型</typeparam>
public sealed class Params<T> : OptimizedParams<T> where T : class
{
    /// <summary>
    /// 默认构造函数
    /// </summary>
    public Params() : base()
    {
    }

    /// <summary>
    /// 带参数的构造函数
    /// </summary>
    /// <param name="value">参数值</param>
    /// <param name="time">时间戳（可选）</param>
    public Params(T value, long? time = null) : base(value, time)
    {
    }

    /// <summary>
    /// 从优化参数创建兼容参数
    /// </summary>
    /// <param name="optimizedParams">优化参数</param>
    public Params(OptimizedParams<T> optimizedParams) : base(optimizedParams.Value!, optimizedParams.Time)
    {
    }
}

/// <summary>
/// 设备广播消息数据
/// </summary>
public class BeaconEventData
{
    /// <summary>
    /// 上报时间
    /// </summary>
    [JsonProperty("report_time")]
    public string report_time { get; set; }
    
    /// <summary>
    /// 设备名称
    /// </summary>
    [JsonProperty("device_name")]
    public string device_name { get; set; }
    
    /// <summary>
    /// 区域
    /// </summary>
    [JsonProperty("area")]
    public string area { get; set; }
    
    /// <summary>
    /// 集群
    /// </summary>
    [JsonProperty("cluster")]
    public string cluster { get; set; }
    
    /// <summary>
    /// 设备编号
    /// </summary>
    [JsonProperty("number")]
    public string number { get; set; }
    
    /// <summary>
    /// 设备UUID
    /// </summary>
    [JsonProperty("uuid")]
    public string uuid { get; set; }
    
    /// <summary>
    /// 设备类型
    /// </summary>
    [JsonProperty("type")]
    public string type { get; set; }
    
    /// <summary>
    /// 设备版本
    /// </summary>
    [JsonProperty("version")]
    public string version { get; set; }
    
    /// <summary>
    /// 信号强度（扩展属性）
    /// </summary>
    [JsonIgnore]
    public int SignalStrength { get; set; }
    
    /// <summary>
    /// 电池电量（扩展属性）
    /// </summary>
    [JsonIgnore]
    public int BatteryLevel { get; set; }
    
    /// <summary>
    /// 设备密钥（扩展属性）
    /// </summary>
    [JsonIgnore]
    public string DeviceKey { get; set; }
    
    /// <summary>
    /// 硬件版本（扩展属性）
    /// </summary>
    [JsonIgnore]
    public string HardwareVersion { get; set; }
    
    /// <summary>
    /// 制造商（扩展属性）
    /// </summary>
    [JsonIgnore]
    public string Manufacturer { get; set; }
    
    /// <summary>
    /// 设备类型（扩展属性，向后兼容）
    /// </summary>
    [JsonIgnore]
    public string DeviceType => type;
    
    /// <summary>
    /// 设备名称（扩展属性，向后兼容）
    /// </summary>
    [JsonIgnore]
    public string DeviceName => device_name;
    
    /// <summary>
    /// 版本（扩展属性，向后兼容）
    /// </summary>
    [JsonIgnore]
    public string Version => version;
    
    /// <summary>
    /// 唯一标识（扩展属性，向后兼容）
    /// </summary>
    [JsonIgnore]
    public string UniqueId => uuid;
}


/// <summary>
/// 照明参数数据
/// </summary>
public class SettingEventData
{
    /// <summary>
    /// 更新时间
    /// </summary>
    [JsonProperty("updated_time")]
    public string updated_time { get; set; }
    
    /// <summary>
    /// 设备名称
    /// </summary>
    [JsonProperty("device_name")]
    public string device_name { get; set; }
    
    /// <summary>
    /// 区域
    /// </summary>
    [JsonProperty("area")]
    public string area { get; set; }
    
    /// <summary>
    /// 设备编号
    /// </summary>
    [JsonProperty("number")]
    public string number { get; set; }
    
    /// <summary>
    /// 设备UUID
    /// </summary>
    [JsonProperty("uuid")]
    public string uuid { get; set; }
    
    /// <summary>
    /// 光感控制模式
    /// </summary>
    [JsonProperty("alscontrol_mode")]
    public string alscontrol_mode { get; set; }
    
    /// <summary>
    /// 灯光模式
    /// </summary>
    [JsonProperty("light_mode")]
    public string light_mode { get; set; }
    
    /// <summary>
    /// 色温亮度
    /// </summary>
    [JsonProperty("cct_bright")]
    public string cct_bright { get; set; }
    
    /// <summary>
    /// 延时模式
    /// </summary>
    [JsonProperty("delay_mode")]
    public string delay_mode { get; set; }
    
    /// <summary>
    /// 延时时间
    /// </summary>
    [JsonProperty("delay_time")]
    public string delay_time { get; set; }
    
    /// <summary>
    /// 延时时间2
    /// </summary>
    [JsonProperty("delay_time2")]
    public string delay_time2 { get; set; }
    
    /// <summary>
    /// 场景有效性
    /// </summary>
    [JsonProperty("scene_validity")]
    public string scene_validity { get; set; }
    
    /// <summary>
    /// 高亮度
    /// </summary>
    [JsonProperty("high_bright")]
    public string high_bright { get; set; }
    
    /// <summary>
    /// 场景编号
    /// </summary>
    [JsonProperty("scene_no")]
    public string scene_no { get; set; }
    
    /// <summary>
    /// 待机亮度
    /// </summary>
    [JsonProperty("standby_bright")]
    public string standby_bright { get; set; }
    
    /// <summary>
    /// 亮度
    /// </summary>
    public int Brightness { get; set; }
    
    /// <summary>
    /// 色温
    /// </summary>
    public int ColorTemperature { get; set; }
    
    /// <summary>
    /// 红色值
    /// </summary>
    public int Red { get; set; }
    
    /// <summary>
    /// 绿色值
    /// </summary>
    public int Green { get; set; }
    
    /// <summary>
    /// 蓝色值
    /// </summary>
    public int Blue { get; set; }
    
    /// <summary>
    /// 白色值
    /// </summary>
    public int White { get; set; }
    
    /// <summary>
    /// 过渡时间
    /// </summary>
    public int TransitionTime { get; set; }
}

/// <summary>
/// 传感器配置数据
/// </summary>
public class SensorEventData
{
    /// <summary>
    /// 更新时间
    /// </summary>
    [JsonProperty("updated_time")]
    public string updated_time { get; set; }
    
    /// <summary>
    /// 设备名称
    /// </summary>
    [JsonProperty("device_name")]
    public string device_name { get; set; }
    
    /// <summary>
    /// 区域
    /// </summary>
    [JsonProperty("area")]
    public string area { get; set; }
    
    /// <summary>
    /// 设备编号
    /// </summary>
    [JsonProperty("number")]
    public string number { get; set; }
    
    /// <summary>
    /// 设备UUID
    /// </summary>
    [JsonProperty("uuid")]
    public string uuid { get; set; }
    
    /// <summary>
    /// 传感器状态
    /// </summary>
    [JsonProperty("sensor_status")]
    public string sensor_status { get; set; }
    
    /// <summary>
    /// 传感器同步
    /// </summary>
    [JsonProperty("sensor_sync")]
    public string sensor_sync { get; set; }
    
    /// <summary>
    /// 传感器间隔
    /// </summary>
    [JsonProperty("sensor_interval")]
    public string sensor_interval { get; set; }
    
    /// <summary>
    /// 温度
    /// </summary>
    [JsonProperty("temperature")]
    public double Temperature { get; set; }
    
    /// <summary>
    /// 湿度
    /// </summary>
    [JsonProperty("humidity")]
    public double Humidity { get; set; }
}


/// <summary>
/// 能耗信息数据
/// </summary>
public class ConsumptionEventData
{
    /// <summary>
    /// 采集时间
    /// </summary>
    [JsonProperty("acquisition_time")]
    public string acquisition_time { get; set; }
    
    /// <summary>
    /// 设备名称
    /// </summary>
    [JsonProperty("device_name")]
    public string device_name { get; set; }
    
    /// <summary>
    /// 区域
    /// </summary>
    [JsonProperty("area")]
    public string area { get; set; }
    
    /// <summary>
    /// 设备编号
    /// </summary>
    [JsonProperty("number")]
    public string number { get; set; }
    
    /// <summary>
    /// 设备UUID
    /// </summary>
    [JsonProperty("uuid")]
    public string uuid { get; set; }
    
    /// <summary>
    /// 能耗持续时间
    /// </summary>
    [JsonProperty("energy_dur")]
    public int energy_dur { get; set; }
    
    /// <summary>
    /// 照明持续时间
    /// </summary>
    [JsonProperty("lighting_dur")]
    public int lighting_dur { get; set; }
    
    /// <summary>
    /// 功率（整数）
    /// </summary>
    [JsonProperty("power")]
    public float power { get; set; }
    
    /// <summary>
    /// 传感器持续时间
    /// </summary>
    [JsonProperty("sensor_dur")]
    public int sensor_dur { get; set; }
    
    /// <summary>
    /// 时间持续时间
    /// </summary>
    [JsonProperty("time_dur")]
    public int time_dur { get; set; }
    
    /// <summary>
    /// 功率（扩展属性，向后兼容）
    /// </summary>
    [JsonIgnore]
    public double Power { get; set; }
    
    /// <summary>
    /// 电压（扩展属性，向后兼容）
    /// </summary>
    [JsonIgnore]
    public double Voltage { get; set; }
    
    /// <summary>
    /// 电流（扩展属性，向后兼容）
    /// </summary>
    [JsonIgnore]
    public double Current { get; set; }
}


/// <summary>
/// 心跳消息数据
/// </summary>
public class HeartbeatEventData
{
    /// <summary>
    /// 更新时间
    /// </summary>
    [JsonProperty("updated_time")]
    public string updated_time { get; set; }
    
    /// <summary>
    /// 设备名称
    /// </summary>
    [JsonProperty("device_name")]
    public string device_name { get; set; }
    
    /// <summary>
    /// 区域
    /// </summary>
    [JsonProperty("area")]
    public string area { get; set; }
    
    /// <summary>
    /// 集群
    /// </summary>
    [JsonProperty("cluster")]
    public string cluster { get; set; }
    
    /// <summary>
    /// 设备编号
    /// </summary>
    [JsonProperty("number")]
    public string number { get; set; }
    
    /// <summary>
    /// 设备UUID
    /// </summary>
    [JsonProperty("uuid")]
    public string uuid { get; set; }
    
    /// <summary>
    /// 灯光模式
    /// </summary>
    [JsonProperty("light_mode")]
    public string light_mode { get; set; }
    
    /// <summary>
    /// 当前亮度
    /// </summary>
    [JsonProperty("current_bright")]
    public string current_bright { get; set; }
    
    /// <summary>
    /// 当前色温
    /// </summary>
    [JsonProperty("current_cct")]
    public string current_cct { get; set; }
    
    /// <summary>
    /// 内存使用率（扩展属性，向后兼容）
    /// </summary>
    [JsonIgnore]
    public double MemoryUsage { get; set; }
    
    /// <summary>
    /// 系统负载（扩展属性，向后兼容）
    /// </summary>
    [JsonIgnore]
    public double SystemLoad { get; set; }
    
    /// <summary>
    /// 网络质量（扩展属性，向后兼容）
    /// </summary>
    [JsonIgnore]
    public int NetworkQuality { get; set; }
    
    /// <summary>
    /// 状态（扩展属性，向后兼容）
    /// </summary>
    [JsonIgnore]
    public string Status { get; set; }
    
    /// <summary>
    /// 在线设备数量（扩展属性，向后兼容）
    /// </summary>
    [JsonIgnore]
    public int OnlineDeviceCount { get; set; }
    
    /// <summary>
    /// 信号强度（扩展属性，向后兼容）
    /// </summary>
    [JsonIgnore]
    public int SignalStrength { get; set; }
}

/// <summary>
/// 触发事件数据
/// </summary>
public class TriggerEventData
{
    /// <summary>
    /// 更新时间
    /// </summary>
    [JsonProperty("updated_time")]
    public string updated_time { get; set; }
    
    /// <summary>
    /// 设备名称
    /// </summary>
    [JsonProperty("device_name")]
    public string device_name { get; set; }
    
    /// <summary>
    /// 区域
    /// </summary>
    [JsonProperty("area")]
    public string area { get; set; }
    
    /// <summary>
    /// 设备编号
    /// </summary>
    [JsonProperty("number")]
    public string number { get; set; }
    
    /// <summary>
    /// 设备UUID
    /// </summary>
    [JsonProperty("uuid")]
    public string uuid { get; set; }
    
    /// <summary>
    /// 触发类型
    /// </summary>
    [JsonProperty("trigger_type")]
    public int trigger_type { get; set; }
    
    /// <summary>
    /// 触发值
    /// </summary>
    [JsonProperty("trigger_value")]
    public string trigger_value { get; set; }
    
    /// <summary>
    /// 设备Id（扩展属性，向后兼容）
    /// </summary>
    [JsonIgnore]
    public string DeviceId { get; set; }
    
    /// <summary>
    /// 触发类型（扩展属性，向后兼容）
    /// </summary>
    [JsonIgnore]
    public int TriggerType { get; set; }
    
    /// <summary>
    /// 触发值（扩展属性，向后兼容）
    /// </summary>
    [JsonIgnore]
    public string Value { get; set; }
}

/// <summary>
/// 调光参数事件参数
/// </summary>
public class DimmingEventData
{
    public string updated_time { get; set; }
    public string device_name { get; set; }
    public string area { get; set; }
    public string number { get; set; }
    public string uuid { get; set; }
    public string bright_falltime { get; set; }
    public string bright_risetime { get; set; }
    public string cct_falltime { get; set; }
    public string cct_risetime { get; set; }
}

/// <summary>
/// network（mesh组网参数）
/// </summary>
public class NetworkEventData
{

    public string updated_time { get; set; }
    public string device_name { get; set; }
    public string area { get; set; }
    public string number { get; set; }
    public string uuid { get; set; }
    public string group_ttl { get; set; }
    public string nwk_ttl { get; set; }
    public string node_type { get; set; }
    public string tx_times { get; set; }
}

/// <summary>
/// irc（红外遥控器配置）
/// </summary>
public class IrcEventData
{
    public string updated_time { get; set; }
    public string device_name { get; set; }
    public string area { get; set; }
    public string number { get; set; }
    public string uuid { get; set; }
    public string irc_status { get; set; }
}

/// <summary>
/// 配置事件数据
/// </summary>
public class ConfigEventData
{
    public string updated_time { get; set; }
    public string device_name { get; set; }
    public string area { get; set; }
    public string number { get; set; }
    public string uuid { get; set; }
    
    /// <summary>
    /// 配置类型
    /// </summary>
    public string ConfigType { get; set; }
    
    /// <summary>
    /// 配置值
    /// </summary>
    public string ConfigValue { get; set; }
}

/// <summary>
/// 升级事件数据
/// </summary>
public class UpgradeEventData
{
    public string updated_time { get; set; }
    public string device_name { get; set; }
    public string area { get; set; }
    public string number { get; set; }
    public string uuid { get; set; }
    
    /// <summary>
    /// 版本号
    /// </summary>
    public string Version { get; set; }
    
    /// <summary>
    /// 升级状态
    /// </summary>
    public string Status { get; set; }
    
    /// <summary>
    /// 升级进度
    /// </summary>
    public int Progress { get; set; }
}

/// <summary>
/// 日志事件数据
/// </summary>
public class LogEventData
{
    public string updated_time { get; set; }
    public string device_name { get; set; }
    public string area { get; set; }
    public string number { get; set; }
    public string uuid { get; set; }
    
    /// <summary>
    /// 日志级别
    /// </summary>
    public string Level { get; set; }
    
    /// <summary>
    /// 日志消息
    /// </summary>
    public string Message { get; set; }
    
    /// <summary>
    /// 日志时间戳
    /// </summary>
    public long Timestamp { get; set; }
}

/// <summary>
/// 状态事件数据
/// </summary>
public class StatusEventData
{
    public string updated_time { get; set; }
    public string device_name { get; set; }
    public string area { get; set; }
    public string number { get; set; }
    public string uuid { get; set; }
    
    /// <summary>
    /// 设备状态
    /// </summary>
    public string Status { get; set; }
    
    /// <summary>
    /// 状态描述
    /// </summary>
    public string Description { get; set; }
}

/// <summary>
/// 报警事件数据
/// </summary>
public class AlarmEventData
{
    public string updated_time { get; set; }
    public string device_name { get; set; }
    public string area { get; set; }
    public string number { get; set; }
    public string uuid { get; set; }
    
    /// <summary>
    /// 报警类型
    /// </summary>
    public string AlarmType { get; set; }
    
    /// <summary>
    /// 报警级别
    /// </summary>
    public string Level { get; set; }
    
    /// <summary>
    /// 报警描述
    /// </summary>
    public string Description { get; set; }
    
    /// <summary>
    /// 报警时间戳
    /// </summary>
    public long Timestamp { get; set; }
}
