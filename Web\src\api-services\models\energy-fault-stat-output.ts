/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { FaultLevelStatItem } from './fault-level-stat-item';
import { FaultTypeStatItem } from './fault-type-stat-item';
/**
 * 故障统计输出参数
 * @export
 * @interface EnergyFaultStatOutput
 */
export interface EnergyFaultStatOutput {
    /**
     * 故障总数
     * @type {number}
     * @memberof EnergyFaultStatOutput
     */
    totalCount?: number;
    /**
     * 待处理故障数
     * @type {number}
     * @memberof EnergyFaultStatOutput
     */
    pendingCount?: number;
    /**
     * 处理中故障数
     * @type {number}
     * @memberof EnergyFaultStatOutput
     */
    processingCount?: number;
    /**
     * 已解决故障数
     * @type {number}
     * @memberof EnergyFaultStatOutput
     */
    resolvedCount?: number;
    /**
     * 今日新增故障数
     * @type {number}
     * @memberof EnergyFaultStatOutput
     */
    todayNewCount?: number;
    /**
     * 本月新增故障数
     * @type {number}
     * @memberof EnergyFaultStatOutput
     */
    monthNewCount?: number;
    /**
     * 平均处理时长(小时)
     * @type {number}
     * @memberof EnergyFaultStatOutput
     */
    avgProcessHours?: number;
    /**
     * 故障解决率
     * @type {number}
     * @memberof EnergyFaultStatOutput
     */
    resolveRate?: number;
    /**
     * 低级故障数
     * @type {number}
     * @memberof EnergyFaultStatOutput
     */
    lowLevelCount?: number;
    /**
     * 中级故障数
     * @type {number}
     * @memberof EnergyFaultStatOutput
     */
    mediumLevelCount?: number;
    /**
     * 高级故障数
     * @type {number}
     * @memberof EnergyFaultStatOutput
     */
    highLevelCount?: number;
    /**
     * 严重故障数
     * @type {number}
     * @memberof EnergyFaultStatOutput
     */
    criticalLevelCount?: number;
    /**
     * 修复率
     * @type {number}
     * @memberof EnergyFaultStatOutput
     */
    repairRate?: number;
    /**
     * 平均修复时间(小时)
     * @type {number}
     * @memberof EnergyFaultStatOutput
     */
    avgRepairTime?: number;
    /**
     * 热门故障类型排行
     * @type {Array<FaultTypeStatItem>}
     * @memberof EnergyFaultStatOutput
     */
    topFaultTypes?: Array<FaultTypeStatItem> | null;
    /**
     * 按故障类型统计
     * @type {Array<FaultTypeStatItem>}
     * @memberof EnergyFaultStatOutput
     */
    typeStats?: Array<FaultTypeStatItem> | null;
    /**
     * 按故障等级统计
     * @type {Array<FaultLevelStatItem>}
     * @memberof EnergyFaultStatOutput
     */
    levelStats?: Array<FaultLevelStatItem> | null;
    /**
     * 统计时间
     * @type {string}
     * @memberof EnergyFaultStatOutput
     */
    statTime?: string | null;
    /**
     * 已修复数量
     * @type {number}
     * @memberof EnergyFaultStatOutput
     */
    repairedCount?: number;
    /**
     * 已忽略数量
     * @type {number}
     * @memberof EnergyFaultStatOutput
     */
    ignoredCount?: number;
}
