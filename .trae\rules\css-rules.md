---
description: Uni-App X CSS 规则 (ucss)
globs: *.uvue,*.css,*.less,*.scss,*.sass
alwaysApply: false
---

# CSS 规则 (ucss)

## 基础语法
- 支持大部分标准 CSS 语法
- 使用 Flex 布局作为默认布局方式
- 支持响应式单位 rpx
- 部分 CSS 属性在不同平台有差异

## 文件组织
```
// 全局样式
@import "@/styles/common.css";
@import "@/styles/variables.css";

// 组件样式
<style scoped>
/* 组件特定样式 */
</style>

// 平台特定样式
/* #ifdef APP */
.app-only {
  /* App 专用样式 */
}
/* #endif */
```

## 单位系统

### rpx 单位
- 响应式像素单位，根据屏幕宽度自适应
- 设计稿宽度为 750px 时，1rpx = 1px
- 推荐用于宽度、高度、边距等

```css
.container {
  width: 750rpx;        /* 全屏宽度 */
  height: 200rpx;       /* 响应式高度 */
  padding: 20rpx;       /* 响应式内边距 */
  margin: 10rpx 20rpx;  /* 响应式外边距 */
}
```

### px 单位
- 固定像素单位
- 用于边框、字体大小等不需要缩放的属性

```css
.text {
  font-size: 16px;      /* 固定字体大小 */
  border: 1px solid #ccc; /* 固定边框 */
}
```

### 相对单位
- `%` - 百分比单位
- `vh/vw` - 视口单位
- `em/rem` - 字体相对单位

## 选择器规范

### 支持的选择器
```css
/* 元素选择器 */
view { }
text { }
image { }

/* 类选择器 */
.class-name { }

/* ID 选择器 */
#element-id { }

/* 后代选择器 */
.parent .child { }

/* 子选择器 */
.parent > .child { }

/* 伪类选择器 */
.button:active { }
.input:focus { }
```

### 选择器最佳实践
- 避免使用过深的嵌套选择器
- 优先使用类选择器而非标签选择器
- 合理使用 scoped 样式避免样式冲突

## 布局系统

### Flex 布局
```css
.flex-container {
  display: flex;
  flex-direction: row;    /* row | column */
  justify-content: center; /* flex-start | center | flex-end | space-between | space-around */
  align-items: center;    /* flex-start | center | flex-end | stretch */
  flex-wrap: wrap;        /* nowrap | wrap */
}

.flex-item {
  flex: 1;               /* 弹性增长 */
  flex-shrink: 0;        /* 不收缩 */
  flex-basis: 200rpx;    /* 基础尺寸 */
}
```

### 定位布局
```css
.positioned {
  position: relative;     /* static | relative | absolute | fixed */
  top: 10rpx;
  left: 20rpx;
  z-index: 10;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 999;
}
```

### Grid 布局（部分支持）
```css
.grid-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 20rpx;
}
```

## 样式最佳实践

### 命名规范
```css
/* BEM 命名法 */
.block { }
.block__element { }
.block--modifier { }

/* 语义化命名 */
.header { }
.navigation { }
.content { }
.sidebar { }
.footer { }

/* 状态类 */
.is-active { }
.is-disabled { }
.is-loading { }
```

### 代码组织
```css
/* 1. 定位相关 */
position: relative;
top: 0;
left: 0;
z-index: 1;

/* 2. 盒模型 */
display: flex;
width: 100%;
height: 200rpx;
padding: 20rpx;
margin: 10rpx;
border: 1px solid #ccc;

/* 3. 字体相关 */
font-size: 16px;
font-weight: bold;
line-height: 1.5;
color: #333;
text-align: center;

/* 4. 背景和装饰 */
background-color: #f5f5f5;
border-radius: 8rpx;
box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);

/* 5. 动画和过渡 */
transition: all 0.3s ease;
transform: translateX(10rpx);
```

### 性能优化
```css
/* 避免复杂选择器 */
/* 不推荐 */
.page .content .list .item .title { }

/* 推荐 */
.item-title { }

/* 使用 transform 代替改变位置属性 */
/* 不推荐 */
.move {
  left: 100rpx;
  top: 100rpx;
}

/* 推荐 */
.move {
  transform: translate(100rpx, 100rpx);
}
```

### 跨平台兼容性
```css
/* 使用条件编译处理平台差异 */
.container {
  padding: 20rpx;
}

/* #ifdef APP */
.container {
  padding-top: var(--status-bar-height);
}
/* #endif */

/* #ifdef H5 */
.container {
  max-width: 750px;
  margin: 0 auto;
}
/* #endif */
```

### 常用样式模式
```css
/* 水平垂直居中 */
.center {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 文本省略 */
.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* 多行文本省略 */
.multi-ellipsis {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 清除浮动 */
.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

/* 响应式图片 */
.responsive-image {
  width: 100%;
  height: auto;
  object-fit: cover;
}
```