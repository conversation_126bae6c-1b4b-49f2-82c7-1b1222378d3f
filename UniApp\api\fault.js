/**
 * 故障管理相关API
 */

import request from '../utils/request.js'
import { createCachedApi } from '../src/utils/apiCache.js'

// 修复：将基础路径从 /api/fault 改为 /api/energyFault 以匹配后端接口
const BASE_URL = '/api/energyFault'

/**
 * 获取故障列表
 * @param {Object} params 查询参数
 * @param {number} params.page 页码
 * @param {number} params.pageSize 每页数量
 * @param {string} params.keyword 关键词搜索
 * @param {string} params.status 故障状态
 * @param {string} params.level 故障等级
 * @param {string} params.deviceId 设备ID
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @returns {Promise} 故障列表
 */
export const getFaultList = (params) => {
  return request.get(`${BASE_URL}/list`, params, {
    cache: true,
    cacheTime: 2 * 60 * 1000, // 2分钟缓存
    offline: true
  })
}

/**
 * 获取故障详情
 * @param {string} faultId 故障ID
 * @returns {Promise} 故障详情
 */
export const getFaultDetail = (id) => {
  return request.get(`${BASE_URL}/${id}`, {}, {
    cache: true,
    cacheTime: 5 * 60 * 1000, // 5分钟缓存
    offline: true
  })
}

/**
 * 报告故障
 * @param {Object} faultData 故障数据
 * @param {string} faultData.deviceId 设备ID
 * @param {string} faultData.title 故障标题
 * @param {string} faultData.description 故障描述
 * @param {string} faultData.level 故障等级 low/medium/high/critical
 * @param {string} faultData.type 故障类型
 * @param {Array} faultData.images 故障图片
 * @param {string} faultData.reporterId 报告人ID
 * @returns {Promise} 报告结果
 */
export const createFault = (data) => {
  return request.post(`${BASE_URL}`, data, {
    cache: false,
    offline: false
  })
}

/**
 * 更新故障信息
 * @param {string} faultId 故障ID
 * @param {Object} faultData 故障数据
 * @returns {Promise} 更新结果
 */
export const updateFault = (id, data) => {
  return request.put(`${BASE_URL}/${id}`, data, {
    cache: false,
    offline: false
  })
}

/**
 * 删除故障记录
 * @param {string} faultId 故障ID
 * @returns {Promise} 删除结果
 */
export const deleteFault = (faultId) => {
  return request.delete(`${BASE_URL}/${faultId}`, {
    cache: false,
    offline: false
  })
}

/**
 * 分配故障处理人员
 * @param {string} faultId 故障ID
 * @param {Object} assignData 分配数据
 * @param {string} assignData.assigneeId 处理人员ID
 * @param {string} assignData.priority 优先级
 * @param {string} assignData.expectedTime 预期完成时间
 * @param {string} assignData.remark 备注
 * @returns {Promise} 分配结果
 */
export const assignFault = (faultId, assignData) => {
  return request.post(`${BASE_URL}/assign/${faultId}`, assignData, {
    cache: false,
    offline: false
  })
}

/**
 * 开始处理故障
 * @param {string} faultId 故障ID
 * @param {Object} processData 处理数据
 * @param {string} processData.solution 解决方案
 * @param {string} processData.remark 处理备注
 * @returns {Promise} 处理结果
 */
export const startProcessFault = (faultId, processData) => {
  return request.post(`${BASE_URL}/process/start/${faultId}`, processData, {
    cache: false,
    offline: false
  })
}

/**
 * 完成故障处理
 * @param {string} faultId 故障ID
 * @param {Object} completeData 完成数据
 * @param {string} completeData.solution 最终解决方案
 * @param {string} completeData.result 处理结果
 * @param {Array} completeData.images 处理后图片
 * @param {number} completeData.cost 处理成本
 * @param {string} completeData.remark 完成备注
 * @returns {Promise} 完成结果
 */
export const completeFault = (faultId, completeData) => {
  return request.post(`${BASE_URL}/process/complete/${faultId}`, completeData, {
    cache: false,
    offline: false
  })
}

/**
 * 关闭故障
 * @param {string} faultId 故障ID
 * @param {Object} closeData 关闭数据
 * @param {string} closeData.reason 关闭原因
 * @param {string} closeData.remark 关闭备注
 * @returns {Promise} 关闭结果
 */
export const closeFault = (faultId, closeData) => {
  return request.post(`${BASE_URL}/close/${faultId}`, closeData, {
    cache: false,
    offline: false
  })
}

/**
 * 重新打开故障
 * @param {string} faultId 故障ID
 * @param {Object} reopenData 重开数据
 * @param {string} reopenData.reason 重开原因
 * @param {string} reopenData.remark 重开备注
 * @returns {Promise} 重开结果
 */
export const reopenFault = (faultId, reopenData) => {
  return request.post(`${BASE_URL}/reopen/${faultId}`, reopenData, {
    cache: false,
    offline: false
  })
}

/**
 * 获取故障处理历史
 * @param {string} faultId 故障ID
 * @returns {Promise} 处理历史
 */
export const getFaultHistory = (faultId) => {
  return request.get(`${BASE_URL}/history/${faultId}`, {}, {
    cache: true,
    cacheTime: 5 * 60 * 1000, // 5分钟缓存
    offline: true
  })
}

/**
 * 添加故障处理记录
 * @param {string} faultId 故障ID
 * @param {Object} recordData 记录数据
 * @param {string} recordData.action 操作类型
 * @param {string} recordData.content 记录内容
 * @param {Array} recordData.images 相关图片
 * @param {string} recordData.remark 备注
 * @returns {Promise} 添加结果
 */
export const addFaultRecord = (faultId, recordData) => {
  return request.post(`${BASE_URL}/records/${faultId}`, recordData, {
    cache: false,
    offline: false
  })
}

/**
 * 获取故障统计概览
 * @param {Object} params 查询参数
 * @param {string} params.period 统计周期 today/week/month/year
 * @param {string} params.deviceId 设备ID
 * @param {string} params.groupId 设备分组ID
 * @returns {Promise} 故障统计概览
 */
export const getFaultStatistics = (params = {}) => {
  return request.get(`${BASE_URL}/statistics`, params, {
    cache: true,
    cacheTime: 5 * 60 * 1000, // 5分钟缓存
    offline: true
  })
}

/**
 * 获取故障趋势分析
 * @param {Object} params 查询参数
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @param {string} params.granularity 数据粒度 day/week/month
 * @param {string} params.deviceId 设备ID
 * @returns {Promise} 故障趋势数据
 */
export const getFaultTrend = (params = {}) => {
  return request.get(`${BASE_URL}/trend`, params, {
    cache: true,
    cacheTime: 5 * 60 * 1000, // 5分钟缓存
    offline: true
  })
}

/**
 * 获取故障类型分布
 * @param {Object} params 查询参数
 * @param {string} params.period 统计周期
 * @param {string} params.deviceId 设备ID
 * @returns {Promise} 故障类型分布
 */
export const getFaultTypeDistribution = (params = {}) => {
  return request.get(`${BASE_URL}/type-distribution`, params, {
    cache: true,
    cacheTime: 5 * 60 * 1000, // 5分钟缓存
    offline: true
  })
}

/**
 * 获取故障处理效率统计
 * @param {Object} params 查询参数
 * @param {string} params.period 统计周期
 * @param {string} params.assigneeId 处理人员ID
 * @returns {Promise} 处理效率统计
 */
export const getFaultProcessEfficiency = (params = {}) => {
  return request.get(`${BASE_URL}/process-efficiency`, params, {
    cache: true,
    cacheTime: 5 * 60 * 1000, // 5分钟缓存
    offline: true
  })
}

/**
 * 获取故障预警规则列表
 * @returns {Promise} 预警规则列表
 */
export const getFaultAlertRules = () => {
  return request.get(`${BASE_URL}/alert-rules`, {}, {
    cache: true,
    cacheTime: 10 * 60 * 1000, // 10分钟缓存
    offline: true
  })
}

/**
 * 创建故障预警规则
 * @param {Object} ruleData 规则数据
 * @param {string} ruleData.name 规则名称
 * @param {string} ruleData.description 规则描述
 * @param {Array} ruleData.conditions 触发条件
 * @param {Array} ruleData.actions 预警动作
 * @param {boolean} ruleData.enabled 是否启用
 * @returns {Promise} 创建结果
 */
export const createFaultAlertRule = (ruleData) => {
  return request.post(`${BASE_URL}/alert-rules`, ruleData, {
    cache: false,
    offline: false
  })
}

/**
 * 更新故障预警规则
 * @param {string} ruleId 规则ID
 * @param {Object} ruleData 规则数据
 * @returns {Promise} 更新结果
 */
export const updateFaultAlertRule = (ruleId, ruleData) => {
  return request.put(`${BASE_URL}/alert-rules/${ruleId}`, ruleData, {
    cache: false,
    offline: false
  })
}

/**
 * 删除故障预警规则
 * @param {string} ruleId 规则ID
 * @returns {Promise} 删除结果
 */
export const deleteFaultAlertRule = (ruleId) => {
  return request.delete(`${BASE_URL}/alert-rules/${ruleId}`, {
    cache: false,
    offline: false
  })
}

/**
 * 获取故障知识库
 * @param {Object} params 查询参数
 * @param {string} params.keyword 关键词
 * @param {string} params.category 分类
 * @param {string} params.faultType 故障类型
 * @returns {Promise} 知识库列表
 */
export const getFaultKnowledge = (params = {}) => {
  return request.get(`${BASE_URL}/knowledge`, params, {
    cache: true,
    cacheTime: 30 * 60 * 1000, // 30分钟缓存
    offline: true
  })
}

/**
 * 添加故障知识
 * @param {Object} knowledgeData 知识数据
 * @param {string} knowledgeData.title 标题
 * @param {string} knowledgeData.category 分类
 * @param {string} knowledgeData.faultType 故障类型
 * @param {string} knowledgeData.symptoms 故障症状
 * @param {string} knowledgeData.causes 故障原因
 * @param {string} knowledgeData.solutions 解决方案
 * @param {Array} knowledgeData.images 相关图片
 * @returns {Promise} 添加结果
 */
export const addFaultKnowledge = (knowledgeData) => {
  return request.post(`${BASE_URL}/knowledge`, knowledgeData, {
    cache: false,
    offline: false
  })
}

/**
 * 获取故障报表
 * @param {Object} params 查询参数
 * @param {string} params.type 报表类型
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @param {string} params.format 导出格式
 * @returns {Promise} 报表数据
 */
export const getFaultReport = (params = {}) => {
  return request.get(`${BASE_URL}/reports`, params, {
    cache: true,
    cacheTime: 5 * 60 * 1000, // 5分钟缓存
    offline: true
  })
}

/**
 * 生成故障报表
 * @param {Object} reportData 报表参数
 * @param {string} reportData.type 报表类型
 * @param {string} reportData.startTime 开始时间
 * @param {string} reportData.endTime 结束时间
 * @param {string} reportData.format 导出格式
 * @returns {Promise} 生成结果
 */
export const generateFaultReport = (reportData) => {
  return request.post(`${BASE_URL}/reports/generate`, reportData, {
    cache: false,
    offline: false
  })
}

/**
 * 获取故障处理人员列表
 * @returns {Promise} 处理人员列表
 */
export const getFaultHandlers = () => {
  return request.get(`${BASE_URL}/handlers`, {}, {
    cache: true,
    cacheTime: 30 * 60 * 1000, // 30分钟缓存
    offline: true
  })
}

/**
 * 获取故障类型列表
 * @returns {Promise} 故障类型列表
 */
export const getFaultTypes = () => {
  return request.get(`${BASE_URL}/types`, {}, {
    cache: true,
    cacheTime: 30 * 60 * 1000, // 30分钟缓存
    offline: true
  })
}

/**
 * 上传故障相关图片
 * @param {string} filePath 图片文件路径
 * @returns {Promise} 上传结果
 */
export const uploadFaultImage = (filePath) => {
  // 修复：使用动态配置的baseURL而非硬编码URL
  return request.upload(`${BASE_URL}/upload-image`, filePath, {
    name: 'file',
    silent: false,
    loadingText: '上传中...'
  })
}

// 注释掉缓存版本的API函数，因为 createCachedApi 函数未正确导入
// 如果需要使用缓存功能，请先导入 createCachedApi 函数
// const cachedGetFaultList = createCachedApi(getFaultList, 'fault_list', 2 * 60 * 1000) // 2分钟缓存
// const cachedGetFaultDetail = createCachedApi(getFaultDetail, 'fault_detail', 5 * 60 * 1000) // 5分钟缓存
// const cachedGetFaultHistory = createCachedApi(getFaultHistory, 'fault_history', 5 * 60 * 1000) // 5分钟缓存
// const cachedGetFaultStatistics = createCachedApi(getFaultStatistics, 'fault_statistics', 5 * 60 * 1000) // 5分钟缓存
// const cachedGetFaultTrend = createCachedApi(getFaultTrend, 'fault_trend', 5 * 60 * 1000) // 5分钟缓存
// const cachedGetFaultTypeDistribution = createCachedApi(getFaultTypeDistribution, 'fault_type_distribution', 5 * 60 * 1000) // 5分钟缓存
// const cachedGetFaultProcessEfficiency = createCachedApi(getFaultProcessEfficiency, 'fault_process_efficiency', 5 * 60 * 1000) // 5分钟缓存
// const cachedGetFaultAlertRules = createCachedApi(getFaultAlertRules, 'fault_alert_rules', 10 * 60 * 1000) // 10分钟缓存
// const cachedGetFaultKnowledge = createCachedApi(getFaultKnowledge, 'fault_knowledge', 30 * 60 * 1000) // 30分钟缓存
// const cachedGetFaultReport = createCachedApi(getFaultReport, 'fault_report', 5 * 60 * 1000) // 5分钟缓存
// const cachedGetFaultHandlers = createCachedApi(getFaultHandlers, 'fault_handlers', 30 * 60 * 1000) // 30分钟缓存
// const cachedGetFaultTypes = createCachedApi(getFaultTypes, 'fault_types', 30 * 60 * 1000) // 30分钟缓存

// 默认导出所有故障管理相关API
export default {
  // 原始API函数
  getFaultList,
  getFaultDetail,
  createFault,
  updateFault,
  deleteFault,
  assignFault,
  startProcessFault,
  completeFault,
  closeFault,
  reopenFault,
  getFaultHistory,
  addFaultRecord,
  getFaultStatistics,
  getFaultTrend,
  getFaultTypeDistribution,
  getFaultProcessEfficiency,
  getFaultAlertRules,
  createFaultAlertRule,
  updateFaultAlertRule,
  deleteFaultAlertRule,
  getFaultKnowledge,
  addFaultKnowledge,
  getFaultReport,
  generateFaultReport,
  getFaultHandlers,
  getFaultTypes,
  uploadFaultImage
  // 缓存版本API函数已注释，如需使用请先导入 createCachedApi 函数
}