/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 设备统计信息
 * @export
 * @interface DeviceStatInfo
 */
export interface DeviceStatInfo {
    /**
     * 今日用电量(kWh)
     * @type {number}
     * @memberof DeviceStatInfo
     */
    todayEnergy?: number;
    /**
     * 本月用电量(kWh)
     * @type {number}
     * @memberof DeviceStatInfo
     */
    monthEnergy?: number;
    /**
     * 累计用电量(kWh)
     * @type {number}
     * @memberof DeviceStatInfo
     */
    totalEnergy?: number;
    /**
     * 今日控制次数
     * @type {number}
     * @memberof DeviceStatInfo
     */
    todayControlCount?: number;
    /**
     * 本月控制次数
     * @type {number}
     * @memberof DeviceStatInfo
     */
    monthControlCount?: number;
    /**
     * 累计控制次数
     * @type {number}
     * @memberof DeviceStatInfo
     */
    totalControlCount?: number;
    /**
     * 故障次数
     * @type {number}
     * @memberof DeviceStatInfo
     */
    faultCount?: number;
    /**
     * 运行天数
     * @type {number}
     * @memberof DeviceStatInfo
     */
    runDays?: number;
    /**
     * 在线时长(小时)
     * @type {number}
     * @memberof DeviceStatInfo
     */
    onlineHours?: number;
}
