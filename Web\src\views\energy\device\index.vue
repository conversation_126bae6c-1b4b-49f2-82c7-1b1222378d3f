<template>
	<div ref="containerRef"
		class="energy-device-container device-management mobile-device-control responsive-table mobile-safe-area">
		<el-card shadow="hover" :body-style="{ padding: 5 }">
			<el-form :model="state.queryParams" ref="queryForm" :inline="true">
				<el-form-item label="设备名称">
					<el-input v-model="state.queryParams.deviceName" placeholder="设备名称" clearable />
				</el-form-item>
				<el-form-item label="设备编号">
					<el-input v-model="state.queryParams.deviceCode" placeholder="设备编号" clearable />
				</el-form-item>
				<el-form-item label="设备类型">
					<el-select v-model="state.queryParams.deviceType" placeholder="请选择设备类型" clearable>
						<el-option v-for="option in deviceTypeOptions" :key="option.value" :label="option.label"
							:value="option.value" />
					</el-select>
				</el-form-item>
				<el-form-item label="设备状态">
					<el-select v-model="state.queryParams.status" placeholder="请选择设备状态" clearable>
						<el-option label="在线" :value="1" />
						<el-option label="离线" :value="0" />
						<el-option label="故障" :value="2" />
					</el-select>
				</el-form-item>
				<el-form-item>
					<el-button-group>
						<el-button type="primary" icon="ele-Search" @click="handleQuery"> 查询 </el-button>
						<el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
					</el-button-group>
				</el-form-item>
				<el-form-item>
					<el-button-group>
						<el-button type="primary" icon="ele-Plus" @click="openAddDevice"> 新增设备 </el-button>
						<el-button type="success" icon="ele-Upload" @click="openDeviceImport"> 批量导入 </el-button>
					</el-button-group>
				</el-form-item>
				<el-form-item>
					<el-button-group>
						<el-button type="warning" icon="ele-Collection" @click="openDeviceGroup"> 分组管理 </el-button>
						<el-button type="info" icon="ele-Location" @click="openDeviceMap"> 地图定位 </el-button>
					</el-button-group>
				</el-form-item>
			</el-form>
		</el-card>

		<el-card class="full-table" shadow="hover" style="margin-top: 5px">
			<el-table ref="tableRef" :data="state.deviceData" style="width: 100%" v-loading="state.loading" border>
				<el-table-column type="index" label="序号" width="55" align="center" fixed />
				<el-table-column prop="deviceName" label="设备名称" width="150" align="center" show-overflow-tooltip />
				<el-table-column prop="deviceCode" label="设备编号" width="150" align="center" show-overflow-tooltip />
				<el-table-column prop="deviceType" label="设备类型" width="100" align="center" show-overflow-tooltip>
					<template #default="scope">
						<el-tag :type="getDeviceTypeTagType(scope.row.deviceType)">
							{{ getDeviceTypeLabel(scope.row.deviceType) }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="location" label="安装位置" width="150" align="center" show-overflow-tooltip />
				<el-table-column prop="power" label="功率(W)" width="100" align="center" show-overflow-tooltip />
				<el-table-column prop="brightness" label="亮度(%)" width="100" align="center" show-overflow-tooltip>
					<template #default="scope">
						<el-progress :percentage="scope.row.brightness" :show-text="false" :stroke-width="8" />
						<span style="margin-left: 10px">{{ scope.row.brightness }}%</span>
					</template>
				</el-table-column>
				<el-table-column label="设备状态" width="100" align="center" show-overflow-tooltip>
					<template #default="scope">
						<el-tag v-if="scope.row.status === 1" type="success">在线</el-tag>
						<el-tag v-else-if="scope.row.status === 0" type="danger">离线</el-tag>
						<el-tag v-else-if="scope.row.status === 2" type="warning">故障</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="下属设备" width="120" align="center" show-overflow-tooltip>
					<template #default="scope">
						<el-button 
							v-if="scope.row.deviceType == 'gateway'" 
							icon="ele-Search" 
							text 
							type="primary" 
							size="small"
							:loading="state.scanningDevices[scope.row.id]"
							@click="scanSubDevices(scope.row)"
							title="扫描下属设备">
							{{ state.scanningDevices[scope.row.id] ? '扫描中' : '扫描' }}
						</el-button>
						<span v-else class="text-gray-400">-</span>
					</template>
				</el-table-column>
				<el-table-column prop="installDate" label="安装日期" width="120" align="center" show-overflow-tooltip>
					<template #default="scope">
						{{ scope.row.installDate ? formatDate(new Date(scope.row.installDate), 'YYYY-mm-dd') : '-' }}
					</template>
				</el-table-column>
				<el-table-column label="操作" width="200" align="center" fixed="right" show-overflow-tooltip>
					<template #default="scope">
						<el-tooltip content="编辑" placement="top">
							<el-button icon="ele-Edit" text type="primary" @click="openEditDevice(scope.row)">
							</el-button>
						</el-tooltip>
						<el-tooltip content="控制" placement="top">
							<el-button icon="ele-Setting" text type="warning" :disabled="scope.row.status !== 1"
								@click="openControlDevice(scope.row)"> </el-button>
						</el-tooltip>
						<el-tooltip content="删除" placement="top">
							<el-button icon="ele-Delete" text type="danger" @click="delDevice(scope.row)"> </el-button>
						</el-tooltip>
						<el-dropdown>
							<el-button icon="ele-MoreFilled" size="small" text type="primary"
								style="padding-left: 12px" />
							<template #dropdown>
								<el-dropdown-menu>
									<el-dropdown-item icon="ele-View"
										@click="viewDeviceDetail(scope.row)">查看详情</el-dropdown-item>
									<el-dropdown-item icon="ele-DataAnalysis"
										@click="viewEnergyConsumption(scope.row)">能耗统计</el-dropdown-item>
									<el-dropdown-item icon="ele-Warning"
										@click="viewFaultHistory(scope.row)">故障历史</el-dropdown-item>
								</el-dropdown-menu>
							</template>
						</el-dropdown>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination v-model:currentPage="state.tableParams.page" v-model:page-size="state.tableParams.pageSize"
				@current-change="handleCurrentChange" @size-change="handleSizeChange" :total="state.tableParams.total"
				layout="total, sizes, prev, pager, next, jumper" />
		</el-card>

		<!-- 设备编辑对话框 -->
		<EditDevice ref="editDeviceRef" :title="state.editDeviceTitle" @handleQuery="handleQuery" />

		<!-- 设备控制对话框 -->
		<ControlDevice ref="controlDeviceRef" @handleQuery="handleQuery" />

		<!-- 设备分组管理对话框 -->
		<DeviceGroup ref="deviceGroupRef" @refresh="handleQuery" />

		<!-- 设备批量导入对话框 -->
		<DeviceImport ref="deviceImportRef" @refresh="handleQuery" @success="handleImportSuccess" />

		<!-- 设备地图定位对话框 -->
		<DeviceMap ref="deviceMapRef" @refresh="handleQuery" />
	</div>
</template>

<script lang="ts" setup name="energyDevice">
import { onMounted, reactive, ref } from 'vue';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import EditDevice from './component/editDevice.vue';
import ControlDevice from './component/controlDevice.vue';
import DeviceGroup from './component/deviceGroup.vue';
import DeviceImport from './component/deviceImport.vue';
import DeviceMap from './component/deviceMap.vue';
import { getAPI } from '/@/utils/axios-utils';
import { useDeviceApi } from '/@/api-services/device';
import { useMqttApi } from '/@/api-services/mqtt';
import { formatDate } from '/@/utils/formatTime';
import { useDeviceDetection, usePullToRefresh, useMobileTable } from '@/composables/useMobile';
import { handleApiError } from '@/utils/errorHandler';
import { useDeviceTypeDict } from '@/composables/useDeviceTypeDict';

const editDeviceRef = ref();
const controlDeviceRef = ref();
const deviceGroupRef = ref();
const deviceImportRef = ref();
const deviceMapRef = ref();
const containerRef = ref(null);
const tableRef = ref(null);

// 移动端适配
const { deviceInfo } = useDeviceDetection();
const { isRefreshing } = usePullToRefresh(containerRef, async () => {
	await handleQuery();
});
const { optimizeTable } = useMobileTable(tableRef);

// 设备类型字典数据
const { deviceTypeOptions, getDeviceTypeLabel } = useDeviceTypeDict();

const state = reactive({
	loading: false,
	deviceData: [] as Array<any>,
	queryParams: {
		deviceName: undefined,
		deviceCode: undefined,
		deviceType: undefined,
		status: undefined
	},
	tableParams: {
		page: 1,
		pageSize: 20,
		total: 0 as any,
	},
	editDeviceTitle: '',
	// MQTT设备扫描相关状态
	scanningDevices: {} as Record<string, boolean>, // 记录正在扫描的设备ID
	scannedDevices: {} as Record<string, any[]>, // 记录扫描到的下属设备
	scanResults: [] as Array<any>, // 扫描结果列表
	showScanResults: false, // 是否显示扫描结果对话框
	currentGatewayDevice: null as any, // 当前扫描的网关设备
});

onMounted(async () => {
	await handleQuery();
});

// 查询操作
const handleQuery = async () => {
	state.loading = true;
	try {
		let params = Object.assign(state.queryParams, state.tableParams);
		const deviceApi = useDeviceApi();
		let res = await deviceApi.getPage(params);
		// 修复数据绑定：axios响应拦截器返回完整的res对象，所以需要访问res.data.result
		console.log('查询设备列表响应:', res.data.result?.items);
		state.deviceData = res.data.result?.items ?? [];
		state.tableParams.total = res.data.result?.total ?? 0;
		console.log('设备数据加载成功:', state.deviceData.length, '条记录');
	} catch (error) {
      handleApiError(error, '查询设备列表', '查询设备列表失败');
    } finally {
		state.loading = false;
	}
};

// 重置操作
const resetQuery = async () => {
	state.queryParams.deviceName = undefined;
	state.queryParams.deviceCode = undefined;
	state.queryParams.deviceType = undefined;
	state.queryParams.status = undefined;
	await handleQuery();
};

// 打开新增页面
const openAddDevice = () => {
	state.editDeviceTitle = '添加设备';
	editDeviceRef.value?.openDialog({ 
		id: undefined, 
		power: 10, 
		brightness: 100, 
		status: 1,
		isOn: false,
		installDate: new Date().toISOString().split('T')[0]
	});
};

// 打开编辑页面
const openEditDevice = (row: any) => {
	state.editDeviceTitle = '编辑设备';
	editDeviceRef.value?.openDialog(row);
};

// 打开设备控制页面
const openControlDevice = (row: any) => {
	controlDeviceRef.value?.openDialog(row);
};

// 删除设备
const delDevice = (row: any) => {
	ElMessageBox.confirm(`确定删除设备：【${row.deviceName}】?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			try {
				const deviceApi = useDeviceApi();
			await deviceApi.delete({ id: row.id });
				await handleQuery();
				ElMessage.success('删除成功');
			} catch (error) {
				handleApiError(error, '删除设备', '删除设备失败');
			}
		})
		.catch(() => { });
};

// 改变页面容量
const handleSizeChange = (val: number) => {
	state.tableParams.pageSize = val;
	handleQuery();
};

// 改变页码序号
const handleCurrentChange = async (val: number) => {
	state.tableParams.page = val;
	await handleQuery();
};

// 修改设备开关状态
const changeDeviceStatus = async (row: any) => {
	try {
		const deviceApi = useDeviceApi();
		await deviceApi.control({
			deviceId: row.id,
			action: row.isOn ? 'ON' : 'OFF',
			brightness: row.brightness
		});
		ElMessage.success(`设备${row.isOn ? '开启' : '关闭'}成功`);
	} catch (error) {
		handleApiError(error, '控制设备', '控制设备失败');
		// 恢复原状态
		row.isOn = !row.isOn;
	}
};

// 查看设备详情
const viewDeviceDetail = (row: any) => {
	// 跳转到设备详情页面
	window.open(`/energy/device/detail/${row.id}`, '_blank');
};

// 查看能耗统计
const viewEnergyConsumption = (row: any) => {
	// 跳转到能耗统计页面，传递设备ID参数
	window.open(`/energy/consumption?deviceId=${row.id}&deviceName=${encodeURIComponent(row.deviceName)}`, '_blank');
};

// 查看故障历史
const viewFaultHistory = (row: any) => {
	// 跳转到故障历史页面，传递设备ID参数
	window.open(`/energy/fault?deviceId=${row.id}&deviceName=${encodeURIComponent(row.deviceName)}`, '_blank');
};

/**
 * 扫描网关下属设备
 * 通过MQTT服务接口在线查询绑定在该网关下的智能设备
 * @param gatewayDevice 网关设备信息
 */
const scanSubDevices = async (gatewayDevice: any) => {
	// 显示扫描进度
	const loadingInstance = ElLoading.service({
		lock: true,
		text: `正在启动网关 [${gatewayDevice.deviceName}] 设备扫描...`,
		background: 'rgba(0, 0, 0, 0.7)'
	});

	try {
		// 检查网关设备状态
		if (gatewayDevice.status !== 1) {
			loadingInstance.close();
			ElMessage.warning('网关设备离线，无法扫描下属设备');
			return;
		}

		// 设置扫描状态
		state.scanningDevices[gatewayDevice.id] = true;
		state.currentGatewayDevice = gatewayDevice;

		console.log(`开始扫描网关 [${gatewayDevice.deviceName}] 下的设备...`);

		// 调用MQTT设备扫描API
		const mqttApi = useMqttApi();
		// 使用网关设备名称作为GatewayName参数，区域和地址使用默认值
		const scanResponse = await mqttApi.scanDevices(
			gatewayDevice.deviceName || gatewayDevice.deviceCode || 'Gateway',
			'00 00', // area 默认值
			'00 00', // address 默认值
			30 // timeoutSeconds
		);
		
		console.log('设备扫描响应:', scanResponse);

		if (scanResponse && (scanResponse.data.result || scanResponse.data)) {
			loadingInstance.close();
			ElMessage.success('扫描已启动，正在搜索设备...');
			// 等待扫描完成并获取结果
			await waitForScanResults(scanResponse.data.result || scanResponse.data, gatewayDevice);
		} else {
			loadingInstance.close();
			ElMessage.error('扫描启动失败：服务器响应异常');
		}

	} catch (error) {
		loadingInstance.close();
		console.error('扫描设备失败:', error);
		
		let errorMessage = '扫描网关下属设备失败';
		if (error.response) {
			// HTTP错误
			errorMessage += `：HTTP ${error.response.status}`;
			if (error.response.data && error.response.data.message) {
				errorMessage += ` - ${error.response.data.message}`;
			}
		} else if (error.message) {
			errorMessage += `：${error.message}`;
		}
		
		ElMessage.error(errorMessage);
	} finally {
		// 清除扫描状态
		state.scanningDevices[gatewayDevice.id] = false;
	}
};

/**
 * 等待扫描结果并处理
 * @param requestId 扫描请求ID
 * @param gatewayDevice 网关设备信息
 */
const waitForScanResults = async (requestId: string, gatewayDevice: any) => {
	const mqttApi = useMqttApi();
	const maxAttempts = 10; // 最大轮询次数
	const pollInterval = 3000; // 轮询间隔3秒
	let consecutiveErrors = 0;
	const maxConsecutiveErrors = 3; // 最多连续3次错误
	
	// 显示扫描进度
	const progressMessage = ElMessage({
		message: '正在搜索设备... (0/10)',
		type: 'info',
		duration: 0, // 不自动关闭
		showClose: true
	});

	for (let attempt = 0; attempt < maxAttempts; attempt++) {
		try {
			// 更新进度提示
			progressMessage.message = `正在搜索设备... (${attempt + 1}/${maxAttempts})`;
			// 等待一段时间后查询结果
			await new Promise(resolve => setTimeout(resolve, pollInterval));

			// 获取在线设备列表作为扫描结果
			const devicesResponse = await mqttApi.getOnlineDevices(requestId);
			console.log(`第 ${attempt + 1} 次轮询响应:`, devicesResponse);
			
			// 重置连续错误计数
			consecutiveErrors = 0;
			
			// 处理响应数据格式
			let onlineDevices = [];
			if (devicesResponse?.data) {
				// 如果data是数组，直接使用
				if (Array.isArray(devicesResponse.data)) {
					onlineDevices = devicesResponse.data;
				} else if (devicesResponse.data.result && Array.isArray(devicesResponse.data.result)) {
					// 如果data.result是数组，使用result
					onlineDevices = devicesResponse.data.result;
				} else if (typeof devicesResponse.data === 'object') {
					// 如果data是对象，尝试转换为数组
					onlineDevices = [devicesResponse.data];
				}
			}

			console.log(`获取到 ${onlineDevices.length} 个在线设备:`, onlineDevices);

			// 过滤出属于当前网关的设备或新发现的设备
			const subDevices = onlineDevices.filter((device: any) => {
				// 简化关联判断逻辑，优先检查扫描来源和时间
				const isFromScan = device.source === 'scan';
				const isRecentlyDiscovered = device.discoveredTime && 
					new Date(device.discoveredTime) > new Date(Date.now() - 120000); // 2分钟内
				const isRecentlyUpdated = device.updateTime && 
					new Date(device.updateTime) > new Date(Date.now() - 120000); // 2分钟内
				
				// 检查设备关联关系
				const hasParentRelation = device.parentDeviceId === gatewayDevice.id || 
					device.gatewayId === gatewayDevice.id;
				
				// 检查设备编码关联（更宽松的匹配）
				const hasCodeRelation = device.deviceCode && gatewayDevice.deviceCode && 
					(device.deviceCode.includes(gatewayDevice.deviceCode) || 
					 gatewayDevice.deviceCode.includes(device.deviceCode));
				
				// 检查设备名称关联（更宽松的匹配）
				const hasNameRelation = device.deviceName && gatewayDevice.deviceName && 
					(device.deviceName.toLowerCase().includes(gatewayDevice.deviceName.toLowerCase().substring(0, 3)) ||
					 gatewayDevice.deviceName.toLowerCase().includes(device.deviceName.toLowerCase().substring(0, 3)));
				
				const isRelated = isFromScan || isRecentlyDiscovered || isRecentlyUpdated || 
					hasParentRelation || hasCodeRelation || hasNameRelation;
				
				console.log(`设备 ${device.deviceName || device.deviceCode} 关联检查:`, {
					deviceCode: device.deviceCode,
					deviceName: device.deviceName,
					source: device.source,
					discoveredTime: device.discoveredTime,
					isFromScan,
					isRecentlyDiscovered,
					hasParentRelation,
					hasCodeRelation,
					hasNameRelation,
					isRelated
				});
				
				return isRelated;
			});

			if (subDevices.length > 0) {
				progressMessage.close();
				console.log(`发现 ${subDevices.length} 个下属设备:`, subDevices);
				
				// 保存扫描结果
				state.scannedDevices[gatewayDevice.id] = subDevices;
				state.scanResults = subDevices;

				// 保存设备数据到数据库
				await saveScannedDevicesToDatabase(subDevices, gatewayDevice);

				// 显示扫描结果
				showScanResultsDialog(subDevices, gatewayDevice);
				return;
			}

			console.log(`第 ${attempt + 1} 次轮询，暂未发现下属设备`);
		} catch (error) {
			console.error(`第 ${attempt + 1} 次轮询失败:`, error);
			consecutiveErrors++;
			
			// 如果连续错误次数过多，提前结束
			if (consecutiveErrors >= maxConsecutiveErrors) {
				progressMessage.close();
				ElMessage.error('网络连接异常，扫描中断');
				return;
			}
			
			// 更新错误提示
			progressMessage.message = `搜索设备中... (${attempt + 1}/${maxAttempts}) - 网络异常，正在重试`;
			
			// 如果是网络错误，继续重试
			if (attempt < maxAttempts - 1) {
				console.log(`将在 ${pollInterval * 2}ms 后重试...`);
				// 网络错误时延长等待时间
				await new Promise(resolve => setTimeout(resolve, pollInterval));
			}
		}
	}

	// 扫描超时，显示提示
	progressMessage.close();
	ElMessage.warning(`网关 [${gatewayDevice.deviceName}] 扫描超时，未发现下属设备。请检查网关设备状态或稍后重试。`);
	console.log(`网关 [${gatewayDevice.deviceName}] 扫描完成，共轮询 ${maxAttempts} 次`);
};

/**
 * 保存扫描到的设备数据到数据库
 * @param devices 扫描到的设备列表
 * @param gatewayDevice 网关设备信息
 */
const saveScannedDevicesToDatabase = async (devices: any[], gatewayDevice: any) => {
	try {
		const deviceApi = useDeviceApi();
		let savedCount = 0;
		let updatedCount = 0;

		// 显示保存进度
		const saveProgress = ElMessage({
			message: `正在保存设备... (0/${devices.length})`,
			type: 'info',
			duration: 0
		});

		for (let i = 0; i < devices.length; i++) {
			const device = devices[i];
			try {
				saveProgress.message = `正在保存设备... (${i + 1}/${devices.length})`;
				
				// 构造设备数据
				const deviceData = {
					deviceName: device.deviceName || `设备_${device.deviceCode}`,
					deviceCode: device.deviceCode || device.id,
					deviceType: device.deviceType || 'light',
					location: device.location || `${gatewayDevice.location}_子设备`,
					power: device.power || 10,
					brightness: device.brightness || 100,
					status: device.status === 'online' ? 1 : 0,
					isOn: device.isOn || false,
					parentDeviceId: gatewayDevice.id, // 设置父设备ID
					gatewayId: gatewayDevice.id, // 设置网关ID
					installDate: new Date().toISOString().split('T')[0],
					signalStrength: device.signalStrength || 0,
					lastOnlineTime: device.lastOnlineTime || new Date().toISOString(),
					properties: JSON.stringify(device.properties || {}),
					level: (gatewayDevice.level || 0) + 1 // 设备层级
				};

				// 检查设备是否已存在
				const existingDevices = await deviceApi.getList({ deviceCode: deviceData.deviceCode });
				const existingDevice = existingDevices.data.result?.find((d: any) => d.deviceCode === deviceData.deviceCode);

				if (existingDevice) {
					// 更新现有设备
					await deviceApi.update({ ...deviceData, id: existingDevice.id });
					updatedCount++;
					console.log(`更新设备: ${deviceData.deviceName}`);
				} else {
					// 添加新设备
					await deviceApi.add(deviceData);
					savedCount++;
					console.log(`保存新设备: ${deviceData.deviceName}`);
				}
			} catch (deviceError) {
				console.error(`保存设备 ${device.deviceName} 失败:`, deviceError);
			}
		}

		saveProgress.close();

		const message = `设备数据保存完成：新增 ${savedCount} 个，更新 ${updatedCount} 个设备`;
		ElMessage.success(message);
		console.log(message);

		// 刷新设备列表
		await handleQuery();

	} catch (error) {
		console.error('保存设备数据失败:', error);
		ElMessage.error('保存设备数据到数据库失败');
	}
};

/**
 * 显示扫描结果对话框
 * @param devices 扫描到的设备列表
 * @param gatewayDevice 网关设备信息
 */
const showScanResultsDialog = (devices: any[], gatewayDevice: any) => {
	const deviceList = devices.map(device => 
		`• ${device.deviceName || device.deviceCode} (${device.deviceType || '未知类型'}) - ${device.status === 1 || device.isOnline ? '在线' : '离线'}`
	).join('\n');

	ElMessageBox.alert(
		`网关 [${gatewayDevice.deviceName}] 扫描完成！\n\n发现 ${devices.length} 个下属设备：\n\n${deviceList}\n\n设备数据已自动保存到数据库。`,
		'设备扫描结果',
		{
			confirmButtonText: '确定',
			type: 'success',
			dangerouslyUseHTMLString: false
		}
	);
};

/**
 * 处理API错误
 * @param error 错误对象
 * @param operation 操作名称
 * @param defaultMessage 默认错误消息
 */
const handleApiError = (error: any, operation: string, defaultMessage: string) => {
	console.error(`${operation}失败:`, error);
	
	let errorMessage = defaultMessage;
	if (error?.response?.data?.message) {
		errorMessage = error.response.data.message;
	} else if (error?.message) {
		errorMessage = error.message;
	} else if (typeof error === 'string') {
		errorMessage = error;
	}
	
	ElMessage.error(errorMessage);
};

// 打开设备分组管理
const openDeviceGroup = () => {
	deviceGroupRef.value?.openDialog();
};

// 打开设备批量导入
const openDeviceImport = () => {
	deviceImportRef.value?.openDialog();
};

// 打开设备地图定位
const openDeviceMap = () => {
	deviceMapRef.value?.openDialog();
};

// 处理导入成功
const handleImportSuccess = (importedCount: number) => {
	ElMessage.success(`成功导入 ${importedCount} 个设备`);
	handleQuery();
};

/**
 * 根据设备类型获取对应的标签颜色类型
 * @param deviceType 设备类型值
 * @returns 标签颜色类型
 */
const getDeviceTypeTagType = (deviceType: string): string => {
	// 根据设备类型动态获取标签颜色
	const typeOption = deviceTypeOptions.value.find(option => option.value === deviceType);
	if (typeOption) {
		// 根据设备类型名称设置不同的颜色
		const typeName = typeOption.label;
		if (typeName.includes('LED')) return 'success';
		if (typeName.includes('节能')) return 'primary';
		if (typeName.includes('荧光')) return 'warning';
		if (typeName.includes('网关')) return 'info';
		if (typeName.includes('智能')) return 'success';
		return 'primary';
	}
	// 如果找不到匹配项，根据数值直接映射颜色
	switch (deviceType) {
		case '1': return 'success';  // LED灯
		case '2': return 'info';     // 网关
		case '3': return 'warning';  // 荧光灯
		case '4': return 'danger';   // 卤素灯
		case '智能灯': return 'success';  // 智能灯
		default: return 'info';
	}
};
</script>

<style scoped lang="scss">
.energy-device-container {
	height: 100%;
	display: flex;
	flex-direction: column;
}

.full-table {
	flex: 1;
	display: flex;
	flex-direction: column;
	
	:deep(.el-card__body) {
		flex: 1;
		display: flex;
		flex-direction: column;
	}
	
	:deep(.el-table) {
		flex: 1;
	}
}

.el-form--inline .el-form-item,
.el-form-item:last-of-type {
	margin: 5px 15px;
}
</style>