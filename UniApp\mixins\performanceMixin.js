/**
 * 性能优化混入
 * 提供统一的性能监控、内存管理和优化功能
 */
import { debounce, throttle, MemoryGuard, PerformanceMonitor } from '@/utils/performance.js'

export const performanceMixin = {
	data() {
		return {
			// 性能监控实例
			performanceMonitor: null,
			memoryGuard: null,
			
			// 防抖节流函数缓存
			debouncedFunctions: {},
			throttledFunctions: {}
		}
	},
	
	onLoad() {
		// 初始化性能监控
		this.initPerformanceMonitoring()
	},
	
	onUnload() {
		// 清理性能监控资源
		this.cleanupPerformanceMonitoring()
	},
	
	methods: {
		/**
		 * 初始化性能监控
		 */
		initPerformanceMonitoring() {
			// 初始化性能监控器
			this.performanceMonitor = new PerformanceMonitor({
				enableMemoryMonitoring: true,
				enableRenderMonitoring: true,
				memoryThreshold: 50 * 1024 * 1024, // 50MB
				renderThreshold: 16, // 16ms
				onMemoryWarning: (usage) => {
					console.warn(`内存使用过高: ${(usage / 1024 / 1024).toFixed(2)}MB`)
					this.handleMemoryWarning()
				},
				onRenderWarning: (time) => {
					console.warn(`渲染时间过长: ${time}ms`)
					this.handleRenderWarning()
				}
			})
			
			// 初始化内存守护
			this.memoryGuard = new MemoryGuard({
				maxMemoryUsage: 50 * 1024 * 1024,
				checkInterval: 30000,
				onMemoryWarning: () => {
					this.handleMemoryWarning()
				}
			})
		},
		
		/**
		 * 清理性能监控资源
		 */
		cleanupPerformanceMonitoring() {
			if (this.performanceMonitor) {
				this.performanceMonitor.destroy()
				this.performanceMonitor = null
			}
			
			if (this.memoryGuard) {
				this.memoryGuard.destroy()
				this.memoryGuard = null
			}
			
			// 清理防抖节流函数缓存
			this.debouncedFunctions = {}
			this.throttledFunctions = {}
		},
		
		/**
		 * 创建防抖函数
		 * @param {Function} func - 要防抖的函数
		 * @param {number} delay - 延迟时间
		 * @param {string} key - 缓存键名
		 */
		createDebounced(func, delay = 300, key = 'default') {
			if (!this.debouncedFunctions[key]) {
				this.debouncedFunctions[key] = debounce(func, delay)
			}
			return this.debouncedFunctions[key]
		},
		
		/**
		 * 创建节流函数
		 * @param {Function} func - 要节流的函数
		 * @param {number} delay - 延迟时间
		 * @param {string} key - 缓存键名
		 */
		createThrottled(func, delay = 300, key = 'default') {
			if (!this.throttledFunctions[key]) {
				this.throttledFunctions[key] = throttle(func, delay)
			}
			return this.throttledFunctions[key]
		},
		
		/**
		 * 处理内存警告
		 */
		handleMemoryWarning() {
			// 清理缓存数据
			this.clearCache && this.clearCache()
			
			// 触发垃圾回收
			if (typeof wx !== 'undefined' && wx.triggerGC) {
				wx.triggerGC()
			}
			
			// 显示内存警告提示
			uni.showToast({
				title: '内存使用过高，已自动清理',
				icon: 'none',
				duration: 2000
			})
		},
		
		/**
		 * 处理渲染警告
		 */
		handleRenderWarning() {
			// 可以在这里实现渲染优化逻辑
			console.log('检测到渲染性能问题，建议优化页面结构')
		},
		
		/**
		 * 获取当前内存使用情况
		 */
		getMemoryUsage() {
			if (this.performanceMonitor) {
				return this.performanceMonitor.getMemoryUsage()
			}
			return null
		},
		
		/**
		 * 获取性能统计信息
		 */
		getPerformanceStats() {
			if (this.performanceMonitor) {
				return this.performanceMonitor.getStats()
			}
			return null
		}
	}
}

export default performanceMixin