<template>
	<view class="user-container">
		<!-- 用户信息卡片 -->
		<view class="user-info-card">
			<view class="user-avatar">
				<image :src="userInfo.avatar || '/static/images/default-avatar.png'" mode="aspectFill" />
				<view class="avatar-badge" v-if="userInfo.isOnline">
					<text class="badge-dot"></text>
				</view>
			</view>
			<view class="user-details">
				<view class="user-name">{{ userInfo.name || '未设置' }}</view>
				<view class="user-role">{{ userInfo.role || '普通用户' }}</view>
				<view class="user-status">
					<text class="status-dot" :class="userInfo.isOnline ? 'online' : 'offline'"></text>
					<text class="status-text">{{ userInfo.isOnline ? '在线' : '离线' }}</text>
				</view>
			</view>
			<view class="edit-btn" @click="editProfile">
				<text class="iconfont icon-edit">✏️</text>
			</view>
		</view>

		<!-- 统计信息 -->
		<view class="stats-section">
			<view class="stats-grid">
				<view class="stat-item" v-for="stat in userStats" :key="stat.key">
					<view class="stat-icon" :style="{ backgroundColor: stat.color }">
						<text>{{ stat.icon }}</text>
					</view>
					<view class="stat-content">
						<view class="stat-value">{{ stat.value }}</view>
						<view class="stat-label">{{ stat.label }}</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 功能菜单 -->
		<view class="menu-section">
			<view class="menu-group" v-for="group in menuGroups" :key="group.title">
				<view class="group-title">{{ group.title }}</view>
				<view class="menu-list">
					<view 
						class="menu-item" 
						v-for="item in group.items" 
						:key="item.key"
						@click="handleMenuClick(item)"
					>
						<view class="menu-icon" :style="{ backgroundColor: item.color }">
							<text>{{ item.icon }}</text>
						</view>
						<view class="menu-content">
							<view class="menu-title">{{ item.title }}</view>
							<view class="menu-desc" v-if="item.desc">{{ item.desc }}</view>
						</view>
						<view class="menu-extra" v-if="item.extra">
							<text class="extra-text">{{ item.extra }}</text>
						</view>
						<view class="menu-arrow">
							<text class="iconfont icon-arrow-right">›</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 退出登录 -->
		<view class="logout-section">
			<view class="logout-btn" @click="showLogoutConfirm">
				<text>退出登录</text>
			</view>
		</view>

		<!-- 个人信息编辑弹窗 -->
		<view class="profile-modal" v-if="showProfileModal" @click="closeProfileModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">编辑个人信息</text>
					<text class="close-btn" @click="closeProfileModal">×</text>
				</view>
				<view class="profile-form">
					<view class="form-item">
						<view class="form-label">头像</view>
						<view class="avatar-upload" @click="chooseAvatar">
							<image :src="editForm.avatar || '/static/images/default-avatar.png'" mode="aspectFill" />
							<view class="upload-overlay">
								<text>📷</text>
							</view>
						</view>
					</view>
					<view class="form-item">
						<view class="form-label">姓名</view>
						<input 
							class="form-input" 
							v-model="editForm.name" 
							placeholder="请输入姓名"
							maxlength="20"
						/>
					</view>
					<view class="form-item">
						<view class="form-label">手机号</view>
						<input 
							class="form-input" 
							v-model="editForm.phone" 
							placeholder="请输入手机号"
							type="number"
							maxlength="11"
						/>
					</view>
					<view class="form-item">
						<view class="form-label">邮箱</view>
						<input 
							class="form-input" 
							v-model="editForm.email" 
							placeholder="请输入邮箱"
							type="email"
						/>
					</view>
					<view class="form-item">
						<view class="form-label">部门</view>
						<input 
							class="form-input" 
							v-model="editForm.department" 
							placeholder="请输入部门"
							maxlength="30"
						/>
					</view>
				</view>
				<view class="modal-actions">
					<button class="btn btn-secondary" @click="closeProfileModal">取消</button>
					<button class="btn btn-primary" @click="saveProfile">保存</button>
				</view>
			</view>
		</view>

		<!-- 确认退出弹窗 -->
		<view class="confirm-modal" v-if="showLogoutModal" @click="closeLogoutModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">确认退出</text>
				</view>
				<view class="modal-body">
					<text>确定要退出登录吗？</text>
				</view>
				<view class="modal-actions">
					<button class="btn btn-secondary" @click="closeLogoutModal">取消</button>
					<button class="btn btn-danger" @click="logoutUser">确认退出</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnload } from 'vue'
import { useStore } from 'vuex'

// Vuex store
const store = useStore()

// 计算属性 - 从store获取数据
const userInfo = computed(() => store.getters['user/userInfo'])
const isLoggedIn = computed(() => store.getters['user/isLoggedIn'])
const userStats = computed(() => {
	const stats = store.getters['user/stats']
	return [
		{
			key: 'devices',
			label: '管理设备',
			value: stats.devices?.toString() || '0',
			icon: '💡',
			color: '#1890ff'
		},
		{
			key: 'operations',
			label: '今日操作',
			value: stats.operations?.toString() || '0',
			icon: '⚡',
			color: '#52c41a'
		},
		{
			key: 'energy',
			label: '节能率',
			value: stats.energy ? `${stats.energy}%` : '0%',
			icon: '🌱',
			color: '#faad14'
		},
		{
			key: 'alerts',
			label: '待处理告警',
			value: stats.alerts?.toString() || '0',
			icon: '⚠️',
			color: '#ff4d4f'
		}
	]
})

const menuGroups = ref([
	{
		title: '账户设置',
		items: [
			{
				key: 'security',
				title: '安全设置',
				desc: '密码、登录验证',
				icon: '🔒',
				color: '#1890ff'
			},
			{
				key: 'privacy',
				title: '隐私设置',
				desc: '数据权限管理',
				icon: '🛡️',
				color: '#52c41a'
			},
			{
				key: 'notification',
				title: '消息通知',
				desc: '推送、邮件提醒',
				icon: '🔔',
				color: '#faad14',
				extra: '已开启'
			}
		]
	},
	{
		title: '应用设置',
		items: [
			{
				key: 'theme',
				title: '主题设置',
				desc: '深色模式、界面风格',
				icon: '🎨',
				color: '#722ed1',
				extra: '浅色'
			},
			{
				key: 'language',
				title: '语言设置',
				desc: '界面语言选择',
				icon: '🌐',
				color: '#13c2c2',
				extra: '中文'
			},
			{
				key: 'cache',
				title: '缓存管理',
				desc: '清理应用缓存',
				icon: '🗂️',
				color: '#fa8c16',
				extra: '23.5MB'
			}
		]
	},
	{
		title: '帮助支持',
		items: [
			{
				key: 'help',
				title: '使用帮助',
				desc: '操作指南、常见问题',
				icon: '❓',
				color: '#1890ff'
			},
			{
				key: 'feedback',
				title: '意见反馈',
				desc: '问题反馈、建议',
				icon: '💬',
				color: '#52c41a'
			},
			{
				key: 'about',
				title: '关于应用',
				desc: '版本信息、更新日志',
				icon: 'ℹ️',
				color: '#faad14',
				extra: 'v1.0.0'
			}
		]
	}
])

// 弹窗状态
const showProfileModal = ref(false)
const showLogoutModal = ref(false)

// 定时器变量
const cacheTimer = ref(null)

// 编辑表单
const editForm = reactive({
	name: '',
	phone: '',
	email: '',
	department: '',
	avatar: ''
})

// 页面生命周期
onMounted(async () => {
	// 检查登录状态
	if (!isLoggedIn.value) {
		uni.reLaunch({ url: '/pages/user/login' })
		return
	}
	
	await loadUserData()
})

// 加载用户数据
const loadUserData = async () => {
	try {
		uni.showLoading({ title: '加载中...' })
		
		// 并行加载用户信息和统计数据
		await Promise.all([
			store.dispatch('user/getUserInfo'),
			store.dispatch('user/getUserStats')
		])
		
		uni.hideLoading()
		console.log('用户数据加载完成')
	} catch (error) {
		uni.hideLoading()
		console.error('加载用户数据失败:', error)
		uni.showToast({
			title: error.message || '加载失败',
			icon: 'error'
		})
	}
}

// 编辑个人信息
const editProfile = () => {
	// 复制当前用户信息到编辑表单
	const currentUser = userInfo.value
	Object.assign(editForm, {
		name: currentUser.realName || currentUser.name || '',
		phone: currentUser.phone || '',
		email: currentUser.email || '',
		department: currentUser.orgName || currentUser.department || '',
		avatar: currentUser.avatar || ''
	})
	showProfileModal.value = true
}

// 关闭个人信息弹窗
const closeProfileModal = () => {
	showProfileModal.value = false
}

// 选择头像
const chooseAvatar = () => {
	uni.chooseImage({
		count: 1,
		sizeType: ['compressed'],
		sourceType: ['album', 'camera'],
		success: (res) => {
			const tempFilePath = res.tempFilePaths[0]
			editForm.avatar = tempFilePath
			
			// 这里应该上传图片到服务器
			// uploadAvatar(tempFilePath)
		}
	})
}

// 保存个人信息
const saveProfile = async () => {
	try {
		// 表单验证
		if (!editForm.name.trim()) {
			uni.showToast({
				title: '请输入姓名',
				icon: 'error'
			})
			return
		}
		
		if (editForm.phone && !/^1[3-9]\d{9}$/.test(editForm.phone)) {
			uni.showToast({
				title: '手机号格式不正确',
				icon: 'error'
			})
			return
		}
		
		if (editForm.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(editForm.email)) {
			uni.showToast({
				title: '邮箱格式不正确',
				icon: 'error'
			})
			return
		}
		
		uni.showLoading({ title: '保存中...' })
		
		// 构建更新数据
		const updateData = {
			realName: editForm.name,
			phone: editForm.phone,
			email: editForm.email,
			avatar: editForm.avatar
		}
		
		// 调用Vuex action更新用户信息
		await store.dispatch('user/updateUserInfo', updateData)
		
		uni.hideLoading()
		uni.showToast({
			title: '保存成功',
			icon: 'success'
		})
		
		closeProfileModal()
	} catch (error) {
		uni.hideLoading()
		console.error('保存个人信息失败:', error)
		uni.showToast({
			title: error.message || '保存失败',
			icon: 'error'
		})
	}
}

// 处理菜单点击
const handleMenuClick = (item) => {
	switch (item.key) {
		case 'security':
			uni.navigateTo({ url: '/pages/user/security' })
			break
		case 'privacy':
			uni.navigateTo({ url: '/pages/user/privacy' })
			break
		case 'notification':
			uni.navigateTo({ url: '/pages/user/notification' })
			break
		case 'theme':
			uni.navigateTo({ url: '/pages/user/theme' })
			break
		case 'language':
			uni.navigateTo({ url: '/pages/user/language' })
			break
		case 'cache':
			clearCache()
			break
		case 'help':
			uni.navigateTo({ url: '/pages/user/help' })
			break
		case 'feedback':
			uni.navigateTo({ url: '/pages/user/feedback' })
			break
		case 'about':
			uni.navigateTo({ url: '/pages/user/about' })
			break
		default:
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			})
	}
}

// 清理缓存
const clearCache = () => {
	uni.showModal({
		title: '清理缓存',
		content: '确定要清理应用缓存吗？',
		success: (res) => {
			if (res.confirm) {
				uni.showLoading({ title: '清理中...' })
				
				cacheTimer.value = setTimeout(() => {
					uni.hideLoading()
					uni.showToast({
						title: '清理完成',
						icon: 'success'
					})
					
					// 更新缓存大小显示
					const cacheItem = menuGroups.value[1].items.find(item => item.key === 'cache')
					if (cacheItem) {
						cacheItem.extra = '0MB'
					}
					cacheTimer.value = null
				}, 1500)
			}
		}
	})
}

// 显示退出确认
const showLogoutConfirm = () => {
	showLogoutModal.value = true
}

// 关闭退出确认
const closeLogoutModal = () => {
	showLogoutModal.value = false
}

// 退出登录
const logoutUser = async () => {
	try {
		uni.showLoading({ title: '退出中...' })
		
		// 调用Vuex action退出登录
		await store.dispatch('user/logout')
		
		uni.hideLoading()
		uni.showToast({
			title: '已退出登录',
			icon: 'success'
		})
		
		// 跳转到登录页
		setTimeout(() => {
			uni.reLaunch({
				url: '/pages/user/login'
			})
		}, 1000)
		
	} catch (error) {
		uni.hideLoading()
		console.error('退出登录失败:', error)
		
		// 即使API调用失败，也要清除本地数据并跳转
		store.commit('user/CLEAR_USER_DATA')
		
		uni.showToast({
			title: '已退出登录',
			icon: 'success'
		})
		
		// 跳转到登录页
		setTimeout(() => {
			uni.reLaunch({
				url: '/pages/user/login'
			})
		}, 1000)
	}
	
	closeLogoutModal()
}

// 页面卸载时清理定时器
onUnload(() => {
	if (cacheTimer.value) {
		clearTimeout(cacheTimer.value)
		cacheTimer.value = null
	}
})
</script>

<style lang="scss" scoped>
.user-container {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding: 20rpx;
}

// 用户信息卡片
.user-info-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	display: flex;
	align-items: center;
	position: relative;
}

.user-avatar {
	position: relative;
	margin-right: 24rpx;

	image {
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
		border: 4rpx solid #f0f0f0;
	}
}

.avatar-badge {
	position: absolute;
	bottom: 8rpx;
	right: 8rpx;
	width: 24rpx;
	height: 24rpx;
	background-color: #ffffff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.badge-dot {
	width: 16rpx;
	height: 16rpx;
	background-color: #52c41a;
	border-radius: 50%;
}

.user-details {
	flex: 1;
}

.user-name {
	font-size: 36rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.user-role {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 12rpx;
}

.user-status {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.status-dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;

	&.online {
		background-color: #52c41a;
	}

	&.offline {
		background-color: #d9d9d9;
	}
}

.status-text {
	font-size: 24rpx;
	color: #666666;
}

.edit-btn {
	width: 64rpx;
	height: 64rpx;
	background-color: #1890ff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;

	text {
		font-size: 24rpx;
		color: #ffffff;
	}
}

// 统计信息
.stats-section {
	margin-bottom: 20rpx;
}

.stats-grid {
	display: flex;
	gap: 16rpx;
}

.stat-item {
	flex: 1;
	background-color: #ffffff;
	border-radius: 12rpx;
	padding: 24rpx 16rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
}

.stat-icon {
	width: 64rpx;
	height: 64rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 16rpx;

	text {
		font-size: 28rpx;
		color: #ffffff;
	}
}

.stat-content {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.stat-value {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 4rpx;
}

.stat-label {
	font-size: 24rpx;
	color: #666666;
}

// 功能菜单
.menu-section {
	margin-bottom: 20rpx;
}

.menu-group {
	margin-bottom: 20rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.group-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 16rpx;
	padding: 0 16rpx;
}

.menu-list {
	background-color: #ffffff;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.menu-item {
	display: flex;
	align-items: center;
	padding: 24rpx;
	border-bottom: 2rpx solid #f0f0f0;
	transition: background-color 0.3s;

	&:last-child {
		border-bottom: none;
	}

	&:active {
		background-color: #f5f5f5;
	}
}

.menu-icon {
	width: 64rpx;
	height: 64rpx;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;

	text {
		font-size: 28rpx;
		color: #ffffff;
	}
}

.menu-content {
	flex: 1;
}

.menu-title {
	font-size: 30rpx;
	font-weight: 500;
	color: #333333;
	margin-bottom: 4rpx;
}

.menu-desc {
	font-size: 24rpx;
	color: #999999;
}

.menu-extra {
	margin-right: 16rpx;
}

.extra-text {
	font-size: 26rpx;
	color: #666666;
}

.menu-arrow {
	text {
		font-size: 32rpx;
		color: #d9d9d9;
	}
}

// 退出登录
.logout-section {
	margin-bottom: 40rpx;
}

.logout-btn {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	text-align: center;
	transition: background-color 0.3s;

	&:active {
		background-color: #f5f5f5;
	}

	text {
		font-size: 32rpx;
		font-weight: 500;
		color: #ff4d4f;
	}
}

// 个人信息编辑弹窗
.profile-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}

.modal-content {
	background-color: #ffffff;
	border-radius: 16rpx;
	width: 100%;
	max-width: 600rpx;
	max-height: 80vh;
	overflow-y: auto;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 32rpx;
	border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.close-btn {
	font-size: 40rpx;
	color: #999999;
	padding: 8rpx;
}

.profile-form {
	padding: 32rpx;
}

.form-item {
	margin-bottom: 32rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.form-label {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 16rpx;
}

.avatar-upload {
	position: relative;
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	overflow: hidden;
	border: 4rpx solid #f0f0f0;

	image {
		width: 100%;
		height: 100%;
	}
}

.upload-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	opacity: 0;
	transition: opacity 0.3s;

	text {
		font-size: 32rpx;
		color: #ffffff;
	}
}

.avatar-upload:active .upload-overlay {
	opacity: 1;
}

.form-input {
	width: 100%;
	height: 80rpx;
	padding: 0 16rpx;
	border: 2rpx solid #d9d9d9;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333333;
	background-color: #ffffff;

	&:focus {
		border-color: #1890ff;
	}
}

.modal-actions {
	display: flex;
	gap: 16rpx;
	padding: 32rpx;
	border-top: 2rpx solid #f0f0f0;
	justify-content: flex-end;
}

.btn {
	padding: 16rpx 32rpx;
	border-radius: 8rpx;
	font-size: 28rpx;
	font-weight: 500;
	border: none;
	transition: all 0.3s;

	&.btn-primary {
		background-color: #1890ff;
		color: #ffffff;

		&:active {
			background-color: #096dd9;
		}
	}

	&.btn-secondary {
		background-color: #f5f5f5;
		color: #666666;

		&:active {
			background-color: #e6e6e6;
		}
	}

	&.btn-danger {
		background-color: #ff4d4f;
		color: #ffffff;

		&:active {
			background-color: #d9363e;
		}
	}
}

// 确认退出弹窗
.confirm-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}

.modal-body {
	padding: 32rpx;
	text-align: center;

	text {
		font-size: 30rpx;
		color: #333333;
		line-height: 1.5;
	}
}

// 响应式设计
@media screen and (min-width: 768px) {
	.user-container {
		padding: 40rpx;
		max-width: 800rpx;
		margin: 0 auto;
	}

	.user-info-card {
		padding: 40rpx;
	}

	.user-avatar {
		margin-right: 32rpx;

		image {
			width: 140rpx;
			height: 140rpx;
		}
	}

	.stats-grid {
		gap: 24rpx;
	}

	.stat-item {
		padding: 32rpx 24rpx;
	}

	.menu-item {
		padding: 32rpx;
	}

	.modal-content {
		max-width: 800rpx;
	}
}

// 暗色主题适配
@media (prefers-color-scheme: dark) {
	.user-container {
		background-color: #1f1f1f;
	}

	.user-info-card,
	.stat-item,
	.menu-list,
	.logout-btn,
	.modal-content {
		background-color: #2d2d2d;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
	}

	.user-name,
	.stat-value,
	.group-title,
	.menu-title,
	.modal-title {
		color: #ffffff;
	}

	.user-role,
	.status-text,
	.stat-label,
	.menu-desc,
	.extra-text {
		color: #cccccc;
	}

	.menu-item {
		border-bottom-color: #404040;

		&:active {
			background-color: #404040;
		}
	}

	.logout-btn {
		&:active {
			background-color: #404040;
		}
	}

	.form-input {
		background-color: #2d2d2d;
		color: #ffffff;
		border-color: #404040;

		&:focus {
			border-color: #1890ff;
		}
	}

	.btn-secondary {
		background-color: #404040;
		color: #cccccc;

		&:active {
			background-color: #4d4d4d;
		}
	}

	.modal-header,
	.modal-actions {
		border-color: #404040;
	}
}
</style>