<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net8.0;net9.0</TargetFrameworks>
    <NoWarn>1701;1702;1591;8632</NoWarn>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <Copyright>Admin.NET</Copyright>
    <Description>Admin.NET MQTT通信插件 - 支持设备通信和数据处理</Description>
  </PropertyGroup>

  <ItemGroup>
    <None Update="Configuration\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="MQTTnet" Version="4.3.7.1207" />
    <PackageReference Include="MQTTnet.Extensions.ManagedClient" Version="4.3.7.1207" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Hosting.Abstractions" Version="2.2.0" />
    <PackageReference Include="xunit" Version="2.4.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.5" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="Moq" Version="4.20.69" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Admin.NET.Core\Admin.NET.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Test\**" />
    <EmbeddedResource Remove="Test\**" />
    <None Remove="Test\**" />
  </ItemGroup>

</Project>