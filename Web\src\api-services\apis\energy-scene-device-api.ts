/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AddEnergySceneDeviceInput } from '../models';
import { AdminResultEnergySceneDeviceDetailOutput } from '../models';
import { AdminResultInt32 } from '../models';
import { AdminResultInt64 } from '../models';
import { AdminResultListEnergySceneDeviceOutput } from '../models';
import { AdminResultSqlSugarPagedListEnergySceneDeviceOutput } from '../models';
import { BatchAddSceneDeviceInput } from '../models';
import { DeleteEnergySceneDeviceInput } from '../models';
import { EnergySceneDeviceStatusInput } from '../models';
import { Filter } from '../models';
import { FilterLogicEnum } from '../models';
import { FilterOperatorEnum } from '../models';
import { UpdateEnergySceneDeviceInput } from '../models';
/**
 * EnergySceneDeviceApi - axios parameter creator
 * @export
 */
export const EnergySceneDeviceApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加场景设备 🔖
         * @param {AddEnergySceneDeviceInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergySceneDeviceAddPost: async (body?: AddEnergySceneDeviceInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energySceneDevice/add`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 批量添加场景设备 🔖
         * @param {BatchAddSceneDeviceInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergySceneDeviceBatchAddPost: async (body?: BatchAddSceneDeviceInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energySceneDevice/batchAdd`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 批量删除场景设备 🔖
         * @param {number} sceneId 场景ID
         * @param {Array<number>} [body] 设备ID列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergySceneDeviceBatchDeleteSceneIdPost: async (sceneId: number, body?: Array<number>, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'sceneId' is not null or undefined
            if (sceneId === null || sceneId === undefined) {
                throw new RequiredError('sceneId','Required parameter sceneId was null or undefined when calling apiEnergySceneDeviceBatchDeleteSceneIdPost.');
            }
            const localVarPath = `/api/energySceneDevice/batchDelete/{sceneId}`
                .replace(`{${"sceneId"}}`, encodeURIComponent(String(sceneId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 复制场景设备到其他场景 🔖
         * @param {number} sourceSceneId 源场景ID
         * @param {number} targetSceneId 目标场景ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergySceneDeviceCopyToSceneSourceSceneIdTargetSceneIdPost: async (sourceSceneId: number, targetSceneId: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'sourceSceneId' is not null or undefined
            if (sourceSceneId === null || sourceSceneId === undefined) {
                throw new RequiredError('sourceSceneId','Required parameter sourceSceneId was null or undefined when calling apiEnergySceneDeviceCopyToSceneSourceSceneIdTargetSceneIdPost.');
            }
            // verify required parameter 'targetSceneId' is not null or undefined
            if (targetSceneId === null || targetSceneId === undefined) {
                throw new RequiredError('targetSceneId','Required parameter targetSceneId was null or undefined when calling apiEnergySceneDeviceCopyToSceneSourceSceneIdTargetSceneIdPost.');
            }
            const localVarPath = `/api/energySceneDevice/copyToScene/{sourceSceneId}/{targetSceneId}`
                .replace(`{${"sourceSceneId"}}`, encodeURIComponent(String(sourceSceneId)))
                .replace(`{${"targetSceneId"}}`, encodeURIComponent(String(targetSceneId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 删除场景设备 🔖
         * @param {DeleteEnergySceneDeviceInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergySceneDeviceDeletePost: async (body?: DeleteEnergySceneDeviceInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energySceneDevice/delete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取场景设备详情 🔖
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergySceneDeviceDetailGet: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiEnergySceneDeviceDetailGet.');
            }
            const localVarPath = `/api/energySceneDevice/detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (id !== undefined) {
                localVarQueryParameter['Id'] = id;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新场景设备执行统计 🔖
         * @param {number} sceneId 场景ID
         * @param {number} deviceId 设备ID
         * @param {boolean} isSuccess 是否成功
         * @param {number} duration 执行时长
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergySceneDeviceExecuteStatsSceneIdDeviceIdIsSuccessDurationPut: async (sceneId: number, deviceId: number, isSuccess: boolean, duration: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'sceneId' is not null or undefined
            if (sceneId === null || sceneId === undefined) {
                throw new RequiredError('sceneId','Required parameter sceneId was null or undefined when calling apiEnergySceneDeviceExecuteStatsSceneIdDeviceIdIsSuccessDurationPut.');
            }
            // verify required parameter 'deviceId' is not null or undefined
            if (deviceId === null || deviceId === undefined) {
                throw new RequiredError('deviceId','Required parameter deviceId was null or undefined when calling apiEnergySceneDeviceExecuteStatsSceneIdDeviceIdIsSuccessDurationPut.');
            }
            // verify required parameter 'isSuccess' is not null or undefined
            if (isSuccess === null || isSuccess === undefined) {
                throw new RequiredError('isSuccess','Required parameter isSuccess was null or undefined when calling apiEnergySceneDeviceExecuteStatsSceneIdDeviceIdIsSuccessDurationPut.');
            }
            // verify required parameter 'duration' is not null or undefined
            if (duration === null || duration === undefined) {
                throw new RequiredError('duration','Required parameter duration was null or undefined when calling apiEnergySceneDeviceExecuteStatsSceneIdDeviceIdIsSuccessDurationPut.');
            }
            const localVarPath = `/api/energySceneDevice/executeStats/{sceneId}/{deviceId}/{isSuccess}/{duration}`
                .replace(`{${"sceneId"}}`, encodeURIComponent(String(sceneId)))
                .replace(`{${"deviceId"}}`, encodeURIComponent(String(deviceId)))
                .replace(`{${"isSuccess"}}`, encodeURIComponent(String(isSuccess)))
                .replace(`{${"duration"}}`, encodeURIComponent(String(duration)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取场景设备列表 🔖
         * @param {number} sceneId 场景ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergySceneDeviceListBySceneIdSceneIdGet: async (sceneId: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'sceneId' is not null or undefined
            if (sceneId === null || sceneId === undefined) {
                throw new RequiredError('sceneId','Required parameter sceneId was null or undefined when calling apiEnergySceneDeviceListBySceneIdSceneIdGet.');
            }
            const localVarPath = `/api/energySceneDevice/listBySceneId/{sceneId}`
                .replace(`{${"sceneId"}}`, encodeURIComponent(String(sceneId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取场景设备分页列表 🔖
         * @param {number} [sceneId] 场景ID
         * @param {number} [deviceId] 设备ID
         * @param {string} [deviceCode] 设备编码
         * @param {string} [deviceName] 设备名称
         * @param {string} [controlType] 控制类型
         * @param {number} [status] 状态
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergySceneDevicePageGet: async (sceneId?: number, deviceId?: number, deviceCode?: string, deviceName?: string, controlType?: string, status?: number, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energySceneDevice/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (sceneId !== undefined) {
                localVarQueryParameter['SceneId'] = sceneId;
            }

            if (deviceId !== undefined) {
                localVarQueryParameter['DeviceId'] = deviceId;
            }

            if (deviceCode !== undefined) {
                localVarQueryParameter['DeviceCode'] = deviceCode;
            }

            if (deviceName !== undefined) {
                localVarQueryParameter['DeviceName'] = deviceName;
            }

            if (controlType !== undefined) {
                localVarQueryParameter['ControlType'] = controlType;
            }

            if (status !== undefined) {
                localVarQueryParameter['Status'] = status;
            }

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            if (field !== undefined) {
                localVarQueryParameter['Field'] = field;
            }

            if (order !== undefined) {
                localVarQueryParameter['Order'] = order;
            }

            if (descStr !== undefined) {
                localVarQueryParameter['DescStr'] = descStr;
            }

            if (searchFields) {
                localVarQueryParameter['Search.Fields'] = searchFields;
            }

            if (searchKeyword !== undefined) {
                localVarQueryParameter['Search.Keyword'] = searchKeyword;
            }

            if (keyword !== undefined) {
                localVarQueryParameter['Keyword'] = keyword;
            }

            if (filterLogic !== undefined) {
                localVarQueryParameter['Filter.Logic'] = filterLogic;
            }

            if (filterFilters) {
                localVarQueryParameter['Filter.Filters'] = filterFilters;
            }

            if (filterField !== undefined) {
                localVarQueryParameter['Filter.Field'] = filterField;
            }

            if (filterOperator !== undefined) {
                localVarQueryParameter['Filter.Operator'] = filterOperator;
            }

            if (filterValue !== undefined) {
                localVarQueryParameter['Filter.Value'] = filterValue;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 设置场景设备状态 🔖
         * @param {EnergySceneDeviceStatusInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergySceneDeviceSetStatusPost: async (body?: EnergySceneDeviceStatusInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energySceneDevice/setStatus`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新场景设备 🔖
         * @param {UpdateEnergySceneDeviceInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergySceneDeviceUpdatePost: async (body?: UpdateEnergySceneDeviceInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energySceneDevice/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 调整场景设备排序 🔖
         * @param {number} sceneId 场景ID
         * @param {number} deviceId 设备ID
         * @param {number} sort 新排序
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergySceneDeviceUpdateSortSceneIdDeviceIdSortPost: async (sceneId: number, deviceId: number, sort: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'sceneId' is not null or undefined
            if (sceneId === null || sceneId === undefined) {
                throw new RequiredError('sceneId','Required parameter sceneId was null or undefined when calling apiEnergySceneDeviceUpdateSortSceneIdDeviceIdSortPost.');
            }
            // verify required parameter 'deviceId' is not null or undefined
            if (deviceId === null || deviceId === undefined) {
                throw new RequiredError('deviceId','Required parameter deviceId was null or undefined when calling apiEnergySceneDeviceUpdateSortSceneIdDeviceIdSortPost.');
            }
            // verify required parameter 'sort' is not null or undefined
            if (sort === null || sort === undefined) {
                throw new RequiredError('sort','Required parameter sort was null or undefined when calling apiEnergySceneDeviceUpdateSortSceneIdDeviceIdSortPost.');
            }
            const localVarPath = `/api/energySceneDevice/updateSort/{sceneId}/{deviceId}/{sort}`
                .replace(`{${"sceneId"}}`, encodeURIComponent(String(sceneId)))
                .replace(`{${"deviceId"}}`, encodeURIComponent(String(deviceId)))
                .replace(`{${"sort"}}`, encodeURIComponent(String(sort)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * EnergySceneDeviceApi - functional programming interface
 * @export
 */
export const EnergySceneDeviceApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加场景设备 🔖
         * @param {AddEnergySceneDeviceInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergySceneDeviceAddPost(body?: AddEnergySceneDeviceInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt64>>> {
            const localVarAxiosArgs = await EnergySceneDeviceApiAxiosParamCreator(configuration).apiEnergySceneDeviceAddPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 批量添加场景设备 🔖
         * @param {BatchAddSceneDeviceInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergySceneDeviceBatchAddPost(body?: BatchAddSceneDeviceInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt32>>> {
            const localVarAxiosArgs = await EnergySceneDeviceApiAxiosParamCreator(configuration).apiEnergySceneDeviceBatchAddPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 批量删除场景设备 🔖
         * @param {number} sceneId 场景ID
         * @param {Array<number>} [body] 设备ID列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergySceneDeviceBatchDeleteSceneIdPost(sceneId: number, body?: Array<number>, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt32>>> {
            const localVarAxiosArgs = await EnergySceneDeviceApiAxiosParamCreator(configuration).apiEnergySceneDeviceBatchDeleteSceneIdPost(sceneId, body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 复制场景设备到其他场景 🔖
         * @param {number} sourceSceneId 源场景ID
         * @param {number} targetSceneId 目标场景ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergySceneDeviceCopyToSceneSourceSceneIdTargetSceneIdPost(sourceSceneId: number, targetSceneId: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt32>>> {
            const localVarAxiosArgs = await EnergySceneDeviceApiAxiosParamCreator(configuration).apiEnergySceneDeviceCopyToSceneSourceSceneIdTargetSceneIdPost(sourceSceneId, targetSceneId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 删除场景设备 🔖
         * @param {DeleteEnergySceneDeviceInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergySceneDeviceDeletePost(body?: DeleteEnergySceneDeviceInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await EnergySceneDeviceApiAxiosParamCreator(configuration).apiEnergySceneDeviceDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取场景设备详情 🔖
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergySceneDeviceDetailGet(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultEnergySceneDeviceDetailOutput>>> {
            const localVarAxiosArgs = await EnergySceneDeviceApiAxiosParamCreator(configuration).apiEnergySceneDeviceDetailGet(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新场景设备执行统计 🔖
         * @param {number} sceneId 场景ID
         * @param {number} deviceId 设备ID
         * @param {boolean} isSuccess 是否成功
         * @param {number} duration 执行时长
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergySceneDeviceExecuteStatsSceneIdDeviceIdIsSuccessDurationPut(sceneId: number, deviceId: number, isSuccess: boolean, duration: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await EnergySceneDeviceApiAxiosParamCreator(configuration).apiEnergySceneDeviceExecuteStatsSceneIdDeviceIdIsSuccessDurationPut(sceneId, deviceId, isSuccess, duration, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取场景设备列表 🔖
         * @param {number} sceneId 场景ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergySceneDeviceListBySceneIdSceneIdGet(sceneId: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultListEnergySceneDeviceOutput>>> {
            const localVarAxiosArgs = await EnergySceneDeviceApiAxiosParamCreator(configuration).apiEnergySceneDeviceListBySceneIdSceneIdGet(sceneId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取场景设备分页列表 🔖
         * @param {number} [sceneId] 场景ID
         * @param {number} [deviceId] 设备ID
         * @param {string} [deviceCode] 设备编码
         * @param {string} [deviceName] 设备名称
         * @param {string} [controlType] 控制类型
         * @param {number} [status] 状态
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergySceneDevicePageGet(sceneId?: number, deviceId?: number, deviceCode?: string, deviceName?: string, controlType?: string, status?: number, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSqlSugarPagedListEnergySceneDeviceOutput>>> {
            const localVarAxiosArgs = await EnergySceneDeviceApiAxiosParamCreator(configuration).apiEnergySceneDevicePageGet(sceneId, deviceId, deviceCode, deviceName, controlType, status, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 设置场景设备状态 🔖
         * @param {EnergySceneDeviceStatusInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergySceneDeviceSetStatusPost(body?: EnergySceneDeviceStatusInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await EnergySceneDeviceApiAxiosParamCreator(configuration).apiEnergySceneDeviceSetStatusPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新场景设备 🔖
         * @param {UpdateEnergySceneDeviceInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergySceneDeviceUpdatePost(body?: UpdateEnergySceneDeviceInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await EnergySceneDeviceApiAxiosParamCreator(configuration).apiEnergySceneDeviceUpdatePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 调整场景设备排序 🔖
         * @param {number} sceneId 场景ID
         * @param {number} deviceId 设备ID
         * @param {number} sort 新排序
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergySceneDeviceUpdateSortSceneIdDeviceIdSortPost(sceneId: number, deviceId: number, sort: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await EnergySceneDeviceApiAxiosParamCreator(configuration).apiEnergySceneDeviceUpdateSortSceneIdDeviceIdSortPost(sceneId, deviceId, sort, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * EnergySceneDeviceApi - factory interface
 * @export
 */
export const EnergySceneDeviceApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 增加场景设备 🔖
         * @param {AddEnergySceneDeviceInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergySceneDeviceAddPost(body?: AddEnergySceneDeviceInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt64>> {
            return EnergySceneDeviceApiFp(configuration).apiEnergySceneDeviceAddPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 批量添加场景设备 🔖
         * @param {BatchAddSceneDeviceInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergySceneDeviceBatchAddPost(body?: BatchAddSceneDeviceInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt32>> {
            return EnergySceneDeviceApiFp(configuration).apiEnergySceneDeviceBatchAddPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 批量删除场景设备 🔖
         * @param {number} sceneId 场景ID
         * @param {Array<number>} [body] 设备ID列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergySceneDeviceBatchDeleteSceneIdPost(sceneId: number, body?: Array<number>, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt32>> {
            return EnergySceneDeviceApiFp(configuration).apiEnergySceneDeviceBatchDeleteSceneIdPost(sceneId, body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 复制场景设备到其他场景 🔖
         * @param {number} sourceSceneId 源场景ID
         * @param {number} targetSceneId 目标场景ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergySceneDeviceCopyToSceneSourceSceneIdTargetSceneIdPost(sourceSceneId: number, targetSceneId: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt32>> {
            return EnergySceneDeviceApiFp(configuration).apiEnergySceneDeviceCopyToSceneSourceSceneIdTargetSceneIdPost(sourceSceneId, targetSceneId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 删除场景设备 🔖
         * @param {DeleteEnergySceneDeviceInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergySceneDeviceDeletePost(body?: DeleteEnergySceneDeviceInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return EnergySceneDeviceApiFp(configuration).apiEnergySceneDeviceDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取场景设备详情 🔖
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergySceneDeviceDetailGet(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultEnergySceneDeviceDetailOutput>> {
            return EnergySceneDeviceApiFp(configuration).apiEnergySceneDeviceDetailGet(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新场景设备执行统计 🔖
         * @param {number} sceneId 场景ID
         * @param {number} deviceId 设备ID
         * @param {boolean} isSuccess 是否成功
         * @param {number} duration 执行时长
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergySceneDeviceExecuteStatsSceneIdDeviceIdIsSuccessDurationPut(sceneId: number, deviceId: number, isSuccess: boolean, duration: number, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return EnergySceneDeviceApiFp(configuration).apiEnergySceneDeviceExecuteStatsSceneIdDeviceIdIsSuccessDurationPut(sceneId, deviceId, isSuccess, duration, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取场景设备列表 🔖
         * @param {number} sceneId 场景ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergySceneDeviceListBySceneIdSceneIdGet(sceneId: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultListEnergySceneDeviceOutput>> {
            return EnergySceneDeviceApiFp(configuration).apiEnergySceneDeviceListBySceneIdSceneIdGet(sceneId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取场景设备分页列表 🔖
         * @param {number} [sceneId] 场景ID
         * @param {number} [deviceId] 设备ID
         * @param {string} [deviceCode] 设备编码
         * @param {string} [deviceName] 设备名称
         * @param {string} [controlType] 控制类型
         * @param {number} [status] 状态
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergySceneDevicePageGet(sceneId?: number, deviceId?: number, deviceCode?: string, deviceName?: string, controlType?: string, status?: number, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSqlSugarPagedListEnergySceneDeviceOutput>> {
            return EnergySceneDeviceApiFp(configuration).apiEnergySceneDevicePageGet(sceneId, deviceId, deviceCode, deviceName, controlType, status, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 设置场景设备状态 🔖
         * @param {EnergySceneDeviceStatusInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergySceneDeviceSetStatusPost(body?: EnergySceneDeviceStatusInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return EnergySceneDeviceApiFp(configuration).apiEnergySceneDeviceSetStatusPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新场景设备 🔖
         * @param {UpdateEnergySceneDeviceInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergySceneDeviceUpdatePost(body?: UpdateEnergySceneDeviceInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return EnergySceneDeviceApiFp(configuration).apiEnergySceneDeviceUpdatePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 调整场景设备排序 🔖
         * @param {number} sceneId 场景ID
         * @param {number} deviceId 设备ID
         * @param {number} sort 新排序
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergySceneDeviceUpdateSortSceneIdDeviceIdSortPost(sceneId: number, deviceId: number, sort: number, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return EnergySceneDeviceApiFp(configuration).apiEnergySceneDeviceUpdateSortSceneIdDeviceIdSortPost(sceneId, deviceId, sort, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * EnergySceneDeviceApi - object-oriented interface
 * @export
 * @class EnergySceneDeviceApi
 * @extends {BaseAPI}
 */
export class EnergySceneDeviceApi extends BaseAPI {
    /**
     * 
     * @summary 增加场景设备 🔖
     * @param {AddEnergySceneDeviceInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergySceneDeviceApi
     */
    public async apiEnergySceneDeviceAddPost(body?: AddEnergySceneDeviceInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt64>> {
        return EnergySceneDeviceApiFp(this.configuration).apiEnergySceneDeviceAddPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 批量添加场景设备 🔖
     * @param {BatchAddSceneDeviceInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergySceneDeviceApi
     */
    public async apiEnergySceneDeviceBatchAddPost(body?: BatchAddSceneDeviceInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt32>> {
        return EnergySceneDeviceApiFp(this.configuration).apiEnergySceneDeviceBatchAddPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 批量删除场景设备 🔖
     * @param {number} sceneId 场景ID
     * @param {Array<number>} [body] 设备ID列表
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergySceneDeviceApi
     */
    public async apiEnergySceneDeviceBatchDeleteSceneIdPost(sceneId: number, body?: Array<number>, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt32>> {
        return EnergySceneDeviceApiFp(this.configuration).apiEnergySceneDeviceBatchDeleteSceneIdPost(sceneId, body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 复制场景设备到其他场景 🔖
     * @param {number} sourceSceneId 源场景ID
     * @param {number} targetSceneId 目标场景ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergySceneDeviceApi
     */
    public async apiEnergySceneDeviceCopyToSceneSourceSceneIdTargetSceneIdPost(sourceSceneId: number, targetSceneId: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt32>> {
        return EnergySceneDeviceApiFp(this.configuration).apiEnergySceneDeviceCopyToSceneSourceSceneIdTargetSceneIdPost(sourceSceneId, targetSceneId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 删除场景设备 🔖
     * @param {DeleteEnergySceneDeviceInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergySceneDeviceApi
     */
    public async apiEnergySceneDeviceDeletePost(body?: DeleteEnergySceneDeviceInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return EnergySceneDeviceApiFp(this.configuration).apiEnergySceneDeviceDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取场景设备详情 🔖
     * @param {number} id 主键Id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergySceneDeviceApi
     */
    public async apiEnergySceneDeviceDetailGet(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultEnergySceneDeviceDetailOutput>> {
        return EnergySceneDeviceApiFp(this.configuration).apiEnergySceneDeviceDetailGet(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新场景设备执行统计 🔖
     * @param {number} sceneId 场景ID
     * @param {number} deviceId 设备ID
     * @param {boolean} isSuccess 是否成功
     * @param {number} duration 执行时长
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergySceneDeviceApi
     */
    public async apiEnergySceneDeviceExecuteStatsSceneIdDeviceIdIsSuccessDurationPut(sceneId: number, deviceId: number, isSuccess: boolean, duration: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return EnergySceneDeviceApiFp(this.configuration).apiEnergySceneDeviceExecuteStatsSceneIdDeviceIdIsSuccessDurationPut(sceneId, deviceId, isSuccess, duration, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取场景设备列表 🔖
     * @param {number} sceneId 场景ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergySceneDeviceApi
     */
    public async apiEnergySceneDeviceListBySceneIdSceneIdGet(sceneId: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultListEnergySceneDeviceOutput>> {
        return EnergySceneDeviceApiFp(this.configuration).apiEnergySceneDeviceListBySceneIdSceneIdGet(sceneId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取场景设备分页列表 🔖
     * @param {number} [sceneId] 场景ID
     * @param {number} [deviceId] 设备ID
     * @param {string} [deviceCode] 设备编码
     * @param {string} [deviceName] 设备名称
     * @param {string} [controlType] 控制类型
     * @param {number} [status] 状态
     * @param {number} [page] 当前页码
     * @param {number} [pageSize] 页码容量
     * @param {string} [field] 排序字段
     * @param {string} [order] 排序方向
     * @param {string} [descStr] 降序排序
     * @param {Array<string>} [searchFields] 字段名称集合
     * @param {string} [searchKeyword] 关键字
     * @param {string} [keyword] 模糊查询关键字
     * @param {FilterLogicEnum} [filterLogic] 过滤条件
     * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
     * @param {string} [filterField] 字段名称
     * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
     * @param {any} [filterValue] 字段值
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergySceneDeviceApi
     */
    public async apiEnergySceneDevicePageGet(sceneId?: number, deviceId?: number, deviceCode?: string, deviceName?: string, controlType?: string, status?: number, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSqlSugarPagedListEnergySceneDeviceOutput>> {
        return EnergySceneDeviceApiFp(this.configuration).apiEnergySceneDevicePageGet(sceneId, deviceId, deviceCode, deviceName, controlType, status, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 设置场景设备状态 🔖
     * @param {EnergySceneDeviceStatusInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergySceneDeviceApi
     */
    public async apiEnergySceneDeviceSetStatusPost(body?: EnergySceneDeviceStatusInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return EnergySceneDeviceApiFp(this.configuration).apiEnergySceneDeviceSetStatusPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新场景设备 🔖
     * @param {UpdateEnergySceneDeviceInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergySceneDeviceApi
     */
    public async apiEnergySceneDeviceUpdatePost(body?: UpdateEnergySceneDeviceInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return EnergySceneDeviceApiFp(this.configuration).apiEnergySceneDeviceUpdatePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 调整场景设备排序 🔖
     * @param {number} sceneId 场景ID
     * @param {number} deviceId 设备ID
     * @param {number} sort 新排序
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergySceneDeviceApi
     */
    public async apiEnergySceneDeviceUpdateSortSceneIdDeviceIdSortPost(sceneId: number, deviceId: number, sort: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return EnergySceneDeviceApiFp(this.configuration).apiEnergySceneDeviceUpdateSortSceneIdDeviceIdSortPost(sceneId, deviceId, sort, options).then((request) => request(this.axios, this.basePath));
    }
}
