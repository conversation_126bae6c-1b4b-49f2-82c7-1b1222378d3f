// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core.Service;

/// <summary>
/// 节能率分析输出参数
/// </summary>
public class EnergySavingAnalysisOutput
{
    /// <summary>
    /// 分析时间
    /// </summary>
    public DateTime AnalysisTime { get; set; }

    /// <summary>
    /// 当前周期数据
    /// </summary>
    public PeriodData CurrentPeriod { get; set; } = new();

    /// <summary>
    /// 对比周期数据
    /// </summary>
    public PeriodData ComparePeriod { get; set; } = new();

    /// <summary>
    /// 节能率(%)
    /// </summary>
    public decimal EnergySavingRate { get; set; }

    /// <summary>
    /// 功率节约率(%)
    /// </summary>
    public decimal PowerSavingRate { get; set; }

    /// <summary>
    /// 节约能耗(kWh)
    /// </summary>
    public decimal EnergySaved { get; set; }

    /// <summary>
    /// 节约费用(元)
    /// </summary>
    public decimal CostSaved { get; set; }

    /// <summary>
    /// 对比类型（同比/环比）
    /// </summary>
    public string ComparisonType { get; set; } = string.Empty;

    /// <summary>
    /// 节能建议
    /// </summary>
    public List<string> Suggestions { get; set; } = new();
}

/// <summary>
/// 周期数据
/// </summary>
public class PeriodData
{
    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 总能耗(kWh)
    /// </summary>
    public decimal TotalEnergy { get; set; }

    /// <summary>
    /// 平均功率(W)
    /// </summary>
    public decimal AveragePower { get; set; }

    /// <summary>
    /// 设备数量
    /// </summary>
    public int DeviceCount { get; set; }

    /// <summary>
    /// 总费用(元)
    /// </summary>
    public decimal TotalCost { get; set; }

    /// <summary>
    /// 运行天数
    /// </summary>
    public int RunningDays => (EndTime - StartTime).Days + 1;

    /// <summary>
    /// 日均能耗(kWh)
    /// </summary>
    public decimal DailyAverageEnergy => RunningDays > 0 ? TotalEnergy / RunningDays : 0;
}