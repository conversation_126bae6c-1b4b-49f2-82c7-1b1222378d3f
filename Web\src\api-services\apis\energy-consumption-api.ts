/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AddEnergyConsumptionInput } from '../models';
import { AdminResultByte } from '../models';
import { AdminResultEnergyConsumptionStatOutput } from '../models';
import { AdminResultEnergySavingAnalysisOutput } from '../models';
import { AdminResultIActionResult } from '../models';
import { AdminResultInt32 } from '../models';
import { AdminResultInt64 } from '../models';
import { AdminResultListEnergyConsumptionRankOutput } from '../models';
import { AdminResultListEnergyConsumptionTrendOutput } from '../models';
import { AdminResultSqlSugarPagedListEnergyConsumptionOutput } from '../models';
import { DeleteEnergyConsumptionInput } from '../models';
import { EnergyConsumptionInput } from '../models';
import { EnergyConsumptionStatInput } from '../models';
import { Filter } from '../models';
import { FilterLogicEnum } from '../models';
import { FilterOperatorEnum } from '../models';
import { UpdateEnergyConsumptionInput } from '../models';
/**
 * EnergyConsumptionApi - axios parameter creator
 * @export
 */
export const EnergyConsumptionApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加能耗记录 🔖
         * @param {AddEnergyConsumptionInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyConsumptionAddPost: async (body?: AddEnergyConsumptionInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyConsumption/add`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 批量增加能耗记录 🔖
         * @param {Array<AddEnergyConsumptionInput>} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyConsumptionBatchAddPost: async (body?: Array<AddEnergyConsumptionInput>, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyConsumption/batchAdd`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 清理历史数据 🔖
         * @param {number} days 保留天数
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyConsumptionCleanHistoryDataDaysPost: async (days: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'days' is not null or undefined
            if (days === null || days === undefined) {
                throw new RequiredError('days','Required parameter days was null or undefined when calling apiEnergyConsumptionCleanHistoryDataDaysPost.');
            }
            const localVarPath = `/api/energyConsumption/cleanHistoryData/{days}`
                .replace(`{${"days"}}`, encodeURIComponent(String(days)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 删除能耗记录 🔖
         * @param {DeleteEnergyConsumptionInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyConsumptionDeletePost: async (body?: DeleteEnergyConsumptionInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyConsumption/delete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取节能率分析报告 🔖
         * @param {EnergyConsumptionStatInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyConsumptionEnergySavingAnalysisPost: async (body?: EnergyConsumptionStatInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyConsumption/energySavingAnalysis`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 导出能耗数据 🔖
         * @param {number} [deviceId] 设备ID
         * @param {string} [deviceCode] 设备编码
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {string} [recordType] 记录类型
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyConsumptionExportDataPost: async (deviceId?: number, deviceCode?: string, startTime?: Date, endTime?: Date, recordType?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyConsumption/exportData`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (deviceId !== undefined) {
                localVarQueryParameter['DeviceId'] = deviceId;
            }

            if (deviceCode !== undefined) {
                localVarQueryParameter['DeviceCode'] = deviceCode;
            }

            if (startTime !== undefined) {
                localVarQueryParameter['StartTime'] = (startTime as any instanceof Date) ?
                    (startTime as any).toISOString() :
                    startTime;
            }

            if (endTime !== undefined) {
                localVarQueryParameter['EndTime'] = (endTime as any instanceof Date) ?
                    (endTime as any).toISOString() :
                    endTime;
            }

            if (recordType !== undefined) {
                localVarQueryParameter['RecordType'] = recordType;
            }

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            if (field !== undefined) {
                localVarQueryParameter['Field'] = field;
            }

            if (order !== undefined) {
                localVarQueryParameter['Order'] = order;
            }

            if (descStr !== undefined) {
                localVarQueryParameter['DescStr'] = descStr;
            }

            if (searchFields) {
                localVarQueryParameter['Search.Fields'] = searchFields;
            }

            if (searchKeyword !== undefined) {
                localVarQueryParameter['Search.Keyword'] = searchKeyword;
            }

            if (keyword !== undefined) {
                localVarQueryParameter['Keyword'] = keyword;
            }

            if (filterLogic !== undefined) {
                localVarQueryParameter['Filter.Logic'] = filterLogic;
            }

            if (filterFilters) {
                localVarQueryParameter['Filter.Filters'] = filterFilters;
            }

            if (filterField !== undefined) {
                localVarQueryParameter['Filter.Field'] = filterField;
            }

            if (filterOperator !== undefined) {
                localVarQueryParameter['Filter.Operator'] = filterOperator;
            }

            if (filterValue !== undefined) {
                localVarQueryParameter['Filter.Value'] = filterValue;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 导出能耗数据到Excel 🔖
         * @param {EnergyConsumptionInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyConsumptionExportExcelPost: async (body?: EnergyConsumptionInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyConsumption/exportExcel`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取能耗记录分页列表 🔖
         * @param {number} [deviceId] 设备ID
         * @param {string} [deviceCode] 设备编码
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {string} [recordType] 记录类型
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyConsumptionPageGet: async (deviceId?: number, deviceCode?: string, startTime?: Date, endTime?: Date, recordType?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyConsumption/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (deviceId !== undefined) {
                localVarQueryParameter['DeviceId'] = deviceId;
            }

            if (deviceCode !== undefined) {
                localVarQueryParameter['DeviceCode'] = deviceCode;
            }

            if (startTime !== undefined) {
                localVarQueryParameter['StartTime'] = (startTime as any instanceof Date) ?
                    (startTime as any).toISOString() :
                    startTime;
            }

            if (endTime !== undefined) {
                localVarQueryParameter['EndTime'] = (endTime as any instanceof Date) ?
                    (endTime as any).toISOString() :
                    endTime;
            }

            if (recordType !== undefined) {
                localVarQueryParameter['RecordType'] = recordType;
            }

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            if (field !== undefined) {
                localVarQueryParameter['Field'] = field;
            }

            if (order !== undefined) {
                localVarQueryParameter['Order'] = order;
            }

            if (descStr !== undefined) {
                localVarQueryParameter['DescStr'] = descStr;
            }

            if (searchFields) {
                localVarQueryParameter['Search.Fields'] = searchFields;
            }

            if (searchKeyword !== undefined) {
                localVarQueryParameter['Search.Keyword'] = searchKeyword;
            }

            if (keyword !== undefined) {
                localVarQueryParameter['Keyword'] = keyword;
            }

            if (filterLogic !== undefined) {
                localVarQueryParameter['Filter.Logic'] = filterLogic;
            }

            if (filterFilters) {
                localVarQueryParameter['Filter.Filters'] = filterFilters;
            }

            if (filterField !== undefined) {
                localVarQueryParameter['Filter.Field'] = filterField;
            }

            if (filterOperator !== undefined) {
                localVarQueryParameter['Filter.Operator'] = filterOperator;
            }

            if (filterValue !== undefined) {
                localVarQueryParameter['Filter.Value'] = filterValue;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取设备能耗排行 🔖
         * @param {number} [deviceId] 设备ID
         * @param {number} [groupId] 分组ID
         * @param {Array<number>} [deviceIds] 设备ID列表
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyConsumptionRankGet: async (deviceId?: number, groupId?: number, deviceIds?: Array<number>, startTime?: Date, endTime?: Date, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyConsumption/rank`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (deviceId !== undefined) {
                localVarQueryParameter['DeviceId'] = deviceId;
            }

            if (groupId !== undefined) {
                localVarQueryParameter['GroupId'] = groupId;
            }

            if (deviceIds) {
                localVarQueryParameter['DeviceIds'] = deviceIds;
            }

            if (startTime !== undefined) {
                localVarQueryParameter['StartTime'] = (startTime as any instanceof Date) ?
                    (startTime as any).toISOString() :
                    startTime;
            }

            if (endTime !== undefined) {
                localVarQueryParameter['EndTime'] = (endTime as any instanceof Date) ?
                    (endTime as any).toISOString() :
                    endTime;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取能耗统计数据 🔖
         * @param {number} [deviceId] 设备ID
         * @param {number} [groupId] 分组ID
         * @param {Array<number>} [deviceIds] 设备ID列表
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyConsumptionStatGet: async (deviceId?: number, groupId?: number, deviceIds?: Array<number>, startTime?: Date, endTime?: Date, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyConsumption/stat`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (deviceId !== undefined) {
                localVarQueryParameter['DeviceId'] = deviceId;
            }

            if (groupId !== undefined) {
                localVarQueryParameter['GroupId'] = groupId;
            }

            if (deviceIds) {
                localVarQueryParameter['DeviceIds'] = deviceIds;
            }

            if (startTime !== undefined) {
                localVarQueryParameter['StartTime'] = (startTime as any instanceof Date) ?
                    (startTime as any).toISOString() :
                    startTime;
            }

            if (endTime !== undefined) {
                localVarQueryParameter['EndTime'] = (endTime as any instanceof Date) ?
                    (endTime as any).toISOString() :
                    endTime;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取能耗趋势数据 🔖
         * @param {number} [deviceId] 设备ID
         * @param {number} [groupId] 分组ID
         * @param {Array<number>} [deviceIds] 设备ID列表
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyConsumptionTrendGet: async (deviceId?: number, groupId?: number, deviceIds?: Array<number>, startTime?: Date, endTime?: Date, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyConsumption/trend`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (deviceId !== undefined) {
                localVarQueryParameter['DeviceId'] = deviceId;
            }

            if (groupId !== undefined) {
                localVarQueryParameter['GroupId'] = groupId;
            }

            if (deviceIds) {
                localVarQueryParameter['DeviceIds'] = deviceIds;
            }

            if (startTime !== undefined) {
                localVarQueryParameter['StartTime'] = (startTime as any instanceof Date) ?
                    (startTime as any).toISOString() :
                    startTime;
            }

            if (endTime !== undefined) {
                localVarQueryParameter['EndTime'] = (endTime as any instanceof Date) ?
                    (endTime as any).toISOString() :
                    endTime;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新能耗记录 🔖
         * @param {UpdateEnergyConsumptionInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyConsumptionUpdatePost: async (body?: UpdateEnergyConsumptionInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyConsumption/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * EnergyConsumptionApi - functional programming interface
 * @export
 */
export const EnergyConsumptionApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加能耗记录 🔖
         * @param {AddEnergyConsumptionInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyConsumptionAddPost(body?: AddEnergyConsumptionInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt64>>> {
            const localVarAxiosArgs = await EnergyConsumptionApiAxiosParamCreator(configuration).apiEnergyConsumptionAddPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 批量增加能耗记录 🔖
         * @param {Array<AddEnergyConsumptionInput>} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyConsumptionBatchAddPost(body?: Array<AddEnergyConsumptionInput>, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt32>>> {
            const localVarAxiosArgs = await EnergyConsumptionApiAxiosParamCreator(configuration).apiEnergyConsumptionBatchAddPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 清理历史数据 🔖
         * @param {number} days 保留天数
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyConsumptionCleanHistoryDataDaysPost(days: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt32>>> {
            const localVarAxiosArgs = await EnergyConsumptionApiAxiosParamCreator(configuration).apiEnergyConsumptionCleanHistoryDataDaysPost(days, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 删除能耗记录 🔖
         * @param {DeleteEnergyConsumptionInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyConsumptionDeletePost(body?: DeleteEnergyConsumptionInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await EnergyConsumptionApiAxiosParamCreator(configuration).apiEnergyConsumptionDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取节能率分析报告 🔖
         * @param {EnergyConsumptionStatInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyConsumptionEnergySavingAnalysisPost(body?: EnergyConsumptionStatInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultEnergySavingAnalysisOutput>>> {
            const localVarAxiosArgs = await EnergyConsumptionApiAxiosParamCreator(configuration).apiEnergyConsumptionEnergySavingAnalysisPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 导出能耗数据 🔖
         * @param {number} [deviceId] 设备ID
         * @param {string} [deviceCode] 设备编码
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {string} [recordType] 记录类型
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyConsumptionExportDataPost(deviceId?: number, deviceCode?: string, startTime?: Date, endTime?: Date, recordType?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultByte>>> {
            const localVarAxiosArgs = await EnergyConsumptionApiAxiosParamCreator(configuration).apiEnergyConsumptionExportDataPost(deviceId, deviceCode, startTime, endTime, recordType, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 导出能耗数据到Excel 🔖
         * @param {EnergyConsumptionInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyConsumptionExportExcelPost(body?: EnergyConsumptionInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultIActionResult>>> {
            const localVarAxiosArgs = await EnergyConsumptionApiAxiosParamCreator(configuration).apiEnergyConsumptionExportExcelPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取能耗记录分页列表 🔖
         * @param {number} [deviceId] 设备ID
         * @param {string} [deviceCode] 设备编码
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {string} [recordType] 记录类型
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyConsumptionPageGet(deviceId?: number, deviceCode?: string, startTime?: Date, endTime?: Date, recordType?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSqlSugarPagedListEnergyConsumptionOutput>>> {
            const localVarAxiosArgs = await EnergyConsumptionApiAxiosParamCreator(configuration).apiEnergyConsumptionPageGet(deviceId, deviceCode, startTime, endTime, recordType, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取设备能耗排行 🔖
         * @param {number} [deviceId] 设备ID
         * @param {number} [groupId] 分组ID
         * @param {Array<number>} [deviceIds] 设备ID列表
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyConsumptionRankGet(deviceId?: number, groupId?: number, deviceIds?: Array<number>, startTime?: Date, endTime?: Date, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultListEnergyConsumptionRankOutput>>> {
            const localVarAxiosArgs = await EnergyConsumptionApiAxiosParamCreator(configuration).apiEnergyConsumptionRankGet(deviceId, groupId, deviceIds, startTime, endTime, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取能耗统计数据 🔖
         * @param {number} [deviceId] 设备ID
         * @param {number} [groupId] 分组ID
         * @param {Array<number>} [deviceIds] 设备ID列表
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyConsumptionStatGet(deviceId?: number, groupId?: number, deviceIds?: Array<number>, startTime?: Date, endTime?: Date, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultEnergyConsumptionStatOutput>>> {
            const localVarAxiosArgs = await EnergyConsumptionApiAxiosParamCreator(configuration).apiEnergyConsumptionStatGet(deviceId, groupId, deviceIds, startTime, endTime, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取能耗趋势数据 🔖
         * @param {number} [deviceId] 设备ID
         * @param {number} [groupId] 分组ID
         * @param {Array<number>} [deviceIds] 设备ID列表
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyConsumptionTrendGet(deviceId?: number, groupId?: number, deviceIds?: Array<number>, startTime?: Date, endTime?: Date, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultListEnergyConsumptionTrendOutput>>> {
            const localVarAxiosArgs = await EnergyConsumptionApiAxiosParamCreator(configuration).apiEnergyConsumptionTrendGet(deviceId, groupId, deviceIds, startTime, endTime, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新能耗记录 🔖
         * @param {UpdateEnergyConsumptionInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyConsumptionUpdatePost(body?: UpdateEnergyConsumptionInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await EnergyConsumptionApiAxiosParamCreator(configuration).apiEnergyConsumptionUpdatePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * EnergyConsumptionApi - factory interface
 * @export
 */
export const EnergyConsumptionApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 增加能耗记录 🔖
         * @param {AddEnergyConsumptionInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyConsumptionAddPost(body?: AddEnergyConsumptionInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt64>> {
            return EnergyConsumptionApiFp(configuration).apiEnergyConsumptionAddPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 批量增加能耗记录 🔖
         * @param {Array<AddEnergyConsumptionInput>} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyConsumptionBatchAddPost(body?: Array<AddEnergyConsumptionInput>, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt32>> {
            return EnergyConsumptionApiFp(configuration).apiEnergyConsumptionBatchAddPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 清理历史数据 🔖
         * @param {number} days 保留天数
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyConsumptionCleanHistoryDataDaysPost(days: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt32>> {
            return EnergyConsumptionApiFp(configuration).apiEnergyConsumptionCleanHistoryDataDaysPost(days, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 删除能耗记录 🔖
         * @param {DeleteEnergyConsumptionInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyConsumptionDeletePost(body?: DeleteEnergyConsumptionInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return EnergyConsumptionApiFp(configuration).apiEnergyConsumptionDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取节能率分析报告 🔖
         * @param {EnergyConsumptionStatInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyConsumptionEnergySavingAnalysisPost(body?: EnergyConsumptionStatInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultEnergySavingAnalysisOutput>> {
            return EnergyConsumptionApiFp(configuration).apiEnergyConsumptionEnergySavingAnalysisPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 导出能耗数据 🔖
         * @param {number} [deviceId] 设备ID
         * @param {string} [deviceCode] 设备编码
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {string} [recordType] 记录类型
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyConsumptionExportDataPost(deviceId?: number, deviceCode?: string, startTime?: Date, endTime?: Date, recordType?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultByte>> {
            return EnergyConsumptionApiFp(configuration).apiEnergyConsumptionExportDataPost(deviceId, deviceCode, startTime, endTime, recordType, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 导出能耗数据到Excel 🔖
         * @param {EnergyConsumptionInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyConsumptionExportExcelPost(body?: EnergyConsumptionInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultIActionResult>> {
            return EnergyConsumptionApiFp(configuration).apiEnergyConsumptionExportExcelPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取能耗记录分页列表 🔖
         * @param {number} [deviceId] 设备ID
         * @param {string} [deviceCode] 设备编码
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {string} [recordType] 记录类型
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyConsumptionPageGet(deviceId?: number, deviceCode?: string, startTime?: Date, endTime?: Date, recordType?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSqlSugarPagedListEnergyConsumptionOutput>> {
            return EnergyConsumptionApiFp(configuration).apiEnergyConsumptionPageGet(deviceId, deviceCode, startTime, endTime, recordType, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取设备能耗排行 🔖
         * @param {number} [deviceId] 设备ID
         * @param {number} [groupId] 分组ID
         * @param {Array<number>} [deviceIds] 设备ID列表
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyConsumptionRankGet(deviceId?: number, groupId?: number, deviceIds?: Array<number>, startTime?: Date, endTime?: Date, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultListEnergyConsumptionRankOutput>> {
            return EnergyConsumptionApiFp(configuration).apiEnergyConsumptionRankGet(deviceId, groupId, deviceIds, startTime, endTime, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取能耗统计数据 🔖
         * @param {number} [deviceId] 设备ID
         * @param {number} [groupId] 分组ID
         * @param {Array<number>} [deviceIds] 设备ID列表
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyConsumptionStatGet(deviceId?: number, groupId?: number, deviceIds?: Array<number>, startTime?: Date, endTime?: Date, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultEnergyConsumptionStatOutput>> {
            return EnergyConsumptionApiFp(configuration).apiEnergyConsumptionStatGet(deviceId, groupId, deviceIds, startTime, endTime, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取能耗趋势数据 🔖
         * @param {number} [deviceId] 设备ID
         * @param {number} [groupId] 分组ID
         * @param {Array<number>} [deviceIds] 设备ID列表
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyConsumptionTrendGet(deviceId?: number, groupId?: number, deviceIds?: Array<number>, startTime?: Date, endTime?: Date, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultListEnergyConsumptionTrendOutput>> {
            return EnergyConsumptionApiFp(configuration).apiEnergyConsumptionTrendGet(deviceId, groupId, deviceIds, startTime, endTime, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新能耗记录 🔖
         * @param {UpdateEnergyConsumptionInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyConsumptionUpdatePost(body?: UpdateEnergyConsumptionInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return EnergyConsumptionApiFp(configuration).apiEnergyConsumptionUpdatePost(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * EnergyConsumptionApi - object-oriented interface
 * @export
 * @class EnergyConsumptionApi
 * @extends {BaseAPI}
 */
export class EnergyConsumptionApi extends BaseAPI {
    /**
     * 
     * @summary 增加能耗记录 🔖
     * @param {AddEnergyConsumptionInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyConsumptionApi
     */
    public async apiEnergyConsumptionAddPost(body?: AddEnergyConsumptionInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt64>> {
        return EnergyConsumptionApiFp(this.configuration).apiEnergyConsumptionAddPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 批量增加能耗记录 🔖
     * @param {Array<AddEnergyConsumptionInput>} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyConsumptionApi
     */
    public async apiEnergyConsumptionBatchAddPost(body?: Array<AddEnergyConsumptionInput>, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt32>> {
        return EnergyConsumptionApiFp(this.configuration).apiEnergyConsumptionBatchAddPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 清理历史数据 🔖
     * @param {number} days 保留天数
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyConsumptionApi
     */
    public async apiEnergyConsumptionCleanHistoryDataDaysPost(days: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt32>> {
        return EnergyConsumptionApiFp(this.configuration).apiEnergyConsumptionCleanHistoryDataDaysPost(days, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 删除能耗记录 🔖
     * @param {DeleteEnergyConsumptionInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyConsumptionApi
     */
    public async apiEnergyConsumptionDeletePost(body?: DeleteEnergyConsumptionInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return EnergyConsumptionApiFp(this.configuration).apiEnergyConsumptionDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取节能率分析报告 🔖
     * @param {EnergyConsumptionStatInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyConsumptionApi
     */
    public async apiEnergyConsumptionEnergySavingAnalysisPost(body?: EnergyConsumptionStatInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultEnergySavingAnalysisOutput>> {
        return EnergyConsumptionApiFp(this.configuration).apiEnergyConsumptionEnergySavingAnalysisPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 导出能耗数据 🔖
     * @param {number} [deviceId] 设备ID
     * @param {string} [deviceCode] 设备编码
     * @param {Date} [startTime] 开始时间
     * @param {Date} [endTime] 结束时间
     * @param {string} [recordType] 记录类型
     * @param {number} [page] 当前页码
     * @param {number} [pageSize] 页码容量
     * @param {string} [field] 排序字段
     * @param {string} [order] 排序方向
     * @param {string} [descStr] 降序排序
     * @param {Array<string>} [searchFields] 字段名称集合
     * @param {string} [searchKeyword] 关键字
     * @param {string} [keyword] 模糊查询关键字
     * @param {FilterLogicEnum} [filterLogic] 过滤条件
     * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
     * @param {string} [filterField] 字段名称
     * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
     * @param {any} [filterValue] 字段值
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyConsumptionApi
     */
    public async apiEnergyConsumptionExportDataPost(deviceId?: number, deviceCode?: string, startTime?: Date, endTime?: Date, recordType?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultByte>> {
        return EnergyConsumptionApiFp(this.configuration).apiEnergyConsumptionExportDataPost(deviceId, deviceCode, startTime, endTime, recordType, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 导出能耗数据到Excel 🔖
     * @param {EnergyConsumptionInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyConsumptionApi
     */
    public async apiEnergyConsumptionExportExcelPost(body?: EnergyConsumptionInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultIActionResult>> {
        return EnergyConsumptionApiFp(this.configuration).apiEnergyConsumptionExportExcelPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取能耗记录分页列表 🔖
     * @param {number} [deviceId] 设备ID
     * @param {string} [deviceCode] 设备编码
     * @param {Date} [startTime] 开始时间
     * @param {Date} [endTime] 结束时间
     * @param {string} [recordType] 记录类型
     * @param {number} [page] 当前页码
     * @param {number} [pageSize] 页码容量
     * @param {string} [field] 排序字段
     * @param {string} [order] 排序方向
     * @param {string} [descStr] 降序排序
     * @param {Array<string>} [searchFields] 字段名称集合
     * @param {string} [searchKeyword] 关键字
     * @param {string} [keyword] 模糊查询关键字
     * @param {FilterLogicEnum} [filterLogic] 过滤条件
     * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
     * @param {string} [filterField] 字段名称
     * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
     * @param {any} [filterValue] 字段值
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyConsumptionApi
     */
    public async apiEnergyConsumptionPageGet(deviceId?: number, deviceCode?: string, startTime?: Date, endTime?: Date, recordType?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSqlSugarPagedListEnergyConsumptionOutput>> {
        return EnergyConsumptionApiFp(this.configuration).apiEnergyConsumptionPageGet(deviceId, deviceCode, startTime, endTime, recordType, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取设备能耗排行 🔖
     * @param {number} [deviceId] 设备ID
     * @param {number} [groupId] 分组ID
     * @param {Array<number>} [deviceIds] 设备ID列表
     * @param {Date} [startTime] 开始时间
     * @param {Date} [endTime] 结束时间
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyConsumptionApi
     */
    public async apiEnergyConsumptionRankGet(deviceId?: number, groupId?: number, deviceIds?: Array<number>, startTime?: Date, endTime?: Date, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultListEnergyConsumptionRankOutput>> {
        return EnergyConsumptionApiFp(this.configuration).apiEnergyConsumptionRankGet(deviceId, groupId, deviceIds, startTime, endTime, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取能耗统计数据 🔖
     * @param {number} [deviceId] 设备ID
     * @param {number} [groupId] 分组ID
     * @param {Array<number>} [deviceIds] 设备ID列表
     * @param {Date} [startTime] 开始时间
     * @param {Date} [endTime] 结束时间
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyConsumptionApi
     */
    public async apiEnergyConsumptionStatGet(deviceId?: number, groupId?: number, deviceIds?: Array<number>, startTime?: Date, endTime?: Date, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultEnergyConsumptionStatOutput>> {
        return EnergyConsumptionApiFp(this.configuration).apiEnergyConsumptionStatGet(deviceId, groupId, deviceIds, startTime, endTime, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取能耗趋势数据 🔖
     * @param {number} [deviceId] 设备ID
     * @param {number} [groupId] 分组ID
     * @param {Array<number>} [deviceIds] 设备ID列表
     * @param {Date} [startTime] 开始时间
     * @param {Date} [endTime] 结束时间
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyConsumptionApi
     */
    public async apiEnergyConsumptionTrendGet(deviceId?: number, groupId?: number, deviceIds?: Array<number>, startTime?: Date, endTime?: Date, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultListEnergyConsumptionTrendOutput>> {
        return EnergyConsumptionApiFp(this.configuration).apiEnergyConsumptionTrendGet(deviceId, groupId, deviceIds, startTime, endTime, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新能耗记录 🔖
     * @param {UpdateEnergyConsumptionInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyConsumptionApi
     */
    public async apiEnergyConsumptionUpdatePost(body?: UpdateEnergyConsumptionInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return EnergyConsumptionApiFp(this.configuration).apiEnergyConsumptionUpdatePost(body, options).then((request) => request(this.axios, this.basePath));
    }
}
