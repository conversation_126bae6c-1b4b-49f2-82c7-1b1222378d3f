/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 场景排行项
 * @export
 * @interface SceneRankItem
 */
export interface SceneRankItem {
    /**
     * 场景ID
     * @type {number}
     * @memberof SceneRankItem
     */
    sceneId?: number;
    /**
     * 场景编码
     * @type {string}
     * @memberof SceneRankItem
     */
    sceneCode?: string | null;
    /**
     * 场景名称
     * @type {string}
     * @memberof SceneRankItem
     */
    sceneName?: string | null;
    /**
     * 执行次数
     * @type {number}
     * @memberof SceneRankItem
     */
    executeCount?: number;
    /**
     * 排名
     * @type {number}
     * @memberof SceneRankItem
     */
    rank?: number;
    /**
     * 最近执行时间
     * @type {Date}
     * @memberof SceneRankItem
     */
    lastExecuteTime?: Date | null;
}
