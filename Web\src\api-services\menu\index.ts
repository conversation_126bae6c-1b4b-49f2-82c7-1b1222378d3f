import { service as request } from '/@/utils/request';
import { useBaseApi } from '../base';

/**
 * 菜单管理API接口集合
 * @method getPage 获取菜单分页列表
 * @method getDetail 获取菜单详情
 * @method add 添加菜单
 * @method update 更新菜单
 * @method delete 删除菜单
 * @method getMenuTree 获取菜单树
 * @method getUserMenus 获取用户菜单
 * @method setStatus 设置菜单状态
 */
export function useMenuApi() {
	const baseApi = useBaseApi('sysMenu');
	
	return {
		...baseApi,
		// 获取菜单树
		getMenuTree: () => {
			return request({
				url: '/api/sysMenu/tree',
				method: 'get',
			});
		},
		// 获取用户菜单
		getUserMenus: () => {
			return request({
				url: '/api/sysUserMenu/menuList',
				method: 'get',
			});
		},
		// 获取用户菜单树
		getUserMenuTree: () => {
			return request({
				url: '/api/sysUserMenu/menuTree',
				method: 'get',
			});
		},
		// 获取菜单列表
		getList: (title?: string, type?: number, tenantId?: number) => {
			return request({
				url: '/api/sysMenu/list',
				method: 'get',
				params: { title, type, tenantId },
			});
		},
		// 获取登录菜单树
		getLoginMenuTree: () => {
			return request({
				url: '/api/sysMenu/loginMenuTree',
				method: 'get',
			});
		},
		// 批量删除菜单
		batchDelete: (data: any) => {
			return request({
				url: '/api/sysMenu/batchDelete',
				method: 'post',
				data,
			});
		},
		// 设置菜单状态
		setStatus: (data: any) => {
			return request({
				url: '/api/sysMenu/setStatus',
				method: 'post',
				data,
			});
		},
	};
}