// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using Microsoft.VisualStudio.TestPlatform.ObjectModel.DataCollection;
using MQTTnet.Protocol;
using SqlSugar;
using Newtonsoft.Json;
using NewLife.Caching;
using Admin.NET.Plugin.MQTT.Core.Entity;
using Admin.NET.Plugin.MQTT.Core.Services;
using Admin.NET.Plugin.MQTT.Core.Configuration;
using Admin.NET.Plugin.MQTT.Core.Models;
using Microsoft.Extensions.Options;

namespace Admin.NET.Plugin.MQTT;

/// <summary>
/// 设备控制服务
/// </summary>
public class DeviceControlService : IScoped
{
    private readonly ILogger<DeviceControlService> _logger;
    private readonly MqttMessageRouter _messageRouter;
    private readonly MqttOptions _options;
    private readonly ConcurrentDictionary<string, DeviceControlRequest> _pendingRequests;
    private readonly Timer _timeoutTimer;
    private readonly SqlSugarRepository<EnergyDevice> _energyDeviceRep;
    private readonly DeviceEventService _deviceEventService;
    private readonly ICacheProvider _cacheProvider;
    
    /// <summary>
    /// 设备扫描状态字典 - 记录正在扫描的网关设备
    /// </summary>
    private readonly ConcurrentDictionary<string, DeviceScanSession> _scanSessions;
    
    /// <summary>
    /// 扫描超时定时器字典
    /// </summary>
    private readonly ConcurrentDictionary<string, Timer> _scanTimers;

    /// <summary>
    /// 控制指令执行结果事件
    /// </summary>
    public event EventHandler<DeviceControlResult> ControlResultReceived;

    /// <summary>
    /// 构造函数
    /// </summary>
    public DeviceControlService(ILogger<DeviceControlService> logger,
                              MqttMessageRouter messageRouter,
                              IOptions<MqttOptions> options,
                              SqlSugarRepository<EnergyDevice> energyDeviceRep,
                              DeviceEventService deviceEventService,
                              ICacheProvider cacheProvider)
    {
        _logger = logger;
        _messageRouter = messageRouter;
        _options = options.Value;
        _energyDeviceRep = energyDeviceRep;
        _deviceEventService = deviceEventService;
        _cacheProvider = cacheProvider;
        _pendingRequests = new ConcurrentDictionary<string, DeviceControlRequest>();
        _scanSessions = new ConcurrentDictionary<string, DeviceScanSession>();
        _scanTimers = new ConcurrentDictionary<string, Timer>();
        
        // 启动超时检查定时器
        _timeoutTimer = new Timer(CheckTimeouts, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
        
        // 订阅设备广播事件
        _deviceEventService.DeviceBeaconReceived += OnDeviceBeaconReceived;
    }

    /// <summary>
    /// 扫描设备
    /// </summary>
    /// <param name="GatewayName">网关名称</param>
    /// <param name="area">区域</param>
    /// <param name="address">地址</param>
    /// <param name="timeoutSeconds">超时时间（秒）</param>
    /// <returns>扫描会话ID</returns>
    public async Task<string> ScanDevicesAsync(string GatewayName, string area = "00 00", string address = "00 00", int timeoutSeconds = 30)
    {
        try
        {
            var sessionId = Guid.NewGuid().ToString();
            var requestId = Guid.NewGuid().ToString();
            
            // 创建扫描会话
            var scanSession = new DeviceScanSession
            {
                SessionId = sessionId,
                GatewayName = GatewayName,
                Area = area,
                Address = address,
                StartTime = DateTime.Now,
                LastActivityTime = DateTime.Now,
                TimeoutSeconds = timeoutSeconds,
                Status = ScanStatus.Running,
                ScannedDevices = new List<ScannedDeviceInfo>()
            };
            
            _scanSessions.TryAdd(sessionId, scanSession);
            
            // 持久化会话数据到Redis
            try
            {
                var cacheKey = $"scan_session:{sessionId}";
                _cacheProvider.Cache.Set(cacheKey, scanSession, TimeSpan.FromMinutes(30));
                _logger.LogDebug("扫描会话已保存到Redis: SessionId={SessionId}", sessionId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存扫描会话到Redis失败: SessionId={SessionId}", sessionId);
            }
            
            // 构建MQTT主题和消息
            var topic = string.Format(MqttConst.Topics.GatewaySubscribe, _options.InstanceId, GatewayName);
            _logger.LogDebug("扫描设备MQTT主题: {Topic}", topic);
            
            var message = new DeviceControlMessage
            {
                code = int.Parse(MqttConst.ControlCodes.AreaControl),
                area = area,
                address = address,
                action = MqttConst.Actions.Scan,
                @params = "",
                identity = sessionId, // 使用会话ID作为标识
                deviceName = GatewayName
            };

            // 记录待处理请求（保持原有逻辑兼容性）
            _pendingRequests.TryAdd(requestId, new DeviceControlRequest
            {
                RequestId = requestId,
                RequestTime = DateTime.UtcNow,
                TimeoutSeconds = timeoutSeconds,
                Action = MqttConst.Actions.Scan
            });

            // 发布MQTT扫描命令
            await _messageRouter.PublishAsync(topic, message, MqttQualityOfServiceLevel.AtLeastOnce);
            
            // 启动扫描超时定时器
            StartScanTimeout(sessionId, timeoutSeconds * 1000); // 转换为毫秒
            
            _logger.LogInformation("已启动设备扫描: Gateway={Gateway}, Area={Area}, SessionId={SessionId}, Timeout={Timeout}s", 
                GatewayName, area, sessionId, timeoutSeconds);
                
            return sessionId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动设备扫描失败: Gateway={Gateway}, Area={Area}", GatewayName, area);
            throw;
        }
    }

    /// <summary>
    /// 获取扫描结果（在线设备列表）
    /// </summary>
    /// <param name="sessionId">扫描会话ID</param>
    /// <returns>在线设备列表</returns>
    public async Task<List<object>> GetScanResultsAsync(string sessionId)
    {
        try
        {
            if (!_scanSessions.TryGetValue(sessionId, out var session))
            {
                // 尝试从Redis恢复会话数据
                try
                {
                    var cacheKey = $"scan_session:{sessionId}";
                    session = _cacheProvider.Cache.Get<DeviceScanSession>(cacheKey);
                    if (session != null)
                    {
                        _scanSessions.TryAdd(sessionId, session);
                        _logger.LogDebug("从Redis恢复扫描会话: SessionId={SessionId}", sessionId);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "从Redis恢复扫描会话失败: SessionId={SessionId}", sessionId);
                }
                
                if (session == null)
                {
                    _logger.LogWarning("扫描会话不存在: SessionId={SessionId}，请检查MQTT插件配置及连接状态", sessionId);
                    return new List<object>();
                }
            }
            
            // 检查会话状态，如果已结束则返回缓存的结果
            if (session.Status != ScanStatus.Running)
            {
                _logger.LogInformation("获取已结束扫描会话的结果: SessionId={SessionId}, Status={Status}", sessionId, session.Status);
            }

            // 获取扫描到的设备信息
            var scannedDevices = session.ScannedDevices.ToList();
            
            // 同时从数据库获取在线设备信息，确保数据完整性
            var onlineDevices = await _energyDeviceRep.GetListAsync(d => d.IsOnline == true);
            
            // 构建前端兼容的设备列表格式
            var deviceList = new List<object>();
            
            // 添加扫描到的设备
            foreach (var scannedDevice in scannedDevices)
            {
                var deviceInfo = new
                {
                    id = scannedDevice.UniqueId,
                    deviceCode = scannedDevice.UniqueId,
                    deviceName = scannedDevice.DeviceName ?? $"设备_{scannedDevice.UniqueId}",
                    deviceType = MapDeviceType(scannedDevice.DeviceType),
                    status = 1, // 在线状态
                    isOnline = true,
                    location = "自动发现",
                    power = (int?)null,
                    brightness = (int?)null,
                    installDate = (DateTime?)null,
                    updateTime = DateTime.Now,
                    signalStrength = scannedDevice.SignalStrength,
                    batteryLevel = scannedDevice.BatteryLevel,
                    version = scannedDevice.Version,
                    discoveredTime = scannedDevice.DiscoveredTime,
                    parentDeviceId = (long?)null,
                    gatewayId = (long?)null,
                    source = "scan" // 标识来源为扫描
                };
                deviceList.Add(deviceInfo);
            }
            
            // 添加数据库中的在线设备（避免重复）
            var scannedDeviceIds = scannedDevices.Select(d => d.UniqueId).ToHashSet();
            foreach (var dbDevice in onlineDevices)
            {
                if (!scannedDeviceIds.Contains(dbDevice.DeviceCode))
                {
                    var deviceInfo = new
                    {
                        id = dbDevice.Id,
                        deviceCode = dbDevice.DeviceCode,
                        deviceName = dbDevice.DeviceName,
                        deviceType = dbDevice.DeviceType,
                        status = dbDevice.Status,
                        isOnline = dbDevice.IsOnline,
                        location = dbDevice.Location,
                        power = dbDevice.PowerRating,
                        brightness = dbDevice.Brightness,
                        installDate = dbDevice.InstallDate,
                        updateTime = dbDevice.UpdateTime,
                        signalStrength = (int?)null,
                        batteryLevel = (int?)null,
                        version = (string)null,
                        discoveredTime = (DateTime?)null,
                        source = "database" // 标识来源为数据库
                    };
                    deviceList.Add(deviceInfo);
                }
            }
            
            _logger.LogInformation("获取扫描结果: SessionId={SessionId}, 扫描设备={ScannedCount}, 数据库设备={DbCount}, 总计={Total}", 
                sessionId, scannedDevices.Count, onlineDevices.Count, deviceList.Count);
                
            return deviceList;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取扫描结果失败: SessionId={SessionId}", sessionId);
            return new List<object>();
        }
    }

    /// <summary>
    /// 获取设备状态
    /// </summary>
    public async Task<string> GetDeviceStatusAsync(string area, string address, int timeoutSeconds = 10)
    {
        try
        {
            var requestId = Guid.NewGuid().ToString();
            var topic = string.Format(MqttConst.Topics.GatewaySubscribe, _options.ClientId, "Gateway");
            
            var message = new DeviceControlMessage
            {
                code = int.Parse(MqttConst.ControlCodes.SingleLight),
                area = area,
                address = address,
                action = MqttConst.Actions.GetStatus,
                @params = "",
                identity = "AdminNET",
                deviceName = "Gateway"
            };

            _pendingRequests.TryAdd(requestId, new DeviceControlRequest
            {
                RequestId = requestId,
                RequestTime = DateTime.UtcNow,
                TimeoutSeconds = timeoutSeconds,
                Action = MqttConst.Actions.GetStatus
            });

            await _messageRouter.PublishAsync(topic, message, MqttQualityOfServiceLevel.AtLeastOnce);
            
            _logger.LogInformation("发送获取设备状态指令, 区域: {Area}, 地址: {Address}, 请求ID: {RequestId}", 
                                 area, address, requestId);
            return requestId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备状态失败, 区域: {Area}, 地址: {Address}", area, address);
            throw;
        }
    }

    /// <summary>
    /// 设置照明参数
    /// </summary>
    public async Task<string> SetLightingAsync(string area, string address, SettingEventData settings, int timeoutSeconds = 10)
    {
        try
        {
            var requestId = Guid.NewGuid().ToString();
            var topic = string.Format(MqttConst.Topics.GatewaySubscribe, _options.ClientId, "Gateway");
            var paramsJson = JsonConvert.SerializeObject(settings);
            var message = new DeviceControlMessage
            {
                code = int.Parse(MqttConst.ControlCodes.SingleLight),
                area = area,
                address = address,
                action = MqttConst.Actions.SetLighting,
                @params = paramsJson,
                identity = "AdminNET",
                deviceName = "Gateway"
            };

            _pendingRequests.TryAdd(requestId, new DeviceControlRequest
            {
                RequestId = requestId,
                RequestTime = DateTime.UtcNow,
                TimeoutSeconds = timeoutSeconds,
                Action = MqttConst.Actions.SetLighting
            });

            await _messageRouter.PublishAsync(topic, message, MqttQualityOfServiceLevel.AtLeastOnce);
            
            _logger.LogInformation("发送设置照明参数指令, 区域: {Area}, 地址: {Address}, 请求ID: {RequestId}", 
                                 area, address, requestId);
            return requestId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置照明参数失败, 区域: {Area}, 地址: {Address}", area, address);
            throw;
        }
    }
    /// <summary>
    /// 开关灯控制
    /// </summary>
    public async Task<string> SwitchLightAsync(string area, string address, bool isOn, int timeoutSeconds = 10)
    {
        try
        {
            var requestId = Guid.NewGuid().ToString();
            var topic = string.Format(MqttConst.Topics.GatewaySubscribe, _options.ClientId, "Gateway");
            var action = isOn ? MqttConst.Actions.TurnOn : MqttConst.Actions.TurnOff;
            
            var message = new DeviceControlMessage
            {
                code = int.Parse(MqttConst.ControlCodes.SingleLight),
                area = area,
                address = address,
                action = action,
                @params = "",
                identity = "AdminNET",
                deviceName = "Gateway"
            };

            _pendingRequests.TryAdd(requestId, new DeviceControlRequest
            {
                RequestId = requestId,
                RequestTime = DateTime.UtcNow,
                TimeoutSeconds = timeoutSeconds,
                Action = action
            });
            await _messageRouter.PublishAsync(topic, message, MqttQualityOfServiceLevel.AtLeastOnce);
            
            _logger.LogInformation("发送开关灯指令, 区域: {Area}, 地址: {Address}, 状态: {IsOn}, 请求ID: {RequestId}", 
                                 area, address, isOn, requestId);
            return requestId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "开关灯控制失败, 区域: {Area}, 地址: {Address}, 状态: {IsOn}", area, address, isOn);
            throw;
        }
    }

    /// <summary>
    /// 调光控制
    /// </summary>
    public async Task<string> DimLightAsync(string area, string address, int brightness, int transitionTime = 0, int timeoutSeconds = 10)
    {
        try
        {
            var requestId = Guid.NewGuid().ToString();
            var topic = string.Format(MqttConst.Topics.GatewaySubscribe, _options.ClientId, "Gateway");
            
            var dimParams = new
            {
                brightness = Math.Max(0, Math.Min(100, brightness)),
                transitionTime = Math.Max(0, transitionTime)
            };
            var paramsJson = JsonConvert.SerializeObject(dimParams);
            
            var message = new DeviceControlMessage
            {
                code = int.Parse(MqttConst.ControlCodes.SingleLight),
                area = area,
                address = address,
                action = MqttConst.Actions.Dimming,
                @params = paramsJson,
                identity = "",
                deviceName = "Gateway"
            };

            _pendingRequests.TryAdd(requestId, new DeviceControlRequest
            {
                RequestId = requestId,
                RequestTime = DateTime.UtcNow,
                TimeoutSeconds = timeoutSeconds,
                Action = MqttConst.Actions.Dimming
            });

            await _messageRouter.PublishAsync(topic, message, MqttQualityOfServiceLevel.AtLeastOnce);
            
            _logger.LogInformation("发送调光指令, 区域: {Area}, 地址: {Address}, 亮度: {Brightness}%, 请求ID: {RequestId}", 
                                 area, address, brightness, requestId);
            return requestId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "调光控制失败, 区域: {Area}, 地址: {Address}, 亮度: {Brightness}", area, address, brightness);
            throw;
        }
    }

    /// <summary>
    /// 群组控制
    /// </summary>
    public async Task<string> GroupControlAsync(string groupId, string action, string parameters = "", int timeoutSeconds = 15)
    {
        try
        {
            var requestId = Guid.NewGuid().ToString();
            var topic = string.Format(MqttConst.Topics.GatewaySubscribe, _options.ClientId, "Gateway");
            
            var message = new DeviceControlMessage
            {
                code = int.Parse(MqttConst.ControlCodes.GroupControl),
                area = "*",
                address = groupId,
                action = action,
                @params = parameters,
                identity = "",
                deviceName = "Gateway"
            };

            _pendingRequests.TryAdd(requestId, new DeviceControlRequest
            {
                RequestId = requestId,
                RequestTime = DateTime.UtcNow,
                TimeoutSeconds = timeoutSeconds,
                Action = action
            });

            await _messageRouter.PublishAsync(topic, message, MqttQualityOfServiceLevel.AtLeastOnce);
            
            _logger.LogInformation("发送群组控制指令, 群组ID: {GroupId}, 动作: {Action}, 请求ID: {RequestId}", 
                                 groupId, action, requestId);
            return requestId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "群组控制失败, 群组ID: {GroupId}, 动作: {Action}", groupId, action);
            throw;
        }
    }

    /// <summary>
    /// 标签控制
    /// </summary>
    public async Task<string> TagControlAsync(string tag, string action, string parameters = "", int timeoutSeconds = 15)
    {
        try
        {
            var requestId = Guid.NewGuid().ToString();
            var topic = string.Format(MqttConst.Topics.GatewaySubscribe, _options.ClientId, "Gateway");
            
            var message = new DeviceControlMessage
            {
                code = int.Parse(MqttConst.ControlCodes.TagControl),
                area = "*",
                address = tag,
                action = action,
                @params = parameters,
                identity = "",
                deviceName = "Gateway"
            };

            _pendingRequests.TryAdd(requestId, new DeviceControlRequest
            {
                RequestId = requestId,
                RequestTime = DateTime.UtcNow,
                TimeoutSeconds = timeoutSeconds,
                Action = action
            });

            await _messageRouter.PublishAsync(topic, message, MqttQualityOfServiceLevel.AtLeastOnce);
            
            _logger.LogInformation("发送标签控制指令, 标签: {Tag}, 动作: {Action}, 请求ID: {RequestId}", 
                                 tag, action, requestId);
            return requestId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "标签控制失败, 标签: {Tag}, 动作: {Action}", tag, action);
            throw;
        }
    }

    /// <summary>
    /// 全区控制
    /// </summary>
    public async Task<string> AreaControlAsync(string action, string parameters = "", int timeoutSeconds = 20)
    {
        try
        {
            var requestId = Guid.NewGuid().ToString();
            var topic = string.Format(MqttConst.Topics.GatewaySubscribe, _options.ClientId, "Gateway");
            
            var message = new DeviceControlMessage
            {
                code = int.Parse(MqttConst.ControlCodes.AreaControl),
                area = "*",
                address = "*",
                action = action,
                @params = parameters,
                identity = "AdminNET",
                deviceName = "Gateway"
            };

            _pendingRequests.TryAdd(requestId, new DeviceControlRequest
            {
                RequestId = requestId,
                RequestTime = DateTime.UtcNow,
                TimeoutSeconds = timeoutSeconds,
                Action = action
            });

            await _messageRouter.PublishAsync(topic, message, MqttQualityOfServiceLevel.AtLeastOnce);
            
            _logger.LogInformation("发送全区控制指令, 动作: {Action}, 请求ID: {RequestId}", action, requestId);
            return requestId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "全区控制失败, 动作: {Action}", action);
            throw;
        }
    }

    /// <summary>
    /// 处理控制结果
    /// </summary>
    public void HandleControlResult(string requestId, DeviceControlResult result)
    {
        try
        {
            if (_pendingRequests.TryRemove(requestId, out var request))
            {
                result.RequestId = requestId;
                result.RequestTime = request.RequestTime;
                result.ResponseTime = DateTime.UtcNow;
                result.Duration = result.ResponseTime - result.RequestTime;

                ControlResultReceived?.Invoke(this, result);
                
                _logger.LogInformation("收到控制结果, 请求ID: {RequestId}, 状态: {Status}, 耗时: {Duration}ms", 
                                     requestId, result.Status, result.Duration.TotalMilliseconds);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理控制结果失败, 请求ID: {RequestId}", requestId);
        }
    }

    /// <summary>
    /// 获取待处理请求
    /// </summary>
    public Dictionary<string, DeviceControlRequest> GetPendingRequests()
    {
        return new Dictionary<string, DeviceControlRequest>(_pendingRequests);
    }

    /// <summary>
    /// 取消请求
    /// </summary>
    public bool CancelRequest(string requestId)
    {
        var removed = _pendingRequests.TryRemove(requestId, out var request);
        if (removed)
        {
            _logger.LogInformation("取消控制请求, 请求ID: {RequestId}", requestId);
        }
        return removed;
    }

    /// <summary>
    /// 检查超时请求
    /// </summary>
    private void CheckTimeouts(object state)
    {
        try
        {
            var now = DateTime.UtcNow;
            var timeoutRequests = _pendingRequests
                .Where(kvp => (now - kvp.Value.RequestTime).TotalSeconds > kvp.Value.TimeoutSeconds)
                .ToList();

            foreach (var kvp in timeoutRequests)
            {
                if (_pendingRequests.TryRemove(kvp.Key, out var request))
                {
                    var result = new DeviceControlResult
                    {
                        RequestId = kvp.Key,
                        Status = "timeout",
                        Message = "请求超时",
                        RequestTime = request.RequestTime,
                        ResponseTime = now,
                        Duration = now - request.RequestTime
                    };

                    ControlResultReceived?.Invoke(this, result);
                    
                    _logger.LogWarning("控制请求超时, 请求ID: {RequestId}, 动作: {Action}, 超时时间: {TimeoutSeconds}秒", 
                                     kvp.Key, request.Action, request.TimeoutSeconds);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查超时请求失败");
        }
    }

    /// <summary>
    /// 处理设备广播事件
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">设备广播事件参数</param>
    private async void OnDeviceBeaconReceived(object sender, DeviceBeaconEventArgs e)
    {
        try
        {
            _logger.LogInformation("收到设备广播事件: DeviceKey={DeviceKey}, DeviceType={DeviceType}", 
                e.DeviceKey, e.BeaconData?.DeviceType);

            // 检查是否有正在进行的扫描会话
            var activeSessions = _scanSessions.Values.Where(s => s.Status == ScanStatus.Running).ToList();
            if (!activeSessions.Any())
            {
                _logger.LogDebug("当前没有活跃的扫描会话，忽略beacon事件");
                return;
            }

            // 解析设备信息
            var deviceInfo = ParseBeaconData(e);
            if (deviceInfo == null)
            {
                _logger.LogWarning("解析beacon数据失败: {DeviceKey}", e.DeviceKey);
                return;
            }

            // 将设备添加到相关的扫描会话中
            foreach (var session in activeSessions)
            {
                // 检查设备是否已经在会话中
                if (!session.ScannedDevices.Any(d => d.UniqueId == deviceInfo.UniqueId))
                {
                    session.ScannedDevices.Add(deviceInfo);
                    _logger.LogInformation("设备已添加到扫描会话 {SessionId}: {DeviceName}", 
                        session.SessionId, deviceInfo.DeviceName);
                }
            }

            // 保存设备信息到数据库
            await SaveScannedDeviceToDatabase(deviceInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理设备广播事件时发生错误: {DeviceKey}", e.DeviceKey);
        }
    }

    /// <summary>
    /// 解析beacon事件数据
    /// </summary>
    /// <param name="e">设备广播事件参数</param>
    /// <returns>扫描到的设备信息</returns>
    private ScannedDeviceInfo ParseBeaconData(DeviceBeaconEventArgs e)
    {
        try
        {
            if (e.BeaconData == null)
            {
                _logger.LogWarning("Beacon数据为空: {DeviceKey}", e.DeviceKey);
                return null;
            }

            var deviceInfo = new ScannedDeviceInfo
            {
                UniqueId = e.BeaconData.UniqueId ?? e.DeviceKey,
                DeviceType = e.BeaconData.DeviceType ?? "unknown",
                DeviceName = e.BeaconData.DeviceName ?? $"设备_{e.DeviceKey}",
                Version = e.BeaconData.Version ?? "1.0.0",
                SignalStrength = e.BeaconData.SignalStrength,
                    BatteryLevel = e.BeaconData.BatteryLevel,
                DiscoveredTime = DateTime.Now,
                RawData = e.BeaconData
            };

            _logger.LogDebug("成功解析设备信息: {DeviceName} ({DeviceType})", 
                deviceInfo.DeviceName, deviceInfo.DeviceType);

            return deviceInfo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析beacon数据时发生错误: {DeviceKey}", e.DeviceKey);
            return null;
        }
    }

    /// <summary>
    /// 保存扫描到的设备信息到数据库
    /// </summary>
    /// <param name="deviceInfo">扫描到的设备信息</param>
    private async Task SaveScannedDeviceToDatabase(ScannedDeviceInfo deviceInfo)
    {
        try
        {
            // 检查设备是否已存在
            var existingDevice = await _energyDeviceRep.GetFirstAsync(d => 
                d.DeviceCode == deviceInfo.UniqueId);

            if (existingDevice != null)
            {
                // 更新现有设备状态
                existingDevice.IsOnline = true;
                existingDevice.Status = 1; // 在线状态
                existingDevice.UpdateTime = DateTime.Now;
                
                // 更新设备属性（如果有新信息）
                if (!string.IsNullOrEmpty(deviceInfo.Version))
                {
                    // 更新设备型号为版本信息
                    existingDevice.DeviceModel = deviceInfo.Version;
                }

                await _energyDeviceRep.UpdateAsync(existingDevice);
                _logger.LogInformation("更新现有设备: {DeviceName}", deviceInfo.DeviceName);
            }
            else
            {
                // 创建新设备记录
                var newDevice = new EnergyDevice
                {
                    DeviceCode = deviceInfo.UniqueId,
                    DeviceName = deviceInfo.DeviceName,
                    DeviceType = MapDeviceType(deviceInfo.DeviceType),
                    DeviceModel = deviceInfo.Version,
                    Location = "自动发现",
                    Status = 1, // 在线状态
                    IsOnline = true,
                    InstallDate = DateTime.Now,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                };

                await _energyDeviceRep.InsertAsync(newDevice);
                _logger.LogInformation("创建新设备记录: {DeviceName}", deviceInfo.DeviceName);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存设备信息到数据库时发生错误: {DeviceName}", deviceInfo.DeviceName);
        }
    }

    /// <summary>
    /// 映射设备类型
    /// </summary>
    /// <param name="beaconDeviceType">beacon中的设备类型</param>
    /// <returns>系统中的设备类型</returns>
    private string MapDeviceType(string beaconDeviceType)
    {
        return beaconDeviceType?.ToLower() switch
        {
            "light" or "led" or "lamp" => "LED灯",
            "gateway" or "hub" => "网关",
            "sensor" => "传感器",
            "switch" => "开关",
            "dimmer" => "调光器",
            _ => "智能设备"
        };
    }

    /// <summary>
    /// 测试数据库操作
    /// 用于验证设备扫描后的数据库保存和查询功能
    /// </summary>
    /// <returns>测试结果</returns>
    public async Task<object> TestDatabaseOperationsAsync()
    {
        try
        {
            var result = new
            {
                Success = true,
                Message = "数据库操作测试完成",
                Details = new List<object>()
            };

            // 1. 测试数据库连接
            try
            {
                var connectionTest = await _energyDeviceRep.Context.Ado.GetDataTableAsync("SELECT 1");
                result.Details.Add(new { Test = "数据库连接", Status = "成功", Message = "数据库连接正常" });
            }
            catch (Exception ex)
            {
                result.Details.Add(new { Test = "数据库连接", Status = "失败", Message = ex.Message });
                return result;
            }

            // 2. 查询现有设备数量
            var totalDevices = await _energyDeviceRep.AsQueryable().CountAsync();
            result.Details.Add(new { Test = "设备总数查询", Status = "成功", Message = $"数据库中共有 {totalDevices} 个设备" });

            // 3. 查询最近创建的设备
            var recentDevices = await _energyDeviceRep.AsQueryable()
                .Where(d => d.CreateTime >= DateTime.Now.AddHours(-24))
                .OrderBy(d => d.CreateTime, OrderByType.Desc)
                .Take(10)
                .ToListAsync();
            
            result.Details.Add(new { 
                Test = "最近设备查询", 
                Status = "成功", 
                Message = $"最近24小时创建了 {recentDevices.Count} 个设备",
                Devices = recentDevices.Select(d => new {
                    d.DeviceCode,
                    d.DeviceName,
                    d.DeviceType,
                    d.Status,
                    d.IsOnline,
                    d.CreateTime
                }).ToList()
            });

            // 4. 测试设备保存功能
            var testDeviceCode = "TEST_DB_" + DateTime.Now.Ticks;
            var testDevice = new EnergyDevice
            {
                DeviceCode = testDeviceCode,
                DeviceName = "数据库测试设备_" + DateTime.Now.ToString("HHmmss"),
                DeviceType = "LED灯",
                DeviceModel = "测试型号",
                Location = "测试位置",
                Status = 1,
                IsOnline = true,
                InstallDate = DateTime.Now,
                CreateTime = DateTime.Now,
                UpdateTime = DateTime.Now
            };

            try
            {
                var insertResult = await _energyDeviceRep.InsertAsync(testDevice);
                
                // 验证保存是否成功
                var savedDevice = await _energyDeviceRep.GetFirstAsync(d => d.DeviceCode == testDeviceCode);
                if (savedDevice != null)
                {
                    result.Details.Add(new { 
                        Test = "设备保存测试", 
                        Status = "成功", 
                        Message = $"测试设备保存成功，ID: {savedDevice.Id}",
                        Device = new {
                            savedDevice.Id,
                            savedDevice.DeviceCode,
                            savedDevice.DeviceName,
                            savedDevice.CreateTime
                        }
                    });
                    
                    // 清理测试数据
                    await _energyDeviceRep.DeleteAsync(savedDevice);
                    result.Details.Add(new { Test = "测试数据清理", Status = "成功", Message = "测试设备已删除" });
                }
                else
                {
                    result.Details.Add(new { Test = "设备保存测试", Status = "失败", Message = "设备保存后无法查询到" });
                }
            }
            catch (Exception ex)
            {
                result.Details.Add(new { Test = "设备保存测试", Status = "失败", Message = ex.Message });
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据库操作测试失败");
            return new
            {
                Success = false,
                Message = "数据库操作测试失败",
                Error = ex.Message
            };
        }
    }

    /// <summary>
    /// 启动扫描超时定时器
    /// </summary>
    /// <param name="sessionId">扫描会话ID</param>
    /// <param name="timeoutMs">超时时间（毫秒）</param>
    private void StartScanTimeout(string sessionId, int timeoutMs)
    {
        try
        {
            // 如果已存在定时器，先清理
            if (_scanTimers.ContainsKey(sessionId))
            {
                _scanTimers[sessionId]?.Dispose();
                _scanTimers.Remove(sessionId);
            }

            // 创建新的超时定时器
            var timer = new Timer(async _ => await OnScanTimeout(sessionId), null, timeoutMs, Timeout.Infinite);
            _scanTimers[sessionId] = timer;

            _logger.LogInformation("扫描超时定时器已启动: SessionId={SessionId}, Timeout={TimeoutMs}ms", 
                sessionId, timeoutMs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动扫描超时定时器失败: SessionId={SessionId}", sessionId);
        }
    }

    /// <summary>
    /// 更新会话状态并同步到Redis
    /// </summary>
    /// <param name="session">扫描会话</param>
    /// <param name="status">新状态</param>
    /// <param name="errorMessage">错误信息（可选）</param>
    private void UpdateSessionStatus(DeviceScanSession session, ScanStatus status, string errorMessage = null)
    {
        try
        {
            session.Status = status;
            session.LastActivityTime = DateTime.Now;
            
            if (status != ScanStatus.Running)
            {
                session.EndTime = DateTime.Now;
            }
            
            if (!string.IsNullOrEmpty(errorMessage))
            {
                session.ErrorMessage = errorMessage;
            }
            
            // 同步到Redis
            var cacheKey = $"scan_session:{session.SessionId}";
            _cacheProvider.Cache.Set(cacheKey, session, TimeSpan.FromMinutes(30));
            _logger.LogDebug("会话状态已更新并同步到Redis: SessionId={SessionId}, Status={Status}", session.SessionId, status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新会话状态到Redis失败: SessionId={SessionId}, Status={Status}", session.SessionId, status);
        }
    }
    
    /// <summary>
    /// 扫描超时处理
    /// </summary>
    /// <param name="sessionId">扫描会话ID</param>
    private async Task OnScanTimeout(string sessionId)
    {
        try
        {
            _logger.LogInformation("扫描会话超时: SessionId={SessionId}", sessionId);

            if (_scanSessions.TryGetValue(sessionId, out var session))
            {
                // 更新会话状态为超时
                UpdateSessionStatus(session, ScanStatus.Timeout, "扫描超时");

                // 发布停止扫描的MQTT消息
                await PublishStopScanCommand(session.GatewayName, session.Area, session.Address);

                _logger.LogInformation("扫描会话已超时并停止: SessionId={SessionId}, 发现设备数量={DeviceCount}", 
                    sessionId, session.ScannedDevices.Count);
            }

            // 清理定时器
            if (_scanTimers.TryGetValue(sessionId, out var timer))
            {
                timer?.Dispose();
                _scanTimers.Remove(sessionId);
            }
            
            // 延迟清理会话（给前端一些时间获取最终结果）
            _ = Task.Delay(TimeSpan.FromMinutes(5)).ContinueWith(_ => 
            {
                if (_scanSessions.TryRemove(sessionId, out var removedSession))
                {
                    _logger.LogInformation("已清理超时扫描会话: SessionId={SessionId}", sessionId);
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理扫描超时时发生错误: SessionId={SessionId}", sessionId);
        }
    }

    /// <summary>
    /// 发布停止扫描的MQTT命令
    /// </summary>
    /// <param name="gatewayName">网关名称</param>
    /// <param name="area">区域</param>
    /// <param name="address">地址</param>
    /// <param name="maxRetries">最大重试次数</param>
    private async Task PublishStopScanCommand(string gatewayName, string area, string address, int maxRetries = 3)
    {
        var retryCount = 0;
        
        while (retryCount < maxRetries)
        {
            try
            {
                // 检查MQTT连接状态
                if (!_messageRouter.IsConnected)
                {
                    _logger.LogWarning("MQTT未连接，等待连接建立: Gateway={Gateway}", gatewayName);
                    
                    // 等待连接建立
                    var waitTime = 0;
                    while (!_messageRouter.IsConnected && waitTime < 5000)
                    {
                        await Task.Delay(100);
                        waitTime += 100;
                    }
                    
                    if (!_messageRouter.IsConnected)
                    {
                        throw new InvalidOperationException("MQTT连接失败");
                    }
                }

                // 构建MQTT主题和消息
                var topic = string.Format(MqttConst.Topics.GatewaySubscribe, _options.InstanceId, gatewayName);
                _logger.LogDebug("停止扫描MQTT主题: {Topic}", topic);

                var stopScanMessage = new DeviceControlMessage
                {
                    code = int.Parse(MqttConst.ControlCodes.AreaControl),
                    area = area,
                    address = address,
                    action = MqttConst.Actions.StopScan, // 使用停止扫描动作
                    @params = "",
                    identity = Guid.NewGuid().ToString(), // 添加唯一标识
                    deviceName = gatewayName
                };

                var messageJson = JsonConvert.SerializeObject(stopScanMessage);
                
                // 发布MQTT消息
                await _messageRouter.PublishAsync(topic, messageJson);
                
                _logger.LogInformation("停止扫描命令发布成功: Topic={Topic}, Gateway={Gateway}, Identity={Identity}", 
                    topic, gatewayName, stopScanMessage.identity);
                return; // 成功发布，退出重试循环
            }
            catch (Exception ex)
            {
                retryCount++;
                _logger.LogError(ex, "发布停止扫描命令失败 (尝试 {RetryCount}/{MaxRetries}): Gateway={Gateway}", 
                    retryCount, maxRetries, gatewayName);
                
                if (retryCount >= maxRetries)
                {
                    _logger.LogError("停止扫描命令发布最终失败: Gateway={Gateway}", gatewayName);
                    break;
                }
                
                // 指数退避重试
                var delay = TimeSpan.FromMilliseconds(Math.Pow(2, retryCount) * 1000);
                await Task.Delay(delay);
            }
        }
    }

    /// <summary>
    /// 完成扫描会话
    /// </summary>
    /// <param name="sessionId">扫描会话ID</param>
    public async Task CompleteScanSession(string sessionId)
    {
        try
        {
            if (_scanSessions.TryGetValue(sessionId, out var session))
            {
                // 更新会话状态为已完成
                session.DeviceCount = session.ScannedDevices.Count;
                UpdateSessionStatus(session, ScanStatus.Completed);

                // 发布停止扫描命令
                await PublishStopScanCommand(session.GatewayName, session.Area, session.Address);

                // 清理定时器
                if (_scanTimers.TryGetValue(sessionId, out var timer))
                {
                    timer?.Dispose();
                    _scanTimers.Remove(sessionId);
                }
                
                // 延迟清理会话（给前端一些时间获取最终结果）
                _ = Task.Delay(TimeSpan.FromMinutes(5)).ContinueWith(_ => 
                {
                    if (_scanSessions.TryRemove(sessionId, out var removedSession))
                    {
                        _logger.LogInformation("已清理完成的扫描会话: SessionId={SessionId}", sessionId);
                    }
                });

                _logger.LogInformation("扫描会话已完成: SessionId={SessionId}, 发现设备数量={DeviceCount}", 
                    sessionId, session.ScannedDevices.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "完成扫描会话失败: SessionId={SessionId}", sessionId);
        }
    }

    /// <summary>
    /// 停止扫描会话
    /// </summary>
    /// <param name="sessionId">扫描会话ID</param>
    /// <param name="reason">停止原因</param>
    public async Task StopScanSession(string sessionId, string reason = null)
    {
        try
        {
            if (_scanSessions.TryGetValue(sessionId, out var session))
            {
                // 更新会话状态
                session.DeviceCount = session.ScannedDevices.Count;
                UpdateSessionStatus(session, ScanStatus.Cancelled, reason);

                // 发布停止扫描命令
                await PublishStopScanCommand(session.GatewayName, session.Area, session.Address);

                // 清理定时器
                if (_scanTimers.TryGetValue(sessionId, out var timer))
                {
                    timer?.Dispose();
                    _scanTimers.Remove(sessionId);
                }
                
                // 延迟清理会话（给前端一些时间获取最终结果）
                _ = Task.Delay(TimeSpan.FromMinutes(5)).ContinueWith(_ => 
                {
                    if (_scanSessions.TryRemove(sessionId, out var removedSession))
                    {
                        _logger.LogInformation("已清理停止的扫描会话: SessionId={SessionId}", sessionId);
                    }
                });

                _logger.LogInformation("扫描会话已手动停止: SessionId={SessionId}", sessionId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止扫描会话失败: SessionId={SessionId}", sessionId);
        }
    }

    /// <summary>
    /// 获取扫描会话结果
    /// </summary>
    /// <param name="sessionId">扫描会话ID</param>
    /// <returns>扫描会话信息</returns>
    public DeviceScanSession GetScanSession(string sessionId)
    {
        _scanSessions.TryGetValue(sessionId, out var session);
        return session;
    }
    
    /// <summary>
    /// 检查扫描会话是否存在且有效
    /// </summary>
    /// <param name="sessionId">扫描会话ID</param>
    /// <returns>会话是否存在且有效</returns>
    public bool IsValidScanSession(string sessionId)
    {
        if (string.IsNullOrEmpty(sessionId))
            return false;
            
        if (!_scanSessions.TryGetValue(sessionId, out var session))
            return false;
            
        // 检查会话是否仍然活跃且未过期
        if (!session.IsActive)
            return false;
            
        // 检查会话是否超过最大生命周期（30分钟）
        var maxLifetime = TimeSpan.FromMinutes(30);
        if (DateTime.Now - session.StartTime > maxLifetime)
        {
            UpdateSessionStatus(session, ScanStatus.Timeout, "会话超时");
            return false;
        }
            
        return true;
    }
    
    /// <summary>
    /// 清理过期的扫描会话
    /// </summary>
    public void CleanupExpiredSessions()
    {
        try
        {
            var now = DateTime.Now;
            var expiredSessions = new List<string>();
            
            foreach (var kvp in _scanSessions)
            {
                var session = kvp.Value;
                var sessionAge = now - session.StartTime;
                
                // 清理超过30分钟的会话
                if (sessionAge > TimeSpan.FromMinutes(30))
                {
                    expiredSessions.Add(kvp.Key);
                }
                // 标记超过10分钟但未完成的会话为超时
                else if (sessionAge > TimeSpan.FromMinutes(10) && session.IsActive)
                {
                    UpdateSessionStatus(session, ScanStatus.Timeout, "扫描超时");
                }
            }
                
            foreach (var sessionId in expiredSessions)
            {
                if (_scanSessions.TryRemove(sessionId, out var session))
                {
                    // 清理相关的定时器
                    if (_scanTimers.TryRemove(sessionId, out var timer))
                    {
                        timer?.Dispose();
                    }
                    
                    // 从Redis中删除会话数据
                    try
                    {
                        var cacheKey = $"scan_session:{sessionId}";
                        _cacheProvider.Cache.Remove(cacheKey);
                        _logger.LogDebug("已从Redis删除过期会话: SessionId={SessionId}", sessionId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "从Redis删除过期会话失败: SessionId={SessionId}", sessionId);
                    }
                    
                    _logger.LogInformation("已清理过期扫描会话: SessionId={SessionId}", sessionId);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期扫描会话时发生错误");
        }
    }

    /// <summary>
    /// 获取所有活跃的扫描会话
    /// </summary>
    /// <returns>活跃的扫描会话列表</returns>
    public List<DeviceScanSession> GetActiveScanSessions()
    {
        return _scanSessions.Values.Where(s => s.Status == ScanStatus.Running).ToList();
    }

    /// <summary>
    /// 获取所有在线设备列表
    /// </summary>
    /// <returns>在线设备列表</returns>
    public async Task<List<object>> GetOnlineDevicesAsync()
    {
        try
        {
            // 从DeviceEventService获取在线设备
            var onlineDevices = _deviceEventService.GetOnlineDevices();
            
            // 同时从数据库获取在线设备信息，确保数据完整性
            var dbOnlineDevices = await _energyDeviceRep.GetListAsync(d => d.IsOnline == true);
            
            // 构建前端兼容的设备列表格式
            var deviceList = new List<object>();
            
            // 添加DeviceEventService中的在线设备
            foreach (var device in onlineDevices)
            {
                var deviceInfo = new
                {
                    id = device.DeviceKey,
                    deviceCode = device.DeviceKey,
                    deviceName = device.DeviceName ?? $"设备_{device.DeviceKey}",
                    deviceType = "智能灯具", // 默认设备类型
                    status = device.IsOnline ? 1 : 2,
                    isOnline = device.IsOnline,
                    location = device.Address ?? "未知位置", // 使用Address属性
                    power = (int?)null,
                    brightness = (int?)null,
                    installDate = (DateTime?)null,
                    updateTime = device.LastSeen,
                    signalStrength = (int?)null,
                    batteryLevel = (int?)null,
                    version = (string)null,
                    discoveredTime = device.LastSeen,
                    parentDeviceId = (long?)null,
                    gatewayId = (long?)null,
                    source = "event_service" // 标识来源为事件服务
                };
                deviceList.Add(deviceInfo);
            }
            
            // 添加数据库中的在线设备（避免重复）
            var eventServiceDeviceKeys = onlineDevices.Select(d => d.DeviceKey).ToHashSet();
            foreach (var dbDevice in dbOnlineDevices)
            {
                if (!eventServiceDeviceKeys.Contains(dbDevice.DeviceCode))
                {
                    var deviceInfo = new
                    {
                        id = dbDevice.Id,
                        deviceCode = dbDevice.DeviceCode,
                        deviceName = dbDevice.DeviceName,
                        deviceType = dbDevice.DeviceType,
                        status = dbDevice.Status,
                        isOnline = dbDevice.IsOnline,
                        location = dbDevice.Location,
                        power = dbDevice.PowerRating,
                        brightness = dbDevice.Brightness,
                        installDate = dbDevice.InstallDate,
                        updateTime = dbDevice.UpdateTime,
                        signalStrength = (int?)null,
                        batteryLevel = (int?)null,
                        version = (string)null,
                        discoveredTime = dbDevice.CreateTime,
                        parentDeviceId = (long?)null,
                        gatewayId = (long?)null,
                        source = "database" // 标识来源为数据库
                    };
                    deviceList.Add(deviceInfo);
                }
            }
            
            _logger.LogInformation("获取在线设备列表成功，共 {Count} 个设备", deviceList.Count);
            return deviceList;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取在线设备列表失败");
            return new List<object>();
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        // 取消订阅事件
        if (_deviceEventService != null)
        {
            _deviceEventService.DeviceBeaconReceived -= OnDeviceBeaconReceived;
        }
        
        // 清理扫描定时器
        foreach (var timer in _scanTimers.Values)
        {
            timer?.Dispose();
        }
        _scanTimers.Clear();
        
        _timeoutTimer?.Dispose();
    }
}

/// <summary>
/// 设备控制请求
/// </summary>
public class DeviceControlRequest
{
    /// <summary>
    /// 请求ID
    /// </summary>
    public string RequestId { get; set; }

    /// <summary>
    /// 请求时间
    /// </summary>
    public DateTime RequestTime { get; set; }

    /// <summary>
    /// 超时时间(秒)
    /// </summary>
    public int TimeoutSeconds { get; set; }

    /// <summary>
    /// 动作类型
    /// </summary>
    public string Action { get; set; }
}

/// <summary>
/// 设备控制结果
/// </summary>
public class DeviceControlResult
{
    /// <summary>
    /// 请求ID
    /// </summary>
    public string RequestId { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public string Status { get; set; }

    /// <summary>
    /// 消息
    /// </summary>
    public string Message { get; set; }

    /// <summary>
    /// 数据
    /// </summary>
    public object Data { get; set; }

    /// <summary>
    /// 请求时间
    /// </summary>
    public DateTime RequestTime { get; set; }

    /// <summary>
    /// 响应时间
    /// </summary>
    public DateTime ResponseTime { get; set; }

    /// <summary>
    /// 持续时间
    /// </summary>
    public TimeSpan Duration { get; set; }
}

/// <summary>
/// 设备扫描会话信息
/// </summary>
public class DeviceScanSession
{
    /// <summary>
    /// 扫描会话ID
    /// </summary>
    public string SessionId { get; set; }
    
    /// <summary>
    /// 网关设备名称
    /// </summary>
    public string GatewayName { get; set; }
    
    /// <summary>
    /// 区域
    /// </summary>
    public string Area { get; set; }
    
    /// <summary>
    /// 地址
    /// </summary>
    public string Address { get; set; }
    
    /// <summary>
    /// 扫描开始时间
    /// </summary>
    public DateTime StartTime { get; set; }
    
    /// <summary>
    /// 扫描结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
    
    /// <summary>
    /// 超时时间（秒）
    /// </summary>
    public int TimeoutSeconds { get; set; }
    
    /// <summary>
    /// 扫描状态
    /// </summary>
    public ScanStatus Status { get; set; } = ScanStatus.Running;
    
    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; }
    
    /// <summary>
    /// 设备数量
    /// </summary>
    public int DeviceCount { get; set; } = 0;
    
    /// <summary>
    /// 最后活动时间
    /// </summary>
    public DateTime LastActivityTime { get; set; }
    
    /// <summary>
    /// 扫描到的设备列表
    /// </summary>
    public List<ScannedDeviceInfo> ScannedDevices { get; set; } = new();
    
    /// <summary>
    /// 是否处于活跃状态
    /// </summary>
    public bool IsActive => Status == ScanStatus.Running;
    
    /// <summary>
    /// 是否已完成
    /// </summary>
    public bool IsCompleted => Status == ScanStatus.Completed || Status == ScanStatus.Timeout;
    
    /// <summary>
    /// 扫描持续时间
    /// </summary>
    public TimeSpan Duration => (EndTime ?? DateTime.Now) - StartTime;
}

/// <summary>
/// 扫描到的设备信息
/// </summary>
public class ScannedDeviceInfo
{
    /// <summary>
    /// 设备唯一标识
    /// </summary>
    public string UniqueId { get; set; }
    
    /// <summary>
    /// 设备类型
    /// </summary>
    public string DeviceType { get; set; }
    
    /// <summary>
    /// 设备名称
    /// </summary>
    public string DeviceName { get; set; }
    
    /// <summary>
    /// 版本信息
    /// </summary>
    public string Version { get; set; }
    
    /// <summary>
    /// 信号强度
    /// </summary>
    public int SignalStrength { get; set; }
    
    /// <summary>
    /// 电池电量
    /// </summary>
    public int BatteryLevel { get; set; }
    
    /// <summary>
    /// 发现时间
    /// </summary>
    public DateTime DiscoveredTime { get; set; }
    
    /// <summary>
    /// 原始beacon数据
    /// </summary>
    public BeaconEventData RawData { get; set; }
}

/// <summary>
/// 扫描状态枚举
/// </summary>
public enum ScanStatus
{
    /// <summary>
    /// 运行中
    /// </summary>
    Running,
    
    /// <summary>
    /// 已完成
    /// </summary>
    Completed,
    
    /// <summary>
    /// 已超时
    /// </summary>
    Timeout,
    
    /// <summary>
    /// 已取消
    /// </summary>
    Cancelled,
    
    /// <summary>
    /// 错误状态
    /// </summary>
    Error
}