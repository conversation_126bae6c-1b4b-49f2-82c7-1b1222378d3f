// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System;
using System.Buffers;
using System.Collections.Concurrent;
using System.Runtime.CompilerServices;
using System.Threading;
using Microsoft.Extensions.Logging;

namespace Admin.NET.Plugin.MQTT.Services;

/// <summary>
/// 内存池管理器实现
/// 提供高效的内存分配和回收机制，支持字节数组、字符数组和内存块的池化管理
/// </summary>
public class MemoryPoolManager : IMemoryPoolManager, IDisposable
{
    #region 私有字段
    
    private readonly ILogger<MemoryPoolManager> _logger;
    private readonly PerformanceConfiguration _configuration;
    
    // 内存池
    private readonly ArrayPool<byte> _byteArrayPool;
    private readonly ArrayPool<char> _charArrayPool;
    private readonly MemoryPool<byte> _memoryPool;
    
    // 统计信息
    private long _totalAllocatedBytes;
    private long _currentUsedBytes;
    private long _poolHits;
    private long _poolMisses;
    
    // 内存使用跟踪
    private readonly ConcurrentDictionary<byte[], int> _rentedByteArrays;
    private readonly ConcurrentDictionary<char[], int> _rentedCharArrays;
    private readonly ConcurrentDictionary<IMemoryOwner<byte>, int> _rentedMemoryOwners;
    
    // 内存池配置
    private readonly int _maxArrayLength;
    private readonly int _maxArraysPerBucket;
    
    private volatile bool _disposed;
    
    #endregion
    
    #region 构造函数
    
    /// <summary>
    /// 构造函数 - 初始化内存池管理器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="configuration">性能配置</param>
    public MemoryPoolManager(ILogger<MemoryPoolManager> logger, PerformanceConfiguration configuration)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        
        _maxArrayLength = _configuration.MemoryPoolMaxArrayLength;
        _maxArraysPerBucket = _configuration.MemoryPoolMaxArraysPerBucket;
        
        // 创建自定义配置的数组池
        _byteArrayPool = ArrayPool<byte>.Create(_maxArrayLength, _maxArraysPerBucket);
        _charArrayPool = ArrayPool<char>.Create(_maxArrayLength, _maxArraysPerBucket);
        _memoryPool = MemoryPool<byte>.Shared;
        
        // 初始化跟踪字典
        _rentedByteArrays = new ConcurrentDictionary<byte[], int>();
        _rentedCharArrays = new ConcurrentDictionary<char[], int>();
        _rentedMemoryOwners = new ConcurrentDictionary<IMemoryOwner<byte>, int>();
        
        _logger.LogInformation("内存池管理器已初始化，最大数组长度: {MaxLength}，每桶最大数组数: {MaxArrays}", 
            _maxArrayLength, _maxArraysPerBucket);
    }
    
    #endregion
    
    #region 公共方法
    
    /// <summary>
    /// 租用字节数组
    /// </summary>
    /// <param name="minimumLength">最小长度</param>
    /// <returns>租用的字节数组</returns>
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public byte[] RentByteArray(int minimumLength)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(MemoryPoolManager));
        
        if (minimumLength < 0)
            throw new ArgumentOutOfRangeException(nameof(minimumLength), "最小长度不能为负数");
        
        try
        {
            var array = _byteArrayPool.Rent(minimumLength);
            
            // 更新统计信息
            Interlocked.Add(ref _totalAllocatedBytes, array.Length);
            Interlocked.Add(ref _currentUsedBytes, array.Length);
            Interlocked.Increment(ref _poolHits);
            
            // 跟踪租用的数组
            _rentedByteArrays.TryAdd(array, array.Length);
            
            _logger.LogTrace("租用字节数组，请求长度: {RequestedLength}，实际长度: {ActualLength}", 
                minimumLength, array.Length);
            
            return array;
        }
        catch (Exception ex)
        {
            Interlocked.Increment(ref _poolMisses);
            _logger.LogError(ex, "租用字节数组失败，请求长度: {Length}", minimumLength);
            throw;
        }
    }
    
    /// <summary>
    /// 归还字节数组
    /// </summary>
    /// <param name="array">要归还的数组</param>
    /// <param name="clearArray">是否清空数组</param>
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public void ReturnByteArray(byte[] array, bool clearArray = false)
    {
        if (_disposed)
            return;
        
        if (array == null)
            return;
        
        try
        {
            // 从跟踪字典中移除
            if (_rentedByteArrays.TryRemove(array, out var length))
            {
                Interlocked.Add(ref _currentUsedBytes, -length);
            }
            
            _byteArrayPool.Return(array, clearArray);
            
            _logger.LogTrace("归还字节数组，长度: {Length}，清空: {Clear}", array.Length, clearArray);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "归还字节数组失败，长度: {Length}", array.Length);
        }
    }
    
    /// <summary>
    /// 租用字符数组
    /// </summary>
    /// <param name="minimumLength">最小长度</param>
    /// <returns>租用的字符数组</returns>
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public char[] RentCharArray(int minimumLength)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(MemoryPoolManager));
        
        if (minimumLength < 0)
            throw new ArgumentOutOfRangeException(nameof(minimumLength), "最小长度不能为负数");
        
        try
        {
            var array = _charArrayPool.Rent(minimumLength);
            
            // 更新统计信息（字符占用2字节）
            var bytes = array.Length * sizeof(char);
            Interlocked.Add(ref _totalAllocatedBytes, bytes);
            Interlocked.Add(ref _currentUsedBytes, bytes);
            Interlocked.Increment(ref _poolHits);
            
            // 跟踪租用的数组
            _rentedCharArrays.TryAdd(array, bytes);
            
            _logger.LogTrace("租用字符数组，请求长度: {RequestedLength}，实际长度: {ActualLength}", 
                minimumLength, array.Length);
            
            return array;
        }
        catch (Exception ex)
        {
            Interlocked.Increment(ref _poolMisses);
            _logger.LogError(ex, "租用字符数组失败，请求长度: {Length}", minimumLength);
            throw;
        }
    }
    
    /// <summary>
    /// 归还字符数组
    /// </summary>
    /// <param name="array">要归还的数组</param>
    /// <param name="clearArray">是否清空数组</param>
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public void ReturnCharArray(char[] array, bool clearArray = false)
    {
        if (_disposed)
            return;
        
        if (array == null)
            return;
        
        try
        {
            // 从跟踪字典中移除
            if (_rentedCharArrays.TryRemove(array, out var bytes))
            {
                Interlocked.Add(ref _currentUsedBytes, -bytes);
            }
            
            _charArrayPool.Return(array, clearArray);
            
            _logger.LogTrace("归还字符数组，长度: {Length}，清空: {Clear}", array.Length, clearArray);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "归还字符数组失败，长度: {Length}", array.Length);
        }
    }
    
    /// <summary>
    /// 租用内存块
    /// </summary>
    /// <param name="minimumLength">最小长度</param>
    /// <returns>租用的内存块</returns>
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public IMemoryOwner<byte> RentMemory(int minimumLength)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(MemoryPoolManager));
        
        if (minimumLength < 0)
            throw new ArgumentOutOfRangeException(nameof(minimumLength), "最小长度不能为负数");
        
        try
        {
            var memoryOwner = _memoryPool.Rent(minimumLength);
            
            // 更新统计信息
            var actualLength = memoryOwner.Memory.Length;
            Interlocked.Add(ref _totalAllocatedBytes, actualLength);
            Interlocked.Add(ref _currentUsedBytes, actualLength);
            Interlocked.Increment(ref _poolHits);
            
            // 跟踪租用的内存块
            _rentedMemoryOwners.TryAdd(memoryOwner, actualLength);
            
            _logger.LogTrace("租用内存块，请求长度: {RequestedLength}，实际长度: {ActualLength}", 
                minimumLength, actualLength);
            
            return new TrackedMemoryOwner(memoryOwner, this);
        }
        catch (Exception ex)
        {
            Interlocked.Increment(ref _poolMisses);
            _logger.LogError(ex, "租用内存块失败，请求长度: {Length}", minimumLength);
            throw;
        }
    }
    
    /// <summary>
    /// 获取内存池统计信息
    /// </summary>
    /// <returns>内存池统计信息</returns>
    public MemoryPoolStatistics GetStatistics()
    {
        return new MemoryPoolStatistics
        {
            TotalAllocatedBytes = Interlocked.Read(ref _totalAllocatedBytes),
            CurrentUsedBytes = Interlocked.Read(ref _currentUsedBytes),
            PoolHits = Interlocked.Read(ref _poolHits),
            PoolMisses = Interlocked.Read(ref _poolMisses)
        };
    }
    
    #endregion
    
    #region 内部方法
    
    /// <summary>
    /// 内部方法 - 归还内存块
    /// </summary>
    /// <param name="memoryOwner">要归还的内存块</param>
    internal void ReturnMemory(IMemoryOwner<byte> memoryOwner)
    {
        if (_disposed || memoryOwner == null)
            return;
        
        try
        {
            // 从跟踪字典中移除
            if (_rentedMemoryOwners.TryRemove(memoryOwner, out var length))
            {
                Interlocked.Add(ref _currentUsedBytes, -length);
            }
            
            memoryOwner.Dispose();
            
            _logger.LogTrace("归还内存块，长度: {Length}", length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "归还内存块失败");
        }
    }
    
    #endregion
    
    #region IDisposable实现
    
    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;
            
            // 清理所有租用的资源
            foreach (var kvp in _rentedByteArrays)
            {
                try
                {
                    _byteArrayPool.Return(kvp.Key, true);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "释放字节数组失败");
                }
            }
            
            foreach (var kvp in _rentedCharArrays)
            {
                try
                {
                    _charArrayPool.Return(kvp.Key, true);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "释放字符数组失败");
                }
            }
            
            foreach (var kvp in _rentedMemoryOwners)
            {
                try
                {
                    kvp.Key.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "释放内存块失败");
                }
            }
            
            // 清空跟踪字典
            _rentedByteArrays.Clear();
            _rentedCharArrays.Clear();
            _rentedMemoryOwners.Clear();
            
            _logger.LogInformation("内存池管理器已释放资源，总分配: {TotalAllocated:N0} 字节", 
                Interlocked.Read(ref _totalAllocatedBytes));
        }
    }
    
    #endregion
}

/// <summary>
/// 跟踪的内存拥有者
/// 包装原始内存拥有者，在释放时自动归还到池中
/// </summary>
internal class TrackedMemoryOwner : IMemoryOwner<byte>
{
    private readonly IMemoryOwner<byte> _innerMemoryOwner;
    private readonly MemoryPoolManager _poolManager;
    private volatile bool _disposed;
    
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="innerMemoryOwner">内部内存拥有者</param>
    /// <param name="poolManager">池管理器</param>
    public TrackedMemoryOwner(IMemoryOwner<byte> innerMemoryOwner, MemoryPoolManager poolManager)
    {
        _innerMemoryOwner = innerMemoryOwner ?? throw new ArgumentNullException(nameof(innerMemoryOwner));
        _poolManager = poolManager ?? throw new ArgumentNullException(nameof(poolManager));
    }
    
    /// <summary>
    /// 内存块
    /// </summary>
    public Memory<byte> Memory => _innerMemoryOwner.Memory;
    
    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;
            _poolManager.ReturnMemory(_innerMemoryOwner);
        }
    }
}

/// <summary>
/// 性能配置
/// 定义内存池和性能优化的相关配置
/// </summary>
public class PerformanceConfiguration
{
    /// <summary>
    /// 内存池最大数组长度
    /// </summary>
    public int MemoryPoolMaxArrayLength { get; set; } = 1024 * 1024; // 1MB
    
    /// <summary>
    /// 内存池每桶最大数组数
    /// </summary>
    public int MemoryPoolMaxArraysPerBucket { get; set; } = 50;
    
    /// <summary>
    /// 内存阈值（MB）
    /// </summary>
    public int MemoryThreshold { get; set; } = 512;
    
    /// <summary>
    /// GC优化间隔（分钟）
    /// </summary>
    public int GcOptimizationInterval { get; set; } = 30;
    
    /// <summary>
    /// 内存压缩间隔（分钟）
    /// </summary>
    public int MemoryCompactionInterval { get; set; } = 60;
    
    /// <summary>
    /// 性能报告间隔（分钟）
    /// </summary>
    public int PerformanceReportInterval { get; set; } = 5;
    
    /// <summary>
    /// 对象池命中率阈值
    /// </summary>
    public double ObjectPoolHitRateThreshold { get; set; } = 0.8;
    
    /// <summary>
    /// 任务队列长度阈值
    /// </summary>
    public int TaskQueueLengthThreshold { get; set; } = 1000;
    
    /// <summary>
    /// 是否启用内存池优化
    /// </summary>
    public bool EnableMemoryPoolOptimization { get; set; } = true;
    
    /// <summary>
    /// 是否启用对象池优化
    /// </summary>
    public bool EnableObjectPoolOptimization { get; set; } = true;
    
    /// <summary>
    /// 是否启用异步任务优化
    /// </summary>
    public bool EnableAsyncTaskOptimization { get; set; } = true;
    
    /// <summary>
    /// 是否启用性能监控
    /// </summary>
    public bool EnablePerformanceMonitoring { get; set; } = true;
    
    /// <summary>
    /// 最大并发任务数
    /// </summary>
    public int MaxConcurrentTasks { get; set; } = Environment.ProcessorCount * 2;
    
    /// <summary>
    /// 任务超时时间（毫秒）
    /// </summary>
    public int TaskTimeoutMs { get; set; } = 30000;
    
    /// <summary>
    /// 高优先级任务队列容量
    /// </summary>
    public int HighPriorityQueueCapacity { get; set; } = 1000;
    
    /// <summary>
    /// 普通优先级任务队列容量
    /// </summary>
    public int NormalPriorityQueueCapacity { get; set; } = 5000;
    
    /// <summary>
    /// 低优先级任务队列容量
    /// </summary>
    public int LowPriorityQueueCapacity { get; set; } = 10000;
    
    /// <summary>
    /// 对象池最大大小
    /// </summary>
    public int ObjectPoolMaxSize { get; set; } = 100;
    
    /// <summary>
    /// 对象池清理间隔（分钟）
    /// </summary>
    public int ObjectPoolCleanupInterval { get; set; } = 15;
    
    /// <summary>
    /// 性能指标保留时间（小时）
    /// </summary>
    public int PerformanceMetricsRetentionHours { get; set; } = 24;
}