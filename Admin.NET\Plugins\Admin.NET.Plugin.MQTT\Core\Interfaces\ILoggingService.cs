using Microsoft.Extensions.Logging;
using Admin.NET.Plugin.MQTT.Core.Models;

namespace Admin.NET.Plugin.MQTT.Core.Interfaces
{
    /// <summary>
    /// 日志服务接口
    /// 提供结构化日志记录、性能监控、日志聚合、异步写入等功能
    /// </summary>
    public interface ILoggingService
    {
        #region 属性

        /// <summary>
        /// 总日志条目数
        /// </summary>
        long TotalLogEntries { get; }

        /// <summary>
        /// 总错误数
        /// </summary>
        long TotalErrors { get; }

        /// <summary>
        /// 总警告数
        /// </summary>
        long TotalWarnings { get; }

        /// <summary>
        /// 队列中的日志条目数
        /// </summary>
        int QueuedLogEntries { get; }

        /// <summary>
        /// 是否正在处理日志
        /// </summary>
        bool IsProcessing { get; }

        #endregion

        #region 事件

        /// <summary>
        /// 日志条目写入事件
        /// </summary>
        event EventHandler<LogEntryWrittenEventArgs> LogEntryWritten;

        /// <summary>
        /// 日志错误事件
        /// </summary>
        event EventHandler<LogErrorEventArgs> LogError;

        /// <summary>
        /// 性能警告事件
        /// </summary>
        event EventHandler<PerformanceWarningEventArgs> PerformanceWarning;

        /// <summary>
        /// 日志统计更新事件
        /// </summary>
        event EventHandler<LogStatisticsUpdatedEventArgs> StatisticsUpdated;

        #endregion

        #region 方法

        /// <summary>
        /// 记录日志
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        /// <param name="properties">附加属性</param>
        /// <param name="category">日志分类</param>
        /// <returns>记录任务</returns>
        Task LogAsync(Microsoft.Extensions.Logging.LogLevel level, string message, Exception exception = null,
            Dictionary<string, object> properties = null, string category = null);

        /// <summary>
        /// 记录性能指标
        /// </summary>
        /// <param name="name">指标名称</param>
        /// <param name="value">指标值</param>
        /// <param name="unit">单位</param>
        /// <param name="properties">附加属性</param>
        /// <returns>记录任务</returns>
        Task LogPerformanceAsync(string name, double value, string unit = null, 
            Dictionary<string, object> properties = null);

        /// <summary>
        /// 开始性能计时
        /// </summary>
        /// <param name="name">计时器名称</param>
        /// <param name="properties">附加属性</param>
        /// <returns>性能计时器</returns>
        IPerformanceTimer StartTimer(string name, Dictionary<string, object> properties = null);

        /// <summary>
        /// 获取日志统计信息
        /// </summary>
        /// <param name="category">日志分类</param>
        /// <returns>统计信息</returns>
        LogStatisticsInfo GetStatistics(string category = null);

        /// <summary>
        /// 获取性能统计信息
        /// </summary>
        /// <param name="name">性能指标名称</param>
        /// <returns>性能统计信息</returns>
        PerformanceStatisticsInfo GetPerformanceStatistics(string name = null);

        /// <summary>
        /// 搜索日志
        /// </summary>
        /// <param name="criteria">搜索条件</param>
        /// <returns>搜索结果</returns>
        Task<LogSearchResult> SearchLogsAsync(LogSearchCriteria criteria);

        /// <summary>
        /// 导出日志
        /// </summary>
        /// <param name="criteria">导出条件</param>
        /// <param name="format">导出格式</param>
        /// <param name="filePath">文件路径</param>
        /// <returns>导出任务</returns>
        Task<LogExportResult> ExportLogsAsync(LogSearchCriteria criteria, LogExportFormat format, string filePath);

        /// <summary>
        /// 清理旧日志
        /// </summary>
        /// <param name="retentionDays">保留天数</param>
        /// <returns>清理结果</returns>
        Task<LogCleanupResult> CleanupOldLogsAsync(int retentionDays = 30);

        /// <summary>
        /// 刷新日志到文件
        /// </summary>
        /// <returns>刷新任务</returns>
        Task FlushAsync();

        #endregion
    }

    /// <summary>
    /// 性能计时器接口
    /// </summary>
    public interface IPerformanceTimer : IDisposable
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        string OperationName { get; }

        /// <summary>
        /// 开始时间
        /// </summary>
        DateTime StartTime { get; }

        /// <summary>
        /// 已经过时间
        /// </summary>
        TimeSpan Elapsed { get; }

        /// <summary>
        /// 停止计时并记录结果
        /// </summary>
        void Stop();

        /// <summary>
        /// 停止计时并记录性能指标
        /// </summary>
        /// <returns>停止任务</returns>
        Task StopAsync();
    }
}
