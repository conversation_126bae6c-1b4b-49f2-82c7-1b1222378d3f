/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { EnergyFaultOutput } from './energy-fault-output';
/**
 * 设备故障输出参数
 * @export
 * @interface EnergyFaultOutput
 */
export interface EnergyFaultOutput {
    /**
     * 主键ID
     * @type {number}
     * @memberof EnergyFaultOutput
     */
    id?: number;
    /**
     * 设备ID
     * @type {number}
     * @memberof EnergyFaultOutput
     */
    deviceId?: number;
    /**
     * 设备编码
     * @type {string}
     * @memberof EnergyFaultOutput
     */
    deviceCode?: string | null;
    /**
     * 故障编码
     * @type {string}
     * @memberof EnergyFaultOutput
     */
    faultCode?: string | null;
    /**
     * 设备名称
     * @type {string}
     * @memberof EnergyFaultOutput
     */
    deviceName?: string | null;
    /**
     * 故障类型
     * @type {string}
     * @memberof EnergyFaultOutput
     */
    faultType?: string | null;
    /**
     * 故障等级
     * @type {number}
     * @memberof EnergyFaultOutput
     */
    faultLevel?: number;
    /**
     * 故障描述
     * @type {string}
     * @memberof EnergyFaultOutput
     */
    faultDescription?: string | null;
    /**
     * 故障时间
     * @type {Date}
     * @memberof EnergyFaultOutput
     */
    faultTime?: Date;
    /**
     * 故障状态
     * @type {number}
     * @memberof EnergyFaultOutput
     */
    faultStatus?: number;
    /**
     * 维修人员
     * @type {string}
     * @memberof EnergyFaultOutput
     */
    repairPerson?: string | null;
    /**
     * 维修时间
     * @type {Date}
     * @memberof EnergyFaultOutput
     */
    repairTime?: Date | null;
    /**
     * 维修描述
     * @type {string}
     * @memberof EnergyFaultOutput
     */
    repairDescription?: string | null;
    /**
     * 维修费用
     * @type {number}
     * @memberof EnergyFaultOutput
     */
    repairCost?: number | null;
    /**
     * 解决时间
     * @type {Date}
     * @memberof EnergyFaultOutput
     */
    resolveTime?: Date | null;
    /**
     * 解决方案
     * @type {string}
     * @memberof EnergyFaultOutput
     */
    solution?: string | null;
    /**
     * 备注
     * @type {string}
     * @memberof EnergyFaultOutput
     */
    remark?: string | null;
    /**
     * 创建时间
     * @type {Date}
     * @memberof EnergyFaultOutput
     */
    createTime?: Date;
    /**
     * 更新时间
     * @type {Date}
     * @memberof EnergyFaultOutput
     */
    updateTime?: Date | null;
    /**
     * 历史故障记录
     * @type {Array<EnergyFaultOutput>}
     * @memberof EnergyFaultOutput
     */
    historyFaults?: Array<EnergyFaultOutput> | null;
}
