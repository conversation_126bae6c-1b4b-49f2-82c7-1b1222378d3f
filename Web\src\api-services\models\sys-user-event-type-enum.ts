/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 事件类型-系统用户操作枚举<br />&nbsp;增加用户 Add = 111<br />&nbsp;更新用户 Update = 222<br />&nbsp;授权用户角色 UpdateRole = 333<br />&nbsp;删除用户 Delete = 444<br />&nbsp;设置用户状态 SetStatus = 555<br />&nbsp;修改密码 ChangePwd = 666<br />&nbsp;重置密码 ResetPwd = 777<br />&nbsp;解除登录锁定 UnlockLogin = 888<br />&nbsp;注册用户 Register = 999<br />&nbsp;用户登录 Login = 1000<br />&nbsp;用户退出 LoginOut = 1001<br />&nbsp;刷新Token RefreshToken = 1002<br />&nbsp;MQTT登录 MqttLogin = 1003<br />
 * @export
 * @enum {string}
 */
export enum SysUserEventTypeEnum {
    NUMBER_111 = 111,
    NUMBER_222 = 222,
    NUMBER_333 = 333,
    NUMBER_444 = 444,
    NUMBER_555 = 555,
    NUMBER_666 = 666,
    NUMBER_777 = 777,
    NUMBER_888 = 888,
    NUMBER_999 = 999,
    NUMBER_1000 = 1000,
    NUMBER_1001 = 1001,
    NUMBER_1002 = 1002,
    NUMBER_1003 = 1003
}

