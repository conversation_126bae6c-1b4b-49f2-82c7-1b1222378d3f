/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { EnergySceneDeviceOutput } from './energy-scene-device-output';
import { SceneExecuteHistoryItem } from './scene-execute-history-item';
import { SceneStatInfo } from './scene-stat-info';
/**
 * 场景详情输出参数
 * @export
 * @interface EnergySceneDetailOutput
 */
export interface EnergySceneDetailOutput {
    /**
     * 主键ID
     * @type {number}
     * @memberof EnergySceneDetailOutput
     */
    id?: number;
    /**
     * 场景编码
     * @type {string}
     * @memberof EnergySceneDetailOutput
     */
    sceneCode?: string | null;
    /**
     * 场景名称
     * @type {string}
     * @memberof EnergySceneDetailOutput
     */
    sceneName?: string | null;
    /**
     * 场景类型：1手动场景 2定时场景 3感应场景 4联动场景
     * @type {number}
     * @memberof EnergySceneDetailOutput
     */
    sceneType?: number;
    /**
     * 场景描述
     * @type {string}
     * @memberof EnergySceneDetailOutput
     */
    description?: string | null;
    /**
     * 场景图标
     * @type {string}
     * @memberof EnergySceneDetailOutput
     */
    icon?: string | null;
    /**
     * 执行次数
     * @type {number}
     * @memberof EnergySceneDetailOutput
     */
    executeCount?: number;
    /**
     * 最后执行时间
     * @type {Date}
     * @memberof EnergySceneDetailOutput
     */
    lastExecuteTime?: Date | null;
    /**
     * 平均执行时长(秒)
     * @type {number}
     * @memberof EnergySceneDetailOutput
     */
    avgExecuteTime?: number;
    /**
     * 成功率
     * @type {number}
     * @memberof EnergySceneDetailOutput
     */
    successRate?: number | null;
    /**
     * 排序
     * @type {number}
     * @memberof EnergySceneDetailOutput
     */
    sort?: number;
    /**
     * 状态
     * @type {number}
     * @memberof EnergySceneDetailOutput
     */
    status?: number;
    /**
     * 状态名称
     * @type {string}
     * @memberof EnergySceneDetailOutput
     */
    statusName?: string | null;
    /**
     * 创建时间
     * @type {Date}
     * @memberof EnergySceneDetailOutput
     */
    createTime?: Date;
    /**
     * 更新时间
     * @type {Date}
     * @memberof EnergySceneDetailOutput
     */
    updateTime?: Date | null;
    /**
     * 触发条件(JSON格式)
     * @type {string}
     * @memberof EnergySceneDetailOutput
     */
    triggerCondition?: string | null;
    /**
     * 执行动作(JSON格式)
     * @type {string}
     * @memberof EnergySceneDetailOutput
     */
    executeActions?: string | null;
    /**
     * 定时表达式
     * @type {string}
     * @memberof EnergySceneDetailOutput
     */
    cronExpression?: string | null;
    /**
     * 场景设备列表
     * @type {Array<EnergySceneDeviceOutput>}
     * @memberof EnergySceneDetailOutput
     */
    devices?: Array<EnergySceneDeviceOutput> | null;
    /**
     * 场景执行历史
     * @type {Array<SceneExecuteHistoryItem>}
     * @memberof EnergySceneDetailOutput
     */
    executeHistory?: Array<SceneExecuteHistoryItem> | null;
    /**
     * 
     * @type {SceneStatInfo}
     * @memberof EnergySceneDetailOutput
     */
    statInfo?: SceneStatInfo;
    /**
     * 关联设备列表
     * @type {Array<EnergySceneDeviceOutput>}
     * @memberof EnergySceneDetailOutput
     */
    deviceList?: Array<EnergySceneDeviceOutput> | null;
    /**
     * 最近执行记录
     * @type {Array<any>}
     * @memberof EnergySceneDetailOutput
     */
    recentExecuteRecords?: Array<any> | null;
    /**
     * 今日执行次数
     * @type {number}
     * @memberof EnergySceneDetailOutput
     */
    todayExecuteCount?: number;
    /**
     * 本月执行次数
     * @type {number}
     * @memberof EnergySceneDetailOutput
     */
    thisMonthExecuteCount?: number;
}
