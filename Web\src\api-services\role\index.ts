import { service as request } from '/@/utils/request';
import { useBaseApi } from '../base';

/**
 * 角色管理API接口集合
 * @method getPage 获取角色分页列表
 * @method getDetail 获取角色详情
 * @method add 添加角色
 * @method update 更新角色
 * @method delete 删除角色
 * @method getList 获取角色列表
 * @method grantMenu 授权菜单
 * @method grantData 授权数据
 * @method setStatus 设置状态
 */
export function useRoleApi() {
	const baseApi = useBaseApi('sysRole');
	
	return {
		...baseApi,
		// 获取角色列表
		getList: (name?: string, code?: string, status?: number) => {
			return request({
				url: '/api/sysRole/list',
				method: 'get',
				params: { name, code, status },
			});
		},
		// 授权菜单
		grantMenu: (data: any) => {
			return request({
				url: '/api/sysRole/grantMenu',
				method: 'post',
				data,
			});
		},
		// 授权数据
		grantData: (data: any) => {
			return request({
				url: '/api/sysRole/grantData',
				method: 'post',
				data,
			});
		},
		// 设置状态
		setStatus: (data: any) => {
			return request({
				url: '/api/sysRole/setStatus',
				method: 'post',
				data,
			});
		},
		// 获取角色拥有的菜单列表
		getOwnMenuList: (roleId: number) => {
			return request({
				url: '/api/sysRole/ownMenuList',
				method: 'get',
				params: { roleId },
			});
		},
		// 获取角色拥有的组织列表
		getOwnOrgList: (roleId: number) => {
			return request({
				url: '/api/sysRole/ownOrgList',
				method: 'get',
				params: { roleId },
			});
		},
		// 授权数据范围
		grantDataScope: (data: any) => {
			return request({
				url: '/api/sysRole/grantDataScope',
				method: 'post',
				data,
			});
		},
	};
}