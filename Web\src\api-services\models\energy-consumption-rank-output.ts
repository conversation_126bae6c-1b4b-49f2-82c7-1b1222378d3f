/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 能耗排行输出参数
 * @export
 * @interface EnergyConsumptionRankOutput
 */
export interface EnergyConsumptionRankOutput {
    /**
     * 设备ID
     * @type {number}
     * @memberof EnergyConsumptionRankOutput
     */
    deviceId?: number;
    /**
     * 设备名称
     * @type {string}
     * @memberof EnergyConsumptionRankOutput
     */
    deviceName?: string | null;
    /**
     * 设备编号
     * @type {string}
     * @memberof EnergyConsumptionRankOutput
     */
    deviceCode?: string | null;
    /**
     * 设备类型
     * @type {string}
     * @memberof EnergyConsumptionRankOutput
     */
    deviceType?: string | null;
    /**
     * 分组名称
     * @type {string}
     * @memberof EnergyConsumptionRankOutput
     */
    groupName?: string | null;
    /**
     * 排名
     * @type {number}
     * @memberof EnergyConsumptionRankOutput
     */
    rank?: number;
    /**
     * 能耗值(kWh)
     * @type {number}
     * @memberof EnergyConsumptionRankOutput
     */
    energyValue?: number;
    /**
     * 占比(%)
     * @type {number}
     * @memberof EnergyConsumptionRankOutput
     */
    percentage?: number;
    /**
     * 统计时间段
     * @type {string}
     * @memberof EnergyConsumptionRankOutput
     */
    timePeriod?: string | null;
    /**
     * 同比增长率(%)
     * @type {number}
     * @memberof EnergyConsumptionRankOutput
     */
    growthRate?: number | null;
    /**
     * 平均功率(W)
     * @type {number}
     * @memberof EnergyConsumptionRankOutput
     */
    avgPower?: number | null;
    /**
     * 运行时长(小时)
     * @type {number}
     * @memberof EnergyConsumptionRankOutput
     */
    runningHours?: number | null;
    /**
     * 设备位置
     * @type {string}
     * @memberof EnergyConsumptionRankOutput
     */
    deviceLocation?: string | null;
    /**
     * 总能耗(kWh)
     * @type {number}
     * @memberof EnergyConsumptionRankOutput
     */
    totalEnergyConsumption?: number;
    /**
     * 平均功率(W) - 别名
     * @type {number}
     * @memberof EnergyConsumptionRankOutput
     */
    averagePower?: number | null;
    /**
     * 运行时长 - 别名
     * @type {number}
     * @memberof EnergyConsumptionRankOutput
     */
    runningTime?: number | null;
    /**
     * 总费用(元)
     * @type {number}
     * @memberof EnergyConsumptionRankOutput
     */
    totalCost?: number;
}
