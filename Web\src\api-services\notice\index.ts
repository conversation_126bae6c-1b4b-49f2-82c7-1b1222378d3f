import { service as request } from '/@/utils/request';
import { useBaseApi } from '../base';

/**
 * 通知管理API接口集合
 * @method getPage 获取通知分页列表
 * @method getDetail 获取通知详情
 * @method add 添加通知
 * @method update 更新通知
 * @method delete 删除通知
 * @method getList 获取通知列表
 * @method setStatus 设置状态
 * @method getReceivedList 获取接收的通知列表
 * @method setRead 设置已读
 */
export function useNoticeApi() {
	const baseApi = useBaseApi('sysNotice');
	
	return {
		...baseApi,
		// 获取通知列表
		getList: (title?: string, type?: number, status?: number) => {
			return request({
				url: '/api/sysNotice/list',
				method: 'get',
				params: { title, type, status },
			});
		},
		// 设置状态
		setStatus: (data: any) => {
			return request({
				url: '/api/sysNotice/setStatus',
				method: 'post',
				data,
			});
		},
		// 获取接收的通知列表
		getReceivedList: (page?: number, pageSize?: number) => {
			return request({
				url: '/api/sysNotice/received',
				method: 'get',
				params: { page, pageSize },
			});
		},
		// 设置已读
		setRead: (noticeId: number) => {
			return request({
				url: '/api/sysNotice/setRead',
				method: 'post',
				data: { noticeId },
			});
		},
		// 获取未读通知列表
		getUnReadList: () => {
			return request({
				url: '/api/sysNotice/unReadList',
				method: 'get',
			});
		},
	};
}