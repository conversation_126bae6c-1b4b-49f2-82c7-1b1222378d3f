<template>
	<view class="fault-container">
		<!-- 故障统计概览 -->
		<view class="fault-overview-card">
			<view class="card-header">
				<view class="card-title">故障概览</view>
				<view class="refresh-btn" @tap="refreshData">
					<text class="refresh-icon">🔄</text>
					<text>刷新</text>
				</view>
			</view>
			
			<view class="overview-stats">
				<view class="stat-card" v-for="stat in overviewStats" :key="stat.key" :class="stat.className">
					<view class="stat-icon">
						<text>{{ stat.icon }}</text>
					</view>
					<view class="stat-content">
						<view class="stat-number">{{ stat.value }}</view>
						<view class="stat-label">{{ stat.label }}</view>
						<view class="stat-trend" :class="stat.trendClass" v-if="stat.trend">
							{{ stat.trend }}
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 筛选和搜索 -->
		<view class="filter-section">
			<view class="search-box">
				<input 
					class="search-input" 
					placeholder="搜索设备名称或故障描述"
					v-model="searchKeyword"
					@input="handleSearch"
				/>
				<text class="search-icon">🔍</text>
			</view>
			
			<view class="filter-tabs">
				<text 
					v-for="tab in filterTabs" 
					:key="tab.key"
					:class="{ active: currentFilter === tab.key }"
					@tap="switchFilter(tab.key)"
				>
					{{ tab.label }}
					<text class="tab-count" v-if="tab.count > 0">({{ tab.count }})</text>
				</text>
			</view>
		</view>
		
		<!-- 故障列表 -->
		<view class="fault-list-card">
			<view class="card-header">
				<view class="card-title">故障列表</view>
				<view class="list-actions">
					<text class="action-btn" @tap="showFilterModal">
						<text class="btn-icon">⚙️</text>
						<text>筛选</text>
					</text>
					<text class="action-btn" @tap="exportFaults">
						<text class="btn-icon">📤</text>
						<text>导出</text>
					</text>
				</view>
			</view>
			
			<view class="fault-list" v-if="filteredFaults.length > 0">
				<view class="fault-item" v-for="fault in filteredFaults" :key="fault.id" @tap="viewFaultDetail(fault)">
					<view class="fault-header">
						<view class="fault-level" :class="getFaultLevelClass(fault.level)">
							{{ getFaultLevelText(fault.level) }}
						</view>
						<view class="fault-status" :class="getFaultStatusClass(fault.status)">
							{{ getFaultStatusText(fault.status) }}
						</view>
						<view class="fault-time">{{ formatTime(fault.createTime) }}</view>
					</view>
					
					<view class="fault-content">
						<view class="fault-title">{{ fault.title }}</view>
						<view class="fault-device">
							<text class="device-icon">💡</text>
							<text class="device-name">{{ fault.deviceName }}</text>
							<text class="device-location">{{ fault.location }}</text>
						</view>
						<view class="fault-desc">{{ fault.description }}</view>
					</view>
					
					<view class="fault-actions">
						<text class="action-btn primary" @tap.stop="handleFault(fault)" v-if="fault.status === 'pending'">
							处理
						</text>
						<text class="action-btn" @tap.stop="viewFaultDetail(fault)">
							详情
						</text>
						<text class="action-btn" @tap.stop="assignFault(fault)" v-if="fault.status === 'pending'">
							分配
						</text>
					</view>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view class="empty-state" v-else>
				<text class="empty-icon">📋</text>
				<text class="empty-text">暂无故障记录</text>
				<text class="empty-desc">系统运行正常，没有发现故障</text>
			</view>
		</view>
		
		<!-- 维修记录 -->
		<view class="repair-records-card">
			<view class="card-header">
				<view class="card-title">最近维修记录</view>
				<view class="view-more-btn" @tap="viewAllRepairs">
					<text>查看全部</text>
				</view>
			</view>
			
			<view class="repair-list" v-if="repairRecords.length > 0">
				<view class="repair-item" v-for="repair in repairRecords.slice(0, 3)" :key="repair.id">
					<view class="repair-avatar">
						<text>{{ repair.technician.charAt(0) }}</text>
					</view>
					<view class="repair-content">
						<view class="repair-header">
							<view class="repair-title">{{ repair.title }}</view>
							<view class="repair-time">{{ formatTime(repair.completeTime) }}</view>
						</view>
						<view class="repair-info">
							<text class="repair-device">设备: {{ repair.deviceName }}</text>
							<text class="repair-technician">维修员: {{ repair.technician }}</text>
						</view>
						<view class="repair-desc">{{ repair.description }}</view>
						<view class="repair-result" :class="getRepairResultClass(repair.result)">
							{{ getRepairResultText(repair.result) }}
						</view>
					</view>
				</view>
			</view>
			
			<view class="empty-state" v-else>
				<text class="empty-icon">🔧</text>
				<text class="empty-text">暂无维修记录</text>
			</view>
		</view>
		
		<!-- 故障统计图表 -->
		<view class="fault-chart-card">
			<view class="card-header">
				<view class="card-title">故障趋势</view>
				<view class="chart-period">
					<text 
						v-for="period in chartPeriods" 
						:key="period.key"
						:class="{ active: currentChartPeriod === period.key }"
						@tap="switchChartPeriod(period.key)"
					>
						{{ period.label }}
					</text>
				</view>
			</view>
			
			<!-- 模拟图表 -->
			<view class="chart-container">
				<view class="chart-bars">
					<view 
						class="chart-bar" 
						v-for="(bar, index) in chartData" 
						:key="index"
						:style="{ height: bar.height + '%' }"
					>
						<view class="bar-segments">
							<view 
								class="bar-segment" 
								v-for="(segment, segIndex) in bar.segments" 
								:key="segIndex"
								:style="{ height: segment.percentage + '%', backgroundColor: segment.color }"
							></view>
						</view>
						<view class="bar-value">{{ bar.total }}</view>
					</view>
				</view>
				<view class="chart-labels">
					<text v-for="(label, index) in chartLabels" :key="index">{{ label }}</text>
				</view>
			</view>
			
			<view class="chart-legend">
				<view class="legend-item" v-for="legend in chartLegend" :key="legend.key">
					<view class="legend-color" :style="{ backgroundColor: legend.color }"></view>
					<text class="legend-label">{{ legend.label }}</text>
				</view>
			</view>
		</view>
		
		<!-- 筛选弹窗 -->
		<view class="filter-modal" v-if="showFilter" @tap="hideFilterModal">
			<view class="modal-content" @tap.stop>
				<view class="modal-header">
					<view class="modal-title">筛选条件</view>
					<text class="close-btn" @tap="hideFilterModal">✕</text>
				</view>
				
				<view class="filter-options">
					<view class="filter-group">
						<view class="group-title">故障等级</view>
						<view class="option-list">
							<text 
								v-for="level in faultLevels" 
								:key="level.key"
								:class="{ active: filterOptions.levels.includes(level.key) }"
								@tap="toggleFilterLevel(level.key)"
							>
								{{ level.label }}
							</text>
						</view>
					</view>
					
					<view class="filter-group">
						<view class="group-title">处理状态</view>
						<view class="option-list">
							<text 
								v-for="status in faultStatuses" 
								:key="status.key"
								:class="{ active: filterOptions.statuses.includes(status.key) }"
								@tap="toggleFilterStatus(status.key)"
							>
								{{ status.label }}
							</text>
						</view>
					</view>
					
					<view class="filter-group">
						<view class="group-title">时间范围</view>
						<view class="date-range">
							<input class="date-input" type="date" v-model="filterOptions.startDate" placeholder="开始日期" />
							<text class="date-separator">至</text>
							<input class="date-input" type="date" v-model="filterOptions.endDate" placeholder="结束日期" />
						</view>
					</view>
				</view>
				
				<view class="modal-actions">
					<text class="action-btn secondary" @tap="resetFilter">重置</text>
					<text class="action-btn primary" @tap="applyFilter">应用</text>
				</view>
			</view>
		</view>
		
		<!-- 故障处理弹窗 -->
		<view class="handle-modal" v-if="showHandleModal" @tap="hideHandleModal">
			<view class="modal-content" @tap.stop>
				<view class="modal-header">
					<view class="modal-title">处理故障</view>
					<text class="close-btn" @tap="hideHandleModal">✕</text>
				</view>
				
				<view class="handle-form">
					<view class="form-item">
						<view class="form-label">处理方式</view>
						<view class="handle-options">
							<text 
								v-for="option in handleOptions" 
								:key="option.key"
								:class="{ active: handleForm.type === option.key }"
								@tap="selectHandleType(option.key)"
							>
								{{ option.label }}
							</text>
						</view>
					</view>
					
					<view class="form-item" v-if="handleForm.type === 'assign'">
						<view class="form-label">分配给</view>
						<picker :range="technicians" range-key="name" @change="selectTechnician">
							<view class="picker-input">
								{{ handleForm.technician || '请选择维修员' }}
							</view>
						</picker>
					</view>
					
					<view class="form-item">
						<view class="form-label">处理说明</view>
						<textarea 
							class="form-textarea" 
							placeholder="请输入处理说明或备注"
							v-model="handleForm.remark"
							maxlength="200"
						></textarea>
					</view>
				</view>
				
				<view class="modal-actions">
					<text class="action-btn secondary" @tap="hideHandleModal">取消</text>
					<text class="action-btn primary" @tap="submitHandle">确认处理</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { getFaultList, exportFaultData, handleFault, getFaultChartData } from '@/api/fault.js'

export default {
	data() {
		return {
			loading: false,
		searchKeyword: '',
		currentFilter: 'all',
		currentChartPeriod: 'week',
		showFilter: false,
		showHandleModal: false,
		currentFault: null,
		
		// 概览统计
		overviewStats: [
			{
				key: 'total',
				icon: '⚠️',
				value: 0,
				label: '总故障数',
				trend: '',
				trendClass: '',
				className: 'stat-total'
			},
			{
				key: 'pending',
				icon: '🔴',
				value: 0,
				label: '待处理',
				trend: '',
				trendClass: '',
				className: 'stat-pending'
			},
			{
				key: 'processing',
				icon: '🟡',
				value: 0,
				label: '处理中',
				trend: '',
				trendClass: '',
				className: 'stat-processing'
			},
			{
				key: 'resolved',
				icon: '🟢',
				value: 0,
				label: '已解决',
				trend: '',
				trendClass: '',
				className: 'stat-resolved'
			}
		],
		
		// 筛选标签
		filterTabs: [
			{ key: 'all', label: '全部', count: 0 },
			{ key: 'pending', label: '待处理', count: 0 },
			{ key: 'processing', label: '处理中', count: 0 },
			{ key: 'resolved', label: '已解决', count: 0 }
		],
			
			// 故障列表
		faultList: [],
			
			// 维修记录
			repairRecords: [],
			
			// 图表周期
			chartPeriods: [
				{ key: 'week', label: '本周' },
				{ key: 'month', label: '本月' },
				{ key: 'quarter', label: '本季度' }
			],
			
			// 图表数据
			chartData: [],
			
			// 图表标签
			chartLabels: [],
			
			// 图表图例
			chartLegend: [
				{ key: 'high', label: '高级故障', color: '#ff4d4f' },
				{ key: 'medium', label: '中级故障', color: '#faad14' },
				{ key: 'low', label: '低级故障', color: '#52c41a' }
			],
			
			// 筛选选项
			filterOptions: {
				levels: [],
				statuses: [],
				startDate: '',
				endDate: ''
			},
			
			// 故障等级
			faultLevels: [
				{ key: 'high', label: '高级' },
				{ key: 'medium', label: '中级' },
				{ key: 'low', label: '低级' }
			],
			
			// 故障状态
			faultStatuses: [
				{ key: 'pending', label: '待处理' },
				{ key: 'processing', label: '处理中' },
				{ key: 'resolved', label: '已解决' }
			],
			
			// 处理表单
			handleForm: {
				type: 'resolve',
				technician: '',
				remark: ''
			},
			
			// 处理选项
			handleOptions: [
				{ key: 'resolve', label: '直接解决' },
				{ key: 'assign', label: '分配维修' }
			],
			
			// 维修员列表
			technicians: [
				{ id: 1, name: '王维修' },
				{ id: 2, name: '李维修' },
				{ id: 3, name: '张维修' }
			]
		}
	},
	
	computed: {
		// 筛选后的故障列表
		filteredFaults() {
			let result = this.faultList
			
			// 按状态筛选
			if (this.currentFilter !== 'all') {
				result = result.filter(fault => fault.status === this.currentFilter)
			}
			
			// 按关键词搜索
			if (this.searchKeyword) {
				const keyword = this.searchKeyword.toLowerCase()
				result = result.filter(fault => 
					fault.title.toLowerCase().includes(keyword) ||
					fault.deviceName.toLowerCase().includes(keyword) ||
					fault.description.toLowerCase().includes(keyword)
				)
			}
			
			return result
		}
	},
	
	onLoad() {
		this.loadFaultData()
	},
	
	onShow() {
		// 页面显示时刷新数据
		this.refreshData()
	},
	
	onUnload() {
		// 页面卸载时清理资源
		// 清空数据数组，释放内存
		this.faultList = []
		this.repairRecords = []
		this.overviewStats = []
		this.filterTabs = []
		
		// 重置筛选条件
		this.filterOptions = {
			levels: [],
			statuses: [],
			startDate: '',
			endDate: ''
		}
		
		// 重置表单数据
		this.handleForm = {
			type: 'resolve',
			technician: '',
			remark: ''
		}
		
		// 关闭所有弹窗
		this.showFilter = false
		this.showHandleModal = false
		
		// 清空当前故障引用
		this.currentFault = null
		
		// 隐藏loading状态
		uni.hideLoading()
	},
	
	methods: {
		// 加载故障数据
		async loadFaultData() {
			try {
				uni.showLoading({ title: '加载数据...' })
				
				const response = await getFaultList()
				this.faultList = response.data.faults || []
				this.repairRecords = response.data.repairs || []
				this.overviewStats = response.data.stats || this.overviewStats
				this.updateOverviewStats()
				
				uni.hideLoading()
			} catch (error) {
				console.error('加载故障数据失败:', error)
				uni.hideLoading()
				uni.showToast({
					title: error.message || '数据加载失败',
					icon: 'error'
				})
			}
		},
		
		// 刷新数据
		async refreshData() {
			try {
				await this.loadFaultData()
				uni.showToast({
					title: '刷新成功',
					icon: 'success'
				})
			} catch (error) {
				console.error('刷新失败:', error)
				uni.showToast({
					title: '刷新失败',
					icon: 'error'
				})
			}
		},
		
		// 更新概览统计
		updateOverviewStats() {
			const stats = {
				total: this.faultList.length,
				pending: this.faultList.filter(f => f.status === 'pending').length,
				processing: this.faultList.filter(f => f.status === 'processing').length,
				resolved: this.faultList.filter(f => f.status === 'resolved').length
			}
			
			this.overviewStats.forEach(stat => {
				stat.value = stats[stat.key]
			})
			
			// 更新筛选标签计数
			this.filterTabs.forEach(tab => {
				if (tab.key === 'all') {
					tab.count = stats.total
				} else {
					tab.count = stats[tab.key] || 0
				}
			})
		},
		
		// 处理搜索
		handleSearch() {
			// 实时搜索，无需额外处理
		},
		
		// 切换筛选
		switchFilter(filter) {
			this.currentFilter = filter
		},
		
		// 显示筛选弹窗
		showFilterModal() {
			this.showFilter = true
		},
		
		// 隐藏筛选弹窗
		hideFilterModal() {
			this.showFilter = false
		},
		
		// 切换筛选等级
		toggleFilterLevel(level) {
			const index = this.filterOptions.levels.indexOf(level)
			if (index > -1) {
				this.filterOptions.levels.splice(index, 1)
			} else {
				this.filterOptions.levels.push(level)
			}
		},
		
		// 切换筛选状态
		toggleFilterStatus(status) {
			const index = this.filterOptions.statuses.indexOf(status)
			if (index > -1) {
				this.filterOptions.statuses.splice(index, 1)
			} else {
				this.filterOptions.statuses.push(status)
			}
		},
		
		// 重置筛选
		resetFilter() {
			this.filterOptions = {
				levels: [],
				statuses: [],
				startDate: '',
				endDate: ''
			}
		},
		
		// 应用筛选
		async applyFilter() {
			try {
				uni.showLoading({ title: '筛选中...' })
				const response = await getFaultList({
					filter: this.currentFilter,
					keyword: this.searchKeyword,
					levels: this.filterOptions.levels,
					statuses: this.filterOptions.statuses,
					startDate: this.filterOptions.startDate,
					endDate: this.filterOptions.endDate
				})
				this.faultList = response.data.faults || []
				this.updateOverviewStats()
				this.hideFilterModal()
				uni.hideLoading()
				uni.showToast({
					title: '筛选条件已应用',
					icon: 'success'
				})
			} catch (error) {
				console.error('应用筛选失败:', error)
				uni.hideLoading()
				uni.showToast({
					title: error.message || '筛选失败',
					icon: 'error'
				})
			}
		},
		
		// 导出故障数据
		async exportFaults() {
			try {
				uni.showLoading({
					title: '导出中...'
				})
				
				await exportFaultData({
				filter: this.currentFilter,
				keyword: this.searchKeyword
			})
				
				uni.hideLoading()
				uni.showToast({
					title: '导出成功',
					icon: 'success'
				})
			} catch (error) {
				console.error('导出失败:', error)
				uni.hideLoading()
				uni.showToast({
					title: error.message || '导出失败',
					icon: 'error'
				})
			}
		},
		
		// 查看故障详情
		viewFaultDetail(fault) {
			uni.navigateTo({
				url: `/pages/fault/detail?id=${fault.id}`
			})
		},
		
		// 处理故障
		handleFault(fault) {
			this.currentFault = fault
			this.showHandleModal = true
			this.resetHandleForm()
		},
		
		// 分配故障
		assignFault(fault) {
			this.currentFault = fault
			this.handleForm.type = 'assign'
			this.showHandleModal = true
		},
		
		// 隐藏处理弹窗
		hideHandleModal() {
			this.showHandleModal = false
			this.currentFault = null
		},
		
		// 重置处理表单
		resetHandleForm() {
			this.handleForm = {
				type: 'resolve',
				technician: '',
				remark: ''
			}
		},
		
		// 选择处理类型
		selectHandleType(type) {
			this.handleForm.type = type
		},
		
		// 选择维修员
		selectTechnician(e) {
			const index = e.detail.value
			this.handleForm.technician = this.technicians[index].name
		},
		
		// 提交处理
		async submitHandle() {
			try {
				if (this.handleForm.type === 'assign' && !this.handleForm.technician) {
					uni.showToast({
						title: '请选择维修员',
						icon: 'none'
					})
					return
				}
				
				uni.showLoading({ title: '处理中...' })
				
				const response = await handleFault({
					id: this.currentFault.id,
					type: this.handleForm.type,
					technician: this.handleForm.technician,
					remark: this.handleForm.remark
				})
				
				// 更新本地故障状态
				const fault = this.faultList.find(f => f.id === this.currentFault.id)
				if (fault && response.data) {
					Object.assign(fault, response.data)
				}
				
				// 更新统计
				this.updateOverviewStats()
				
				uni.hideLoading()
				this.hideHandleModal()
				uni.showToast({
					title: '处理成功',
					icon: 'success'
				})
			} catch (error) {
				console.error('处理故障失败:', error)
				uni.hideLoading()
				uni.showToast({
					title: error.message || '处理失败',
					icon: 'error'
				})
			}
		},
		
		// 切换图表周期
		switchChartPeriod(period) {
			this.currentChartPeriod = period
			this.loadChartData(period)
		},
		
		// 加载图表数据
		async loadChartData(period) {
			try {
				uni.showLoading({ title: '加载图表数据...' })
				
				// 调用API获取图表数据
				const response = await getFaultChartData(period)
				
				if (response.data) {
					this.chartLabels = response.data.labels || []
					this.chartData = response.data.data || []
				}
				
				uni.hideLoading()
			} catch (error) {
				console.error('加载图表数据失败:', error)
				uni.hideLoading()
				uni.showToast({
					title: error.message || '加载图表数据失败',
					icon: 'error'
				})
			}
		},
		
		// 查看所有维修记录
		viewAllRepairs() {
			uni.navigateTo({
				url: '/pages/fault/repairs'
			})
		},
		
		// 获取故障等级样式类
		getFaultLevelClass(level) {
			return `level-${level}`
		},
		
		// 获取故障等级文本
		getFaultLevelText(level) {
			const levelMap = {
				high: '高级',
				medium: '中级',
				low: '低级'
			}
			return levelMap[level] || level
		},
		
		// 获取故障状态样式类
		getFaultStatusClass(status) {
			return `status-${status}`
		},
		
		// 获取故障状态文本
		getFaultStatusText(status) {
			const statusMap = {
				pending: '待处理',
				processing: '处理中',
				resolved: '已解决'
			}
			return statusMap[status] || status
		},
		
		// 获取维修结果样式类
		getRepairResultClass(result) {
			return `result-${result}`
		},
		
		// 获取维修结果文本
		getRepairResultText(result) {
			const resultMap = {
				success: '维修成功',
				failed: '维修失败',
				pending: '维修中'
			}
			return resultMap[result] || result
		},
		
		// 格式化时间
		formatTime(timeStr) {
			if (!timeStr) return ''
			
			const date = new Date(timeStr)
			const now = new Date()
			const diff = now - date
			
			// 小于1小时显示分钟
			if (diff < 3600000) {
				const minutes = Math.floor(diff / 60000)
				return `${minutes}分钟前`
			}
			
			// 小于24小时显示小时
			if (diff < 86400000) {
				const hours = Math.floor(diff / 3600000)
				return `${hours}小时前`
			}
			
			// 显示日期
			const month = date.getMonth() + 1
			const day = date.getDate()
			const hour = date.getHours()
			const minute = date.getMinutes()
			
			return `${month}月${day}日 ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
		}
	}
}
</script>

<style lang="scss" scoped>
.fault-container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

// 故障概览卡片
.fault-overview-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.card-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.refresh-btn {
	display: flex;
	align-items: center;
	padding: 8rpx 16rpx;
	background-color: #f0f9ff;
	border-radius: 8rpx;
	color: #1890ff;
	font-size: 24rpx;

	.refresh-icon {
		margin-right: 8rpx;
		font-size: 28rpx;
	}
}

.overview-stats {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
}

.stat-card {
	flex: 1;
	min-width: 160rpx;
	padding: 24rpx 16rpx;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	gap: 16rpx;

	&.stat-total {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
	}

	&.stat-pending {
		background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
		color: white;
	}

	&.stat-processing {
		background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
		color: white;
	}

	&.stat-resolved {
		background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
		color: white;
	}
}

.stat-icon {
	font-size: 48rpx;
}

.stat-content {
	flex: 1;
}

.stat-number {
	font-size: 36rpx;
	font-weight: 700;
	margin-bottom: 4rpx;
}

.stat-label {
	font-size: 24rpx;
	opacity: 0.9;
	margin-bottom: 8rpx;
}

.stat-trend {
	font-size: 20rpx;
	padding: 2rpx 8rpx;
	border-radius: 4rpx;
	background-color: rgba(255, 255, 255, 0.2);

	&.trend-up {
		color: #ff4d4f;
	}

	&.trend-down {
		color: #52c41a;
	}

	&.trend-stable {
		color: #faad14;
	}
}

// 筛选区域
.filter-section {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.search-box {
	position: relative;
	margin-bottom: 24rpx;
}

.search-input {
	width: 100%;
	height: 80rpx;
	padding: 0 80rpx 0 24rpx;
	border: 2rpx solid #e8e8e8;
	border-radius: 12rpx;
	font-size: 28rpx;
	background-color: #fafafa;

	&:focus {
		border-color: #1890ff;
		background-color: #ffffff;
	}
}

.search-icon {
	position: absolute;
	right: 24rpx;
	top: 50%;
	transform: translateY(-50%);
	font-size: 32rpx;
	color: #999999;
}

.filter-tabs {
	display: flex;
	gap: 16rpx;

	text {
		padding: 12rpx 24rpx;
		border-radius: 8rpx;
		font-size: 26rpx;
		color: #666666;
		background-color: #f5f5f5;
		transition: all 0.3s;

		&.active {
			color: #1890ff;
			background-color: #e6f7ff;
			border: 2rpx solid #1890ff;
		}
	}
}

.tab-count {
	margin-left: 8rpx;
	font-size: 22rpx;
	opacity: 0.8;
}

// 故障列表卡片
.fault-list-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.list-actions {
	display: flex;
	gap: 16rpx;
}

.action-btn {
	display: flex;
	align-items: center;
	padding: 8rpx 16rpx;
	border-radius: 8rpx;
	font-size: 24rpx;
	color: #666666;
	background-color: #f5f5f5;
	transition: all 0.3s;

	&.primary {
		color: #ffffff;
		background-color: #1890ff;
	}

	&.secondary {
		color: #666666;
		background-color: #f5f5f5;
		border: 2rpx solid #d9d9d9;
	}

	.btn-icon {
		margin-right: 8rpx;
		font-size: 28rpx;
	}
}

.fault-list {
	margin-top: 24rpx;
}

.fault-item {
	padding: 24rpx;
	border: 2rpx solid #f0f0f0;
	border-radius: 12rpx;
	margin-bottom: 16rpx;
	transition: all 0.3s;

	&:hover {
		border-color: #1890ff;
		box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.1);
	}

	&:last-child {
		margin-bottom: 0;
	}
}

.fault-header {
	display: flex;
	align-items: center;
	gap: 16rpx;
	margin-bottom: 16rpx;
}

.fault-level {
	padding: 4rpx 12rpx;
	border-radius: 6rpx;
	font-size: 22rpx;
	font-weight: 500;

	&.level-high {
		color: #ffffff;
		background-color: #ff4d4f;
	}

	&.level-medium {
		color: #ffffff;
		background-color: #faad14;
	}

	&.level-low {
		color: #ffffff;
		background-color: #52c41a;
	}
}

.fault-status {
	padding: 4rpx 12rpx;
	border-radius: 6rpx;
	font-size: 22rpx;
	font-weight: 500;

	&.status-pending {
		color: #ff4d4f;
		background-color: #fff2f0;
		border: 2rpx solid #ffccc7;
	}

	&.status-processing {
		color: #faad14;
		background-color: #fffbe6;
		border: 2rpx solid #ffe58f;
	}

	&.status-resolved {
		color: #52c41a;
		background-color: #f6ffed;
		border: 2rpx solid #b7eb8f;
	}
}

.fault-time {
	font-size: 24rpx;
	color: #999999;
	margin-left: auto;
}

.fault-content {
	margin-bottom: 20rpx;
}

.fault-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 12rpx;
}

.fault-device {
	display: flex;
	align-items: center;
	gap: 8rpx;
	margin-bottom: 12rpx;
}

.device-icon {
	font-size: 28rpx;
}

.device-name {
	font-size: 26rpx;
	color: #1890ff;
	font-weight: 500;
}

.device-location {
	font-size: 24rpx;
	color: #999999;
}

.fault-desc {
	font-size: 26rpx;
	color: #666666;
	line-height: 1.5;
}

.fault-actions {
	display: flex;
	gap: 12rpx;
	justify-content: flex-end;
}

// 空状态
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 80rpx 40rpx;
	text-align: center;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 24rpx;
	opacity: 0.3;
}

.empty-text {
	font-size: 32rpx;
	color: #333333;
	margin-bottom: 12rpx;
}

.empty-desc {
	font-size: 26rpx;
	color: #999999;
}

// 维修记录卡片
.repair-records-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.view-more-btn {
	color: #1890ff;
	font-size: 26rpx;
}

.repair-list {
	margin-top: 24rpx;
}

.repair-item {
	display: flex;
	gap: 16rpx;
	padding: 20rpx 0;
	border-bottom: 2rpx solid #f0f0f0;

	&:last-child {
		border-bottom: none;
	}
}

.repair-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background-color: #1890ff;
	color: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	font-weight: 600;
	flex-shrink: 0;
}

.repair-content {
	flex: 1;
}

.repair-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8rpx;
}

.repair-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
}

.repair-time {
	font-size: 24rpx;
	color: #999999;
}

.repair-info {
	display: flex;
	gap: 24rpx;
	margin-bottom: 8rpx;
}

.repair-device,
.repair-technician {
	font-size: 24rpx;
	color: #666666;
}

.repair-desc {
	font-size: 26rpx;
	color: #666666;
	line-height: 1.4;
	margin-bottom: 8rpx;
}

.repair-result {
	padding: 4rpx 12rpx;
	border-radius: 6rpx;
	font-size: 22rpx;
	font-weight: 500;
	align-self: flex-start;

	&.result-success {
		color: #52c41a;
		background-color: #f6ffed;
		border: 2rpx solid #b7eb8f;
	}

	&.result-failed {
		color: #ff4d4f;
		background-color: #fff2f0;
		border: 2rpx solid #ffccc7;
	}

	&.result-pending {
		color: #faad14;
		background-color: #fffbe6;
		border: 2rpx solid #ffe58f;
	}
}

// 故障统计图表
.fault-chart-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.chart-period {
	display: flex;
	gap: 16rpx;

	text {
		padding: 8rpx 16rpx;
		border-radius: 6rpx;
		font-size: 24rpx;
		color: #666666;
		background-color: #f5f5f5;
		transition: all 0.3s;

		&.active {
			color: #1890ff;
			background-color: #e6f7ff;
		}
	}
}

.chart-container {
	margin: 32rpx 0;
}

.chart-bars {
	display: flex;
	align-items: flex-end;
	height: 400rpx;
	gap: 24rpx;
	padding: 0 16rpx;
	margin-bottom: 16rpx;
}

.chart-bar {
	flex: 1;
	position: relative;
	display: flex;
	flex-direction: column;
	justify-content: flex-end;
	min-height: 40rpx;
}

.bar-segments {
	width: 100%;
	border-radius: 8rpx 8rpx 0 0;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	height: 100%;
}

.bar-segment {
	width: 100%;
	transition: all 0.3s;
}

.bar-value {
	position: absolute;
	top: -32rpx;
	left: 50%;
	transform: translateX(-50%);
	font-size: 24rpx;
	font-weight: 600;
	color: #333333;
}

.chart-labels {
	display: flex;
	gap: 24rpx;
	padding: 0 16rpx;

	text {
		flex: 1;
		text-align: center;
		font-size: 24rpx;
		color: #666666;
	}
}

.chart-legend {
	display: flex;
	flex-wrap: wrap;
	gap: 24rpx;
	margin-top: 24rpx;
	justify-content: center;
}

.legend-item {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.legend-color {
	width: 24rpx;
	height: 24rpx;
	border-radius: 4rpx;
}

.legend-label {
	font-size: 24rpx;
	color: #666666;
}

// 筛选弹窗
.filter-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}

.modal-content {
	background-color: #ffffff;
	border-radius: 16rpx;
	width: 100%;
	max-width: 600rpx;
	max-height: 80vh;
	overflow-y: auto;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 32rpx;
	border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.close-btn {
	font-size: 32rpx;
	color: #999999;
	padding: 8rpx;
}

.filter-options {
	padding: 32rpx;
}

.filter-group {
	margin-bottom: 32rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.group-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 16rpx;
}

.option-list {
	display: flex;
	flex-wrap: wrap;
	gap: 12rpx;

	text {
		padding: 12rpx 20rpx;
		border: 2rpx solid #d9d9d9;
		border-radius: 8rpx;
		font-size: 26rpx;
		color: #666666;
		background-color: #ffffff;
		transition: all 0.3s;

		&.active {
			color: #1890ff;
			border-color: #1890ff;
			background-color: #e6f7ff;
		}
	}
}

.date-range {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.date-input {
	flex: 1;
	height: 80rpx;
	padding: 0 16rpx;
	border: 2rpx solid #d9d9d9;
	border-radius: 8rpx;
	font-size: 26rpx;
	background-color: #ffffff;

	&:focus {
		border-color: #1890ff;
	}
}

.date-separator {
	font-size: 26rpx;
	color: #666666;
}

.modal-actions {
	display: flex;
	gap: 16rpx;
	padding: 32rpx;
	border-top: 2rpx solid #f0f0f0;
	justify-content: flex-end;
}

// 故障处理弹窗
.handle-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}

.handle-form {
	padding: 32rpx;
}

.form-item {
	margin-bottom: 32rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.form-label {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 16rpx;
}

.handle-options {
	display: flex;
	gap: 16rpx;

	text {
		padding: 16rpx 24rpx;
		border: 2rpx solid #d9d9d9;
		border-radius: 8rpx;
		font-size: 26rpx;
		color: #666666;
		background-color: #ffffff;
		transition: all 0.3s;

		&.active {
			color: #1890ff;
			border-color: #1890ff;
			background-color: #e6f7ff;
		}
	}
}

.picker-input {
	height: 80rpx;
	padding: 0 16rpx;
	border: 2rpx solid #d9d9d9;
	border-radius: 8rpx;
	font-size: 26rpx;
	color: #333333;
	background-color: #ffffff;
	display: flex;
	align-items: center;
}

.form-textarea {
	width: 100%;
	height: 160rpx;
	padding: 16rpx;
	border: 2rpx solid #d9d9d9;
	border-radius: 8rpx;
	font-size: 26rpx;
	color: #333333;
	background-color: #ffffff;
	resize: none;

	&:focus {
		border-color: #1890ff;
	}
}

// 响应式设计
@media screen and (min-width: 768px) {
	.fault-container {
		padding: 40rpx;
	}

	.overview-stats {
		gap: 24rpx;
	}

	.stat-card {
		min-width: 200rpx;
		padding: 32rpx 24rpx;
	}

	.filter-tabs {
		gap: 24rpx;
	}

	.fault-item {
		padding: 32rpx;
	}

	.chart-bars {
		height: 500rpx;
		gap: 32rpx;
	}

	.modal-content {
		max-width: 800rpx;
	}
}
</style>