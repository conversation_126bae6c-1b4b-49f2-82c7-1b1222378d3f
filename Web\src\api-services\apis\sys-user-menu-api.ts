/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AdminResultListInt64 } from '../models';
import { AdminResultListMenuOutput } from '../models';
import { UserMenuInput } from '../models';
/**
 * SysUserMenuApi - axios parameter creator
 * @export
 */
export const SysUserMenuApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 收藏菜单 🔖
         * @param {UserMenuInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSysUserMenuAddPost: async (body?: UserMenuInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/sysUserMenu/add`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 取消收藏菜单 🔖
         * @param {UserMenuInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSysUserMenuDeleteUserMenuPost: async (body?: UserMenuInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/sysUserMenu/deleteUserMenu`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取当前用户收藏的菜单Id集合 🔖
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSysUserMenuUserMenuIdListGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/sysUserMenu/userMenuIdList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取当前用户收藏的菜单集合 🔖
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSysUserMenuUserMenuListGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/sysUserMenu/userMenuList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * SysUserMenuApi - functional programming interface
 * @export
 */
export const SysUserMenuApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 收藏菜单 🔖
         * @param {UserMenuInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysUserMenuAddPost(body?: UserMenuInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await SysUserMenuApiAxiosParamCreator(configuration).apiSysUserMenuAddPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 取消收藏菜单 🔖
         * @param {UserMenuInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysUserMenuDeleteUserMenuPost(body?: UserMenuInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await SysUserMenuApiAxiosParamCreator(configuration).apiSysUserMenuDeleteUserMenuPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取当前用户收藏的菜单Id集合 🔖
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysUserMenuUserMenuIdListGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultListInt64>>> {
            const localVarAxiosArgs = await SysUserMenuApiAxiosParamCreator(configuration).apiSysUserMenuUserMenuIdListGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取当前用户收藏的菜单集合 🔖
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysUserMenuUserMenuListGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultListMenuOutput>>> {
            const localVarAxiosArgs = await SysUserMenuApiAxiosParamCreator(configuration).apiSysUserMenuUserMenuListGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * SysUserMenuApi - factory interface
 * @export
 */
export const SysUserMenuApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 收藏菜单 🔖
         * @param {UserMenuInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysUserMenuAddPost(body?: UserMenuInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return SysUserMenuApiFp(configuration).apiSysUserMenuAddPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 取消收藏菜单 🔖
         * @param {UserMenuInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysUserMenuDeleteUserMenuPost(body?: UserMenuInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return SysUserMenuApiFp(configuration).apiSysUserMenuDeleteUserMenuPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取当前用户收藏的菜单Id集合 🔖
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysUserMenuUserMenuIdListGet(options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultListInt64>> {
            return SysUserMenuApiFp(configuration).apiSysUserMenuUserMenuIdListGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取当前用户收藏的菜单集合 🔖
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysUserMenuUserMenuListGet(options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultListMenuOutput>> {
            return SysUserMenuApiFp(configuration).apiSysUserMenuUserMenuListGet(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * SysUserMenuApi - object-oriented interface
 * @export
 * @class SysUserMenuApi
 * @extends {BaseAPI}
 */
export class SysUserMenuApi extends BaseAPI {
    /**
     * 
     * @summary 收藏菜单 🔖
     * @param {UserMenuInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysUserMenuApi
     */
    public async apiSysUserMenuAddPost(body?: UserMenuInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return SysUserMenuApiFp(this.configuration).apiSysUserMenuAddPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 取消收藏菜单 🔖
     * @param {UserMenuInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysUserMenuApi
     */
    public async apiSysUserMenuDeleteUserMenuPost(body?: UserMenuInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return SysUserMenuApiFp(this.configuration).apiSysUserMenuDeleteUserMenuPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取当前用户收藏的菜单Id集合 🔖
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysUserMenuApi
     */
    public async apiSysUserMenuUserMenuIdListGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultListInt64>> {
        return SysUserMenuApiFp(this.configuration).apiSysUserMenuUserMenuIdListGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取当前用户收藏的菜单集合 🔖
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysUserMenuApi
     */
    public async apiSysUserMenuUserMenuListGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultListMenuOutput>> {
        return SysUserMenuApiFp(this.configuration).apiSysUserMenuUserMenuListGet(options).then((request) => request(this.axios, this.basePath));
    }
}
