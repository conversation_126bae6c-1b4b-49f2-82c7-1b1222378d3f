/**
 * 图标格式转换脚本
 * 将SVG图标转换为PNG格式作为备用
 * 使用方法: node scripts/generate-png-icons.js
 */

const fs = require('fs')
const path = require('path')

// 图标配置
const ICON_CONFIG = {
  // 输入目录（SVG图标）
  inputDir: path.join(__dirname, '../static/icons'),
  // 输出目录（PNG图标）
  outputDir: path.join(__dirname, '../static/icons'),
  // PNG图标尺寸
  sizes: [24, 48, 96], // 支持多种尺寸
  // 默认尺寸
  defaultSize: 48
}

// 创建简单的SVG到PNG转换（使用Canvas API的替代方案）
function createPngFallback() {
  const iconsDir = ICON_CONFIG.inputDir
  
  if (!fs.existsSync(iconsDir)) {
    console.error('图标目录不存在:', iconsDir)
    return
  }
  
  // 读取所有SVG文件
  const svgFiles = fs.readdirSync(iconsDir)
    .filter(file => file.endsWith('.svg'))
  
  console.log(`找到 ${svgFiles.length} 个SVG图标文件`)
  
  // 创建PNG备用方案的配置文件
  const pngConfig = {
    icons: {},
    fallbackStrategy: {
      // 当SVG加载失败时的处理策略
      usePng: true,
      useBase64: false,
      useIconFont: false
    },
    generatedAt: new Date().toISOString()
  }
  
  svgFiles.forEach(file => {
    const iconName = path.basename(file, '.svg')
    const svgPath = path.join(iconsDir, file)
    
    try {
      // 读取SVG内容
      const svgContent = fs.readFileSync(svgPath, 'utf8')
      
      // 提取SVG的基本信息
      const viewBoxMatch = svgContent.match(/viewBox=["']([^"']+)["']/)
      const widthMatch = svgContent.match(/width=["']([^"']+)["']/)
      const heightMatch = svgContent.match(/height=["']([^"']+)["']/)
      
      pngConfig.icons[iconName] = {
        svg: {
          path: `/static/icons/${file}`,
          viewBox: viewBoxMatch ? viewBoxMatch[1] : '0 0 24 24',
          width: widthMatch ? widthMatch[1] : '24',
          height: heightMatch ? heightMatch[1] : '24'
        },
        png: {
          // 注意：实际的PNG文件需要使用专门的转换工具生成
          // 这里只是创建配置，实际转换需要使用如sharp、canvas等库
          path: `/static/icons/${iconName}.png`,
          available: false, // 标记PNG文件是否可用
          sizes: ICON_CONFIG.sizes
        },
        fallback: {
          // 备用方案：使用Unicode字符或emoji
          char: getIconFallbackChar(iconName),
          color: getIconColor(svgContent)
        }
      }
      
      console.log(`✓ 处理图标: ${iconName}`)
      
    } catch (error) {
      console.error(`✗ 处理图标失败: ${iconName}`, error.message)
    }
  })
  
  // 保存配置文件
  const configPath = path.join(ICON_CONFIG.outputDir, 'icon-config.json')
  fs.writeFileSync(configPath, JSON.stringify(pngConfig, null, 2), 'utf8')
  
  console.log(`\n图标配置已生成: ${configPath}`)
  console.log('\n注意事项:')
  console.log('1. 此脚本只生成配置文件，实际PNG转换需要额外工具')
  console.log('2. 推荐使用在线SVG转PNG工具或安装sharp库进行批量转换')
  console.log('3. 转换完成后，请更新icon-config.json中的available字段')
  
  // 生成转换指南
  generateConversionGuide(pngConfig)
}

// 根据图标名称获取备用字符
function getIconFallbackChar(iconName) {
  const fallbackMap = {
    'home': '🏠',
    'home-active': '🏠',
    'device': '📱',
    'device-active': '📱',
    'lighting': '💡',
    'lighting-active': '💡',
    'energy': '⚡',
    'energy-active': '⚡',
    'user': '👤',
    'user-active': '👤',
    'light-on': '💡',
    'light-off': '🔘',
    'led-on': '🔆',
    'led-off': '⚫',
    'sensor': '📡',
    'switch': '🔘',
    'device-default': '📱',
    'status-online': '🟢',
    'status-offline': '🔴',
    'status-warning': '🟡',
    'add': '➕',
    'delete': '❌',
    'edit': '✏️',
    'filter': '🔍',
    'settings': '⚙️'
  }
  
  return fallbackMap[iconName] || '📄'
}

// 从SVG内容中提取主要颜色
function getIconColor(svgContent) {
  const strokeMatch = svgContent.match(/stroke=["']([^"']+)["']/)
  const fillMatch = svgContent.match(/fill=["']([^"']+)["']/)
  
  if (strokeMatch && strokeMatch[1] !== 'none') {
    return strokeMatch[1]
  }
  if (fillMatch && fillMatch[1] !== 'none') {
    return fillMatch[1]
  }
  
  return '#666666' // 默认颜色
}

// 生成转换指南
function generateConversionGuide(config) {
  const guidePath = path.join(ICON_CONFIG.outputDir, 'PNG转换指南.md')
  
  let guide = `# PNG图标转换指南\n\n`
  guide += `生成时间: ${config.generatedAt}\n\n`
  guide += `## 方法一：使用在线工具\n\n`
  guide += `1. 访问 https://convertio.co/zh/svg-png/ 或类似在线转换工具\n`
  guide += `2. 批量上传SVG文件\n`
  guide += `3. 设置输出尺寸为48x48像素\n`
  guide += `4. 下载转换后的PNG文件\n\n`
  
  guide += `## 方法二：使用Node.js脚本（推荐）\n\n`
  guide += `\`\`\`bash\n`
  guide += `# 安装依赖\n`
  guide += `npm install sharp\n\n`
  guide += `# 运行转换脚本\n`
  guide += `node scripts/svg-to-png.js\n`
  guide += `\`\`\`\n\n`
  
  guide += `## 需要转换的图标列表\n\n`
  Object.keys(config.icons).forEach(iconName => {
    guide += `- ${iconName}.svg → ${iconName}.png\n`
  })
  
  guide += `\n## 转换完成后\n\n`
  guide += `1. 将PNG文件放入 static/icons/ 目录\n`
  guide += `2. 更新 icon-config.json 中对应图标的 available 字段为 true\n`
  guide += `3. 测试图标加载效果\n`
  
  fs.writeFileSync(guidePath, guide, 'utf8')
  console.log(`转换指南已生成: ${guidePath}`)
}

// 执行脚本
if (require.main === module) {
  createPngFallback()
}

module.exports = {
  createPngFallback,
  ICON_CONFIG
}