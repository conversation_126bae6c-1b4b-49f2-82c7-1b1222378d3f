// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Microsoft.Extensions.Logging;
using Moq;
using MQTTnet;
using MQTTnet.Client;
using Newtonsoft.Json;
using Xunit;

namespace Admin.NET.Plugin.MQTT.Test;

/// <summary>
/// DeviceEventService Beacon事件处理测试
/// </summary>
public class DeviceEventServiceBeaconTests
{
    private readonly Mock<ILogger<DeviceEventService>> _loggerMock;
    private readonly DeviceEventService _service;

    public DeviceEventServiceBeaconTests()
    {
        _loggerMock = new Mock<ILogger<DeviceEventService>>();
        _service = new DeviceEventService(_loggerMock.Object);
    }

    [Fact]
    public async Task HandleAsync_WithValidBeaconJson_ShouldProcessSuccessfully()
    {
        // Arrange
        var topic = "/sys/guangzhouminrui/guangzhouminrui@@@18C8E716B349/thing/event/beacon/post";
        var payload = @"{
            ""id"": ""1756110419700"",
            ""method"": ""thing.event.beacon.post"",
            ""params"": {
                ""time"": 1756110419700,
                ""value"": {
                    ""report_time"": ""1756110419700"",
                    ""device_name"": ""guangzhouminrui@@@18C8E716B349"",
                    ""area"": ""00 00"",
                    ""cluster"": ""C0 00"",
                    ""number"": ""0B 49"",
                    ""uuid"": ""2B 90 18 E9 06 12"",
                    ""type"": ""灯具"",
                    ""version"": ""v5.11.1""
                }
            },
            ""version"": ""1.0""
        }";

        var eventArgs = new MqttApplicationMessageReceivedEventArgs(
            "test-client",
            new MqttApplicationMessage
            {
                Topic = topic,
                PayloadSegment = System.Text.Encoding.UTF8.GetBytes(payload)
            },
            new MqttPublishPacket(),
            null);

        bool eventFired = false;
        _service.DeviceBeaconReceived += (sender, e) =>
        {
            eventFired = true;
            Assert.Equal("00 00_0B 49", e.DeviceKey);
            Assert.Equal("guangzhouminrui@@@18C8E716B349", e.BeaconData.DeviceName);
            Assert.Equal("2B 90 18 E9 06 12", e.BeaconData.UniqueId);
            Assert.Equal("灯具", e.BeaconData.DeviceType);
            Assert.Equal("v5.11.1", e.BeaconData.Version);
        };

        // Act
        await _service.HandleAsync(topic, payload, eventArgs);

        // Assert
        Assert.True(eventFired, "DeviceBeaconReceived事件应该被触发");
    }

    [Fact]
    public async Task HandleAsync_WithMissingRequiredField_ShouldLogError()
    {
        // Arrange
        var topic = "/sys/test/device/thing/event/beacon/post";
        var payload = @"{
            ""id"": ""1756110419700"",
            ""method"": ""thing.event.beacon.post"",
            ""params"": {
                ""time"": 1756110419700,
                ""value"": {
                    ""report_time"": ""1756110419700"",
                    ""device_name"": ""test-device"",
                    ""area"": ""00 00""
                    // 缺少必需字段: cluster, number, uuid, type, version
                }
            },
            ""version"": ""1.0""
        }";

        var eventArgs = new MqttApplicationMessageReceivedEventArgs(
            "test-client",
            new MqttApplicationMessage
            {
                Topic = topic,
                PayloadSegment = System.Text.Encoding.UTF8.GetBytes(payload)
            },
            new MqttPublishPacket(),
            null);

        // Act
        await _service.HandleAsync(topic, payload, eventArgs);

        // Assert
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("缺少必需字段")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.AtLeastOnce);
    }

    [Fact]
    public async Task HandleAsync_WithInvalidJson_ShouldLogError()
    {
        // Arrange
        var topic = "/sys/test/device/thing/event/beacon/post";
        var payload = @"{ invalid json }";

        var eventArgs = new MqttApplicationMessageReceivedEventArgs(
            "test-client",
            new MqttApplicationMessage
            {
                Topic = topic,
                PayloadSegment = System.Text.Encoding.UTF8.GetBytes(payload)
            },
            new MqttPublishPacket(),
            null);

        // Act
        await _service.HandleAsync(topic, payload, eventArgs);

        // Assert
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("解析beacon事件JSON失败")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.AtLeastOnce);
    }

    [Fact]
    public async Task HandleAsync_WithNonBeaconMethod_ShouldUseBaseClassHandling()
    {
        // Arrange
        var topic = "/sys/test/device/thing/event/setting/post";
        var payload = @"{
            ""id"": ""1756110419700"",
            ""method"": ""thing.event.setting.post"",
            ""params"": {
                ""time"": 1756110419700,
                ""value"": {
                    ""brightness"": ""50%""
                }
            },
            ""version"": ""1.0""
        }";

        var eventArgs = new MqttApplicationMessageReceivedEventArgs(
            "test-client",
            new MqttApplicationMessage
            {
                Topic = topic,
                PayloadSegment = System.Text.Encoding.UTF8.GetBytes(payload)
            },
            new MqttPublishPacket(),
            null);

        // Act & Assert - 应该不会抛出异常，会使用基类处理
        await _service.HandleAsync(topic, payload, eventArgs);
    }

    [Fact]
    public void GetDevices_ShouldReturnCachedDevices()
    {
        // Act
        var devices = _service.GetDevices();

        // Assert
        Assert.NotNull(devices);
        Assert.IsType<List<DeviceInfo>>(devices);
    }

    [Fact]
    public void GetDeviceStatistics_ShouldReturnStatistics()
    {
        // Act
        var stats = _service.GetDeviceStatistics();

        // Assert
        Assert.NotNull(stats);
        Assert.True(stats.TotalDevices >= 0);
        Assert.True(stats.OnlineDevices >= 0);
        Assert.True(stats.OfflineDevices >= 0);
    }
}
