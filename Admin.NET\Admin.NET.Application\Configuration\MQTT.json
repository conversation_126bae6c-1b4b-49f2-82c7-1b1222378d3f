{"$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json", "MqttOptions": {"Server": "14.103.146.84", "Port": 1883, "InstanceId": "energylight", "AccessKeyId": "ak_c371a2975f7c4c0eb672b4675bfd2", "AccessKeySecret": "sk_5905b2a8fb62457691f22aa2446b7d49b9174283b3d64ec785f8557c5ed2d", "EnableSsl": false, "ConnectionTimeoutSeconds": 30, "KeepAliveSeconds": 60, "ReconnectInterval": 5, "MaxReconnectAttempts": 10, "DefaultQoS": 1, "CleanSession": true, "WillTopic": "device/offline", "WillMessage": "<PERSON><PERSON> disconnected", "WillQoS": 1, "WillRetain": false, "SubscribeTopics": ["/sys/+/+/thing/event/+/post", "gateway/+/event/+"], "EnableMessageLogging": true, "MessageBufferSize": 1000}}