/* 通用样式文件 */

/* 引入图标字体 */
@import url('../fonts/iconfont.css');

/* CSS变量定义 - 主题色彩系统 */
:root {
  /* 主色调 */
  --primary-color: #007aff;
  --primary-light: #4da6ff;
  --primary-dark: #0056cc;
  
  /* 功能色 */
  --success-color: #4cd964;
  --warning-color: #f0ad4e;
  --danger-color: #dd524d;
  --info-color: #5ac8fa;
  
  /* 中性色 */
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --text-disabled: #cccccc;
  
  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #f5f5f5;
  --bg-disabled: #f0f0f0;
  
  /* 边框色 */
  --border-light: #e6e6e6;
  --border-medium: #d9d9d9;
  --border-dark: #cccccc;
  
  /* 阴影 */
  --shadow-light: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  --shadow-heavy: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  
  /* 圆角 */
  --radius-small: 8rpx;
  --radius-medium: 12rpx;
  --radius-large: 16rpx;
  --radius-round: 50%;
  
  /* 间距 */
  --space-xs: 8rpx;
  --space-sm: 12rpx;
  --space-md: 16rpx;
  --space-lg: 24rpx;
  --space-xl: 32rpx;
  --space-xxl: 48rpx;
}

/* 重置样式 */
* {
  box-sizing: border-box;
}

page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 布局样式 */
.container {
  padding: 20rpx;
  min-height: 100vh;
}

.page-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: calc(100vh - 100rpx);
}

.content-wrapper {
  padding: 0 20rpx;
}

/* 卡片样式 */
.card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.card-content {
  padding: 16rpx 0;
}

/* Flex布局 */
.flex {
  display: flex;
}

.flex-row {
  flex-direction: row;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

.flex-start {
  justify-content: flex-start;
}

.flex-end {
  justify-content: flex-end;
}

.align-center {
  align-items: center;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

.flex-1 {
  flex: 1;
}

.flex-wrap {
  flex-wrap: wrap;
}

/* 文本样式 */
.text-primary {
  color: #1890ff;
}

.text-success {
  color: #52c41a;
}

.text-warning {
  color: #faad14;
}

.text-danger {
  color: #f5222d;
}

.text-info {
  color: #13c2c2;
}

.text-gray {
  color: #999999;
}

.text-dark {
  color: #333333;
}

.text-light {
  color: #666666;
}

.text-white {
  color: #ffffff;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* 字体大小 */
.text-xs {
  font-size: 20rpx;
}

.text-sm {
  font-size: 24rpx;
}

.text-base {
  font-size: 28rpx;
}

.text-lg {
  font-size: 32rpx;
}

.text-xl {
  font-size: 36rpx;
}

.text-2xl {
  font-size: 40rpx;
}

.text-3xl {
  font-size: 48rpx;
}

/* 字体粗细 */
.font-normal {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

/* 间距 */
.m-0 { margin: 0; }
.m-1 { margin: 8rpx; }
.m-2 { margin: 16rpx; }
.m-3 { margin: 24rpx; }
.m-4 { margin: 32rpx; }
.m-5 { margin: 40rpx; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8rpx; }
.mt-2 { margin-top: 16rpx; }
.mt-3 { margin-top: 24rpx; }
.mt-4 { margin-top: 32rpx; }
.mt-5 { margin-top: 40rpx; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8rpx; }
.mb-2 { margin-bottom: 16rpx; }
.mb-3 { margin-bottom: 24rpx; }
.mb-4 { margin-bottom: 32rpx; }
.mb-5 { margin-bottom: 40rpx; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: 8rpx; }
.ml-2 { margin-left: 16rpx; }
.ml-3 { margin-left: 24rpx; }
.ml-4 { margin-left: 32rpx; }
.ml-5 { margin-left: 40rpx; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: 8rpx; }
.mr-2 { margin-right: 16rpx; }
.mr-3 { margin-right: 24rpx; }
.mr-4 { margin-right: 32rpx; }
.mr-5 { margin-right: 40rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 8rpx; }
.p-2 { padding: 16rpx; }
.p-3 { padding: 24rpx; }
.p-4 { padding: 32rpx; }
.p-5 { padding: 40rpx; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 8rpx; }
.pt-2 { padding-top: 16rpx; }
.pt-3 { padding-top: 24rpx; }
.pt-4 { padding-top: 32rpx; }
.pt-5 { padding-top: 40rpx; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 8rpx; }
.pb-2 { padding-bottom: 16rpx; }
.pb-3 { padding-bottom: 24rpx; }
.pb-4 { padding-bottom: 32rpx; }
.pb-5 { padding-bottom: 40rpx; }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: 8rpx; }
.pl-2 { padding-left: 16rpx; }
.pl-3 { padding-left: 24rpx; }
.pl-4 { padding-left: 32rpx; }
.pl-5 { padding-left: 40rpx; }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: 8rpx; }
.pr-2 { padding-right: 16rpx; }
.pr-3 { padding-right: 24rpx; }
.pr-4 { padding-right: 32rpx; }
.pr-5 { padding-right: 40rpx; }

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
  cursor: pointer;
  transition: all 0.3s;
  text-align: center;
  line-height: 1;
}

.btn-primary {
  background-color: #1890ff;
  color: #ffffff;
}

.btn-primary:active {
  background-color: #096dd9;
}

.btn-success {
  background-color: #52c41a;
  color: #ffffff;
}

.btn-success:active {
  background-color: #389e0d;
}

.btn-warning {
  background-color: #faad14;
  color: #ffffff;
}

.btn-warning:active {
  background-color: #d48806;
}

.btn-danger {
  background-color: #f5222d;
  color: #ffffff;
}

.btn-danger:active {
  background-color: #cf1322;
}

.btn-default {
  background-color: #ffffff;
  color: #333333;
  border: 2rpx solid #d9d9d9;
}

.btn-default:active {
  background-color: #f5f5f5;
}

.btn-small {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.btn-large {
  padding: 20rpx 40rpx;
  font-size: 32rpx;
}

.btn-block {
  width: 100%;
  display: block;
}

.btn-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 输入框样式 */
.input {
  padding: 16rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #ffffff;
  width: 100%;
}

.input:focus {
  border-color: #1890ff;
  outline: none;
}

.input-group {
  margin-bottom: 24rpx;
}

.input-label {
  display: block;
  margin-bottom: 8rpx;
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

/* 状态指示器 */
.status-dot {
  display: inline-block;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

.status-online {
  background-color: #52c41a;
}

.status-offline {
  background-color: #f5222d;
}

.status-warning {
  background-color: #faad14;
}

/* 列表样式 */
.list-item {
  background-color: #ffffff;
  padding: 24rpx;
  margin-bottom: 2rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.list-item:first-child {
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
}

.list-item:last-child {
  border-bottom-left-radius: 16rpx;
  border-bottom-right-radius: 16rpx;
  margin-bottom: 20rpx;
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 8rpx;
}

.list-item-desc {
  font-size: 24rpx;
  color: #999999;
}

.list-item-action {
  margin-left: 16rpx;
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  color: #999999;
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  color: #999999;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 16rpx;
}

.empty-text {
  font-size: 28rpx;
}

/* 分割线 */
.divider {
  height: 2rpx;
  background-color: #f0f0f0;
  margin: 20rpx 0;
}

/* 标签 */
.tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  font-size: 20rpx;
  border-radius: 4rpx;
  color: #666666;
  background-color: #f5f5f5;
}

.tag-primary {
  color: #1890ff;
  background-color: #e6f7ff;
}

.tag-success {
  color: #52c41a;
  background-color: #f6ffed;
}

.tag-warning {
  color: #faad14;
  background-color: #fffbe6;
}

.tag-danger {
  color: #f5222d;
  background-color: #fff2f0;
}

/* 动画效果类 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.fade-out {
  animation: fadeOut 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.slide-down {
  animation: slideDown 0.3s ease-out;
}

.bounce {
  animation: bounce 0.6s ease-in-out;
}

.pulse {
  animation: pulse 2s infinite;
}

.rotate {
  animation: rotate 1s linear infinite;
}

/* 动画关键帧 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes slideUp {
  from { transform: translateY(100%); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
  from { transform: translateY(-100%); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translate3d(0, 0, 0); }
  40%, 43% { transform: translate3d(0, -30rpx, 0); }
  70% { transform: translate3d(0, -15rpx, 0); }
  90% { transform: translate3d(0, -4rpx, 0); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 过渡效果类 */
.transition-all {
  transition: all 0.3s ease;
}

.transition-colors {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

.transition-transform {
  transition: transform 0.3s ease;
}

/* 显示/隐藏工具类 */
.show {
  display: block !important;
}

.hide {
  display: none !important;
}

.invisible {
  visibility: hidden;
}

.visible {
  visibility: visible;
}

/* 透明度工具类 */
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* 位置工具类 */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* 层级工具类 */
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

/* 宽度工具类 */
.w-full { width: 100%; }
.w-auto { width: auto; }
.w-screen { width: 100vw; }

/* 高度工具类 */
.h-full { height: 100%; }
.h-auto { height: auto; }
.h-screen { height: 100vh; }

/* 圆角工具类 */
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: 50%; }

/* 阴影工具类 */
.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

/* 边框工具类 */
.border-0 { border-width: 0; }
.border { border-width: 1rpx; }
.border-2 { border-width: 2rpx; }
.border-4 { border-width: 4rpx; }

.border-solid { border-style: solid; }
.border-dashed { border-style: dashed; }
.border-dotted { border-style: dotted; }

.border-light { border-color: var(--border-light); }
.border-medium { border-color: var(--border-medium); }
.border-dark { border-color: var(--border-dark); }

/* 溢出处理 */
.overflow-hidden { overflow: hidden; }
.overflow-visible { overflow: visible; }
.overflow-scroll { overflow: scroll; }
.overflow-auto { overflow: auto; }

/* 文本溢出 */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 用户选择 */
.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }

/* 指针事件 */
.pointer-events-none { pointer-events: none; }
.pointer-events-auto { pointer-events: auto; }

/* 光标样式 */
.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }
.cursor-default { cursor: default; }

/* 智能照明系统专用样式 */
.device-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-lg);
  margin-bottom: var(--space-md);
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-light);
  transition: all 0.3s ease;
}

.device-card:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-md);
}

.device-status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: 20rpx;
  font-weight: 500;
}

.device-status.online {
  background-color: #f6ffed;
  color: var(--success-color);
}

.device-status.offline {
  background-color: #fff2f0;
  color: var(--danger-color);
}

.device-status.warning {
  background-color: #fffbe6;
  color: var(--warning-color);
}

.energy-meter {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: var(--radius-xl);
  padding: var(--space-lg);
  margin-bottom: var(--space-md);
}

.lighting-control {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-lg);
  box-shadow: var(--shadow-sm);
}

.brightness-slider {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 8rpx;
  border-radius: var(--radius-sm);
  background: var(--bg-tertiary);
  outline: none;
}

.brightness-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  box-shadow: var(--shadow-sm);
}

/* 动画效果类 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.fade-out {
  animation: fadeOut 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.slide-down {
  animation: slideDown 0.3s ease-out;
}

.bounce {
  animation: bounce 0.6s ease-in-out;
}

.pulse {
  animation: pulse 2s infinite;
}

.rotate {
  animation: rotate 1s linear infinite;
}

/* 动画关键帧 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes slideUp {
  from { transform: translateY(100%); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
  from { transform: translateY(-100%); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translate3d(0, 0, 0); }
  40%, 43% { transform: translate3d(0, -30rpx, 0); }
  70% { transform: translate3d(0, -15rpx, 0); }
  90% { transform: translate3d(0, -4rpx, 0); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 过渡效果 */
.transition-all {
  transition: all 0.3s ease;
}

.transition-colors {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

.transition-transform {
  transition: transform 0.3s ease;
}

/* 显示/隐藏工具类 */
.show {
  display: block !important;
}

.hide {
  display: none !important;
}

.invisible {
  visibility: hidden;
}

.visible {
  visibility: visible;
}

/* 位置工具类 */
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.sticky {
  position: sticky;
}

/* 层级工具类 */
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }
.z-999 { z-index: 999; }
.z-9999 { z-index: 9999; }

/* 宽高工具类 */
.w-full { width: 100%; }
.w-auto { width: auto; }
.w-screen { width: 100vw; }
.h-full { height: 100%; }
.h-auto { height: auto; }
.h-screen { height: 100vh; }

/* 圆角工具类 */
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: var(--radius-small); }
.rounded { border-radius: var(--radius-medium); }
.rounded-lg { border-radius: var(--radius-large); }
.rounded-full { border-radius: var(--radius-round); }

/* 阴影工具类 */
.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: var(--shadow-light); }
.shadow { box-shadow: var(--shadow-medium); }
.shadow-lg { box-shadow: var(--shadow-heavy); }

/* 边框工具类 */
.border-0 { border-width: 0; }
.border { border-width: 2rpx; border-style: solid; border-color: var(--border-light); }
.border-2 { border-width: 4rpx; border-style: solid; border-color: var(--border-light); }
.border-t { border-top: 2rpx solid var(--border-light); }
.border-b { border-bottom: 2rpx solid var(--border-light); }
.border-l { border-left: 2rpx solid var(--border-light); }
.border-r { border-right: 2rpx solid var(--border-light); }

/* 溢出处理 */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-hidden { overflow-y: hidden; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }

/* 文本溢出 */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-ellipsis-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 光标样式 */
.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }
.cursor-default { cursor: default; }

/* 用户选择 */
.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }

/* 响应式设计 */
@media screen and (min-width: 768px) {
  .container {
    max-width: 1200rpx;
    margin: 0 auto;
  }
  
  .page-container {
    max-width: 1200rpx;
    margin: 0 auto;
  }
  
  /* 平板端隐藏/显示 */
  .md-show { display: block !important; }
  .md-hide { display: none !important; }
}

@media screen and (max-width: 767px) {
  /* 移动端隐藏/显示 */
  .mobile-show { display: block !important; }
  .mobile-hide { display: none !important; }
}

/* 安全区域适配 */
.safe-area-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}

/* 主题色彩应用类 */
.bg-primary { background-color: var(--primary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-danger { background-color: var(--danger-color); }
.bg-info { background-color: var(--info-color); }

.text-theme-primary { color: var(--primary-color); }
.text-theme-success { color: var(--success-color); }
.text-theme-warning { color: var(--warning-color); }
.text-theme-danger { color: var(--danger-color); }
.text-theme-info { color: var(--info-color); }

.border-primary { border-color: var(--primary-color); }
.border-success { border-color: var(--success-color); }
.border-warning { border-color: var(--warning-color); }
.border-danger { border-color: var(--danger-color); }
.border-info { border-color: var(--info-color); }
  
  /* 平板和桌面端优化 */
  .device-card {
    margin-bottom: var(--space-lg);
  }
  
  .grid-cols-2 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-md);
  }
  
  .grid-cols-3 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-md);
  }

@media screen and (min-width: 1024px) {
  .grid-cols-4 {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-md);
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-tertiary: #999999;
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #404040;
    --border-light: #404040;
    --border-medium: #555555;
    --border-dark: #666666;
  }
}