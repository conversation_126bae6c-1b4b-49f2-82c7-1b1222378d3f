<template>
	<view class="dashboard-container">
		<!-- 未登录用户的介绍页面 -->
		<view v-if="!isLoggedIn" class="guest-container">
			<!-- 节能灯介绍动画 -->
			<view class="intro-animation">
				<view class="logo-container">
					<view class="logo-circle">
						<text class="logo-icon">💡</text>
					</view>
					<view class="logo-waves">
						<view class="wave wave-1"></view>
						<view class="wave wave-2"></view>
						<view class="wave wave-3"></view>
					</view>
				</view>
			</view>

			<!-- 介绍内容 -->
			<view class="intro-content">
				<view class="intro-title">智能节能照明系统</view>
				<view class="intro-subtitle">让每一度电都发挥最大价值</view>

				<view class="feature-list">
					<view class="feature-item">
						<text class="feature-icon">🌱</text>
						<text class="feature-text">智能节能，降低30%电耗</text>
					</view>
					<view class="feature-item">
						<text class="feature-icon">📱</text>
						<text class="feature-text">远程控制，随时随地管理</text>
					</view>
					<view class="feature-item">
						<text class="feature-icon">📊</text>
						<text class="feature-text">数据分析，优化用电策略</text>
					</view>
					<view class="feature-item">
						<text class="feature-icon">⚡</text>
						<text class="feature-text">实时监控，预防设备故障</text>
					</view>
				</view>

				<!-- 登录按钮 -->
				<view class="login-actions">
					<button class="login-btn primary" @tap="goToLogin">立即登录</button>
					<button class="login-btn secondary" @tap="goToRegister">免费注册</button>
				</view>
			</view>
		</view>

		<!-- 已登录用户的仪表板 -->
		<view v-else class="dashboard-content">
			<!-- 顶部状态栏 -->
			<view class="status-bar">
				<view class="status-item">
					<view class="status-dot status-online"></view>
					<text class="status-text">系统正常</text>
				</view>
				<view class="status-item">
					<text class="status-time">{{ currentTime }}</text>
				</view>
			</view>

			<!-- 时间范围筛选器 -->
			<view class="card ">
				<view class="time-filter-title">数据统计</view>
				<view class="time-filter-tabs">
					<view class="time-filter-tab" :class="{ active: selectedTimeRange === item.value }"
						v-for="(item, index) in timeRangeOptions" :key="index" @tap="handleTimeRangeChange(item.value)">
						{{ item.label }}
					</view>
				</view>
				<!-- 统计卡片 -->
				<view class="stats-grid">
					<view class="stats-card" v-for="(item, index) in statsData" :key="index">
						<view class="stats-icon" :class="item.iconClass">
							<text class="iconfont" :class="item.icon"></text>
						</view>
						<view class="stats-content">
							<view class="stats-value">{{ item.value }}</view>
							<view class="stats-label">{{ item.label }}</view>
							<view class="stats-change" :class="item.changeClass">
								{{ item.change }}
							</view>
						</view>
					</view>
				</view>
			</view>



			<!-- 设备状态概览 -->
			<view class="card">
				<view class="card-header">
					<view class="card-title">设备状态概览</view>
					<view class="card-action" @tap="refreshDeviceStatus">
						<text class="refresh-icon">🔄</text>
					</view>
				</view>
				<view class="device-overview">
					<view class="device-chart">
						<!-- 设备状态饼图 -->
						<view class="chart-container">
							<view class="chart-center">
								<view class="chart-total">{{ deviceSummary.total }}</view>
								<view class="chart-label">总设备</view>
							</view>
						</view>
					</view>
					<view class="device-legend">
						<view class="legend-item" v-for="(item, index) in deviceSummary.statusList" :key="index">
							<view class="legend-dot" :style="{backgroundColor: item.color}"></view>
							<view class="legend-text">
								<view class="legend-label">{{ item.label }}</view>
								<view class="legend-value">{{ item.count }}</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 实时监控 -->
			<view class="card">
				<view class="card-header">
					<view class="card-title">实时监控</view>
					<view class="card-action">
						<text class="live-indicator">● LIVE</text>
					</view>
				</view>
				<view class="monitor-grid">
					<view class="monitor-item" v-for="(item, index) in monitorData" :key="index">
						<view class="monitor-icon" :class="item.iconClass">
							<text>{{ item.icon }}</text>
						</view>
						<view class="monitor-content">
							<view class="monitor-value">{{ item.value }}</view>
							<view class="monitor-label">{{ item.label }}</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 最近告警 -->
			<view class="card">
				<view class="card-header">
					<view class="card-title">最近告警</view>
					<view class="card-action" @tap="viewAllAlerts">
						<text class="link-text">查看全部</text>
					</view>
				</view>
				<view class="alert-list">
					<view class="alert-item" v-for="(item, index) in recentAlerts" :key="index"
						@tap="viewAlertDetail(item)">
						<view class="alert-icon" :class="item.levelClass">
							<text>{{ item.icon }}</text>
						</view>
						<view class="alert-content">
							<view class="alert-title">{{ item.title }}</view>
							<view class="alert-desc">{{ item.description }}</view>
							<view class="alert-time">{{ formatTime(item.createTime) }}</view>
						</view>
						<view class="alert-status" :class="item.statusClass">
							{{ item.status }}
						</view>
					</view>
					<view v-if="recentAlerts.length === 0" class="empty">
						<view class="empty-icon">📋</view>
						<view class="empty-text">暂无告警信息</view>
					</view>
				</view>
			</view>

			<!-- 快捷操作 -->
			<view class="card">
				<view class="card-header">
					<view class="card-title">快捷操作</view>
				</view>
				<view class="quick-actions">
					<view class="action-item" v-for="(item, index) in quickActions" :key="index"
						@tap="handleQuickAction(item)">
						<view class="action-icon" :class="item.iconClass">
							<text>{{ item.icon }}</text>
						</view>
						<view class="action-label">{{ item.label }}</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import { handleError, withRetry } from '@/utils/errorHandler.js'
import api from '@/api/index.js'

export default {
	computed: {
		...mapGetters('user', ['isLoggedIn']),
		...mapGetters('device', ['onlineDeviceCount', 'offlineDeviceCount', 'faultDeviceCount', 'totalDeviceCount', 'onlineRate']),
		...mapGetters('energy', ['formattedEnergyStats', 'formattedRealtimeData']),
		...mapGetters('fault', ['pendingAlertsCount', 'formattedRecentAlerts'])
	},
	
	data() {
			return {
				currentTime: '',
				timer: null,
				loading: false,
				// 时间范围筛选相关
				selectedTimeRange: 'today', // 当前选中的时间范围
				timeRangeOptions: [
					{ label: '当日', value: 'today' },
					{ label: '近一周', value: 'week' },
					{ label: '近一月', value: 'month' },
					{ label: '近一年', value: 'year' }
				],
			// 统计数据
			statsData: [
				{
					icon: '💡',
					iconClass: 'icon-primary',
					value: '0',
					label: '在线设备',
					change: '0',
					changeClass: 'change-up'
				},
				{
					icon: '⚡',
					iconClass: 'icon-success',
					value: '0kW',
					label: '当前功耗',
					change: '0kW',
					changeClass: 'change-down'
				},
				{
					icon: '⚠️',
					iconClass: 'icon-warning',
					value: '0',
					label: '待处理告警',
					change: '0',
					changeClass: 'change-up'
				},
				{
					icon: '🌱',
					iconClass: 'icon-info',
					value: '0%',
					label: '节能率',
					change: '0%',
					changeClass: 'change-down'
				}
			],
			// 设备概览
			deviceSummary: {
				total: 0,
				online: 0,
				offline: 0,
				fault: 0,
				onlineRate: 0,
				statusList: [
					{ label: '在线', count: 0, color: '#52c41a' },
					{ label: '离线', count: 0, color: '#f5222d' },
					{ label: '故障', count: 0, color: '#faad14' }
				]
			},
			// 实时监控数据
			monitorData: [
				{
					icon: '⚡',
					iconClass: 'monitor-power',
					value: '0kW',
					label: '实时功率'
				},
				{
					icon: '💡',
					iconClass: 'monitor-light',
					value: '0',
					label: '活跃灯具'
				},
				{
					icon: '🌡️',
					iconClass: 'monitor-temp',
					value: '0°C',
					label: '平均温度'
				},
				{
					icon: '💰',
					iconClass: 'monitor-cost',
					value: '¥0',
					label: '今日电费'
				}
			],
			// 最近告警
			recentAlerts: [
				{
					id: 1,
					title: '设备离线告警',
					description: '会议室A-001照明设备失去连接',
					level: 'high',
					levelClass: 'alert-high',
					icon: '🚨',
					status: '待处理',
					statusClass: 'status-pending',
					createTime: new Date(Date.now() - 10 * 60 * 1000)
				},
				{
					id: 2,
					title: '功耗异常告警',
					description: '办公区B-205功耗超出正常范围',
					level: 'medium',
					levelClass: 'alert-medium',
					icon: '⚠️',
					status: '处理中',
					statusClass: 'status-processing',
					createTime: new Date(Date.now() - 30 * 60 * 1000)
				},
				{
					id: 3,
					title: '定时任务执行失败',
					description: '夜间自动关灯任务执行异常',
					level: 'low',
					levelClass: 'alert-low',
					icon: '💡',
					status: '已处理',
					statusClass: 'status-resolved',
					createTime: new Date(Date.now() - 2 * 60 * 60 * 1000)
				}
			],
			// 快捷操作
			quickActions: [
				{
					icon: '💡',
					iconClass: 'action-light',
					label: '一键开灯',
					action: 'turnOnAll'
				},
				{
					icon: '🌙',
					iconClass: 'action-night',
					label: '夜间模式',
					action: 'nightMode'
				},
				{
					icon: '🔧',
					iconClass: 'action-maintain',
					label: '设备检测',
					action: 'deviceCheck'
				},
				{
					icon: '📊',
					iconClass: 'action-report',
					label: '生成报告',
					action: 'generateReport'
				},
				{
					icon: '🎨',
					iconClass: 'action-test',
					label: '图标测试',
					action: 'iconTest'
				},
				{
					icon: '🐛',
					iconClass: 'action-debug',
					label: '错误日志',
					action: 'errorLogs'
				},
				{
					icon: '🔍',
					iconClass: 'action-test',
					label: '照明调试',
					action: 'lightingDebug'
				},
				{
					icon: '🧪',
					iconClass: 'action-test',
					label: '照明测试',
					action: 'lightingTest'
				}
			],
			// 错误处理相关
			lastErrorTime: null
		}
	},
	
	onLoad() {
		this.initPage()
	},
	
	onShow() {
		this.startTimer()
		this.loadDashboardData()
	},
	
	onHide() {
		this.stopTimer()
	},
	
	onUnload() {
		this.stopTimer()
	},
	
	methods: {
		...mapActions('device', ['getDeviceStats']),
		...mapActions('energy', ['getEnergyStats', 'refreshRealtimeData']),
		...mapActions('fault', ['getFaultList', 'getFaultStats']),
		
		// 跳转到登录页面
		goToLogin() {
			uni.navigateTo({
				url: '/pages/user/login'
			})
		},
		
		// 跳转到注册页面
		goToRegister() {
			uni.navigateTo({
				url: '/pages/user/register'
			})
		},
		
		/**
		 * 处理时间范围切换
		 * @param {string} timeRange 时间范围值
		 */
		handleTimeRangeChange(timeRange) {
			if (this.selectedTimeRange === timeRange) {
				return
			}
			
			this.selectedTimeRange = timeRange
			
			// 重新加载数据
			this.loadDashboardData()
		},
		
		/**
		 * 根据时间范围获取开始和结束时间
		 * @param {string} timeRange 时间范围
		 * @returns {Object} 包含startTime和endTime的对象
		 */
		getTimeRangeParams(timeRange) {
			const now = new Date()
			const endTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
			let startTime
			
			switch (timeRange) {
				case 'today':
					// 当日：从今天00:00:00开始
					startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0)
					break
				case 'week':
					// 近一周：从7天前00:00:00开始
					startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
					startTime.setHours(0, 0, 0, 0)
					break
				case 'month':
					// 近一月：从30天前00:00:00开始
					startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
					startTime.setHours(0, 0, 0, 0)
					break
				case 'year':
					// 近一年：从365天前00:00:00开始
					startTime = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
					startTime.setHours(0, 0, 0, 0)
					break
				default:
					// 默认为当日
					startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0)
			}
			return {
				StartTime: this.formatDate(startTime, 'YYYY-MM-DD HH:mm:ss'),
				EndTime:this.formatDate(endTime, 'YYYY-MM-DD HH:mm:ss')
			}
		},
		// 初始化页面
		initPage() {
			this.updateCurrentTime()
			this.loadDashboardData()
		},
		
		// 开始定时器
		startTimer() {
			this.timer = setInterval(() => {
				this.updateCurrentTime()
				this.refreshMonitorData()
			}, 1000)
		},
		
		// 停止定时器
		stopTimer() {
			if (this.timer) {
				clearInterval(this.timer)
				this.timer = null
			}
		},
		
		// 更新当前时间
		updateCurrentTime() {
			const now = new Date()
			this.currentTime = this.formatDate(now, 'MM-DD HH:mm:ss')
		},
		
		/**
		 * 加载仪表板数据 - 使用Vuex优化版本
		 * @param {boolean} showLoading 是否显示加载提示
		 */
		async loadDashboardData(showLoading = true) {
			// 如果用户未登录，不加载数据
			if (!this.isLoggedIn) {
				return
			}
			try {
				if (showLoading) {
					uni.showLoading({ title: '加载数据中...' })
				}
				// 获取时间范围参数
				const timeParams = this.getTimeRangeParams(this.selectedTimeRange)
				// 使用Vuex actions并行加载数据
				const results = await Promise.allSettled([
					this.getDeviceStats(timeParams),
					this.getEnergyStats(timeParams),
					this.getFaultList({ 
						status: 'pending', 
						pageSize: 3,
						...timeParams
					})
				])
				
				// 处理结果
				const [deviceResult, energyResult, faultResult] = results
				
				// 更新统计数据
				if (deviceResult.status === 'fulfilled' && energyResult.status === 'fulfilled') {
					this.updateStatsDataFromVuex()
				}
				
				// 更新设备概览
				if (deviceResult.status === 'fulfilled') {
					this.updateDeviceSummaryFromVuex()
				}
				
				// 更新告警列表 - 直接使用Vuex getter
				this.recentAlerts = this.formattedRecentAlerts
				
				// 检查是否有失败的请求
				const failedRequests = results.filter(result => result.status === 'rejected')
				if (failedRequests.length > 0) {
					console.warn(`${failedRequests.length}个请求失败，使用缓存或默认数据`)
				}
				
				if (showLoading) {
					uni.hideLoading()
				}
				
				// 显示加载结果提示
				if (failedRequests.length === 0) {
					// 全部成功，不显示提示
				} else if (failedRequests.length < results.length) {
					uni.showToast({
						title: '部分数据加载失败',
						icon: 'none',
						duration: 2000
					})
				} else {
					throw new Error('所有数据加载失败')
				}
				
			} catch (error) {
				console.error('加载仪表板数据失败:', error)
				
				if (showLoading) {
					uni.hideLoading()
				}
				
				// 使用错误处理器
				handleError(error, {
					url: 'loadDashboardData',
					method: 'GET',
					params: this.selectedTimeRange
				}, {
					showToast: true,
					enableRetry: true,
					retryCallback: () => this.loadDashboardData(false)
				})
			}
		},
		
		// 刷新设备状态 - 使用Vuex优化版本
		async refreshDeviceStatus() {
			try {
				uni.showToast({
					title: '刷新中...',
					icon: 'loading',
					duration: 1000
				})
				
				// 使用Vuex action重新加载设备统计数据
				const timeParams = this.getTimeRangeParams(this.selectedTimeRange)
				await this.getDeviceStats(timeParams)
				
				// 更新设备概览数据
				this.updateDeviceSummaryFromVuex()
				
				uni.showToast({
					title: '刷新成功',
					icon: 'success'
				})
			} catch (error) {
				console.error('刷新设备状态失败:', error)
				
				// 使用错误处理器
				const { handleError } = require('@/utils/errorHandler')
				handleError(error, {
					url: 'refreshDeviceStatus',
					method: 'GET'
				}, {
					showToast: true,
					enableRetry: true,
					retryCallback: () => this.refreshDeviceStatus()
				})
			}
		},
		
		/**
		 * 刷新监控数据 - 使用Vuex优化版本
		 */
		async refreshMonitorData() {
			try {
				// 获取时间范围参数
				const timeParams = this.getTimeRangeParams(this.selectedTimeRange)
				
				// 使用Vuex action刷新实时数据
				await this.refreshRealtimeData(timeParams)
				
				// 从Vuex获取格式化的实时数据
				const realtimeData = this.formattedRealtimeData
				
				this.monitorData[0].value = realtimeData.realtimePower
				this.monitorData[1].value = realtimeData.activeLights
				this.monitorData[2].value = realtimeData.avgTemperature
				
				// 根据时间范围调整成本显示
				const costLabel = this.getCostLabel(this.selectedTimeRange)
				this.monitorData[3].value = realtimeData.totalCost
				this.monitorData[3].label = costLabel
				
			} catch (error) {
				// 使用错误处理器进行统一错误处理
				const { handleError } = require('@/utils/errorHandler')
				handleError(error, {
					url: 'refreshMonitorData',
					method: 'GET',
					params: this.selectedTimeRange
				}, {
					showToast: false, // 静默失败，使用缓存数据
					enableRetry: false,
					fallbackData: () => {
						// 使用模拟数据作为降级方案
						this.monitorData[0].value = `${Math.floor(Math.random() * 50 + 10)}kW`
						this.monitorData[1].value = Math.floor(Math.random() * 100 + 50).toString()
						this.monitorData[2].value = `${Math.floor(Math.random() * 10 + 20)}°C`
						this.monitorData[3].value = `¥${Math.floor(Math.random() * 1000 + 500)}`
					}
				})
			}
		},
		
		/**
		 * 根据时间范围获取成本标签
		 * @param {string} timeRange 时间范围
		 * @returns {string} 成本标签
		 */
		getCostLabel(timeRange) {
			switch (timeRange) {
				case 'today':
					return '今日电费'
				case 'week':
					return '本周电费'
				case 'month':
					return '本月电费'
				case 'year':
					return '本年电费'
				default:
					return '今日电费'
			}
		},
		
		// 查看所有告警
		viewAllAlerts() {
			this.navigateTo('/pages/fault/list')
		},
		
		// 查看告警详情
		viewAlertDetail(alert) {
			this.navigateTo('/pages/fault/detail', { id: alert.id })
		},
		
		// 处理快捷操作
		async handleQuickAction(action) {
			try {
				switch (action.action) {
					case 'turnOnAll':
						await this.handleTurnOnAll()
						break
					case 'nightMode':
						await this.handleNightMode()
						break
					case 'deviceCheck':
						this.navigateTo('/pages/device/list')
						break
					case 'generateReport':
					await this.handleGenerateReport()
					break
				case 'iconTest':
					this.navigateTo('/pages/test/icon-test')
					break
				case 'errorLogs':
					this.navigateTo('/pages/debug/errorLogs')
					break
				case 'lightingDebug':
					this.navigateTo('/pages/lighting/control-simple')
					break
				case 'lightingTest':
					this.navigateTo('/pages/lighting/control-test')
					break
				default:
					this.showError('功能暂未开放')
				}
			} catch (error) {
				console.error('快捷操作失败:', error)
				this.showError('操作失败')
			}
		},
		
		// 一键开灯
		async handleTurnOnAll() {
			uni.showModal({
				title: '确认操作',
				content: '确定要开启所有照明设备吗？',
				success: async (res) => {
					if (res.confirm) {
						try {
							uni.showLoading({ title: '开启中...' })
							await api.lighting.globalControl({ action: 'turnOnAll' })
							uni.hideLoading()
							uni.showToast({
								title: '所有设备已开启',
								icon: 'success'
							})
							// 刷新数据
							this.loadDashboardData()
						} catch (error) {
							uni.hideLoading()
							console.error('一键开灯失败:', error)
							uni.showToast({
								title: '操作失败',
								icon: 'error'
							})
						}
					}
				}
			})
		},
		
		// 夜间模式
		async handleNightMode() {
			uni.showModal({
				title: '确认操作',
				content: '确定要切换到夜间模式吗？',
				success: async (res) => {
					if (res.confirm) {
						try {
							uni.showLoading({ title: '切换中...' })
							await api.lighting.applyScene({ sceneId: 'night_mode' })
							uni.hideLoading()
							uni.showToast({
								title: '已切换到夜间模式',
								icon: 'success'
							})
							// 刷新数据
							this.loadDashboardData()
						} catch (error) {
							uni.hideLoading()
							console.error('切换夜间模式失败:', error)
							uni.showToast({
								title: '切换失败',
								icon: 'error'
							})
						}
					}
				}
			})
		},
		
		// 生成报告
		async handleGenerateReport() {
			uni.showModal({
				title: '生成报告',
				content: '确定要生成今日能耗报告吗？',
				success: async (res) => {
					if (res.confirm) {
						try {
							uni.showLoading({ title: '生成中...' })
							// 跳转到能耗报告页面
							uni.navigateTo({
								url: '/pages/energy/report'
							})
							uni.hideLoading()
						} catch (error) {
							uni.hideLoading()
							console.error('跳转失败:', error)
							uni.showToast({
								title: '操作失败',
								icon: 'error'
							})
						}
					}
				}
			})
		},
		
		// 从Vuex更新统计数据
		updateStatsDataFromVuex() {
			this.statsData[0].value = this.onlineDeviceCount.toString()
			this.statsData[0].change = `+${Math.floor(Math.random() * 5)}`
			
			const energyStats = this.formattedEnergyStats
			this.statsData[1].value = energyStats.realtimePower
			this.statsData[1].change = `${Math.random() > 0.5 ? '+' : '-'}${Math.floor(Math.random() * 10)}kW`
			
			this.statsData[2].value = this.pendingAlertsCount.toString()
			this.statsData[2].change = this.pendingAlertsCount > 0 ? `+${this.pendingAlertsCount}` : '0'
			
			this.statsData[3].value = energyStats.energySavingRate
			this.statsData[3].change = `+${Math.floor(Math.random() * 5)}%`
		},
		
		// 从Vuex更新设备概览
		updateDeviceSummaryFromVuex() {
			this.deviceSummary.total = this.totalDeviceCount
			this.deviceSummary.online = this.onlineDeviceCount
			this.deviceSummary.offline = this.offlineDeviceCount
			this.deviceSummary.fault = this.faultDeviceCount
			this.deviceSummary.onlineRate = this.onlineRate
			
			// 更新状态列表
			if (this.deviceSummary.statusList && this.deviceSummary.statusList.length >= 3) {
				this.deviceSummary.statusList[0].count = this.onlineDeviceCount
				this.deviceSummary.statusList[1].count = this.offlineDeviceCount
				this.deviceSummary.statusList[2].count = this.faultDeviceCount
			}
		},
		
		// 更新设备概览
		updateDeviceSummary(data) {
			if (data) {
				this.deviceSummary.totalDevices = data.totalCount || 0
				this.deviceSummary.onlineDevices = data.onlineCount || 0
				this.deviceSummary.offlineDevices = data.offlineCount || 0
				this.deviceSummary.faultDevices = data.faultCount || 0
				
				// 更新状态列表
				if (this.deviceSummary.statusList && this.deviceSummary.statusList.length >= 3) {
					this.deviceSummary.statusList[0].count = data.onlineCount || 0
					this.deviceSummary.statusList[1].count = data.offlineCount || 0
					this.deviceSummary.statusList[2].count = data.faultCount || 0
				}
			}
		},
		
		// 更新告警列表
		updateRecentAlerts(alerts) {
			if (Array.isArray(alerts) && alerts.length > 0) {
				this.recentAlerts = alerts.slice(0, 3).map(alert => ({
					id: alert.id,
					title: alert.title || alert.description || '设备告警',
					description: alert.description || alert.message || alert.content,
					level: alert.level || 'medium',
					levelClass: `alert-${alert.level || 'medium'}`,
					icon: alert.level === 'high' ? '🚨' : alert.level === 'low' ? '💡' : '⚠️',
					status: alert.status === 'resolved' ? '已处理' : alert.status === 'processing' ? '处理中' : '待处理',
					statusClass: alert.status === 'resolved' ? 'status-resolved' : alert.status === 'processing' ? 'status-processing' : 'status-pending',
					createTime: new Date(alert.createTime || alert.createdAt || alert.time)
				}))
			}
		},
		
		// 导航到页面
		navigateTo(url, params = {}) {
			let fullUrl = url
			if (Object.keys(params).length > 0) {
				const query = Object.keys(params).map(key => `${key}=${params[key]}`).join('&')
				fullUrl += `?${query}`
			}
			uni.navigateTo({ url: fullUrl })
		},
		
		// 显示错误信息
		showError(message) {
			uni.showToast({
				title: message,
				icon: 'error'
			})
		},
		
		// 格式化日期
		formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
			const year = date.getFullYear()
			const month = String(date.getMonth() + 1).padStart(2, '0')
			const day = String(date.getDate()).padStart(2, '0')
			const hours = String(date.getHours()).padStart(2, '0')
			const minutes = String(date.getMinutes()).padStart(2, '0')
			const seconds = String(date.getSeconds()).padStart(2, '0')
			
			return format
				.replace('YYYY', year)
				.replace('MM', month)
				.replace('DD', day)
				.replace('HH', hours)
				.replace('mm', minutes)
				.replace('ss', seconds)
		},
		
		// 格式化时间
		formatTime(date) {
			return date.toLocaleTimeString('zh-CN', {
				hour: '2-digit',
				minute: '2-digit',
				second: '2-digit'
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.dashboard-container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

// 未登录用户介绍页面样式
.guest-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	min-height: 100vh;
	padding: 40rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.intro-animation {
	margin-bottom: 60rpx;
}

.logo-container {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 200rpx;
	height: 200rpx;
}

.logo-circle {
	position: relative;
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	animation: pulse 2s ease-in-out infinite;
	z-index: 2;
}

.logo-icon {
	font-size: 60rpx;
	animation: glow 2s ease-in-out infinite alternate;
}

.logo-waves {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.wave {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	border: 2rpx solid rgba(255, 255, 255, 0.3);
	border-radius: 50%;
	animation: wave 3s ease-in-out infinite;
}

.wave-1 {
	animation-delay: 0s;
}

.wave-2 {
	animation-delay: 1s;
}

.wave-3 {
	animation-delay: 2s;
}

@keyframes pulse {
	0%, 100% {
		transform: scale(1);
		box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
	}
	50% {
		transform: scale(1.05);
		box-shadow: 0 0 0 20rpx rgba(255, 255, 255, 0);
	}
}

@keyframes glow {
	0% {
		text-shadow: 0 0 10rpx rgba(255, 255, 255, 0.5);
	}
	100% {
		text-shadow: 0 0 20rpx rgba(255, 255, 255, 0.8), 0 0 30rpx rgba(255, 255, 255, 0.6);
	}
}

@keyframes wave {
	0% {
		transform: scale(1);
		opacity: 1;
	}
	100% {
		transform: scale(2);
		opacity: 0;
	}
}

.intro-content {
	text-align: center;
	max-width: 600rpx;
}

.intro-title {
	font-size: 48rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
	animation: fadeInUp 1s ease-out 0.5s both;
}

.intro-subtitle {
	font-size: 28rpx;
	opacity: 0.9;
	margin-bottom: 60rpx;
	animation: fadeInUp 1s ease-out 0.7s both;
}

.feature-list {
	margin-bottom: 80rpx;
}

.feature-item {
	display: flex;
	align-items: center;
	justify-content: flex-start;
	padding: 20rpx 0;
	animation: fadeInLeft 1s ease-out both;
}

.feature-item:nth-child(1) { animation-delay: 0.9s; }
.feature-item:nth-child(2) { animation-delay: 1.1s; }
.feature-item:nth-child(3) { animation-delay: 1.3s; }
.feature-item:nth-child(4) { animation-delay: 1.5s; }

.feature-icon {
	font-size: 32rpx;
	margin-right: 20rpx;
	width: 50rpx;
	text-align: center;
}

.feature-text {
	font-size: 28rpx;
	opacity: 0.9;
}

.login-actions {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	animation: fadeInUp 1s ease-out 1.7s both;
}

.login-btn {
	width: 100%;
	height: 88rpx;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: 600;
	border: none;
	transition: all 0.3s ease;
}

.login-btn.primary {
	background: rgba(255, 255, 255, 0.9);
	color: #667eea;
}

.login-btn.primary:active {
	background: rgba(255, 255, 255, 1);
	transform: scale(0.98);
}

.login-btn.secondary {
	background: transparent;
	color: white;
	border: 2rpx solid rgba(255, 255, 255, 0.6);
}

.login-btn.secondary:active {
	background: rgba(255, 255, 255, 0.1);
	transform: scale(0.98);
}

@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(60rpx);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes fadeInLeft {
	from {
		opacity: 0;
		transform: translateX(-60rpx);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

// 已登录用户仪表板样式
.dashboard-content {
	// 原有的仪表板样式保持不变
}

.time-filter-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 20rpx;
}

.time-filter-tabs {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
	background-color: #f5f5f5;
	border-radius: 12rpx;
	padding: 6rpx;
}

.time-filter-tab {
	flex: 1;
	height: 64rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 8rpx;
	font-size: 26rpx;
	color: #666666;
	transition: all 0.3s ease;
	position: relative;
}

.time-filter-tab.active {
	background-color: #ffffff;
	color: #1890ff;
	font-weight: 600;
	box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.2);
}

.time-filter-tab:not(.active):hover {
	color: #333333;
	background-color: rgba(255, 255, 255, 0.5);
}

// 状态栏
.status-bar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16rpx 24rpx;
	background-color: #ffffff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.status-item {
	display: flex;
	align-items: center;
}

.status-dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	margin-right: 8rpx;
}

.status-text {
	font-size: 28rpx;
	color: #333333;
}

.status-time {
	font-size: 24rpx;
	color: #999999;
}

// 统计卡片
.stats-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
	margin-top: 20rpx;
	margin-bottom: 20rpx;
}

.stats-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 24rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.stats-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
	font-size: 32rpx;
}

.icon-primary {
	background-color: #e6f7ff;
	color: #1890ff;
}

.icon-success {
	background-color: #f6ffed;
	color: #52c41a;
}

.icon-warning {
	background-color: #fffbe6;
	color: #faad14;
}

.icon-info {
	background-color: #e6fffb;
	color: #13c2c2;
}

.stats-content {
	flex: 1;
}

.stats-value {
	font-size: 36rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 4rpx;
}

.stats-label {
	font-size: 24rpx;
	color: #999999;
	margin-bottom: 4rpx;
}

.stats-change {
	font-size: 20rpx;
}

.change-up {
	color: #f5222d;
}

.change-down {
	color: #52c41a;
}

// 设备概览
.device-overview {
	display: flex;
	align-items: center;
}

.device-chart {
	flex: 1;
	margin-right: 40rpx;
}

.chart-container {
	width: 200rpx;
	height: 200rpx;
	border-radius: 50%;
	background: conic-gradient(#52c41a 0deg 312deg, #f5222d 312deg 351deg, #faad14 351deg 360deg);
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}

.chart-center {
	width: 120rpx;
	height: 120rpx;
	background-color: #ffffff;
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.chart-total {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.chart-label {
	font-size: 20rpx;
	color: #999999;
}

.device-legend {
	flex: 1;
}

.legend-item {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
}

.legend-dot {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	margin-right: 12rpx;
}

.legend-text {
	flex: 1;
}

.legend-label {
	font-size: 28rpx;
	color: #333333;
	margin-bottom: 4rpx;
}

.legend-value {
	font-size: 24rpx;
	color: #999999;
}

// 实时监控
.live-indicator {
	font-size: 20rpx;
	color: #f5222d;
	animation: blink 1s infinite;
}

@keyframes blink {
	0%, 50% { opacity: 1; }
	51%, 100% { opacity: 0.3; }
}

.monitor-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

.monitor-item {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 12rpx;
}

.monitor-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
	font-size: 24rpx;
}

.monitor-energy {
	background-color: #fff7e6;
}

.monitor-light {
	background-color: #f6ffed;
}

.monitor-temp {
	background-color: #e6f7ff;
}

.monitor-cost {
	background-color: #fff2f0;
}

.monitor-content {
	flex: 1;
}

.monitor-value {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 4rpx;
}

.monitor-label {
	font-size: 20rpx;
	color: #999999;
}

// 告警列表
.alert-list {
	max-height: 600rpx;
	overflow-y: auto;
}

.alert-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 2rpx solid #f0f0f0;
}

.alert-item:last-child {
	border-bottom: none;
}

.alert-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
	font-size: 24rpx;
}

.alert-high {
	background-color: #fff2f0;
}

.alert-medium {
	background-color: #fffbe6;
}

.alert-low {
	background-color: #e6f7ff;
}

.alert-content {
	flex: 1;
	margin-right: 16rpx;
}

.alert-title {
	font-size: 28rpx;
	color: #333333;
	margin-bottom: 4rpx;
}

.alert-desc {
	font-size: 24rpx;
	color: #666666;
	margin-bottom: 4rpx;
}

.alert-time {
	font-size: 20rpx;
	color: #999999;
}

.alert-status {
	padding: 4rpx 12rpx;
	border-radius: 4rpx;
	font-size: 20rpx;
}

.status-pending {
	background-color: #fff2f0;
	color: #f5222d;
}

.status-processing {
	background-color: #e6f7ff;
	color: #1890ff;
}

.status-resolved {
	background-color: #f6ffed;
	color: #52c41a;
}

// 快捷操作
.quick-actions {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 20rpx;
}

.action-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 24rpx 16rpx;
	background-color: #f8f9fa;
	border-radius: 12rpx;
	transition: all 0.3s;
}

.action-item:active {
	background-color: #e9ecef;
	transform: scale(0.95);
}

.action-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 12rpx;
	font-size: 24rpx;
	background-color: #ffffff;
}

.action-label {
	font-size: 24rpx;
	color: #333333;
	text-align: center;
}

.refresh-icon {
	font-size: 24rpx;
	color: #1890ff;
}

.link-text {
	font-size: 24rpx;
	color: #1890ff;
}
</style>