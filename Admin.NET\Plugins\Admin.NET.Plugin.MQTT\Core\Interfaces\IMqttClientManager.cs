using MQTTnet;
using MQTTnet.Protocol;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Admin.NET.Plugin.MQTT.Interfaces
{
    /// <summary>
    /// MQTT客户端管理器接口
    /// 定义MQTT连接管理、消息发布订阅的核心功能
    /// </summary>
    public interface IMqttClientManager : IDisposable
    {
        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        event EventHandler<MqttConnectionStatusChangedEventArgs> ConnectionStatusChanged;
        
        /// <summary>
        /// 消息接收事件
        /// </summary>
        event EventHandler<MqttMessageReceivedEventArgs> MessageReceived;
        
        /// <summary>
        /// 获取当前连接状态
        /// </summary>
        bool IsConnected { get; }
        
        /// <summary>
        /// 获取客户端ID
        /// </summary>
        string ClientId { get; }
        
        /// <summary>
        /// 启动MQTT客户端连接
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task StartAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 停止MQTT客户端连接
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task StopAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 订阅主题
        /// </summary>
        /// <param name="topic">主题</param>
        /// <param name="qos">服务质量等级</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task SubscribeAsync(string topic, MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtMostOnce, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 取消订阅主题
        /// </summary>
        /// <param name="topic">主题</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task UnsubscribeAsync(string topic, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 发布消息
        /// </summary>
        /// <param name="topic">主题</param>
        /// <param name="payload">消息内容</param>
        /// <param name="qos">服务质量等级</param>
        /// <param name="retain">是否保留消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task PublishAsync(string topic, object payload, MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtMostOnce, bool retain = false, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 发布原始字节消息
        /// </summary>
        /// <param name="topic">主题</param>
        /// <param name="payload">消息字节数组</param>
        /// <param name="qos">服务质量等级</param>
        /// <param name="retain">是否保留消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task PublishAsync(string topic, byte[] payload, MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtMostOnce, bool retain = false, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 获取连接统计信息
        /// </summary>
        /// <returns>连接统计信息</returns>
        MqttConnectionStats GetConnectionStats();
        
        /// <summary>
        /// 注册消息处理器
        /// </summary>
        /// <param name="topicPattern">主题模式</param>
        /// <param name="handler">消息处理器</param>
        /// <returns>处理器ID</returns>
        string RegisterMessageHandler(string topicPattern, Func<MqttApplicationMessage, Task> handler);
        
        /// <summary>
        /// 移除消息处理器
        /// </summary>
        /// <param name="handlerId">处理器ID</param>
        /// <returns>是否成功移除</returns>
        bool RemoveMessageHandler(string handlerId);
    }
    
    /// <summary>
    /// MQTT连接状态变化事件参数
    /// </summary>
    public class MqttConnectionStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 连接状态
        /// </summary>
        public bool IsConnected { get; set; }
        
        /// <summary>
        /// 状态变化原因
        /// </summary>
        public string Reason { get; set; }
        
        /// <summary>
        /// 异常信息（如果有）
        /// </summary>
        public Exception Exception { get; set; }
    }
    
    /// <summary>
    /// MQTT消息接收事件参数
    /// </summary>
    public class MqttMessageReceivedEventArgs : EventArgs
    {
        /// <summary>
        /// 接收到的消息
        /// </summary>
        public MqttApplicationMessage Message { get; set; }
        
        /// <summary>
        /// 接收时间
        /// </summary>
        public DateTime ReceivedAt { get; set; }
    }
    
    /// <summary>
    /// MQTT连接统计信息
    /// </summary>
    public class MqttConnectionStats
    {
        /// <summary>
        /// 连接时间
        /// </summary>
        public DateTime? ConnectedAt { get; set; }
        
        /// <summary>
        /// 已发送消息数
        /// </summary>
        public long MessagesSent { get; set; }
        
        /// <summary>
        /// 已接收消息数
        /// </summary>
        public long MessagesReceived { get; set; }
        
        /// <summary>
        /// 传输字节数
        /// </summary>
        public long BytesTransferred { get; set; }
        
        /// <summary>
        /// 重连次数
        /// </summary>
        public int ReconnectCount { get; set; }
        
        /// <summary>
        /// 最后心跳时间
        /// </summary>
        public DateTime? LastHeartbeat { get; set; }
        
        /// <summary>
        /// 活跃订阅数
        /// </summary>
        public int ActiveSubscriptions { get; set; }
        
        /// <summary>
        /// 活跃处理器数
        /// </summary>
        public int ActiveHandlers { get; set; }
    }
}