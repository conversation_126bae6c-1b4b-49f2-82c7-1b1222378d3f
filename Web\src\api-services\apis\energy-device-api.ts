/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AddEnergyDeviceInput } from '../models';
import { AdminResultBoolean } from '../models';
import { AdminResultByte } from '../models';
import { AdminResultEnergyDeviceDetailOutput } from '../models';
import { AdminResultEnergyDeviceStatOutput } from '../models';
import { AdminResultIActionResult } from '../models';
import { AdminResultInt32 } from '../models';
import { AdminResultInt64 } from '../models';
import { AdminResultListEnergyDeviceOutput } from '../models';
import { AdminResultListObject } from '../models';
import { AdminResultObject } from '../models';
import { AdminResultSqlSugarPagedListEnergyDeviceOutput } from '../models';
import { DeleteEnergyDeviceInput } from '../models';
import { DeviceControlInput } from '../models';
import { EnergyDeviceInput } from '../models';
import { EnergyDeviceStatusInput } from '../models';
import { Filter } from '../models';
import { FilterLogicEnum } from '../models';
import { FilterOperatorEnum } from '../models';
import { UpdateEnergyDeviceInput } from '../models';
/**
 * EnergyDeviceApi - axios parameter creator
 * @export
 */
export const EnergyDeviceApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加设备 🔖
         * @param {AddEnergyDeviceInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyDeviceAddPost: async (body?: AddEnergyDeviceInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyDevice/add`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 设备控制 🔖
         * @param {DeviceControlInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyDeviceControlDevicePost: async (body?: DeviceControlInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyDevice/controlDevice`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 删除设备 🔖
         * @param {DeleteEnergyDeviceInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyDeviceDeletePost: async (body?: DeleteEnergyDeviceInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyDevice/delete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取设备详情 🔖
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyDeviceDetailGet: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiEnergyDeviceDetailGet.');
            }
            const localVarPath = `/api/energyDevice/detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (id !== undefined) {
                localVarQueryParameter['Id'] = id;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取设备统计信息 🔖
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyDeviceDeviceStatGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyDevice/deviceStat`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 导出设备数据 🔖
         * @param {string} [deviceCode] 设备编码
         * @param {string} [deviceName] 设备名称
         * @param {string} [deviceType] 设备类型
         * @param {string} [deviceModel] 设备型号
         * @param {string} [location] 设备位置
         * @param {number} [groupId] 分组ID
         * @param {number} [status] 设备状态
         * @param {number} [onlineStatus] 在线状态
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyDeviceExportDataPost: async (deviceCode?: string, deviceName?: string, deviceType?: string, deviceModel?: string, location?: string, groupId?: number, status?: number, onlineStatus?: number, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyDevice/exportData`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (deviceCode !== undefined) {
                localVarQueryParameter['DeviceCode'] = deviceCode;
            }

            if (deviceName !== undefined) {
                localVarQueryParameter['DeviceName'] = deviceName;
            }

            if (deviceType !== undefined) {
                localVarQueryParameter['DeviceType'] = deviceType;
            }

            if (deviceModel !== undefined) {
                localVarQueryParameter['DeviceModel'] = deviceModel;
            }

            if (location !== undefined) {
                localVarQueryParameter['Location'] = location;
            }

            if (groupId !== undefined) {
                localVarQueryParameter['GroupId'] = groupId;
            }

            if (status !== undefined) {
                localVarQueryParameter['Status'] = status;
            }

            if (onlineStatus !== undefined) {
                localVarQueryParameter['OnlineStatus'] = onlineStatus;
            }

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            if (field !== undefined) {
                localVarQueryParameter['Field'] = field;
            }

            if (order !== undefined) {
                localVarQueryParameter['Order'] = order;
            }

            if (descStr !== undefined) {
                localVarQueryParameter['DescStr'] = descStr;
            }

            if (searchFields) {
                localVarQueryParameter['Search.Fields'] = searchFields;
            }

            if (searchKeyword !== undefined) {
                localVarQueryParameter['Search.Keyword'] = searchKeyword;
            }

            if (keyword !== undefined) {
                localVarQueryParameter['Keyword'] = keyword;
            }

            if (filterLogic !== undefined) {
                localVarQueryParameter['Filter.Logic'] = filterLogic;
            }

            if (filterFilters) {
                localVarQueryParameter['Filter.Filters'] = filterFilters;
            }

            if (filterField !== undefined) {
                localVarQueryParameter['Filter.Field'] = filterField;
            }

            if (filterOperator !== undefined) {
                localVarQueryParameter['Filter.Operator'] = filterOperator;
            }

            if (filterValue !== undefined) {
                localVarQueryParameter['Filter.Value'] = filterValue;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 导出设备数据到Excel 🔖
         * @param {EnergyDeviceInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyDeviceExportExcelPost: async (body?: EnergyDeviceInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyDevice/exportExcel`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取设备列表 🔖
         * @param {string} [deviceCode] 设备编码
         * @param {string} [deviceName] 设备名称
         * @param {string} [deviceType] 设备类型
         * @param {string} [deviceModel] 设备型号
         * @param {string} [location] 设备位置
         * @param {number} [groupId] 分组ID
         * @param {number} [status] 设备状态
         * @param {number} [onlineStatus] 在线状态
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyDeviceListGet: async (deviceCode?: string, deviceName?: string, deviceType?: string, deviceModel?: string, location?: string, groupId?: number, status?: number, onlineStatus?: number, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyDevice/list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (deviceCode !== undefined) {
                localVarQueryParameter['DeviceCode'] = deviceCode;
            }

            if (deviceName !== undefined) {
                localVarQueryParameter['DeviceName'] = deviceName;
            }

            if (deviceType !== undefined) {
                localVarQueryParameter['DeviceType'] = deviceType;
            }

            if (deviceModel !== undefined) {
                localVarQueryParameter['DeviceModel'] = deviceModel;
            }

            if (location !== undefined) {
                localVarQueryParameter['Location'] = location;
            }

            if (groupId !== undefined) {
                localVarQueryParameter['GroupId'] = groupId;
            }

            if (status !== undefined) {
                localVarQueryParameter['Status'] = status;
            }

            if (onlineStatus !== undefined) {
                localVarQueryParameter['OnlineStatus'] = onlineStatus;
            }

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            if (field !== undefined) {
                localVarQueryParameter['Field'] = field;
            }

            if (order !== undefined) {
                localVarQueryParameter['Order'] = order;
            }

            if (descStr !== undefined) {
                localVarQueryParameter['DescStr'] = descStr;
            }

            if (searchFields) {
                localVarQueryParameter['Search.Fields'] = searchFields;
            }

            if (searchKeyword !== undefined) {
                localVarQueryParameter['Search.Keyword'] = searchKeyword;
            }

            if (keyword !== undefined) {
                localVarQueryParameter['Keyword'] = keyword;
            }

            if (filterLogic !== undefined) {
                localVarQueryParameter['Filter.Logic'] = filterLogic;
            }

            if (filterFilters) {
                localVarQueryParameter['Filter.Filters'] = filterFilters;
            }

            if (filterField !== undefined) {
                localVarQueryParameter['Filter.Field'] = filterField;
            }

            if (filterOperator !== undefined) {
                localVarQueryParameter['Filter.Operator'] = filterOperator;
            }

            if (filterValue !== undefined) {
                localVarQueryParameter['Filter.Value'] = filterValue;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取设备分页列表 🔖
         * @param {string} [deviceCode] 设备编码
         * @param {string} [deviceName] 设备名称
         * @param {string} [deviceType] 设备类型
         * @param {string} [deviceModel] 设备型号
         * @param {string} [location] 设备位置
         * @param {number} [groupId] 分组ID
         * @param {number} [status] 设备状态
         * @param {number} [onlineStatus] 在线状态
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyDevicePageGet: async (deviceCode?: string, deviceName?: string, deviceType?: string, deviceModel?: string, location?: string, groupId?: number, status?: number, onlineStatus?: number, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyDevice/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (deviceCode !== undefined) {
                localVarQueryParameter['DeviceCode'] = deviceCode;
            }

            if (deviceName !== undefined) {
                localVarQueryParameter['DeviceName'] = deviceName;
            }

            if (deviceType !== undefined) {
                localVarQueryParameter['DeviceType'] = deviceType;
            }

            if (deviceModel !== undefined) {
                localVarQueryParameter['DeviceModel'] = deviceModel;
            }

            if (location !== undefined) {
                localVarQueryParameter['Location'] = location;
            }

            if (groupId !== undefined) {
                localVarQueryParameter['GroupId'] = groupId;
            }

            if (status !== undefined) {
                localVarQueryParameter['Status'] = status;
            }

            if (onlineStatus !== undefined) {
                localVarQueryParameter['OnlineStatus'] = onlineStatus;
            }

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            if (field !== undefined) {
                localVarQueryParameter['Field'] = field;
            }

            if (order !== undefined) {
                localVarQueryParameter['Order'] = order;
            }

            if (descStr !== undefined) {
                localVarQueryParameter['DescStr'] = descStr;
            }

            if (searchFields) {
                localVarQueryParameter['Search.Fields'] = searchFields;
            }

            if (searchKeyword !== undefined) {
                localVarQueryParameter['Search.Keyword'] = searchKeyword;
            }

            if (keyword !== undefined) {
                localVarQueryParameter['Keyword'] = keyword;
            }

            if (filterLogic !== undefined) {
                localVarQueryParameter['Filter.Logic'] = filterLogic;
            }

            if (filterFilters) {
                localVarQueryParameter['Filter.Filters'] = filterFilters;
            }

            if (filterField !== undefined) {
                localVarQueryParameter['Filter.Field'] = filterField;
            }

            if (filterOperator !== undefined) {
                localVarQueryParameter['Filter.Operator'] = filterOperator;
            }

            if (filterValue !== undefined) {
                localVarQueryParameter['Filter.Value'] = filterValue;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 设置设备状态 🔖
         * @param {EnergyDeviceStatusInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyDeviceSetStatusPost: async (body?: EnergyDeviceStatusInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyDevice/setStatus`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取设备状态统计 🔖
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyDeviceStatsGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyDevice/stats`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取设备类型分布 🔖
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyDeviceTypeDistributionGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyDevice/typeDistribution`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新设备 🔖
         * @param {UpdateEnergyDeviceInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyDeviceUpdatePost: async (body?: UpdateEnergyDeviceInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyDevice/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * EnergyDeviceApi - functional programming interface
 * @export
 */
export const EnergyDeviceApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加设备 🔖
         * @param {AddEnergyDeviceInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDeviceAddPost(body?: AddEnergyDeviceInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt64>>> {
            const localVarAxiosArgs = await EnergyDeviceApiAxiosParamCreator(configuration).apiEnergyDeviceAddPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 设备控制 🔖
         * @param {DeviceControlInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDeviceControlDevicePost(body?: DeviceControlInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultBoolean>>> {
            const localVarAxiosArgs = await EnergyDeviceApiAxiosParamCreator(configuration).apiEnergyDeviceControlDevicePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 删除设备 🔖
         * @param {DeleteEnergyDeviceInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDeviceDeletePost(body?: DeleteEnergyDeviceInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await EnergyDeviceApiAxiosParamCreator(configuration).apiEnergyDeviceDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取设备详情 🔖
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDeviceDetailGet(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultEnergyDeviceDetailOutput>>> {
            const localVarAxiosArgs = await EnergyDeviceApiAxiosParamCreator(configuration).apiEnergyDeviceDetailGet(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取设备统计信息 🔖
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDeviceDeviceStatGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultEnergyDeviceStatOutput>>> {
            const localVarAxiosArgs = await EnergyDeviceApiAxiosParamCreator(configuration).apiEnergyDeviceDeviceStatGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 导出设备数据 🔖
         * @param {string} [deviceCode] 设备编码
         * @param {string} [deviceName] 设备名称
         * @param {string} [deviceType] 设备类型
         * @param {string} [deviceModel] 设备型号
         * @param {string} [location] 设备位置
         * @param {number} [groupId] 分组ID
         * @param {number} [status] 设备状态
         * @param {number} [onlineStatus] 在线状态
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDeviceExportDataPost(deviceCode?: string, deviceName?: string, deviceType?: string, deviceModel?: string, location?: string, groupId?: number, status?: number, onlineStatus?: number, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultByte>>> {
            const localVarAxiosArgs = await EnergyDeviceApiAxiosParamCreator(configuration).apiEnergyDeviceExportDataPost(deviceCode, deviceName, deviceType, deviceModel, location, groupId, status, onlineStatus, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 导出设备数据到Excel 🔖
         * @param {EnergyDeviceInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDeviceExportExcelPost(body?: EnergyDeviceInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultIActionResult>>> {
            const localVarAxiosArgs = await EnergyDeviceApiAxiosParamCreator(configuration).apiEnergyDeviceExportExcelPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取设备列表 🔖
         * @param {string} [deviceCode] 设备编码
         * @param {string} [deviceName] 设备名称
         * @param {string} [deviceType] 设备类型
         * @param {string} [deviceModel] 设备型号
         * @param {string} [location] 设备位置
         * @param {number} [groupId] 分组ID
         * @param {number} [status] 设备状态
         * @param {number} [onlineStatus] 在线状态
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDeviceListGet(deviceCode?: string, deviceName?: string, deviceType?: string, deviceModel?: string, location?: string, groupId?: number, status?: number, onlineStatus?: number, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultListEnergyDeviceOutput>>> {
            const localVarAxiosArgs = await EnergyDeviceApiAxiosParamCreator(configuration).apiEnergyDeviceListGet(deviceCode, deviceName, deviceType, deviceModel, location, groupId, status, onlineStatus, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取设备分页列表 🔖
         * @param {string} [deviceCode] 设备编码
         * @param {string} [deviceName] 设备名称
         * @param {string} [deviceType] 设备类型
         * @param {string} [deviceModel] 设备型号
         * @param {string} [location] 设备位置
         * @param {number} [groupId] 分组ID
         * @param {number} [status] 设备状态
         * @param {number} [onlineStatus] 在线状态
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDevicePageGet(deviceCode?: string, deviceName?: string, deviceType?: string, deviceModel?: string, location?: string, groupId?: number, status?: number, onlineStatus?: number, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSqlSugarPagedListEnergyDeviceOutput>>> {
            const localVarAxiosArgs = await EnergyDeviceApiAxiosParamCreator(configuration).apiEnergyDevicePageGet(deviceCode, deviceName, deviceType, deviceModel, location, groupId, status, onlineStatus, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 设置设备状态 🔖
         * @param {EnergyDeviceStatusInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDeviceSetStatusPost(body?: EnergyDeviceStatusInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt32>>> {
            const localVarAxiosArgs = await EnergyDeviceApiAxiosParamCreator(configuration).apiEnergyDeviceSetStatusPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取设备状态统计 🔖
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDeviceStatsGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultObject>>> {
            const localVarAxiosArgs = await EnergyDeviceApiAxiosParamCreator(configuration).apiEnergyDeviceStatsGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取设备类型分布 🔖
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDeviceTypeDistributionGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultListObject>>> {
            const localVarAxiosArgs = await EnergyDeviceApiAxiosParamCreator(configuration).apiEnergyDeviceTypeDistributionGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新设备 🔖
         * @param {UpdateEnergyDeviceInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDeviceUpdatePost(body?: UpdateEnergyDeviceInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await EnergyDeviceApiAxiosParamCreator(configuration).apiEnergyDeviceUpdatePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * EnergyDeviceApi - factory interface
 * @export
 */
export const EnergyDeviceApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 增加设备 🔖
         * @param {AddEnergyDeviceInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDeviceAddPost(body?: AddEnergyDeviceInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt64>> {
            return EnergyDeviceApiFp(configuration).apiEnergyDeviceAddPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 设备控制 🔖
         * @param {DeviceControlInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDeviceControlDevicePost(body?: DeviceControlInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultBoolean>> {
            return EnergyDeviceApiFp(configuration).apiEnergyDeviceControlDevicePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 删除设备 🔖
         * @param {DeleteEnergyDeviceInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDeviceDeletePost(body?: DeleteEnergyDeviceInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return EnergyDeviceApiFp(configuration).apiEnergyDeviceDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取设备详情 🔖
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDeviceDetailGet(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultEnergyDeviceDetailOutput>> {
            return EnergyDeviceApiFp(configuration).apiEnergyDeviceDetailGet(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取设备统计信息 🔖
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDeviceDeviceStatGet(options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultEnergyDeviceStatOutput>> {
            return EnergyDeviceApiFp(configuration).apiEnergyDeviceDeviceStatGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 导出设备数据 🔖
         * @param {string} [deviceCode] 设备编码
         * @param {string} [deviceName] 设备名称
         * @param {string} [deviceType] 设备类型
         * @param {string} [deviceModel] 设备型号
         * @param {string} [location] 设备位置
         * @param {number} [groupId] 分组ID
         * @param {number} [status] 设备状态
         * @param {number} [onlineStatus] 在线状态
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDeviceExportDataPost(deviceCode?: string, deviceName?: string, deviceType?: string, deviceModel?: string, location?: string, groupId?: number, status?: number, onlineStatus?: number, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultByte>> {
            return EnergyDeviceApiFp(configuration).apiEnergyDeviceExportDataPost(deviceCode, deviceName, deviceType, deviceModel, location, groupId, status, onlineStatus, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 导出设备数据到Excel 🔖
         * @param {EnergyDeviceInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDeviceExportExcelPost(body?: EnergyDeviceInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultIActionResult>> {
            return EnergyDeviceApiFp(configuration).apiEnergyDeviceExportExcelPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取设备列表 🔖
         * @param {string} [deviceCode] 设备编码
         * @param {string} [deviceName] 设备名称
         * @param {string} [deviceType] 设备类型
         * @param {string} [deviceModel] 设备型号
         * @param {string} [location] 设备位置
         * @param {number} [groupId] 分组ID
         * @param {number} [status] 设备状态
         * @param {number} [onlineStatus] 在线状态
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDeviceListGet(deviceCode?: string, deviceName?: string, deviceType?: string, deviceModel?: string, location?: string, groupId?: number, status?: number, onlineStatus?: number, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultListEnergyDeviceOutput>> {
            return EnergyDeviceApiFp(configuration).apiEnergyDeviceListGet(deviceCode, deviceName, deviceType, deviceModel, location, groupId, status, onlineStatus, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取设备分页列表 🔖
         * @param {string} [deviceCode] 设备编码
         * @param {string} [deviceName] 设备名称
         * @param {string} [deviceType] 设备类型
         * @param {string} [deviceModel] 设备型号
         * @param {string} [location] 设备位置
         * @param {number} [groupId] 分组ID
         * @param {number} [status] 设备状态
         * @param {number} [onlineStatus] 在线状态
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDevicePageGet(deviceCode?: string, deviceName?: string, deviceType?: string, deviceModel?: string, location?: string, groupId?: number, status?: number, onlineStatus?: number, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSqlSugarPagedListEnergyDeviceOutput>> {
            return EnergyDeviceApiFp(configuration).apiEnergyDevicePageGet(deviceCode, deviceName, deviceType, deviceModel, location, groupId, status, onlineStatus, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 设置设备状态 🔖
         * @param {EnergyDeviceStatusInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDeviceSetStatusPost(body?: EnergyDeviceStatusInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt32>> {
            return EnergyDeviceApiFp(configuration).apiEnergyDeviceSetStatusPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取设备状态统计 🔖
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDeviceStatsGet(options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultObject>> {
            return EnergyDeviceApiFp(configuration).apiEnergyDeviceStatsGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取设备类型分布 🔖
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDeviceTypeDistributionGet(options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultListObject>> {
            return EnergyDeviceApiFp(configuration).apiEnergyDeviceTypeDistributionGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新设备 🔖
         * @param {UpdateEnergyDeviceInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyDeviceUpdatePost(body?: UpdateEnergyDeviceInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return EnergyDeviceApiFp(configuration).apiEnergyDeviceUpdatePost(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * EnergyDeviceApi - object-oriented interface
 * @export
 * @class EnergyDeviceApi
 * @extends {BaseAPI}
 */
export class EnergyDeviceApi extends BaseAPI {
    /**
     * 
     * @summary 增加设备 🔖
     * @param {AddEnergyDeviceInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyDeviceApi
     */
    public async apiEnergyDeviceAddPost(body?: AddEnergyDeviceInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt64>> {
        return EnergyDeviceApiFp(this.configuration).apiEnergyDeviceAddPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 设备控制 🔖
     * @param {DeviceControlInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyDeviceApi
     */
    public async apiEnergyDeviceControlDevicePost(body?: DeviceControlInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultBoolean>> {
        return EnergyDeviceApiFp(this.configuration).apiEnergyDeviceControlDevicePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 删除设备 🔖
     * @param {DeleteEnergyDeviceInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyDeviceApi
     */
    public async apiEnergyDeviceDeletePost(body?: DeleteEnergyDeviceInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return EnergyDeviceApiFp(this.configuration).apiEnergyDeviceDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取设备详情 🔖
     * @param {number} id 主键Id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyDeviceApi
     */
    public async apiEnergyDeviceDetailGet(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultEnergyDeviceDetailOutput>> {
        return EnergyDeviceApiFp(this.configuration).apiEnergyDeviceDetailGet(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取设备统计信息 🔖
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyDeviceApi
     */
    public async apiEnergyDeviceDeviceStatGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultEnergyDeviceStatOutput>> {
        return EnergyDeviceApiFp(this.configuration).apiEnergyDeviceDeviceStatGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 导出设备数据 🔖
     * @param {string} [deviceCode] 设备编码
     * @param {string} [deviceName] 设备名称
     * @param {string} [deviceType] 设备类型
     * @param {string} [deviceModel] 设备型号
     * @param {string} [location] 设备位置
     * @param {number} [groupId] 分组ID
     * @param {number} [status] 设备状态
     * @param {number} [onlineStatus] 在线状态
     * @param {number} [page] 当前页码
     * @param {number} [pageSize] 页码容量
     * @param {string} [field] 排序字段
     * @param {string} [order] 排序方向
     * @param {string} [descStr] 降序排序
     * @param {Array<string>} [searchFields] 字段名称集合
     * @param {string} [searchKeyword] 关键字
     * @param {string} [keyword] 模糊查询关键字
     * @param {FilterLogicEnum} [filterLogic] 过滤条件
     * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
     * @param {string} [filterField] 字段名称
     * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
     * @param {any} [filterValue] 字段值
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyDeviceApi
     */
    public async apiEnergyDeviceExportDataPost(deviceCode?: string, deviceName?: string, deviceType?: string, deviceModel?: string, location?: string, groupId?: number, status?: number, onlineStatus?: number, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultByte>> {
        return EnergyDeviceApiFp(this.configuration).apiEnergyDeviceExportDataPost(deviceCode, deviceName, deviceType, deviceModel, location, groupId, status, onlineStatus, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 导出设备数据到Excel 🔖
     * @param {EnergyDeviceInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyDeviceApi
     */
    public async apiEnergyDeviceExportExcelPost(body?: EnergyDeviceInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultIActionResult>> {
        return EnergyDeviceApiFp(this.configuration).apiEnergyDeviceExportExcelPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取设备列表 🔖
     * @param {string} [deviceCode] 设备编码
     * @param {string} [deviceName] 设备名称
     * @param {string} [deviceType] 设备类型
     * @param {string} [deviceModel] 设备型号
     * @param {string} [location] 设备位置
     * @param {number} [groupId] 分组ID
     * @param {number} [status] 设备状态
     * @param {number} [onlineStatus] 在线状态
     * @param {number} [page] 当前页码
     * @param {number} [pageSize] 页码容量
     * @param {string} [field] 排序字段
     * @param {string} [order] 排序方向
     * @param {string} [descStr] 降序排序
     * @param {Array<string>} [searchFields] 字段名称集合
     * @param {string} [searchKeyword] 关键字
     * @param {string} [keyword] 模糊查询关键字
     * @param {FilterLogicEnum} [filterLogic] 过滤条件
     * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
     * @param {string} [filterField] 字段名称
     * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
     * @param {any} [filterValue] 字段值
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyDeviceApi
     */
    public async apiEnergyDeviceListGet(deviceCode?: string, deviceName?: string, deviceType?: string, deviceModel?: string, location?: string, groupId?: number, status?: number, onlineStatus?: number, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultListEnergyDeviceOutput>> {
        return EnergyDeviceApiFp(this.configuration).apiEnergyDeviceListGet(deviceCode, deviceName, deviceType, deviceModel, location, groupId, status, onlineStatus, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取设备分页列表 🔖
     * @param {string} [deviceCode] 设备编码
     * @param {string} [deviceName] 设备名称
     * @param {string} [deviceType] 设备类型
     * @param {string} [deviceModel] 设备型号
     * @param {string} [location] 设备位置
     * @param {number} [groupId] 分组ID
     * @param {number} [status] 设备状态
     * @param {number} [onlineStatus] 在线状态
     * @param {number} [page] 当前页码
     * @param {number} [pageSize] 页码容量
     * @param {string} [field] 排序字段
     * @param {string} [order] 排序方向
     * @param {string} [descStr] 降序排序
     * @param {Array<string>} [searchFields] 字段名称集合
     * @param {string} [searchKeyword] 关键字
     * @param {string} [keyword] 模糊查询关键字
     * @param {FilterLogicEnum} [filterLogic] 过滤条件
     * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
     * @param {string} [filterField] 字段名称
     * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
     * @param {any} [filterValue] 字段值
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyDeviceApi
     */
    public async apiEnergyDevicePageGet(deviceCode?: string, deviceName?: string, deviceType?: string, deviceModel?: string, location?: string, groupId?: number, status?: number, onlineStatus?: number, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSqlSugarPagedListEnergyDeviceOutput>> {
        return EnergyDeviceApiFp(this.configuration).apiEnergyDevicePageGet(deviceCode, deviceName, deviceType, deviceModel, location, groupId, status, onlineStatus, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 设置设备状态 🔖
     * @param {EnergyDeviceStatusInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyDeviceApi
     */
    public async apiEnergyDeviceSetStatusPost(body?: EnergyDeviceStatusInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt32>> {
        return EnergyDeviceApiFp(this.configuration).apiEnergyDeviceSetStatusPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取设备状态统计 🔖
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyDeviceApi
     */
    public async apiEnergyDeviceStatsGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultObject>> {
        return EnergyDeviceApiFp(this.configuration).apiEnergyDeviceStatsGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取设备类型分布 🔖
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyDeviceApi
     */
    public async apiEnergyDeviceTypeDistributionGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultListObject>> {
        return EnergyDeviceApiFp(this.configuration).apiEnergyDeviceTypeDistributionGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新设备 🔖
     * @param {UpdateEnergyDeviceInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyDeviceApi
     */
    public async apiEnergyDeviceUpdatePost(body?: UpdateEnergyDeviceInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return EnergyDeviceApiFp(this.configuration).apiEnergyDeviceUpdatePost(body, options).then((request) => request(this.axios, this.basePath));
    }
}
