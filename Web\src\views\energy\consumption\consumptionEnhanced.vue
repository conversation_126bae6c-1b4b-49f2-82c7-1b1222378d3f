<template>
  <el-dialog
    v-model="visible"
    title="能耗监控增强分析"
    width="90%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-tabs v-model="activeTab" type="card">
      <!-- 节能分析 -->
      <el-tab-pane label="节能分析" name="saving">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card class="analysis-card">
              <template #header>
                <span>节能效果统计</span>
              </template>
              <div class="saving-stats">
                <div class="stat-item">
                  <div class="stat-icon saving">
                    <el-icon :size="24"><Lightning /></el-icon>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ savingData.totalSaved }}kWh</div>
                    <div class="stat-label">累计节能</div>
                  </div>
                </div>
                <div class="stat-item">
                  <div class="stat-icon cost">
                    <el-icon :size="24"><Money /></el-icon>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">¥{{ savingData.costSaved }}</div>
                    <div class="stat-label">节省费用</div>
                  </div>
                </div>
                <div class="stat-item">
                  <div class="stat-icon rate">
                    <el-icon :size="24"><TrendCharts /></el-icon>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ savingData.savingRate }}%</div>
                    <div class="stat-label">节能率</div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="16">
            <el-card class="chart-card">
              <template #header>
                <div class="chart-header">
                  <span>节能趋势分析</span>
                  <el-radio-group v-model="savingPeriod" size="small" @change="loadSavingChart">
                    <el-radio-button label="week">周</el-radio-button>
                    <el-radio-button label="month">月</el-radio-button>
                    <el-radio-button label="quarter">季</el-radio-button>
                  </el-radio-group>
                </div>
              </template>
              <div ref="savingChartRef" class="chart-container"></div>
            </el-card>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="12">
            <el-card class="analysis-card">
              <template #header>
                <span>节能建议</span>
              </template>
              <div class="suggestions-list">
                <div v-for="(suggestion, index) in savingData.suggestions" :key="index" class="suggestion-item">
                  <div class="suggestion-icon">
                    <el-icon :size="16" :color="suggestion.priority === 'high' ? '#F56C6C' : suggestion.priority === 'medium' ? '#E6A23C' : '#67C23A'">
                      <Warning v-if="suggestion.priority === 'high'" />
                      <InfoFilled v-else-if="suggestion.priority === 'medium'" />
                      <SuccessFilled v-else />
                    </el-icon>
                  </div>
                  <div class="suggestion-content">
                    <div class="suggestion-title">{{ suggestion.title }}</div>
                    <div class="suggestion-desc">{{ suggestion.description }}</div>
                    <div class="suggestion-benefit">预计节能: {{ suggestion.expectedSaving }}kWh/月</div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="analysis-card">
              <template #header>
                <span>设备能效排行</span>
              </template>
              <div ref="efficiencyChartRef" class="chart-container"></div>
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>
      
      <!-- 趋势预测 -->
      <el-tab-pane label="趋势预测" name="prediction">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="prediction-card">
              <div class="prediction-item">
                <div class="prediction-icon next-month">
                  <el-icon :size="24"><Calendar /></el-icon>
                </div>
                <div class="prediction-content">
                  <div class="prediction-value">{{ predictionData.nextMonth }}kWh</div>
                  <div class="prediction-label">下月预测</div>
                  <div class="prediction-change" :class="{ increase: predictionData.monthChange > 0, decrease: predictionData.monthChange < 0 }">
                    <el-icon v-if="predictionData.monthChange > 0"><CaretTop /></el-icon>
                    <el-icon v-else><CaretBottom /></el-icon>
                    {{ Math.abs(predictionData.monthChange) }}%
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="prediction-card">
              <div class="prediction-item">
                <div class="prediction-icon next-quarter">
                  <el-icon :size="24"><DataAnalysis /></el-icon>
                </div>
                <div class="prediction-content">
                  <div class="prediction-value">{{ predictionData.nextQuarter }}kWh</div>
                  <div class="prediction-label">下季预测</div>
                  <div class="prediction-change" :class="{ increase: predictionData.quarterChange > 0, decrease: predictionData.quarterChange < 0 }">
                    <el-icon v-if="predictionData.quarterChange > 0"><CaretTop /></el-icon>
                    <el-icon v-else><CaretBottom /></el-icon>
                    {{ Math.abs(predictionData.quarterChange) }}%
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="prediction-card">
              <div class="prediction-item">
                <div class="prediction-icon peak-time">
                  <el-icon :size="24"><Clock /></el-icon>
                </div>
                <div class="prediction-content">
                  <div class="prediction-value">{{ predictionData.peakTime }}</div>
                  <div class="prediction-label">用电高峰</div>
                  <div class="prediction-desc">{{ predictionData.peakDesc }}</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="prediction-card">
              <div class="prediction-item">
                <div class="prediction-icon accuracy">
                  <el-icon :size="24"><Aim /></el-icon>
                </div>
                <div class="prediction-content">
                  <div class="prediction-value">{{ predictionData.accuracy }}%</div>
                  <div class="prediction-label">预测准确率</div>
                  <div class="prediction-desc">基于历史数据</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="24">
            <el-card class="chart-card">
              <template #header>
                <div class="chart-header">
                  <span>能耗预测趋势</span>
                  <div class="prediction-controls">
                    <el-select v-model="predictionModel" size="small" style="width: 120px; margin-right: 10px;" @change="loadPredictionChart">
                      <el-option label="线性回归" value="linear" />
                      <el-option label="时间序列" value="arima" />
                      <el-option label="神经网络" value="neural" />
                    </el-select>
                    <el-radio-group v-model="predictionPeriod" size="small" @change="loadPredictionChart">
                      <el-radio-button label="30">30天</el-radio-button>
                      <el-radio-button label="90">90天</el-radio-button>
                      <el-radio-button label="365">1年</el-radio-button>
                    </el-radio-group>
                  </div>
                </div>
              </template>
              <div ref="predictionChartRef" class="chart-container" style="height: 400px;"></div>
            </el-card>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="12">
            <el-card class="analysis-card">
              <template #header>
                <span>预测因子分析</span>
              </template>
              <div class="factors-list">
                <div v-for="factor in predictionData.factors" :key="factor.name" class="factor-item">
                  <div class="factor-name">{{ factor.name }}</div>
                  <div class="factor-impact">
                    <el-progress :percentage="factor.impact" :color="getFactorColor(factor.impact)" />
                  </div>
                  <div class="factor-desc">{{ factor.description }}</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="analysis-card">
              <template #header>
                <span>异常预警</span>
              </template>
              <div class="alerts-list">
                <div v-for="alert in predictionData.alerts" :key="alert.id" class="alert-item" :class="alert.level">
                  <div class="alert-icon">
                    <el-icon :size="16">
                      <Warning v-if="alert.level === 'high'" />
                      <InfoFilled v-else-if="alert.level === 'medium'" />
                      <SuccessFilled v-else />
                    </el-icon>
                  </div>
                  <div class="alert-content">
                    <div class="alert-title">{{ alert.title }}</div>
                    <div class="alert-desc">{{ alert.description }}</div>
                    <div class="alert-time">预计时间: {{ alert.expectedTime }}</div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>
      
      <!-- 成本计算 -->
      <el-tab-pane label="成本计算" name="cost">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card class="cost-card">
              <template #header>
                <span>电费统计</span>
              </template>
              <div class="cost-stats">
                <div class="cost-item">
                  <div class="cost-icon today">
                    <el-icon :size="24"><Sunny /></el-icon>
                  </div>
                  <div class="cost-content">
                    <div class="cost-value">¥{{ costData.today }}</div>
                    <div class="cost-label">今日电费</div>
                  </div>
                </div>
                <div class="cost-item">
                  <div class="cost-icon month">
                    <el-icon :size="24"><Calendar /></el-icon>
                  </div>
                  <div class="cost-content">
                    <div class="cost-value">¥{{ costData.month }}</div>
                    <div class="cost-label">本月电费</div>
                  </div>
                </div>
                <div class="cost-item">
                  <div class="cost-icon year">
                    <el-icon :size="24"><DataAnalysis /></el-icon>
                  </div>
                  <div class="cost-content">
                    <div class="cost-value">¥{{ costData.year }}</div>
                    <div class="cost-label">本年电费</div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="16">
            <el-card class="chart-card">
              <template #header>
                <div class="chart-header">
                  <span>电费趋势分析</span>
                  <el-radio-group v-model="costPeriod" size="small" @change="loadCostChart">
                    <el-radio-button label="day">日</el-radio-button>
                    <el-radio-button label="month">月</el-radio-button>
                    <el-radio-button label="year">年</el-radio-button>
                  </el-radio-group>
                </div>
              </template>
              <div ref="costChartRef" class="chart-container"></div>
            </el-card>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="12">
            <el-card class="analysis-card">
              <template #header>
                <span>电价配置</span>
              </template>
              <el-form :model="priceConfig" label-width="100px">
                <el-form-item label="峰时电价">
                  <el-input-number v-model="priceConfig.peakPrice" :precision="4" :step="0.0001" :min="0" @change="calculateCost" />
                  <span style="margin-left: 8px;">元/kWh</span>
                </el-form-item>
                <el-form-item label="平时电价">
                  <el-input-number v-model="priceConfig.normalPrice" :precision="4" :step="0.0001" :min="0" @change="calculateCost" />
                  <span style="margin-left: 8px;">元/kWh</span>
                </el-form-item>
                <el-form-item label="谷时电价">
                  <el-input-number v-model="priceConfig.valleyPrice" :precision="4" :step="0.0001" :min="0" @change="calculateCost" />
                  <span style="margin-left: 8px;">元/kWh</span>
                </el-form-item>
                <el-form-item label="峰时时段">
                  <el-time-picker
                    v-model="priceConfig.peakTime"
                    is-range
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    format="HH:mm"
                    value-format="HH:mm"
                    @change="calculateCost"
                  />
                </el-form-item>
                <el-form-item label="谷时时段">
                  <el-time-picker
                    v-model="priceConfig.valleyTime"
                    is-range
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    format="HH:mm"
                    value-format="HH:mm"
                    @change="calculateCost"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="savePriceConfig">保存配置</el-button>
                  <el-button @click="resetPriceConfig">重置</el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="analysis-card">
              <template #header>
                <span>成本优化建议</span>
              </template>
              <div class="optimization-list">
                <div v-for="(opt, index) in costData.optimizations" :key="index" class="optimization-item">
                  <div class="optimization-icon">
                    <el-icon :size="16" :color="opt.priority === 'high' ? '#F56C6C' : opt.priority === 'medium' ? '#E6A23C' : '#67C23A'">
                      <Star v-if="opt.priority === 'high'" />
                      <StarFilled v-else-if="opt.priority === 'medium'" />
                      <Check v-else />
                    </el-icon>
                  </div>
                  <div class="optimization-content">
                    <div class="optimization-title">{{ opt.title }}</div>
                    <div class="optimization-desc">{{ opt.description }}</div>
                    <div class="optimization-saving">预计节省: ¥{{ opt.expectedSaving }}/月</div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="24">
            <el-card class="analysis-card">
              <template #header>
                <span>分时电费分析</span>
              </template>
              <div ref="timeBasedCostChartRef" class="chart-container" style="height: 300px;"></div>
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import {
  Lightning, Money, TrendCharts, Warning, InfoFilled, SuccessFilled,
  Calendar, DataAnalysis, Clock, Aim, CaretTop, CaretBottom,
  Sunny, Star, StarFilled, Check
} from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'refresh'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const activeTab = ref('saving')
const savingPeriod = ref('month')
const predictionModel = ref('linear')
const predictionPeriod = ref('30')
const costPeriod = ref('month')

// 图表引用
const savingChartRef = ref()
const efficiencyChartRef = ref()
const predictionChartRef = ref()
const costChartRef = ref()
const timeBasedCostChartRef = ref()

let savingChart = null
let efficiencyChart = null
let predictionChart = null
let costChart = null
let timeBasedCostChart = null

// 节能数据
const savingData = reactive({
  totalSaved: 1250.5,
  costSaved: 875.35,
  savingRate: 15.8,
  suggestions: [
    {
      title: '优化照明时间',
      description: '建议在非工作时间降低照明亮度或关闭部分设备',
      expectedSaving: 120,
      priority: 'high'
    },
    {
      title: '设备能效升级',
      description: '更换老旧设备为高能效LED灯具',
      expectedSaving: 200,
      priority: 'medium'
    },
    {
      title: '智能调光策略',
      description: '根据环境光线自动调节设备亮度',
      expectedSaving: 80,
      priority: 'low'
    }
  ]
})

// 预测数据
const predictionData = reactive({
  nextMonth: 2850,
  monthChange: -5.2,
  nextQuarter: 8200,
  quarterChange: -3.8,
  peakTime: '19:00-21:00',
  peakDesc: '晚间用电高峰',
  accuracy: 92.5,
  factors: [
    { name: '季节因素', impact: 85, description: '季节变化对能耗影响最大' },
    { name: '工作日模式', impact: 72, description: '工作日与休息日用电差异' },
    { name: '设备老化', impact: 45, description: '设备使用年限影响能效' },
    { name: '环境温度', impact: 38, description: '温度变化影响设备功耗' }
  ],
  alerts: [
    {
      id: 1,
      title: '能耗异常增长',
      description: '预计下月能耗将超出预算15%',
      expectedTime: '2024-02-15',
      level: 'high'
    },
    {
      id: 2,
      title: '设备维护提醒',
      description: '部分设备需要进行定期维护',
      expectedTime: '2024-02-20',
      level: 'medium'
    }
  ]
})

// 成本数据
const costData = reactive({
  today: 125.50,
  month: 3250.80,
  year: 28500.00,
  optimizations: [
    {
      title: '峰谷电价优化',
      description: '调整设备运行时间，避开用电高峰期',
      expectedSaving: 300,
      priority: 'high'
    },
    {
      title: '负荷均衡',
      description: '合理分配设备负荷，提高用电效率',
      expectedSaving: 150,
      priority: 'medium'
    },
    {
      title: '定时控制',
      description: '设置定时开关，减少不必要的能耗',
      expectedSaving: 100,
      priority: 'low'
    }
  ]
})

// 电价配置
const priceConfig = reactive({
  peakPrice: 0.8976,
  normalPrice: 0.5583,
  valleyPrice: 0.3583,
  peakTime: ['08:00', '22:00'],
  valleyTime: ['23:00', '07:00']
})

// 加载节能图表
const loadSavingChart = async () => {
  await nextTick()
  if (!savingChart && savingChartRef.value) {
    savingChart = echarts.init(savingChartRef.value)
  }
  
  if (savingChart) {
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        data: ['实际能耗', '基准能耗', '节能量']
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: {
        type: 'value',
        name: '能耗(kWh)'
      },
      series: [
        {
          name: '实际能耗',
          type: 'line',
          data: [2800, 2650, 2900, 2750, 2600, 2500],
          itemStyle: { color: '#409EFF' }
        },
        {
          name: '基准能耗',
          type: 'line',
          data: [3200, 3100, 3300, 3150, 3000, 2950],
          itemStyle: { color: '#F56C6C' },
          lineStyle: { type: 'dashed' }
        },
        {
          name: '节能量',
          type: 'bar',
          data: [400, 450, 400, 400, 400, 450],
          itemStyle: { color: '#67C23A' }
        }
      ]
    }
    savingChart.setOption(option)
  }
}

// 加载设备能效图表
const loadEfficiencyChart = async () => {
  await nextTick()
  if (!efficiencyChart && efficiencyChartRef.value) {
    efficiencyChart = echarts.init(efficiencyChartRef.value)
  }
  
  if (efficiencyChart) {
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'value',
        name: '能效比'
      },
      yAxis: {
        type: 'category',
        data: ['LED-001', 'LED-002', 'LED-003', 'LED-004', 'LED-005']
      },
      series: [{
        type: 'bar',
        data: [95, 88, 92, 85, 90],
        itemStyle: {
          color: function(params) {
            const colors = ['#67C23A', '#E6A23C', '#F56C6C']
            if (params.value >= 90) return colors[0]
            if (params.value >= 85) return colors[1]
            return colors[2]
          }
        }
      }]
    }
    efficiencyChart.setOption(option)
  }
}

// 加载预测图表
const loadPredictionChart = async () => {
  await nextTick()
  if (!predictionChart && predictionChartRef.value) {
    predictionChart = echarts.init(predictionChartRef.value)
  }
  
  if (predictionChart) {
    const option = {
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['历史数据', '预测数据', '置信区间']
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月']
      },
      yAxis: {
        type: 'value',
        name: '能耗(kWh)'
      },
      series: [
        {
          name: '历史数据',
          type: 'line',
          data: [2800, 2650, 2900, 2750, 2600, 2500, null, null, null],
          itemStyle: { color: '#409EFF' }
        },
        {
          name: '预测数据',
          type: 'line',
          data: [null, null, null, null, null, 2500, 2450, 2400, 2350],
          itemStyle: { color: '#E6A23C' },
          lineStyle: { type: 'dashed' }
        },
        {
          name: '置信区间',
          type: 'line',
          data: [null, null, null, null, null, 2600, 2550, 2500, 2450],
          itemStyle: { color: '#F56C6C' },
          lineStyle: { type: 'dotted' },
          areaStyle: {
            color: 'rgba(245, 108, 108, 0.1)'
          }
        }
      ]
    }
    predictionChart.setOption(option)
  }
}

// 加载成本图表
const loadCostChart = async () => {
  await nextTick()
  if (!costChart && costChartRef.value) {
    costChart = echarts.init(costChartRef.value)
  }
  
  if (costChart) {
    const option = {
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['电费支出', '节省费用']
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: {
        type: 'value',
        name: '费用(元)'
      },
      series: [
        {
          name: '电费支出',
          type: 'bar',
          data: [3200, 2950, 3100, 2800, 2650, 2500],
          itemStyle: { color: '#409EFF' }
        },
        {
          name: '节省费用',
          type: 'bar',
          data: [400, 450, 400, 400, 400, 450],
          itemStyle: { color: '#67C23A' }
        }
      ]
    }
    costChart.setOption(option)
  }
}

// 加载分时电费图表
const loadTimeBasedCostChart = async () => {
  await nextTick()
  if (!timeBasedCostChart && timeBasedCostChartRef.value) {
    timeBasedCostChart = echarts.init(timeBasedCostChartRef.value)
  }
  
  if (timeBasedCostChart) {
    const option = {
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['峰时', '平时', '谷时']
      },
      xAxis: {
        type: 'category',
        data: Array.from({length: 24}, (_, i) => `${i}:00`)
      },
      yAxis: {
        type: 'value',
        name: '电价(元/kWh)'
      },
      series: [
        {
          name: '峰时',
          type: 'line',
          step: 'end',
          data: Array.from({length: 24}, (_, i) => {
            if (i >= 8 && i < 22) return 0.8976
            return null
          }),
          itemStyle: { color: '#F56C6C' }
        },
        {
          name: '平时',
          type: 'line',
          step: 'end',
          data: Array.from({length: 24}, (_, i) => {
            if ((i >= 7 && i < 8) || i >= 22) return 0.5583
            return null
          }),
          itemStyle: { color: '#E6A23C' }
        },
        {
          name: '谷时',
          type: 'line',
          step: 'end',
          data: Array.from({length: 24}, (_, i) => {
            if (i < 7) return 0.3583
            return null
          }),
          itemStyle: { color: '#67C23A' }
        }
      ]
    }
    timeBasedCostChart.setOption(option)
  }
}

// 获取因子颜色
const getFactorColor = (impact) => {
  if (impact >= 70) return '#F56C6C'
  if (impact >= 50) return '#E6A23C'
  return '#67C23A'
}

// 计算成本
const calculateCost = () => {
  // 重新计算成本数据
  ElMessage.success('成本计算已更新')
}

// 保存电价配置
const savePriceConfig = () => {
  ElMessage.success('电价配置保存成功')
}

// 重置电价配置
const resetPriceConfig = () => {
  priceConfig.peakPrice = 0.8976
  priceConfig.normalPrice = 0.5583
  priceConfig.valleyPrice = 0.3583
  priceConfig.peakTime = ['08:00', '22:00']
  priceConfig.valleyTime = ['23:00', '07:00']
  ElMessage.success('电价配置已重置')
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}

// 监听标签页切换
watch(activeTab, (newTab) => {
  nextTick(() => {
    if (newTab === 'saving') {
      loadSavingChart()
      loadEfficiencyChart()
    } else if (newTab === 'prediction') {
      loadPredictionChart()
    } else if (newTab === 'cost') {
      loadCostChart()
      loadTimeBasedCostChart()
    }
  })
})

// 监听弹窗显示
watch(visible, (newVisible) => {
  if (newVisible) {
    nextTick(() => {
      if (activeTab.value === 'saving') {
        loadSavingChart()
        loadEfficiencyChart()
      } else if (activeTab.value === 'prediction') {
        loadPredictionChart()
      } else if (activeTab.value === 'cost') {
        loadCostChart()
        loadTimeBasedCostChart()
      }
    })
  }
})

onMounted(() => {
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    savingChart?.resize()
    efficiencyChart?.resize()
    predictionChart?.resize()
    costChart?.resize()
    timeBasedCostChart?.resize()
  })
})
</script>

<style scoped lang="scss">
.analysis-card, .prediction-card, .cost-card, .chart-card {
  height: 100%;
  
  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .prediction-controls {
      display: flex;
      align-items: center;
    }
  }
  
  .chart-container {
    height: 300px;
    width: 100%;
  }
}

.saving-stats, .cost-stats {
  .stat-item, .cost-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    
    .stat-icon, .cost-icon {
      width: 48px;
      height: 48px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      
      &.saving {
        background: linear-gradient(135deg, #67C23A, #85CE61);
        color: white;
      }
      
      &.cost {
        background: linear-gradient(135deg, #E6A23C, #EEBE77);
        color: white;
      }
      
      &.rate {
        background: linear-gradient(135deg, #409EFF, #79BBFF);
        color: white;
      }
      
      &.today {
        background: linear-gradient(135deg, #F56C6C, #F78989);
        color: white;
      }
      
      &.month {
        background: linear-gradient(135deg, #909399, #B1B3B8);
        color: white;
      }
      
      &.year {
        background: linear-gradient(135deg, #606266, #8B8D94);
        color: white;
      }
    }
    
    .stat-content, .cost-content {
      .stat-value, .cost-value {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 4px;
      }
      
      .stat-label, .cost-label {
        font-size: 14px;
        color: #909399;
      }
    }
  }
}

.prediction-item {
  display: flex;
  align-items: center;
  
  .prediction-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    
    &.next-month {
      background: linear-gradient(135deg, #409EFF, #79BBFF);
      color: white;
    }
    
    &.next-quarter {
      background: linear-gradient(135deg, #67C23A, #85CE61);
      color: white;
    }
    
    &.peak-time {
      background: linear-gradient(135deg, #E6A23C, #EEBE77);
      color: white;
    }
    
    &.accuracy {
      background: linear-gradient(135deg, #F56C6C, #F78989);
      color: white;
    }
  }
  
  .prediction-content {
    .prediction-value {
      font-size: 20px;
      font-weight: bold;
      color: #303133;
      margin-bottom: 4px;
    }
    
    .prediction-label {
      font-size: 14px;
      color: #909399;
      margin-bottom: 4px;
    }
    
    .prediction-change {
      font-size: 12px;
      display: flex;
      align-items: center;
      
      &.increase {
        color: #F56C6C;
      }
      
      &.decrease {
        color: #67C23A;
      }
    }
    
    .prediction-desc {
      font-size: 12px;
      color: #C0C4CC;
    }
  }
}

.suggestions-list, .optimization-list {
  .suggestion-item, .optimization-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
    padding: 12px;
    background: #F5F7FA;
    border-radius: 8px;
    
    .suggestion-icon, .optimization-icon {
      margin-right: 12px;
      margin-top: 2px;
    }
    
    .suggestion-content, .optimization-content {
      flex: 1;
      
      .suggestion-title, .optimization-title {
        font-weight: bold;
        color: #303133;
        margin-bottom: 4px;
      }
      
      .suggestion-desc, .optimization-desc {
        font-size: 14px;
        color: #606266;
        margin-bottom: 4px;
      }
      
      .suggestion-benefit, .optimization-saving {
        font-size: 12px;
        color: #67C23A;
        font-weight: bold;
      }
    }
  }
}

.factors-list {
  .factor-item {
    margin-bottom: 16px;
    
    .factor-name {
      font-weight: bold;
      color: #303133;
      margin-bottom: 8px;
    }
    
    .factor-impact {
      margin-bottom: 4px;
    }
    
    .factor-desc {
      font-size: 12px;
      color: #909399;
    }
  }
}

.alerts-list {
  .alert-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
    padding: 12px;
    border-radius: 8px;
    border-left: 4px solid;
    
    &.high {
      background: #FEF0F0;
      border-left-color: #F56C6C;
      
      .alert-icon {
        color: #F56C6C;
      }
    }
    
    &.medium {
      background: #FDF6EC;
      border-left-color: #E6A23C;
      
      .alert-icon {
        color: #E6A23C;
      }
    }
    
    &.low {
      background: #F0F9FF;
      border-left-color: #67C23A;
      
      .alert-icon {
        color: #67C23A;
      }
    }
    
    .alert-icon {
      margin-right: 12px;
      margin-top: 2px;
    }
    
    .alert-content {
      flex: 1;
      
      .alert-title {
        font-weight: bold;
        color: #303133;
        margin-bottom: 4px;
      }
      
      .alert-desc {
        font-size: 14px;
        color: #606266;
        margin-bottom: 4px;
      }
      
      .alert-time {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}
</style>