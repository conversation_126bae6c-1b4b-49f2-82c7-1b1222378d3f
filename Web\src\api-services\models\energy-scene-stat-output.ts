/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { SceneRankItem } from './scene-rank-item';
/**
 * 场景统计输出参数
 * @export
 * @interface EnergySceneStatOutput
 */
export interface EnergySceneStatOutput {
    /**
     * 场景总数
     * @type {number}
     * @memberof EnergySceneStatOutput
     */
    totalCount?: number;
    /**
     * 启用场景数
     * @type {number}
     * @memberof EnergySceneStatOutput
     */
    enabledCount?: number;
    /**
     * 禁用场景数
     * @type {number}
     * @memberof EnergySceneStatOutput
     */
    disabledCount?: number;
    /**
     * 总执行次数
     * @type {number}
     * @memberof EnergySceneStatOutput
     */
    totalExecuteCount?: number;
    /**
     * 今日执行次数
     * @type {number}
     * @memberof EnergySceneStatOutput
     */
    todayExecuteCount?: number;
    /**
     * 本月执行次数
     * @type {number}
     * @memberof EnergySceneStatOutput
     */
    monthExecuteCount?: number;
    /**
     * 成功执行次数
     * @type {number}
     * @memberof EnergySceneStatOutput
     */
    successExecuteCount?: number;
    /**
     * 失败执行次数
     * @type {number}
     * @memberof EnergySceneStatOutput
     */
    failedExecuteCount?: number;
    /**
     * 平均成功率
     * @type {number}
     * @memberof EnergySceneStatOutput
     */
    avgSuccessRate?: number;
    /**
     * 平均执行时长(秒)
     * @type {number}
     * @memberof EnergySceneStatOutput
     */
    avgExecuteDuration?: number;
    /**
     * 统计时间
     * @type {Date}
     * @memberof EnergySceneStatOutput
     */
    statTime?: Date;
    /**
     * 场景总数
     * @type {number}
     * @memberof EnergySceneStatOutput
     */
    totalSceneCount?: number;
    /**
     * 启用场景数
     * @type {number}
     * @memberof EnergySceneStatOutput
     */
    enabledSceneCount?: number;
    /**
     * 禁用场景数
     * @type {number}
     * @memberof EnergySceneStatOutput
     */
    disabledSceneCount?: number;
    /**
     * 热门场景排行
     * @type {Array<SceneRankItem>}
     * @memberof EnergySceneStatOutput
     */
    popularScenes?: Array<SceneRankItem> | null;
}
