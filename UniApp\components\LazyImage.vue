<template>
  <view class="lazy-image-container" :style="containerStyle">
    <image 
      :src="currentSrc" 
      :mode="mode"
      :lazy-load="true"
      :fade-show="fadeShow"
      :webp="webp"
      :show-menu-by-longpress="showMenuByLongpress"
      :class="imageClass"
      :style="imageStyle"
      @load="onImageLoad"
      @error="onImageError"
      @tap="onImageTap"
    />
    
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-overlay">
      <view class="loading-spinner"></view>
      <text v-if="showLoadingText" class="loading-text">{{ loadingText }}</text>
    </view>
    
    <!-- 错误状态 -->
    <view v-if="error && showErrorText" class="error-overlay">
      <text class="error-text">{{ errorText }}</text>
      <view v-if="showRetry" class="retry-btn" @tap="retryLoad">
        <text>重试</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'LazyImage',
  props: {
    // 图片源地址
    src: {
      type: String,
      required: true
    },
    // 占位图片
    placeholder: {
      type: String,
      default: '/static/images/placeholder.png'
    },
    // 错误图片
    errorImage: {
      type: String,
      default: '/static/images/error.png'
    },
    // 图片裁剪、缩放的模式
    mode: {
      type: String,
      default: 'aspectFill'
    },
    // 图片宽度
    width: {
      type: [String, Number],
      default: '100%'
    },
    // 图片高度
    height: {
      type: [String, Number],
      default: 'auto'
    },
    // 圆角
    borderRadius: {
      type: [String, Number],
      default: 0
    },
    // 是否启用淡入效果
    fadeShow: {
      type: Boolean,
      default: true
    },
    // 是否启用webp格式
    webp: {
      type: Boolean,
      default: true
    },
    // 是否开启长按图片显示识别小程序码菜单
    showMenuByLongpress: {
      type: Boolean,
      default: false
    },
    // 是否显示加载文本
    showLoadingText: {
      type: Boolean,
      default: false
    },
    // 加载文本
    loadingText: {
      type: String,
      default: '加载中...'
    },
    // 是否显示错误文本
    showErrorText: {
      type: Boolean,
      default: false
    },
    // 错误文本
    errorText: {
      type: String,
      default: '加载失败'
    },
    // 是否显示重试按钮
    showRetry: {
      type: Boolean,
      default: false
    },
    // 预加载距离（像素）
    threshold: {
      type: Number,
      default: 100
    },
    // 最大重试次数
    maxRetry: {
      type: Number,
      default: 3
    },
    // 自定义样式类
    customClass: {
      type: String,
      default: ''
    }
  },
  
  data() {
    return {
      currentSrc: this.placeholder,
      loading: false,
      error: false,
      loaded: false,
      retryCount: 0,
      observer: null
    }
  },
  
  computed: {
    containerStyle() {
      return {
        width: this.formatSize(this.width),
        height: this.formatSize(this.height),
        borderRadius: this.formatSize(this.borderRadius),
        overflow: 'hidden',
        position: 'relative'
      }
    },
    
    imageStyle() {
      return {
        width: '100%',
        height: '100%',
        transition: this.fadeShow ? 'opacity 0.3s ease' : 'none'
      }
    },
    
    imageClass() {
      return [
        'lazy-image',
        this.customClass,
        {
          'image-loading': this.loading,
          'image-loaded': this.loaded,
          'image-error': this.error
        }
      ]
    }
  },
  
  mounted() {
    this.initLazyLoad()
  },
  
  beforeUnmount() {
    this.cleanup()
  },
  
  methods: {
    /**
     * 初始化懒加载
     */
    initLazyLoad() {
      // 创建交叉观察器
      this.observer = uni.createIntersectionObserver(this)
      this.observer.relativeToViewport({ bottom: this.threshold })
      
      // 观察当前组件
      this.observer.observe('.lazy-image-container', (res) => {
        if (res.intersectionRatio > 0 && !this.loaded && !this.loading) {
          this.loadImage()
        }
      })
    },
    
    /**
     * 加载图片
     */
    async loadImage() {
      if (this.loading || this.loaded) return
      
      this.loading = true
      this.error = false
      
      try {
        // 预加载图片
        await this.preloadImage(this.src)
        
        // 加载成功，更新图片源
        this.currentSrc = this.src
        this.loaded = true
        this.loading = false
        
        // 触发加载成功事件
        this.$emit('load', {
          src: this.src,
          width: 0,
          height: 0
        })
        
      } catch (error) {
        this.handleLoadError(error)
      }
    },
    
    /**
     * 预加载图片
     * @param {string} src 图片地址
     * @returns {Promise}
     */
    preloadImage(src) {
      return new Promise((resolve, reject) => {
        const img = new Image()
        
        img.onload = () => {
          resolve({
            src,
            width: img.width,
            height: img.height
          })
        }
        
        img.onerror = () => {
          reject(new Error(`Failed to load image: ${src}`))
        }
        
        // 设置超时
        const timeout = setTimeout(() => {
          reject(new Error(`Image load timeout: ${src}`))
        }, 10000)
        
        img.onload = () => {
          clearTimeout(timeout)
          resolve({
            src,
            width: img.width,
            height: img.height
          })
        }
        
        img.src = src
      })
    },
    
    /**
     * 处理加载错误
     * @param {Error} error 错误对象
     */
    handleLoadError(error) {
      this.loading = false
      this.error = true
      this.currentSrc = this.errorImage
      
      // 触发错误事件
      this.$emit('error', {
        error,
        retryCount: this.retryCount
      })
      
      console.warn('LazyImage load error:', error.message)
    },
    
    /**
     * 图片加载完成
     * @param {Object} _e 事件对象
     */
    onImageLoad(_e) {
      if (this.currentSrc !== this.placeholder && this.currentSrc !== this.errorImage) {
        this.loaded = true
        this.loading = false
        this.error = false
        
        this.$emit('load', _e.detail)
      }
    },
    
    /**
     * 图片加载错误
     * @param {Object} _e 事件对象
     */
    onImageError(_e) {
      if (this.currentSrc !== this.errorImage) {
        this.handleLoadError(new Error('Image load failed'))
      }
      
      this.$emit('error', _e.detail)
    },
    
    /**
     * 图片点击
     * @param {Object} _e 事件对象
     */
    onImageTap(_e) { // eslint-disable-line no-unused-vars
      this.$emit('tap', {
        src: this.currentSrc,
        loaded: this.loaded,
        error: this.error
      })
    },
    
    /**
     * 重试加载
     */
    retryLoad() {
      if (this.retryCount >= this.maxRetry) {
        this.$emit('maxRetryReached', {
          retryCount: this.retryCount,
          src: this.src
        })
        return
      }
      
      this.retryCount++
      this.error = false
      this.loaded = false
      this.currentSrc = this.placeholder
      
      // 延迟重试
      setTimeout(() => {
        this.loadImage()
      }, 1000 * this.retryCount)
      
      this.$emit('retry', {
        retryCount: this.retryCount,
        src: this.src
      })
    },
    
    /**
     * 格式化尺寸
     * @param {string|number} size 尺寸值
     * @returns {string} 格式化后的尺寸
     */
    formatSize(size) {
      if (typeof size === 'number') {
        return `${size}rpx`
      }
      return size
    },
    
    /**
     * 清理资源
     */
    cleanup() {
      if (this.observer) {
        this.observer.disconnect()
        this.observer = null
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.lazy-image-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.lazy-image {
  display: block;
  
  &.image-loading {
    opacity: 0.6;
  }
  
  &.image-loaded {
    opacity: 1;
  }
  
  &.image-error {
    opacity: 0.5;
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 1;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 16rpx;
  font-size: 24rpx;
  color: #666666;
}

.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.error-text {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 16rpx;
}

.retry-btn {
  padding: 8rpx 16rpx;
  background-color: #1890ff;
  border-radius: 8rpx;
  
  text {
    font-size: 24rpx;
    color: #ffffff;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>