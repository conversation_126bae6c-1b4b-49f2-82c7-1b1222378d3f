// 在浏览器控制台中运行此脚本来检查错误日志
console.log('=== 错误日志检查 ===');

try {
  // 检查本地存储中的错误日志
  const errorLogs = typeof localStorage !== 'undefined' ? localStorage.getItem('error_logs') : null;
  
  if (errorLogs) {
    const logs = JSON.parse(errorLogs);
    console.log(`发现 ${logs.length} 条错误日志:`);
    
    // 显示最近的10条错误
    const recentLogs = logs.slice(-10);
    recentLogs.forEach((log, index) => {
      console.group(`错误 #${logs.length - 10 + index + 1}`);
      console.log('时间:', new Date(log.timestamp).toLocaleString());
      console.log('级别:', log.error?.level || '未知');
      console.log('类型:', log.error?.type || '未知');
      console.log('消息:', log.error?.message || '无消息');
      if (log.error?.stack) {
        console.log('堆栈:', log.error.stack);
      }
      if (log.context) {
        console.log('上下文:', log.context);
      }
      console.groupEnd();
    });
    
    // 统计错误级别
    const levelStats = {};
    logs.forEach(log => {
      const level = log.error?.level || 'UNKNOWN';
      levelStats[level] = (levelStats[level] || 0) + 1;
    });
    
    console.log('\n=== 错误级别统计 ===');
    Object.entries(levelStats).forEach(([level, count]) => {
      console.log(`${level}: ${count} 条`);
    });
    
  } else {
    console.log('未发现错误日志');
  }
  
  // 检查会话存储
  const sessionErrors = typeof sessionStorage !== 'undefined' ? sessionStorage.getItem('error_logs') : null;
  if (sessionErrors) {
    console.log('\n=== 会话存储中的错误 ===');
    console.log(JSON.parse(sessionErrors));
  }
  
} catch (e) {
  console.error('检查错误日志时出错:', e);
}

// 清除错误日志的函数
window.clearErrorLogs = function() {
  if (typeof localStorage !== 'undefined') {
    localStorage.removeItem('error_logs');
  }
  if (typeof sessionStorage !== 'undefined') {
    sessionStorage.removeItem('error_logs');
  }
  console.log('错误日志已清除');
};

// 模拟一个测试错误
window.testError = function() {
  throw new Error('这是一个测试错误');
};

console.log('\n可用命令:');
console.log('- clearErrorLogs(): 清除所有错误日志');
console.log('- testError(): 触发一个测试错误');
console.log('=== 检查完成 ===');