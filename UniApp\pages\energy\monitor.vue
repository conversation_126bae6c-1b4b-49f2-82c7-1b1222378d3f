<template>
  <view class="monitor-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">能耗监控</text>
      <view class="header-actions">
        <button class="btn-refresh" @click="refreshData">
          <text class="icon" :class="{ rotating: isRefreshing }">🔄</text>
          <text>刷新</text>
        </button>
        <button class="btn-settings" @click="showSettings">
          <text class="icon">⚙️</text>
          <text>设置</text>
        </button>
      </view>
    </view>

    <!-- 实时概览卡片 -->
    <view class="overview-cards">
      <view class="overview-card">
        <view class="card-icon total">
          <text>⚡</text>
        </view>
        <view class="card-content">
          <text class="card-value">{{ totalPower.toFixed(1) }}</text>
          <text class="card-unit">kW</text>
          <text class="card-label">总功耗</text>
          <view class="card-trend" :class="getTrendClass(totalPowerTrend)">
            <text class="trend-icon">{{ getTrendIcon(totalPowerTrend) }}</text>
            <text class="trend-text">{{ Math.abs(totalPowerTrend).toFixed(1) }}%</text>
          </view>
        </view>
      </view>
      
      <view class="overview-card">
        <view class="card-icon cost">
          <text>💰</text>
        </view>
        <view class="card-content">
          <text class="card-value">{{ dailyCost.toFixed(2) }}</text>
          <text class="card-unit">元</text>
          <text class="card-label">今日费用</text>
          <view class="card-trend" :class="getTrendClass(dailyCostTrend)">
            <text class="trend-icon">{{ getTrendIcon(dailyCostTrend) }}</text>
            <text class="trend-text">{{ Math.abs(dailyCostTrend).toFixed(1) }}%</text>
          </view>
        </view>
      </view>
      
      <view class="overview-card">
        <view class="card-icon efficiency">
          <text>🌱</text>
        </view>
        <view class="card-content">
          <text class="card-value">{{ efficiency.toFixed(1) }}</text>
          <text class="card-unit">%</text>
          <text class="card-label">能效比</text>
          <view class="card-trend" :class="getTrendClass(efficiencyTrend)">
            <text class="trend-icon">{{ getTrendIcon(efficiencyTrend) }}</text>
            <text class="trend-text">{{ Math.abs(efficiencyTrend).toFixed(1) }}%</text>
          </view>
        </view>
      </view>
      
      <view class="overview-card">
        <view class="card-icon devices">
          <text>💡</text>
        </view>
        <view class="card-content">
          <text class="card-value">{{ activeDevices }}</text>
          <text class="card-unit">台</text>
          <text class="card-label">在线设备</text>
          <text class="card-subtitle">共{{ totalDevices }}台</text>
        </view>
      </view>
    </view>

    <!-- 实时功耗图表 -->
    <view class="chart-section">
      <view class="section-header">
        <text class="section-title">实时功耗趋势</text>
        <view class="time-range">
          <text 
            v-for="range in timeRanges" 
            :key="range.value"
            class="time-tab"
            :class="{ active: activeTimeRange === range.value }"
            @click="switchTimeRange(range.value)"
          >
            {{ range.label }}
          </text>
        </view>
      </view>
      
      <view class="chart-container">
        <view class="chart-placeholder">
          <canvas 
            canvas-id="powerChart" 
            class="chart-canvas"
            @touchstart="onChartTouch"
            @touchmove="onChartTouch"
          ></canvas>
          
          <!-- 图表数据点提示 -->
          <view v-if="chartTooltip.show" class="chart-tooltip" :style="tooltipStyle">
            <text class="tooltip-time">{{ chartTooltip.time }}</text>
            <text class="tooltip-value">{{ chartTooltip.value }}kW</text>
          </view>
        </view>
        
        <!-- 图表图例 -->
        <view class="chart-legend">
          <view class="legend-item">
            <view class="legend-color" style="background-color: #1890ff;"></view>
            <text class="legend-text">实时功耗</text>
          </view>
          <view class="legend-item">
            <view class="legend-color" style="background-color: #52c41a;"></view>
            <text class="legend-text">平均功耗</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 设备能耗排行 -->
    <view class="ranking-section">
      <view class="section-header">
        <text class="section-title">设备能耗排行</text>
        <view class="ranking-filter">
          <picker 
            :value="rankingFilter" 
            :range="rankingOptions" 
            range-key="label"
            @change="updateRankingFilter"
          >
            <view class="filter-text">{{ getRankingFilterLabel() }}</view>
          </picker>
        </view>
      </view>
      
      <view class="ranking-list">
        <view 
          v-for="(device, index) in rankedDevices" 
          :key="device.id"
          class="ranking-item"
          @click="viewDeviceDetail(device)"
        >
          <view class="ranking-number" :class="getRankingClass(index)">
            <text>{{ index + 1 }}</text>
          </view>
          
          <view class="device-info">
            <view class="device-header">
              <text class="device-name">{{ device.name }}</text>
              <view class="device-status" :class="getStatusClass(device.status)">
                <text class="status-dot"></text>
                <text class="status-text">{{ getStatusText(device.status) }}</text>
              </view>
            </view>
            <text class="device-location">{{ device.location }}</text>
            
            <view class="power-info">
              <view class="power-bar">
                <view 
                  class="power-fill" 
                  :style="{ width: (device.power / maxPower * 100) + '%' }"
                ></view>
              </view>
              <text class="power-value">{{ device.power.toFixed(1) }}W</text>
            </view>
            
            <view class="device-stats">
              <view class="stat-item">
                <text class="stat-label">今日用电</text>
                <text class="stat-value">{{ device.dailyConsumption.toFixed(2) }}kWh</text>
              </view>
              <view class="stat-item">
                <text class="stat-label">运行时长</text>
                <text class="stat-value">{{ formatDuration(device.runTime) }}</text>
              </view>
              <view class="stat-item">
                <text class="stat-label">能效等级</text>
                <text class="stat-value" :class="getEfficiencyClass(device.efficiency)">
                  {{ device.efficiency }}
                </text>
              </view>
            </view>
          </view>
          
          <view class="device-actions">
            <button class="btn-detail" @click.stop="viewDeviceDetail(device)">
              <text>详情</text>
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 告警信息 -->
    <view class="alert-section" v-if="alerts.length > 0">
      <view class="section-header">
        <text class="section-title">告警信息</text>
        <view class="alert-count">
          <text class="count-text">{{ alerts.length }}条</text>
          <button class="btn-clear-all" @click="clearAllAlerts">
            <text>全部清除</text>
          </button>
        </view>
      </view>
      
      <view class="alert-list">
        <view 
          v-for="alert in alerts" 
          :key="alert.id"
          class="alert-item"
          :class="getAlertClass(alert.level)"
        >
          <view class="alert-icon">
            <text>{{ getAlertIcon(alert.level) }}</text>
          </view>
          
          <view class="alert-content">
            <view class="alert-header">
              <text class="alert-title">{{ alert.title }}</text>
              <text class="alert-time">{{ formatTime(alert.time) }}</text>
            </view>
            <text class="alert-message">{{ alert.message }}</text>
            
            <view class="alert-device" v-if="alert.deviceName">
              <text class="device-label">设备：</text>
              <text class="device-name">{{ alert.deviceName }}</text>
              <text class="device-location">({{ alert.deviceLocation }})</text>
            </view>
          </view>
          
          <view class="alert-actions">
            <button class="btn-handle" @click="handleAlert(alert)">
              <text>处理</text>
            </button>
            <button class="btn-dismiss" @click="dismissAlert(alert.id)">
              <text>忽略</text>
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-if="alerts.length === 0" class="empty-alerts">
      <text class="empty-icon">✅</text>
      <text class="empty-text">暂无告警信息</text>
      <text class="empty-subtitle">系统运行正常</text>
    </view>

    <!-- 设置弹窗 -->
    <uni-popup ref="settingsModal" type="center">
      <view class="settings-modal">
        <view class="modal-header">
          <text class="modal-title">监控设置</text>
          <button class="btn-close" @click="closeSettings">✕</button>
        </view>
        
        <view class="modal-content">
          <view class="setting-section">
            <text class="section-title">告警阈值</text>
            
            <view class="setting-item">
              <text class="setting-label">功耗告警阈值（kW）</text>
              <input 
                v-model.number="settings.powerThreshold" 
                class="setting-input" 
                type="number" 
                placeholder="请输入阈值"
              />
            </view>
            
            <view class="setting-item">
              <text class="setting-label">费用告警阈值（元/日）</text>
              <input 
                v-model.number="settings.costThreshold" 
                class="setting-input" 
                type="number" 
                placeholder="请输入阈值"
              />
            </view>
            
            <view class="setting-item">
              <text class="setting-label">能效告警阈值（%）</text>
              <input 
                v-model.number="settings.efficiencyThreshold" 
                class="setting-input" 
                type="number" 
                placeholder="请输入阈值"
              />
            </view>
          </view>
          
          <view class="setting-section">
            <text class="section-title">刷新设置</text>
            
            <view class="setting-item">
              <view class="setting-switch">
                <text class="setting-label">自动刷新</text>
                <switch :checked="settings.autoRefresh" @change="onAutoRefreshChange" />
              </view>
            </view>
            
            <view v-if="settings.autoRefresh" class="setting-item">
              <text class="setting-label">刷新间隔（秒）</text>
              <picker 
                :value="settings.refreshInterval" 
                :range="refreshIntervals" 
                range-key="label"
                @change="updateRefreshInterval"
              >
                <view class="picker-text">{{ getRefreshIntervalLabel() }}</view>
              </picker>
            </view>
          </view>
          
          <view class="setting-section">
            <text class="section-title">显示设置</text>
            
            <view class="setting-item">
              <view class="setting-switch">
                <text class="setting-label">显示图表网格</text>
                <switch :checked="settings.showGrid" @change="onShowGridChange" />
              </view>
            </view>
            
            <view class="setting-item">
              <view class="setting-switch">
                <text class="setting-label">显示数据点</text>
                <switch :checked="settings.showDataPoints" @change="onShowDataPointsChange" />
              </view>
            </view>
            
            <view class="setting-item">
              <view class="setting-switch">
                <text class="setting-label">启用动画效果</text>
                <switch :checked="settings.enableAnimation" @change="onEnableAnimationChange" />
              </view>
            </view>
          </view>
        </view>
        
        <view class="modal-actions">
          <button class="btn-cancel" @click="closeSettings">取消</button>
          <button class="btn-save" @click="saveSettings">保存</button>
        </view>
      </view>
    </uni-popup>

    <!-- 设备详情弹窗 -->
    <uni-popup ref="deviceModal" type="center">
      <view class="device-modal" v-if="selectedDevice">
        <view class="modal-header">
          <text class="modal-title">设备详情</text>
          <button class="btn-close" @click="closeDeviceModal">✕</button>
        </view>
        
        <view class="modal-content">
          <view class="device-overview">
            <view class="device-header">
              <text class="device-name">{{ selectedDevice.name }}</text>
              <view class="device-status" :class="getStatusClass(selectedDevice.status)">
                <text class="status-dot"></text>
                <text class="status-text">{{ getStatusText(selectedDevice.status) }}</text>
              </view>
            </view>
            <text class="device-location">{{ selectedDevice.location }}</text>
            
            <view class="device-metrics">
              <view class="metric-item">
                <text class="metric-label">当前功耗</text>
                <text class="metric-value">{{ selectedDevice.power.toFixed(1) }}W</text>
              </view>
              <view class="metric-item">
                <text class="metric-label">今日用电</text>
                <text class="metric-value">{{ selectedDevice.dailyConsumption.toFixed(2) }}kWh</text>
              </view>
              <view class="metric-item">
                <text class="metric-label">运行时长</text>
                <text class="metric-value">{{ formatDuration(selectedDevice.runTime) }}</text>
              </view>
              <view class="metric-item">
                <text class="metric-label">能效等级</text>
                <text class="metric-value" :class="getEfficiencyClass(selectedDevice.efficiency)">
                  {{ selectedDevice.efficiency }}
                </text>
              </view>
            </view>
          </view>
          
          <view class="device-history">
            <text class="section-title">24小时功耗历史</text>
            <view class="history-chart">
              <canvas 
                canvas-id="deviceChart" 
                class="device-chart-canvas"
              ></canvas>
            </view>
          </view>
        </view>
        
        <view class="modal-actions">
          <button class="btn-cancel" @click="closeDeviceModal">关闭</button>
          <button class="btn-control" @click="controlDevice">
            <text>设备控制</text>
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { getOverview, getAlerts } from '@/api/energy'
import { getDeviceList } from '@/api/device'
import { authGuard } from '@/utils/authGuard'

export default {
  name: 'MonitorPage',
  setup() {
    // 响应式数据
    const isRefreshing = ref(false)
    const totalPower = ref(12.5)
    const totalPowerTrend = ref(2.3)
    const dailyCost = ref(45.67)
    const dailyCostTrend = ref(-1.2)
    const efficiency = ref(87.5)
    const efficiencyTrend = ref(3.1)
    const activeDevices = ref(24)
    const totalDevices = ref(28)
    const activeTimeRange = ref('1h')
    const rankingFilter = ref(0)
    const selectedDevice = ref(null)
    const maxPower = ref(500)
    const refreshTimer = ref(null)
    
    const chartTooltip = reactive({
      show: false,
      x: 0,
      y: 0,
      time: '',
      value: ''
    })
    
    const settings = reactive({
      powerThreshold: 15,
      costThreshold: 100,
      efficiencyThreshold: 70,
      autoRefresh: true,
      refreshInterval: 1,
      showGrid: true,
      showDataPoints: true,
      enableAnimation: true
    })
    
    const timeRanges = [
      { value: '1h', label: '1小时' },
      { value: '6h', label: '6小时' },
      { value: '24h', label: '24小时' },
      { value: '7d', label: '7天' }
    ]
    
    const rankingOptions = [
      { value: 'power', label: '按功耗排序' },
      { value: 'consumption', label: '按用电量排序' },
      { value: 'efficiency', label: '按能效排序' },
      { value: 'runtime', label: '按运行时长排序' }
    ]
    
    const refreshIntervals = [
      { value: 0, label: '30秒' },
      { value: 1, label: '1分钟' },
      { value: 2, label: '5分钟' },
      { value: 3, label: '10分钟' }
    ]
    
    const devices = ref([])
    
    const alerts = ref([])

    // 计算属性
    const rankedDevices = computed(() => {
      const sortKey = rankingOptions[rankingFilter.value].value
      return [...devices.value].sort((a, b) => {
        switch (sortKey) {
          case 'power':
            return b.power - a.power
          case 'consumption':
            return b.dailyConsumption - a.dailyConsumption
          case 'efficiency':
            return getEfficiencyScore(b.efficiency) - getEfficiencyScore(a.efficiency)
          case 'runtime':
            return b.runTime - a.runTime
          default:
            return b.power - a.power
        }
      })
    })
    
    const tooltipStyle = computed(() => {
      return {
        left: chartTooltip.x + 'px',
        top: chartTooltip.y + 'px'
      }
    })

    // 方法
    const initData = async () => {
      try {
        isRefreshing.value = true
        
        // 并行获取所有数据
        const [overviewData, deviceData, alertData] = await Promise.all([
          getOverview(),
          getDeviceList(),
          getAlerts()
        ])
        
        // 更新实时数据
        totalPower.value = overviewData.totalPower || 0
        totalPowerTrend.value = overviewData.totalPowerTrend || 0
        dailyCost.value = overviewData.dailyCost || 0
        dailyCostTrend.value = overviewData.dailyCostTrend || 0
        efficiency.value = overviewData.efficiency || 0
        efficiencyTrend.value = overviewData.efficiencyTrend || 0
        activeDevices.value = overviewData.activeDevices || 0
        totalDevices.value = overviewData.totalDevices || 0
        
        // 更新设备数据
        devices.value = deviceData.map(device => ({
          ...device,
          dailyConsumption: device.dailyConsumption || 0,
          runTime: device.runTime || 0,
          efficiency: device.efficiency || 'B',
          type: device.type || 'led'
        }))
        
        // 更新告警数据
        alerts.value = alertData || []
        
        // 计算最大功耗用于图表显示
        if (devices.value.length > 0) {
          maxPower.value = Math.max(...devices.value.map(d => d.power || 0))
        }
        
        // 绘制图表
        drawChart()
        
      } catch (error) {
        console.error('初始化数据失败:', error)
        uni.showToast({ title: error.message || '数据加载失败', icon: 'error' })
      } finally {
        isRefreshing.value = false
      }
      
      // 启动自动刷新
      if (settings.autoRefresh) {
        startAutoRefresh()
      }
    }

    const refreshData = async () => {
      try {
        await initData()
        uni.showToast({ title: '数据已刷新', icon: 'success' })
      } catch (error) {
        console.error('刷新数据失败:', error)
        uni.showToast({ title: error.message || '刷新失败', icon: 'error' })
      }
    }

    const startAutoRefresh = () => {
      if (refreshTimer.value) {
        clearInterval(refreshTimer.value)
      }
      
      const intervals = [30000, 60000, 300000, 600000] // 30秒, 1分钟, 5分钟, 10分钟
      const interval = intervals[settings.refreshInterval]
      
      refreshTimer.value = setInterval(() => {
        refreshData()
      }, interval)
    }

    const stopAutoRefresh = () => {
      if (refreshTimer.value) {
        clearInterval(refreshTimer.value)
        refreshTimer.value = null
      }
    }

    const getTrendClass = (trend) => {
      return {
        'trend-up': trend > 0,
        'trend-down': trend < 0,
        'trend-stable': trend === 0
      }
    }

    const getTrendIcon = (trend) => {
      if (trend > 0) return '↗'
      if (trend < 0) return '↘'
      return '→'
    }

    const switchTimeRange = (range) => {
      activeTimeRange.value = range
      // 重新绘制图表
      drawChart()
    }

    const updateRankingFilter = (event) => {
      rankingFilter.value = event.detail.value
    }

    const getRankingFilterLabel = () => {
      return rankingOptions[rankingFilter.value].label
    }

    const getRankingClass = (index) => {
      return {
        'rank-first': index === 0,
        'rank-second': index === 1,
        'rank-third': index === 2
      }
    }

    const getStatusClass = (status) => {
      return {
        'status-online': status === 'online',
        'status-offline': status === 'offline',
        'status-warning': status === 'warning',
        'status-error': status === 'error'
      }
    }

    const getStatusText = (status) => {
      const statusMap = {
        online: '在线',
        offline: '离线',
        warning: '告警',
        error: '故障'
      }
      return statusMap[status] || '未知'
    }

    const getEfficiencyClass = (efficiency) => {
      return {
        'efficiency-a-plus': efficiency === 'A+',
        'efficiency-a': efficiency === 'A',
        'efficiency-b-plus': efficiency === 'B+',
        'efficiency-b': efficiency === 'B',
        'efficiency-c': efficiency === 'C'
      }
    }

    const getEfficiencyScore = (efficiency) => {
      const scoreMap = {
        'A+': 5,
        'A': 4,
        'B+': 3,
        'B': 2,
        'C': 1
      }
      return scoreMap[efficiency] || 0
    }

    const formatDuration = (seconds) => {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      
      if (hours > 0) {
        return `${hours}小时${minutes}分钟`
      }
      return `${minutes}分钟`
    }

    const formatTime = (timeStr) => {
      const date = new Date(timeStr)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) {
        return '刚刚'
      } else if (diff < 3600000) {
        return `${Math.floor(diff / 60000)}分钟前`
      } else if (diff < 86400000) {
        return `${Math.floor(diff / 3600000)}小时前`
      } else {
        return date.toLocaleDateString()
      }
    }

    const getAlertClass = (level) => {
      return {
        'alert-high': level === 'high',
        'alert-medium': level === 'medium',
        'alert-low': level === 'low'
      }
    }

    const getAlertIcon = (level) => {
      const iconMap = {
        high: '🚨',
        medium: '⚠️',
        low: 'ℹ️'
      }
      return iconMap[level] || 'ℹ️'
    }

    const viewDeviceDetail = (device) => {
      selectedDevice.value = device
      uni.$refs.deviceModal.open()
      
      // 绘制设备历史图表
      setTimeout(() => {
        drawDeviceChart(device)
      }, 100)
    }

    const closeDeviceModal = () => {
      uni.$refs.deviceModal.close()
      selectedDevice.value = null
    }

    const controlDevice = () => {
      uni.navigateTo({
        url: `/pages/lighting/control?deviceId=${selectedDevice.value.id}`
      })
    }

    const handleAlert = (alert) => {
      uni.showModal({
        title: '处理告警',
        content: `确定要处理告警"${alert.title}"吗？`,
        success: (res) => {
          if (res.confirm) {
            dismissAlert(alert.id)
            uni.showToast({ title: '告警已处理', icon: 'success' })
          }
        }
      })
    }

    const dismissAlert = (alertId) => {
      const index = alerts.value.findIndex(alert => alert.id === alertId)
      if (index > -1) {
        alerts.value.splice(index, 1)
      }
    }

    const clearAllAlerts = () => {
      uni.showModal({
        title: '清除所有告警',
        content: '确定要清除所有告警信息吗？',
        success: (res) => {
          if (res.confirm) {
            alerts.value = []
            uni.showToast({ title: '已清除所有告警', icon: 'success' })
          }
        }
      })
    }

    // 设置相关方法
    const showSettings = () => {
      uni.$refs.settingsModal.open()
    }

    const closeSettings = () => {
      uni.$refs.settingsModal.close()
    }

    const saveSettings = () => {
      // 保存设置到本地存储
      uni.setStorageSync('monitorSettings', settings)
      
      // 重新启动自动刷新
      if (settings.autoRefresh) {
        startAutoRefresh()
      } else {
        stopAutoRefresh()
      }
      
      closeSettings()
      uni.showToast({ title: '设置已保存', icon: 'success' })
    }

    const updateRefreshInterval = (event) => {
      settings.refreshInterval = event.detail.value
    }

    const getRefreshIntervalLabel = () => {
      return refreshIntervals[settings.refreshInterval].label
    }

    const onAutoRefreshChange = (event) => {
  settings.autoRefresh = event.detail.value
}

const onShowGridChange = (event) => {
  settings.showGrid = event.detail.value
}

const onShowDataPointsChange = (event) => {
  settings.showDataPoints = event.detail.value
}

const onEnableAnimationChange = (event) => {
  settings.enableAnimation = event.detail.value
}

    // 图表相关方法
    const drawChart = () => {
      // 这里应该使用实际的图表库，如 uCharts 或 F2
      // 由于是示例，这里只是占位
      console.log('绘制功耗趋势图表')
    }

    const drawDeviceChart = (device) => {
      // 绘制设备历史图表
      console.log('绘制设备历史图表', device.name)
    }

    const onChartTouch = (event) => {
      // 处理图表触摸事件，显示数据点提示
      const touch = event.touches[0]
      chartTooltip.x = touch.x
      chartTooltip.y = touch.y - 50
      chartTooltip.time = '12:30'
      chartTooltip.value = '12.5'
      chartTooltip.show = true
      
      // 3秒后隐藏提示
      setTimeout(() => {
        chartTooltip.show = false
      }, 3000)
    }

    // 生命周期
    onMounted(async () => {
      // 检查登录状态
      const canAccess = await authGuard({
        requireAuth: true,
        message: '能耗监控页面需要登录后才能访问'
      })
      
      if (!canAccess) {
        return
      }
      
      // 加载保存的设置
      const savedSettings = uni.getStorageSync('monitorSettings')
      if (savedSettings) {
        Object.assign(settings, savedSettings)
      }
      
      initData()
      drawChart()
    })

    onUnmounted(() => {
      stopAutoRefresh()
    })

    return {
      isRefreshing,
      totalPower,
      totalPowerTrend,
      dailyCost,
      dailyCostTrend,
      efficiency,
      efficiencyTrend,
      activeDevices,
      totalDevices,
      activeTimeRange,
      rankingFilter,
      selectedDevice,
      chartTooltip,
      settings,
      timeRanges,
      rankingOptions,
      refreshIntervals,
      devices,
      alerts,
      rankedDevices,
      tooltipStyle,
      refreshData,
      getTrendClass,
      getTrendIcon,
      switchTimeRange,
      updateRankingFilter,
      getRankingFilterLabel,
      getRankingClass,
      getStatusClass,
      getStatusText,
      getEfficiencyClass,
      formatDuration,
      formatTime,
      getAlertClass,
      getAlertIcon,
      viewDeviceDetail,
      closeDeviceModal,
      controlDevice,
      handleAlert,
      dismissAlert,
      clearAllAlerts,
      showSettings,
      closeSettings,
      saveSettings,
      updateRefreshInterval,
      getRefreshIntervalLabel,
      onAutoRefreshChange,
      onShowGridChange,
      onShowDataPointsChange,
      onEnableAnimationChange,
      onChartTouch
    }
  }
}
</script>

<style scoped>
.monitor-page {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 15rpx;
}

.btn-refresh,
.btn-settings {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background-color: white;
  border: 1rpx solid #d9d9d9;
  border-radius: 6rpx;
  font-size: 26rpx;
  color: #666;
}

.btn-refresh {
  background-color: #1890ff;
  color: white;
  border-color: #1890ff;
}

.icon {
  font-size: 24rpx;
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 概览卡片 */
.overview-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.overview-card {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.card-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.card-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.card-icon.cost {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.card-icon.efficiency {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.card-icon.devices {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 8rpx;
}

.card-unit {
  font-size: 24rpx;
  color: #666;
}

.card-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin: 8rpx 0;
}

.card-subtitle {
  font-size: 22rpx;
  color: #999;
}

.card-trend {
  display: flex;
  align-items: center;
  gap: 4rpx;
  font-size: 22rpx;
}

.trend-up {
  color: #f5222d;
}

.trend-down {
  color: #52c41a;
}

.trend-stable {
  color: #666;
}

.trend-icon {
  font-size: 20rpx;
}

/* 图表区域 */
.chart-section {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.time-range {
  display: flex;
  gap: 10rpx;
}

.time-tab {
  padding: 8rpx 16rpx;
  background-color: #f0f0f0;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666;
  cursor: pointer;
  transition: all 0.3s;
}

.time-tab.active {
  background-color: #1890ff;
  color: white;
}

.chart-container {
  position: relative;
}

.chart-placeholder {
  position: relative;
  height: 400rpx;
  background-color: #fafafa;
  border-radius: 8rpx;
  overflow: hidden;
}

.chart-canvas {
  width: 100%;
  height: 100%;
}

.chart-tooltip {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10rpx 15rpx;
  border-radius: 6rpx;
  font-size: 22rpx;
  pointer-events: none;
  z-index: 10;
}

.tooltip-time {
  display: block;
  margin-bottom: 5rpx;
}

.tooltip-value {
  font-weight: bold;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 30rpx;
  margin-top: 20rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.legend-color {
  width: 20rpx;
  height: 20rpx;
  border-radius: 2rpx;
}

.legend-text {
  font-size: 24rpx;
  color: #666;
}

/* 设备排行 */
.ranking-section {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.ranking-filter {
  padding: 8rpx 16rpx;
  background-color: #f0f0f0;
  border-radius: 6rpx;
  font-size: 24rpx;
  color: #666;
}

.filter-text {
  font-size: 24rpx;
  color: #666;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 25rpx;
  background-color: #fafafa;
  border-radius: 12rpx;
  cursor: pointer;
  transition: all 0.3s;
}

.ranking-item:hover {
  background-color: #f0f0f0;
  transform: translateY(-2rpx);
}

.ranking-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  background-color: #d9d9d9;
}

.rank-first {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #333;
}

.rank-second {
  background: linear-gradient(135deg, #c0c0c0 0%, #e8e8e8 100%);
  color: #333;
}

.rank-third {
  background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
  color: white;
}

.device-info {
  flex: 1;
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.device-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.device-status {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 22rpx;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
}

.status-online .status-dot {
  background-color: #52c41a;
}

.status-offline .status-dot {
  background-color: #d9d9d9;
}

.status-warning .status-dot {
  background-color: #faad14;
}

.status-error .status-dot {
  background-color: #f5222d;
}

.status-online {
  color: #52c41a;
}

.status-offline {
  color: #d9d9d9;
}

.status-warning {
  color: #faad14;
}

.status-error {
  color: #f5222d;
}

.device-location {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.power-info {
  display: flex;
  align-items: center;
  gap: 15rpx;
  margin-bottom: 15rpx;
}

.power-bar {
  flex: 1;
  height: 8rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.power-fill {
  height: 100%;
  background: linear-gradient(90deg, #52c41a 0%, #1890ff 100%);
  transition: width 0.3s;
}

.power-value {
  font-size: 24rpx;
  font-weight: bold;
  color: #1890ff;
  min-width: 80rpx;
  text-align: right;
}

.device-stats {
  display: flex;
  gap: 30rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666;
}

.stat-value {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
}

.efficiency-a-plus {
  color: #52c41a;
}

.efficiency-a {
  color: #1890ff;
}

.efficiency-b-plus {
  color: #faad14;
}

.efficiency-b {
  color: #fa8c16;
}

.efficiency-c {
  color: #f5222d;
}

.device-actions {
  display: flex;
  gap: 10rpx;
}

.btn-detail {
  padding: 12rpx 20rpx;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 6rpx;
  font-size: 24rpx;
}

/* 告警区域 */
.alert-section {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.alert-count {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.count-text {
  font-size: 24rpx;
  color: #666;
}

.btn-clear-all {
  padding: 8rpx 16rpx;
  background-color: #f5222d;
  color: white;
  border: none;
  border-radius: 4rpx;
  font-size: 22rpx;
}

.alert-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  padding: 25rpx;
  border-radius: 8rpx;
  border-left: 6rpx solid;
}

.alert-high {
  background-color: #fff2f0;
  border-left-color: #f5222d;
}

.alert-medium {
  background-color: #fffbe6;
  border-left-color: #faad14;
}

.alert-low {
  background-color: #f6ffed;
  border-left-color: #52c41a;
}

.alert-icon {
  font-size: 32rpx;
  margin-top: 5rpx;
}

.alert-content {
  flex: 1;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.alert-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.alert-time {
  font-size: 22rpx;
  color: #999;
}

.alert-message {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 10rpx;
}

.alert-device {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #666;
}

.device-label {
  color: #999;
}

.alert-actions {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.btn-handle,
.btn-dismiss {
  padding: 10rpx 20rpx;
  border: none;
  border-radius: 4rpx;
  font-size: 22rpx;
  min-width: 80rpx;
}

.btn-handle {
  background-color: #1890ff;
  color: white;
}

.btn-dismiss {
  background-color: #f0f0f0;
  color: #666;
}

/* 空状态 */
.empty-alerts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 50rpx;
  background-color: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.empty-subtitle {
  font-size: 24rpx;
  color: #999;
}

/* 弹窗样式 */
.settings-modal,
.device-modal {
  background-color: white;
  border-radius: 16rpx;
  width: 700rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.btn-close {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f8f9fa;
  border: none;
  font-size: 28rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  padding: 30rpx;
  max-height: 600rpx;
  overflow-y: auto;
}

/* 设置表单 */
.setting-section {
  margin-bottom: 40rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.setting-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.setting-item {
  margin-bottom: 30rpx;
}

.setting-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.setting-input {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.setting-switch {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.picker-text {
  padding: 20rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
}

/* 设备详情 */
.device-overview {
  margin-bottom: 30rpx;
}

.device-metrics {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-top: 20rpx;
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.metric-label {
  font-size: 24rpx;
  color: #666;
}

.metric-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #1890ff;
}

.device-history {
  border-top: 1rpx solid #e9ecef;
  padding-top: 30rpx;
}

.history-chart {
  height: 300rpx;
  background-color: #fafafa;
  border-radius: 8rpx;
  margin-top: 20rpx;
}

.device-chart-canvas {
  width: 100%;
  height: 100%;
}

.modal-actions {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #e9ecef;
}

.btn-cancel {
  flex: 1;
  padding: 25rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #dee2e6;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #495057;
}

.btn-save,
.btn-control {
  flex: 1;
  padding: 25rpx;
  background-color: #1890ff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: white;
}
</style>