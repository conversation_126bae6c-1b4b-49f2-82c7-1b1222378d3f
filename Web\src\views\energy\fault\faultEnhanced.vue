<template>
  <el-dialog
    v-model="visible"
    title="故障管理增强功能"
    width="90%"
    :close-on-click-modal="false"
    destroy-on-close
    class="fault-enhanced-dialog"
  >
    <el-tabs v-model="activeTab" type="border-card" class="enhanced-tabs">
      <!-- 工单流程 -->
      <el-tab-pane label="工单流程" name="workflow">
        <div class="workflow-section">
          <div class="workflow-stats">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-card class="stat-card">
                  <div class="stat-item">
                    <div class="stat-value pending">{{ workflowStats.pendingTickets }}</div>
                    <div class="stat-label">待处理工单</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card">
                  <div class="stat-item">
                    <div class="stat-value processing">{{ workflowStats.processingTickets }}</div>
                    <div class="stat-label">处理中工单</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card">
                  <div class="stat-item">
                    <div class="stat-value resolved">{{ workflowStats.resolvedTickets }}</div>
                    <div class="stat-label">已完成工单</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card">
                  <div class="stat-item">
                    <div class="stat-value avg-time">{{ workflowStats.avgProcessTime }}h</div>
                    <div class="stat-label">平均处理时长</div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>

          <div class="workflow-chart">
            <el-card>
              <template #header>
                <div class="chart-header">
                  <span>工单处理流程图</span>
                  <el-button type="primary" size="small" @click="createWorkOrder">创建工单</el-button>
                </div>
              </template>
              <div ref="workflowChartRef" style="height: 300px;"></div>
            </el-card>
          </div>

          <div class="workflow-list">
            <el-card>
              <template #header>
                <span>工单列表</span>
              </template>
              <el-table :data="workOrderList" stripe>
                <el-table-column prop="ticketNo" label="工单号" width="120" />
                <el-table-column prop="faultType" label="故障类型" width="100" />
                <el-table-column prop="deviceName" label="设备名称" width="150" />
                <el-table-column prop="priority" label="优先级" width="80">
                  <template #default="{ row }">
                    <el-tag :type="getPriorityColor(row.priority)">{{ getPriorityText(row.priority) }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="assignee" label="处理人" width="100" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getWorkOrderStatusColor(row.status)">{{ getWorkOrderStatusText(row.status) }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="createTime" label="创建时间" width="150" />
                <el-table-column label="操作" width="150">
                  <template #default="{ row }">
                    <el-button link type="primary" size="small" @click="viewWorkOrder(row)">查看</el-button>
                    <el-button link type="success" size="small" @click="processWorkOrder(row)" v-if="row.status !== 'completed'">处理</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </div>
        </div>
      </el-tab-pane>

      <!-- 预警设置 -->
      <el-tab-pane label="预警设置" name="alert">
        <div class="alert-section">
          <div class="alert-config">
            <el-card>
              <template #header>
                <div class="card-header">
                  <span>预警规则配置</span>
                  <el-button type="primary" size="small" @click="addAlertRule">新增规则</el-button>
                </div>
              </template>
              
              <el-table :data="alertRules" stripe>
                <el-table-column prop="ruleName" label="规则名称" width="150" />
                <el-table-column prop="ruleType" label="预警类型" width="120">
                  <template #default="{ row }">
                    <el-tag>{{ getAlertTypeText(row.ruleType) }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="condition" label="触发条件" min-width="200" />
                <el-table-column prop="level" label="预警级别" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getAlertLevelColor(row.level)">{{ getAlertLevelText(row.level) }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="enabled" label="状态" width="80">
                  <template #default="{ row }">
                    <el-switch v-model="row.enabled" @change="toggleAlertRule(row)" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150">
                  <template #default="{ row }">
                    <el-button link type="primary" size="small" @click="editAlertRule(row)">编辑</el-button>
                    <el-button link type="danger" size="small" @click="deleteAlertRule(row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </div>

          <div class="alert-history">
            <el-card>
              <template #header>
                <span>预警历史</span>
              </template>
              <div ref="alertTrendChartRef" style="height: 300px;"></div>
            </el-card>
          </div>

          <div class="alert-notifications">
            <el-card>
              <template #header>
                <span>通知设置</span>
              </template>
              <el-form :model="notificationSettings" label-width="120px">
                <el-form-item label="邮件通知">
                  <el-switch v-model="notificationSettings.emailEnabled" />
                  <el-input v-model="notificationSettings.emailList" placeholder="多个邮箱用逗号分隔" style="margin-left: 10px; width: 300px;" />
                </el-form-item>
                <el-form-item label="短信通知">
                  <el-switch v-model="notificationSettings.smsEnabled" />
                  <el-input v-model="notificationSettings.phoneList" placeholder="多个手机号用逗号分隔" style="margin-left: 10px; width: 300px;" />
                </el-form-item>
                <el-form-item label="微信通知">
                  <el-switch v-model="notificationSettings.wechatEnabled" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="saveNotificationSettings">保存设置</el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </div>
        </div>
      </el-tab-pane>

      <!-- 维修记录 -->
      <el-tab-pane label="维修记录" name="maintenance">
        <div class="maintenance-section">
          <div class="maintenance-stats">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-card class="stat-card">
                  <div class="stat-item">
                    <div class="stat-value">{{ maintenanceStats.totalRecords }}</div>
                    <div class="stat-label">总维修次数</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card">
                  <div class="stat-item">
                    <div class="stat-value cost">¥{{ maintenanceStats.totalCost }}</div>
                    <div class="stat-label">总维修费用</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card">
                  <div class="stat-item">
                    <div class="stat-value time">{{ maintenanceStats.avgRepairTime }}h</div>
                    <div class="stat-label">平均维修时长</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card">
                  <div class="stat-item">
                    <div class="stat-value rate">{{ maintenanceStats.successRate }}%</div>
                    <div class="stat-label">维修成功率</div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>

          <div class="maintenance-chart">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-card>
                  <template #header>
                    <span>维修趋势分析</span>
                  </template>
                  <div ref="maintenanceTrendChartRef" style="height: 300px;"></div>
                </el-card>
              </el-col>
              <el-col :span="12">
                <el-card>
                  <template #header>
                    <span>维修类型分布</span>
                  </template>
                  <div ref="maintenanceTypeChartRef" style="height: 300px;"></div>
                </el-card>
              </el-col>
            </el-row>
          </div>

          <div class="maintenance-records">
            <el-card>
              <template #header>
                <div class="card-header">
                  <span>维修记录</span>
                  <el-button type="primary" size="small" @click="addMaintenanceRecord">新增记录</el-button>
                </div>
              </template>
              
              <el-table :data="maintenanceRecords" stripe>
                <el-table-column prop="recordNo" label="记录编号" width="120" />
                <el-table-column prop="deviceName" label="设备名称" width="150" />
                <el-table-column prop="faultType" label="故障类型" width="120" />
                <el-table-column prop="maintenanceType" label="维修类型" width="120">
                  <template #default="{ row }">
                    <el-tag>{{ getMaintenanceTypeText(row.maintenanceType) }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="technician" label="维修人员" width="100" />
                <el-table-column prop="cost" label="费用" width="100">
                  <template #default="{ row }">
                    <span class="cost-value">¥{{ row.cost }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="duration" label="耗时" width="80">
                  <template #default="{ row }">
                    <span>{{ row.duration }}h</span>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getMaintenanceStatusColor(row.status)">{{ getMaintenanceStatusText(row.status) }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="completedTime" label="完成时间" width="150" />
                <el-table-column label="操作" width="120">
                  <template #default="{ row }">
                    <el-button link type="primary" size="small" @click="viewMaintenanceDetail(row)">详情</el-button>
                    <el-button link type="success" size="small" @click="exportMaintenanceReport(row)">导出</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
        <el-button type="primary" @click="refreshData">刷新数据</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'refresh'])

const visible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const activeTab = ref('workflow')

// 工单流程数据
const workflowStats = reactive({
  pendingTickets: 15,
  processingTickets: 8,
  resolvedTickets: 142,
  avgProcessTime: 4.2
})

const workOrderList = ref([
  {
    id: 1,
    ticketNo: 'WO202401001',
    faultType: '硬件故障',
    deviceName: 'LED灯具-001',
    priority: 'high',
    assignee: '张工',
    status: 'processing',
    createTime: '2024-01-15 09:30:00'
  },
  {
    id: 2,
    ticketNo: 'WO202401002',
    faultType: '网络故障',
    deviceName: 'LED灯具-002',
    priority: 'medium',
    assignee: '李工',
    status: 'pending',
    createTime: '2024-01-15 10:15:00'
  }
])

// 预警设置数据
const alertRules = ref([
  {
    id: 1,
    ruleName: '设备离线预警',
    ruleType: 'device_offline',
    condition: '设备离线时间超过5分钟',
    level: 'high',
    enabled: true
  },
  {
    id: 2,
    ruleName: '能耗异常预警',
    ruleType: 'energy_abnormal',
    condition: '能耗超过正常值20%',
    level: 'medium',
    enabled: true
  }
])

const notificationSettings = reactive({
  emailEnabled: true,
  emailList: '<EMAIL>,<EMAIL>',
  smsEnabled: false,
  phoneList: '',
  wechatEnabled: true
})

// 维修记录数据
const maintenanceStats = reactive({
  totalRecords: 89,
  totalCost: 15680,
  avgRepairTime: 3.5,
  successRate: 95.2
})

const maintenanceRecords = ref([
  {
    id: 1,
    recordNo: 'MR202401001',
    deviceName: 'LED灯具-001',
    faultType: '硬件故障',
    maintenanceType: 'replacement',
    technician: '张师傅',
    cost: 280,
    duration: 2.5,
    status: 'completed',
    completedTime: '2024-01-14 16:30:00'
  },
  {
    id: 2,
    recordNo: 'MR202401002',
    deviceName: 'LED灯具-002',
    faultType: '软件故障',
    maintenanceType: 'repair',
    technician: '李师傅',
    cost: 120,
    duration: 1.5,
    status: 'completed',
    completedTime: '2024-01-14 14:20:00'
  }
])

// 图表引用
const workflowChartRef = ref(null)
const alertTrendChartRef = ref(null)
const maintenanceTrendChartRef = ref(null)
const maintenanceTypeChartRef = ref(null)

// 图表实例
let workflowChart = null
let alertTrendChart = null
let maintenanceTrendChart = null
let maintenanceTypeChart = null

// 工单相关方法
const createWorkOrder = () => {
  ElMessage.info('创建工单功能开发中...')
}

const viewWorkOrder = (row) => {
  ElMessage.info(`查看工单: ${row.ticketNo}`)
}

const processWorkOrder = (row) => {
  ElMessage.info(`处理工单: ${row.ticketNo}`)
}

const getPriorityColor = (priority) => {
  const colorMap = {
    'low': 'info',
    'medium': 'warning',
    'high': 'danger',
    'urgent': 'danger'
  }
  return colorMap[priority] || 'info'
}

const getPriorityText = (priority) => {
  const textMap = {
    'low': '低',
    'medium': '中',
    'high': '高',
    'urgent': '紧急'
  }
  return textMap[priority] || priority
}

const getWorkOrderStatusColor = (status) => {
  const colorMap = {
    'pending': 'warning',
    'processing': 'primary',
    'completed': 'success',
    'cancelled': 'info'
  }
  return colorMap[status] || 'info'
}

const getWorkOrderStatusText = (status) => {
  const textMap = {
    'pending': '待处理',
    'processing': '处理中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return textMap[status] || status
}

// 预警相关方法
const addAlertRule = () => {
  ElMessage.info('新增预警规则功能开发中...')
}

const editAlertRule = (row) => {
  ElMessage.info(`编辑预警规则: ${row.ruleName}`)
}

const deleteAlertRule = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这条预警规则吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

const toggleAlertRule = (row) => {
  ElMessage.success(`${row.enabled ? '启用' : '禁用'}预警规则: ${row.ruleName}`)
}

const getAlertTypeText = (type) => {
  const textMap = {
    'device_offline': '设备离线',
    'energy_abnormal': '能耗异常',
    'temperature_high': '温度过高',
    'voltage_abnormal': '电压异常'
  }
  return textMap[type] || type
}

const getAlertLevelColor = (level) => {
  const colorMap = {
    'low': 'info',
    'medium': 'warning',
    'high': 'danger',
    'critical': 'danger'
  }
  return colorMap[level] || 'info'
}

const getAlertLevelText = (level) => {
  const textMap = {
    'low': '低',
    'medium': '中',
    'high': '高',
    'critical': '严重'
  }
  return textMap[level] || level
}

const saveNotificationSettings = () => {
  ElMessage.success('通知设置保存成功')
}

// 维修记录相关方法
const addMaintenanceRecord = () => {
  ElMessage.info('新增维修记录功能开发中...')
}

const viewMaintenanceDetail = (row) => {
  ElMessage.info(`查看维修详情: ${row.recordNo}`)
}

const exportMaintenanceReport = (row) => {
  ElMessage.info(`导出维修报告: ${row.recordNo}`)
}

const getMaintenanceTypeText = (type) => {
  const textMap = {
    'repair': '维修',
    'replacement': '更换',
    'upgrade': '升级',
    'cleaning': '清洁'
  }
  return textMap[type] || type
}

const getMaintenanceStatusColor = (status) => {
  const colorMap = {
    'pending': 'warning',
    'processing': 'primary',
    'completed': 'success',
    'failed': 'danger'
  }
  return colorMap[status] || 'info'
}

const getMaintenanceStatusText = (status) => {
  const textMap = {
    'pending': '待处理',
    'processing': '处理中',
    'completed': '已完成',
    'failed': '失败'
  }
  return textMap[status] || status
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    initWorkflowChart()
    initAlertTrendChart()
    initMaintenanceTrendChart()
    initMaintenanceTypeChart()
  })
}

// 工单流程图
const initWorkflowChart = () => {
  if (!workflowChartRef.value) return
  
  workflowChart = echarts.init(workflowChartRef.value)
  
  const option = {
    title: {
      text: '工单处理流程统计',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '工单状态',
        type: 'pie',
        radius: '50%',
        data: [
          { value: workflowStats.pendingTickets, name: '待处理' },
          { value: workflowStats.processingTickets, name: '处理中' },
          { value: workflowStats.resolvedTickets, name: '已完成' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  workflowChart.setOption(option)
}

// 预警趋势图
const initAlertTrendChart = () => {
  if (!alertTrendChartRef.value) return
  
  alertTrendChart = echarts.init(alertTrendChartRef.value)
  
  const option = {
    title: {
      text: '预警趋势分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['预警次数', '处理次数']
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '预警次数',
        type: 'line',
        data: [12, 8, 15, 6, 9, 4]
      },
      {
        name: '处理次数',
        type: 'line',
        data: [10, 7, 13, 5, 8, 3]
      }
    ]
  }
  
  alertTrendChart.setOption(option)
}

// 维修趋势图
const initMaintenanceTrendChart = () => {
  if (!maintenanceTrendChartRef.value) return
  
  maintenanceTrendChart = echarts.init(maintenanceTrendChartRef.value)
  
  const option = {
    title: {
      text: '维修趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '维修次数',
        type: 'bar',
        data: [15, 12, 18, 8, 14, 10],
        itemStyle: {
          color: '#409EFF'
        }
      }
    ]
  }
  
  maintenanceTrendChart.setOption(option)
}

// 维修类型分布图
const initMaintenanceTypeChart = () => {
  if (!maintenanceTypeChartRef.value) return
  
  maintenanceTypeChart = echarts.init(maintenanceTypeChartRef.value)
  
  const option = {
    title: {
      text: '维修类型分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '维修类型',
        type: 'pie',
        radius: ['40%', '70%'],
        data: [
          { value: 35, name: '维修' },
          { value: 25, name: '更换' },
          { value: 20, name: '升级' },
          { value: 9, name: '清洁' }
        ]
      }
    ]
  }
  
  maintenanceTypeChart.setOption(option)
}

// 刷新数据
const refreshData = () => {
  ElMessage.success('数据刷新成功')
  emit('refresh')
}

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    initCharts()
  }
})

// 监听窗口大小变化
const handleResize = () => {
  if (workflowChart) workflowChart.resize()
  if (alertTrendChart) alertTrendChart.resize()
  if (maintenanceTrendChart) maintenanceTrendChart.resize()
  if (maintenanceTypeChart) maintenanceTypeChart.resize()
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (workflowChart) workflowChart.dispose()
  if (alertTrendChart) alertTrendChart.dispose()
  if (maintenanceTrendChart) maintenanceTrendChart.dispose()
  if (maintenanceTypeChart) maintenanceTypeChart.dispose()
})
</script>

<style scoped lang="scss">
.fault-enhanced-dialog {
  .enhanced-tabs {
    :deep(.el-tabs__content) {
      padding: 20px;
    }
  }

  .workflow-section,
  .alert-section,
  .maintenance-section {
    .stat-card {
      margin-bottom: 20px;
      
      .stat-item {
        text-align: center;
        
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 8px;
          
          &.pending {
            color: #F56C6C;
          }
          
          &.processing {
            color: #E6A23C;
          }
          
          &.resolved {
            color: #67C23A;
          }
          
          &.avg-time {
            color: #409EFF;
          }
          
          &.cost {
            color: #F56C6C;
          }
          
          &.time {
            color: #E6A23C;
          }
          
          &.rate {
            color: #67C23A;
          }
        }
        
        .stat-label {
          font-size: 14px;
          color: #909399;
        }
      }
    }

    .workflow-chart,
    .workflow-list,
    .alert-config,
    .alert-history,
    .alert-notifications,
    .maintenance-chart,
    .maintenance-records {
      margin-bottom: 20px;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .cost-value {
      color: #F56C6C;
      font-weight: bold;
    }
  }

  .dialog-footer {
    text-align: right;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .fault-enhanced-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 5vh auto;
    }
    
    .enhanced-tabs {
      :deep(.el-tabs__content) {
        padding: 10px;
      }
    }
    
    .stat-card {
      margin-bottom: 10px;
      
      .stat-item {
        .stat-value {
          font-size: 20px;
        }
      }
    }
  }
}
</style>