using Admin.NET.Plugin.MQTT.Interfaces;
using Microsoft.Extensions.Logging;
using MQTTnet;
using Newtonsoft.Json;
using System;
using System.Diagnostics;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Admin.NET.Plugin.MQTT.Core
{
    /// <summary>
    /// 消息处理器基类
    /// 提供消息处理的通用功能和模板方法
    /// </summary>
    public abstract class MessageHandlerBase : IMessageHandler
    {
        /// <summary>
        /// 日志记录器
        /// </summary>
        protected readonly ILogger Logger;
        
        /// <summary>
        /// 异常处理器
        /// </summary>
        protected readonly IMqttExceptionHandler ExceptionHandler;
        
        /// <summary>
        /// 处理器名称
        /// </summary>
        public abstract string Name { get; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="exceptionHandler">异常处理器</param>
        protected MessageHandlerBase(ILogger logger, IMqttExceptionHandler exceptionHandler = null)
        {
            Logger = logger ?? throw new ArgumentNullException(nameof(logger));
            ExceptionHandler = exceptionHandler;
        }
     
        
        /// <summary>
        /// 验证消息
        /// </summary>
        /// <param name="message">MQTT消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否有效</returns>
        protected virtual Task<bool> ValidateMessageAsync(MqttApplicationMessage message, CancellationToken cancellationToken = default)
        {
            if (message == null)
            {
                return Task.FromResult(false);
            }
            
            if (string.IsNullOrEmpty(message.Topic))
            {
                return Task.FromResult(false);
            }
            
            return Task.FromResult(true);
        }
        
        /// <summary>
        /// 处理消息前的准备工作
        /// </summary>
        /// <param name="message">MQTT消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        protected virtual Task OnBeforeHandleAsync(MqttApplicationMessage message, CancellationToken cancellationToken = default)
        {
            return Task.CompletedTask;
        }
        
        /// <summary>
        /// 处理消息后的清理工作
        /// </summary>
        /// <param name="message">MQTT消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        protected virtual Task OnAfterHandleAsync(MqttApplicationMessage message, CancellationToken cancellationToken = default)
        {
            return Task.CompletedTask;
        }
        
        /// <summary>
        /// 处理消息的具体实现（子类必须实现）
        /// </summary>
        /// <param name="message">MQTT消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        protected abstract Task ProcessMessageAsync(MqttApplicationMessage message, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 获取消息负载的字符串内容
        /// </summary>
        /// <param name="message">MQTT消息</param>
        /// <returns>字符串内容</returns>
        protected string GetPayloadString(MqttApplicationMessage message)
        {
            if (message?.Payload == null || message.Payload.Length == 0)
            {
                return string.Empty;
            }
            
            return Encoding.UTF8.GetString(message.Payload);
        }
        
        /// <summary>
        /// 反序列化消息负载为指定类型
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="message">MQTT消息</param>
        /// <returns>反序列化后的对象</returns>
        protected T DeserializePayload<T>(MqttApplicationMessage message) where T : class
        {
            var payloadString = GetPayloadString(message);
            if (string.IsNullOrEmpty(payloadString))
            {
                return null;
            }
            
            try
            {
                return JsonConvert.DeserializeObject<T>(payloadString);
            }
            catch (JsonException ex)
            {
                Logger.LogWarning(ex, "反序列化消息负载失败: Handler={HandlerName}, Topic={Topic}, Payload={Payload}", 
                    Name, message.Topic, payloadString);
                return null;
            }
        }

        public async Task HandleAsync(string topic, string payload, MqttApplicationMessageReceivedEventArgs e)
        {
            var stopwatch = Stopwatch.StartNew();
            var context = new ExceptionContext
            {
                OperationName = "MessageHandling",
                ComponentName = Name,
                Data = new { Topic = message.Topic, PayloadLength = message.Payload?.Length ?? 0 }
            };

            try
            {
                Logger.LogDebug("开始处理消息: Handler={HandlerName}, Topic={Topic}, PayloadSize={PayloadSize}",
                    Name, message.Topic, message.Payload?.Length ?? 0);

                // 验证消息
                if (!await ValidateMessageAsync(message, cancellationToken))
                {
                    Logger.LogWarning("消息验证失败: Handler={HandlerName}, Topic={Topic}", Name, message.Topic);
                    return;
                }

                // 处理消息前的准备工作
                await OnBeforeHandleAsync(message, cancellationToken);

                // 执行具体的消息处理逻辑
                await ProcessMessageAsync(message, cancellationToken);

                // 处理消息后的清理工作
                await OnAfterHandleAsync(message, cancellationToken);

                stopwatch.Stop();
                Logger.LogDebug("消息处理完成: Handler={HandlerName}, Topic={Topic}, Duration={Duration}ms",
                    Name, message.Topic, stopwatch.ElapsedMilliseconds);
            }
            catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
            {
                Logger.LogInformation("消息处理被取消: Handler={HandlerName}, Topic={Topic}", Name, message.Topic);
                throw;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                Logger.LogError(ex, "消息处理失败: Handler={HandlerName}, Topic={Topic}, Duration={Duration}ms",
                    Name, message.Topic, stopwatch.ElapsedMilliseconds);

                if (ExceptionHandler != null)
                {
                    var result = await ExceptionHandler.HandleExceptionAsync(ex, context, cancellationToken);
                    if (!result.IsHandled)
                    {
                        throw;
                    }
                }
                else
                {
                    throw;
                }
            }
        }
    }
    
    /// <summary>
    /// 泛型消息处理器基类
    /// 提供强类型消息处理的通用功能
    /// </summary>
    /// <typeparam name="T">消息类型</typeparam>
    public abstract class MessageHandlerBase<T> : IMessageHandler<T> where T : class
    {
        /// <summary>
        /// 日志记录器
        /// </summary>
        protected readonly ILogger Logger;
        
        /// <summary>
        /// 异常处理器
        /// </summary>
        protected readonly IMqttExceptionHandler ExceptionHandler;
        
        /// <summary>
        /// 处理器名称
        /// </summary>
        public abstract string Name { get; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="exceptionHandler">异常处理器</param>
        protected MessageHandlerBase(ILogger logger, IMqttExceptionHandler exceptionHandler = null)
        {
            Logger = logger ?? throw new ArgumentNullException(nameof(logger));
            ExceptionHandler = exceptionHandler;
        }
        
        /// <summary>
        /// 处理强类型消息
        /// </summary>
        /// <param name="message">强类型消息</param>
        /// <param name="originalMessage">原始MQTT消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public async Task HandleAsync(T message, MqttApplicationMessage originalMessage, CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            var context = new ExceptionContext
            {
                OperationName = "TypedMessageHandling",
                ComponentName = Name,
                Data = new { 
                    Topic = originalMessage.Topic, 
                    MessageType = typeof(T).Name,
                    PayloadLength = originalMessage.Payload?.Length ?? 0 
                }
            };
            
            try
            {
                Logger.LogDebug("开始处理强类型消息: Handler={HandlerName}, Topic={Topic}, MessageType={MessageType}", 
                    Name, originalMessage.Topic, typeof(T).Name);
                
                // 验证消息
                if (!await ValidateMessageAsync(message, originalMessage, cancellationToken))
                {
                    Logger.LogWarning("强类型消息验证失败: Handler={HandlerName}, Topic={Topic}, MessageType={MessageType}", 
                        Name, originalMessage.Topic, typeof(T).Name);
                    return;
                }
                
                // 处理消息前的准备工作
                await OnBeforeHandleAsync(message, originalMessage, cancellationToken);
                
                // 执行具体的消息处理逻辑
                await ProcessMessageAsync(message, originalMessage, cancellationToken);
                
                // 处理消息后的清理工作
                await OnAfterHandleAsync(message, originalMessage, cancellationToken);
                
                stopwatch.Stop();
                Logger.LogDebug("强类型消息处理完成: Handler={HandlerName}, Topic={Topic}, MessageType={MessageType}, Duration={Duration}ms", 
                    Name, originalMessage.Topic, typeof(T).Name, stopwatch.ElapsedMilliseconds);
            }
            catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
            {
                Logger.LogInformation("强类型消息处理被取消: Handler={HandlerName}, Topic={Topic}, MessageType={MessageType}", 
                    Name, originalMessage.Topic, typeof(T).Name);
                throw;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                Logger.LogError(ex, "强类型消息处理失败: Handler={HandlerName}, Topic={Topic}, MessageType={MessageType}, Duration={Duration}ms", 
                    Name, originalMessage.Topic, typeof(T).Name, stopwatch.ElapsedMilliseconds);
                
                if (ExceptionHandler != null)
                {
                    var result = await ExceptionHandler.HandleExceptionAsync(ex, context, cancellationToken);
                    if (!result.IsHandled)
                    {
                        throw;
                    }
                }
                else
                {
                    throw;
                }
            }
        }
        
        /// <summary>
        /// 验证强类型消息
        /// </summary>
        /// <param name="message">强类型消息</param>
        /// <param name="originalMessage">原始MQTT消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否有效</returns>
        protected virtual Task<bool> ValidateMessageAsync(T message, MqttApplicationMessage originalMessage, CancellationToken cancellationToken = default)
        {
            if (message == null)
            {
                return Task.FromResult(false);
            }
            
            if (originalMessage == null || string.IsNullOrEmpty(originalMessage.Topic))
            {
                return Task.FromResult(false);
            }
            
            return Task.FromResult(true);
        }
        
        /// <summary>
        /// 处理消息前的准备工作
        /// </summary>
        /// <param name="message">强类型消息</param>
        /// <param name="originalMessage">原始MQTT消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        protected virtual Task OnBeforeHandleAsync(T message, MqttApplicationMessage originalMessage, CancellationToken cancellationToken = default)
        {
            return Task.CompletedTask;
        }
        
        /// <summary>
        /// 处理消息后的清理工作
        /// </summary>
        /// <param name="message">强类型消息</param>
        /// <param name="originalMessage">原始MQTT消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        protected virtual Task OnAfterHandleAsync(T message, MqttApplicationMessage originalMessage, CancellationToken cancellationToken = default)
        {
            return Task.CompletedTask;
        }
        
        /// <summary>
        /// 处理强类型消息的具体实现（子类必须实现）
        /// </summary>
        /// <param name="message">强类型消息</param>
        /// <param name="originalMessage">原始MQTT消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        protected abstract Task ProcessMessageAsync(T message, MqttApplicationMessage originalMessage, CancellationToken cancellationToken = default);
    }
}