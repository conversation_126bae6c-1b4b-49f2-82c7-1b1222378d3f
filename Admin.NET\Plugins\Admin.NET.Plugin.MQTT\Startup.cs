// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MQTTnet;
using MQTTnet.Client;
using System.Collections.Concurrent;
using System.Text;
using Newtonsoft.Json;
using Admin.NET.Plugin.MQTT.Core.Services;
using Admin.NET.Plugin.MQTT.Core.Configuration;
using Admin.NET.Plugin.MQTT.Core.Models;
using Admin.NET.Plugin.MQTT.Core.Entity;
// 所有类都在Admin.NET.Plugin.MQTT命名空间下

namespace Admin.NET.Plugin.MQTT;

/// <summary>
/// MQTT插件启动配置
/// </summary>
[AppStartup(100)]
public class Startup : AppStartup
{
    /// <summary>
    /// 配置服务
    /// </summary>
    /// <param name="services">服务集合</param>
    public void ConfigureServices(IServiceCollection services)
    {
        // 注册MQTT配置选项
        services.AddConfigurableOptions<MqttOptions>();
        
        // 注册MQTT核心服务
        services.AddSingleton<MqttClientManager>();
        services.AddSingleton<MqttMessageRouter>();
        
        // 注册设备服务
        services.AddScoped<DeviceControlService>();
        services.AddSingleton<DeviceEventService>();
        
        // 注册MQTT托管服务
        services.AddHostedService<MqttHostedService>();
        
        // 注册Web API控制器
        services.AddControllers();
        
        // 注册日志和监控服务
        services.AddSingleton<MqttMonitoringService>();
        services.AddSingleton<MqttLogService>();
    }

    /// <summary>
    /// 配置应用程序
    /// </summary>
    /// <param name="app">应用程序构建器</param>
    /// <param name="env">环境</param>
    public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
    {
        // MQTT插件不需要特殊的中间件配置
        // 所有功能通过托管服务和依赖注入实现
    }
}

/// <summary>
/// MQTT托管服务
/// </summary>
public class MqttHostedService : BackgroundService
{
    private readonly ILogger<MqttHostedService> _logger;
    private readonly MqttClientManager _clientManager;
    private readonly MqttMessageRouter _messageRouter;
    private readonly DeviceEventService _deviceEventService;
    private readonly IServiceProvider _serviceProvider;
    private readonly MqttOptions _options;

    /// <summary>
    /// 构造函数
    /// </summary>
    public MqttHostedService(
        ILogger<MqttHostedService> logger,
        MqttClientManager clientManager,
        MqttMessageRouter messageRouter,
        DeviceEventService deviceEventService,
        IServiceProvider serviceProvider,
        IOptions<MqttOptions> options)
    {
        _logger = logger;
        _clientManager = clientManager;
        _messageRouter = messageRouter;
        _deviceEventService = deviceEventService;
        _serviceProvider = serviceProvider;
        _options = options.Value;
    }

    /// <summary>
    /// 启动服务
    /// </summary>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            _logger.LogInformation("MQTT插件服务启动中...");

            // 初始化MQTT客户端管理器
            await _clientManager.StartAsync();

            // 注册设备事件处理器 - 使用正确的主题模式
            await _messageRouter.RegisterHandlerAsync<DeviceEventMessage>("/sys/+/+/thing/event/+/post", _deviceEventService);

            // 订阅默认主题
            await SubscribeDefaultTopicsAsync();

            // 启动MQTT连接
            await _clientManager.StartAsync();

            _logger.LogInformation("MQTT插件服务启动成功");

            // 保持服务运行
            await Task.Delay(Timeout.Infinite, stoppingToken);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("MQTT插件服务正在停止...");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MQTT插件服务启动失败");
            throw;
        }
    }

    /// <summary>
    /// 停止服务
    /// </summary>
    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("MQTT插件服务停止中...");

            // 停止MQTT连接
            await _clientManager.StopAsync();

            _logger.LogInformation("MQTT插件服务已停止");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MQTT插件服务停止失败");
        }

        await base.StopAsync(cancellationToken);
    }

    /// <summary>
    /// 订阅默认主题
    /// </summary>
    private async Task SubscribeDefaultTopicsAsync()
    {
        try
        {
            // 订阅配置中的主题
            if (_options.SubscribeTopics?.Any() == true)
            {
                foreach (var topic in _options.SubscribeTopics)
                {
                    _messageRouter.Subscribe(topic, _options.DefaultQoS);
                    _logger.LogInformation("订阅主题: {Topic}", topic);
                }
            }
            else
            {
                // 订阅默认的网关发布主题（设备上报数据）
                var defaultTopic = "/sys/+/+/thing/event/+/post";
                _messageRouter.Subscribe(defaultTopic, _options.DefaultQoS);
                _logger.LogInformation("订阅默认主题: {Topic}", defaultTopic);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "订阅默认主题失败");
            throw;
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public override void Dispose()
    {
        _clientManager?.Dispose();
        _deviceEventService?.Dispose();
        base.Dispose();
    }
}

/// <summary>
/// MQTT监控服务
/// </summary>
public class MqttMonitoringService : ISingleton
{
    private readonly ILogger<MqttMonitoringService> _logger;
    private readonly MqttClientManager _clientManager;
    private readonly DeviceEventService _deviceEventService;
    private readonly ConcurrentDictionary<string, MqttMetrics> _metrics;
    private readonly Timer _metricsTimer;

    /// <summary>
    /// 构造函数
    /// </summary>
    public MqttMonitoringService(
        ILogger<MqttMonitoringService> logger,
        MqttClientManager clientManager,
        DeviceEventService deviceEventService)
    {
        _logger = logger;
        _clientManager = clientManager;
        _deviceEventService = deviceEventService;
        _metrics = new ConcurrentDictionary<string, MqttMetrics>();
        
        // 启动指标收集定时器，每分钟收集一次
        _metricsTimer = new Timer(CollectMetrics, null, 
                                TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
    }

    /// <summary>
    /// 获取MQTT连接状态
    /// </summary>
    public MqttConnectionStats GetConnectionStats()
    {
        return _clientManager.GetConnectionStats();
    }

    /// <summary>
    /// 获取设备统计信息
    /// </summary>
    public DeviceStatistics GetDeviceStatistics()
    {
        return _deviceEventService.GetDeviceStatistics();
    }

    /// <summary>
    /// 获取性能指标
    /// </summary>
    public Dictionary<string, MqttMetrics> GetMetrics()
    {
        return new Dictionary<string, MqttMetrics>(_metrics);
    }

    /// <summary>
    /// 收集指标
    /// </summary>
    private void CollectMetrics(object state)
    {
        try
        {
            var timestamp = DateTime.UtcNow;
            var connectionStats = _clientManager.GetConnectionStats();
            var deviceStats = _deviceEventService.GetDeviceStatistics();

            var metrics = new MqttMetrics
            {
                Timestamp = timestamp,
                IsConnected = connectionStats.IsConnected,
                TotalDevices = deviceStats.TotalDevices,
                OnlineDevices = deviceStats.OnlineDevices,
                OfflineDevices = deviceStats.OfflineDevices,
                MessagesReceived = 0, // 暂时设为0，需要在MqttConnectionStats中添加此属性
                MessagesSent = 0, // 暂时设为0，需要在MqttConnectionStats中添加此属性
                ConnectionUptime = connectionStats.LastHeartbeat != default(DateTime) 
                    ? timestamp - connectionStats.LastHeartbeat 
                    : TimeSpan.Zero
            };

            var key = timestamp.ToString("yyyy-MM-dd HH:mm");
            _metrics.AddOrUpdate(key, metrics, (k, v) => metrics);

            // 保留最近24小时的指标数据
            var cutoffTime = timestamp.AddHours(-24);
            var keysToRemove = _metrics.Keys
                .Where(k => DateTime.TryParseExact(k, "yyyy-MM-dd HH:mm", null, 
                    DateTimeStyles.None, out var dt) && dt < cutoffTime)
                .ToList();

            foreach (var key2 in keysToRemove)
            {
                _metrics.TryRemove(key2, out _);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "收集MQTT指标失败");
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        _metricsTimer?.Dispose();
    }
}

/// <summary>
/// MQTT日志服务
/// </summary>
public class MqttLogService : ISingleton
{
    private readonly ILogger<MqttLogService> _logger;
    private readonly Queue<MqttLogEntry> _logEntries;
    private readonly object _lockObject = new object();
    private const int MaxLogEntries = 1000;

    /// <summary>
    /// 构造函数
    /// </summary>
    public MqttLogService(ILogger<MqttLogService> logger)
    {
        _logger = logger;
        _logEntries = new Queue<MqttLogEntry>();
    }

    /// <summary>
    /// 记录MQTT日志
    /// </summary>
    public void LogMqttEvent(string eventType, string message, object data = null)
    {
        try
        {
            var logEntry = new MqttLogEntry
            {
                Timestamp = DateTime.UtcNow,
                EventType = eventType,
                Message = message,
                Data = data
            };

            lock (_lockObject)
            {
                _logEntries.Enqueue(logEntry);
                
                // 保持日志条目数量在限制范围内
                while (_logEntries.Count > MaxLogEntries)
                {
                    _logEntries.Dequeue();
                }
            }

            _logger.LogInformation("MQTT事件: {EventType} - {Message}", eventType, message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录MQTT日志失败");
        }
    }

    /// <summary>
    /// 获取日志条目
    /// </summary>
    public List<MqttLogEntry> GetLogEntries(int count = 100)
    {
        lock (_lockObject)
        {
            return _logEntries.TakeLast(count).ToList();
        }
    }

    /// <summary>
    /// 清空日志
    /// </summary>
    public void ClearLogs()
    {
        lock (_lockObject)
        {
            _logEntries.Clear();
        }
        _logger.LogInformation("MQTT日志已清空");
    }
}

#region 数据模型

/// <summary>
/// MQTT指标
/// </summary>
public class MqttMetrics
{
    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// 是否连接
    /// </summary>
    public bool IsConnected { get; set; }

    /// <summary>
    /// 总设备数
    /// </summary>
    public int TotalDevices { get; set; }

    /// <summary>
    /// 在线设备数
    /// </summary>
    public int OnlineDevices { get; set; }

    /// <summary>
    /// 离线设备数
    /// </summary>
    public int OfflineDevices { get; set; }

    /// <summary>
    /// 接收消息数
    /// </summary>
    public long MessagesReceived { get; set; }

    /// <summary>
    /// 发送消息数
    /// </summary>
    public long MessagesSent { get; set; }

    /// <summary>
    /// 连接运行时间
    /// </summary>
    public TimeSpan ConnectionUptime { get; set; }
}

/// <summary>
/// MQTT日志条目
/// </summary>
public class MqttLogEntry
{
    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// 事件类型
    /// </summary>
    public string EventType { get; set; }

    /// <summary>
    /// 消息
    /// </summary>
    public string Message { get; set; }

    /// <summary>
    /// 数据
    /// </summary>
    public object Data { get; set; }
}

#endregion