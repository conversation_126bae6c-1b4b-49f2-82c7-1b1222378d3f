// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace Admin.NET.Plugin.MQTT.Services;

/// <summary>
/// 性能监控器实现
/// 提供实时的性能监控和报告功能，包括CPU、内存、网络等指标
/// </summary>
public class PerformanceMonitor : IPerformanceMonitor, IDisposable
{
    #region 私有字段
    
    private readonly ILogger<PerformanceMonitor> _logger;
    private readonly PerformanceConfiguration _configuration;
    
    // 性能计数器
    private readonly PerformanceCounter _cpuCounter;
    private readonly PerformanceCounter _memoryCounter;
    private readonly Process _currentProcess;
    
    // 性能指标历史数据
    private readonly ConcurrentQueue<PerformanceSnapshot> _performanceHistory;
    private readonly ConcurrentDictionary<string, MetricTracker> _customMetrics;
    
    // 统计信息
    private readonly PerformanceStatistics _statistics;
    
    // 监控定时器
    private readonly Timer _monitoringTimer;
    private readonly Timer _reportTimer;
    private readonly Timer _cleanupTimer;
    
    // 警告事件
    public event EventHandler<PerformanceWarningEventArgs> PerformanceWarning;
    public event EventHandler<PerformanceReportEventArgs> PerformanceReport;
    
    private volatile bool _disposed;
    
    #endregion
    
    #region 构造函数
    
    /// <summary>
    /// 构造函数 - 初始化性能监控器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="configuration">性能配置</param>
    public PerformanceMonitor(ILogger<PerformanceMonitor> logger, PerformanceConfiguration configuration)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        
        // 初始化性能计数器
        _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
        _memoryCounter = new PerformanceCounter("Memory", "Available MBytes");
        _currentProcess = Process.GetCurrentProcess();
        
        // 初始化数据结构
        _performanceHistory = new ConcurrentQueue<PerformanceSnapshot>();
        _customMetrics = new ConcurrentDictionary<string, MetricTracker>();
        _statistics = new PerformanceStatistics();
        
        // 启动监控定时器
        var monitoringInterval = TimeSpan.FromSeconds(5); // 每5秒监控一次
        _monitoringTimer = new Timer(MonitoringCallback, null, TimeSpan.Zero, monitoringInterval);
        
        // 启动报告定时器
        var reportInterval = TimeSpan.FromMinutes(_configuration.PerformanceReportInterval);
        _reportTimer = new Timer(ReportCallback, null, reportInterval, reportInterval);
        
        // 启动清理定时器
        var cleanupInterval = TimeSpan.FromHours(1); // 每小时清理一次
        _cleanupTimer = new Timer(CleanupCallback, null, cleanupInterval, cleanupInterval);
        
        _logger.LogInformation("性能监控器已初始化，监控间隔: 5秒，报告间隔: {ReportInterval}分钟", 
            _configuration.PerformanceReportInterval);
    }
    
    #endregion
    
    #region 公共方法
    
    /// <summary>
    /// 记录自定义指标
    /// </summary>
    /// <param name="metricName">指标名称</param>
    /// <param name="value">指标值</param>
    /// <param name="unit">单位</param>
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public void RecordMetric(string metricName, double value, string unit = "")
    {
        if (_disposed || string.IsNullOrEmpty(metricName))
            return;
        
        var tracker = _customMetrics.GetOrAdd(metricName, _ => new MetricTracker(metricName, unit));
        tracker.RecordValue(value);
        
        _logger.LogTrace("记录自定义指标: {MetricName} = {Value} {Unit}", metricName, value, unit);
    }
    
    /// <summary>
    /// 开始性能计时
    /// </summary>
    /// <param name="operationName">操作名称</param>
    /// <returns>性能计时器</returns>
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public IDisposable StartTiming(string operationName)
    {
        if (_disposed || string.IsNullOrEmpty(operationName))
            return new NullDisposable();
        
        return new PerformanceTimer(this, operationName);
    }
    
    /// <summary>
    /// 记录操作耗时
    /// </summary>
    /// <param name="operationName">操作名称</param>
    /// <param name="duration">耗时</param>
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public void RecordTiming(string operationName, TimeSpan duration)
    {
        if (_disposed || string.IsNullOrEmpty(operationName))
            return;
        
        RecordMetric($"{operationName}_duration_ms", duration.TotalMilliseconds, "ms");
        
        // 检查是否超过阈值
        if (duration.TotalMilliseconds > _configuration.TaskTimeoutMs * 0.8) // 80%阈值
        {
            OnPerformanceWarning(new PerformanceWarningEventArgs
            {
                WarningType = PerformanceWarningType.SlowOperation,
                Message = $"操作 '{operationName}' 耗时过长: {duration.TotalMilliseconds:F2}ms",
                Timestamp = DateTime.UtcNow,
                MetricName = operationName,
                CurrentValue = duration.TotalMilliseconds,
                ThresholdValue = _configuration.TaskTimeoutMs * 0.8
            });
        }
    }
    
    /// <summary>
    /// 增加计数器
    /// </summary>
    /// <param name="counterName">计数器名称</param>
    /// <param name="increment">增量</param>
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public void IncrementCounter(string counterName, long increment = 1)
    {
        if (_disposed || string.IsNullOrEmpty(counterName))
            return;
        
        var tracker = _customMetrics.GetOrAdd(counterName, _ => new MetricTracker(counterName, "count"));
        tracker.IncrementCounter(increment);
        
        _logger.LogTrace("增加计数器: {CounterName} += {Increment}", counterName, increment);
    }
    
    /// <summary>
    /// 获取当前性能快照
    /// </summary>
    /// <returns>性能快照</returns>
    public PerformanceSnapshot GetCurrentSnapshot()
    {
        if (_disposed)
            return new PerformanceSnapshot();
        
        try
        {
            var snapshot = new PerformanceSnapshot
            {
                Timestamp = DateTime.UtcNow,
                CpuUsagePercent = _cpuCounter.NextValue(),
                AvailableMemoryMB = _memoryCounter.NextValue(),
                ProcessMemoryMB = _currentProcess.WorkingSet64 / (1024.0 * 1024.0),
                ProcessCpuTime = _currentProcess.TotalProcessorTime,
                ThreadCount = _currentProcess.Threads.Count,
                HandleCount = _currentProcess.HandleCount
            };
            
            // 计算内存使用率
            var totalMemoryMB = GC.GetTotalMemory(false) / (1024.0 * 1024.0);
            snapshot.MemoryUsagePercent = (totalMemoryMB / (snapshot.AvailableMemoryMB + totalMemoryMB)) * 100;
            
            return snapshot;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取性能快照失败");
            return new PerformanceSnapshot { Timestamp = DateTime.UtcNow };
        }
    }
    
    /// <summary>
    /// 获取性能统计信息
    /// </summary>
    /// <returns>性能统计信息</returns>
    public PerformanceStatistics GetStatistics()
    {
        var currentSnapshot = GetCurrentSnapshot();
        
        return new PerformanceStatistics
        {
            CurrentCpuUsage = currentSnapshot.CpuUsagePercent,
            CurrentMemoryUsage = currentSnapshot.MemoryUsagePercent,
            CurrentProcessMemoryMB = currentSnapshot.ProcessMemoryMB,
            CurrentThreadCount = currentSnapshot.ThreadCount,
            CurrentHandleCount = currentSnapshot.HandleCount,
            AverageCpuUsage = _statistics.AverageCpuUsage,
            AverageMemoryUsage = _statistics.AverageMemoryUsage,
            PeakCpuUsage = _statistics.PeakCpuUsage,
            PeakMemoryUsage = _statistics.PeakMemoryUsage,
            TotalWarnings = _statistics.TotalWarnings,
            TotalReports = _statistics.TotalReports,
            UptimeHours = (DateTime.UtcNow - Process.GetCurrentProcess().StartTime.ToUniversalTime()).TotalHours
        };
    }
    
    /// <summary>
    /// 获取性能历史数据
    /// </summary>
    /// <param name="duration">时间范围</param>
    /// <returns>性能快照列表</returns>
    public List<PerformanceSnapshot> GetPerformanceHistory(TimeSpan duration)
    {
        var cutoffTime = DateTime.UtcNow - duration;
        
        return _performanceHistory
            .Where(snapshot => snapshot.Timestamp >= cutoffTime)
            .OrderBy(snapshot => snapshot.Timestamp)
            .ToList();
    }
    
    /// <summary>
    /// 获取自定义指标
    /// </summary>
    /// <param name="metricName">指标名称</param>
    /// <returns>指标跟踪器</returns>
    public MetricTracker GetCustomMetric(string metricName)
    {
        if (string.IsNullOrEmpty(metricName))
            return null;
        
        _customMetrics.TryGetValue(metricName, out var tracker);
        return tracker;
    }
    
    /// <summary>
    /// 获取所有自定义指标
    /// </summary>
    /// <returns>指标字典</returns>
    public Dictionary<string, MetricTracker> GetAllCustomMetrics()
    {
        return new Dictionary<string, MetricTracker>(_customMetrics);
    }
    
    /// <summary>
    /// 生成性能报告
    /// </summary>
    /// <returns>性能报告</returns>
    public PerformanceReport GenerateReport()
    {
        var statistics = GetStatistics();
        var recentHistory = GetPerformanceHistory(TimeSpan.FromHours(1));
        
        var report = new PerformanceReport
        {
            GeneratedAt = DateTime.UtcNow,
            Statistics = statistics,
            RecentSnapshots = recentHistory,
            CustomMetrics = GetAllCustomMetrics().ToDictionary(kvp => kvp.Key, kvp => kvp.Value.GetSummary())
        };
        
        // 添加性能建议
        report.Recommendations = GenerateRecommendations(statistics, recentHistory);
        
        return report;
    }
    
    /// <summary>
    /// 重置统计信息
    /// </summary>
    public void ResetStatistics()
    {
        _statistics.Reset();
        
        foreach (var metric in _customMetrics.Values)
        {
            metric.Reset();
        }
        
        _logger.LogInformation("性能统计信息已重置");
    }
    
    #endregion
    
    #region 私有方法
    
    /// <summary>
    /// 监控回调
    /// </summary>
    /// <param name="state">状态</param>
    private void MonitoringCallback(object state)
    {
        if (_disposed)
            return;
        
        try
        {
            var snapshot = GetCurrentSnapshot();
            
            // 添加到历史记录
            _performanceHistory.Enqueue(snapshot);
            
            // 更新统计信息
            UpdateStatistics(snapshot);
            
            // 检查性能警告
            CheckPerformanceWarnings(snapshot);
            
            _logger.LogTrace("性能监控快照: CPU={CpuUsage:F1}%, 内存={MemoryUsage:F1}%, " +
                "进程内存={ProcessMemory:F1}MB, 线程数={ThreadCount}", 
                snapshot.CpuUsagePercent, snapshot.MemoryUsagePercent, 
                snapshot.ProcessMemoryMB, snapshot.ThreadCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "性能监控回调失败");
        }
    }
    
    /// <summary>
    /// 报告回调
    /// </summary>
    /// <param name="state">状态</param>
    private void ReportCallback(object state)
    {
        if (_disposed)
            return;
        
        try
        {
            var report = GenerateReport();
            
            Interlocked.Increment(ref _statistics._totalReports);
            
            OnPerformanceReport(new PerformanceReportEventArgs
            {
                Report = report,
                Timestamp = DateTime.UtcNow
            });
            
            _logger.LogInformation("性能报告已生成: CPU平均={AvgCpu:F1}%, 内存平均={AvgMemory:F1}%, " +
                "运行时间={Uptime:F1}小时, 警告数={Warnings}", 
                report.Statistics.AverageCpuUsage, report.Statistics.AverageMemoryUsage, 
                report.Statistics.UptimeHours, report.Statistics.TotalWarnings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "性能报告回调失败");
        }
    }
    
    /// <summary>
    /// 清理回调
    /// </summary>
    /// <param name="state">状态</param>
    private void CleanupCallback(object state)
    {
        if (_disposed)
            return;
        
        try
        {
            var retentionTime = TimeSpan.FromHours(_configuration.PerformanceMetricsRetentionHours);
            var cutoffTime = DateTime.UtcNow - retentionTime;
            
            // 清理过期的性能历史数据
            var removedCount = 0;
            while (_performanceHistory.TryPeek(out var snapshot) && snapshot.Timestamp < cutoffTime)
            {
                if (_performanceHistory.TryDequeue(out _))
                {
                    removedCount++;
                }
            }
            
            // 清理过期的自定义指标数据
            foreach (var metric in _customMetrics.Values)
            {
                metric.CleanupOldData(retentionTime);
            }
            
            _logger.LogDebug("性能数据清理完成，移除了 {RemovedCount} 个过期快照", removedCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "性能数据清理失败");
        }
    }
    
    /// <summary>
    /// 更新统计信息
    /// </summary>
    /// <param name="snapshot">性能快照</param>
    private void UpdateStatistics(PerformanceSnapshot snapshot)
    {
        // 更新平均值
        var sampleCount = Interlocked.Increment(ref _statistics._sampleCount);
        
        var currentAvgCpu = _statistics.AverageCpuUsage;
        var newAvgCpu = (currentAvgCpu * (sampleCount - 1) + snapshot.CpuUsagePercent) / sampleCount;
        Interlocked.Exchange(ref _statistics._averageCpuUsage, (long)(newAvgCpu * 100));
        
        var currentAvgMemory = _statistics.AverageMemoryUsage;
        var newAvgMemory = (currentAvgMemory * (sampleCount - 1) + snapshot.MemoryUsagePercent) / sampleCount;
        Interlocked.Exchange(ref _statistics._averageMemoryUsage, (long)(newAvgMemory * 100));
        
        // 更新峰值
        var currentPeakCpu = _statistics.PeakCpuUsage;
        if (snapshot.CpuUsagePercent > currentPeakCpu)
        {
            Interlocked.Exchange(ref _statistics._peakCpuUsage, (long)(snapshot.CpuUsagePercent * 100));
        }
        
        var currentPeakMemory = _statistics.PeakMemoryUsage;
        if (snapshot.MemoryUsagePercent > currentPeakMemory)
        {
            Interlocked.Exchange(ref _statistics._peakMemoryUsage, (long)(snapshot.MemoryUsagePercent * 100));
        }
    }
    
    /// <summary>
    /// 检查性能警告
    /// </summary>
    /// <param name="snapshot">性能快照</param>
    private void CheckPerformanceWarnings(PerformanceSnapshot snapshot)
    {
        // 检查CPU使用率
        if (snapshot.CpuUsagePercent > 80)
        {
            OnPerformanceWarning(new PerformanceWarningEventArgs
            {
                WarningType = PerformanceWarningType.HighCpuUsage,
                Message = $"CPU使用率过高: {snapshot.CpuUsagePercent:F1}%",
                Timestamp = snapshot.Timestamp,
                MetricName = "CPU使用率",
                CurrentValue = snapshot.CpuUsagePercent,
                ThresholdValue = 80
            });
        }
        
        // 检查内存使用率
        if (snapshot.MemoryUsagePercent > 85)
        {
            OnPerformanceWarning(new PerformanceWarningEventArgs
            {
                WarningType = PerformanceWarningType.HighMemoryUsage,
                Message = $"内存使用率过高: {snapshot.MemoryUsagePercent:F1}%",
                Timestamp = snapshot.Timestamp,
                MetricName = "内存使用率",
                CurrentValue = snapshot.MemoryUsagePercent,
                ThresholdValue = 85
            });
        }
        
        // 检查进程内存
        if (snapshot.ProcessMemoryMB > _configuration.MemoryThreshold)
        {
            OnPerformanceWarning(new PerformanceWarningEventArgs
            {
                WarningType = PerformanceWarningType.HighMemoryUsage,
                Message = $"进程内存使用过高: {snapshot.ProcessMemoryMB:F1}MB",
                Timestamp = snapshot.Timestamp,
                MetricName = "进程内存",
                CurrentValue = snapshot.ProcessMemoryMB,
                ThresholdValue = _configuration.MemoryThreshold
            });
        }
        
        // 检查线程数
        if (snapshot.ThreadCount > _configuration.MaxConcurrentTasks * 2)
        {
            OnPerformanceWarning(new PerformanceWarningEventArgs
            {
                WarningType = PerformanceWarningType.HighThreadCount,
                Message = $"线程数过多: {snapshot.ThreadCount}",
                Timestamp = snapshot.Timestamp,
                MetricName = "线程数",
                CurrentValue = snapshot.ThreadCount,
                ThresholdValue = _configuration.MaxConcurrentTasks * 2
            });
        }
    }
    
    /// <summary>
    /// 生成性能建议
    /// </summary>
    /// <param name="statistics">统计信息</param>
    /// <param name="recentHistory">最近历史数据</param>
    /// <returns>建议列表</returns>
    private List<string> GenerateRecommendations(PerformanceStatistics statistics, List<PerformanceSnapshot> recentHistory)
    {
        var recommendations = new List<string>();
        
        // CPU相关建议
        if (statistics.AverageCpuUsage > 70)
        {
            recommendations.Add("CPU使用率较高，建议优化计算密集型操作或增加并发控制");
        }
        
        // 内存相关建议
        if (statistics.AverageMemoryUsage > 80)
        {
            recommendations.Add("内存使用率较高，建议启用内存池优化或增加GC频率");
        }
        
        // 线程相关建议
        if (statistics.CurrentThreadCount > _configuration.MaxConcurrentTasks)
        {
            recommendations.Add("线程数过多，建议优化异步操作或调整并发限制");
        }
        
        // 趋势分析建议
        if (recentHistory.Count > 10)
        {
            var cpuTrend = CalculateTrend(recentHistory.Select(s => s.CpuUsagePercent).ToList());
            if (cpuTrend > 0.1) // 上升趋势
            {
                recommendations.Add("CPU使用率呈上升趋势，建议监控并优化性能瓶颈");
            }
            
            var memoryTrend = CalculateTrend(recentHistory.Select(s => s.MemoryUsagePercent).ToList());
            if (memoryTrend > 0.1) // 上升趋势
            {
                recommendations.Add("内存使用率呈上升趋势，可能存在内存泄漏，建议检查");
            }
        }
        
        return recommendations;
    }
    
    /// <summary>
    /// 计算趋势
    /// </summary>
    /// <param name="values">数值列表</param>
    /// <returns>趋势值</returns>
    private double CalculateTrend(List<double> values)
    {
        if (values.Count < 2)
            return 0;
        
        var n = values.Count;
        var sumX = n * (n - 1) / 2.0;
        var sumY = values.Sum();
        var sumXY = values.Select((y, x) => x * y).Sum();
        var sumX2 = n * (n - 1) * (2 * n - 1) / 6.0;
        
        return (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    }
    
    /// <summary>
    /// 触发性能警告事件
    /// </summary>
    /// <param name="args">警告事件参数</param>
    private void OnPerformanceWarning(PerformanceWarningEventArgs args)
    {
        Interlocked.Increment(ref _statistics._totalWarnings);
        
        PerformanceWarning?.Invoke(this, args);
        
        _logger.LogWarning("性能警告: {Message}", args.Message);
    }
    
    /// <summary>
    /// 触发性能报告事件
    /// </summary>
    /// <param name="args">报告事件参数</param>
    private void OnPerformanceReport(PerformanceReportEventArgs args)
    {
        PerformanceReport?.Invoke(this, args);
    }
    
    #endregion
    
    #region IDisposable实现
    
    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;
            
            // 停止定时器
            _monitoringTimer?.Dispose();
            _reportTimer?.Dispose();
            _cleanupTimer?.Dispose();
            
            // 释放性能计数器
            _cpuCounter?.Dispose();
            _memoryCounter?.Dispose();
            _currentProcess?.Dispose();
            
            // 清理数据
            while (_performanceHistory.TryDequeue(out _)) { }
            _customMetrics.Clear();
            
            var finalStats = GetStatistics();
            _logger.LogInformation("性能监控器已释放资源，运行时间: {Uptime:F1}小时，" +
                "总警告: {Warnings}，总报告: {Reports}", 
                finalStats.UptimeHours, finalStats.TotalWarnings, finalStats.TotalReports);
        }
    }
    
    #endregion
}

/// <summary>
/// 空的Disposable实现
/// 用于在监控器已释放时返回
/// </summary>
internal class NullDisposable : IDisposable
{
    public void Dispose() { }
}

/// <summary>
/// 性能计时器
/// 用于测量操作耗时
/// </summary>
internal class PerformanceTimer : IDisposable
{
    private readonly PerformanceMonitor _monitor;
    private readonly string _operationName;
    private readonly Stopwatch _stopwatch;
    private bool _disposed;
    
    public PerformanceTimer(PerformanceMonitor monitor, string operationName)
    {
        _monitor = monitor;
        _operationName = operationName;
        _stopwatch = Stopwatch.StartNew();
    }
    
    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;
            _stopwatch.Stop();
            _monitor.RecordTiming(_operationName, _stopwatch.Elapsed);
        }
    }
}

/// <summary>
/// 指标跟踪器
/// 用于跟踪自定义指标的统计信息
/// </summary>
public class MetricTracker
{
    private readonly string _name;
    private readonly string _unit;
    private readonly ConcurrentQueue<MetricValue> _values;
    private long _totalCount;
    private double _sum;
    private double _min = double.MaxValue;
    private double _max = double.MinValue;
    
    public MetricTracker(string name, string unit)
    {
        _name = name;
        _unit = unit;
        _values = new ConcurrentQueue<MetricValue>();
    }
    
    public string Name => _name;
    public string Unit => _unit;
    public long Count => Interlocked.Read(ref _totalCount);
    public double Average => Count > 0 ? _sum / Count : 0;
    public double Min => _min == double.MaxValue ? 0 : _min;
    public double Max => _max == double.MinValue ? 0 : _max;
    
    public void RecordValue(double value)
    {
        _values.Enqueue(new MetricValue { Value = value, Timestamp = DateTime.UtcNow });
        
        Interlocked.Increment(ref _totalCount);
        _sum += value;
        
        if (value < _min) _min = value;
        if (value > _max) _max = value;
    }
    
    public void IncrementCounter(long increment = 1)
    {
        RecordValue(increment);
    }
    
    public void Reset()
    {
        while (_values.TryDequeue(out _)) { }
        Interlocked.Exchange(ref _totalCount, 0);
        _sum = 0;
        _min = double.MaxValue;
        _max = double.MinValue;
    }
    
    public void CleanupOldData(TimeSpan retention)
    {
        var cutoffTime = DateTime.UtcNow - retention;
        
        while (_values.TryPeek(out var value) && value.Timestamp < cutoffTime)
        {
            _values.TryDequeue(out _);
        }
    }
    
    public MetricSummary GetSummary()
    {
        return new MetricSummary
        {
            Name = _name,
            Unit = _unit,
            Count = Count,
            Average = Average,
            Min = Min,
            Max = Max,
            Sum = _sum
        };
    }
}

/// <summary>
/// 指标值
/// 表示一个指标的值和时间戳
/// </summary>
internal struct MetricValue
{
    public double Value { get; set; }
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// 指标摘要
/// 包含指标的统计信息
/// </summary>
public class MetricSummary
{
    public string Name { get; set; } = string.Empty;
    public string Unit { get; set; } = string.Empty;
    public long Count { get; set; }
    public double Average { get; set; }
    public double Min { get; set; }
    public double Max { get; set; }
    public double Sum { get; set; }
}

/// <summary>
/// 性能快照
/// 表示某个时间点的系统性能状态
/// </summary>
public class PerformanceSnapshot
{
    public DateTime Timestamp { get; set; }
    public double CpuUsagePercent { get; set; }
    public double MemoryUsagePercent { get; set; }
    public double AvailableMemoryMB { get; set; }
    public double ProcessMemoryMB { get; set; }
    public TimeSpan ProcessCpuTime { get; set; }
    public int ThreadCount { get; set; }
    public int HandleCount { get; set; }
}

/// <summary>
/// 性能统计信息
/// 包含系统性能的统计数据
/// </summary>
public class PerformanceStatistics
{
    internal long _sampleCount;
    internal long _averageCpuUsage; // 乘以100存储
    internal long _averageMemoryUsage; // 乘以100存储
    internal long _peakCpuUsage; // 乘以100存储
    internal long _peakMemoryUsage; // 乘以100存储
    internal long _totalWarnings;
    internal long _totalReports;
    
    public double CurrentCpuUsage { get; set; }
    public double CurrentMemoryUsage { get; set; }
    public double CurrentProcessMemoryMB { get; set; }
    public int CurrentThreadCount { get; set; }
    public int CurrentHandleCount { get; set; }
    
    public double AverageCpuUsage => Interlocked.Read(ref _averageCpuUsage) / 100.0;
    public double AverageMemoryUsage => Interlocked.Read(ref _averageMemoryUsage) / 100.0;
    public double PeakCpuUsage => Interlocked.Read(ref _peakCpuUsage) / 100.0;
    public double PeakMemoryUsage => Interlocked.Read(ref _peakMemoryUsage) / 100.0;
    public long TotalWarnings => Interlocked.Read(ref _totalWarnings);
    public long TotalReports => Interlocked.Read(ref _totalReports);
    
    public double UptimeHours { get; set; }
    
    public void Reset()
    {
        Interlocked.Exchange(ref _sampleCount, 0);
        Interlocked.Exchange(ref _averageCpuUsage, 0);
        Interlocked.Exchange(ref _averageMemoryUsage, 0);
        Interlocked.Exchange(ref _peakCpuUsage, 0);
        Interlocked.Exchange(ref _peakMemoryUsage, 0);
        Interlocked.Exchange(ref _totalWarnings, 0);
        Interlocked.Exchange(ref _totalReports, 0);
    }
}

/// <summary>
/// 性能报告
/// 包含完整的性能分析报告
/// </summary>
public class PerformanceReport
{
    public DateTime GeneratedAt { get; set; }
    public PerformanceStatistics Statistics { get; set; } = new();
    public List<PerformanceSnapshot> RecentSnapshots { get; set; } = new();
    public Dictionary<string, MetricSummary> CustomMetrics { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}

/// <summary>
/// 性能警告类型枚举
/// 定义各种性能警告的类型
/// </summary>
public enum PerformanceWarningType
{
    HighCpuUsage,
    HighMemoryUsage,
    HighThreadCount,
    SlowOperation,
    QueueOverflow,
    Other
}

/// <summary>
/// 性能警告事件参数
/// 包含性能警告的详细信息
/// </summary>
public class PerformanceWarningEventArgs : EventArgs
{
    public PerformanceWarningType WarningType { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string MetricName { get; set; } = string.Empty;
    public double CurrentValue { get; set; }
    public double ThresholdValue { get; set; }
}

/// <summary>
/// 性能报告事件参数
/// 包含性能报告的信息
/// </summary>
public class PerformanceReportEventArgs : EventArgs
{
    public PerformanceReport Report { get; set; } = new();
    public DateTime Timestamp { get; set; }
}