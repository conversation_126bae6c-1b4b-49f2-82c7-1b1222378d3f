/**
 * 认证相关API
 */

import request from '../utils/request.js'
import { createCachedApi } from '../src/utils/apiCache.js'

const BASE_URL = '/api/sysAuth'



/**
 * 用户登录
 * @param {Object} loginForm 登录表单数据
 * @param {string} loginForm.account 用户账号
 * @param {string} loginForm.password 密码
 * @param {string} loginForm.captcha 验证码
 * @returns {Promise} 登录结果
 */
export const login = (loginForm) => {
  return request.post(`${BASE_URL}/login`, loginForm, {
    cache: false,
    offline: false
  })
}

/**
 * 用户登出
 * @returns {Promise} 登出结果
 */
export const logout = () => {
  return request.post(`${BASE_URL}/logout`, {}, {
    cache: false,
    offline: false
  })
}

/**
 * 获取验证码
 * @returns {Promise} 验证码图片base64
 */
export const getCaptcha = () => {
  return request.get(`${BASE_URL}/captcha`, {
    cache: false,
    offline: false
  })
}

/**
 * 刷新Token
 * @param {string} refreshToken 刷新令牌
 * @returns {Promise} 新的访问令牌
 */
export const refreshToken = (refreshToken) => {
  return request.post(`${BASE_URL}/refresh`, { refreshToken }, {
    cache: false,
    offline: false
  })
}

/**
 * 获取当前用户信息
 * @returns {Promise} 用户信息
 */
export const getUserInfo = () => {
  return request.get(`${BASE_URL}/userinfo`, {
    cache: true,
    cacheTime: 10 * 60 * 1000, // 10分钟缓存
    offline: true
  })
}

/**
 * 获取用户权限菜单
 * @returns {Promise} 用户菜单权限列表
 */
export const getUserMenus = () => {
  return request.get(`${BASE_URL}/loginMenu`, {
    cache: true,
    cacheTime: 5 * 60 * 1000,
    offline: true
  })
}

/**
 * 获取用户权限按钮
 * @returns {Promise} 用户按钮权限列表
 */
export const getUserButtons = () => {
  return request.get(`${BASE_URL}/loginButton`, {
    cache: true,
    cacheTime: 5 * 60 * 1000,
    offline: true
  })
}

/**
 * 修改密码
 * @param {Object} passwordForm 密码修改表单
 * @param {string} passwordForm.oldPassword 原密码
 * @param {string} passwordForm.newPassword 新密码
 * @param {string} passwordForm.confirmPassword 确认密码
 * @returns {Promise} 修改结果
 */
export const changePassword = (passwordForm) => {
  return request.put(`${BASE_URL}/password`, passwordForm, {
    cache: false,
    offline: false
  })
}

/**
 * 修改用户信息
 * @param {Object} userInfo 用户信息
 * @param {string} userInfo.realName 真实姓名
 * @param {string} userInfo.phone 手机号
 * @param {string} userInfo.email 邮箱
 * @param {string} userInfo.avatar 头像
 * @returns {Promise} 修改结果
 */
export const updateUserInfo = (userInfo) => {
  return request.put(`${BASE_URL}/userinfo`, userInfo, {
    cache: false,
    offline: false
  })
}

/**
 * 用户注册
 * @param {Object} registerForm 注册表单数据
 * @param {string} registerForm.account 用户账号
 * @param {string} registerForm.realName 真实姓名
 * @param {string} registerForm.phone 手机号
 * @param {string} registerForm.email 邮箱
 * @param {string} registerForm.password 密码
 * @param {string} registerForm.smsCode 短信验证码
 * @returns {Promise} 注册结果
 */
export const register = (registerForm) => {
  return request.post(`${BASE_URL}/register`, registerForm, {
    cache: false,
    offline: false
  })
}

/**
 * 发送短信验证码
 * @param {Object} smsForm 短信表单
 * @param {string} smsForm.phone 手机号
 * @param {string} smsForm.type 验证码类型 (register/forgot/bind)
 * @returns {Promise} 发送结果
 */
export const sendSmsCode = (smsForm) => {
  return request.post(`${BASE_URL}/sms/send`, smsForm, {
    cache: false,
    offline: false
  })
}

/**
 * 发送邮箱验证码
 * @param {Object} emailForm 邮箱表单
 * @param {string} emailForm.email 邮箱地址
 * @param {string} emailForm.type 验证码类型 (register/forgot/bind)
 * @returns {Promise} 发送结果
 */
export const sendEmailCode = (emailForm) => {
  return request.post(`${BASE_URL}/email/send`, emailForm, {
    cache: false,
    offline: false
  })
}

/**
 * 验证重置密码验证码
 * @param {Object} verifyForm 验证表单
 * @param {string} verifyForm.phone 手机号 (手机验证时)
 * @param {string} verifyForm.email 邮箱 (邮箱验证时)
 * @param {string} verifyForm.smsCode 短信验证码 (手机验证时)
 * @param {string} verifyForm.emailCode 邮箱验证码 (邮箱验证时)
 * @param {string} verifyForm.type 验证类型 (phone/email)
 * @returns {Promise} 验证结果，包含重置token
 */
export const verifyResetCode = (verifyForm) => {
  return request.post(`${BASE_URL}/reset/verify`, verifyForm, {
    cache: false,
    offline: false
  })
}

/**
 * 重置密码
 * @param {Object} resetForm 重置密码表单
 * @param {string} resetForm.token 重置token
 * @param {string} resetForm.password 新密码
 * @returns {Promise} 重置结果
 */
export const resetPassword = (resetForm) => {
  return request.post(`${BASE_URL}/reset/password`, resetForm, {
    cache: false,
    offline: false
  })
}

/**
 * 忘记密码 - 发送验证码 (兼容旧版本)
 * @param {string} phone 手机号
 * @returns {Promise} 发送结果
 */
export const sendResetCode = (phone) => {
  return sendSmsCode({ phone, type: 'forgot' })
}

/**
 * 检查Token有效性
 * @returns {Promise} Token验证结果
 */
export const checkToken = () => {
  return request.get(`${BASE_URL}/check`, {
    cache: false,
    offline: false
  })
}

/**
 * 获取用户角色信息
 * @returns {Promise} 用户角色列表
 */
export const getUserRoles = () => {
  return request.get(`${BASE_URL}/loginRole`, {
    cache: true,
    cacheTime: 5 * 60 * 1000,
    offline: true
  })
}

/**
 * 获取用户组织信息
 * @returns {Promise} 用户组织信息
 */
export const getUserOrg = () => {
  return request.get(`${BASE_URL}/loginOrg`, {
    cache: true,
    cacheTime: 10 * 60 * 1000,
    offline: true
  })
}

/**
 * 绑定手机号
 * @param {Object} bindForm 绑定表单
 * @param {string} bindForm.phone 手机号
 * @param {string} bindForm.code 验证码
 * @returns {Promise} 绑定结果
 */
export const bindPhone = (bindForm) => {
  return request.post(`${BASE_URL}/bind/phone`, bindForm, {
    cache: false,
    offline: false
  })
}

/**
 * 绑定邮箱
 * @param {Object} bindForm 绑定表单
 * @param {string} bindForm.email 邮箱
 * @param {string} bindForm.code 验证码
 * @returns {Promise} 绑定结果
 */
export const bindEmail = (bindForm) => {
  return request.post(`${BASE_URL}/bind/email`, bindForm, {
    cache: false,
    offline: false
  })
}

/**
 * 获取用户统计信息
 * @returns {Promise} 用户统计数据
 */
export const getUserStats = () => {
  return request.get(`${BASE_URL}/stats`, {
    cache: true,
    offline: true
  })
}

/**
 * 上传头像
 * @param {string} filePath 头像文件路径
 * @returns {Promise} 上传结果
 */
export const uploadAvatar = (filePath) => {
  // 修复：使用动态配置的baseURL而非硬编码URL
  return request.upload(`${BASE_URL}/avatar`, filePath, {
    name: 'file',
    silent: false,
    loadingText: '上传中...'
  })
}

// 创建缓存版本的API函数
const cachedGetUserInfo = createCachedApi(getUserInfo, 'user_info', 10 * 60 * 1000) // 10分钟缓存
const cachedGetUserMenus = createCachedApi(getUserMenus, 'user_menus', 5 * 60 * 1000) // 5分钟缓存
const cachedGetUserButtons = createCachedApi(getUserButtons, 'user_buttons', 5 * 60 * 1000) // 5分钟缓存
const cachedGetUserRoles = createCachedApi(getUserRoles, 'user_roles', 5 * 60 * 1000) // 5分钟缓存
const cachedGetUserOrg = createCachedApi(getUserOrg, 'user_org', 10 * 60 * 1000) // 10分钟缓存

// 创建authApi对象，包含所有认证相关的API方法
export const authApi = {
  login,
  logout,
  register,
  getCaptcha,
  refreshToken,
  getUserInfo,
  getUserMenus,
  getUserButtons,
  changePassword,
  updateUserInfo,
  sendSmsCode,
  sendEmailCode,
  sendResetCode,
  verifyResetCode,
  resetPassword,
  checkToken,
  getUserRoles,
  getUserOrg,
  getUserStats,
  bindPhone,
  bindEmail,
  uploadAvatar,
  // 缓存版本的API
  cachedGetUserInfo,
  cachedGetUserMenus,
  cachedGetUserButtons,
  cachedGetUserRoles,
  cachedGetUserOrg
}

// 默认导出所有认证相关API
export default {
  // 原始API函数
  login,
  logout,
  register,
  getCaptcha,
  refreshToken,
  getUserInfo,
  getUserMenus,
  getUserButtons,
  changePassword,
  updateUserInfo,
  sendSmsCode,
  sendEmailCode,
  sendResetCode,
  verifyResetCode,
  resetPassword,
  checkToken,
  getUserRoles,
  getUserOrg,
  getUserStats,
  bindPhone,
  bindEmail,
  uploadAvatar,
  // 缓存版本API函数
  cachedGetUserInfo,
  cachedGetUserMenus,
  cachedGetUserButtons,
  cachedGetUserRoles,
  cachedGetUserOrg
}