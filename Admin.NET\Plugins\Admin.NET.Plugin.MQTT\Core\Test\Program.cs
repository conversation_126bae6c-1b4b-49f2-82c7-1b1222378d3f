using Admin.NET.Plugin.MQTT;
using System;

namespace TestConsole
{
    /// <summary>
    /// 测试控制台程序入口
    /// 独立的测试程序，用于验证MQTT凭证生成功能
    /// </summary>
    class Program
    {
        /// <summary>
        /// 程序主入口点
        /// </summary>
        /// <param name="args">命令行参数</param>
        static void Main(string[] args)
        {
            Console.WriteLine("=== MQTT凭证生成功能测试 ===");
            Console.WriteLine();
            
            try
            {
                // 运行完整测试
                var testResult = MqttOptions.RunCompleteTest();
                Console.WriteLine(testResult);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试执行失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
            
            Console.WriteLine();
            Console.WriteLine("=== 测试完成 ===");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}