<template>
  <view class="chart-container" :class="containerClass">
    <!-- 图表标题 -->
    <view class="chart-header" v-if="title || $slots.header">
      <slot name="header">
        <view class="chart-title">
          <text class="title-text">{{ title }}</text>
          <text class="title-subtitle" v-if="subtitle">{{ subtitle }}</text>
        </view>
        <view class="chart-actions" v-if="showActions">
          <view class="action-item" @click="handleRefresh" v-if="showRefresh">
            <text class="action-icon">🔄</text>
          </view>
          <view class="action-item" @click="handleFullscreen" v-if="showFullscreen">
            <text class="action-icon">⛶</text>
          </view>
          <view class="action-item" @click="handleMore" v-if="showMore">
            <text class="action-icon">⋯</text>
          </view>
        </view>
      </slot>
    </view>
    
    <!-- 图表工具栏 -->
    <view class="chart-toolbar" v-if="showToolbar || $slots.toolbar">
      <slot name="toolbar">
        <!-- 时间范围选择 -->
        <view class="toolbar-section" v-if="showTimeRange">
          <view class="time-range-tabs">
            <view 
              class="time-tab"
              :class="{ active: activeTimeRange === item.value }"
              v-for="item in timeRangeOptions"
              :key="item.value"
              @click="handleTimeRangeChange(item.value)"
            >
              {{ item.label }}
            </view>
          </view>
        </view>
        
        <!-- 图表类型切换 -->
        <view class="toolbar-section" v-if="showChartType">
          <view class="chart-type-tabs">
            <view 
              class="type-tab"
              :class="{ active: activeChartType === item.value }"
              v-for="item in chartTypeOptions"
              :key="item.value"
              @click="handleChartTypeChange(item.value)"
            >
              <text class="type-icon">{{ item.icon }}</text>
              <text class="type-label">{{ item.label }}</text>
            </view>
          </view>
        </view>
      </slot>
    </view>
    
    <!-- 图表内容区域 -->
    <view class="chart-content" :style="contentStyle">
      <!-- 加载状态 -->
      <view class="chart-loading" v-if="loading">
        <LoadingSpinner :size="loadingSize" />
        <text class="loading-text">{{ loadingText }}</text>
      </view>
      
      <!-- 空数据状态 -->
      <view class="chart-empty" v-else-if="isEmpty">
        <view class="empty-icon">📊</view>
        <text class="empty-text">{{ emptyText }}</text>
        <button class="empty-action" @click="handleRefresh" v-if="showEmptyAction">
          重新加载
        </button>
      </view>
      
      <!-- 错误状态 -->
      <view class="chart-error" v-else-if="error">
        <view class="error-icon">⚠️</view>
        <text class="error-text">{{ errorText }}</text>
        <button class="error-action" @click="handleRefresh">
          重试
        </button>
      </view>
      
      <!-- 图表组件插槽 -->
      <view class="chart-wrapper" v-else>
        <slot name="chart" :data="chartData" :options="chartOptions">
          <!-- 默认图表实现 -->
          <canvas 
            class="chart-canvas"
            :canvas-id="canvasId"
            :id="canvasId"
            @touchstart="handleTouchStart"
            @touchmove="handleTouchMove"
            @touchend="handleTouchEnd"
          ></canvas>
        </slot>
      </view>
    </view>
    
    <!-- 图表底部信息 -->
    <view class="chart-footer" v-if="showFooter || $slots.footer">
      <slot name="footer">
        <view class="chart-info">
          <view class="info-item" v-if="lastUpdateTime">
            <text class="info-label">更新时间:</text>
            <text class="info-value">{{ formatUpdateTime }}</text>
          </view>
          <view class="info-item" v-if="dataCount !== undefined">
            <text class="info-label">数据量:</text>
            <text class="info-value">{{ dataCount }} 条</text>
          </view>
        </view>
      </slot>
    </view>
  </view>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted, nextTick } from 'vue'
import LoadingSpinner from './LoadingSpinner.vue'
import { formatUtils } from '@/utils'

// Props定义
const props = defineProps({
  // 图表标题
  title: {
    type: String,
    default: ''
  },
  // 图表副标题
  subtitle: {
    type: String,
    default: ''
  },
  // 图表数据
  chartData: {
    type: [Array, Object],
    default: () => []
  },
  // 图表配置
  chartOptions: {
    type: Object,
    default: () => ({})
  },
  // 图表类型
  chartType: {
    type: String,
    default: 'line'
  },
  // 容器高度
  height: {
    type: [String, Number],
    default: '400rpx'
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  },
  // 加载文本
  loadingText: {
    type: String,
    default: '加载中...'
  },
  // 加载图标大小
  loadingSize: {
    type: String,
    default: 'medium'
  },
  // 是否为空
  isEmpty: {
    type: Boolean,
    default: false
  },
  // 空数据文本
  emptyText: {
    type: String,
    default: '暂无数据'
  },
  // 是否显示空数据操作按钮
  showEmptyAction: {
    type: Boolean,
    default: true
  },
  // 错误状态
  error: {
    type: [Boolean, String],
    default: false
  },
  // 错误文本
  errorText: {
    type: String,
    default: '数据加载失败'
  },
  // 是否显示操作按钮
  showActions: {
    type: Boolean,
    default: true
  },
  // 是否显示刷新按钮
  showRefresh: {
    type: Boolean,
    default: true
  },
  // 是否显示全屏按钮
  showFullscreen: {
    type: Boolean,
    default: false
  },
  // 是否显示更多按钮
  showMore: {
    type: Boolean,
    default: false
  },
  // 是否显示工具栏
  showToolbar: {
    type: Boolean,
    default: false
  },
  // 是否显示时间范围选择
  showTimeRange: {
    type: Boolean,
    default: false
  },
  // 是否显示图表类型切换
  showChartType: {
    type: Boolean,
    default: false
  },
  // 时间范围选项
  timeRangeOptions: {
    type: Array,
    default: () => [
      { label: '今日', value: 'today' },
      { label: '本周', value: 'week' },
      { label: '本月', value: 'month' },
      { label: '本年', value: 'year' }
    ]
  },
  // 图表类型选项
  chartTypeOptions: {
    type: Array,
    default: () => [
      { label: '折线图', value: 'line', icon: '📈' },
      { label: '柱状图', value: 'bar', icon: '📊' },
      { label: '饼图', value: 'pie', icon: '🥧' }
    ]
  },
  // 默认时间范围
  defaultTimeRange: {
    type: String,
    default: 'today'
  },
  // 是否显示底部信息
  showFooter: {
    type: Boolean,
    default: false
  },
  // 最后更新时间
  lastUpdateTime: {
    type: [String, Number, Date],
    default: null
  },
  // 数据数量
  dataCount: {
    type: Number,
    default: undefined
  },
  // 容器样式类
  containerClass: {
    type: [String, Array, Object],
    default: ''
  }
})

// Events定义
const emit = defineEmits([
  'refresh',
  'fullscreen',
  'more',
  'time-range-change',
  'chart-type-change',
  'touch-start',
  'touch-move',
  'touch-end'
])

// 响应式数据
const activeTimeRange = ref(props.defaultTimeRange)
const activeChartType = ref(props.chartType)

// 生成唯一的canvas ID
const generateCanvasId = () => {
  return `chart-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}
const canvasId = ref(generateCanvasId())

// 计算属性
const contentStyle = computed(() => {
  return {
    height: typeof props.height === 'number' ? `${props.height}rpx` : props.height
  }
})

const formatUpdateTime = computed(() => {
  if (!props.lastUpdateTime) return ''
  return formatUtils.dateFormat.relative(props.lastUpdateTime)
})

// 方法
const handleRefresh = () => {
  emit('refresh')
}

const handleFullscreen = () => {
  emit('fullscreen')
}

const handleMore = () => {
  emit('more')
}

const handleTimeRangeChange = (value) => {
  activeTimeRange.value = value
  emit('time-range-change', value)
}

const handleChartTypeChange = (value) => {
  activeChartType.value = value
  emit('chart-type-change', value)
}

const handleTouchStart = (event) => {
  emit('touch-start', event)
}

const handleTouchMove = (event) => {
  emit('touch-move', event)
}

const handleTouchEnd = (event) => {
  emit('touch-end', event)
}

// 生命周期
onMounted(() => {
  // 初始化图表
  nextTick(() => {
    initChart()
  })
})

onUnmounted(() => {
  // 清理图表资源
  cleanupChart()
})

// 图表初始化和清理
const initChart = () => {
  // 图表初始化逻辑
  // 这里可以集成具体的图表库，如 uCharts、ECharts 等
  console.log('初始化图表:', canvasId.value)
}

const cleanupChart = () => {
  // 图表清理逻辑
  console.log('清理图表资源:', canvasId.value)
}

// 暴露方法给父组件
defineExpose({
  refresh: handleRefresh,
  getCanvasId: () => canvasId.value,
  initChart,
  cleanupChart
})
</script>

<style lang="scss" scoped>
.chart-container {
  background: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24rpx 24rpx 0;
  
  .chart-title {
    flex: 1;
    
    .title-text {
      font-size: 32rpx;
      font-weight: 600;
      color: #2c3e50;
      line-height: 1.2;
    }
    
    .title-subtitle {
      display: block;
      font-size: 24rpx;
      color: #7f8c8d;
      margin-top: 8rpx;
    }
  }
  
  .chart-actions {
    display: flex;
    gap: 16rpx;
    
    .action-item {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f8f9fa;
      border-radius: 8rpx;
      transition: all 0.2s ease;
      
      &:active {
        background: #e9ecef;
        transform: scale(0.95);
      }
      
      .action-icon {
        font-size: 28rpx;
      }
    }
  }
}

.chart-toolbar {
  padding: 16rpx 24rpx;
  border-bottom: 2rpx solid #f1f2f6;
  
  .toolbar-section {
    margin-bottom: 16rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .time-range-tabs {
    display: flex;
    gap: 8rpx;
    
    .time-tab {
      padding: 12rpx 24rpx;
      background: #f8f9fa;
      border-radius: 20rpx;
      font-size: 24rpx;
      color: #7f8c8d;
      transition: all 0.2s ease;
      
      &.active {
        background: #007AFF;
        color: #ffffff;
      }
      
      &:active {
        transform: scale(0.95);
      }
    }
  }
  
  .chart-type-tabs {
    display: flex;
    gap: 8rpx;
    
    .type-tab {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 16rpx 20rpx;
      background: #f8f9fa;
      border-radius: 12rpx;
      transition: all 0.2s ease;
      
      &.active {
        background: #007AFF;
        
        .type-icon,
        .type-label {
          color: #ffffff;
        }
      }
      
      &:active {
        transform: scale(0.95);
      }
      
      .type-icon {
        font-size: 32rpx;
        margin-bottom: 8rpx;
      }
      
      .type-label {
        font-size: 20rpx;
        color: #7f8c8d;
      }
    }
  }
}

.chart-content {
  position: relative;
  padding: 24rpx;
  
  .chart-loading,
  .chart-empty,
  .chart-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 200rpx;
  }
  
  .chart-loading {
    .loading-text {
      margin-top: 16rpx;
      font-size: 24rpx;
      color: #7f8c8d;
    }
  }
  
  .chart-empty {
    .empty-icon {
      font-size: 80rpx;
      margin-bottom: 16rpx;
      opacity: 0.5;
    }
    
    .empty-text {
      font-size: 28rpx;
      color: #95a5a6;
      margin-bottom: 24rpx;
    }
    
    .empty-action {
      padding: 16rpx 32rpx;
      background: #007AFF;
      color: #ffffff;
      border: none;
      border-radius: 8rpx;
      font-size: 24rpx;
    }
  }
  
  .chart-error {
    .error-icon {
      font-size: 80rpx;
      margin-bottom: 16rpx;
    }
    
    .error-text {
      font-size: 28rpx;
      color: #e74c3c;
      margin-bottom: 24rpx;
      text-align: center;
    }
    
    .error-action {
      padding: 16rpx 32rpx;
      background: #e74c3c;
      color: #ffffff;
      border: none;
      border-radius: 8rpx;
      font-size: 24rpx;
    }
  }
  
  .chart-wrapper {
    width: 100%;
    height: 100%;
    
    .chart-canvas {
      width: 100%;
      height: 100%;
    }
  }
}

.chart-footer {
  padding: 16rpx 24rpx 24rpx;
  border-top: 2rpx solid #f1f2f6;
  
  .chart-info {
    display: flex;
    justify-content: space-between;
    
    .info-item {
      display: flex;
      align-items: center;
      
      .info-label {
        font-size: 22rpx;
        color: #95a5a6;
        margin-right: 8rpx;
      }
      
      .info-value {
        font-size: 22rpx;
        color: #2c3e50;
        font-weight: 500;
      }
    }
  }
}
</style>