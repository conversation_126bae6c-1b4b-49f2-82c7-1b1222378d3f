using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Admin.NET.Plugin.MQTT.Interfaces
{
    /// <summary>
    /// 设备控制服务接口
    /// 定义设备扫描、控制和状态管理的核心功能
    /// </summary>
    public interface IDeviceControlService : IDisposable
    {
        /// <summary>
        /// 设备发现事件
        /// </summary>
        event EventHandler<DeviceDiscoveredEventArgs> DeviceDiscovered;
        
        /// <summary>
        /// 设备状态变化事件
        /// </summary>
        event EventHandler<DeviceStatusChangedEventArgs> DeviceStatusChanged;
        
        /// <summary>
        /// 扫描设备
        /// </summary>
        /// <param name="area">区域</param>
        /// <param name="address">地址（可选，为空则扫描整个区域）</param>
        /// <param name="timeoutSeconds">超时时间（秒）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>扫描会话ID</returns>
        Task<string> ScanDevicesAsync(string area, string address = null, int timeoutSeconds = 30, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 获取扫描结果
        /// </summary>
        /// <param name="sessionId">扫描会话ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>设备列表</returns>
        Task<List<DeviceInfo>> GetScanResultsAsync(string sessionId, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 获取扫描状态
        /// </summary>
        /// <param name="sessionId">扫描会话ID</param>
        /// <returns>扫描状态</returns>
        ScanSessionStatus GetScanStatus(string sessionId);
        
        /// <summary>
        /// 取消扫描
        /// </summary>
        /// <param name="sessionId">扫描会话ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task CancelScanAsync(string sessionId, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 控制设备
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="command">控制命令</param>
        /// <param name="parameters">命令参数</param>
        /// <param name="timeoutSeconds">超时时间（秒）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>控制结果</returns>
        Task<DeviceControlResult> ControlDeviceAsync(string deviceId, string command, Dictionary<string, object> parameters = null, int timeoutSeconds = 10, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 获取设备状态
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="timeoutSeconds">超时时间（秒）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>设备状态</returns>
        Task<DeviceStatus> GetDeviceStatusAsync(string deviceId, int timeoutSeconds = 10, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 批量控制设备
        /// </summary>
        /// <param name="deviceIds">设备ID列表</param>
        /// <param name="command">控制命令</param>
        /// <param name="parameters">命令参数</param>
        /// <param name="timeoutSeconds">超时时间（秒）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>批量控制结果</returns>
        Task<List<DeviceControlResult>> BatchControlDevicesAsync(List<string> deviceIds, string command, Dictionary<string, object> parameters = null, int timeoutSeconds = 30, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 获取在线设备列表
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>在线设备列表</returns>
        Task<List<DeviceInfo>> GetOnlineDevicesAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 启动服务
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task StartAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 停止服务
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task StopAsync(CancellationToken cancellationToken = default);
    }
    
    /// <summary>
    /// 设备发现事件参数
    /// </summary>
    public class DeviceDiscoveredEventArgs : EventArgs
    {
        /// <summary>
        /// 发现的设备信息
        /// </summary>
        public DeviceInfo Device { get; set; }
        
        /// <summary>
        /// 扫描会话ID
        /// </summary>
        public string SessionId { get; set; }
    }
    
    /// <summary>
    /// 设备状态变化事件参数
    /// </summary>
    public class DeviceStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId { get; set; }
        
        /// <summary>
        /// 旧状态
        /// </summary>
        public DeviceStatus OldStatus { get; set; }
        
        /// <summary>
        /// 新状态
        /// </summary>
        public DeviceStatus NewStatus { get; set; }
        
        /// <summary>
        /// 状态变化时间
        /// </summary>
        public DateTime ChangedAt { get; set; }
    }
    
    /// <summary>
    /// 设备信息
    /// </summary>
    public class DeviceInfo
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId { get; set; }
        
        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName { get; set; }
        
        /// <summary>
        /// 设备类型
        /// </summary>
        public string DeviceType { get; set; }
        
        /// <summary>
        /// 设备地址
        /// </summary>
        public string Address { get; set; }
        
        /// <summary>
        /// 所属区域
        /// </summary>
        public string Area { get; set; }
        
        /// <summary>
        /// 是否在线
        /// </summary>
        public bool IsOnline { get; set; }
        
        /// <summary>
        /// 信号强度
        /// </summary>
        public int? SignalStrength { get; set; }
        
        /// <summary>
        /// 电池电量
        /// </summary>
        public int? BatteryLevel { get; set; }
        
        /// <summary>
        /// 固件版本
        /// </summary>
        public string FirmwareVersion { get; set; }
        
        /// <summary>
        /// 发现时间
        /// </summary>
        public DateTime DiscoveredAt { get; set; }
        
        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdatedAt { get; set; }
        
        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new Dictionary<string, object>();
    }
    
    /// <summary>
    /// 设备状态
    /// </summary>
    public class DeviceStatus
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId { get; set; }
        
        /// <summary>
        /// 是否在线
        /// </summary>
        public bool IsOnline { get; set; }
        
        /// <summary>
        /// 电源状态
        /// </summary>
        public bool? PowerOn { get; set; }
        
        /// <summary>
        /// 亮度（0-100）
        /// </summary>
        public int? Brightness { get; set; }
        
        /// <summary>
        /// 颜色温度
        /// </summary>
        public int? ColorTemperature { get; set; }
        
        /// <summary>
        /// RGB颜色
        /// </summary>
        public string RgbColor { get; set; }
        
        /// <summary>
        /// 信号强度
        /// </summary>
        public int? SignalStrength { get; set; }
        
        /// <summary>
        /// 电池电量
        /// </summary>
        public int? BatteryLevel { get; set; }
        
        /// <summary>
        /// 状态更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }
        
        /// <summary>
        /// 扩展状态
        /// </summary>
        public Dictionary<string, object> ExtendedStatus { get; set; } = new Dictionary<string, object>();
    }
    
    /// <summary>
    /// 设备控制结果
    /// </summary>
    public class DeviceControlResult
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId { get; set; }
        
        /// <summary>
        /// 控制命令
        /// </summary>
        public string Command { get; set; }
        
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// 响应数据
        /// </summary>
        public object ResponseData { get; set; }
        
        /// <summary>
        /// 执行时间（毫秒）
        /// </summary>
        public long ExecutionTimeMs { get; set; }
        
        /// <summary>
        /// 执行时间
        /// </summary>
        public DateTime ExecutedAt { get; set; }
    }
    
    /// <summary>
    /// 扫描会话状态
    /// </summary>
    public class ScanSessionStatus
    {
        /// <summary>
        /// 会话ID
        /// </summary>
        public string SessionId { get; set; }
        
        /// <summary>
        /// 扫描状态
        /// </summary>
        public ScanStatus Status { get; set; }
        
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartedAt { get; set; }
        
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? CompletedAt { get; set; }
        
        /// <summary>
        /// 扫描区域
        /// </summary>
        public string Area { get; set; }
        
        /// <summary>
        /// 扫描地址
        /// </summary>
        public string Address { get; set; }
        
        /// <summary>
        /// 发现的设备数量
        /// </summary>
        public int DevicesFound { get; set; }
        
        /// <summary>
        /// 超时时间（秒）
        /// </summary>
        public int TimeoutSeconds { get; set; }
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
    }
    
    /// <summary>
    /// 扫描状态枚举
    /// </summary>
    public enum ScanStatus
    {
        /// <summary>
        /// 运行中
        /// </summary>
        Running,
        
        /// <summary>
        /// 已完成
        /// </summary>
        Completed,
        
        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled,
        
        /// <summary>
        /// 超时
        /// </summary>
        Timeout,
        
        /// <summary>
        /// 错误
        /// </summary>
        Error
    }
}