@import './index.scss';

/* 移动端响应式增强样式
 * 针对能耗监控系统的移动端优化
 * 包括触摸交互、布局适配、用户体验改进
------------------------------- */

/* 超小屏幕设备 (手机竖屏) ≤375px
------------------------------- */
@media screen and (max-width: 375px) {
  // 表格优化
  .el-table {
    font-size: 12px;
    
    .el-table__header-wrapper {
      .el-table__header {
        th {
          padding: 8px 4px;
          font-size: 12px;
        }
      }
    }
    
    .el-table__body-wrapper {
      .el-table__body {
        td {
          padding: 8px 4px;
          font-size: 12px;
        }
      }
    }
  }
  
  // 按钮组优化
  .el-button-group {
    .el-button {
      padding: 6px 8px;
      font-size: 12px;
    }
  }
  
  // 表单优化
  .el-form-item {
    margin-bottom: 15px;
    
    .el-form-item__label {
      font-size: 13px;
      line-height: 1.4;
    }
    
    .el-input__inner,
    .el-textarea__inner {
      font-size: 14px;
    }
  }
}

/* 小屏幕设备 (手机) ≤576px
------------------------------- */
@media screen and (max-width: $xs) {
  // 能耗监控页面优化
  .energy-consumption {
    .consumption-stats {
      .el-col {
        margin-bottom: 15px;
      }
      
      .stat-card {
        .stat-value {
          font-size: 20px;
        }
        
        .stat-label {
          font-size: 12px;
        }
      }
    }
    
    .consumption-chart {
      height: 250px !important;
    }
    
    .consumption-table {
      .el-table__header-wrapper,
      .el-table__body-wrapper {
        overflow-x: auto;
      }
    }
  }
  
  // 设备管理页面优化
  .device-management {
    .device-toolbar {
      flex-direction: column;
      gap: 10px;
      
      .toolbar-left,
      .toolbar-right {
        width: 100%;
        justify-content: center;
      }
    }
    
    .device-stats {
      .el-col {
        margin-bottom: 10px;
      }
    }
    
    .device-map {
      height: 300px !important;
    }
  }
  
  // 故障管理页面优化
  .fault-management {
    .fault-toolbar {
      flex-direction: column;
      gap: 10px;
      
      .el-button {
        width: 100%;
        margin-bottom: 5px;
      }
    }
    
    .fault-stats {
      .el-col {
        margin-bottom: 10px;
      }
    }
  }
  
  // 照明控制页面优化
  .lighting-control {
    .control-panel {
      .control-group {
        margin-bottom: 15px;
        
        .group-header {
          font-size: 14px;
          margin-bottom: 8px;
        }
        
        .control-buttons {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 8px;
          
          .el-button {
            font-size: 12px;
            padding: 8px 12px;
          }
        }
      }
    }
    
    .scene-list {
      .scene-item {
        margin-bottom: 10px;
        padding: 12px;
        
        .scene-name {
          font-size: 14px;
        }
        
        .scene-actions {
          margin-top: 8px;
          
          .el-button {
            font-size: 12px;
            padding: 6px 10px;
          }
        }
      }
    }
  }
  
  // 定时任务页面优化
  .schedule-management {
    .schedule-form {
      .time-picker-group {
        flex-direction: column;
        
        .el-time-picker {
          width: 100%;
          margin-bottom: 10px;
        }
      }
    }
  }
}

/* 中等屏幕设备 (平板竖屏) ≤768px
------------------------------- */
@media screen and (max-width: $sm) {
  // 首页仪表板优化
  .dashboard {
    .dashboard-stats {
      .el-col {
        margin-bottom: 15px;
      }
    }
    
    .dashboard-charts {
      .chart-container {
        height: 300px !important;
        margin-bottom: 20px;
      }
    }
    
    .device-status-grid {
      .device-card {
        margin-bottom: 15px;
      }
    }
  }
  
  // 侧边栏优化
  .layout-aside {
    .el-menu {
      .el-menu-item {
        padding: 0 15px;
        
        .menu-icon {
          margin-right: 8px;
        }
        
        .menu-title {
          font-size: 14px;
        }
      }
      
      .el-submenu {
        .el-submenu__title {
          padding: 0 15px;
          font-size: 14px;
        }
      }
    }
  }
  
  // 表格响应式优化
  .responsive-table {
    .el-table {
      .el-table__header-wrapper,
      .el-table__body-wrapper {
        overflow-x: auto;
      }
      
      // 隐藏次要列
      .table-column-secondary {
        display: none;
      }
    }
  }
  
  // 对话框优化
  .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
    
    .el-dialog__header {
      padding: 15px 20px;
      
      .el-dialog__title {
        font-size: 16px;
      }
    }
    
    .el-dialog__body {
      padding: 15px 20px;
    }
    
    .el-dialog__footer {
      padding: 10px 20px 15px;
    }
  }
}

/* 触摸交互优化
------------------------------- */
@media (hover: none) and (pointer: coarse) {
  // 增大触摸目标
  .el-button {
    min-height: 44px;
    padding: 10px 16px;
    
    &.el-button--small {
      min-height: 36px;
      padding: 8px 12px;
    }
    
    &.el-button--mini {
      min-height: 32px;
      padding: 6px 10px;
    }
  }
  
  // 表格行触摸优化
  .el-table {
    .el-table__row {
      td {
        padding: 12px 8px;
      }
    }
  }
  
  // 菜单项触摸优化
  .el-menu-item,
  .el-submenu__title {
    min-height: 48px;
    line-height: 48px;
  }
  
  // 标签页触摸优化
  .el-tabs__item {
    padding: 0 20px;
    min-height: 44px;
    line-height: 44px;
  }
  
  // 分页器触摸优化
  .el-pagination {
    .el-pager li,
    .btn-prev,
    .btn-next {
      min-width: 44px;
      min-height: 44px;
      line-height: 44px;
    }
  }
  
  // 开关控件触摸优化
  .el-switch {
    .el-switch__core {
      min-width: 50px;
      height: 24px;
      
      &::after {
        width: 20px;
        height: 20px;
      }
    }
  }
  
  // 滑块触摸优化
  .el-slider {
    .el-slider__button {
      width: 20px;
      height: 20px;
    }
    
    .el-slider__runway {
      height: 8px;
    }
  }
}

/* 能耗监控移动端专用样式
------------------------------- */
.mobile-energy-dashboard {
  .energy-overview {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
    
    .overview-card {
      padding: 15px;
      border-radius: 8px;
      background: #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      .card-header {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        
        .card-icon {
          width: 32px;
          height: 32px;
          margin-right: 10px;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .card-title {
          font-size: 14px;
          color: #666;
        }
      }
      
      .card-value {
        font-size: 24px;
        font-weight: bold;
        color: #333;
        margin-bottom: 5px;
      }
      
      .card-trend {
        font-size: 12px;
        display: flex;
        align-items: center;
        
        &.trend-up {
          color: #f56c6c;
        }
        
        &.trend-down {
          color: #67c23a;
        }
        
        .trend-icon {
          margin-right: 4px;
        }
      }
    }
  }
  
  .energy-chart-mobile {
    background: #fff;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      
      .chart-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
      
      .chart-period {
        .el-button-group {
          .el-button {
            padding: 6px 12px;
            font-size: 12px;
          }
        }
      }
    }
    
    .chart-container {
      height: 250px;
    }
  }
}

/* 设备控制移动端专用样式
------------------------------- */
.mobile-device-control {
  .device-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    
    .device-card {
      background: #fff;
      border-radius: 8px;
      padding: 15px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      text-align: center;
      
      .device-icon {
        width: 48px;
        height: 48px;
        margin: 0 auto 10px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        
        &.device-online {
          background: #e8f5e8;
          color: #67c23a;
        }
        
        &.device-offline {
          background: #fef0f0;
          color: #f56c6c;
        }
      }
      
      .device-name {
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-bottom: 5px;
      }
      
      .device-status {
        font-size: 12px;
        color: #666;
        margin-bottom: 10px;
      }
      
      .device-controls {
        display: flex;
        justify-content: center;
        gap: 8px;
        
        .control-btn {
          width: 36px;
          height: 36px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid #ddd;
          background: #fff;
          
          &.active {
            background: #409eff;
            color: #fff;
            border-color: #409eff;
          }
        }
      }
    }
  }
}

/* 故障管理移动端专用样式
------------------------------- */
.mobile-fault-management {
  .fault-list {
    .fault-item {
      background: #fff;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 10px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      .fault-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 10px;
        
        .fault-title {
          flex: 1;
          
          .fault-type {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
          }
          
          .fault-device {
            font-size: 12px;
            color: #666;
          }
        }
        
        .fault-status {
          .el-tag {
            font-size: 11px;
          }
        }
      }
      
      .fault-description {
        font-size: 13px;
        color: #666;
        line-height: 1.4;
        margin-bottom: 10px;
      }
      
      .fault-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: #999;
        
        .fault-time {
          display: flex;
          align-items: center;
          
          .time-icon {
            margin-right: 4px;
          }
        }
        
        .fault-actions {
          display: flex;
          gap: 8px;
          
          .action-btn {
            padding: 4px 8px;
            font-size: 11px;
            border-radius: 4px;
          }
        }
      }
    }
  }
}

/* 下拉刷新和上拉加载
------------------------------- */
.mobile-pull-refresh {
  .pull-refresh-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50px;
    font-size: 14px;
    color: #666;
    
    .refresh-icon {
      margin-right: 8px;
      animation: rotate 1s linear infinite;
    }
  }
  
  .load-more-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50px;
    font-size: 14px;
    color: #666;
    
    .loading-icon {
      margin-right: 8px;
      animation: rotate 1s linear infinite;
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 移动端导航优化
------------------------------- */
.mobile-navigation {
  .nav-tabs {
    display: flex;
    background: #fff;
    border-bottom: 1px solid #eee;
    overflow-x: auto;
    
    .nav-tab {
      flex: 0 0 auto;
      padding: 12px 20px;
      font-size: 14px;
      color: #666;
      text-align: center;
      border-bottom: 2px solid transparent;
      white-space: nowrap;
      
      &.active {
        color: #409eff;
        border-bottom-color: #409eff;
      }
    }
  }
}

/* 移动端表单优化
------------------------------- */
.mobile-form {
  .form-section {
    background: #fff;
    border-radius: 8px;
    margin-bottom: 15px;
    overflow: hidden;
    
    .section-header {
      padding: 15px;
      background: #f8f9fa;
      border-bottom: 1px solid #eee;
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }
    
    .section-content {
      padding: 15px;
      
      .el-form-item {
        margin-bottom: 20px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .el-form-item__label {
          font-size: 14px;
          color: #333;
          margin-bottom: 8px;
        }
        
        .el-input,
        .el-select,
        .el-textarea {
          .el-input__inner,
          .el-textarea__inner {
            border-radius: 6px;
            border: 1px solid #ddd;
            font-size: 14px;
            
            &:focus {
              border-color: #409eff;
            }
          }
        }
      }
    }
  }
  
  .form-actions {
    padding: 15px;
    background: #fff;
    border-radius: 8px;
    
    .el-button {
      width: 100%;
      margin-bottom: 10px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

/* 移动端搜索优化
------------------------------- */
.mobile-search {
  .search-bar {
    padding: 15px;
    background: #fff;
    border-bottom: 1px solid #eee;
    
    .search-input {
      position: relative;
      
      .el-input {
        .el-input__inner {
          border-radius: 20px;
          padding-left: 40px;
          background: #f5f5f5;
          border: none;
        }
      }
      
      .search-icon {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #999;
        z-index: 1;
      }
    }
  }
  
  .search-filters {
    padding: 10px 15px;
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
    
    .filter-tags {
      display: flex;
      gap: 8px;
      overflow-x: auto;
      
      .filter-tag {
        flex: 0 0 auto;
        padding: 6px 12px;
        background: #fff;
        border: 1px solid #ddd;
        border-radius: 16px;
        font-size: 12px;
        color: #666;
        white-space: nowrap;
        
        &.active {
          background: #409eff;
          color: #fff;
          border-color: #409eff;
        }
      }
    }
  }
}

/* 移动端底部安全区域适配
------------------------------- */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .mobile-safe-area {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .mobile-fixed-bottom {
    padding-bottom: calc(20px + env(safe-area-inset-bottom));
  }
}

/* iOS样式适配
------------------------------- */
@supports (-webkit-touch-callout: none) {
  .mobile-ios-fix {
    -webkit-overflow-scrolling: touch;
    
    .el-input__inner,
    .el-textarea__inner {
      -webkit-appearance: none;
      border-radius: 6px;
    }
    
    .el-button {
      -webkit-appearance: none;
    }
  }
}

/* 横屏适配
------------------------------- */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .mobile-landscape {
    .dashboard-stats {
      .el-col {
        margin-bottom: 10px;
      }
    }
    
    .chart-container {
      height: 200px !important;
    }
    
    .el-dialog {
      .el-dialog__body {
        max-height: 60vh;
        overflow-y: auto;
      }
    }
  }
}