/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 设备输出参数
 * @export
 * @interface EnergyDeviceOutput
 */
export interface EnergyDeviceOutput {
    /**
     * 主键ID
     * @type {number}
     * @memberof EnergyDeviceOutput
     */
    id?: number;
    /**
     * 设备编码
     * @type {string}
     * @memberof EnergyDeviceOutput
     */
    deviceCode?: string | null;
    /**
     * 设备名称
     * @type {string}
     * @memberof EnergyDeviceOutput
     */
    deviceName?: string | null;
    /**
     * 设备类型
     * @type {string}
     * @memberof EnergyDeviceOutput
     */
    deviceType?: string | null;
    /**
     * 设备型号
     * @type {string}
     * @memberof EnergyDeviceOutput
     */
    deviceModel?: string | null;
    /**
     * 设备位置
     * @type {string}
     * @memberof EnergyDeviceOutput
     */
    location?: string | null;
    /**
     * 分组ID
     * @type {number}
     * @memberof EnergyDeviceOutput
     */
    groupId?: number | null;
    /**
     * 分组名称
     * @type {string}
     * @memberof EnergyDeviceOutput
     */
    groupName?: string | null;
    /**
     * IP地址
     * @type {string}
     * @memberof EnergyDeviceOutput
     */
    ipAddress?: string | null;
    /**
     * MAC地址
     * @type {string}
     * @memberof EnergyDeviceOutput
     */
    macAddress?: string | null;
    /**
     * 固件版本
     * @type {string}
     * @memberof EnergyDeviceOutput
     */
    firmwareVersion?: string | null;
    /**
     * 硬件版本
     * @type {string}
     * @memberof EnergyDeviceOutput
     */
    hardwareVersion?: string | null;
    /**
     * 额定功率
     * @type {number}
     * @memberof EnergyDeviceOutput
     */
    ratedPower?: number | null;
    /**
     * 额定电压
     * @type {number}
     * @memberof EnergyDeviceOutput
     */
    ratedVoltage?: number | null;
    /**
     * 额定电流
     * @type {number}
     * @memberof EnergyDeviceOutput
     */
    ratedCurrent?: number | null;
    /**
     * 安装日期
     * @type {Date}
     * @memberof EnergyDeviceOutput
     */
    installDate?: Date | null;
    /**
     * 保修期至
     * @type {Date}
     * @memberof EnergyDeviceOutput
     */
    warrantyDate?: Date | null;
    /**
     * 供应商
     * @type {string}
     * @memberof EnergyDeviceOutput
     */
    supplier?: string | null;
    /**
     * 在线状态
     * @type {number}
     * @memberof EnergyDeviceOutput
     */
    onlineStatus?: number;
    /**
     * 最后在线时间
     * @type {Date}
     * @memberof EnergyDeviceOutput
     */
    lastOnlineTime?: Date | null;
    /**
     * 亮度
     * @type {number}
     * @memberof EnergyDeviceOutput
     */
    brightness?: number | null;
    /**
     * 温度
     * @type {number}
     * @memberof EnergyDeviceOutput
     */
    temperature?: number | null;
    /**
     * 湿度
     * @type {number}
     * @memberof EnergyDeviceOutput
     */
    humidity?: number | null;
    /**
     * 功耗
     * @type {number}
     * @memberof EnergyDeviceOutput
     */
    powerConsumption?: number | null;
    /**
     * 电压
     * @type {number}
     * @memberof EnergyDeviceOutput
     */
    voltage?: number | null;
    /**
     * 电流
     * @type {number}
     * @memberof EnergyDeviceOutput
     */
    current?: number | null;
    /**
     * 功率因数
     * @type {number}
     * @memberof EnergyDeviceOutput
     */
    powerFactor?: number | null;
    /**
     * 频率
     * @type {number}
     * @memberof EnergyDeviceOutput
     */
    frequency?: number | null;
    /**
     * 运行时长（小时）
     * @type {number}
     * @memberof EnergyDeviceOutput
     */
    runningTime?: number | null;
    /**
     * 故障次数
     * @type {number}
     * @memberof EnergyDeviceOutput
     */
    faultCount?: number | null;
    /**
     * 最后故障时间
     * @type {Date}
     * @memberof EnergyDeviceOutput
     */
    lastFaultTime?: Date | null;
    /**
     * 维护日期
     * @type {Date}
     * @memberof EnergyDeviceOutput
     */
    maintenanceDate?: Date | null;
    /**
     * 保修到期日期
     * @type {Date}
     * @memberof EnergyDeviceOutput
     */
    warrantyExpiry?: Date | null;
    /**
     * 是否在线
     * @type {boolean}
     * @memberof EnergyDeviceOutput
     */
    isOnline?: boolean | null;
    /**
     * 制造商
     * @type {string}
     * @memberof EnergyDeviceOutput
     */
    manufacturer?: string | null;
    /**
     * 功率等级
     * @type {number}
     * @memberof EnergyDeviceOutput
     */
    powerRating?: number | null;
    /**
     * 电压等级
     * @type {number}
     * @memberof EnergyDeviceOutput
     */
    voltageRating?: number | null;
    /**
     * 电流等级
     * @type {number}
     * @memberof EnergyDeviceOutput
     */
    currentRating?: number | null;
    /**
     * 租户ID
     * @type {number}
     * @memberof EnergyDeviceOutput
     */
    tenantId?: number | null;
    /**
     * 备注
     * @type {string}
     * @memberof EnergyDeviceOutput
     */
    remark?: string | null;
    /**
     * 排序
     * @type {number}
     * @memberof EnergyDeviceOutput
     */
    sort?: number;
    /**
     * 状态
     * @type {number}
     * @memberof EnergyDeviceOutput
     */
    status?: number;
    /**
     * 创建时间
     * @type {Date}
     * @memberof EnergyDeviceOutput
     */
    createTime?: Date;
    /**
     * 更新时间
     * @type {Date}
     * @memberof EnergyDeviceOutput
     */
    updateTime?: Date | null;
}
