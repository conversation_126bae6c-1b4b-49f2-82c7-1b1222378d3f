<template>
  <div ref="containerRef" class="system-consumption energy-consumption mobile-energy-dashboard responsive-table mobile-safe-area">
    <!-- 查询表单 -->
    <el-card class="box-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>能耗监控</span>
        </div>
      </template>
      
      <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
        <el-form-item label="设备名称" prop="deviceName">
          <el-input
            v-model="queryParams.deviceName"
            placeholder="请输入设备名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="时间范围" prop="dateRange">
          <el-date-picker
            v-model="queryParams.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          <el-button type="info" icon="TrendCharts" @click="openEnhancedAnalysis">增强分析</el-button>
          <el-button type="success" icon="Download" @click="handleExport">导出</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ totalConsumption }}</div>
            <div class="stat-label">总能耗 (kWh)</div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ todayConsumption }}</div>
            <div class="stat-label">今日能耗 (kWh)</div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ avgConsumption }}</div>
            <div class="stat-label">平均能耗 (kWh)</div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ activeDevices }}</div>
            <div class="stat-label">活跃设备数</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="chart-section">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>能耗趋势图</span>
              <el-radio-group v-model="trendPeriod" size="small" @change="loadTrendChart">
                <el-radio-button label="day">日</el-radio-button>
                <el-radio-button label="week">周</el-radio-button>
                <el-radio-button label="month">月</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="trendChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>设备能耗排行</span>
            </div>
          </template>
          <div ref="rankChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 能耗记录表格 -->
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>能耗记录</span>
        </div>
      </template>
      
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="consumptionList"
        row-key="id"
        border
        stripe
        style="width: 100%"
      >
        <el-table-column prop="deviceName" label="设备名称" min-width="120" show-overflow-tooltip />
        <el-table-column prop="deviceCode" label="设备编号" min-width="120" show-overflow-tooltip />
        <el-table-column prop="consumption" label="能耗值(kWh)" width="120" align="center">
          <template #default="{ row }">
            <span class="consumption-value">{{ row.consumption }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="power" label="功率(W)" width="100" align="center" />
        <el-table-column prop="duration" label="运行时长(h)" width="120" align="center" />
        <el-table-column prop="cost" label="电费(元)" width="100" align="center">
          <template #default="{ row }">
            <span class="cost-value">¥{{ row.cost }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="recordTime" label="记录时间" width="180" align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.recordTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" size="small" @click="viewDetail(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 详情弹窗 -->
    <el-dialog v-model="detailVisible" title="能耗详情" width="600px">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="设备名称">{{ detailData.deviceName }}</el-descriptions-item>
        <el-descriptions-item label="设备编号">{{ detailData.deviceCode }}</el-descriptions-item>
        <el-descriptions-item label="能耗值">{{ detailData.consumption }} kWh</el-descriptions-item>
        <el-descriptions-item label="功率">{{ detailData.power }} W</el-descriptions-item>
        <el-descriptions-item label="运行时长">{{ detailData.duration }} 小时</el-descriptions-item>
        <el-descriptions-item label="电费">¥{{ detailData.cost }}</el-descriptions-item>
        <el-descriptions-item label="记录时间" :span="2">{{ formatDateTime(detailData.recordTime) }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ detailData.remark || '无' }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 增强分析组件 -->
    <ConsumptionEnhanced
      ref="consumptionEnhancedRef"
      v-model:visible="enhancedVisible"
      @refresh="handleQuery"
    />
  </div>
</template>

<script setup lang="ts" name="consumption">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useSystemApi } from '/@/api-services/system'
import { formatDateTime } from '@/utils/formatTime'
import * as echarts from 'echarts'
import ConsumptionEnhanced from './consumptionEnhanced.vue'
import { useDeviceDetection, usePullToRefresh, useMobileTable } from '@/composables/useMobile'
import { handleApiError } from '@/utils/errorHandler'

const loading = ref(false)
const total = ref(0)
const consumptionList = ref([])
const detailVisible = ref(false)
const detailData = ref({})
const consumptionEnhancedRef = ref(null)
const enhancedVisible = ref(false)
const containerRef = ref(null)
const tableRef = ref(null)

// 移动端适配
const { deviceInfo } = useDeviceDetection()
const { isRefreshing } = usePullToRefresh(containerRef, async () => {
  await getList()
  await getStatistics()
})
const { optimizeTable } = useMobileTable(tableRef)
const trendPeriod = ref('day')
const trendChartRef = ref()
const rankChartRef = ref()
let trendChart: any = null
let rankChart: any = null

// 统计数据
const totalConsumption = ref(0)
const todayConsumption = ref(0)
const avgConsumption = ref(0)
const activeDevices = ref(0)

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  deviceName: '',
  dateRange: []
})

// 获取列表
const getList = async () => {
  loading.value = true
  try {
    const systemApi = useSystemApi()
    const params = {
      ...queryParams,
      startTime: queryParams.dateRange?.[0],
      endTime: queryParams.dateRange?.[1]
    }
    delete params.dateRange
    
    const { data } = await systemApi.energyConsumption.page(params)
    consumptionList.value = data.items || []
    total.value = data.total || 0
  } catch (error) {
    handleApiError(error, '获取能耗记录', '获取能耗记录失败')
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const getStatistics = async () => {
  try {
    const systemApi = useSystemApi()
    const { data } = await systemApi.energyConsumption.getStat({})
    totalConsumption.value = data.totalConsumption || 0
    todayConsumption.value = data.todayConsumption || 0
    avgConsumption.value = data.avgConsumption || 0
    activeDevices.value = data.activeDevices || 0
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 加载趋势图表
const loadTrendChart = async () => {
  try {
    const systemApi = useSystemApi()
    const { data } = await systemApi.energyConsumption.getTrend({ period: trendPeriod.value })
    
    if (trendChart) {
      const option = {
        title: {
          text: '能耗趋势',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: data.dates || []
        },
        yAxis: {
          type: 'value',
          name: '能耗(kWh)'
        },
        series: [{
          data: data.values || [],
          type: 'line',
          smooth: true,
          itemStyle: {
            color: '#409EFF'
          }
        }]
      }
      trendChart.setOption(option)
    }
  } catch (error) {
    console.error('加载趋势图表失败:', error)
  }
}

// 加载排行图表
const loadRankChart = async () => {
  try {
    const systemApi = useSystemApi()
    const { data } = await systemApi.energyConsumption.getRank({ limit: 10 })
    
    if (rankChart) {
      const option = {
        title: {
          text: '设备能耗排行',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'value',
          name: '能耗(kWh)'
        },
        yAxis: {
          type: 'category',
          data: data.deviceNames || []
        },
        series: [{
          data: data.consumptions || [],
          type: 'bar',
          itemStyle: {
            color: '#67C23A'
          }
        }]
      }
      rankChart.setOption(option)
    }
  } catch (error) {
    console.error('加载排行图表失败:', error)
  }
}

// 初始化图表
const initCharts = async () => {
  await nextTick()
  
  if (trendChartRef.value) {
    trendChart = echarts.init(trendChartRef.value)
    loadTrendChart()
  }
  
  if (rankChartRef.value) {
    rankChart = echarts.init(rankChartRef.value)
    loadRankChart()
  }
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    trendChart?.resize()
    rankChart?.resize()
  })
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置
const resetQuery = () => {
  queryParams.deviceName = ''
  queryParams.dateRange = []
  queryParams.pageNum = 1
  getList()
}

// 导出
const handleExport = async () => {
  try {
    const params = {
      deviceName: queryParams.deviceName,
      startTime: queryParams.dateRange?.[0],
      endTime: queryParams.dateRange?.[1]
    }
    
    const systemApi = useSystemApi()
    await systemApi.energyConsumption.export(params)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 查看详情
const viewDetail = (row: any) => {
  detailData.value = { ...row }
  detailVisible.value = true
}

// 打开增强分析
const openEnhancedAnalysis = () => {
  enhancedVisible.value = true
}

onMounted(() => {
  getList()
  getStatistics()
  initCharts()
})
</script>

<style scoped lang="scss">
.system-consumption {
  .box-card {
    margin-bottom: 20px;
  }
  
  .stats-cards {
    margin-bottom: 20px;
    
    .stat-card {
      .stat-item {
        text-align: center;
        
        .stat-value {
          font-size: 28px;
          font-weight: bold;
          color: #409EFF;
          margin-bottom: 8px;
        }
        
        .stat-label {
          font-size: 14px;
          color: #909399;
        }
      }
    }
  }
  
  .chart-section {
    margin-bottom: 20px;
    
    .chart-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .chart-container {
        height: 300px;
        width: 100%;
      }
    }
  }
  
  .consumption-value {
    font-weight: bold;
    color: #E6A23C;
  }
  
  .cost-value {
    font-weight: bold;
    color: #F56C6C;
  }
}
</style>