<template>
	<view class="energy-container">
		<!-- 实时功耗概览 -->
		<view class="realtime-card">
			<view class="card-header">
				<view class="card-title">实时功耗</view>
				<view class="refresh-btn" @tap="refreshRealtime">
					<text class="refresh-icon">🔄</text>
					<text>刷新</text>
				</view>
			</view>
			
			<!-- 骨架屏 -->
			<view v-if="loading && realtimeData.currentPower === 0" class="skeleton-wrapper">
				<SkeletonScreen
					type="statistic-card"
					:animated="true"
				/>
			</view>
			
			<!-- 实际内容 -->
			<view v-else>
				<view class="power-display">
					<view class="current-power">
						<view class="power-value">{{ realtimeData.currentPower }}</view>
						<view class="power-unit">W</view>
					</view>
					<view class="power-trend" :class="realtimeData.trendClass">
						<text class="trend-icon">{{ realtimeData.trendIcon }}</text>
						<text class="trend-text">{{ realtimeData.trendText }}</text>
					</view>
				</view>
				
				<view class="power-stats">
					<view class="stat-item" v-for="stat in powerStats" :key="stat.key">
						<view class="stat-label">{{ stat.label }}</view>
						<view class="stat-value" :style="{ color: stat.color }">{{ stat.value }}</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 今日统计 -->
		<view class="today-stats-card">
			<view class="card-header">
				<view class="card-title">今日统计</view>
				<view class="date-info">{{ todayDate }}</view>
			</view>
			
			<!-- 骨架屏 -->
			<view v-if="loading && todayStats.length === 0" class="skeleton-wrapper">
				<SkeletonScreen
					type="statistic-card"
					:count="4"
					:animated="true"
				/>
			</view>
			
			<!-- 实际内容 -->
			<view v-else class="stats-grid">
				<view class="stat-card" v-for="stat in todayStats" :key="stat.key">
					<view class="stat-icon" :class="stat.iconClass">
						<text>{{ stat.icon }}</text>
					</view>
					<view class="stat-content">
						<view class="stat-number">{{ stat.value }}</view>
						<view class="stat-desc">{{ stat.label }}</view>
						<view class="stat-change" :class="stat.changeClass" v-if="stat.change">
							{{ stat.change }}
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 功耗趋势图表 -->
		<view class="chart-card">
			<view class="card-header">
				<view class="card-title">功耗趋势</view>
				<view class="chart-tabs">
					<text 
						v-for="tab in chartTabs" 
						:key="tab.key"
						:class="{ active: currentChartTab === tab.key }"
						@tap="switchChartTab(tab.key)"
					>
						{{ tab.label }}
					</text>
				</view>
			</view>
			
			<!-- 骨架屏 -->
			<view v-if="loading && chartData.length === 0" class="skeleton-wrapper">
				<SkeletonScreen
					type="chart"
					:animated="true"
				/>
			</view>
			
			<!-- 实际内容 -->
			<view v-else class="chart-container">
				<view class="chart-placeholder">
					<view class="chart-bars">
						<view 
							class="chart-bar" 
							v-for="(bar, index) in chartData" 
							:key="index"
							:style="{ height: bar.height + '%', backgroundColor: bar.color }"
						>
							<view class="bar-value">{{ bar.value }}</view>
						</view>
					</view>
					<view class="chart-labels">
						<text v-for="(label, index) in chartLabels" :key="index">{{ label }}</text>
					</view>
				</view>
				
				<view class="chart-summary">
					<view class="summary-item">
						<text class="summary-label">平均功耗</text>
						<text class="summary-value">{{ chartSummary.average }}W</text>
					</view>
					<view class="summary-item">
						<text class="summary-label">峰值功耗</text>
						<text class="summary-value">{{ chartSummary.peak }}W</text>
					</view>
					<view class="summary-item">
						<text class="summary-label">总耗电量</text>
						<text class="summary-value">{{ chartSummary.total }}kWh</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 节能分析 -->
		<view class="energy-saving-card">
			<view class="card-header">
				<view class="card-title">节能分析</view>
				<view class="analysis-period">本月数据</view>
			</view>
			
			<!-- 骨架屏 -->
			<view v-if="loading && savingData.percentage === 0" class="skeleton-wrapper">
				<SkeletonScreen
					type="statistic-card"
					:animated="true"
				/>
			</view>
			
			<!-- 实际内容 -->
			<view v-else class="saving-overview">
				<view class="saving-circle">
					<view class="circle-progress" :style="{ background: savingProgress.gradient }">
						<view class="circle-inner">
							<view class="saving-percentage">{{ savingData.percentage }}%</view>
							<view class="saving-label">节能率</view>
						</view>
					</view>
				</view>
				
				<view class="saving-details">
					<view class="detail-item">
						<view class="detail-label">节约电量</view>
						<view class="detail-value">{{ savingData.savedEnergy }}kWh</view>
					</view>
					<view class="detail-item">
						<view class="detail-label">节约费用</view>
						<view class="detail-value">¥{{ savingData.savedCost }}</view>
					</view>
					<view class="detail-item">
						<view class="detail-label">减少碳排放</view>
						<view class="detail-value">{{ savingData.carbonReduction }}kg</view>
					</view>
				</view>
			</view>
			
			<view class="saving-tips">
				<view class="tips-header">
					<text class="tips-icon">💡</text>
					<text class="tips-title">节能建议</text>
				</view>
				<view class="tips-list">
					<view class="tip-item" v-for="tip in energyTips" :key="tip.id">
						<view class="tip-icon">{{ tip.icon }}</view>
						<view class="tip-content">
							<view class="tip-text">{{ tip.text }}</view>
							<view class="tip-benefit">预计节能: {{ tip.benefit }}</view>
						</view>
						<view class="tip-action" @tap="applyTip(tip)">
							<text>应用</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 设备能耗排行 -->
		<view class="device-ranking-card">
			<view class="card-header">
				<view class="card-title">设备能耗排行</view>
				<view class="ranking-period">
					<text 
						v-for="period in rankingPeriods" 
						:key="period.key"
						:class="{ active: currentRankingPeriod === period.key }"
						@tap="switchRankingPeriod(period.key)"
					>
						{{ period.label }}
					</text>
				</view>
			</view>
			
			<!-- 骨架屏 -->
			<view v-if="loading && deviceRanking.length === 0" class="skeleton-wrapper">
				<SkeletonScreen
					type="list-item"
					:count="5"
					:animated="true"
				/>
			</view>
			
			<!-- 实际内容 -->
			<view v-else class="ranking-list">
				<view class="ranking-item" v-for="(device, index) in deviceRanking" :key="device.id">
					<view class="ranking-number" :class="getRankingClass(index)">
						{{ index + 1 }}
					</view>
					<view class="device-info">
						<view class="device-name">{{ device.name }}</view>
						<view class="device-location">{{ device.location }}</view>
					</view>
					<view class="energy-info">
						<view class="energy-value">{{ device.energy }}kWh</view>
						<view class="energy-cost">¥{{ device.cost }}</view>
					</view>
					<view class="energy-bar">
						<view class="bar-bg">
							<view class="bar-fill" :style="{ width: device.percentage + '%' }"></view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 历史记录 -->
		<view class="history-card">
			<view class="card-header">
				<view class="card-title">历史记录</view>
				<view class="view-more-btn" @tap="viewMoreHistory">
					<text>查看更多</text>
				</view>
			</view>
			
			<!-- 骨架屏 -->
			<view v-if="loading && historyRecords.length === 0" class="skeleton-wrapper">
				<SkeletonScreen
					type="list-item"
					:count="4"
					:animated="true"
				/>
			</view>
			
			<!-- 实际内容 -->
			<view v-else class="history-list">
				<view class="history-item" v-for="record in historyRecords" :key="record.id">
					<view class="history-date">
						<view class="date-day">{{ record.day }}</view>
						<view class="date-month">{{ record.month }}</view>
					</view>
					<view class="history-content">
						<view class="history-title">{{ record.title }}</view>
						<view class="history-desc">{{ record.description }}</view>
						<view class="history-stats">
							<text class="stat-item">总耗电: {{ record.totalEnergy }}kWh</text>
							<text class="stat-item">费用: ¥{{ record.totalCost }}</text>
							<text class="stat-item">节能: {{ record.savingRate }}%</text>
						</view>
					</view>
					<view class="history-action" @tap="viewHistoryDetail(record)">
						<text>详情</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { 
	getEnergyOverview, 
	getTodayStats, 
	getRealtimeData, 
	getChartData, 
	getDeviceRanking, 
	getHistoryRecords, 
	applyEnergyTip 
} from '@/api/energy'
import SkeletonScreen from '@/components/SkeletonScreen.vue'

export default {
	components: {
		SkeletonScreen
	},
	data() {
		return {
			loading: false,
			currentChartTab: 'today',
			currentRankingPeriod: 'today',
			realtimeTimer: null,
			
			// 实时功耗数据
			realtimeData: {
				currentPower: 0,
				trendIcon: '📈',
				trendText: '加载中...',
				trendClass: 'trend-up'
			},
			
			// 功耗统计
			powerStats: [],
			
			// 今日统计
			todayStats: [],
			
			// 图表标签页
			chartTabs: [
				{ key: 'today', label: '今日' },
				{ key: 'week', label: '本周' },
				{ key: 'month', label: '本月' }
			],
			
			// 图表数据
			chartData: [],
			
			// 图表标签
			chartLabels: [],
			
			// 图表汇总
			chartSummary: {
				average: 0,
				peak: 0,
				total: 0
			},
			
			// 节能数据
			savingData: {
				percentage: 0,
				savedEnergy: 0,
				savedCost: 0,
				carbonReduction: 0
			},
			
			// 节能建议
			energyTips: [],
			
			// 排行周期
			rankingPeriods: [
				{ key: 'today', label: '今日' },
				{ key: 'week', label: '本周' },
				{ key: 'month', label: '本月' }
			],
			
			// 设备能耗排行
			deviceRanking: [],
			
			// 历史记录
			historyRecords: []
		}
	},
	
	computed: {
		// 今日日期
		todayDate() {
			const today = new Date()
			const month = today.getMonth() + 1
			const date = today.getDate()
			return `${month}月${date}日`
		},
		
		// 节能进度
		savingProgress() {
			const percentage = this.savingData.percentage
			let gradient = ''
			
			if (percentage >= 30) {
				gradient = 'conic-gradient(#52c41a 0deg, #52c41a ' + (percentage * 3.6) + 'deg, #f0f0f0 ' + (percentage * 3.6) + 'deg)'
			} else if (percentage >= 20) {
				gradient = 'conic-gradient(#1890ff 0deg, #1890ff ' + (percentage * 3.6) + 'deg, #f0f0f0 ' + (percentage * 3.6) + 'deg)'
			} else {
				gradient = 'conic-gradient(#faad14 0deg, #faad14 ' + (percentage * 3.6) + 'deg, #f0f0f0 ' + (percentage * 3.6) + 'deg)'
			}
			
			return { gradient }
		}
	},
	
	onLoad() {
		this.loadEnergyData()
		this.startRealtimeUpdate()
	},
	
	onShow() {
		// 页面显示时刷新数据
		this.refreshRealtime()
	},
	
	onHide() {
		// 页面隐藏时停止实时更新
		this.stopRealtimeUpdate()
	},
	
	onUnload() {
		// 页面卸载时清理定时器
		this.stopRealtimeUpdate()
	},
	
	methods: {
		// 加载能耗数据
		async loadEnergyData() {
			try {
				uni.showLoading({ title: '加载中...' })
				this.loading = true
				
				// 并行获取数据
				const [overviewData, todayData, rankingData, historyData] = await Promise.all([
					getEnergyOverview(),
					getTodayStats(),
					getDeviceRanking('today'),
					getHistoryRecords()
				])
				
				// 更新实时数据
				this.realtimeData = {
					currentPower: overviewData.currentPower || 0,
					trendIcon: overviewData.trend > 0 ? '📈' : overviewData.trend < 0 ? '📉' : '➡️',
					trendText: overviewData.trendText || '数据稳定',
					trendClass: overviewData.trend > 0 ? 'trend-up' : overviewData.trend < 0 ? 'trend-down' : 'trend-stable'
				}
				
				// 更新功耗统计
				this.powerStats = [
					{
						key: 'peak',
						label: '峰值功耗',
						value: `${overviewData.peakPower || 0}W`,
						color: '#ff4d4f'
					},
					{
						key: 'average',
						label: '平均功耗',
						value: `${overviewData.averagePower || 0}W`,
						color: '#1890ff'
					},
					{
						key: 'minimum',
						label: '最低功耗',
						value: `${overviewData.minimumPower || 0}W`,
						color: '#52c41a'
					}
				]
				
				// 更新今日统计
				this.todayStats = [
					{
						key: 'energy',
						icon: '⚡',
						iconClass: 'icon-energy',
						value: todayData.totalEnergy || '0',
						label: '耗电量(kWh)',
						change: todayData.energyChange || '0%',
						changeClass: (todayData.energyChange && todayData.energyChange.startsWith('+')) ? 'change-up' : 'change-down'
					},
					{
						key: 'cost',
						icon: '💰',
						iconClass: 'icon-cost',
						value: todayData.totalCost || '0',
						label: '电费(元)',
						change: todayData.costChange || '0%',
						changeClass: (todayData.costChange && todayData.costChange.startsWith('+')) ? 'change-up' : 'change-down'
					},
					{
						key: 'devices',
						icon: '💡',
						iconClass: 'icon-devices',
						value: todayData.activeDevices || '0',
						label: '活跃设备',
						change: todayData.devicesChange || '0',
						changeClass: (todayData.devicesChange && !todayData.devicesChange.startsWith('-')) ? 'change-up' : 'change-down'
					},
					{
						key: 'efficiency',
						icon: '🍃',
						iconClass: 'icon-efficiency',
						value: todayData.efficiency || '0',
						label: '能效比(%)',
						change: todayData.efficiencyChange || '0%',
						changeClass: (todayData.efficiencyChange && todayData.efficiencyChange.startsWith('+')) ? 'change-up' : 'change-down'
					}
				]
				
				// 更新设备排行
				this.deviceRanking = rankingData.devices || []
				
				// 更新历史记录
				this.historyRecords = historyData.records || []
				
				this.loading = false
				uni.hideLoading()
			} catch (error) {
				console.error('加载能耗数据失败:', error)
				this.loading = false
			uni.hideLoading()
			uni.showToast({
				title: error.message || '数据加载失败',
				icon: 'error'
			})
		}
		},
		
		// 开始实时更新
		startRealtimeUpdate() {
			this.realtimeTimer = setInterval(() => {
				this.updateRealtimeData()
			}, 5000) // 每5秒更新一次
		},
		
		// 停止实时更新
		stopRealtimeUpdate() {
			if (this.realtimeTimer) {
				clearInterval(this.realtimeTimer)
				this.realtimeTimer = null
			}
		},
		
		// 更新实时数据
		async updateRealtimeData() {
			try {
				const data = await getRealtimeData()
				
				this.realtimeData = {
					currentPower: data.currentPower || 0,
					trendIcon: data.trend > 0 ? '📈' : data.trend < 0 ? '📉' : '➡️',
					trendText: data.trendText || '数据稳定',
					trendClass: data.trend > 0 ? 'trend-up' : data.trend < 0 ? 'trend-down' : 'trend-stable'
				}
			} catch (error) {
			console.error('更新实时数据失败:', error)
			uni.showToast({
				title: error.message || '更新失败',
				icon: 'error'
			})
		}
		},
		
		// 刷新实时数据
		async refreshRealtime() {
			try {
				// 更新实时数据
				await this.updateRealtimeData()
				
				uni.showToast({
					title: '数据已刷新',
					icon: 'success'
				})
			} catch (error) {
		console.error('刷新实时数据失败:', error)
		uni.showToast({
			title: error.message || '刷新失败',
			icon: 'error'
		})
	}
		},
		
		// 切换图表标签页
		switchChartTab(tab) {
			this.currentChartTab = tab
			this.loadChartData(tab)
		},
		
		// 加载图表数据
		async loadChartData(period) {
			try {
				uni.showLoading({ title: '加载图表...' })
				this.loading = true
				
				// 调用API获取图表数据
				const chartData = await getChartData(period)
				
				// 更新图表标签
				this.chartLabels = chartData.labels || []
				
				// 更新图表数据
				this.chartData = chartData.data.map(item => {
					const value = item.value || 0
					const maxValue = Math.max(...chartData.data.map(d => d.value || 0))
					const height = maxValue > 0 ? (value / maxValue * 80 + 20) : 20
					
					let color = '#1890ff'
					if (item.status === 'high') color = '#ff4d4f'
					else if (item.status === 'low') color = '#52c41a'
					
					return {
						value: value + 'W',
						height,
						color
					}
				})
				
				// 更新汇总数据
				this.chartSummary = {
					average: chartData.summary?.average || 0,
					peak: chartData.summary?.peak || 0,
					total: chartData.summary?.total || 0
				}
				
				this.loading = false
				uni.hideLoading()
			} catch (error) {
				console.error('加载图表数据失败:', error)
				this.loading = false
			uni.hideLoading()
			uni.showToast({
				title: error.message || '图表数据加载失败',
				icon: 'error'
			})
		}
		},
		
		// 应用节能建议
		async applyTip(tip) {
			try {
				this.loading = true
				
				// 调用API应用节能建议
				const result = await applyEnergyTip(tip.id)
				
				this.loading = false
				uni.showToast({
					title: result.message || `节能建议已应用，预计节能${tip.benefit}`,
					icon: 'success'
				})
				
				// 刷新数据
				await this.loadEnergyData()
			} catch (error) {
		console.error('应用节能建议失败:', error)
		this.loading = false
		uni.showToast({
			title: error.message || '应用失败',
			icon: 'error'
		})
	}
		},
		
		// 切换排行周期
		switchRankingPeriod(period) {
			this.currentRankingPeriod = period
			this.loadRankingData(period)
		},
		
		// 加载排行数据
		async loadRankingData(period) {
			try {
				// 调用API获取排行数据
				const rankingData = await getDeviceRanking(period)
				
				// 更新设备排行数据
				this.deviceRanking = rankingData.devices || []
				
				// 计算百分比
				const maxEnergy = Math.max(...this.deviceRanking.map(d => d.energy || 0))
				if (maxEnergy > 0) {
					this.deviceRanking.forEach(device => {
						device.percentage = Math.round((device.energy || 0) / maxEnergy * 100)
					})
				}
			} catch (error) {
			console.error('加载排行数据失败:', error)
			uni.showToast({
				title: error.message || '加载排行数据失败',
				icon: 'error'
			})
		}
		},
		
		// 获取排行样式类
		getRankingClass(index) {
			if (index === 0) return 'rank-first'
			if (index === 1) return 'rank-second'
			if (index === 2) return 'rank-third'
			return 'rank-normal'
		},
		
		// 查看更多历史记录
		viewMoreHistory() {
			uni.navigateTo({
				url: '/pages/energy/history'
			})
		},
		
		// 查看历史详情
		viewHistoryDetail(record) {
			uni.navigateTo({
				url: `/pages/energy/detail?id=${record.id}`
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.energy-container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

// 实时功耗卡片
.realtime-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.card-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.refresh-btn {
	display: flex;
	align-items: center;
	padding: 8rpx 16rpx;
	background-color: #f0f0f0;
	color: #666666;
	border-radius: 8rpx;
	font-size: 24rpx;
	gap: 8rpx;
}

.refresh-icon {
	font-size: 20rpx;
}

.power-display {
	display: flex;
	align-items: baseline;
	justify-content: center;
	margin-bottom: 32rpx;
}

.current-power {
	display: flex;
	align-items: baseline;
}

.power-value {
	font-size: 80rpx;
	font-weight: 700;
	color: #1890ff;
	margin-right: 8rpx;
}

.power-unit {
	font-size: 32rpx;
	color: #666666;
	margin-right: 24rpx;
}

.power-trend {
	display: flex;
	align-items: center;
	gap: 8rpx;
	font-size: 24rpx;
}

.trend-up {
	color: #ff4d4f;
}

.trend-down {
	color: #52c41a;
}

.trend-stable {
	color: #666666;
}

.power-stats {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 16rpx;
}

.stat-item {
	text-align: center;
	padding: 16rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
}

.stat-label {
	font-size: 22rpx;
	color: #999999;
	margin-bottom: 8rpx;
}

.stat-value {
	font-size: 28rpx;
	font-weight: 600;
}

// 今日统计卡片
.today-stats-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.date-info {
	font-size: 24rpx;
	color: #666666;
}

.stats-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 16rpx;
	margin-top: 24rpx;
}

.stat-card {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 12rpx;
}

.stat-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
	font-size: 24rpx;
}

.icon-energy {
	background-color: #fff7e6;
}

.icon-cost {
	background-color: #f6ffed;
}

.icon-devices {
	background-color: #e6f7ff;
}

.icon-efficiency {
	background-color: #fff2f0;
}

.stat-content {
	flex: 1;
}

.stat-number {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 4rpx;
}

.stat-desc {
	font-size: 22rpx;
	color: #666666;
	margin-bottom: 4rpx;
}

.stat-change {
	font-size: 20rpx;
	font-weight: 500;
}

.change-up {
	color: #ff4d4f;
}

.change-down {
	color: #52c41a;
}

// 图表卡片
.chart-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.chart-tabs {
	display: flex;
	gap: 16rpx;
	
	text {
		padding: 8rpx 16rpx;
		background-color: #f0f0f0;
		color: #666666;
		border-radius: 8rpx;
		font-size: 24rpx;
		transition: all 0.3s;
	}
	
	.active {
		background-color: #1890ff;
		color: #ffffff;
	}
}

.chart-container {
	margin-top: 24rpx;
}

.chart-placeholder {
	padding: 24rpx 0;
}

.chart-bars {
	display: flex;
	align-items: end;
	justify-content: space-between;
	height: 300rpx;
	margin-bottom: 16rpx;
	padding: 0 16rpx;
}

.chart-bar {
	flex: 1;
	margin: 0 4rpx;
	border-radius: 4rpx 4rpx 0 0;
	position: relative;
	transition: all 0.3s;
	display: flex;
	align-items: flex-start;
	justify-content: center;
	padding-top: 8rpx;
}

.bar-value {
	font-size: 20rpx;
	color: #ffffff;
	font-weight: 500;
}

.chart-labels {
	display: flex;
	justify-content: space-between;
	padding: 0 20rpx;
	
	text {
		font-size: 22rpx;
		color: #666666;
	}
}

.chart-summary {
	display: flex;
	justify-content: space-around;
	margin-top: 24rpx;
	padding-top: 24rpx;
	border-top: 1rpx solid #f0f0f0;
}

.summary-item {
	text-align: center;
}

.summary-label {
	font-size: 22rpx;
	color: #999999;
	margin-bottom: 8rpx;
}

.summary-value {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
}

// 节能分析卡片
.energy-saving-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.analysis-period {
	font-size: 24rpx;
	color: #666666;
}

.saving-overview {
	display: flex;
	align-items: center;
	margin: 24rpx 0;
}

.saving-circle {
	margin-right: 32rpx;
}

.circle-progress {
	width: 160rpx;
	height: 160rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.circle-inner {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	background-color: #ffffff;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.saving-percentage {
	font-size: 36rpx;
	font-weight: 700;
	color: #52c41a;
	margin-bottom: 4rpx;
}

.saving-label {
	font-size: 20rpx;
	color: #666666;
}

.saving-details {
	flex: 1;
}

.detail-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.detail-label {
	font-size: 26rpx;
	color: #666666;
}

.detail-value {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
}

.saving-tips {
	margin-top: 24rpx;
	padding-top: 24rpx;
	border-top: 1rpx solid #f0f0f0;
}

.tips-header {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
}

.tips-icon {
	font-size: 24rpx;
	margin-right: 8rpx;
}

.tips-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
}

.tips-list {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.tip-item {
	display: flex;
	align-items: center;
	padding: 16rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
}

.tip-icon {
	font-size: 24rpx;
	margin-right: 12rpx;
}

.tip-content {
	flex: 1;
}

.tip-text {
	font-size: 24rpx;
	color: #333333;
	margin-bottom: 4rpx;
}

.tip-benefit {
	font-size: 20rpx;
	color: #52c41a;
	font-weight: 500;
}

.tip-action {
	padding: 8rpx 16rpx;
	background-color: #1890ff;
	color: #ffffff;
	border-radius: 6rpx;
	font-size: 22rpx;
}

// 设备排行卡片
.device-ranking-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.ranking-period {
	display: flex;
	gap: 16rpx;
	
	text {
		padding: 6rpx 12rpx;
		background-color: #f0f0f0;
		color: #666666;
		border-radius: 6rpx;
		font-size: 22rpx;
		transition: all 0.3s;
	}
	
	.active {
		background-color: #1890ff;
		color: #ffffff;
	}
}

.ranking-list {
	margin-top: 24rpx;
}

.ranking-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	
	&:last-child {
		border-bottom: none;
	}
}

.ranking-number {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	font-weight: 600;
	margin-right: 16rpx;
}

.rank-first {
	background-color: #ffd700;
	color: #ffffff;
}

.rank-second {
	background-color: #c0c0c0;
	color: #ffffff;
}

.rank-third {
	background-color: #cd7f32;
	color: #ffffff;
}

.rank-normal {
	background-color: #f0f0f0;
	color: #666666;
}

.device-info {
	flex: 1;
	margin-right: 16rpx;
}

.device-name {
	font-size: 26rpx;
	font-weight: 500;
	color: #333333;
	margin-bottom: 4rpx;
}

.device-location {
	font-size: 22rpx;
	color: #999999;
}

.energy-info {
	text-align: right;
	margin-right: 16rpx;
}

.energy-value {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 4rpx;
}

.energy-cost {
	font-size: 22rpx;
	color: #666666;
}

.energy-bar {
	width: 80rpx;
}

.bar-bg {
	width: 100%;
	height: 8rpx;
	background-color: #f0f0f0;
	border-radius: 4rpx;
	overflow: hidden;
}

.bar-fill {
	height: 100%;
	background: linear-gradient(90deg, #52c41a, #1890ff);
	border-radius: 4rpx;
	transition: width 0.3s;
}

// 历史记录卡片
.history-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.view-more-btn {
	padding: 8rpx 16rpx;
	background-color: #f0f0f0;
	color: #666666;
	border-radius: 8rpx;
	font-size: 24rpx;
}

.history-list {
	margin-top: 24rpx;
}

.history-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	
	&:last-child {
		border-bottom: none;
	}
}

.history-date {
	width: 80rpx;
	text-align: center;
	margin-right: 16rpx;
}

.date-day {
	font-size: 32rpx;
	font-weight: 600;
	color: #1890ff;
	margin-bottom: 4rpx;
}

.date-month {
	font-size: 20rpx;
	color: #999999;
}

.history-content {
	flex: 1;
	margin-right: 16rpx;
}

.history-title {
	font-size: 26rpx;
	font-weight: 500;
	color: #333333;
	margin-bottom: 8rpx;
}

.history-desc {
	font-size: 22rpx;
	color: #666666;
	margin-bottom: 8rpx;
}

.history-stats {
	display: flex;
	gap: 16rpx;
	flex-wrap: wrap;
}

.stat-item {
	font-size: 20rpx;
	color: #999999;
}

.history-action {
	padding: 8rpx 16rpx;
	background-color: #1890ff;
	color: #ffffff;
	border-radius: 6rpx;
	font-size: 22rpx;
}

// 骨架屏样式
.skeleton-wrapper {
	padding: 20rpx 0;
}

// 响应式设计
@media screen and (min-width: 768px) {
	.energy-container {
		padding: 40rpx;
		max-width: 1200rpx;
		margin: 0 auto;
	}
	
	.stats-grid {
		grid-template-columns: repeat(4, 1fr);
	}
	
	.saving-overview {
		justify-content: space-around;
	}
	
	.saving-details {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 24rpx;
	}
	
	.detail-item {
		flex-direction: column;
		text-align: center;
		margin-bottom: 0;
	}
}
</style>