<template>
	<div class="device-group-container">
		<el-dialog
			v-model="visible"
			title="设备分组管理"
			width="1200px"
			:before-close="handleClose"
			destroy-on-close
		>
			<div class="group-management">
				<div class="group-sidebar">
					<div class="sidebar-header">
						<h4>设备分组</h4>
						<el-button type="primary" size="small" @click="showAddGroupDialog">
							<el-icon><ele-Plus /></el-icon>
							新建分组
						</el-button>
					</div>
					<div class="group-list">
						<div 
							v-for="group in groupList" 
							:key="group.id"
							class="group-item"
							:class="{ active: selectedGroupId === group.id }"
							@click="selectGroup(group)"
						>
							<div class="group-info">
								<div class="group-name">{{ group.groupName }}</div>
								<div class="group-count">{{ group.deviceCount }}台设备</div>
							</div>
							<div class="group-actions">
								<el-button type="text" size="small" @click.stop="editGroup(group)">
									<el-icon><ele-Edit /></el-icon>
								</el-button>
								<el-button type="text" size="small" @click.stop="deleteGroup(group)">
									<el-icon><ele-Delete /></el-icon>
								</el-button>
							</div>
						</div>
					</div>
				</div>
				
				<div class="group-content">
					<div class="content-header">
						<div class="header-info">
							<h4>{{ selectedGroup?.groupName || '请选择分组' }}</h4>
							<span v-if="selectedGroup" class="device-count">共{{ selectedGroup.deviceCount }}台设备</span>
						</div>
						<div class="header-actions" v-if="selectedGroup">
							<el-button type="primary" size="small" @click="showAddDeviceDialog">
								<el-icon><ele-Plus /></el-icon>
								添加设备
							</el-button>
							<el-button type="success" size="small" @click="batchControl">
								<el-icon><ele-Setting /></el-icon>
								批量控制
							</el-button>
						</div>
					</div>
					
					<div class="device-table" v-if="selectedGroup">
						<el-table
							v-loading="deviceLoading"
							:data="groupDeviceList"
							row-key="id"
							border
							stripe
							style="width: 100%"
							@selection-change="handleSelectionChange"
						>
							<el-table-column type="selection" width="55" />
							<el-table-column prop="deviceName" label="设备名称" min-width="120" show-overflow-tooltip />
							<el-table-column prop="deviceCode" label="设备编号" min-width="120" show-overflow-tooltip />
							<el-table-column prop="deviceType" label="设备类型" width="100" align="center" />
							<el-table-column prop="location" label="安装位置" min-width="120" show-overflow-tooltip />
							<el-table-column prop="status" label="状态" width="100" align="center">
								<template #default="{ row }">
									<el-tag :type="getStatusType(row.status)">{{ getStatusText(row.status) }}</el-tag>
								</template>
							</el-table-column>
							<el-table-column prop="isOn" label="开关" width="80" align="center">
								<template #default="{ row }">
									<el-switch v-model="row.isOn" @change="controlDevice(row)" />
								</template>
							</el-table-column>
							<el-table-column prop="brightness" label="亮度" width="120" align="center">
								<template #default="{ row }">
									<el-slider v-model="row.brightness" :min="0" :max="100" :step="5" style="width: 80px" @change="controlDevice(row)" />
								</template>
							</el-table-column>
							<el-table-column label="操作" width="120" align="center">
								<template #default="{ row }">
									<el-button link type="danger" size="small" @click="removeDeviceFromGroup(row)">
										移除
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</div>
					
					<div class="empty-state" v-else>
						<el-empty description="请选择一个分组查看设备" />
					</div>
				</div>
			</div>
		</el-dialog>
		
		<!-- 新建/编辑分组对话框 -->
		<el-dialog
			v-model="groupDialogVisible"
			:title="groupDialogTitle"
			width="500px"
			destroy-on-close
		>
			<el-form ref="groupFormRef" :model="groupForm" :rules="groupRules" label-width="100px">
				<el-form-item label="分组名称" prop="groupName">
					<el-input v-model="groupForm.groupName" placeholder="请输入分组名称" clearable />
				</el-form-item>
				<el-form-item label="分组描述" prop="description">
					<el-input v-model="groupForm.description" type="textarea" :rows="3" placeholder="请输入分组描述" />
				</el-form-item>
				<el-form-item label="分组颜色" prop="color">
					<el-color-picker v-model="groupForm.color" show-alpha :predefine="predefineColors" />
				</el-form-item>
			</el-form>
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="groupDialogVisible = false">取消</el-button>
					<el-button type="primary" @click="saveGroup" :loading="groupSaving">确定</el-button>
				</div>
			</template>
		</el-dialog>
		
		<!-- 添加设备到分组对话框 -->
		<el-dialog
			v-model="addDeviceDialogVisible"
			title="添加设备到分组"
			width="800px"
			destroy-on-close
		>
			<div class="add-device-content">
				<div class="search-bar">
					<el-input
						v-model="deviceSearchKeyword"
						placeholder="搜索设备名称或编号"
						clearable
						style="width: 300px"
						@input="searchAvailableDevices"
					>
						<template #prefix>
							<el-icon><ele-Search /></el-icon>
						</template>
					</el-input>
				</div>
				<el-table
					v-loading="availableDeviceLoading"
					:data="availableDeviceList"
					row-key="id"
					border
					stripe
					style="width: 100%; margin-top: 20px"
					@selection-change="handleAvailableDeviceSelection"
				>
					<el-table-column type="selection" width="55" />
					<el-table-column prop="deviceName" label="设备名称" min-width="120" show-overflow-tooltip />
					<el-table-column prop="deviceCode" label="设备编号" min-width="120" show-overflow-tooltip />
					<el-table-column prop="deviceType" label="设备类型" width="100" align="center" />
					<el-table-column prop="location" label="安装位置" min-width="120" show-overflow-tooltip />
					<el-table-column prop="status" label="状态" width="100" align="center">
						<template #default="{ row }">
							<el-tag :type="getStatusType(row.status)">{{ getStatusText(row.status) }}</el-tag>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="addDeviceDialogVisible = false">取消</el-button>
					<el-button type="primary" @click="addDevicesToGroup" :loading="addingDevices" :disabled="selectedAvailableDevices.length === 0">
						添加设备 ({{ selectedAvailableDevices.length }})
					</el-button>
				</div>
			</template>
		</el-dialog>
		
		<!-- 批量控制对话框 -->
		<el-dialog
			v-model="batchControlDialogVisible"
			title="批量控制设备"
			width="500px"
			destroy-on-close
		>
			<el-form ref="batchControlFormRef" :model="batchControlForm" label-width="100px">
				<el-form-item label="控制范围">
					<el-radio-group v-model="batchControlForm.scope">
						<el-radio label="selected">选中设备 ({{ selectedDevices.length }}台)</el-radio>
						<el-radio label="all">分组所有设备 ({{ groupDeviceList.length }}台)</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item label="开关控制">
					<el-radio-group v-model="batchControlForm.switchAction">
						<el-radio label="on">全部开启</el-radio>
						<el-radio label="off">全部关闭</el-radio>
						<el-radio label="none">不改变</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item label="亮度设置" v-if="batchControlForm.switchAction !== 'off'">
					<el-checkbox v-model="batchControlForm.setBrightness" label="设置亮度" />
					<el-slider 
						v-if="batchControlForm.setBrightness"
						v-model="batchControlForm.brightness" 
						:min="0" 
						:max="100" 
						:step="5" 
						show-input 
						style="margin-top: 10px"
					/>
				</el-form-item>
			</el-form>
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="batchControlDialogVisible = false">取消</el-button>
					<el-button type="primary" @click="executeBatchControl" :loading="batchControlling">执行控制</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getAPI } from '/@/utils/axios-utils';
import { useDeviceApi } from '/@/api-services/device';

// 组件属性
interface Props {
	title?: string;
}

const props = withDefaults(defineProps<Props>(), {
	title: '设备分组管理'
});

// 组件事件
const emit = defineEmits(['refresh']);

// 响应式数据
const visible = ref(false);
const groupList = ref<any[]>([]);
const selectedGroupId = ref<number | null>(null);
const selectedGroup = ref<any>(null);
const groupDeviceList = ref<any[]>([]);
const deviceLoading = ref(false);
const selectedDevices = ref<any[]>([]);

// 分组对话框
const groupDialogVisible = ref(false);
const groupDialogTitle = ref('');
const groupSaving = ref(false);
const groupFormRef = ref();
const groupForm = reactive({
	id: undefined as number | undefined,
	groupName: '',
	description: '',
	color: '#409EFF'
});

const groupRules = {
	groupName: [{ required: true, message: '请输入分组名称', trigger: 'blur' }]
};

const predefineColors = [
	'#ff4500',
	'#ff8c00',
	'#ffd700',
	'#90ee90',
	'#00ced1',
	'#1e90ff',
	'#c71585',
	'#409EFF',
	'#67C23A',
	'#E6A23C',
	'#F56C6C',
	'#909399'
];

// 添加设备对话框
const addDeviceDialogVisible = ref(false);
const availableDeviceList = ref<any[]>([]);
const availableDeviceLoading = ref(false);
const deviceSearchKeyword = ref('');
const selectedAvailableDevices = ref<any[]>([]);
const addingDevices = ref(false);

// 批量控制对话框
const batchControlDialogVisible = ref(false);
const batchControlling = ref(false);
const batchControlFormRef = ref();
const batchControlForm = reactive({
	scope: 'selected',
	switchAction: 'none',
	setBrightness: false,
	brightness: 80
});

// 打开对话框
const openDialog = () => {
	visible.value = true;
	loadGroupList();
};

// 关闭对话框
const handleClose = () => {
	visible.value = false;
	resetData();
};

// 重置数据
const resetData = () => {
	selectedGroupId.value = null;
	selectedGroup.value = null;
	groupDeviceList.value = [];
	selectedDevices.value = [];
};

// 加载分组列表
const loadGroupList = async () => {
	try {
		// 这里应该调用实际的API
		// 使用新的模块化API获取设备组列表
		// groupList.value = response.data.result || [];
		
		// 模拟数据
		groupList.value = [
			{ id: 1, groupName: '办公区域', description: '办公室照明设备', color: '#409EFF', deviceCount: 12 },
			{ id: 2, groupName: '会议室', description: '会议室照明设备', color: '#67C23A', deviceCount: 8 },
			{ id: 3, groupName: '走廊通道', description: '走廊和通道照明', color: '#E6A23C', deviceCount: 15 },
			{ id: 4, groupName: '停车场', description: '停车场照明设备', color: '#F56C6C', deviceCount: 20 }
		];
	} catch (error) {
		console.error('加载分组列表失败:', error);
		ElMessage.error('加载分组列表失败');
	}
};

// 选择分组
const selectGroup = (group: any) => {
	selectedGroupId.value = group.id;
	selectedGroup.value = group;
	loadGroupDevices(group.id);
};

// 加载分组设备
const loadGroupDevices = async (groupId: number) => {
	deviceLoading.value = true;
	try {
		// 这里应该调用实际的API
		// 使用新的模块化API获取组内设备
		// groupDeviceList.value = response.data.result || [];
		
		// 模拟数据
		groupDeviceList.value = [
			{
				id: 1,
				deviceName: 'LED-001',
				deviceCode: 'LED001',
				deviceType: 'LED',
				location: '办公室A-001',
				status: 1,
				isOn: true,
				brightness: 80
			},
			{
				id: 2,
				deviceName: 'LED-002',
				deviceCode: 'LED002',
				deviceType: 'LED',
				location: '办公室A-002',
				status: 1,
				isOn: false,
				brightness: 60
			}
		];
	} catch (error) {
		console.error('加载分组设备失败:', error);
		ElMessage.error('加载分组设备失败');
	} finally {
		deviceLoading.value = false;
	}
};

// 显示新建分组对话框
const showAddGroupDialog = () => {
	groupDialogTitle.value = '新建分组';
	Object.assign(groupForm, {
		id: undefined,
		groupName: '',
		description: '',
		color: '#409EFF'
	});
	groupDialogVisible.value = true;
};

// 编辑分组
const editGroup = (group: any) => {
	groupDialogTitle.value = '编辑分组';
	Object.assign(groupForm, {
		id: group.id,
		groupName: group.groupName,
		description: group.description,
		color: group.color
	});
	groupDialogVisible.value = true;
};

// 保存分组
const saveGroup = async () => {
	if (!groupFormRef.value) return;
	
	try {
		await groupFormRef.value.validate();
		groupSaving.value = true;
		
		// 这里应该调用实际的API
		if (groupForm.id) {
			// // 使用新的模块化API更新设备组
		} else {
			// 使用新的模块化API添加设备组
			ElMessage.success('分组创建成功');
		}
		
		groupDialogVisible.value = false;
		loadGroupList();
	} catch (error) {
		console.error('保存分组失败:', error);
		ElMessage.error('保存分组失败');
	} finally {
		groupSaving.value = false;
	}
};

// 删除分组
const deleteGroup = async (group: any) => {
	try {
		await ElMessageBox.confirm(
			`确定删除分组"${group.groupName}"？删除后该分组下的设备将移至未分组。`,
			'删除分组',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}
		);
		
		// 这里应该调用实际的API
		// await getAPI(EnergyDeviceApi).apiEnergyDeviceGroupDeletePost({ id: group.id });
		
		ElMessage.success('分组删除成功');
		loadGroupList();
		
		// 如果删除的是当前选中的分组，清空选择
		if (selectedGroupId.value === group.id) {
			resetData();
		}
	} catch (error: any) {
		if (error !== 'cancel') {
			console.error('删除分组失败:', error);
			ElMessage.error('删除分组失败');
		}
	}
};

// 显示添加设备对话框
const showAddDeviceDialog = () => {
	addDeviceDialogVisible.value = true;
	loadAvailableDevices();
};

// 加载可用设备
const loadAvailableDevices = async () => {
	availableDeviceLoading.value = true;
	try {
		// 这里应该调用实际的API获取未分组的设备
		// const response = await getAPI(EnergyDeviceApi).apiEnergyDeviceUngroupedGet();
		// availableDeviceList.value = response.data.result || [];
		
		// 模拟数据
		availableDeviceList.value = [
			{
				id: 10,
				deviceName: 'LED-010',
				deviceCode: 'LED010',
				deviceType: 'LED',
				location: '办公室B-001',
				status: 1
			},
			{
				id: 11,
				deviceName: 'LED-011',
				deviceCode: 'LED011',
				deviceType: 'LED',
				location: '办公室B-002',
				status: 1
			}
		];
	} catch (error) {
		console.error('加载可用设备失败:', error);
		ElMessage.error('加载可用设备失败');
	} finally {
		availableDeviceLoading.value = false;
	}
};

// 搜索可用设备
const searchAvailableDevices = () => {
	// 根据搜索关键词过滤设备
	if (deviceSearchKeyword.value.trim()) {
		// 这里可以实现搜索逻辑
	}
};

// 处理可用设备选择
const handleAvailableDeviceSelection = (selection: any[]) => {
	selectedAvailableDevices.value = selection;
};

// 添加设备到分组
const addDevicesToGroup = async () => {
	if (selectedAvailableDevices.value.length === 0) {
		ElMessage.warning('请选择要添加的设备');
		return;
	}
	
	addingDevices.value = true;
	try {
		const deviceIds = selectedAvailableDevices.value.map(device => device.id);
		
		// 这里应该调用实际的API
		// await getAPI(EnergyDeviceApi).apiEnergyDeviceGroupAddDevicesPost({
		//   groupId: selectedGroupId.value,
		//   deviceIds
		// });
		
		ElMessage.success(`成功添加${selectedAvailableDevices.value.length}台设备到分组`);
		addDeviceDialogVisible.value = false;
		loadGroupDevices(selectedGroupId.value!);
		loadGroupList(); // 刷新分组列表以更新设备数量
	} catch (error) {
		console.error('添加设备到分组失败:', error);
		ElMessage.error('添加设备到分组失败');
	} finally {
		addingDevices.value = false;
	}
};

// 从分组中移除设备
const removeDeviceFromGroup = async (device: any) => {
	try {
		await ElMessageBox.confirm(
			`确定将设备"${device.deviceName}"从分组中移除？`,
			'移除设备',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}
		);
		
		// 这里应该调用实际的API
		// await getAPI(EnergyDeviceApi).apiEnergyDeviceGroupRemoveDevicePost({
		//   groupId: selectedGroupId.value,
		//   deviceId: device.id
		// });
		
		ElMessage.success('设备移除成功');
		loadGroupDevices(selectedGroupId.value!);
		loadGroupList(); // 刷新分组列表以更新设备数量
	} catch (error: any) {
		if (error !== 'cancel') {
			console.error('移除设备失败:', error);
			ElMessage.error('移除设备失败');
		}
	}
};

// 控制设备
const controlDevice = async (device: any) => {
	try {
		// 这里应该调用实际的API
		// await getAPI(EnergyDeviceApi).apiEnergyDeviceControlPost({
		//   deviceId: device.id,
		//   action: device.isOn ? 'ON' : 'OFF',
		//   brightness: device.brightness
		// });
		
		ElMessage.success('设备控制成功');
	} catch (error) {
		console.error('控制设备失败:', error);
		ElMessage.error('控制设备失败');
	}
};

// 处理设备选择
const handleSelectionChange = (selection: any[]) => {
	selectedDevices.value = selection;
};

// 批量控制
const batchControl = () => {
	if (selectedDevices.value.length === 0 && batchControlForm.scope === 'selected') {
		ElMessage.warning('请先选择要控制的设备');
		return;
	}
	
	batchControlDialogVisible.value = true;
};

// 执行批量控制
const executeBatchControl = async () => {
	batchControlling.value = true;
	try {
		const targetDevices = batchControlForm.scope === 'selected' ? selectedDevices.value : groupDeviceList.value;
		const deviceIds = targetDevices.map(device => device.id);
		
		const controlData: any = {
			deviceIds,
			groupId: selectedGroupId.value
		};
		
		if (batchControlForm.switchAction !== 'none') {
			controlData.action = batchControlForm.switchAction.toUpperCase();
		}
		
		if (batchControlForm.setBrightness && batchControlForm.switchAction !== 'off') {
			controlData.brightness = batchControlForm.brightness;
		}
		
		// 这里应该调用实际的API
		// await getAPI(EnergyDeviceApi).apiEnergyDeviceBatchControlPost(controlData);
		
		ElMessage.success(`成功控制${targetDevices.length}台设备`);
		batchControlDialogVisible.value = false;
		loadGroupDevices(selectedGroupId.value!);
	} catch (error) {
		console.error('批量控制失败:', error);
		ElMessage.error('批量控制失败');
	} finally {
		batchControlling.value = false;
	}
};

// 获取状态类型
const getStatusType = (status: number) => {
	switch (status) {
		case 1: return 'success';
		case 2: return 'danger';
		case 3: return 'warning';
		case 4: return 'info';
		default: return 'info';
	}
};

// 获取状态文本
const getStatusText = (status: number) => {
	switch (status) {
		case 1: return '正常';
		case 2: return '故障';
		case 3: return '维护';
		case 4: return '停用';
		default: return '未知';
	}
};

// 暴露方法
defineExpose({
	openDialog
});
</script>

<style scoped lang="scss">
.device-group-container {
	:deep(.el-dialog__body) {
		padding: 0;
	}
}

.group-management {
	display: flex;
	height: 600px;
}

.group-sidebar {
	width: 300px;
	border-right: 1px solid #ebeef5;
	display: flex;
	flex-direction: column;
}

.sidebar-header {
	padding: 20px;
	border-bottom: 1px solid #ebeef5;
	display: flex;
	justify-content: space-between;
	align-items: center;
	
	h4 {
		margin: 0;
		font-size: 16px;
		color: #303133;
	}
}

.group-list {
	flex: 1;
	overflow-y: auto;
	padding: 10px;
}

.group-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12px;
	margin-bottom: 8px;
	border-radius: 6px;
	border: 1px solid #ebeef5;
	cursor: pointer;
	transition: all 0.3s ease;
	
	&:hover {
		background-color: #f5f7fa;
		border-color: #c0c4cc;
	}
	
	&.active {
		background-color: #ecf5ff;
		border-color: #409eff;
		
		.group-name {
			color: #409eff;
		}
	}
}

.group-info {
	flex: 1;
}

.group-name {
	font-size: 14px;
	font-weight: 500;
	color: #303133;
	margin-bottom: 4px;
}

.group-count {
	font-size: 12px;
	color: #909399;
}

.group-actions {
	display: flex;
	gap: 4px;
	opacity: 0;
	transition: opacity 0.3s ease;
	
	.group-item:hover & {
		opacity: 1;
	}
}

.group-content {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.content-header {
	padding: 20px;
	border-bottom: 1px solid #ebeef5;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.header-info {
	h4 {
		margin: 0 0 4px 0;
		font-size: 16px;
		color: #303133;
	}
}

.device-count {
	font-size: 12px;
	color: #909399;
}

.header-actions {
	display: flex;
	gap: 8px;
}

.device-table {
	flex: 1;
	padding: 20px;
	overflow: auto;
}

.empty-state {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
}

.add-device-content {
	.search-bar {
		margin-bottom: 20px;
	}
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 8px;
}

@media (max-width: 768px) {
	.group-management {
		flex-direction: column;
		height: auto;
	}
	
	.group-sidebar {
		width: 100%;
		border-right: none;
		border-bottom: 1px solid #ebeef5;
	}
	
	.group-list {
		max-height: 200px;
	}
}
</style>