/**
 * 移动端适配工具类
 * 提供移动端特有的交互逻辑和设备检测功能
 */

// 设备检测
export const isMobile = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

export const isTablet = () => {
  return /iPad|Android(?!.*Mobile)/i.test(navigator.userAgent)
}

export const isIOS = () => {
  return /iPad|iPhone|iPod/.test(navigator.userAgent)
}

export const isAndroid = () => {
  return /Android/i.test(navigator.userAgent)
}

// 屏幕尺寸检测
export const getScreenSize = () => {
  const width = window.innerWidth
  if (width <= 375) return 'xs'
  if (width <= 576) return 'sm'
  if (width <= 768) return 'md'
  if (width <= 1024) return 'lg'
  return 'xl'
}

// 触摸事件处理
export const addTouchSupport = (element, options = {}) => {
  if (!element || !isMobile()) return

  const {
    onTouchStart = () => {},
    onTouchMove = () => {},
    onTouchEnd = () => {},
    preventDefault = true
  } = options

  let startY = 0
  let startX = 0

  const handleTouchStart = (e) => {
    startY = e.touches[0].clientY
    startX = e.touches[0].clientX
    onTouchStart(e)
  }

  const handleTouchMove = (e) => {
    if (preventDefault) {
      e.preventDefault()
    }
    onTouchMove(e, {
      deltaY: e.touches[0].clientY - startY,
      deltaX: e.touches[0].clientX - startX
    })
  }

  const handleTouchEnd = (e) => {
    onTouchEnd(e)
  }

  element.addEventListener('touchstart', handleTouchStart, { passive: !preventDefault })
  element.addEventListener('touchmove', handleTouchMove, { passive: !preventDefault })
  element.addEventListener('touchend', handleTouchEnd, { passive: true })

  return () => {
    element.removeEventListener('touchstart', handleTouchStart)
    element.removeEventListener('touchmove', handleTouchMove)
    element.removeEventListener('touchend', handleTouchEnd)
  }
}

// 下拉刷新
export const addPullToRefresh = (container, onRefresh) => {
  if (!container || !isMobile()) return

  let startY = 0
  let isPulling = false
  let refreshThreshold = 60

  const pullIndicator = document.createElement('div')
  pullIndicator.className = 'pull-refresh-indicator'
  pullIndicator.innerHTML = '<i class="el-icon-refresh"></i> 下拉刷新'
  pullIndicator.style.cssText = `
    position: absolute;
    top: -60px;
    left: 0;
    right: 0;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    color: #666;
    font-size: 14px;
    transition: transform 0.3s;
    z-index: 1000;
  `
  container.style.position = 'relative'
  container.insertBefore(pullIndicator, container.firstChild)

  const handleTouchStart = (e) => {
    if (container.scrollTop === 0) {
      startY = e.touches[0].clientY
      isPulling = true
    }
  }

  const handleTouchMove = (e) => {
    if (!isPulling) return

    const deltaY = e.touches[0].clientY - startY
    if (deltaY > 0) {
      e.preventDefault()
      const pullDistance = Math.min(deltaY * 0.5, refreshThreshold)
      pullIndicator.style.transform = `translateY(${pullDistance}px)`
      
      if (pullDistance >= refreshThreshold) {
        pullIndicator.innerHTML = '<i class="el-icon-refresh"></i> 释放刷新'
        pullIndicator.style.color = '#409eff'
      } else {
        pullIndicator.innerHTML = '<i class="el-icon-refresh"></i> 下拉刷新'
        pullIndicator.style.color = '#666'
      }
    }
  }

  const handleTouchEnd = (e) => {
    if (!isPulling) return

    const deltaY = e.changedTouches[0].clientY - startY
    isPulling = false

    if (deltaY >= refreshThreshold) {
      pullIndicator.innerHTML = '<i class="el-icon-loading"></i> 刷新中...'
      pullIndicator.style.transform = `translateY(${refreshThreshold}px)`
      
      onRefresh().finally(() => {
        pullIndicator.style.transform = 'translateY(0)'
        pullIndicator.innerHTML = '<i class="el-icon-refresh"></i> 下拉刷新'
        pullIndicator.style.color = '#666'
      })
    } else {
      pullIndicator.style.transform = 'translateY(0)'
    }
  }

  container.addEventListener('touchstart', handleTouchStart, { passive: false })
  container.addEventListener('touchmove', handleTouchMove, { passive: false })
  container.addEventListener('touchend', handleTouchEnd, { passive: true })

  return () => {
    container.removeEventListener('touchstart', handleTouchStart)
    container.removeEventListener('touchmove', handleTouchMove)
    container.removeEventListener('touchend', handleTouchEnd)
    if (pullIndicator.parentNode) {
      pullIndicator.parentNode.removeChild(pullIndicator)
    }
  }
}

// 上拉加载更多
export const addLoadMore = (container, onLoadMore, options = {}) => {
  if (!container || !isMobile()) return

  const {
    threshold = 50,
    loadingText = '加载中...',
    noMoreText = '没有更多了'
  } = options

  let isLoading = false
  let hasMore = true

  const loadIndicator = document.createElement('div')
  loadIndicator.className = 'load-more-indicator'
  loadIndicator.style.cssText = `
    padding: 20px;
    text-align: center;
    color: #666;
    font-size: 14px;
    display: none;
  `
  container.appendChild(loadIndicator)

  const handleScroll = () => {
    if (isLoading || !hasMore) return

    const { scrollTop, scrollHeight, clientHeight } = container
    if (scrollTop + clientHeight >= scrollHeight - threshold) {
      isLoading = true
      loadIndicator.innerHTML = `<i class="el-icon-loading"></i> ${loadingText}`
      loadIndicator.style.display = 'block'

      onLoadMore().then((result) => {
        if (result === false) {
          hasMore = false
          loadIndicator.innerHTML = noMoreText
        } else {
          loadIndicator.style.display = 'none'
        }
      }).finally(() => {
        isLoading = false
      })
    }
  }

  container.addEventListener('scroll', handleScroll, { passive: true })

  return () => {
    container.removeEventListener('scroll', handleScroll)
    if (loadIndicator.parentNode) {
      loadIndicator.parentNode.removeChild(loadIndicator)
    }
  }
}

// 防抖函数（移动端优化）
export const debounce = (func, wait = 300) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 节流函数（移动端优化）
export const throttle = (func, limit = 100) => {
  let inThrottle
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 安全区域适配
export const getSafeAreaInsets = () => {
  const style = getComputedStyle(document.documentElement)
  return {
    top: parseInt(style.getPropertyValue('--safe-area-inset-top') || '0'),
    right: parseInt(style.getPropertyValue('--safe-area-inset-right') || '0'),
    bottom: parseInt(style.getPropertyValue('--safe-area-inset-bottom') || '0'),
    left: parseInt(style.getPropertyValue('--safe-area-inset-left') || '0')
  }
}

// 横屏检测
export const isLandscape = () => {
  return window.innerWidth > window.innerHeight
}

// 添加横屏监听
export const addOrientationListener = (callback) => {
  const handleOrientationChange = () => {
    setTimeout(() => {
      callback({
        isLandscape: isLandscape(),
        width: window.innerWidth,
        height: window.innerHeight
      })
    }, 100)
  }

  window.addEventListener('orientationchange', handleOrientationChange)
  window.addEventListener('resize', handleOrientationChange)

  return () => {
    window.removeEventListener('orientationchange', handleOrientationChange)
    window.removeEventListener('resize', handleOrientationChange)
  }
}

// 移动端表格优化
export const optimizeTableForMobile = (tableRef) => {
  if (!tableRef || !isMobile()) return

  const table = tableRef.$el || tableRef
  if (!table) return

  // 添加横向滚动
  table.style.overflowX = 'auto'
  table.style.webkitOverflowScrolling = 'touch'

  // 优化表格列宽
  const columns = table.querySelectorAll('th')
  columns.forEach(col => {
    if (!col.style.minWidth) {
      col.style.minWidth = '100px'
    }
  })

  return table
}

// 移动端对话框优化
export const optimizeDialogForMobile = (dialogRef) => {
  if (!dialogRef || !isMobile()) return

  const dialog = dialogRef.$el || dialogRef
  if (!dialog) return

  const dialogContent = dialog.querySelector('.el-dialog')
  if (dialogContent) {
    dialogContent.style.margin = '5vh auto'
    dialogContent.style.maxHeight = '90vh'
    dialogContent.style.overflow = 'auto'
  }

  return dialog
}

export default {
  isMobile,
  isTablet,
  isIOS,
  isAndroid,
  getScreenSize,
  addTouchSupport,
  addPullToRefresh,
  addLoadMore,
  debounce,
  throttle,
  getSafeAreaInsets,
  isLandscape,
  addOrientationListener,
  optimizeTableForMobile,
  optimizeDialogForMobile
}