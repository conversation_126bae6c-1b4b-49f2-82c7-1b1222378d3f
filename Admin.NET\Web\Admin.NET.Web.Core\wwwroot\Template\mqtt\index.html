<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MQTT设备管理</title>
    <link href="https://cdn.jsdelivr.net/npm/element-plus@2.4.4/dist/index.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@element-plus/icons-vue@2.1.0/lib/index.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        .full-width {
            grid-column: 1 / -1;
        }
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online {
            background-color: #67c23a;
        }
        .status-offline {
            background-color: #f56c6c;
        }
        .metric-card {
            text-align: center;
            padding: 20px;
            border: 1px solid #ebeef5;
            border-radius: 4px;
            margin: 10px;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #409eff;
        }
        .metric-label {
            color: #909399;
            margin-top: 8px;
        }
        .device-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
        }
        .device-card {
            border: 1px solid #ebeef5;
            border-radius: 4px;
            padding: 15px;
            background: #fafafa;
        }
        .device-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .device-name {
            font-weight: bold;
            color: #303133;
        }
        .device-info {
            font-size: 12px;
            color: #909399;
            margin: 5px 0;
        }
        .control-panel {
            margin-top: 15px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .log-container {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ebeef5;
            border-radius: 4px;
            padding: 10px;
            background: #fafafa;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-timestamp {
            color: #909399;
        }
        .log-level-info {
            color: #409eff;
        }
        .log-level-warn {
            color: #e6a23c;
        }
        .log-level-error {
            color: #f56c6c;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <!-- 头部状态 -->
            <div class="header">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <div class="metric-card">
                            <div class="metric-value">{{ connectionStats.isConnected ? '已连接' : '未连接' }}</div>
                            <div class="metric-label">
                                <span :class="connectionStats.isConnected ? 'status-online' : 'status-offline'" class="status-indicator"></span>
                                MQTT连接状态
                            </div>
                        </div>
                    </el-col>
                    <el-col :span="6">
                        <div class="metric-card">
                            <div class="metric-value">{{ deviceStats.totalDevices }}</div>
                            <div class="metric-label">设备总数</div>
                        </div>
                    </el-col>
                    <el-col :span="6">
                        <div class="metric-card">
                            <div class="metric-value">{{ deviceStats.onlineDevices }}</div>
                            <div class="metric-label">在线设备</div>
                        </div>
                    </el-col>
                    <el-col :span="6">
                        <div class="metric-card">
                            <div class="metric-value">{{ connectionStats.messagesReceived }}</div>
                            <div class="metric-label">接收消息数</div>
                        </div>
                    </el-col>
                </el-row>
                <el-row style="margin-top: 20px;">
                    <el-col :span="24">
                        <el-button type="primary" @click="refreshData" :loading="loading">刷新数据</el-button>
                        <el-button type="success" @click="reconnectMqtt" :loading="reconnecting">重新连接</el-button>
                        <el-button type="warning" @click="scanDevices" :loading="scanning">扫描设备</el-button>
                        <el-button type="info" @click="showLogs = !showLogs">{{ showLogs ? '隐藏日志' : '显示日志' }}</el-button>
                    </el-col>
                </el-row>
            </div>

            <div class="content">
                <!-- 设备列表 -->
                <div class="card">
                    <h3>设备列表</h3>
                    <el-input v-model="deviceFilter" placeholder="搜索设备..." style="margin-bottom: 15px;">
                        <template #prefix>
                            <el-icon><Search /></el-icon>
                        </template>
                    </el-input>
                    <div class="device-grid">
                        <div v-for="device in filteredDevices" :key="device.deviceKey" class="device-card">
                            <div class="device-header">
                                <span class="device-name">{{ device.deviceName || device.deviceKey }}</span>
                                <el-tag :type="device.isOnline ? 'success' : 'danger'" size="small">
                                    {{ device.isOnline ? '在线' : '离线' }}
                                </el-tag>
                            </div>
                            <div class="device-info">区域: {{ device.area }}</div>
                            <div class="device-info">地址: {{ device.address }}</div>
                            <div class="device-info">类型: {{ device.deviceType }}</div>
                            <div class="device-info">最后心跳: {{ formatTime(device.lastHeartbeat) }}</div>
                            <div class="control-panel">
                                <el-button size="small" type="primary" @click="getDeviceStatus(device)">获取状态</el-button>
                                <el-button size="small" type="success" @click="switchLight(device, true)">开灯</el-button>
                                <el-button size="small" type="warning" @click="switchLight(device, false)">关灯</el-button>
                                <el-button size="small" type="info" @click="showDeviceControl(device)">控制</el-button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 连接信息 -->
                <div class="card">
                    <h3>连接信息</h3>
                    <el-descriptions :column="1" border>
                        <el-descriptions-item label="服务器地址">{{ connectionStats.serverAddress }}</el-descriptions-item>
                        <el-descriptions-item label="客户端ID">{{ connectionStats.clientId }}</el-descriptions-item>
                        <el-descriptions-item label="连接时间">{{ formatTime(connectionStats.connectedAt) }}</el-descriptions-item>
                        <el-descriptions-item label="发送消息数">{{ connectionStats.messagesSent }}</el-descriptions-item>
                        <el-descriptions-item label="接收消息数">{{ connectionStats.messagesReceived }}</el-descriptions-item>
                        <el-descriptions-item label="重连次数">{{ connectionStats.reconnectCount }}</el-descriptions-item>
                    </el-descriptions>
                </div>
            </div>

            <!-- 日志面板 -->
            <div v-if="showLogs" class="card full-width">
                <h3>系统日志</h3>
                <el-button size="small" @click="refreshLogs" style="margin-bottom: 10px;">刷新日志</el-button>
                <el-button size="small" type="danger" @click="clearLogs" style="margin-bottom: 10px; margin-left: 10px;">清空日志</el-button>
                <div class="log-container">
                    <div v-for="log in logs" :key="log.id" class="log-entry">
                        <span class="log-timestamp">{{ formatTime(log.timestamp) }}</span>
                        <span :class="'log-level-' + log.level.toLowerCase()"> [{{ log.level }}]</span>
                        <span> {{ log.message }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设备控制对话框 -->
        <el-dialog v-model="controlDialogVisible" title="设备控制" width="500px">
            <el-form :model="controlForm" label-width="100px">
                <el-form-item label="设备">
                    <el-input v-model="controlForm.deviceName" disabled></el-input>
                </el-form-item>
                <el-form-item label="亮度">
                    <el-slider v-model="controlForm.brightness" :min="0" :max="100" show-input></el-slider>
                </el-form-item>
                <el-form-item label="色温">
                    <el-slider v-model="controlForm.colorTemperature" :min="2700" :max="6500" show-input></el-slider>
                </el-form-item>
                <el-form-item label="过渡时间">
                    <el-input-number v-model="controlForm.transitionTime" :min="0" :max="10000" controls-position="right"></el-input-number>
                    <span style="margin-left: 10px; color: #909399;">毫秒</span>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="controlDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="applyDeviceControl" :loading="controlLoading">应用</el-button>
            </template>
        </el-dialog>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/vue@3.3.8/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-plus@2.4.4/dist/index.full.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@element-plus/icons-vue@2.1.0/dist/index.iife.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@1.6.2/dist/axios.min.js"></script>
    <script>
        const { createApp, ref, computed, onMounted } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        createApp({
            setup() {
                // 响应式数据
                const loading = ref(false);
                const reconnecting = ref(false);
                const scanning = ref(false);
                const showLogs = ref(false);
                const deviceFilter = ref('');
                const controlDialogVisible = ref(false);
                const controlLoading = ref(false);

                const connectionStats = ref({
                    isConnected: false,
                    serverAddress: '',
                    clientId: '',
                    connectedAt: null,
                    messagesSent: 0,
                    messagesReceived: 0,
                    reconnectCount: 0
                });

                const deviceStats = ref({
                    totalDevices: 0,
                    onlineDevices: 0,
                    offlineDevices: 0
                });

                const devices = ref([]);
                const logs = ref([]);

                const controlForm = ref({
                    deviceName: '',
                    area: '',
                    address: '',
                    brightness: 50,
                    colorTemperature: 4000,
                    transitionTime: 1000
                });

                // 计算属性
                const filteredDevices = computed(() => {
                    if (!deviceFilter.value) return devices.value;
                    const filter = deviceFilter.value.toLowerCase();
                    return devices.value.filter(device => 
                        (device.deviceName || device.deviceKey).toLowerCase().includes(filter) ||
                        device.area.toLowerCase().includes(filter) ||
                        device.address.toLowerCase().includes(filter)
                    );
                });

                // API 基础配置
                const api = axios.create({
                    baseURL: '/api/mqtt',
                    timeout: 30000
                });

                // 工具函数
                const formatTime = (timestamp) => {
                    if (!timestamp) return '-';
                    return new Date(timestamp).toLocaleString('zh-CN');
                };

                // API 方法
                const getConnectionStatus = async () => {
                    try {
                        const response = await api.get('/connection/status');
                        connectionStats.value = response.data;
                    } catch (error) {
                        console.error('获取连接状态失败:', error);
                    }
                };

                const getDevices = async () => {
                    try {
                        const response = await api.get('/devices');
                        devices.value = response.data || [];
                    } catch (error) {
                        console.error('获取设备列表失败:', error);
                        devices.value = [];
                    }
                };

                const getDeviceStatistics = async () => {
                    try {
                        const response = await api.get('/devices/statistics');
                        deviceStats.value = response.data;
                    } catch (error) {
                        console.error('获取设备统计失败:', error);
                    }
                };

                const getLogs = async () => {
                    try {
                        const response = await api.get('/logs?count=50');
                        logs.value = response.data || [];
                    } catch (error) {
                        console.error('获取日志失败:', error);
                        logs.value = [];
                    }
                };

                // 操作方法
                const refreshData = async () => {
                    loading.value = true;
                    try {
                        await Promise.all([
                            getConnectionStatus(),
                            getDevices(),
                            getDeviceStatistics()
                        ]);
                        ElMessage.success('数据刷新成功');
                    } catch (error) {
                        ElMessage.error('数据刷新失败');
                    } finally {
                        loading.value = false;
                    }
                };

                const reconnectMqtt = async () => {
                    reconnecting.value = true;
                    try {
                        await api.post('/connection/reconnect');
                        ElMessage.success('重新连接成功');
                        await refreshData();
                    } catch (error) {
                        ElMessage.error('重新连接失败');
                    } finally {
                        reconnecting.value = false;
                    }
                };

                const scanDevices = async () => {
                    scanning.value = true;
                    try {
                        await api.post('/devices/scan');
                        ElMessage.success('设备扫描已启动');
                        setTimeout(() => {
                            refreshData();
                        }, 3000);
                    } catch (error) {
                        ElMessage.error('设备扫描失败');
                    } finally {
                        scanning.value = false;
                    }
                };

                const getDeviceStatus = async (device) => {
                    try {
                        await api.post('/devices/status', null, {
                            params: {
                                area: device.area,
                                address: device.address
                            }
                        });
                        ElMessage.success('状态查询已发送');
                    } catch (error) {
                        ElMessage.error('状态查询失败');
                    }
                };

                const switchLight = async (device, isOn) => {
                    try {
                        await api.post('/devices/switch', {
                            area: device.area,
                            address: device.address,
                            isOn: isOn
                        });
                        ElMessage.success(`${isOn ? '开灯' : '关灯'}指令已发送`);
                    } catch (error) {
                        ElMessage.error(`${isOn ? '开灯' : '关灯'}指令发送失败`);
                    }
                };

                const showDeviceControl = (device) => {
                    controlForm.value = {
                        deviceName: device.deviceName || device.deviceKey,
                        area: device.area,
                        address: device.address,
                        brightness: 50,
                        colorTemperature: 4000,
                        transitionTime: 1000
                    };
                    controlDialogVisible.value = true;
                };

                const applyDeviceControl = async () => {
                    controlLoading.value = true;
                    try {
                        await api.post('/devices/lighting', {
                            area: controlForm.value.area,
                            address: controlForm.value.address,
                            brightness: controlForm.value.brightness,
                            colorTemperature: controlForm.value.colorTemperature,
                            red: 0,
                            green: 0,
                            blue: 0,
                            white: 0,
                            transitionTime: controlForm.value.transitionTime
                        });
                        ElMessage.success('控制指令已发送');
                        controlDialogVisible.value = false;
                    } catch (error) {
                        ElMessage.error('控制指令发送失败');
                    } finally {
                        controlLoading.value = false;
                    }
                };

                const refreshLogs = async () => {
                    await getLogs();
                    ElMessage.success('日志刷新成功');
                };

                const clearLogs = async () => {
                    try {
                        await ElMessageBox.confirm('确定要清空所有日志吗？', '确认', {
                            type: 'warning'
                        });
                        await api.delete('/logs');
                        logs.value = [];
                        ElMessage.success('日志已清空');
                    } catch (error) {
                        if (error !== 'cancel') {
                            ElMessage.error('清空日志失败');
                        }
                    }
                };

                // 生命周期
                onMounted(() => {
                    refreshData();
                    // 定时刷新数据
                    setInterval(refreshData, 30000);
                });

                return {
                    loading,
                    reconnecting,
                    scanning,
                    showLogs,
                    deviceFilter,
                    controlDialogVisible,
                    controlLoading,
                    connectionStats,
                    deviceStats,
                    devices,
                    logs,
                    controlForm,
                    filteredDevices,
                    formatTime,
                    refreshData,
                    reconnectMqtt,
                    scanDevices,
                    getDeviceStatus,
                    switchLight,
                    showDeviceControl,
                    applyDeviceControl,
                    refreshLogs,
                    clearLogs
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>