<template>
  <view class="lighting-control">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">照明控制 (简化版)</text>
    </view>

    <!-- 基本信息显示 -->
    <view class="simple-info">
      <text class="info-text">页面加载状态: {{ loadStatus }}</text>
      <text class="info-text">当前时间: {{ currentTime }}</text>
      <text class="info-text">设备数量: {{ deviceCount }}</text>
    </view>

    <!-- 简单按钮测试 -->
    <view class="simple-actions">
      <button @click="testClick" class="simple-btn">测试按钮</button>
      <button @click="loadTestData" class="simple-btn">加载测试数据</button>
    </view>

    <!-- 测试数据显示 -->
    <view class="test-data" v-if="testData.length > 0">
      <text class="section-title">测试数据:</text>
      <view v-for="item in testData" :key="item.id" class="data-item">
        <text>{{ item.name }} - {{ item.status }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, onMounted } from 'vue'

export default {
  name: 'LightingControlSimple',
  setup() {
    const loadStatus = ref('初始化中...')
    const currentTime = ref('')
    const deviceCount = ref(0)
    const testData = ref([])

    // 更新时间
    const updateTime = () => {
      currentTime.value = new Date().toLocaleTimeString()
    }

    // 测试点击
    const testClick = () => {
      console.log('测试按钮被点击')
      uni.showToast({
        title: '按钮点击成功',
        icon: 'success'
      })
      updateTime()
    }

    // 加载测试数据
    const loadTestData = () => {
      console.log('加载测试数据')
      testData.value = [
        { id: 1, name: '测试设备1', status: '在线' },
        { id: 2, name: '测试设备2', status: '离线' },
        { id: 3, name: '测试设备3', status: '在线' }
      ]
      deviceCount.value = testData.value.length
      loadStatus.value = '数据加载完成'
      
      uni.showToast({
        title: '测试数据加载成功',
        icon: 'success'
      })
    }

    // 页面加载时初始化
    onMounted(() => {
      console.log('简化版照明控制页面加载')
      loadStatus.value = '页面加载完成'
      updateTime()
      
      // 定时更新时间
      setInterval(updateTime, 1000)
    })

    return {
      loadStatus,
      currentTime,
      deviceCount,
      testData,
      testClick,
      loadTestData
    }
  }
}
</script>

<style scoped>
.lighting-control {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background-color: white;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.simple-info {
  background-color: white;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.info-text {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.simple-actions {
  background-color: white;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.simple-btn {
  background-color: #1890ff;
  color: white;
  border: none;
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  margin: 0 10rpx 15rpx 10rpx;
  font-size: 28rpx;
}

.simple-btn:active {
  background-color: #0056CC;
}

.test-data {
  background-color: white;
  padding: 30rpx;
  border-radius: 12rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.data-item {
  padding: 15rpx;
  background-color: #f8f9fa;
  margin-bottom: 10rpx;
  border-radius: 8rpx;
}
</style>