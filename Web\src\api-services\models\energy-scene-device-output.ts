/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 场景设备输出参数
 * @export
 * @interface EnergySceneDeviceOutput
 */
export interface EnergySceneDeviceOutput {
    /**
     * 主键ID
     * @type {number}
     * @memberof EnergySceneDeviceOutput
     */
    id?: number;
    /**
     * 场景ID
     * @type {number}
     * @memberof EnergySceneDeviceOutput
     */
    sceneId?: number;
    /**
     * 场景名称
     * @type {string}
     * @memberof EnergySceneDeviceOutput
     */
    sceneName?: string | null;
    /**
     * 设备ID
     * @type {number}
     * @memberof EnergySceneDeviceOutput
     */
    deviceId?: number;
    /**
     * 设备编码
     * @type {string}
     * @memberof EnergySceneDeviceOutput
     */
    deviceCode?: string | null;
    /**
     * 设备名称
     * @type {string}
     * @memberof EnergySceneDeviceOutput
     */
    deviceName?: string | null;
    /**
     * 设备位置
     * @type {string}
     * @memberof EnergySceneDeviceOutput
     */
    deviceLocation?: string | null;
    /**
     * 控制类型
     * @type {string}
     * @memberof EnergySceneDeviceOutput
     */
    controlType?: string | null;
    /**
     * 控制命令
     * @type {string}
     * @memberof EnergySceneDeviceOutput
     */
    controlCommand?: string | null;
    /**
     * 控制参数
     * @type {string}
     * @memberof EnergySceneDeviceOutput
     */
    controlParams?: string | null;
    /**
     * 延迟时间(秒)
     * @type {number}
     * @memberof EnergySceneDeviceOutput
     */
    delayTime?: number;
    /**
     * 延迟时间(秒) - 兼容字段
     * @type {number}
     * @memberof EnergySceneDeviceOutput
     */
    delaySeconds?: number;
    /**
     * 执行次数
     * @type {number}
     * @memberof EnergySceneDeviceOutput
     */
    executeCount?: number;
    /**
     * 成功次数
     * @type {number}
     * @memberof EnergySceneDeviceOutput
     */
    successCount?: number;
    /**
     * 成功率
     * @type {number}
     * @memberof EnergySceneDeviceOutput
     */
    successRate?: number;
    /**
     * 最后执行时间
     * @type {Date}
     * @memberof EnergySceneDeviceOutput
     */
    lastExecuteTime?: Date | null;
    /**
     * 平均执行时长(毫秒)
     * @type {number}
     * @memberof EnergySceneDeviceOutput
     */
    avgExecuteTime?: number | null;
    /**
     * 平均执行持续时间(毫秒)
     * @type {number}
     * @memberof EnergySceneDeviceOutput
     */
    avgExecuteDuration?: number | null;
    /**
     * 排序
     * @type {number}
     * @memberof EnergySceneDeviceOutput
     */
    sort?: number;
    /**
     * 状态
     * @type {number}
     * @memberof EnergySceneDeviceOutput
     */
    status?: number;
    /**
     * 状态名称
     * @type {string}
     * @memberof EnergySceneDeviceOutput
     */
    statusName?: string | null;
    /**
     * 设备类型
     * @type {string}
     * @memberof EnergySceneDeviceOutput
     */
    deviceType?: string | null;
    /**
     * 设备型号
     * @type {string}
     * @memberof EnergySceneDeviceOutput
     */
    deviceModel?: string | null;
    /**
     * 租户ID
     * @type {number}
     * @memberof EnergySceneDeviceOutput
     */
    tenantId?: number | null;
    /**
     * 创建时间
     * @type {Date}
     * @memberof EnergySceneDeviceOutput
     */
    createTime?: Date;
    /**
     * 更新时间
     * @type {Date}
     * @memberof EnergySceneDeviceOutput
     */
    updateTime?: Date | null;
}
