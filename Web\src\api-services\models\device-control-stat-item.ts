/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 设备控制统计项
 * @export
 * @interface DeviceControlStatItem
 */
export interface DeviceControlStatItem {
    /**
     * 设备ID
     * @type {number}
     * @memberof DeviceControlStatItem
     */
    deviceId?: number;
    /**
     * 设备编码
     * @type {string}
     * @memberof DeviceControlStatItem
     */
    deviceCode?: string | null;
    /**
     * 设备名称
     * @type {string}
     * @memberof DeviceControlStatItem
     */
    deviceName?: string | null;
    /**
     * 控制次数
     * @type {number}
     * @memberof DeviceControlStatItem
     */
    count?: number;
    /**
     * 成功次数
     * @type {number}
     * @memberof DeviceControlStatItem
     */
    successCount?: number;
    /**
     * 成功率
     * @type {number}
     * @memberof DeviceControlStatItem
     */
    successRate?: number;
    /**
     * 最近控制时间
     * @type {Date}
     * @memberof DeviceControlStatItem
     */
    lastControlTime?: Date | null;
}
