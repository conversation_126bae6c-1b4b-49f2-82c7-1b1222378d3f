using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Admin.NET.Plugin.MQTT.Core.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Admin.NET.Plugin.MQTT.Core.Interfaces
{
    /// <summary>
    /// MQTT插件主接口
    /// 定义插件的生命周期管理、服务注册和核心功能
    /// </summary>
    public interface IMqttPlugin : IDisposable
    {
        /// <summary>
        /// 插件名称
        /// </summary>
        string Name { get; }
        
        /// <summary>
        /// 插件版本
        /// </summary>
        string Version { get; }
        
        /// <summary>
        /// 插件描述
        /// </summary>
        string Description { get; }
        
        /// <summary>
        /// 插件作者
        /// </summary>
        string Author { get; }
        
        /// <summary>
        /// 插件状态
        /// </summary>
        PluginStatus Status { get; }
        
        /// <summary>
        /// 是否已初始化
        /// </summary>
        bool IsInitialized { get; }
        
        /// <summary>
        /// 是否正在运行
        /// </summary>
        bool IsRunning { get; }
        
        /// <summary>
        /// 服务工厂
        /// </summary>
        IMqttServiceFactory ServiceFactory { get; }
        
        /// <summary>
        /// 配置管理器
        /// </summary>
        IMqttConfigurationManager ConfigurationManager { get; }
        
        /// <summary>
        /// 异常处理器
        /// </summary>
        IMqttExceptionHandler ExceptionHandler { get; }
        
        /// <summary>
        /// 插件状态变更事件
        /// </summary>
        event EventHandler<PluginStatusChangedEventArgs> StatusChanged;
        
        /// <summary>
        /// 插件错误事件
        /// </summary>
        event EventHandler<PluginErrorEventArgs> ErrorOccurred;
        
        /// <summary>
        /// 初始化插件
        /// </summary>
        /// <param name="configuration">插件配置</param>
        /// <param name="serviceCollection">服务集合</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>初始化任务</returns>
        Task InitializeAsync(MqttPluginConfiguration configuration, IServiceCollection serviceCollection = null, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 启动插件
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>启动任务</returns>
        Task StartAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 停止插件
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>停止任务</returns>
        Task StopAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 重启插件
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>重启任务</returns>
        Task RestartAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 暂停插件
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>暂停任务</returns>
        Task PauseAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 恢复插件
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>恢复任务</returns>
        Task ResumeAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 重新加载配置
        /// </summary>
        /// <param name="configuration">新配置</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>重新加载任务</returns>
        Task ReloadConfigurationAsync(MqttPluginConfiguration configuration, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 获取插件信息
        /// </summary>
        /// <returns>插件信息</returns>
        PluginInfo GetPluginInfo();
        
        /// <summary>
        /// 获取插件统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        PluginStatistics GetStatistics();
        
        /// <summary>
        /// 获取插件健康状态
        /// </summary>
        /// <returns>健康状态</returns>
        PluginHealthStatus GetHealthStatus();
        
        /// <summary>
        /// 执行健康检查
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>健康检查结果</returns>
        Task<PluginHealthCheckResult> PerformHealthCheckAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 获取所有服务
        /// </summary>
        /// <returns>服务列表</returns>
        IEnumerable<object> GetServices();
        
        /// <summary>
        /// 获取指定类型的服务
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <returns>服务实例</returns>
        T GetService<T>() where T : class;
        
        /// <summary>
        /// 注册消息处理器
        /// </summary>
        /// <typeparam name="T">处理器类型</typeparam>
        /// <param name="topicPattern">主题模式</param>
        /// <param name="handler">处理器实例</param>
        /// <returns>注册任务</returns>
        Task RegisterMessageHandlerAsync<T>(string topicPattern, T handler) where T : class, IMessageHandler;
        
        /// <summary>
        /// 移除消息处理器
        /// </summary>
        /// <param name="topicPattern">主题模式</param>
        /// <param name="handlerId">处理器ID</param>
        /// <returns>移除任务</returns>
        Task RemoveMessageHandlerAsync(string topicPattern, string handlerId);
        
        /// <summary>
        /// 发布消息
        /// </summary>
        /// <param name="topic">主题</param>
        /// <param name="payload">消息内容</param>
        /// <param name="qos">服务质量</param>
        /// <param name="retain">是否保留</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>发布任务</returns>
        Task PublishAsync(string topic, byte[] payload, int qos = 0, bool retain = false, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 发布消息
        /// </summary>
        /// <param name="topic">主题</param>
        /// <param name="message">消息对象</param>
        /// <param name="qos">服务质量</param>
        /// <param name="retain">是否保留</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>发布任务</returns>
        Task PublishAsync<T>(string topic, T message, int qos = 0, bool retain = false, CancellationToken cancellationToken = default) where T : class;
        
        /// <summary>
        /// 订阅主题
        /// </summary>
        /// <param name="topicPattern">主题模式</param>
        /// <param name="qos">服务质量</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>订阅任务</returns>
        Task SubscribeAsync(string topicPattern, int qos = 0, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 取消订阅主题
        /// </summary>
        /// <param name="topicPattern">主题模式</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>取消订阅任务</returns>
        Task UnsubscribeAsync(string topicPattern, CancellationToken cancellationToken = default);
    }
    
    /// <summary>
    /// 插件状态枚举
    /// </summary>
    public enum PluginStatus
    {
        /// <summary>
        /// 未初始化
        /// </summary>
        NotInitialized,
        
        /// <summary>
        /// 正在初始化
        /// </summary>
        Initializing,
        
        /// <summary>
        /// 已初始化
        /// </summary>
        Initialized,
        
        /// <summary>
        /// 正在启动
        /// </summary>
        Starting,
        
        /// <summary>
        /// 运行中
        /// </summary>
        Running,
        
        /// <summary>
        /// 已暂停
        /// </summary>
        Paused,
        
        /// <summary>
        /// 正在停止
        /// </summary>
        Stopping,
        
        /// <summary>
        /// 已停止
        /// </summary>
        Stopped,
        
        /// <summary>
        /// 错误状态
        /// </summary>
        Error,
        
        /// <summary>
        /// 已释放
        /// </summary>
        Disposed
    }
    
    /// <summary>
    /// 插件健康状态枚举
    /// </summary>
    public enum PluginHealthStatus
    {
        /// <summary>
        /// 健康
        /// </summary>
        Healthy,
        
        /// <summary>
        /// 警告
        /// </summary>
        Warning,
        
        /// <summary>
        /// 不健康
        /// </summary>
        Unhealthy,
        
        /// <summary>
        /// 未知
        /// </summary>
        Unknown
    }
    
    /// <summary>
    /// 插件状态变更事件参数
    /// </summary>
    public class PluginStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 旧状态
        /// </summary>
        public PluginStatus OldStatus { get; set; }
        
        /// <summary>
        /// 新状态
        /// </summary>
        public PluginStatus NewStatus { get; set; }
        
        /// <summary>
        /// 变更时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// 变更原因
        /// </summary>
        public string Reason { get; set; }
    }
    
    /// <summary>
    /// 插件错误事件参数
    /// </summary>
    public class PluginErrorEventArgs : EventArgs
    {
        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception Exception { get; set; }
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string Message { get; set; }
        
        /// <summary>
        /// 错误级别
        /// </summary>
        public LogLevel Level { get; set; }
        
        /// <summary>
        /// 发生时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// 错误来源
        /// </summary>
        public string Source { get; set; }
    }
    
    /// <summary>
    /// 插件信息
    /// </summary>
    public class PluginInfo
    {
        /// <summary>
        /// 插件名称
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 插件版本
        /// </summary>
        public string Version { get; set; }
        
        /// <summary>
        /// 插件描述
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// 插件作者
        /// </summary>
        public string Author { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
        
        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; }
        
        /// <summary>
        /// 支持的功能列表
        /// </summary>
        public List<string> SupportedFeatures { get; set; } = new List<string>();
        
        /// <summary>
        /// 依赖项列表
        /// </summary>
        public List<string> Dependencies { get; set; } = new List<string>();
        
        /// <summary>
        /// 配置架构
        /// </summary>
        public string ConfigurationSchema { get; set; }
    }
    
    /// <summary>
    /// 插件统计信息
    /// </summary>
    public class PluginStatistics
    {
        /// <summary>
        /// 启动时间
        /// </summary>
        public DateTime StartTime { get; set; }
        
        /// <summary>
        /// 运行时长
        /// </summary>
        public TimeSpan Uptime { get; set; }
        
        /// <summary>
        /// 处理的消息总数
        /// </summary>
        public long TotalMessagesProcessed { get; set; }
        
        /// <summary>
        /// 发送的消息总数
        /// </summary>
        public long TotalMessagesSent { get; set; }
        
        /// <summary>
        /// 错误总数
        /// </summary>
        public long TotalErrors { get; set; }
        
        /// <summary>
        /// 重连次数
        /// </summary>
        public int ReconnectionCount { get; set; }
        
        /// <summary>
        /// 当前连接状态
        /// </summary>
        public bool IsConnected { get; set; }
        
        /// <summary>
        /// 内存使用量（字节）
        /// </summary>
        public long MemoryUsage { get; set; }
        
        /// <summary>
        /// CPU使用率（百分比）
        /// </summary>
        public double CpuUsage { get; set; }
        
        /// <summary>
        /// 活跃订阅数
        /// </summary>
        public int ActiveSubscriptions { get; set; }
        
        /// <summary>
        /// 注册的处理器数量
        /// </summary>
        public int RegisteredHandlers { get; set; }
    }
    
    /// <summary>
    /// 插件健康检查结果
    /// </summary>
    public class PluginHealthCheckResult
    {
        /// <summary>
        /// 健康状态
        /// </summary>
        public PluginHealthStatus Status { get; set; }
        
        /// <summary>
        /// 检查时间
        /// </summary>
        public DateTime CheckTime { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// 检查耗时
        /// </summary>
        public TimeSpan Duration { get; set; }
        
        /// <summary>
        /// 健康描述
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// 详细信息
        /// </summary>
        public Dictionary<string, object> Details { get; set; } = new Dictionary<string, object>();
        
        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception Exception { get; set; }
        
        /// <summary>
        /// 建议操作
        /// </summary>
        public List<string> Recommendations { get; set; } = new List<string>();
    }
}