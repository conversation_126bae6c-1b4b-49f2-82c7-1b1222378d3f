<template>
  <view class="skeleton-screen" :class="[`type-${type}`, `size-${size}`, customClass]">
    <!-- 设备卡片骨架屏 -->
    <view class="skeleton-device-card" v-if="type === 'device-card'">
      <view class="skeleton-header">
        <view class="skeleton-avatar"></view>
        <view class="skeleton-info">
          <view class="skeleton-line skeleton-title"></view>
          <view class="skeleton-line skeleton-subtitle"></view>
        </view>
        <view class="skeleton-status"></view>
      </view>
      <view class="skeleton-content">
        <view class="skeleton-line skeleton-text" v-for="i in 2" :key="i"></view>
      </view>
      <view class="skeleton-actions">
        <view class="skeleton-button" v-for="i in 3" :key="i"></view>
      </view>
    </view>
    
    <!-- 列表项骨架屏 -->
    <view class="skeleton-list-item" v-if="type === 'list-item'">
      <view class="skeleton-avatar"></view>
      <view class="skeleton-content">
        <view class="skeleton-line skeleton-title"></view>
        <view class="skeleton-line skeleton-subtitle"></view>
        <view class="skeleton-line skeleton-text"></view>
      </view>
      <view class="skeleton-action">
        <view class="skeleton-switch"></view>
      </view>
    </view>
    
    <!-- 图表骨架屏 -->
    <view class="skeleton-chart" v-if="type === 'chart'">
      <view class="skeleton-chart-header">
        <view class="skeleton-line skeleton-title"></view>
        <view class="skeleton-tabs">
          <view class="skeleton-tab" v-for="i in 3" :key="i"></view>
        </view>
      </view>
      <view class="skeleton-chart-content">
        <view class="skeleton-chart-bars">
          <view 
            class="skeleton-bar" 
            v-for="i in 7" 
            :key="i"
            :style="{ height: getRandomHeight() }"
          ></view>
        </view>
        <view class="skeleton-chart-legend">
          <view class="skeleton-legend-item" v-for="i in 3" :key="i">
            <view class="skeleton-legend-color"></view>
            <view class="skeleton-legend-text"></view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 统计卡片骨架屏 -->
    <view class="skeleton-stat-card" v-if="type === 'stat-card'">
      <view class="skeleton-stat-icon"></view>
      <view class="skeleton-stat-content">
        <view class="skeleton-line skeleton-number"></view>
        <view class="skeleton-line skeleton-label"></view>
      </view>
    </view>
    
    <!-- 表格骨架屏 -->
    <view class="skeleton-table" v-if="type === 'table'">
      <view class="skeleton-table-header">
        <view class="skeleton-th" v-for="i in columns" :key="i"></view>
      </view>
      <view class="skeleton-table-body">
        <view class="skeleton-tr" v-for="i in rows" :key="i">
          <view class="skeleton-td" v-for="j in columns" :key="j"></view>
        </view>
      </view>
    </view>
    
    <!-- 文本块骨架屏 -->
    <view class="skeleton-text-block" v-if="type === 'text'">
      <view class="skeleton-line skeleton-title" v-if="showTitle"></view>
      <view class="skeleton-line skeleton-text" v-for="i in lines" :key="i"></view>
    </view>
    
    <!-- 自定义骨架屏 -->
    <view class="skeleton-custom" v-if="type === 'custom'">
      <slot></slot>
    </view>
  </view>
</template>

<script setup>
// Props定义
defineProps({
  // 骨架屏类型
  type: {
    type: String,
    default: 'text',
    validator: (value) => {
      return [
        'device-card', 'list-item', 'chart', 'stat-card', 
        'table', 'text', 'custom'
      ].includes(value)
    }
  },
  // 尺寸
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  // 文本行数（仅text类型有效）
  lines: {
    type: Number,
    default: 3
  },
  // 是否显示标题（仅text类型有效）
  showTitle: {
    type: Boolean,
    default: true
  },
  // 表格列数（仅table类型有效）
  columns: {
    type: Number,
    default: 4
  },
  // 表格行数（仅table类型有效）
  rows: {
    type: Number,
    default: 5
  },
  // 是否启用动画
  animated: {
    type: Boolean,
    default: true
  },
  // 自定义样式类
  customClass: {
    type: [String, Array, Object],
    default: ''
  }
})

// 方法
const getRandomHeight = () => {
  const heights = ['60%', '80%', '100%', '70%', '90%', '50%', '85%']
  return heights[Math.floor(Math.random() * heights.length)]
}
</script>

<style lang="scss" scoped>
.skeleton-screen {
  width: 100%;
  
  // 基础骨架元素样式
  .skeleton-line,
  .skeleton-avatar,
  .skeleton-button,
  .skeleton-status,
  .skeleton-switch,
  .skeleton-tab,
  .skeleton-bar,
  .skeleton-legend-color,
  .skeleton-stat-icon,
  .skeleton-th,
  .skeleton-td {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s ease-in-out infinite;
    border-radius: 8rpx;
  }
  
  // 禁用动画
  &:not(.animated) {
    .skeleton-line,
    .skeleton-avatar,
    .skeleton-button,
    .skeleton-status,
    .skeleton-switch,
    .skeleton-tab,
    .skeleton-bar,
    .skeleton-legend-color,
    .skeleton-stat-icon,
    .skeleton-th,
    .skeleton-td {
      animation: none;
      background: #f0f0f0;
    }
  }
}

// 设备卡片骨架屏
.skeleton-device-card {
  padding: 24rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 16rpx;
  
  .skeleton-header {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    
    .skeleton-avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      margin-right: 20rpx;
    }
    
    .skeleton-info {
      flex: 1;
      
      .skeleton-title {
        width: 60%;
        height: 32rpx;
        margin-bottom: 12rpx;
      }
      
      .skeleton-subtitle {
        width: 40%;
        height: 24rpx;
      }
    }
    
    .skeleton-status {
      width: 60rpx;
      height: 32rpx;
      border-radius: 16rpx;
    }
  }
  
  .skeleton-content {
    margin-bottom: 20rpx;
    
    .skeleton-text {
      height: 24rpx;
      margin-bottom: 12rpx;
      
      &:nth-child(1) { width: 80%; }
      &:nth-child(2) { width: 60%; }
    }
  }
  
  .skeleton-actions {
    display: flex;
    gap: 16rpx;
    
    .skeleton-button {
      flex: 1;
      height: 60rpx;
      border-radius: 30rpx;
    }
  }
}

// 列表项骨架屏
.skeleton-list-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 2rpx solid #f0f0f0;
  
  .skeleton-avatar {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    margin-right: 20rpx;
  }
  
  .skeleton-content {
    flex: 1;
    
    .skeleton-title {
      width: 70%;
      height: 28rpx;
      margin-bottom: 12rpx;
    }
    
    .skeleton-subtitle {
      width: 50%;
      height: 24rpx;
      margin-bottom: 8rpx;
    }
    
    .skeleton-text {
      width: 40%;
      height: 20rpx;
    }
  }
  
  .skeleton-action {
    .skeleton-switch {
      width: 80rpx;
      height: 40rpx;
      border-radius: 20rpx;
    }
  }
}

// 图表骨架屏
.skeleton-chart {
  padding: 24rpx;
  
  .skeleton-chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32rpx;
    
    .skeleton-title {
      width: 30%;
      height: 32rpx;
    }
    
    .skeleton-tabs {
      display: flex;
      gap: 16rpx;
      
      .skeleton-tab {
        width: 80rpx;
        height: 32rpx;
        border-radius: 16rpx;
      }
    }
  }
  
  .skeleton-chart-content {
    .skeleton-chart-bars {
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      height: 300rpx;
      margin-bottom: 24rpx;
      
      .skeleton-bar {
        width: 32rpx;
        min-height: 60rpx;
        border-radius: 4rpx 4rpx 0 0;
      }
    }
    
    .skeleton-chart-legend {
      display: flex;
      justify-content: center;
      gap: 32rpx;
      
      .skeleton-legend-item {
        display: flex;
        align-items: center;
        gap: 8rpx;
        
        .skeleton-legend-color {
          width: 24rpx;
          height: 24rpx;
          border-radius: 50%;
        }
        
        .skeleton-legend-text {
          width: 60rpx;
          height: 20rpx;
        }
      }
    }
  }
}

// 统计卡片骨架屏
.skeleton-stat-card {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 16rpx;
  
  .skeleton-stat-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    margin-right: 24rpx;
  }
  
  .skeleton-stat-content {
    flex: 1;
    
    .skeleton-number {
      width: 80%;
      height: 40rpx;
      margin-bottom: 12rpx;
    }
    
    .skeleton-label {
      width: 60%;
      height: 24rpx;
    }
  }
}

// 表格骨架屏
.skeleton-table {
  border: 2rpx solid #f0f0f0;
  border-radius: 16rpx;
  overflow: hidden;
  
  .skeleton-table-header {
    display: flex;
    background: #fafafa;
    
    .skeleton-th {
      flex: 1;
      height: 80rpx;
      margin: 16rpx;
      border-radius: 8rpx;
    }
  }
  
  .skeleton-table-body {
    .skeleton-tr {
      display: flex;
      border-bottom: 2rpx solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .skeleton-td {
        flex: 1;
        height: 60rpx;
        margin: 16rpx;
        border-radius: 8rpx;
      }
    }
  }
}

// 文本块骨架屏
.skeleton-text-block {
  .skeleton-title {
    width: 50%;
    height: 32rpx;
    margin-bottom: 20rpx;
  }
  
  .skeleton-text {
    height: 24rpx;
    margin-bottom: 16rpx;
    
    &:nth-child(2) { width: 100%; }
    &:nth-child(3) { width: 85%; }
    &:nth-child(4) { width: 70%; }
    &:last-child { margin-bottom: 0; }
  }
}

// 尺寸变体
.size-small {
  .skeleton-device-card {
    padding: 16rpx;
    
    .skeleton-header .skeleton-avatar {
      width: 60rpx;
      height: 60rpx;
    }
  }
  
  .skeleton-list-item {
    padding: 16rpx;
    
    .skeleton-avatar {
      width: 48rpx;
      height: 48rpx;
    }
  }
  
  .skeleton-stat-card {
    padding: 20rpx;
    
    .skeleton-stat-icon {
      width: 60rpx;
      height: 60rpx;
    }
  }
}

.size-large {
  .skeleton-device-card {
    padding: 32rpx;
    
    .skeleton-header .skeleton-avatar {
      width: 100rpx;
      height: 100rpx;
    }
  }
  
  .skeleton-list-item {
    padding: 32rpx;
    
    .skeleton-avatar {
      width: 80rpx;
      height: 80rpx;
    }
  }
  
  .skeleton-stat-card {
    padding: 40rpx;
    
    .skeleton-stat-icon {
      width: 100rpx;
      height: 100rpx;
    }
  }
}

// 动画定义
@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
</style>