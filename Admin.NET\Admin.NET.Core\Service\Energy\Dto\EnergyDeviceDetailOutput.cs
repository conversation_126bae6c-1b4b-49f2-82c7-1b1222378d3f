// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core.Service;

/// <summary>
/// 设备详情输出参数
/// </summary>
public class EnergyDeviceDetailOutput : EnergyDeviceOutput
{
    /// <summary>
    /// 分组详细信息
    /// </summary>
    public EnergyDeviceGroupOutput? Group { get; set; }

    /// <summary>
    /// 最近能耗记录
    /// </summary>
    public List<EnergyConsumptionOutput> RecentConsumptions { get; set; } = new();

    /// <summary>
    /// 最近故障记录
    /// </summary>
    public List<EnergyFaultOutput> RecentFaults { get; set; } = new();

    /// <summary>
    /// 最近控制记录
    /// </summary>
    public List<EnergyControlOutput> RecentControls { get; set; } = new();

    /// <summary>
    /// 设备统计信息
    /// </summary>
    public DeviceStatInfo StatInfo { get; set; } = new();

    /// <summary>
    /// 今日运行时长(小时)
    /// </summary>
    public decimal TodayRunningTime { get; set; }

    /// <summary>
    /// 本月运行时长(小时)
    /// </summary>
    public decimal MonthRunningTime { get; set; }

    /// <summary>
    /// 累计运行时长(小时)
    /// </summary>
    public decimal TotalRunningTime { get; set; }
}

/// <summary>
/// 设备统计信息
/// </summary>
public class DeviceStatInfo
{
    /// <summary>
    /// 今日用电量(kWh)
    /// </summary>
    public decimal TodayEnergy { get; set; }

    /// <summary>
    /// 本月用电量(kWh)
    /// </summary>
    public decimal MonthEnergy { get; set; }

    /// <summary>
    /// 累计用电量(kWh)
    /// </summary>
    public decimal TotalEnergy { get; set; }

    /// <summary>
    /// 今日控制次数
    /// </summary>
    public int TodayControlCount { get; set; }

    /// <summary>
    /// 本月控制次数
    /// </summary>
    public int MonthControlCount { get; set; }

    /// <summary>
    /// 累计控制次数
    /// </summary>
    public int TotalControlCount { get; set; }

    /// <summary>
    /// 故障次数
    /// </summary>
    public int FaultCount { get; set; }

    /// <summary>
    /// 运行天数
    /// </summary>
    public int RunDays { get; set; }

    /// <summary>
    /// 在线时长(小时)
    /// </summary>
    public decimal OnlineHours { get; set; }
}