using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Admin.NET.Plugin.MQTT.Core.Interfaces;
using Admin.NET.Plugin.MQTT.Core.Models;

namespace Admin.NET.Plugin.MQTT.Core.Services
{
    /// <summary>
    /// 日志服务实现
    /// 提供结构化日志记录、性能监控、日志聚合、异步写入等功能
    /// 支持多种日志级别、自定义格式化、日志轮转等特性
    /// </summary>
    public class LoggingService : ILoggingService, IDisposable
    {
        #region 私有字段
        
        private readonly ILogger<LoggingService> _logger;
        private readonly IMqttConfigurationManager _configurationManager;
        private readonly IMqttExceptionHandler _exceptionHandler;
        
        private readonly ConcurrentQueue<LogEntry> _logQueue;
        private readonly ConcurrentDictionary<string, LogStatistics> _logStatistics;
        private readonly ConcurrentDictionary<string, PerformanceCounter> _performanceCounters;
        
        private readonly Timer _flushTimer;
        private readonly Timer _statisticsTimer;
        private readonly Timer _cleanupTimer;
        private readonly SemaphoreSlim _flushSemaphore;
        
        private readonly string _logDirectory;
        private readonly object _fileLock = new object();
        
        private volatile bool _isDisposed;
        private volatile bool _isProcessing;
        private long _totalLogEntries;
        private long _totalErrors;
        private long _totalWarnings;
        
        #endregion
        
        #region 事件
        
        /// <summary>
        /// 日志条目写入事件
        /// </summary>
        public event EventHandler<LogEntryWrittenEventArgs> LogEntryWritten;
        
        /// <summary>
        /// 日志错误事件
        /// </summary>
        public event EventHandler<LogErrorEventArgs> LogError;
        
        /// <summary>
        /// 性能警告事件
        /// </summary>
        public event EventHandler<PerformanceWarningEventArgs> PerformanceWarning;
        
        /// <summary>
        /// 日志统计更新事件
        /// </summary>
        public event EventHandler<LogStatisticsUpdatedEventArgs> StatisticsUpdated;
        
        #endregion
        
        #region 属性
        
        /// <summary>
        /// 总日志条目数
        /// </summary>
        public long TotalLogEntries => Interlocked.Read(ref _totalLogEntries);
        
        /// <summary>
        /// 总错误数
        /// </summary>
        public long TotalErrors => Interlocked.Read(ref _totalErrors);
        
        /// <summary>
        /// 总警告数
        /// </summary>
        public long TotalWarnings => Interlocked.Read(ref _totalWarnings);
        
        /// <summary>
        /// 队列中的日志条目数
        /// </summary>
        public int QueuedLogEntries => _logQueue.Count;
        
        /// <summary>
        /// 是否正在处理日志
        /// </summary>
        public bool IsProcessing => _isProcessing;
        
        #endregion
        
        #region 构造函数
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="configurationManager">配置管理器</param>
        /// <param name="exceptionHandler">异常处理器</param>
        public LoggingService(
            ILogger<LoggingService> logger,
            IMqttConfigurationManager configurationManager,
            IMqttExceptionHandler exceptionHandler)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configurationManager = configurationManager ?? throw new ArgumentNullException(nameof(configurationManager));
            _exceptionHandler = exceptionHandler ?? throw new ArgumentNullException(nameof(exceptionHandler));
            
            _logQueue = new ConcurrentQueue<LogEntry>();
            _logStatistics = new ConcurrentDictionary<string, LogStatistics>();
            _performanceCounters = new ConcurrentDictionary<string, PerformanceCounter>();
            _flushSemaphore = new SemaphoreSlim(1, 1);
            
            var config = _configurationManager.GetLoggingConfiguration();
            
            // 设置日志目录
            _logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs", "MQTT");
            if (!Directory.Exists(_logDirectory))
            {
                Directory.CreateDirectory(_logDirectory);
            }
            
            // 初始化定时器
            _flushTimer = new Timer(FlushCallback, null, 
                TimeSpan.FromSeconds(config.FlushIntervalSeconds), 
                TimeSpan.FromSeconds(config.FlushIntervalSeconds));
            
            _statisticsTimer = new Timer(StatisticsCallback, null, 
                TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
            
            _cleanupTimer = new Timer(CleanupCallback, null, 
                TimeSpan.FromHours(1), TimeSpan.FromHours(1));
            
            _logger.LogInformation("日志服务已初始化，日志目录: {LogDirectory}", _logDirectory);
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 记录日志
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        /// <param name="properties">附加属性</param>
        /// <param name="category">日志分类</param>
        /// <returns>记录任务</returns>
        public async Task LogAsync(Microsoft.Extensions.Logging.LogLevel level, string message, Exception exception = null,
            Dictionary<string, object> properties = null, string category = null)
        {
            if (string.IsNullOrWhiteSpace(message) && exception == null)
                return;
            
            try
            {
                var config = _configurationManager.GetLoggingConfiguration();
                
                // 检查日志级别
                if (level < config.MinimumLogLevel)
                    return;
                
                var logEntry = new LogEntry
                {
                    Id = Guid.NewGuid().ToString("N"),
                    Timestamp = DateTime.UtcNow,
                    Level = level,
                    Message = message ?? exception?.Message,
                    Exception = exception,
                    Properties = properties ?? new Dictionary<string, object>(),
                    Category = category ?? "General",
                    ThreadId = Thread.CurrentThread.ManagedThreadId,
                    ProcessId = Process.GetCurrentProcess().Id,
                    MachineName = Environment.MachineName,
                    UserName = Environment.UserName
                };
                
                // 添加性能信息
                if (config.IncludePerformanceInfo)
                {
                    var process = Process.GetCurrentProcess();
                    logEntry.Properties["MemoryUsage"] = process.WorkingSet64;
                    logEntry.Properties["CpuTime"] = process.TotalProcessorTime.TotalMilliseconds;
                    logEntry.Properties["ThreadCount"] = process.Threads.Count;
                }
                
                // 添加到队列
                _logQueue.Enqueue(logEntry);
                Interlocked.Increment(ref _totalLogEntries);
                
                // 更新统计
                UpdateLogStatistics(logEntry);
                
                // 如果是错误或警告，立即处理
                if (level >= LogLevel.Warning)
                {
                    if (level >= LogLevel.Error)
                    {
                        Interlocked.Increment(ref _totalErrors);
                    }
                    else
                    {
                        Interlocked.Increment(ref _totalWarnings);
                    }
                    
                    // 立即刷新高优先级日志
                    _ = Task.Run(async () => await FlushLogsAsync());
                }
                
                // 检查队列大小
                if (_logQueue.Count > config.MaxQueueSize)
                {
                    _ = Task.Run(async () => await FlushLogsAsync());
                }
                
                // 触发日志写入事件
                LogEntryWritten?.Invoke(this, new LogEntryWrittenEventArgs
                {
                    LogEntry = logEntry,
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                // 避免递归调用
                try
                {
                    _logger.LogError(ex, "记录日志时发生错误");
                    
                    LogError?.Invoke(this, new LogErrorEventArgs
                    {
                        Exception = ex,
                        OriginalMessage = message,
                        OriginalLevel = level,
                        Timestamp = DateTime.UtcNow
                    });
                }
                catch
                {
                    // 忽略日志记录错误，避免无限递归
                }
            }
        }
        
        /// <summary>
        /// 记录性能指标
        /// </summary>
        /// <param name="name">指标名称</param>
        /// <param name="value">指标值</param>
        /// <param name="unit">单位</param>
        /// <param name="properties">附加属性</param>
        /// <returns>记录任务</returns>
        public async Task LogPerformanceAsync(string name, double value, string unit = null, 
            Dictionary<string, object> properties = null)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("性能指标名称不能为空", nameof(name));
            
            try
            {
                var counter = _performanceCounters.AddOrUpdate(name,
                    new PerformanceCounter
                    {
                        Name = name,
                        Unit = unit,
                        Count = 1,
                        Sum = value,
                        Min = value,
                        Max = value,
                        Average = value,
                        LastValue = value,
                        LastUpdated = DateTime.UtcNow
                    },
                    (key, existing) =>
                    {
                        existing.Count++;
                        existing.Sum += value;
                        existing.Min = Math.Min(existing.Min, value);
                        existing.Max = Math.Max(existing.Max, value);
                        existing.Average = existing.Sum / existing.Count;
                        existing.LastValue = value;
                        existing.LastUpdated = DateTime.UtcNow;
                        return existing;
                    });
                
                // 记录性能日志
                var performanceProperties = new Dictionary<string, object>
                {
                    ["MetricName"] = name,
                    ["MetricValue"] = value,
                    ["MetricUnit"] = unit ?? "count",
                    ["MetricCount"] = counter.Count,
                    ["MetricAverage"] = counter.Average,
                    ["MetricMin"] = counter.Min,
                    ["MetricMax"] = counter.Max
                };
                
                if (properties != null)
                {
                    foreach (var prop in properties)
                    {
                        performanceProperties[prop.Key] = prop.Value;
                    }
                }
                
                await LogAsync(LogLevel.Debug, $"性能指标: {name} = {value} {unit}", 
                    null, performanceProperties, "Performance");
                
                // 检查性能警告阈值
                var config = _configurationManager.GetLoggingConfiguration();
                if (config.PerformanceWarningThresholds.TryGetValue(name, out var threshold) && value > threshold)
                {
                    PerformanceWarning?.Invoke(this, new PerformanceWarningEventArgs
                    {
                        MetricName = name,
                        CurrentValue = value,
                        ThresholdValue = threshold,
                        Unit = unit,
                        Counter = counter,
                        Timestamp = DateTime.UtcNow
                    });
                    
                    await LogAsync(LogLevel.Warning, 
                        $"性能警告: {name} ({value} {unit}) 超过阈值 ({threshold} {unit})", 
                        null, performanceProperties, "PerformanceWarning");
                }
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, $"LogPerformance_{name}");
            }
        }
        
        /// <summary>
        /// 开始性能计时
        /// </summary>
        /// <param name="name">计时器名称</param>
        /// <param name="properties">附加属性</param>
        /// <returns>性能计时器</returns>
        public IPerformanceTimer StartTimer(string name, Dictionary<string, object> properties = null)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("计时器名称不能为空", nameof(name));
            
            return new PerformanceTimer(this, name, properties);
        }
        
        /// <summary>
        /// 获取日志统计信息
        /// </summary>
        /// <param name="category">日志分类</param>
        /// <returns>统计信息</returns>
        public LogStatisticsInfo GetStatistics(string category = null)
        {
            var statistics = _logStatistics.Values.AsEnumerable();
            
            if (!string.IsNullOrWhiteSpace(category))
            {
                statistics = statistics.Where(s => s.Category.Equals(category, StringComparison.OrdinalIgnoreCase));
            }
            
            var statsList = statistics.ToList();
            
            return new LogStatisticsInfo
            {
                TotalEntries = TotalLogEntries,
                TotalErrors = TotalErrors,
                TotalWarnings = TotalWarnings,
                QueuedEntries = QueuedLogEntries,
                Categories = statsList.GroupBy(s => s.Category)
                    .ToDictionary(g => g.Key, g => g.Sum(s => s.Count)),
                Levels = statsList.GroupBy(s => s.Level)
                    .ToDictionary(g => g.Key, g => g.Sum(s => s.Count)),
                ErrorRate = TotalLogEntries > 0 ? (double)TotalErrors / TotalLogEntries : 0.0,
                WarningRate = TotalLogEntries > 0 ? (double)TotalWarnings / TotalLogEntries : 0.0,
                AverageEntriesPerMinute = CalculateAverageEntriesPerMinute(),
                Timestamp = DateTime.UtcNow
            };
        }
        
        /// <summary>
        /// 获取性能统计信息
        /// </summary>
        /// <param name="name">性能指标名称</param>
        /// <returns>性能统计信息</returns>
        public PerformanceStatisticsInfo GetPerformanceStatistics(string name = null)
        {
            var counters = _performanceCounters.Values.AsEnumerable();
            
            if (!string.IsNullOrWhiteSpace(name))
            {
                counters = counters.Where(c => c.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
            }
            
            var countersList = counters.ToList();
            
            return new PerformanceStatisticsInfo
            {
                TotalCounters = _performanceCounters.Count,
                Counters = countersList.ToDictionary(c => c.Name, c => c),
                TopCountersByValue = countersList.OrderByDescending(c => c.LastValue).Take(10).ToList(),
                TopCountersByCount = countersList.OrderByDescending(c => c.Count).Take(10).ToList(),
                Timestamp = DateTime.UtcNow
            };
        }
        
        /// <summary>
        /// 搜索日志
        /// </summary>
        /// <param name="criteria">搜索条件</param>
        /// <returns>搜索结果</returns>
        public async Task<LogSearchResult> SearchLogsAsync(LogSearchCriteria criteria)
        {
            if (criteria == null)
                throw new ArgumentNullException(nameof(criteria));
            
            try
            {
                var results = new List<LogEntry>();
                var searchStartTime = DateTime.UtcNow;
                
                // 搜索内存中的日志
                var memoryLogs = _logQueue.ToArray()
                    .Where(entry => MatchesCriteria(entry, criteria))
                    .ToList();
                
                results.AddRange(memoryLogs);
                
                // 搜索文件中的日志
                if (criteria.IncludeArchivedLogs)
                {
                    var fileLogs = await SearchLogFilesAsync(criteria);
                    results.AddRange(fileLogs);
                }
                
                // 排序和分页
                var sortedResults = criteria.SortDescending 
                    ? results.OrderByDescending(e => e.Timestamp)
                    : results.OrderBy(e => e.Timestamp);
                
                var pagedResults = sortedResults
                    .Skip(criteria.Skip)
                    .Take(criteria.Take)
                    .ToList();
                
                var searchDuration = DateTime.UtcNow - searchStartTime;
                
                return new LogSearchResult
                {
                    Entries = pagedResults,
                    TotalCount = results.Count,
                    SearchDuration = searchDuration,
                    Criteria = criteria,
                    Timestamp = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "SearchLogs");
                return new LogSearchResult
                {
                    Entries = new List<LogEntry>(),
                    TotalCount = 0,
                    SearchDuration = TimeSpan.Zero,
                    Criteria = criteria,
                    Error = ex.Message,
                    Timestamp = DateTime.UtcNow
                };
            }
        }
        
        /// <summary>
        /// 导出日志
        /// </summary>
        /// <param name="criteria">导出条件</param>
        /// <param name="format">导出格式</param>
        /// <param name="filePath">文件路径</param>
        /// <returns>导出任务</returns>
        public async Task<LogExportResult> ExportLogsAsync(LogSearchCriteria criteria, LogExportFormat format, string filePath)
        {
            if (criteria == null)
                throw new ArgumentNullException(nameof(criteria));
            
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));
            
            try
            {
                var exportStartTime = DateTime.UtcNow;
                
                // 搜索日志
                var searchResult = await SearchLogsAsync(criteria);
                
                if (searchResult.Entries == null || !searchResult.Entries.Any())
                {
                    return new LogExportResult
                    {
                        Success = false,
                        Error = "没有找到匹配的日志条目",
                        ExportedCount = 0,
                        FilePath = filePath,
                        Duration = DateTime.UtcNow - exportStartTime
                    };
                }
                
                // 确保目录存在
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
                
                // 导出日志
                await ExportLogsToFileAsync(searchResult.Entries, format, filePath);
                
                var exportDuration = DateTime.UtcNow - exportStartTime;
                
                await LogAsync(LogLevel.Information, 
                    $"日志导出完成: {searchResult.Entries.Count} 条记录导出到 {filePath}", 
                    null, new Dictionary<string, object>
                    {
                        ["ExportedCount"] = searchResult.Entries.Count,
                        ["ExportFormat"] = format.ToString(),
                        ["ExportDuration"] = exportDuration.TotalSeconds,
                        ["FilePath"] = filePath
                    }, "Export");
                
                return new LogExportResult
                {
                    Success = true,
                    ExportedCount = searchResult.Entries.Count,
                    FilePath = filePath,
                    Duration = exportDuration
                };
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "ExportLogs");
                
                var exportStartTime = DateTime.UtcNow;
                return new LogExportResult
                {
                    Success = false,
                    Error = ex.Message,
                    ExportedCount = 0,
                    FilePath = filePath,
                    Duration = DateTime.UtcNow - exportStartTime
                };
            }
        }
        
        /// <summary>
        /// 清理旧日志
        /// </summary>
        /// <param name="retentionDays">保留天数</param>
        /// <returns>清理结果</returns>
        public async Task<LogCleanupResult> CleanupOldLogsAsync(int retentionDays = 30)
        {
            if (retentionDays <= 0)
                throw new ArgumentException("保留天数必须大于0", nameof(retentionDays));
            
            try
            {
                var cleanupStartTime = DateTime.UtcNow;
                var cutoffDate = DateTime.UtcNow.AddDays(-retentionDays);
                
                var deletedFiles = new List<string>();
                var deletedSize = 0L;
                
                // 清理日志文件
                if (Directory.Exists(_logDirectory))
                {
                    var logFiles = Directory.GetFiles(_logDirectory, "*.log", SearchOption.AllDirectories)
                        .Where(f => File.GetCreationTime(f) < cutoffDate)
                        .ToList();
                    
                    foreach (var file in logFiles)
                    {
                        try
                        {
                            var fileInfo = new FileInfo(file);
                            deletedSize += fileInfo.Length;
                            
                            File.Delete(file);
                            deletedFiles.Add(file);
                        }
                        catch (Exception ex)
                        {
                            await LogAsync(LogLevel.Warning, $"删除日志文件失败: {file}", ex, null, "Cleanup");
                        }
                    }
                }
                
                var cleanupDuration = DateTime.UtcNow - cleanupStartTime;
                
                await LogAsync(LogLevel.Information, 
                    $"日志清理完成: 删除了 {deletedFiles.Count} 个文件，释放空间 {deletedSize / (1024.0 * 1024.0):F2} MB", 
                    null, new Dictionary<string, object>
                    {
                        ["DeletedFiles"] = deletedFiles.Count,
                        ["DeletedSizeMB"] = deletedSize / (1024.0 * 1024.0),
                        ["RetentionDays"] = retentionDays,
                        ["CleanupDuration"] = cleanupDuration.TotalSeconds
                    }, "Cleanup");
                
                return new LogCleanupResult
                {
                    Success = true,
                    DeletedFiles = deletedFiles,
                    DeletedSizeBytes = deletedSize,
                    Duration = cleanupDuration
                };
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "CleanupOldLogs");
                
                return new LogCleanupResult
                {
                    Success = false,
                    Error = ex.Message,
                    DeletedFiles = new List<string>(),
                    DeletedSizeBytes = 0,
                    Duration = TimeSpan.Zero
                };
            }
        }
        
        /// <summary>
        /// 刷新日志到文件
        /// </summary>
        /// <returns>刷新任务</returns>
        public async Task FlushAsync()
        {
            await FlushLogsAsync();
        }
        
        #endregion
        
        #region 私有方法
        
        /// <summary>
        /// 刷新日志到文件
        /// </summary>
        /// <returns>刷新任务</returns>
        private async Task FlushLogsAsync()
        {
            if (_isDisposed || _isProcessing)
                return;
            
            await _flushSemaphore.WaitAsync();
            try
            {
                _isProcessing = true;
                
                var config = _configurationManager.GetLoggingConfiguration();
                var logEntries = new List<LogEntry>();
                
                // 从队列中取出日志条目
                var maxBatchSize = Math.Min(_logQueue.Count, config.MaxBatchSize);
                for (int i = 0; i < maxBatchSize && _logQueue.TryDequeue(out var entry); i++)
                {
                    logEntries.Add(entry);
                }
                
                if (!logEntries.Any())
                    return;
                
                // 按日期分组写入文件
                var groupedEntries = logEntries.GroupBy(e => e.Timestamp.Date);
                
                foreach (var group in groupedEntries)
                {
                    await WriteLogEntriesToFileAsync(group.ToList(), group.Key);
                }
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "FlushLogs");
            }
            finally
            {
                _isProcessing = false;
                _flushSemaphore.Release();
            }
        }
        
        /// <summary>
        /// 写入日志条目到文件
        /// </summary>
        /// <param name="entries">日志条目</param>
        /// <param name="date">日期</param>
        /// <returns>写入任务</returns>
        private async Task WriteLogEntriesToFileAsync(List<LogEntry> entries, DateTime date)
        {
            if (!entries.Any())
                return;
            
            var fileName = $"mqtt-{date:yyyy-MM-dd}.log";
            var filePath = Path.Combine(_logDirectory, fileName);
            
            lock (_fileLock)
            {
                try
                {
                    using var writer = new StreamWriter(filePath, append: true, System.Text.Encoding.UTF8);
                    
                    foreach (var entry in entries)
                    {
                        var logLine = FormatLogEntry(entry);
                        writer.WriteLine(logLine);
                    }
                    
                    writer.Flush();
                }
                catch (Exception ex)
                {
                    // 避免递归调用
                    try
                    {
                        _logger.LogError(ex, "写入日志文件失败: {FilePath}", filePath);
                    }
                    catch
                    {
                        // 忽略
                    }
                }
            }
        }
        
        /// <summary>
        /// 格式化日志条目
        /// </summary>
        /// <param name="entry">日志条目</param>
        /// <returns>格式化后的日志行</returns>
        private string FormatLogEntry(LogEntry entry)
        {
            var config = _configurationManager.GetLoggingConfiguration();
            
            return config.LogFormat switch
            {
                LogFormat.Json => JsonSerializer.Serialize(entry, new JsonSerializerOptions
                {
                    WriteIndented = false,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                }),
                LogFormat.Structured => FormatStructuredLog(entry),
                _ => FormatSimpleLog(entry)
            };
        }
        
        /// <summary>
        /// 格式化结构化日志
        /// </summary>
        /// <param name="entry">日志条目</param>
        /// <returns>格式化后的日志</returns>
        private string FormatStructuredLog(LogEntry entry)
        {
            var parts = new List<string>
            {
                entry.Timestamp.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                $"[{entry.Level}]",
                $"[{entry.Category}]",
                $"[T{entry.ThreadId}]",
                entry.Message
            };
            
            if (entry.Exception != null)
            {
                parts.Add($"Exception: {entry.Exception}");
            }
            
            if (entry.Properties.Any())
            {
                var properties = string.Join(", ", entry.Properties.Select(p => $"{p.Key}={p.Value}"));
                parts.Add($"Properties: {{{properties}}}");
            }
            
            return string.Join(" | ", parts);
        }
        
        /// <summary>
        /// 格式化简单日志
        /// </summary>
        /// <param name="entry">日志条目</param>
        /// <returns>格式化后的日志</returns>
        private string FormatSimpleLog(LogEntry entry)
        {
            var message = $"{entry.Timestamp:yyyy-MM-dd HH:mm:ss} [{entry.Level}] {entry.Message}";
            
            if (entry.Exception != null)
            {
                message += $" | Exception: {entry.Exception.Message}";
            }
            
            return message;
        }
        
        /// <summary>
        /// 更新日志统计
        /// </summary>
        /// <param name="entry">日志条目</param>
        private void UpdateLogStatistics(LogEntry entry)
        {
            var key = $"{entry.Category}_{entry.Level}";
            
            _logStatistics.AddOrUpdate(key,
                new LogStatistics
                {
                    Category = entry.Category,
                    Level = entry.Level,
                    Count = 1,
                    FirstOccurrence = entry.Timestamp,
                    LastOccurrence = entry.Timestamp
                },
                (k, existing) =>
                {
                    existing.Count++;
                    existing.LastOccurrence = entry.Timestamp;
                    return existing;
                });
        }
        
        /// <summary>
        /// 计算平均每分钟日志条目数
        /// </summary>
        /// <returns>平均每分钟条目数</returns>
        private double CalculateAverageEntriesPerMinute()
        {
            var oldestStat = _logStatistics.Values.MinBy(s => s.FirstOccurrence);
            if (oldestStat == null)
                return 0.0;
            
            var duration = DateTime.UtcNow - oldestStat.FirstOccurrence;
            if (duration.TotalMinutes < 1)
                return TotalLogEntries;
            
            return TotalLogEntries / duration.TotalMinutes;
        }
        
        /// <summary>
        /// 检查日志条目是否匹配搜索条件
        /// </summary>
        /// <param name="entry">日志条目</param>
        /// <param name="criteria">搜索条件</param>
        /// <returns>是否匹配</returns>
        private bool MatchesCriteria(LogEntry entry, LogSearchCriteria criteria)
        {
            // 时间范围
            if (criteria.StartTime.HasValue && entry.Timestamp < criteria.StartTime.Value)
                return false;
            
            if (criteria.EndTime.HasValue && entry.Timestamp > criteria.EndTime.Value)
                return false;
            
            // 日志级别
            if (criteria.Levels != null && criteria.Levels.Any() && !criteria.Levels.Contains(entry.Level))
                return false;
            
            // 分类
            if (!string.IsNullOrWhiteSpace(criteria.Category) && 
                !entry.Category.Contains(criteria.Category, StringComparison.OrdinalIgnoreCase))
                return false;
            
            // 消息内容
            if (!string.IsNullOrWhiteSpace(criteria.MessageContains) && 
                !entry.Message.Contains(criteria.MessageContains, StringComparison.OrdinalIgnoreCase))
                return false;
            
            // 异常类型
            if (!string.IsNullOrWhiteSpace(criteria.ExceptionType) && 
                (entry.Exception == null || !entry.Exception.GetType().Name.Contains(criteria.ExceptionType, StringComparison.OrdinalIgnoreCase)))
                return false;
            
            return true;
        }
        
        /// <summary>
        /// 搜索日志文件
        /// </summary>
        /// <param name="criteria">搜索条件</param>
        /// <returns>搜索结果</returns>
        private async Task<List<LogEntry>> SearchLogFilesAsync(LogSearchCriteria criteria)
        {
            var results = new List<LogEntry>();
            
            if (!Directory.Exists(_logDirectory))
                return results;
            
            var logFiles = Directory.GetFiles(_logDirectory, "*.log", SearchOption.TopDirectoryOnly)
                .OrderByDescending(f => File.GetCreationTime(f))
                .ToList();
            
            foreach (var file in logFiles)
            {
                try
                {
                    var fileEntries = await ReadLogFileAsync(file, criteria);
                    results.AddRange(fileEntries);
                    
                    // 限制搜索结果数量
                    if (results.Count >= criteria.Take * 2)
                        break;
                }
                catch (Exception ex)
                {
                    await _exceptionHandler.HandleExceptionAsync(ex, $"SearchLogFile_{file}");
                }
            }
            
            return results;
        }
        
        /// <summary>
        /// 读取日志文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="criteria">搜索条件</param>
        /// <returns>日志条目</returns>
        private async Task<List<LogEntry>> ReadLogFileAsync(string filePath, LogSearchCriteria criteria)
        {
            var entries = new List<LogEntry>();
            
            using var reader = new StreamReader(filePath, System.Text.Encoding.UTF8);
            string line;
            
            while ((line = await reader.ReadLineAsync()) != null)
            {
                try
                {
                    var entry = ParseLogLine(line);
                    if (entry != null && MatchesCriteria(entry, criteria))
                    {
                        entries.Add(entry);
                    }
                }
                catch
                {
                    // 忽略解析错误的行
                }
            }
            
            return entries;
        }
        
        /// <summary>
        /// 解析日志行
        /// </summary>
        /// <param name="line">日志行</param>
        /// <returns>日志条目</returns>
        private LogEntry ParseLogLine(string line)
        {
            if (string.IsNullOrWhiteSpace(line))
                return null;
            
            try
            {
                // 尝试解析JSON格式
                if (line.StartsWith("{"))
                {
                    return JsonSerializer.Deserialize<LogEntry>(line, new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    });
                }
                
                // 解析结构化格式
                return ParseStructuredLogLine(line);
            }
            catch
            {
                return null;
            }
        }
        
        /// <summary>
        /// 解析结构化日志行
        /// </summary>
        /// <param name="line">日志行</param>
        /// <returns>日志条目</returns>
        private LogEntry ParseStructuredLogLine(string line)
        {
            // 简单的结构化日志解析
            var parts = line.Split(" | ");
            if (parts.Length < 4)
                return null;
            
            var timestampStr = parts[0];
            var levelStr = parts[1].Trim('[', ']');
            var categoryStr = parts[2].Trim('[', ']');
            var message = parts[3];
            
            if (!DateTime.TryParse(timestampStr, out var timestamp) ||
                !Enum.TryParse<LogLevel>(levelStr, out var level))
                return null;
            
            return new LogEntry
            {
                Id = Guid.NewGuid().ToString("N"),
                Timestamp = timestamp,
                Level = level,
                Category = categoryStr,
                Message = message,
                Properties = new Dictionary<string, object>()
            };
        }
        
        /// <summary>
        /// 导出日志到文件
        /// </summary>
        /// <param name="entries">日志条目</param>
        /// <param name="format">导出格式</param>
        /// <param name="filePath">文件路径</param>
        /// <returns>导出任务</returns>
        private async Task ExportLogsToFileAsync(IEnumerable<LogEntry> entries, LogExportFormat format, string filePath)
        {
            using var writer = new StreamWriter(filePath, false, System.Text.Encoding.UTF8);
            
            switch (format)
            {
                case LogExportFormat.Json:
                    await writer.WriteLineAsync(JsonSerializer.Serialize(entries, new JsonSerializerOptions
                    {
                        WriteIndented = true,
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    }));
                    break;
                
                case LogExportFormat.Csv:
                    await ExportToCsvAsync(writer, entries);
                    break;
                
                case LogExportFormat.Text:
                default:
                    foreach (var entry in entries)
                    {
                        await writer.WriteLineAsync(FormatLogEntry(entry));
                    }
                    break;
            }
        }
        
        /// <summary>
        /// 导出到CSV格式
        /// </summary>
        /// <param name="writer">写入器</param>
        /// <param name="entries">日志条目</param>
        /// <returns>导出任务</returns>
        private async Task ExportToCsvAsync(StreamWriter writer, IEnumerable<LogEntry> entries)
        {
            // 写入CSV头
            await writer.WriteLineAsync("Timestamp,Level,Category,Message,Exception,ThreadId,ProcessId,MachineName");
            
            // 写入数据行
            foreach (var entry in entries)
            {
                var csvLine = $"\"{entry.Timestamp:yyyy-MM-dd HH:mm:ss.fff}\"," +
                             $"\"{entry.Level}\"," +
                             $"\"{entry.Category}\"," +
                             $"\"{EscapeCsvValue(entry.Message)}\"," +
                             $"\"{EscapeCsvValue(entry.Exception?.Message)}\"," +
                             $"{entry.ThreadId}," +
                             $"{entry.ProcessId}," +
                             $"\"{entry.MachineName}\"";
                
                await writer.WriteLineAsync(csvLine);
            }
        }
        
        /// <summary>
        /// 转义CSV值
        /// </summary>
        /// <param name="value">值</param>
        /// <returns>转义后的值</returns>
        private string EscapeCsvValue(string value)
        {
            if (string.IsNullOrEmpty(value))
                return string.Empty;
            
            return value.Replace("\"", "\"\"").Replace("\n", " ").Replace("\r", " ");
        }
        
        /// <summary>
        /// 刷新回调
        /// </summary>
        /// <param name="state">状态对象</param>
        private async void FlushCallback(object state)
        {
            if (_isDisposed)
                return;
            
            await FlushLogsAsync();
        }
        
        /// <summary>
        /// 统计回调
        /// </summary>
        /// <param name="state">状态对象</param>
        private async void StatisticsCallback(object state)
        {
            if (_isDisposed)
                return;
            
            try
            {
                var statistics = GetStatistics();
                
                // 触发统计更新事件
                StatisticsUpdated?.Invoke(this, new LogStatisticsUpdatedEventArgs
                {
                    Statistics = statistics,
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "LogStatisticsCallback");
            }
        }
        
        /// <summary>
        /// 清理回调
        /// </summary>
        /// <param name="state">状态对象</param>
        private async void CleanupCallback(object state)
        {
            if (_isDisposed)
                return;
            
            try
            {
                var config = _configurationManager.GetLoggingConfiguration();
                
                // 自动清理旧日志
                if (config.AutoCleanupEnabled && config.LogRetentionDays > 0)
                {
                    await CleanupOldLogsAsync(config.LogRetentionDays);
                }
                
                // 清理过期统计
                var cutoffTime = DateTime.UtcNow.AddDays(-7);
                var expiredStats = _logStatistics
                    .Where(kvp => kvp.Value.LastOccurrence < cutoffTime)
                    .Select(kvp => kvp.Key)
                    .ToList();
                
                foreach (var key in expiredStats)
                {
                    _logStatistics.TryRemove(key, out _);
                }
                
                // 清理过期性能计数器
                var expiredCounters = _performanceCounters
                    .Where(kvp => kvp.Value.LastUpdated < cutoffTime)
                    .Select(kvp => kvp.Key)
                    .ToList();
                
                foreach (var key in expiredCounters)
                {
                    _performanceCounters.TryRemove(key, out _);
                }
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "LogCleanupCallback");
            }
        }
        
        #endregion
        
        #region IDisposable实现
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed)
                return;
            
            _isDisposed = true;
            
            try
            {
                // 最后一次刷新日志
                FlushLogsAsync().Wait(TimeSpan.FromSeconds(5));
                
                // 停止定时器
                _flushTimer?.Dispose();
                _statisticsTimer?.Dispose();
                _cleanupTimer?.Dispose();
                
                // 释放信号量
                _flushSemaphore?.Dispose();
                
                _logger.LogInformation("日志服务已释放资源");
            }
            catch (Exception ex)
            {
                try
                {
                    _logger.LogError(ex, "释放日志服务资源时发生错误");
                }
                catch
                {
                    // 忽略
                }
            }
        }
        
        #endregion
    }
    
    #region 辅助类
    
    /// <summary>
    /// 性能计时器实现
    /// </summary>
    internal class PerformanceTimer : IPerformanceTimer
    {
        private readonly LoggingService _loggingService;
        private readonly string _name;
        private readonly Dictionary<string, object> _properties;
        private readonly Stopwatch _stopwatch;
        private readonly DateTime _startTime;
        private bool _disposed;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="loggingService">日志服务</param>
        /// <param name="name">计时器名称</param>
        /// <param name="properties">附加属性</param>
        public PerformanceTimer(LoggingService loggingService, string name, Dictionary<string, object> properties)
        {
            _loggingService = loggingService;
            _name = name;
            _properties = properties ?? new Dictionary<string, object>();
            _startTime = DateTime.UtcNow;
            _stopwatch = Stopwatch.StartNew();
        }
        
        /// <summary>
        /// 操作名称
        /// </summary>
        public string OperationName => _name;
        
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime => _startTime;
        
        /// <summary>
        /// 已经过时间
        /// </summary>
        public TimeSpan Elapsed => _stopwatch.Elapsed;
        
        /// <summary>
        /// 停止计时并记录结果
        /// </summary>
        public void Stop()
        {
            if (!_disposed && _stopwatch.IsRunning)
            {
                _stopwatch.Stop();
                _ = Task.Run(async () => await _loggingService.LogPerformanceAsync(_name, _stopwatch.Elapsed.TotalMilliseconds, "ms", _properties));
            }
        }
        
        /// <summary>
        /// 停止计时并记录性能指标
        /// </summary>
        /// <returns>停止任务</returns>
        public async Task StopAsync()
        {
            if (_disposed)
                return;
            
            _stopwatch.Stop();
            
            await _loggingService.LogPerformanceAsync(_name, _stopwatch.Elapsed.TotalMilliseconds, "ms", _properties);
            
            _disposed = true;
        }
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                StopAsync().Wait();
            }
        }
    }
    
    #endregion
    
    #region 事件参数类
    
    /// <summary>
    /// 日志条目写入事件参数
    /// </summary>
    public class LogEntryWrittenEventArgs : EventArgs
    {
        /// <summary>
        /// 日志条目
        /// </summary>
        public LogEntry LogEntry { get; set; }
        
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
    
    /// <summary>
    /// 日志错误事件参数
    /// </summary>
    public class LogErrorEventArgs : EventArgs
    {
        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception Exception { get; set; }
        
        /// <summary>
        /// 原始消息
        /// </summary>
        public string OriginalMessage { get; set; }
        
        /// <summary>
        /// 原始日志级别
        /// </summary>
        public LogLevel OriginalLevel { get; set; }
        
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
    
    /// <summary>
    /// 性能警告事件参数
    /// </summary>
    public class PerformanceWarningEventArgs : EventArgs
    {
        /// <summary>
        /// 指标名称
        /// </summary>
        public string MetricName { get; set; }
        
        /// <summary>
        /// 当前值
        /// </summary>
        public double CurrentValue { get; set; }
        
        /// <summary>
        /// 阈值
        /// </summary>
        public double ThresholdValue { get; set; }
        
        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }
        
        /// <summary>
        /// 性能计数器
        /// </summary>
        public PerformanceCounter Counter { get; set; }
        
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
    
    /// <summary>
    /// 日志统计更新事件参数
    /// </summary>
    public class LogStatisticsUpdatedEventArgs : EventArgs
    {
        /// <summary>
        /// 统计信息
        /// </summary>
        public LogStatisticsInfo Statistics { get; set; }
        
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
    
    #endregion
}