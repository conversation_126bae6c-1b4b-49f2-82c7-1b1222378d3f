// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.Drawing;

namespace Admin.NET.Core.Service;

/// <summary>
/// 节能设备管理服务 🧩
/// </summary>
[ApiDescriptionSettings(Order = 500)]
public class EnergyDeviceService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<EnergyDevice> _energyDeviceRep;
    private readonly SqlSugarRepository<EnergyDeviceGroup> _energyDeviceGroupRep;
    private readonly SysCacheService _sysCacheService;
    private readonly UserManager _userManager;

    public EnergyDeviceService(
        SqlSugarRepository<EnergyDevice> energyDeviceRep,
        SqlSugarRepository<EnergyDeviceGroup> energyDeviceGroupRep,
        SysCacheService sysCacheService,
        UserManager userManager)
    {
        _energyDeviceRep = energyDeviceRep;
        _energyDeviceGroupRep = energyDeviceGroupRep;
        _sysCacheService = sysCacheService;
        _userManager = userManager;
    }

    /// <summary>
    /// 获取设备分页列表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取设备分页列表")]
    public async Task<SqlSugarPagedList<EnergyDeviceOutput>> GetPage([FromQuery] EnergyDeviceInput input)
    {
        var query = _energyDeviceRep.AsQueryable()
            .LeftJoin<EnergyDeviceGroup>((d, g) => d.GroupId == g.Id)
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeviceCode), (d, g) => d.DeviceCode.Contains(input.DeviceCode))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeviceName), (d, g) => d.DeviceName.Contains(input.DeviceName))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeviceType), (d, g) => d.DeviceType == input.DeviceType)
            .WhereIF(input.Status > 0, (d, g) => d.Status == input.Status)
            .WhereIF(input.GroupId > 0, (d, g) => d.GroupId == input.GroupId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Location), (d, g) => d.Location.Contains(input.Location))
            .WhereIF(input.OnlineStatus.HasValue, (d, g) => d.IsOnline == (input.OnlineStatus == 1))
            .Select((d, g) => new EnergyDeviceOutput
            {
                Id = d.Id,
                DeviceCode = d.DeviceCode,
                DeviceName = d.DeviceName,
                DeviceType = d.DeviceType,
                DeviceModel = d.DeviceModel,
                Manufacturer = d.Manufacturer,
                Location = d.Location,
                InstallDate = d.InstallDate,
                PowerRating = d.PowerRating,
                VoltageRating = d.Voltage,
                CurrentRating = null,
                GroupId = d.GroupId,
                GroupName = g.GroupName,
                Status = d.Status,
                IsOnline = d.IsOnline,
                LastOnlineTime = null,
                Brightness = d.Brightness,
                Voltage = d.Voltage,
                FaultCount = d.FaultCount,
                WarrantyExpiry = d.WarrantyExpiry,

                CreateTime = d.CreateTime,
                TenantId = d.TenantId
            });

        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取设备列表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取设备列表")]
    public async Task<List<EnergyDeviceOutput>> GetList([FromQuery] EnergyDeviceInput input)
    {
        var query = _energyDeviceRep.AsQueryable()
            .LeftJoin<EnergyDeviceGroup>((d, g) => d.GroupId == g.Id)
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeviceCode), (d, g) => d.DeviceCode.Contains(input.DeviceCode))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeviceName), (d, g) => d.DeviceName.Contains(input.DeviceName))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeviceType), (d, g) => d.DeviceType == input.DeviceType)
            .WhereIF(input.Status > 0, (d, g) => d.Status == input.Status)
            .WhereIF(input.GroupId > 0, (d, g) => d.GroupId == input.GroupId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Location), (d, g) => d.Location.Contains(input.Location))
            .WhereIF(input.OnlineStatus.HasValue, (d, g) => d.IsOnline == (input.OnlineStatus == 1))
            .Select<EnergyDeviceOutput>((d, g) => new EnergyDeviceOutput
            {
                Id = d.Id,
                DeviceCode = d.DeviceCode,
                DeviceName = d.DeviceName,
                DeviceType = d.DeviceType,
                DeviceModel = d.DeviceModel,
                Manufacturer = d.Manufacturer,
                Location = d.Location,
                InstallDate = d.InstallDate,
                PowerRating = d.PowerRating,
                VoltageRating = d.Voltage,
                CurrentRating = null,
                GroupId = d.GroupId,
                GroupName = g.GroupName,
                Status = d.Status,
                IsOnline = d.IsOnline,
                LastOnlineTime = null,
                Brightness = d.Brightness,
                Temperature = null, // 实时数据，需要从设备获取
                Humidity = null, // 实时数据，需要从设备获取
                PowerConsumption = null, // 实时数据，需要从设备获取
                Voltage = d.Voltage,
                Current = null, // 实时数据，需要从设备获取
                PowerFactor = null, // 实时数据，需要从设备获取
                FaultCount = d.FaultCount,
                MaintenanceDate = null, // 维护日期，需要单独管理
                WarrantyExpiry = d.WarrantyExpiry,
                CreateTime = d.CreateTime,
                TenantId = d.TenantId
            })
            .OrderBy(d => d.CreateTime, OrderByType.Desc);

        return await query.ToListAsync();
    }

    /// <summary>
    /// 获取设备详情 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取设备详情")]
    public async Task<EnergyDeviceDetailOutput> GetDetail([FromQuery] BaseIdInput input)
    {
        var device = await _energyDeviceRep.AsQueryable()
            .LeftJoin<EnergyDeviceGroup>((d, g) => d.GroupId == g.Id)
            .Where((d, g) => d.Id == input.Id)
            .Select((d, g) => new EnergyDeviceDetailOutput
            {
                Id = d.Id,
                DeviceCode = d.DeviceCode,
                DeviceName = d.DeviceName,
                DeviceType = d.DeviceType,
                DeviceModel = d.DeviceModel,
                Manufacturer = d.Manufacturer,
                Location = d.Location,
                InstallDate = d.InstallDate,
                PowerRating = d.PowerRating,
                VoltageRating = d.Voltage,
                CurrentRating = null,
                GroupId = d.GroupId,
                GroupName = g.GroupName,
                Status = d.Status,
                IsOnline = d.IsOnline,
                LastOnlineTime = null,
                Brightness = d.Brightness,
                Voltage = d.Voltage,
                Frequency = d.Frequency,
                RunningTime = d.RunningTime,
                FaultCount = d.FaultCount,
                LastFaultTime = d.LastFaultTime,
                CreateTime = d.CreateTime,
                TenantId = d.TenantId
            })
            .FirstAsync();

        if (device == null)
            throw Oops.Oh("设备不存在");

        // 计算运行状态统计
        var now = DateTime.Now;
        device.TodayRunningTime = device.IsOnline == true ? 0 : 0; // LastOnlineTime not available
        device.MonthRunningTime = 0;
        device.TotalRunningTime = 0;

        return device;
    }

    /// <summary>
    /// 增加设备 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [DisplayName("增加设备")]
    public async Task<long> AddDevice(AddEnergyDeviceInput input)
    {
        // 检查设备编码是否存在
        var isExist = await _energyDeviceRep.IsAnyAsync(u => u.DeviceCode == input.DeviceCode);
        if (isExist)
            throw Oops.Oh("设备编码已存在");

        // 检查分组是否存在
        if (input.GroupId > 0)
        {
            var groupExist = await _energyDeviceGroupRep.IsAnyAsync(u => u.Id == input.GroupId);
            if (!groupExist)
                throw Oops.Oh("设备分组不存在");
        }

        var device = input.Adapt<EnergyDevice>();
        device.TenantId = _userManager.TenantId;
        device.CreateUserId = _userManager.UserId;
        device.CreateTime = DateTime.Now;
        device.IsOnline = false;
        device.Status = 1; // 1表示正常状态

        var newDevice = await _energyDeviceRep.AsInsertable(device).ExecuteReturnEntityAsync();
        return newDevice.Id;
    }

    /// <summary>
    /// 更新设备 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [DisplayName("更新设备")]
    public async Task UpdateDevice(UpdateEnergyDeviceInput input)
    {
        // 检查设备是否存在
        var device = await _energyDeviceRep.GetByIdAsync(input.Id);
        if (device == null)
            throw Oops.Oh("设备不存在");

        // 检查设备编码是否重复
        var isExist = await _energyDeviceRep.IsAnyAsync(u => u.DeviceCode == input.DeviceCode && u.Id != input.Id);
        if (isExist)
            throw Oops.Oh("设备编码已存在");

        // 检查分组是否存在
        if (input.GroupId > 0)
        {
            var groupExist = await _energyDeviceGroupRep.IsAnyAsync(u => u.Id == input.GroupId);
            if (!groupExist)
                throw Oops.Oh("设备分组不存在");
        }

        var updateDevice = input.Adapt<EnergyDevice>();
        updateDevice.UpdateUserId = _userManager.UserId;
        updateDevice.UpdateTime = DateTime.Now;

        await _energyDeviceRep.AsUpdateable(updateDevice).ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除设备 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    [DisplayName("删除设备")]
    public async Task DeleteDevice(DeleteEnergyDeviceInput input)
    {
        var device = await _energyDeviceRep.GetByIdAsync(input.Id);
        if (device == null)
            throw Oops.Oh("设备不存在");

        // 软删除设备
        await _energyDeviceRep.FakeDeleteAsync(device);
    }

    /// <summary>
    /// 设置设备状态 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置设备状态")]
    public async Task<int> SetStatus(EnergyDeviceStatusInput input)
    {
        var device = await _energyDeviceRep.GetByIdAsync(input.Id);
        if (device == null)
            throw Oops.Oh("设备不存在");

        device.Status = input.Status;
        device.UpdateUserId = _userManager.UserId;
        device.UpdateTime = DateTime.Now;

        return await _energyDeviceRep.AsUpdateable(device)
            .UpdateColumns(u => new { u.Status, u.UpdateUserId, u.UpdateTime })
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 设备控制 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设备控制")]
    public async Task<bool> ControlDevice(DeviceControlInput input)
    {
        var device = await _energyDeviceRep.GetByIdAsync(input.DeviceId);
        if (device == null)
            throw Oops.Oh("设备不存在");

        if (!device.IsOnline)
            throw Oops.Oh("设备离线，无法控制");

        // TODO: 实现具体的设备控制逻辑，如MQTT消息发送
        // 这里可以集成MQTT客户端发送控制指令到设备

        // 更新设备状态
        if (input.ControlType == "switch")
        {
            device.Status = input.Value == "on" ? 1 : 2; // 1正常 2故障
        }
        else if (input.ControlType == "brightness")
        {
            if (int.TryParse(input.Value, out int brightness))
            {
                device.Brightness = brightness;
            }
        }

        device.UpdateUserId = _userManager.UserId;
        device.UpdateTime = DateTime.Now;

        await _energyDeviceRep.AsUpdateable(device).ExecuteCommandAsync();

        return true;
    }

    /// <summary>
    /// 获取设备统计信息 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取设备统计信息")]
    public async Task<EnergyDeviceStatOutput> GetDeviceStat()
    {
        var totalCount = await _energyDeviceRep.CountAsync(u => true);
        var onlineCount = await _energyDeviceRep.CountAsync(u => u.IsOnline == true);
        var offlineCount = totalCount - onlineCount;
        var faultCount = await _energyDeviceRep.CountAsync(u => u.FaultCount > 0);
        var enableCount = await _energyDeviceRep.CountAsync(u => u.Status == 1);
        var disableCount = await _energyDeviceRep.CountAsync(u => u.Status == 2);

        return new EnergyDeviceStatOutput
        {
            TotalCount = totalCount,
            OnlineCount = onlineCount,
            OfflineCount = offlineCount,
            FaultCount = faultCount,
            EnableCount = enableCount,
            DisableCount = disableCount,
            OnlineRate = totalCount > 0 ? (decimal)Math.Round((double)onlineCount / totalCount * 100, 2) : 0,
            FaultRate = totalCount > 0 ? (decimal)Math.Round((double)faultCount / totalCount * 100, 2) : 0
        };
    }

    /// <summary>
    /// 获取设备状态统计 🔖
    /// </summary>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Stats"), HttpGet]
    [DisplayName("获取设备状态统计")]
    public async Task<dynamic> GetStats()
    {
        var totalCount = await _energyDeviceRep.CountAsync(u => u.TenantId == _userManager.TenantId);
        var onlineCount = await _energyDeviceRep.CountAsync(u => u.IsOnline == true && u.TenantId == _userManager.TenantId);
        var offlineCount = await _energyDeviceRep.CountAsync(u => u.IsOnline == false && u.TenantId == _userManager.TenantId);
        var faultCount = await _energyDeviceRep.CountAsync(u => u.Status == 2 && u.TenantId == _userManager.TenantId);

        return new
        {
            total = totalCount,
            online = onlineCount,
            offline = offlineCount,
            fault = faultCount
        };
    }

    /// <summary>
    /// 获取设备类型分布 🔖
    /// </summary>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "TypeDistribution"), HttpGet]
    [DisplayName("获取设备类型分布")]
    public async Task<List<dynamic>> GetTypeDistribution()
    {
        var typeDistribution = await _energyDeviceRep.AsQueryable()
            .Where(u => u.TenantId == _userManager.TenantId)
            .GroupBy(u => u.DeviceType)
            .Select(g => new
            {
                name = g.DeviceType,
                value = SqlFunc.AggregateCount(g.DeviceType)
            })
            .ToListAsync();

        return typeDistribution.Cast<dynamic>().ToList();
    }

    /// <summary>
    /// 导出设备数据 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出设备数据")]
    public async Task<byte[]> ExportData([FromQuery] EnergyDeviceInput input)
    {
        var data = await _energyDeviceRep.AsQueryable()
            .LeftJoin<EnergyDeviceGroup>((d, g) => d.GroupId == g.Id)
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeviceCode), (d, g) => d.DeviceCode.Contains(input.DeviceCode))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeviceName), (d, g) => d.DeviceName.Contains(input.DeviceName))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeviceType), (d, g) => d.DeviceType == input.DeviceType)
            .WhereIF(input.Status > 0, (d, g) => d.Status == input.Status)
            .WhereIF(input.GroupId > 0, (d, g) => d.GroupId == input.GroupId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Location), (d, g) => d.Location.Contains(input.Location))
            .WhereIF(input.OnlineStatus.HasValue, (d, g) => d.IsOnline == (input.OnlineStatus == 1))
            .OrderBy((d, g) => d.CreateTime, OrderByType.Desc)
            .Select((d, g) => new
            {
                设备编码 = d.DeviceCode,
                设备名称 = d.DeviceName,
                设备类型 = d.DeviceType,
                设备型号 = d.DeviceModel,
                制造商 = d.Manufacturer,
                安装位置 = d.Location,
                设备分组 = g.GroupName,
                额定功率_W = d.PowerRating,
                额定电压_V = d.Voltage,
                亮度_Percent = d.Brightness,
                安装日期 = d.InstallDate,
                保修到期日 = d.WarrantyExpiry,
                设备状态 = d.Status == 1 ? "正常" : d.Status == 2 ? "故障" : d.Status == 3 ? "离线" : "维护中",
                在线状态 = d.IsOnline == true ? "在线" : "离线",
                故障次数 = d.FaultCount,
                创建时间 = d.CreateTime
            })
            .ToListAsync();

        // 实现Excel导出功能
        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add("设备数据");
        
        // 设置表头
        var headers = new string[]
        {
            "设备编码", "设备名称", "设备类型", "设备型号", "制造商", "安装位置", "设备分组", 
            "额定功率(W)", "额定电压(V)", "亮度(%)", "安装日期", "保修到期日", "设备状态", "在线状态", "故障次数", "创建时间"
        };
        
        for (int i = 0; i < headers.Length; i++)
        {
            worksheet.Cells[1, i + 1].Value = headers[i];
            worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            worksheet.Cells[1, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
            worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(211, 211, 211, 255);
        }
        
        // 填充数据
        for (int row = 0; row < data.Count; row++)
        {
            var item = data[row];
            worksheet.Cells[row + 2, 1].Value = item.设备编码;
            worksheet.Cells[row + 2, 2].Value = item.设备名称;
            worksheet.Cells[row + 2, 3].Value = item.设备类型;
            worksheet.Cells[row + 2, 4].Value = item.设备型号;
            worksheet.Cells[row + 2, 5].Value = item.制造商;
            worksheet.Cells[row + 2, 6].Value = item.安装位置;
            worksheet.Cells[row + 2, 7].Value = item.设备分组;
            worksheet.Cells[row + 2, 8].Value = item.额定功率_W;
            worksheet.Cells[row + 2, 9].Value = item.额定电压_V;
            worksheet.Cells[row + 2, 10].Value = item.亮度_Percent;
            worksheet.Cells[row + 2, 11].Value = item.安装日期?.ToString("yyyy-MM-dd") ?? "";
            worksheet.Cells[row + 2, 12].Value = item.保修到期日?.ToString("yyyy-MM-dd") ?? "";
            worksheet.Cells[row + 2, 13].Value = item.设备状态;
            worksheet.Cells[row + 2, 14].Value = item.在线状态;
            worksheet.Cells[row + 2, 15].Value = item.故障次数;
            worksheet.Cells[row + 2, 16].Value = item.创建时间.ToString("yyyy-MM-dd HH:mm:ss");
        }
        
        // 自动调整列宽
        worksheet.Cells.AutoFitColumns();
        
        return package.GetAsByteArray();
    }

    /// <summary>
    /// 导出设备数据到Excel 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "ExportExcel"), HttpPost]
    [DisplayName("导出设备数据到Excel")]
    public async Task<IActionResult> ExportExcel(EnergyDeviceInput input)
    {
        var excelData = await ExportData(input);
        var fileName = $"设备数据_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
        
        return new FileContentResult(excelData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        {
            FileDownloadName = fileName
        };
    }
}