<template>
  <view class="integration-test">
    <view class="test-header">
      <text class="test-title">组件集成测试</text>
      <text class="test-subtitle">验证所有组件和模块的正常工作</text>
    </view>

    <!-- 状态管理测试 -->
    <view class="test-section">
      <view class="section-title">状态管理测试</view>
      <view class="test-item">
        <text>用户登录状态: {{ userLoginStatus }}</text>
        <button @click="toggleLoginStatus" class="test-btn">切换状态</button>
      </view>
      <view class="test-item">
        <text>设备总数: {{ deviceCount }}</text>
        <button @click="updateDeviceCount" class="test-btn">更新数量</button>
      </view>
      <view class="test-item">
        <text>系统主题: {{ systemTheme }}</text>
        <button @click="toggleTheme" class="test-btn">切换主题</button>
      </view>
    </view>

    <!-- 组件测试 -->
    <view class="test-section">
      <view class="section-title">组件测试</view>
      
      <!-- StatusBadge 测试 -->
      <view class="test-item">
        <text class="test-label">状态标签:</text>
        <StatusBadge status="online" text="在线" />
        <StatusBadge status="offline" text="离线" />
        <StatusBadge status="fault" text="故障" />
      </view>

      <!-- LoadingSpinner 测试 -->
      <view class="test-item">
        <text class="test-label">加载动画:</text>
        <LoadingSpinner v-if="showLoading" type="circle" text="加载中..." />
        <button @click="toggleLoading" class="test-btn">
          {{ showLoading ? '隐藏' : '显示' }}加载
        </button>
      </view>

      <!-- DeviceCard 测试 -->
      <view class="test-item">
        <text class="test-label">设备卡片:</text>
        <DeviceCard 
          :device="testDevice" 
          size="medium"
          @click="onDeviceClick"
          @switch="onDeviceSwitch"
        />
      </view>

      <!-- FilterBar 测试 -->
      <view class="test-item">
        <text class="test-label">筛选条件:</text>
        <FilterBar 
          :filters="testFilters"
          @search="onSearch"
          @filter="onFilter"
          @sort="onSort"
        />
      </view>

      <!-- ChartContainer 测试 -->
      <view class="test-item">
        <text class="test-label">图表容器:</text>
        <ChartContainer 
          title="能耗统计"
          :loading="chartLoading"
          @refresh="onChartRefresh"
        >
          <view class="mock-chart">
            <text>模拟图表内容</text>
          </view>
        </ChartContainer>
      </view>
    </view>

    <!-- API测试 -->
    <view class="test-section">
      <view class="section-title">API测试</view>
      <view class="test-item">
        <button @click="testGetRequest" class="test-btn">测试GET请求</button>
        <button @click="testPostRequest" class="test-btn">测试POST请求</button>
        <button @click="testCacheRequest" class="test-btn">测试缓存</button>
      </view>
      <view class="api-result" v-if="apiResult">
        <text class="result-title">API响应:</text>
        <text class="result-content">{{ apiResult }}</text>
      </view>
    </view>

    <!-- 工具函数测试 -->
    <view class="test-section">
      <view class="section-title">工具函数测试</view>
      <view class="test-item">
        <button @click="testStorage" class="test-btn">测试存储</button>
        <button @click="testValidation" class="test-btn">测试验证</button>
        <button @click="testFormat" class="test-btn">测试格式化</button>
      </view>
      <view class="util-result" v-if="utilResult">
        <text class="result-title">工具测试结果:</text>
        <text class="result-content">{{ utilResult }}</text>
      </view>
    </view>

    <!-- 确认对话框测试 -->
    <view class="test-section">
      <view class="section-title">对话框测试</view>
      <view class="test-item">
        <button @click="showConfirmDialog" class="test-btn">显示确认对话框</button>
      </view>
    </view>

    <!-- ConfirmDialog 组件 -->
    <ConfirmDialog 
      v-model:visible="dialogVisible"
      title="测试对话框"
      content="这是一个测试对话框，确认要继续吗？"
      @confirm="onDialogConfirm"
      @cancel="onDialogCancel"
    />
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnload } from 'vue'
import { useStore } from 'vuex'
import StatusBadge from '../../components/StatusBadge.vue'
import LoadingSpinner from '../../components/LoadingSpinner.vue'
import DeviceCard from '../../components/DeviceCard.vue'
import FilterBar from '../../components/FilterBar.vue'
import ChartContainer from '../../components/ChartContainer.vue'
import ConfirmDialog from '../../components/ConfirmDialog.vue'
import { get, post } from '../../utils/request.js'
import * as utils from '../../utils/index.js'

// Vuex store
const store = useStore()

// 响应式数据
const showLoading = ref(false)
const chartLoading = ref(false)
const dialogVisible = ref(false)
const apiResult = ref('')
const utilResult = ref('')

// 计算属性
const userLoginStatus = computed(() => store.getters['user/isLoggedIn'] ? '已登录' : '未登录')
const deviceCount = computed(() => store.getters['device/totalCount'])
const systemTheme = computed(() => store.getters['system/theme'])

// 测试数据
const testDevice = ref({
  id: '001',
  name: '测试设备',
  location: '办公室A区',
  type: 'light',
  status: 'online',
  power: 85,
  energy: 12.5,
  brightness: 75,
  switch: true
})

const testFilters = ref([
  {
    key: 'status',
    label: '状态',
    type: 'select',
    options: [
      { label: '全部', value: '' },
      { label: '在线', value: 'online' },
      { label: '离线', value: 'offline' }
    ]
  },
  {
    key: 'type',
    label: '类型',
    type: 'select',
    options: [
      { label: '全部', value: '' },
      { label: '照明', value: 'light' },
      { label: '传感器', value: 'sensor' }
    ]
  }
])

// 方法
const toggleLoginStatus = () => {
  if (store.getters['user/isLoggedIn']) {
    store.dispatch('user/logout')
  } else {
    store.dispatch('user/login', {
      username: 'test',
      password: 'test123'
    })
  }
}

const updateDeviceCount = () => {
  store.commit('device/SET_DEVICE_LIST', [
    ...store.state.device.deviceList,
    {
      id: Date.now().toString(),
      name: `新设备${Date.now()}`,
      status: 'online'
    }
  ])
}

const toggleTheme = () => {
  const currentTheme = store.getters['system/theme']
  const newTheme = currentTheme === 'light' ? 'dark' : 'light'
  store.dispatch('system/toggleTheme', newTheme)
}

const toggleLoading = () => {
  showLoading.value = !showLoading.value
}

const onDeviceClick = (device) => {
  uni.showToast({
    title: `点击了设备: ${device.name}`,
    icon: 'none'
  })
}

const onDeviceSwitch = (device, value) => {
  uni.showToast({
    title: `设备${device.name}开关: ${value ? '开' : '关'}`,
    icon: 'none'
  })
}

const onSearch = (keyword) => {
  uni.showToast({
    title: `搜索: ${keyword}`,
    icon: 'none'
  })
}

const onFilter = (filters) => {
  uni.showToast({
    title: `筛选: ${JSON.stringify(filters)}`,
    icon: 'none'
  })
}

const onSort = (sort) => {
  uni.showToast({
    title: `排序: ${sort.field} ${sort.order}`,
    icon: 'none'
  })
}

const onChartRefresh = () => {
  chartLoading.value = true
  // 清理之前的定时器
  if (window.chartRefreshTimer) {
    clearTimeout(window.chartRefreshTimer)
  }
  // 设置新的定时器并保存引用
  window.chartRefreshTimer = setTimeout(() => {
    chartLoading.value = false
    uni.showToast({
      title: '图表已刷新',
      icon: 'success'
    })
    window.chartRefreshTimer = null
  }, 2000)
}

const testGetRequest = async () => {
  try {
    // 模拟GET请求
    const result = await get('/api/test', { page: 1, size: 10 })
    apiResult.value = 'GET请求成功: ' + JSON.stringify(result)
  } catch (error) {
    apiResult.value = 'GET请求失败: ' + error.message
  }
}

const testPostRequest = async () => {
  try {
    // 模拟POST请求
    const result = await post('/api/test', { name: '测试数据', value: 123 })
    apiResult.value = 'POST请求成功: ' + JSON.stringify(result)
  } catch (error) {
    apiResult.value = 'POST请求失败: ' + error.message
  }
}

const testCacheRequest = async () => {
  try {
    // 测试缓存功能
    const result1 = await get('/api/cache-test', { timestamp: Date.now() })
    const result2 = await get('/api/cache-test', { timestamp: Date.now() })
    apiResult.value = '缓存测试: 两次请求结果相同 = ' + (JSON.stringify(result1) === JSON.stringify(result2))
  } catch (error) {
    apiResult.value = '缓存测试失败: ' + error.message
  }
}

const testStorage = () => {
  try {
    // 测试存储功能
    utils.storage.set('test_key', { data: '测试数据', timestamp: Date.now() })
    const result = utils.storage.get('test_key')
    utilResult.value = '存储测试成功: ' + JSON.stringify(result)
  } catch (error) {
    utilResult.value = '存储测试失败: ' + error.message
  }
}

const testValidation = () => {
  try {
    // 测试验证功能
    const email = '<EMAIL>'
    const phone = '13800138000'
    const emailValid = utils.validate.email(email)
    const phoneValid = utils.validate.phone(phone)
    utilResult.value = `验证测试: 邮箱${emailValid ? '有效' : '无效'}, 手机${phoneValid ? '有效' : '无效'}`
  } catch (error) {
    utilResult.value = '验证测试失败: ' + error.message
  }
}

const testFormat = () => {
  try {
    // 测试格式化功能
    const date = new Date()
    const number = 1234.567
    const formattedDate = utils.format.date(date)
    const formattedNumber = utils.format.number(number, 2)
    utilResult.value = `格式化测试: 日期=${formattedDate}, 数字=${formattedNumber}`
  } catch (error) {
    utilResult.value = '格式化测试失败: ' + error.message
  }
}

const showConfirmDialog = () => {
  dialogVisible.value = true
}

const onDialogConfirm = () => {
  uni.showToast({
    title: '用户点击了确认',
    icon: 'success'
  })
}

const onDialogCancel = () => {
  uni.showToast({
    title: '用户点击了取消',
    icon: 'none'
  })
}

// 生命周期
onMounted(() => {
  // 初始化测试数据
  store.dispatch('system/initSystemInfo')
})

// 页面卸载时清理定时器
onUnload(() => {
  // 清理可能存在的定时器
  if (window.chartRefreshTimer) {
    clearTimeout(window.chartRefreshTimer)
    window.chartRefreshTimer = null
  }
})
</script>

<style scoped>
.integration-test {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx;
  background: white;
  border-radius: 16rpx;
}

.test-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.test-subtitle {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.test-section {
  margin-bottom: 30rpx;
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.test-item {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 20rpx;
}

.test-label {
  font-size: 28rpx;
  color: #333;
  min-width: 120rpx;
}

.test-btn {
  padding: 10rpx 20rpx;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.test-btn:active {
  background: #0056cc;
}

.api-result,
.util-result {
  margin-top: 20rpx;
  padding: 20rpx;
  background: #e8f4fd;
  border-radius: 12rpx;
  border-left: 4rpx solid #007aff;
}

.result-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.result-content {
  font-size: 24rpx;
  color: #666;
  word-break: break-all;
  display: block;
}

.mock-chart {
  height: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12rpx;
}
</style>