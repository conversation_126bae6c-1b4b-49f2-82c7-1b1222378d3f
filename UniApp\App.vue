<script>
	export default {
		onLaunch: function() {
			console.log('App Launch')
			// 应用启动时的初始化操作
			try {
				this.initApp()
			} catch (error) {
				console.error('应用启动初始化失败:', error)
			}
			// 设置全局异常处理
			this.setupGlobalErrorHandler()
		},
		onShow: function() {
			console.log('App Show')
			// 应用从后台进入前台时触发
		},
		onHide: function() {
			console.log('App Hide')
			// 应用从前台进入后台时触发
		},
		methods: {
			// 应用初始化
			initApp() {
				// 检查登录状态
				this.checkLoginStatus()
				// 初始化全局配置
				this.initGlobalConfig()
			},
			// 检查登录状态
			checkLoginStatus() {
				try {
					const token = uni.getStorageSync('token')
					if (!token) {
						// 如果没有token，跳转到登录页
						// uni.reLaunch({
						// 	url: '/pages/user/login'
						// })
					}
				} catch (error) {
					console.error('检查登录状态失败:', error)
				}
			},
			// 初始化全局配置
			initGlobalConfig() {
				try {
					// 设置全局请求基础URL
					uni.setStorageSync('baseUrl', 'http://127.0.0.1:5005')
					// 设置MQTT连接配置
					uni.setStorageSync('mqttConfig', {
						host: 'emqx.oucinyun.com',
						port: 8083,
						clientId: 'energy_light_' + Date.now()
					})
				} catch (error) {
					console.error('初始化全局配置失败:', error)
				}
			},
			// 设置全局异常处理
			setupGlobalErrorHandler() {
				// 捕获Promise未处理的rejection
				if (typeof window !== 'undefined') {
					window.addEventListener('unhandledrejection', (event) => {
						console.error('未处理的Promise异常:', event.reason)
						event.preventDefault()
					})
				}
				// 捕获全局JavaScript异常
				if (typeof window !== 'undefined') {
					window.addEventListener('error', (event) => {
						console.error('全局JavaScript异常:', event.error)
					})
				}
			}
		}
	}
</script>

<style lang="scss">
	/* 引入字体资源 */
	@import url('/static/fonts/iconfont.css');
	/* 引入通用样式 */
	@import url('/static/css/common.css');
	
	/* 全局样式重置 */
	* {
		box-sizing: border-box;
		margin: 0;
		padding: 0;
	}
	
	/* 根元素样式 */
	html, body {
		height: 100%;
		font-family: 'iconfont', 'PingFang SC', 'Helvetica Neue', Helvetica, 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
		background-color: var(--bg-page, #f5f5f5);
		color: var(--text-primary, #333333);
		line-height: 1.5;
	}
	
	/* uni-app 页面容器 */
	.uni-page-body {
		height: 100%;
		background-color: var(--bg-page, #f5f5f5);
	}
	
	/* 滚动条样式优化 */
	::-webkit-scrollbar {
		width: 6rpx;
		height: 6rpx;
	}
	
	::-webkit-scrollbar-track {
		background: var(--bg-secondary, #f8f9fa);
		border-radius: 6rpx;
	}
	
	::-webkit-scrollbar-thumb {
		background: var(--border-medium, #d9d9d9);
		border-radius: 6rpx;
	}
	
	::-webkit-scrollbar-thumb:hover {
		background: var(--border-dark, #bfbfbf);
	}
	
	/* 输入框样式优化 */
	input, textarea {
		font-family: inherit;
		font-size: inherit;
		line-height: inherit;
		color: inherit;
	}
	
	/* 按钮样式重置 */
	button {
		font-family: inherit;
		font-size: inherit;
		line-height: inherit;
		border: none;
		outline: none;
		background: none;
		cursor: pointer;
	}
	
	/* 图片样式优化 */
	image {
		max-width: 100%;
		height: auto;
		vertical-align: middle;
	}
	
	/* 链接样式 */
	a {
		color: var(--primary-color, #1890ff);
		text-decoration: none;
		transition: color 0.3s ease;
	}
	
	a:hover {
		color: var(--primary-hover, #40a9ff);
	}
	
	/* 选择文本样式 */
	::selection {
		background-color: var(--primary-color, #1890ff);
		color: #ffffff;
	}
	
	/* 占位符样式 */
	::placeholder {
		color: var(--text-placeholder, #999999);
		opacity: 1;
	}
	
	/* 焦点样式 */
	:focus {
		outline: 2rpx solid var(--primary-color, #1890ff);
		outline-offset: 2rpx;
	}
	
	/* 禁用状态样式 */
	:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}
	
	/* 加载状态样式 */
	.loading {
		pointer-events: none;
		opacity: 0.7;
	}
	
	/* 页面切换动画 */
	.page-enter-active,
	.page-leave-active {
		transition: all 0.3s ease;
	}
	
	.page-enter-from {
		opacity: 0;
		transform: translateX(100%);
	}
	
	.page-leave-to {
		opacity: 0;
		transform: translateX(-100%);
	}
	
	/* 应用特定的全局样式 */
	.uni-app {
		min-height: 100vh;
		background-color: var(--bg-page);
	}
	
	/* 页面容器优化 */
	.page {
		min-height: 100vh;
		background-color: var(--bg-page);
		display: flex;
		flex-direction: column;
	}
	
	/* 导航栏样式优化 */
	.uni-navbar {
		background-color: var(--bg-card);
		border-bottom: 2rpx solid var(--border-light);
		box-shadow: var(--shadow-light);
	}
	
	/* 底部导航栏样式优化 */
	.uni-tabbar {
		background-color: var(--bg-card);
		border-top: 2rpx solid var(--border-light);
		box-shadow: var(--shadow-light);
	}
	
	/* 滚动视图优化 */
	.scroll-view {
		flex: 1;
		height: 100%;
	}
	
	/* 加载状态样式 */
	.loading-container {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 40rpx;
		color: var(--text-secondary);
	}
	
	/* 错误状态样式 */
	.error-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 80rpx 40rpx;
		color: var(--text-secondary);
		text-align: center;
	}
	
	/* 空状态样式 */
	.empty-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 120rpx 40rpx;
		color: var(--text-secondary);
		text-align: center;
	}
	
	/* 图标字体基础样式 */
	.iconfont {
		font-family: 'iconfont' !important;
		font-size: 32rpx;
		font-style: normal;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
	}
	
	/* 主题切换过渡效果 */
	* {
		transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
	}
	
	/* 安全区域适配 */
	.safe-area {
		padding-top: constant(safe-area-inset-top);
		padding-top: env(safe-area-inset-top);
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}
</style>