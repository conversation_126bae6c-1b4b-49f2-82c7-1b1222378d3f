// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;

namespace Admin.NET.Core.Service;

/// <summary>
/// 能源告警管理服务 🧩
/// </summary>
[ApiDescriptionSettings(Order = 510, Description = "能源告警管理")]
public class EnergyAlarmService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<EnergyFault> _energyFaultRep;
    private readonly SqlSugarRepository<EnergyDevice> _energyDeviceRep;
    private readonly UserManager _userManager;

    public EnergyAlarmService(
        SqlSugarRepository<EnergyFault> energyFaultRep,
        SqlSugarRepository<EnergyDevice> energyDeviceRep,
        UserManager userManager)
    {
        _energyFaultRep = energyFaultRep;
        _energyDeviceRep = energyDeviceRep;
        _userManager = userManager;
    }

    /// <summary>
    /// 获取告警统计 🔖
    /// </summary>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Stats"), HttpGet]
    [DisplayName("获取告警统计")]
    public async Task<dynamic> GetStats()
    {
        var query = _energyFaultRep.AsQueryable()
            .Where(f => f.TenantId == _userManager.TenantId);

        var critical = await query.Where(f => f.FaultLevel == 4 && f.Status == 1).CountAsync();
        var warning = await query.Where(f => f.FaultLevel == 3 && f.Status == 1).CountAsync();
        var resolved = await query.Where(f => f.Status == 1).CountAsync();

        return new
        {
            critical,
            warning,
            resolved
        };
    }

    /// <summary>
    /// 获取最新告警列表 🔖
    /// </summary>
    /// <param name="limit">限制数量</param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Recent"), HttpGet]
    [DisplayName("获取最新告警列表")]
    public async Task<List<dynamic>> GetRecentAlarms([FromQuery] int limit = 5)
    {
        var alarms = await _energyFaultRep.AsQueryable()
            .LeftJoin<EnergyDevice>((f, d) => f.DeviceId == d.Id)
            .Where((f, d) => f.TenantId == _userManager.TenantId)
            .OrderByDescending((f, d) => f.CreateTime)
            .Take(limit)
            .Select((f, d) => new
            {
                id = f.Id,
                title = f.FaultType + "告警",
                description = f.FaultDescription,
                deviceName = d.DeviceName,
                level = f.FaultLevel,
                status = f.Status == 0 ? "pending" : "resolved",
                createTime = f.CreateTime
            })
            .ToListAsync();

        return alarms.Cast<dynamic>().ToList();
    }

    /// <summary>
    /// 处理告警 🔖
    /// </summary>
    /// <param name="input">告警处理输入</param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Handle"), HttpPost]
    [DisplayName("处理告警")]
    public async Task<bool> HandleAlarm(HandleAlarmInput input)
    {
        var fault = await _energyFaultRep.GetByIdAsync(input.Id);
        if (fault == null)
            throw Oops.Oh("告警不存在");

        if (fault.TenantId != _userManager.TenantId)
            throw Oops.Oh("无权限操作此告警");

        fault.Status = 3; // 已处理
        fault.HandleTime = DateTime.Now;
        fault.HandlerId = _userManager.UserId;
        fault.Remark = input.Remark ?? "手动处理";
        fault.UpdateTime = DateTime.Now;
        fault.UpdateUserId = _userManager.UserId;

        await _energyFaultRep.AsUpdateable(fault).ExecuteCommandAsync();
        return true;
    }

    /// <summary>
    /// 获取告警分页列表 🔖
    /// </summary>
    /// <param name="input">查询参数</param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "List"), HttpGet]
    [DisplayName("获取告警分页列表")]
    public async Task<SqlSugarPagedList<AlarmListOutput>> GetAlarmList([FromQuery] AlarmListInput input)
    {
        var query = _energyFaultRep.AsQueryable()
            .LeftJoin<EnergyDevice>((f, d) => f.DeviceId == d.Id)
            .Where((f, d) => f.TenantId == _userManager.TenantId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.FaultLevel), (f, d) => f.FaultLevel.ToString() == input.FaultLevel)
            .WhereIF(input.Status.HasValue, (f, d) => f.Status == input.Status)
            .WhereIF(input.DeviceId.HasValue, (f, d) => f.DeviceId == input.DeviceId)
            .WhereIF(input.StartTime.HasValue, (f, d) => f.CreateTime >= input.StartTime)
            .WhereIF(input.EndTime.HasValue, (f, d) => f.CreateTime <= input.EndTime)
            .OrderBy((f, d) => f.CreateTime, OrderByType.Desc)
            .Select((f, d) => new AlarmListOutput
            {
                Id = f.Id,
                DeviceId = f.DeviceId,
                DeviceName = d.DeviceName,
                FaultType = f.FaultType,
                FaultLevel = f.FaultLevel,
                FaultDescription = f.FaultDescription,
                Status = f.Status,
                CreateTime = f.CreateTime,
                HandleTime = f.HandleTime,
                Remark = f.Remark
            });

        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }
}

/// <summary>
/// 处理告警输入
/// </summary>
public class HandleAlarmInput
{
    /// <summary>
    /// 告警ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 处理备注
    /// </summary>
    public string? Remark { get; set; }
}