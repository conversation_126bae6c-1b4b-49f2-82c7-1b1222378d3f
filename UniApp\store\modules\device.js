import { DEVICE_STATUS } from '../types'
import { storage } from '../../utils'

// 初始状态
const state = {
  // 设备列表
  deviceList: [],
  // 设备分组
  deviceGroups: [],
  // 当前选中设备
  currentDevice: null,
  // 收藏设备列表
  favoriteDevices: storage.get('favoriteDevices') || [],
  // 加载状态
  loading: false,
  // 错误信息
  error: null,
  // 最后更新时间
  lastUpdateTime: null,
  // 设备状态缓存
  deviceStatusCache: new Map(),
  // 搜索关键词
  searchKeyword: '',
  // 筛选条件
  filters: {
    status: '', // 设备状态筛选
    type: '', // 设备类型筛选
    groupId: '', // 分组筛选
    location: '' // 位置筛选
  },
  // 排序方式
  sortBy: 'name', // name, status, createTime, updateTime
  sortOrder: 'asc' // asc, desc
}

// Getters
const getters = {
  // 在线设备数量
  onlineDeviceCount: (state) => {
    return state.deviceList.filter(device => device.status === DEVICE_STATUS.ONLINE).length
  },
  
  // 离线设备数量
  offlineDeviceCount: (state) => {
    return state.deviceList.filter(device => device.status === DEVICE_STATUS.OFFLINE).length
  },
  
  // 故障设备数量
  faultDeviceCount: (state) => {
    return state.deviceList.filter(device => device.status === DEVICE_STATUS.FAULT).length
  },
  
  // 设备总数
  totalDeviceCount: (state) => {
    return state.deviceList.length
  },
  
  // 在线率
  onlineRate: (state, getters) => {
    if (getters.totalDeviceCount === 0) return 0
    return Math.round((getters.onlineDeviceCount / getters.totalDeviceCount) * 100)
  },
  
  // 按类型分组的设备
  devicesByType: (state) => {
    const grouped = {}
    state.deviceList.forEach(device => {
      if (!grouped[device.type]) {
        grouped[device.type] = []
      }
      grouped[device.type].push(device)
    })
    return grouped
  },
  
  // 按状态分组的设备
  devicesByStatus: (state) => {
    const grouped = {}
    state.deviceList.forEach(device => {
      if (!grouped[device.status]) {
        grouped[device.status] = []
      }
      grouped[device.status].push(device)
    })
    return grouped
  },
  
  // 按分组分类的设备
  devicesByGroup: (state) => {
    const grouped = {}
    state.deviceList.forEach(device => {
      const groupId = device.groupId || 'ungrouped'
      if (!grouped[groupId]) {
        grouped[groupId] = []
      }
      grouped[groupId].push(device)
    })
    return grouped
  },
  
  // 筛选后的设备列表
  filteredDeviceList: (state) => {
    let filtered = [...state.deviceList]
    
    // 搜索关键词筛选
    if (state.searchKeyword) {
      const keyword = state.searchKeyword.toLowerCase()
      filtered = filtered.filter(device => 
        device.name.toLowerCase().includes(keyword) ||
        device.location.toLowerCase().includes(keyword) ||
        device.id.toLowerCase().includes(keyword)
      )
    }
    
    // 状态筛选
    if (state.filters.status) {
      filtered = filtered.filter(device => device.status === state.filters.status)
    }
    
    // 类型筛选
    if (state.filters.type) {
      filtered = filtered.filter(device => device.type === state.filters.type)
    }
    
    // 分组筛选
    if (state.filters.groupId) {
      filtered = filtered.filter(device => device.groupId === state.filters.groupId)
    }
    
    // 位置筛选
    if (state.filters.location) {
      filtered = filtered.filter(device => 
        device.location.toLowerCase().includes(state.filters.location.toLowerCase())
      )
    }
    
    // 排序
    filtered.sort((a, b) => {
      let aValue = a[state.sortBy]
      let bValue = b[state.sortBy]
      
      // 处理不同数据类型的排序
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }
      
      if (state.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })
    
    return filtered
  },
  
  // 收藏设备列表
  favoriteDeviceList: (state) => {
    return state.deviceList.filter(device => 
      state.favoriteDevices.includes(device.id)
    )
  },
  
  // 根据ID获取设备
  getDeviceById: (state) => (id) => {
    return state.deviceList.find(device => device.id === id)
  },
  
  // 根据分组ID获取设备
  getDevicesByGroupId: (state) => (groupId) => {
    return state.deviceList.filter(device => device.groupId === groupId)
  },
  
  // 获取设备分组信息
  getGroupById: (state) => (id) => {
    return state.deviceGroups.find(group => group.id === id)
  }
}

// Mutations
const mutations = {
  // 设置设备列表
  SET_DEVICE_LIST(state, deviceList) {
    state.deviceList = deviceList || []
    state.lastUpdateTime = new Date().toISOString()
  },
  
  // 设置设备分组
  SET_DEVICE_GROUPS(state, deviceGroups) {
    state.deviceGroups = deviceGroups || []
  },
  
  // 设置当前设备
  SET_CURRENT_DEVICE(state, device) {
    state.currentDevice = device
  },
  
  // 设置设备状态
  SET_DEVICE_STATUS(state, { deviceId, status, data }) {
    const device = state.deviceList.find(d => d.id === deviceId)
    if (device) {
      // 更新设备状态
      Object.assign(device, {
        status: status || device.status,
        ...data,
        updateTime: new Date().toISOString()
      })
    }
    
    // 更新状态缓存
    state.deviceStatusCache.set(deviceId, {
      status,
      data,
      timestamp: Date.now()
    })
    
    // 如果是当前设备，同步更新
    if (state.currentDevice && state.currentDevice.id === deviceId) {
      Object.assign(state.currentDevice, {
        status: status || state.currentDevice.status,
        ...data,
        updateTime: new Date().toISOString()
      })
    }
  },
  
  // 设置收藏设备
  SET_FAVORITE_DEVICES(state, favoriteDevices) {
    state.favoriteDevices = favoriteDevices || []
    storage.set('favoriteDevices', state.favoriteDevices)
  },
  
  // 设置加载状态
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  
  // 设置错误信息
  SET_ERROR(state, error) {
    state.error = error
  },
  
  // 添加设备
  ADD_DEVICE(state, device) {
    state.deviceList.push(device)
    state.lastUpdateTime = new Date().toISOString()
  },
  
  // 更新设备
  UPDATE_DEVICE(state, updatedDevice) {
    const index = state.deviceList.findIndex(d => d.id === updatedDevice.id)
    if (index !== -1) {
      state.deviceList.splice(index, 1, updatedDevice)
      state.lastUpdateTime = new Date().toISOString()
    }
  },
  
  // 删除设备
  REMOVE_DEVICE(state, deviceId) {
    const index = state.deviceList.findIndex(d => d.id === deviceId)
    if (index !== -1) {
      state.deviceList.splice(index, 1)
      state.lastUpdateTime = new Date().toISOString()
    }
    
    // 从收藏中移除
    const favoriteIndex = state.favoriteDevices.indexOf(deviceId)
    if (favoriteIndex !== -1) {
      state.favoriteDevices.splice(favoriteIndex, 1)
      storage.set('favoriteDevices', state.favoriteDevices)
    }
    
    // 清除当前设备
    if (state.currentDevice && state.currentDevice.id === deviceId) {
      state.currentDevice = null
    }
  },
  
  // 清空设备列表
  CLEAR_DEVICES(state) {
    state.deviceList = []
    state.deviceGroups = []
    state.currentDevice = null
    state.deviceStatusCache.clear()
    state.lastUpdateTime = null
  },
  
  // 设置搜索关键词
  SET_SEARCH_KEYWORD(state, keyword) {
    state.searchKeyword = keyword
  },
  
  // 设置筛选条件
  SET_FILTERS(state, filters) {
    state.filters = { ...state.filters, ...filters }
  },
  
  // 设置排序
  SET_SORT(state, { sortBy, sortOrder }) {
    state.sortBy = sortBy
    state.sortOrder = sortOrder
  }
}

// Actions
const actions = {
  // 获取设备列表
  async getDeviceList({ commit }, params = {}) {
    try {
      commit('SET_LOADING', true)
      commit('SET_ERROR', null)
      
      // 导入API函数
      const { getDeviceList } = await import('../../api/device.js')
      const response = await getDeviceList(params)
      
      if (response.code === 200 || response.success) {
        const deviceList = response.data?.list || response.data || []
        commit('SET_DEVICE_LIST', deviceList)
        return { success: true, data: deviceList }
      } else {
        throw new Error(response.message || '获取设备列表失败')
      }
    } catch (error) {
      console.error('获取设备列表失败:', error)
      commit('SET_ERROR', error.message)
      
      // 返回缓存数据或空数组作为降级方案
      const cachedData = uni.getStorageSync('cached_device_list') || []
      if (cachedData.length > 0) {
        commit('SET_DEVICE_LIST', cachedData)
        return { success: true, data: cachedData, fromCache: true }
      }
      
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 获取设备统计数据
  async getDeviceStats({ commit }, params = {}) {
    try {
      commit('SET_LOADING', true)
      commit('SET_ERROR', null)
      
      // 导入API函数
      const { getDeviceStats } = await import('../../api/device.js')
      const response = await getDeviceStats(params)
      
      if (response.code === 200 || response.success) {
        const statsData = response.data || {}
        
        // 缓存统计数据
        uni.setStorageSync('cached_device_stats', {
          data: statsData,
          timestamp: Date.now()
        })
        
        return { success: true, data: statsData }
      } else {
        throw new Error(response.message || '获取设备统计失败')
      }
    } catch (error) {
      console.error('获取设备统计失败:', error)
      commit('SET_ERROR', error.message)
      
      // 尝试使用缓存数据
      const cachedStats = uni.getStorageSync('cached_device_stats')
      if (cachedStats && (Date.now() - cachedStats.timestamp < 10 * 60 * 1000)) {
        return { success: true, data: cachedStats.data, fromCache: true }
      }
      
      // 返回默认数据
      return {
        success: true,
        data: {
          totalCount: 0,
          onlineCount: 0,
          offlineCount: 0,
          faultCount: 0
        },
        isDefault: true
      }
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 获取设备分组
  async getDeviceGroups({ commit }) {
    try {
      // const response = await deviceApi.getDeviceGroups()
      // 模拟数据
      const response = { code: 200, data: [] }
      
      if (response.code === 200) {
        commit('SET_DEVICE_GROUPS', response.data)
        return response.data
      } else {
        throw new Error(response.message || '获取设备分组失败')
      }
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    }
  },
  
  // 获取设备详情
  async getDeviceDetail({ commit }) {
    try {
      commit('SET_LOADING', true)
      
      // const response = await deviceApi.getDeviceDetail(deviceId)
      // 模拟数据
      const response = { code: 200, data: {} }
      
      if (response.code === 200) {
        commit('SET_CURRENT_DEVICE', response.data)
        return response.data
      } else {
        throw new Error(response.message || '获取设备详情失败')
      }
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 控制设备
  async controlDevice({ commit }, { deviceId, params }) {
    try {
      commit('SET_LOADING', true)
      
      // 模拟API调用
      const response = { code: 200, data: {} }
      
      if (response.code === 200) {
        // 更新设备状态
        commit('SET_DEVICE_STATUS', {
          deviceId,
          data: params
        })
        
        uni.showToast({
          title: '控制成功',
          icon: 'success'
        })
        
        return response.data
      } else {
        throw new Error(response.message || '设备控制失败')
      }
    } catch (error) {
      commit('SET_ERROR', error.message)
      uni.showToast({
        title: error.message,
        icon: 'error'
      })
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 添加设备
  async addDevice({ commit }, deviceData) {
    try {
      commit('SET_LOADING', true)
      
      // const response = await deviceApi.addDevice(deviceData)
      // 模拟数据
      const response = { code: 200, data: deviceData }
      
      if (response.code === 200) {
        commit('ADD_DEVICE', response.data)
        
        uni.showToast({
          title: '设备添加成功',
          icon: 'success'
        })
        
        return response.data
      } else {
        throw new Error(response.message || '添加设备失败')
      }
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 更新设备
  async updateDevice({ commit }, { deviceData }) {
    try {
      commit('SET_LOADING', true)
      
      // const response = await deviceApi.updateDevice(deviceId, deviceData)
      // 模拟数据
      const response = { code: 200, data: deviceData }
      
      if (response.code === 200) {
        commit('UPDATE_DEVICE', response.data)
        
        uni.showToast({
          title: '设备更新成功',
          icon: 'success'
        })
        
        return response.data
      } else {
        throw new Error(response.message || '更新设备失败')
      }
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 删除设备
  async deleteDevice({ commit }, deviceId) {
    try {
      commit('SET_LOADING', true)
      
      // const response = await deviceApi.deleteDevice(deviceId)
      // 模拟数据
      const response = { code: 200, data: {} }
      
      if (response.code === 200) {
        commit('REMOVE_DEVICE', deviceId)
        
        uni.showToast({
          title: '设备删除成功',
          icon: 'success'
        })
        
        return response.data
      } else {
        throw new Error(response.message || '删除设备失败')
      }
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 切换收藏状态
  toggleFavorite({ commit, state }, deviceId) {
    const favoriteDevices = [...state.favoriteDevices]
    const index = favoriteDevices.indexOf(deviceId)
    
    if (index !== -1) {
      favoriteDevices.splice(index, 1)
      uni.showToast({
        title: '已取消收藏',
        icon: 'none'
      })
    } else {
      favoriteDevices.push(deviceId)
      uni.showToast({
        title: '已添加收藏',
        icon: 'success'
      })
    }
    
    commit('SET_FAVORITE_DEVICES', favoriteDevices)
  },
  
  // 批量控制设备
  async batchControl({ commit }, { deviceIds, params }) {
    try {
      commit('SET_LOADING', true)
      
      // 模拟API调用
      const response = { code: 200, data: {} }
      
      if (response.code === 200) {
        // 更新所有设备状态
        deviceIds.forEach(deviceId => {
          commit('SET_DEVICE_STATUS', {
            deviceId,
            data: params
          })
        })
        
        uni.showToast({
          title: `批量控制成功(${deviceIds.length}个设备)`,
          icon: 'success'
        })
        
        return response.data
      } else {
        throw new Error(response.message || '批量控制失败')
      }
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 同步设备状态
  async syncDeviceStatus({ commit, state }) {
    try {
      const deviceIds = state.deviceList.map(device => device.id)
      if (deviceIds.length === 0) return
      
      // 模拟API调用
      const response = { code: 200, data: [] }
      
      if (response.code === 200) {
        const statusList = response.data
        statusList.forEach(status => {
          commit('SET_DEVICE_STATUS', {
            deviceId: status.deviceId,
            status: status.status,
            data: status.data
          })
        })
      }
    } catch (error) {
      console.error('同步设备状态失败:', error)
    }
  },
  
  // 搜索设备
  searchDevices({ commit }, keyword) {
    commit('SET_SEARCH_KEYWORD', keyword)
  },
  
  // 设置筛选条件
  setFilters({ commit }, filters) {
    commit('SET_FILTERS', filters)
  },
  
  // 设置排序
  setSorting({ commit }, { sortBy, sortOrder }) {
    commit('SET_SORT', { sortBy, sortOrder })
  },
  
  // 清空筛选和搜索
  clearFilters({ commit }) {
    commit('SET_SEARCH_KEYWORD', '')
    commit('SET_FILTERS', {
      status: '',
      type: '',
      groupId: '',
      location: ''
    })
  },

  // 更新设备状态
  async updateDeviceStatus({ commit }, { deviceId, status }) {
    try {
      commit('SET_LOADING', true)
      // 模拟API调用
      const response = { success: true, data: { deviceId, status } }
      
      if (response.success) {
        // 更新本地设备状态
        commit('UPDATE_DEVICE_STATUS', { deviceId, status })
        
        // 更新缓存
        commit('UPDATE_DEVICE_CACHE', { deviceId, status, timestamp: Date.now() })
        
        return { success: true, data: response.data }
      } else {
        throw new Error(response.message || '更新设备状态失败')
      }
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}