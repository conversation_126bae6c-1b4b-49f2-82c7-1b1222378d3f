/**
 * 图标管理工具
 * 处理图标加载、备用方案和缓存
 */

import iconConfig from '@/static/icons/icon-config.json'

class IconManager {
  constructor() {
    this.config = iconConfig
    this.loadCache = new Map() // 加载状态缓存
    this.errorCache = new Set() // 错误缓存
  }

  /**
   * 获取图标路径
   * @param {string} iconName 图标名称
   * @param {string} format 格式 'svg' | 'png' | 'auto'
   * @returns {string} 图标路径
   */
  getIconPath(iconName, format = 'auto') {
    const iconInfo = this.config.icons[iconName]
    
    if (!iconInfo) {
      console.warn(`图标不存在: ${iconName}`)
      return this.getDefaultIcon()
    }

    // 如果之前加载失败过，直接返回备用方案
    if (this.errorCache.has(`${iconName}.svg`) && format === 'auto') {
      format = 'png'
    }

    switch (format) {
      case 'svg':
        return iconInfo.svg.path
      case 'png':
        return iconInfo.png.available ? iconInfo.png.path : this.getFallbackIcon(iconName)
      case 'auto':
      default:
        // 优先使用SVG，如果不可用则使用PNG
        return iconInfo.svg.path
    }
  }

  /**
   * 获取备用图标
   * @param {string} iconName 图标名称
   * @returns {string} 备用图标信息
   */
  getFallbackIcon(iconName) {
    const iconInfo = this.config.icons[iconName]
    
    if (iconInfo && iconInfo.fallback) {
      return {
        type: 'emoji',
        char: iconInfo.fallback.char,
        color: iconInfo.fallback.color
      }
    }
    
    return {
      type: 'emoji',
      char: '📄',
      color: '#666666'
    }
  }

  /**
   * 获取默认图标
   * @returns {string} 默认图标路径
   */
  getDefaultIcon() {
    return '/static/icons/device-default.svg'
  }

  /**
   * 标记图标加载失败
   * @param {string} iconPath 图标路径
   */
  markIconError(iconPath) {
    this.errorCache.add(iconPath)
    console.warn(`图标加载失败，已加入错误缓存: ${iconPath}`)
  }

  /**
   * 标记图标加载成功
   * @param {string} iconPath 图标路径
   */
  markIconLoaded(iconPath) {
    this.loadCache.set(iconPath, true)
  }

  /**
   * 检查图标是否加载过
   * @param {string} iconPath 图标路径
   * @returns {boolean} 是否已加载
   */
  isIconLoaded(iconPath) {
    return this.loadCache.has(iconPath)
  }

  /**
   * 检查图标是否加载失败过
   * @param {string} iconPath 图标路径
   * @returns {boolean} 是否加载失败过
   */
  isIconError(iconPath) {
    return this.errorCache.has(iconPath)
  }

  /**
   * 预加载图标
   * @param {string[]} iconNames 图标名称数组
   * @returns {Promise} 预加载Promise
   */
  async preloadIcons(iconNames) {
    const promises = iconNames.map(iconName => {
      return new Promise((resolve) => {
        const iconPath = this.getIconPath(iconName)
        
        // 创建临时image元素进行预加载
        const img = new Image()
        img.onload = () => {
          this.markIconLoaded(iconPath)
          resolve({ iconName, success: true })
        }
        img.onerror = () => {
          this.markIconError(iconPath)
          resolve({ iconName, success: false })
        }
        img.src = iconPath
      })
    })

    const results = await Promise.all(promises)
    const successCount = results.filter(r => r.success).length
    const failCount = results.length - successCount
    
    console.log(`图标预加载完成: 成功 ${successCount}, 失败 ${failCount}`)
    return results
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.loadCache.clear()
    this.errorCache.clear()
    console.log('图标缓存已清除')
  }

  /**
   * 获取图标统计信息
   * @returns {object} 统计信息
   */
  getStats() {
    const totalIcons = Object.keys(this.config.icons).length
    const loadedIcons = this.loadCache.size
    const errorIcons = this.errorCache.size
    const availablePngIcons = Object.values(this.config.icons)
      .filter(icon => icon.png.available).length

    return {
      total: totalIcons,
      loaded: loadedIcons,
      errors: errorIcons,
      pngAvailable: availablePngIcons,
      loadRate: totalIcons > 0 ? (loadedIcons / totalIcons * 100).toFixed(1) : 0
    }
  }

  /**
   * 获取所有图标列表
   * @returns {array} 图标列表
   */
  getAllIcons() {
    return Object.keys(this.config.icons).map(iconName => ({
      name: iconName,
      ...this.config.icons[iconName],
      loaded: this.isIconLoaded(this.getIconPath(iconName)),
      error: this.isIconError(this.getIconPath(iconName))
    }))
  }
}

// 创建单例实例
const iconManager = new IconManager()

// 导出实例和类
export default iconManager
export { IconManager }

// 导出常用方法
export const getIconPath = (iconName, format) => iconManager.getIconPath(iconName, format)
export const getFallbackIcon = (iconName) => iconManager.getFallbackIcon(iconName)
export const preloadIcons = (iconNames) => iconManager.preloadIcons(iconNames)
export const getIconStats = () => iconManager.getStats()
export const getAllIcons = () => iconManager.getAllIcons()