using Admin.NET.Plugin.MQTT;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
using Newtonsoft.Json;

namespace Admin.NET.Plugin.MQTT.Test
{
    /// <summary>
    /// MQTT凭证生成测试类
    /// 用于验证MqttOptions类的凭证生成功能
    /// </summary>
    public class TestMqttCredentials
    {
        /// <summary>
        /// 运行MQTT凭证生成测试
        /// </summary>
        /// <returns>测试结果</returns>
        public static string RunTest()
        {
            try
            {
                Console.WriteLine("开始MQTT凭证生成测试...");
                
                // 创建日志记录器（简单的控制台日志）
                using var loggerFactory = LoggerFactory.Create(builder => 
                    builder.AddConsole().SetMinimumLevel(LogLevel.Information));
                var logger = loggerFactory.CreateLogger<MqttOptions>();
                
                // 读取MQTT.json配置文件
                var configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, 
                    "..\\..\\..\\..\\Admin.NET.Application\\Configuration\\MQTT.json");
                
                if (!File.Exists(configPath))
                {
                    return $"配置文件不存在: {configPath}";
                }
                
                var jsonContent = File.ReadAllText(configPath);
                var jsonDoc = JsonDocument.Parse(jsonContent);
                
                // 创建MqttOptions实例并从JSON加载配置
                var mqttOptions = new MqttOptions();
                mqttOptions.DeserializeFromJsonElement(jsonDoc.RootElement.GetProperty("MqttOptions"));
                
                // 运行测试
                var testResult = mqttOptions.TestCredentialGeneration("AdminNET_MQTT_Client");
                
                Console.WriteLine("测试完成！");
                return testResult;
            }
            catch (Exception ex)
            {
                var errorMsg = $"测试执行失败: {ex.Message}\n堆栈跟踪: {ex.StackTrace}";
                Console.WriteLine(errorMsg);
                return errorMsg;
            }
        }
        
        /// <summary>
        /// 测试与SysAuthService算法的一致性
        /// </summary>
        /// <returns>一致性测试结果</returns>
        public static string TestConsistencyWithSysAuth()
        {
            try
            {
                Console.WriteLine("开始与SysAuthService算法一致性测试...");
                
                // 创建日志记录器
                using var loggerFactory = LoggerFactory.Create(builder => 
                    builder.AddConsole().SetMinimumLevel(LogLevel.Information));
                var logger = loggerFactory.CreateLogger<MqttOptions>();
                
                // 使用测试参数
                var mqttOptions = new MqttOptions
                {
                    InstanceId = "energylight",
                    AccessKeyId = "ak_c371a2975f7c4c0eb672b4675bfd2",
                    AccessKeySecret = "sk_test_secret_key_for_mqtt_auth"
                };
                
                var deviceId = "AdminNET_MQTT_Client";
                
                // 生成凭证
                var credentials = mqttOptions.GenerateCredentials(deviceId);
                
                // 验证算法一致性
                var expectedClientId = $"{mqttOptions.InstanceId}@@@{deviceId}";
                var expectedUsername = $"DeviceCredential|{mqttOptions.AccessKeyId}|{expectedClientId}";
                
                var results = new System.Text.StringBuilder();
                results.AppendLine("SysAuthService算法一致性测试结果:");
                results.AppendLine($"测试参数:");
                results.AppendLine($"  InstanceId: {mqttOptions.InstanceId}");
                results.AppendLine($"  AccessKeyId: {mqttOptions.AccessKeyId}");
                results.AppendLine($"  DeviceId: {deviceId}");
                results.AppendLine();
                
                results.AppendLine("生成的凭证:");
                results.AppendLine($"  ClientId: {credentials.ClientId}");
                results.AppendLine($"  Username: {credentials.Username}");
                results.AppendLine($"  Password: {credentials.Password}");
                results.AppendLine();
                
                results.AppendLine("算法验证:");
                results.AppendLine($"  ClientId匹配: {(credentials.ClientId == expectedClientId ? "✓" : "✗")}");
                results.AppendLine($"  Username匹配: {(credentials.Username == expectedUsername ? "✓" : "✗")}");
                results.AppendLine($"  Password非空: {(!string.IsNullOrWhiteSpace(credentials.Password) ? "✓" : "✗")}");
                
                // 验证HMAC-SHA1签名算法
                var testSignature = mqttOptions.ComputeHmacSha1Signature(credentials.ClientId, mqttOptions.AccessKeySecret);
                results.AppendLine($"  HMAC-SHA1签名: {testSignature}");
                results.AppendLine($"  签名匹配: {(credentials.Password == testSignature ? "✓" : "✗")}");
                
                Console.WriteLine("一致性测试完成！");
                return results.ToString();
            }
            catch (Exception ex)
            {
                var errorMsg = $"一致性测试失败: {ex.Message}\n堆栈跟踪: {ex.StackTrace}";
                Console.WriteLine(errorMsg);
                return errorMsg;
            }
        }
    }
}