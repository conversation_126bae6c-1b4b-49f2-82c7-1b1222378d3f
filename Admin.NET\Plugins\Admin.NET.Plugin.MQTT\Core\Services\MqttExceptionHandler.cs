using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Admin.NET.Plugin.MQTT.Core.Interfaces;
using Admin.NET.Plugin.MQTT.Core.Models;
using Admin.NET.Plugin.MQTT.Interfaces;

namespace Admin.NET.Plugin.MQTT.Core.Services
{
    /// <summary>
    /// MQTT异常处理器实现
    /// 负责异常处理、重试机制、错误统计和报告等功能
    /// </summary>
    public class MqttExceptionHandler : IMqttExceptionHandler, IDisposable
    {
        #region 私有字段
        
        private readonly ILogger<MqttExceptionHandler> _logger;
        private readonly IMqttConfigurationManager _configurationManager;
        
        private readonly ConcurrentDictionary<string, InternalExceptionStatistics> _exceptionStats;
        private readonly ConcurrentDictionary<string, RetryContext> _retryContexts;
        private readonly ConcurrentQueue<ExceptionRecord> _recentExceptions;
        private readonly ConcurrentDictionary<Type, IExceptionHandlingStrategy> _exceptionStrategies;
        
        private readonly Timer _cleanupTimer;
        private readonly Timer _reportTimer;
        private readonly SemaphoreSlim _retrySemaphore;
        
        private volatile bool _isDisposed;
        private readonly object _lockObject = new object();
        
        #endregion
        
        #region 事件
        
        /// <summary>
        /// 异常发生事件
        /// </summary>
        public event EventHandler<ExceptionOccurredEventArgs> ExceptionOccurred;
        
        /// <summary>
        /// 错误恢复事件
        /// </summary>
        public event EventHandler<ErrorRecoveredEventArgs> ErrorRecovered;
        
        /// <summary>
        /// 重试执行事件
        /// </summary>
        public event EventHandler<RetryExecutedEventArgs> RetryExecuted;
        
        /// <summary>
        /// 错误报告生成事件
        /// </summary>
        public event EventHandler<ErrorReportGeneratedEventArgs> ErrorReportGenerated;
        
        #endregion
        
        #region 属性
        
        /// <summary>
        /// 异常统计信息
        /// </summary>
        public IReadOnlyDictionary<string, InternalExceptionStatistics> InternalStatistics => _exceptionStats;
        
        /// <summary>
        /// 最近异常记录
        /// </summary>
        public IEnumerable<ExceptionRecord> RecentExceptions => _recentExceptions.ToArray();
        
        #endregion
        
        #region 构造函数
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="configurationManager">配置管理器</param>
        public MqttExceptionHandler(
            ILogger<MqttExceptionHandler> logger,
            IMqttConfigurationManager configurationManager)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configurationManager = configurationManager ?? throw new ArgumentNullException(nameof(configurationManager));
            
            _exceptionStats = new ConcurrentDictionary<string, InternalExceptionStatistics>();
            _retryContexts = new ConcurrentDictionary<string, RetryContext>();
            _recentExceptions = new ConcurrentQueue<ExceptionRecord>();
            _exceptionStrategies = new ConcurrentDictionary<Type, IExceptionHandlingStrategy>();
            
            _retrySemaphore = new SemaphoreSlim(Environment.ProcessorCount * 2, Environment.ProcessorCount * 2);
            
            // 初始化定时器
            _cleanupTimer = new Timer(CleanupCallback, null, TimeSpan.FromMinutes(10), TimeSpan.FromMinutes(10));
            _reportTimer = new Timer(ReportCallback, null, TimeSpan.FromHours(1), TimeSpan.FromHours(1));
            
            _logger.LogInformation("MQTT异常处理器已初始化");
        }

        /// <summary>
        /// 执行带重试的操作（有返回值）
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="operation">要执行的操作</param>
        /// <param name="retryPolicy">重试策略</param>
        /// <param name="context">操作上下文</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>操作结果</returns>
        public async Task<T> ExecuteWithRetryAsync<T>(Func<CancellationToken, Task<T>> operation, RetryPolicy retryPolicy, OperationContext context, CancellationToken cancellationToken = default)
        {
            return await ExecuteWithRetryInternalAsync(operation, context?.OperationName ?? "Unknown", retryPolicy?.MaxRetryCount ?? 3, cancellationToken);
        }

        /// <summary>
        /// 执行带重试的操作（无返回值）
        /// </summary>
        /// <param name="operation">要执行的操作</param>
        /// <param name="retryPolicy">重试策略</param>
        /// <param name="context">操作上下文</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public async Task ExecuteWithRetryAsync(Func<CancellationToken, Task> operation, RetryPolicy retryPolicy, OperationContext context, CancellationToken cancellationToken = default)
        {
            await ExecuteWithRetryInternalAsync(async (ct) => 
            {
                await operation(ct);
                return true; // 返回一个虚拟值
            }, context?.OperationName ?? "Unknown", retryPolicy?.MaxRetryCount ?? 3, cancellationToken);
        }

        /// <summary>
        /// 注册异常处理策略
        /// </summary>
        /// <param name="exceptionType">异常类型</param>
        /// <param name="strategy">处理策略</param>
        public void RegisterExceptionStrategy(Type exceptionType, IExceptionHandlingStrategy strategy)
        {
            if (exceptionType == null) throw new ArgumentNullException(nameof(exceptionType));
            if (strategy == null) throw new ArgumentNullException(nameof(strategy));
            
            _exceptionStrategies.AddOrUpdate(exceptionType, strategy, (key, oldValue) => strategy);
            _logger.LogInformation("已注册异常处理策略: {ExceptionType} -> {StrategyName}", exceptionType.Name, strategy.Name);
        }

        /// <summary>
        /// 移除异常处理策略
        /// </summary>
        /// <param name="exceptionType">异常类型</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveExceptionStrategy(Type exceptionType)
        {
            if (exceptionType == null) return false;
            
            var removed = _exceptionStrategies.TryRemove(exceptionType, out var strategy);
            if (removed)
            {
                _logger.LogInformation("已移除异常处理策略: {ExceptionType} -> {StrategyName}", exceptionType.Name, strategy?.Name);
            }
            return removed;
        }

        /// <summary>
        /// 获取异常统计信息
        /// </summary>
        /// <returns>异常统计信息</returns>
        public ExceptionStatistics GetExceptionStatistics()
        {
            var totalExceptions = _exceptionStats.Values.Sum(s => s.TotalCount);
            var handledException = totalExceptions; // 假设所有异常都已处理
            var unhandledException = 0;
            
            var exceptionTypeStats = new Dictionary<string, ExceptionTypeStatistics>();
            foreach (var stat in _exceptionStats.Values)
            {
                exceptionTypeStats[stat.ExceptionType] = new ExceptionTypeStatistics
                {
                    ExceptionType = stat.ExceptionType,
                    Count = stat.TotalCount,
                    LastOccurredAt = stat.LastOccurrence ?? DateTime.MinValue,
                    AverageHandlingTimeMs = 0, // 暂时设为0
                    RecoverySuccessCount = 0,
                    RecoveryFailureCount = 0
                };
            }
            
            return new ExceptionStatistics
            {
                TotalExceptions = totalExceptions,
                HandledException = handledException,
                UnhandledException = unhandledException,
                RetrySuccessCount = _retryContexts.Values.Count(r => r.IsSuccessful),
                RetryFailureCount = _retryContexts.Values.Count(r => !r.IsSuccessful),
                ExceptionTypeStats = exceptionTypeStats,
                StatisticsStartTime = DateTime.UtcNow.AddHours(-1), // 假设统计开始时间
                LastUpdatedAt = DateTime.UtcNow
            };
        }

        /// <summary>
        /// 重置异常统计
        /// </summary>
        public void ResetStatistics()
        {
            _exceptionStats.Clear();
            _logger.LogInformation("异常统计信息已重置");
        }

        /// <summary>
        /// 判断异常是否应该重试
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <returns>是否应该重试</returns>
        private bool ShouldRetryException(Exception exception)
        {
            // 网络相关异常通常可以重试
            return exception is System.Net.Sockets.SocketException ||
                   exception is System.Net.Http.HttpRequestException ||
                   exception is TimeoutException ||
                   exception.Message.Contains("timeout", StringComparison.OrdinalIgnoreCase) ||
                   exception.Message.Contains("connection", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// 计算重试延迟时间
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <returns>重试延迟时间（毫秒）</returns>
        private int CalculateRetryDelay(Exception exception)
        {
            // 根据异常类型返回不同的延迟时间
            if (exception is System.Net.Sockets.SocketException)
                return 2000;
            if (exception is TimeoutException)
                return 1000;
            return 500;
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 处理异常（接口实现）
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <param name="context">异常上下文</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异常处理结果</returns>
        public async Task<ExceptionHandlingResult> HandleExceptionAsync(Exception exception, ExceptionContext context, CancellationToken cancellationToken = default)
        {
            if (exception == null)
                return new ExceptionHandlingResult { IsHandled = false, Message = "异常对象为空" };

            try
            {
                // 检查是否有注册的策略可以处理此异常
                var strategy = _exceptionStrategies.Values.FirstOrDefault(s => s.CanHandle(exception, context));
                if (strategy != null)
                {
                    return await strategy.HandleAsync(exception, context, cancellationToken);
                }

                // 默认处理逻辑
                await HandleExceptionInternalAsync(exception, context?.OperationName ?? "Unknown", cancellationToken);
                
                return new ExceptionHandlingResult 
                { 
                    IsHandled = true, 
                    ShouldRetry = ShouldRetryException(exception),
                    RetryDelayMs = CalculateRetryDelay(exception),
                    StrategyName = "DefaultStrategy",
                    Message = "异常已处理"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理异常时发生错误");
                return new ExceptionHandlingResult { IsHandled = false, Message = $"处理异常时发生错误: {ex.Message}" };
            }
        }

        /// <summary>
        /// 处理异常（内部方法）
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <param name="context">上下文信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>处理任务</returns>
        public async Task HandleExceptionInternalAsync(Exception exception, string context = null, CancellationToken cancellationToken = default)
        {
            if (exception == null)
                return;
            
            try
            {
                var exceptionType = exception.GetType().Name;
                var contextKey = context ?? "Unknown";
                
                // 记录异常
                var record = new ExceptionRecord
                {
                    Exception = exception,
                    Context = contextKey,
                    Timestamp = DateTime.UtcNow,
                    ThreadId = Thread.CurrentThread.ManagedThreadId,
                    StackTrace = exception.StackTrace
                };
                
                // 添加到最近异常队列
                _recentExceptions.Enqueue(record);
                
                // 限制队列大小
                while (_recentExceptions.Count > 1000)
                {
                    _recentExceptions.TryDequeue(out _);
                }
                
                // 更新统计信息
                UpdateExceptionStatistics(exceptionType, contextKey);
                
                // 记录日志
                LogException(exception, contextKey);
                
                // 触发异常事件
                ExceptionOccurred?.Invoke(this, new ExceptionOccurredEventArgs
                {
                    Exception = exception,
                    Context = new ExceptionContext { OperationName = contextKey },
                    OccurredAt = DateTime.UtcNow,
                    IsHandled = true
                });
                
                // 检查是否需要特殊处理
                await HandleSpecialExceptionsAsync(exception, contextKey, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理异常时发生错误");
            }
        }
        
        /// <summary>
        /// 执行带重试的操作
        /// </summary>
        /// <param name="operation">要执行的操作</param>
        /// <param name="operationName">操作名称</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public async Task<T> ExecuteWithRetryAsync<T>(Func<CancellationToken, Task<T>> operation, string operationName = null, CancellationToken cancellationToken = default)
        {
            if (operation == null)
                throw new ArgumentNullException(nameof(operation));
            
            operationName ??= "UnknownOperation";
            var retryConfig = _configurationManager.GetRetryPolicyConfiguration();
            
            var contextKey = $"{operationName}_{Guid.NewGuid():N}";
            var retryContext = new RetryContext
            {
                OperationName = operationName,
                MaxAttempts = retryConfig.MaxRetryAttempts,
                CurrentAttempt = 0,
                BaseDelay = TimeSpan.FromMilliseconds(retryConfig.BaseDelayMs),
                MaxDelay = TimeSpan.FromMilliseconds(retryConfig.MaxDelayMs),
                BackoffMultiplier = retryConfig.BackoffMultiplier,
                EnableJitter = retryConfig.EnableJitter,
                JitterMax = TimeSpan.FromMilliseconds(retryConfig.JitterMaxMs),
                StartTime = DateTime.UtcNow
            };
            
            _retryContexts.TryAdd(contextKey, retryContext);
            
            try
            {
                await _retrySemaphore.WaitAsync(cancellationToken);
                
                while (retryContext.CurrentAttempt <= retryContext.MaxAttempts)
                {
                    try
                    {
                        retryContext.CurrentAttempt++;
                        retryContext.LastAttemptTime = DateTime.UtcNow;
                        
                        var result = await operation(cancellationToken);
                        
                        // 成功执行
                        retryContext.IsSuccessful = true;
                        retryContext.EndTime = DateTime.UtcNow;
                        
                        if (retryContext.CurrentAttempt > 1)
                        {
                            _logger.LogInformation("操作 {OperationName} 在第 {Attempt} 次尝试后成功", operationName, retryContext.CurrentAttempt);
                            
                            RetryExecuted?.Invoke(this, new RetryExecutedEventArgs
                            {
                                OperationName = operationName,
                                AttemptNumber = retryContext.CurrentAttempt,
                                IsSuccessful = true,
                                Context = retryContext,
                                Timestamp = DateTime.UtcNow
                            });
                        }
                        
                        return result;
                    }
                    catch (Exception ex) when (retryContext.CurrentAttempt <= retryContext.MaxAttempts)
                    {
                        retryContext.LastException = ex;
                        retryContext.Exceptions.Add(ex);
                        
                        // 记录异常
                        await HandleExceptionAsync(ex, $"Retry_{operationName}_Attempt_{retryContext.CurrentAttempt}", cancellationToken);
                        
                        if (retryContext.CurrentAttempt >= retryContext.MaxAttempts)
                        {
                            // 最后一次尝试失败
                            retryContext.IsSuccessful = false;
                            retryContext.EndTime = DateTime.UtcNow;
                            
                            _logger.LogError(ex, "操作 {OperationName} 在 {MaxAttempts} 次尝试后仍然失败", operationName, retryContext.MaxAttempts);
                            
                            RetryExecuted?.Invoke(this, new RetryExecutedEventArgs
                            {
                                OperationName = operationName,
                                AttemptNumber = retryContext.CurrentAttempt,
                                IsSuccessful = false,
                                Exception = ex,
                                Context = retryContext,
                                Timestamp = DateTime.UtcNow
                            });
                            
                            throw new RetryExhaustedException($"操作 {operationName} 在 {retryContext.MaxAttempts} 次重试后失败", ex);
                        }
                        
                        // 计算延迟时间
                        var delay = CalculateDelay(retryContext);
                        
                        _logger.LogWarning("操作 {OperationName} 第 {Attempt} 次尝试失败，{Delay}ms 后重试: {Error}", 
                            operationName, retryContext.CurrentAttempt, delay.TotalMilliseconds, ex.Message);
                        
                        RetryExecuted?.Invoke(this, new RetryExecutedEventArgs
                        {
                            OperationName = operationName,
                            AttemptNumber = retryContext.CurrentAttempt,
                            IsSuccessful = false,
                            Exception = ex,
                            RetryDelay = delay,
                            Context = retryContext,
                            Timestamp = DateTime.UtcNow
                        });
                        
                        // 等待重试
                        if (delay > TimeSpan.Zero)
                        {
                            await Task.Delay(delay, cancellationToken);
                        }
                    }
                }
                
                // 不应该到达这里
                throw new InvalidOperationException($"操作 {operationName} 重试逻辑异常");
            }
            finally
            {
                _retryContexts.TryRemove(contextKey, out _);
                _retrySemaphore.Release();
            }
        }
        
        /// <summary>
        /// 执行带重试的操作（无返回值）
        /// </summary>
        /// <param name="operation">要执行的操作</param>
        /// <param name="operationName">操作名称</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行任务</returns>
        public async Task ExecuteWithRetryAsync(Func<CancellationToken, Task> operation, string operationName = null, CancellationToken cancellationToken = default)
        {
            await ExecuteWithRetryAsync(async ct =>
            {
                await operation(ct);
                return true;
            }, operationName, cancellationToken);
        }
        
        /// <summary>
        /// 获取内部异常统计信息
        /// </summary>
        /// <param name="exceptionType">异常类型</param>
        /// <returns>统计信息</returns>
        public InternalExceptionStatistics GetInternalExceptionStatistics(string exceptionType = null)
        {
            if (string.IsNullOrWhiteSpace(exceptionType))
            {
                // 返回总体统计
                var totalStats = new InternalExceptionStatistics
                {
                    ExceptionType = "Total",
                    TotalCount = _exceptionStats.Values.Sum(s => s.TotalCount),
                    LastOccurrence = _exceptionStats.Values.Where(s => s.LastOccurrence.HasValue).Max(s => s.LastOccurrence),
                    FirstOccurrence = _exceptionStats.Values.Where(s => s.FirstOccurrence.HasValue).Min(s => s.FirstOccurrence),
                    Contexts = _exceptionStats.Values.SelectMany(s => s.Contexts).GroupBy(c => c.Key)
                        .ToDictionary(g => g.Key, g => g.Sum(c => c.Value))
                };
                
                return totalStats;
            }
            
            return _exceptionStats.TryGetValue(exceptionType, out var stats) ? stats : null;
        }
        
        /// <summary>
        /// 获取错误报告
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>错误报告</returns>
        public ErrorReport GenerateErrorReport(DateTime? startTime = null, DateTime? endTime = null)
        {
            startTime ??= DateTime.UtcNow.AddHours(-24);
            endTime ??= DateTime.UtcNow;
            
            var filteredExceptions = _recentExceptions
                .Where(e => e.Timestamp >= startTime && e.Timestamp <= endTime)
                .ToList();
            
            var report = new ErrorReport
            {
                StartTime = startTime.Value,
                EndTime = endTime.Value,
                TotalExceptions = filteredExceptions.Count,
                UniqueExceptionTypes = filteredExceptions.Select(e => e.Exception.GetType().Name).Distinct().Count(),
                ExceptionsByType = filteredExceptions
                    .GroupBy(e => e.Exception.GetType().Name)
                    .ToDictionary(g => g.Key, g => g.Count()),
                ExceptionsByContext = filteredExceptions
                    .GroupBy(e => e.Context)
                    .ToDictionary(g => g.Key, g => g.Count()),
                TopExceptions = filteredExceptions
                    .GroupBy(e => e.Exception.GetType().Name)
                    .OrderByDescending(g => g.Count())
                    .Take(10)
                    .Select(g => new ExceptionSummary
                    {
                        ExceptionType = g.Key,
                        Count = g.Count(),
                        LastOccurrence = g.Max(e => e.Timestamp),
                        SampleMessage = g.First().Exception.Message
                    })
                    .ToList(),
                RecentExceptions = filteredExceptions
                    .OrderByDescending(e => e.Timestamp)
                    .Take(50)
                    .ToList(),
                GeneratedAt = DateTime.UtcNow
            };
            
            // 触发报告生成事件
            ErrorReportGenerated?.Invoke(this, new ErrorReportGeneratedEventArgs
            {
                Report = report,
                Timestamp = DateTime.UtcNow
            });
            
            return report;
        }
        
        /// <summary>
        /// 清理过期数据
        /// </summary>
        /// <param name="retentionHours">保留小时数</param>
        /// <returns>清理任务</returns>
        public Task CleanupExpiredDataAsync(int retentionHours = 24)
        {
            return Task.Run(() =>
            {
                try
                {
                    var cutoffTime = DateTime.UtcNow.AddHours(-retentionHours);
                    
                    // 清理最近异常记录
                    var tempQueue = new ConcurrentQueue<ExceptionRecord>();
                    while (_recentExceptions.TryDequeue(out var record))
                    {
                        if (record.Timestamp >= cutoffTime)
                        {
                            tempQueue.Enqueue(record);
                        }
                    }
                    
                    // 重新添加未过期的记录
                    while (tempQueue.TryDequeue(out var record))
                    {
                        _recentExceptions.Enqueue(record);
                    }
                    
                    // 清理重试上下文
                    var expiredContexts = _retryContexts
                        .Where(kvp => kvp.Value.EndTime.HasValue && kvp.Value.EndTime < cutoffTime)
                        .Select(kvp => kvp.Key)
                        .ToList();
                    
                    foreach (var key in expiredContexts)
                    {
                        _retryContexts.TryRemove(key, out _);
                    }
                    
                    _logger.LogDebug("异常数据清理完成，清理了 {ExpiredContexts} 个过期重试上下文", expiredContexts.Count);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "清理过期异常数据时发生错误");
                }
            });
        }
        
        #endregion
        
        #region 私有方法
        
        /// <summary>
        /// 更新异常统计信息
        /// </summary>
        /// <param name="exceptionType">异常类型</param>
        /// <param name="context">上下文</param>
        private void UpdateExceptionStatistics(string exceptionType, string context)
        {
            _exceptionStats.AddOrUpdate(exceptionType, 
                new InternalExceptionStatistics
                {
                    ExceptionType = exceptionType,
                    TotalCount = 1,
                    FirstOccurrence = DateTime.UtcNow,
                    LastOccurrence = DateTime.UtcNow,
                    Contexts = new Dictionary<string, int> { { context, 1 } }
                },
                (key, existing) =>
                {
                    existing.TotalCount++;
                    existing.LastOccurrence = DateTime.UtcNow;
                    
                    if (existing.Contexts.ContainsKey(context))
                    {
                        existing.Contexts[context]++;
                    }
                    else
                    {
                        existing.Contexts[context] = 1;
                    }
                    
                    return existing;
                });
        }
        
        /// <summary>
        /// 记录异常日志
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <param name="context">上下文</param>
        private void LogException(Exception exception, string context)
        {
            var logLevel = DetermineLogLevel(exception);
            
            switch (logLevel)
            {
                case LogLevel.Critical:
                    _logger.LogCritical(exception, "严重异常发生在上下文 {Context}: {Message}", context, exception.Message);
                    break;
                case LogLevel.Error:
                    _logger.LogError(exception, "错误发生在上下文 {Context}: {Message}", context, exception.Message);
                    break;
                case LogLevel.Warning:
                    _logger.LogWarning(exception, "警告发生在上下文 {Context}: {Message}", context, exception.Message);
                    break;
                default:
                    _logger.LogInformation(exception, "异常发生在上下文 {Context}: {Message}", context, exception.Message);
                    break;
            }
        }
        
        /// <summary>
        /// 确定日志级别
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <returns>日志级别</returns>
        private LogLevel DetermineLogLevel(Exception exception)
        {
            return exception switch
            {
                OutOfMemoryException => LogLevel.Critical,
                StackOverflowException => LogLevel.Critical,
                AccessViolationException => LogLevel.Critical,
                ArgumentNullException => LogLevel.Error,
                ArgumentException => LogLevel.Error,
                InvalidOperationException => LogLevel.Error,
                NotSupportedException => LogLevel.Warning,
                TimeoutException => LogLevel.Warning,
                TaskCanceledException => LogLevel.Information,
                OperationCanceledException => LogLevel.Information,
                _ => LogLevel.Error
            };
        }
        
        /// <summary>
        /// 处理特殊异常
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <param name="context">上下文</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>处理任务</returns>
        private async Task HandleSpecialExceptionsAsync(Exception exception, string context, CancellationToken cancellationToken)
        {
            try
            {
                switch (exception)
                {
                    case OutOfMemoryException:
                        _logger.LogCritical("检测到内存不足异常，触发垃圾回收");
                        GC.Collect();
                        GC.WaitForPendingFinalizers();
                        GC.Collect();
                        break;
                        
                    case TimeoutException when context.Contains("Connection"):
                        _logger.LogWarning("检测到连接超时异常，可能需要调整连接参数");
                        break;
                        
                    case UnauthorizedAccessException:
                        _logger.LogError("检测到未授权访问异常，请检查权限配置");
                        break;
                        
                    case System.Net.Sockets.SocketException socketEx:
                        _logger.LogWarning("检测到网络异常: {SocketError}", socketEx.SocketErrorCode);
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理特殊异常时发生错误");
            }
        }
        
        /// <summary>
        /// 计算重试延迟时间
        /// </summary>
        /// <param name="context">重试上下文</param>
        /// <returns>延迟时间</returns>
        private TimeSpan CalculateDelay(RetryContext context)
        {
            if (context.CurrentAttempt <= 1)
                return TimeSpan.Zero;
            
            // 指数退避
            var delay = TimeSpan.FromTicks((long)(context.BaseDelay.Ticks * Math.Pow(context.BackoffMultiplier, context.CurrentAttempt - 2)));
            
            // 限制最大延迟
            if (delay > context.MaxDelay)
            {
                delay = context.MaxDelay;
            }
            
            // 添加抖动
            if (context.EnableJitter && context.JitterMax > TimeSpan.Zero)
            {
                var random = new Random();
                var jitter = TimeSpan.FromTicks((long)(random.NextDouble() * context.JitterMax.Ticks));
                delay = delay.Add(jitter);
            }
            
            return delay;
        }
        
        /// <summary>
        /// 清理回调
        /// </summary>
        /// <param name="state">状态对象</param>
        private async void CleanupCallback(object state)
        {
            try
            {
                await CleanupExpiredDataAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "定期清理异常数据时发生错误");
            }
        }
        
        /// <summary>
        /// 报告回调
        /// </summary>
        /// <param name="state">状态对象</param>
        private void ReportCallback(object state)
        {
            try
            {
                var report = GenerateErrorReport();
                
                if (report.TotalExceptions > 0)
                {
                    _logger.LogInformation("错误报告: 过去24小时内发生 {TotalExceptions} 个异常，涉及 {UniqueTypes} 种异常类型", 
                        report.TotalExceptions, report.UniqueExceptionTypes);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成定期错误报告时发生错误");
            }
        }
        
        #endregion
        
        #region IDisposable实现
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed)
                return;
            
            _isDisposed = true;
            
            try
            {
                // 停止定时器
                _cleanupTimer?.Dispose();
                _reportTimer?.Dispose();
                
                // 释放信号量
                _retrySemaphore?.Dispose();
                
                _logger.LogInformation("MQTT异常处理器已释放资源");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放MQTT异常处理器资源时发生错误");
            }
        }
        
        #endregion
    }
    
    #region 辅助类
    
    /// <summary>
    /// 异常记录
    /// </summary>
    public class ExceptionRecord
    {
        /// <summary>
        /// 异常对象
        /// </summary>
        public Exception Exception { get; set; }
        
        /// <summary>
        /// 上下文信息
        /// </summary>
        public string Context { get; set; }
        
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
        
        /// <summary>
        /// 线程ID
        /// </summary>
        public int ThreadId { get; set; }
        
        /// <summary>
        /// 堆栈跟踪
        /// </summary>
        public string StackTrace { get; set; }
    }
    
    /// <summary>
    /// 内部异常统计信息（用于内部统计）
    /// </summary>
    public class InternalExceptionStatistics
    {
        /// <summary>
        /// 异常类型
        /// </summary>
        public string ExceptionType { get; set; }
        
        /// <summary>
        /// 总次数
        /// </summary>
        public int TotalCount { get; set; }
        
        /// <summary>
        /// 首次发生时间
        /// </summary>
        public DateTime? FirstOccurrence { get; set; }
        
        /// <summary>
        /// 最后发生时间
        /// </summary>
        public DateTime? LastOccurrence { get; set; }
        
        /// <summary>
        /// 上下文统计
        /// </summary>
        public Dictionary<string, int> Contexts { get; set; } = new Dictionary<string, int>();
    }
    
    /// <summary>
    /// 重试上下文
    /// </summary>
    public class RetryContext
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        public string OperationName { get; set; }
        
        /// <summary>
        /// 最大尝试次数
        /// </summary>
        public int MaxAttempts { get; set; }
        
        /// <summary>
        /// 当前尝试次数
        /// </summary>
        public int CurrentAttempt { get; set; }
        
        /// <summary>
        /// 基础延迟时间
        /// </summary>
        public TimeSpan BaseDelay { get; set; }
        
        /// <summary>
        /// 最大延迟时间
        /// </summary>
        public TimeSpan MaxDelay { get; set; }
        
        /// <summary>
        /// 退避倍数
        /// </summary>
        public double BackoffMultiplier { get; set; }
        
        /// <summary>
        /// 启用抖动
        /// </summary>
        public bool EnableJitter { get; set; }
        
        /// <summary>
        /// 最大抖动时间
        /// </summary>
        public TimeSpan JitterMax { get; set; }
        
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }
        
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
        
        /// <summary>
        /// 最后尝试时间
        /// </summary>
        public DateTime? LastAttemptTime { get; set; }
        
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccessful { get; set; }
        
        /// <summary>
        /// 最后异常
        /// </summary>
        public Exception LastException { get; set; }
        
        /// <summary>
        /// 所有异常
        /// </summary>
        public List<Exception> Exceptions { get; set; } = new List<Exception>();
    }
    
    /// <summary>
    /// 错误报告
    /// </summary>
    public class ErrorReport
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }
        
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }
        
        /// <summary>
        /// 总异常数
        /// </summary>
        public int TotalExceptions { get; set; }
        
        /// <summary>
        /// 唯一异常类型数
        /// </summary>
        public int UniqueExceptionTypes { get; set; }
        
        /// <summary>
        /// 按类型分组的异常
        /// </summary>
        public Dictionary<string, int> ExceptionsByType { get; set; } = new Dictionary<string, int>();
        
        /// <summary>
        /// 按上下文分组的异常
        /// </summary>
        public Dictionary<string, int> ExceptionsByContext { get; set; } = new Dictionary<string, int>();
        
        /// <summary>
        /// 顶级异常
        /// </summary>
        public List<ExceptionSummary> TopExceptions { get; set; } = new List<ExceptionSummary>();
        
        /// <summary>
        /// 最近异常
        /// </summary>
        public List<ExceptionRecord> RecentExceptions { get; set; } = new List<ExceptionRecord>();
        
        /// <summary>
        /// 生成时间
        /// </summary>
        public DateTime GeneratedAt { get; set; }
    }
    
    /// <summary>
    /// 异常摘要
    /// </summary>
    public class ExceptionSummary
    {
        /// <summary>
        /// 异常类型
        /// </summary>
        public string ExceptionType { get; set; }
        
        /// <summary>
        /// 次数
        /// </summary>
        public int Count { get; set; }
        
        /// <summary>
        /// 最后发生时间
        /// </summary>
        public DateTime LastOccurrence { get; set; }
        
        /// <summary>
        /// 示例消息
        /// </summary>
        public string SampleMessage { get; set; }
    }
    
    /// <summary>
    /// 重试耗尽异常
    /// </summary>
    public class RetryExhaustedException : Exception
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="innerException">内部异常</param>
        public RetryExhaustedException(string message, Exception innerException = null) 
            : base(message, innerException)
        {
        }
    }
    
    #endregion
    
    #region 事件参数类
    
    /// <summary>
    /// 重试执行事件参数
    /// </summary>
    public class RetryExecutedEventArgs : EventArgs
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        public string OperationName { get; set; }
        
        /// <summary>
        /// 尝试次数
        /// </summary>
        public int AttemptNumber { get; set; }
        
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccessful { get; set; }
        
        /// <summary>
        /// 异常对象
        /// </summary>
        public Exception Exception { get; set; }
        
        /// <summary>
        /// 重试延迟
        /// </summary>
        public TimeSpan? RetryDelay { get; set; }
        
        /// <summary>
        /// 重试上下文
        /// </summary>
        public RetryContext Context { get; set; }
        
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
    
    /// <summary>
    /// 错误报告生成事件参数
    /// </summary>
    public class ErrorReportGeneratedEventArgs : EventArgs
    {
        /// <summary>
        /// 错误报告
        /// </summary>
        public ErrorReport Report { get; set; }
        
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
    
    #endregion
}