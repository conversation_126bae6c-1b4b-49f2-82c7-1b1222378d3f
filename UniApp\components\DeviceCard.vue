<template>
  <view class="device-card" :class="cardClass" @click="handleClick">
    <!-- 设备状态指示器 -->
    <view class="device-status-indicator" :class="statusClass"></view>
    
    <!-- 设备图标 -->
    <view class="device-icon">
      <IconLoader 
        :name="deviceIconName" 
        :size="48" 
        :custom-svg-path="deviceIcon"
        @error="onIconError"
        @load="onIconLoad"
      />
    </view>
    
    <!-- 设备信息 -->
    <view class="device-info">
      <view class="device-name">{{ device.name || '未命名设备' }}</view>
      <view class="device-location">{{ device.location || '未设置位置' }}</view>
      <view class="device-type">{{ deviceTypeText }}</view>
    </view>
    
    <!-- 设备控制区域 -->
    <view class="device-controls" v-if="showControls">
      <!-- 开关控制 -->
      <view class="control-item" v-if="device.supportSwitch">
        <switch 
          :checked="device.status === 'on'"
          @change="handleSwitchChange"
          :disabled="!device.online || loading"
          color="#007AFF"
        />
      </view>
      
      <!-- 亮度控制 -->
      <view class="control-item" v-if="device.supportBrightness && device.status === 'on'">
        <view class="brightness-control">
          <text class="control-label">亮度</text>
          <slider 
            :value="device.brightness || 0"
            @change="handleBrightnessChange"
            :disabled="!device.online || loading"
            min="0"
            max="100"
            activeColor="#007AFF"
            backgroundColor="#E5E5E5"
            block-size="16"
          />
          <text class="brightness-value">{{ device.brightness || 0 }}%</text>
        </view>
      </view>
    </view>
    
    <!-- 设备状态信息 -->
    <view class="device-status">
      <view class="status-row">
        <view class="status-item">
          <text class="status-label">状态:</text>
          <StatusBadge :status="device.status" :type="'device'" />
        </view>
        <view class="status-item" v-if="device.online !== undefined">
          <text class="status-label">连接:</text>
          <StatusBadge :status="device.online ? 'online' : 'offline'" :type="'connection'" />
        </view>
      </view>
      
      <view class="status-row" v-if="showEnergyInfo">
        <view class="status-item" v-if="device.power !== undefined">
          <text class="status-label">功率:</text>
          <text class="status-value">{{ formatPower(device.power) }}</text>
        </view>
        <view class="status-item" v-if="device.energy !== undefined">
          <text class="status-label">能耗:</text>
          <text class="status-value">{{ formatEnergy(device.energy) }}</text>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="device-actions" v-if="showActions">
      <button 
        class="action-btn primary"
        size="mini"
        @click.stop="handleEdit"
        :disabled="loading"
      >
        编辑
      </button>
      <button 
        class="action-btn secondary"
        size="mini"
        @click.stop="handleMore"
        :disabled="loading"
      >
        更多
      </button>
    </view>
    
    <!-- 加载状态 -->
    <view class="loading-overlay" v-if="loading">
      <LoadingSpinner size="small" />
    </view>
  </view>
</template>

<script setup>
import { computed, ref } from 'vue'
import StatusBadge from './StatusBadge.vue'
import LoadingSpinner from './LoadingSpinner.vue'
import IconLoader from './IconLoader.vue'
import { formatUtils } from '@/utils'

// Props定义
const props = defineProps({
  // 设备数据
  device: {
    type: Object,
    required: true,
    default: () => ({})
  },
  // 是否显示控制组件
  showControls: {
    type: Boolean,
    default: true
  },
  // 是否显示操作按钮
  showActions: {
    type: Boolean,
    default: true
  },
  // 是否显示能耗信息
  showEnergyInfo: {
    type: Boolean,
    default: true
  },
  // 卡片大小
  size: {
    type: String,
    default: 'normal', // normal, small, large
    validator: (value) => ['small', 'normal', 'large'].includes(value)
  },
  // 是否可点击
  clickable: {
    type: Boolean,
    default: true
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  }
})

// Events定义
const emit = defineEmits([
  'click',
  'switch-change',
  'brightness-change',
  'edit',
  'more'
])

// 响应式数据
const internalLoading = ref(false)

// 计算属性
const cardClass = computed(() => {
  return {
    [`device-card--${props.size}`]: true,
    'device-card--clickable': props.clickable,
    'device-card--loading': props.loading || internalLoading.value,
    'device-card--offline': !props.device.online,
    'device-card--error': props.device.status === 'error'
  }
})

const statusClass = computed(() => {
  const status = props.device.status
  const online = props.device.online
  
  if (!online) return 'status-offline'
  if (status === 'on') return 'status-on'
  if (status === 'off') return 'status-off'
  if (status === 'error') return 'status-error'
  return 'status-unknown'
})

const deviceIconName = computed(() => {
  const type = props.device.type
  const status = props.device.status
  
  // 根据设备类型和状态返回对应图标名称
  const iconMap = {
    'light': status === 'on' ? 'light-on' : 'light-off',
    'led': status === 'on' ? 'led-on' : 'led-off',
    'sensor': 'sensor',
    'switch': 'switch'
  }
  
  return iconMap[type] || 'device-default'
})

const deviceIcon = computed(() => {
  const type = props.device.type
  const status = props.device.status
  
  // 根据设备类型和状态返回对应图标
  const iconMap = {
    'light': status === 'on' ? '/static/icons/light-on.svg' : '/static/icons/light-off.svg',
    'led': status === 'on' ? '/static/icons/led-on.svg' : '/static/icons/led-off.svg',
    'sensor': '/static/icons/sensor.svg',
    'switch': '/static/icons/switch.svg'
  }
  
  return iconMap[type] || '/static/icons/device-default.svg'
})

const deviceTypeText = computed(() => {
  const typeMap = {
    'light': '照明灯',
    'led': 'LED灯',
    'sensor': '传感器',
    'switch': '开关',
    'dimmer': '调光器'
  }
  
  return typeMap[props.device.type] || '未知设备'
})

// 方法
const handleClick = () => {
  if (props.clickable && !props.loading && !internalLoading.value) {
    emit('click', props.device)
  }
}

const handleSwitchChange = async (event) => {
  if (props.loading || internalLoading.value) return
  
  internalLoading.value = true
  try {
    const newStatus = event.detail.value ? 'on' : 'off'
    emit('switch-change', {
      device: props.device,
      status: newStatus
    })
  } finally {
    // 延迟重置加载状态，给用户反馈
    setTimeout(() => {
      internalLoading.value = false
    }, 500)
  }
}

const handleBrightnessChange = async (event) => {
  if (props.loading || internalLoading.value) return
  
  const brightness = event.detail.value
  emit('brightness-change', {
    device: props.device,
    brightness
  })
}

const handleEdit = () => {
  emit('edit', props.device)
}

const handleMore = () => {
  emit('more', props.device)
}

const formatPower = (power) => {
  return formatUtils.numberFormat.power(power)
}

const formatEnergy = (energy) => {
  return formatUtils.numberFormat.energy(energy)
}

const onIconError = (error) => {
  console.warn('Device icon load error:', error)
}

const onIconLoad = () => {
  // 图标加载成功
}
</script>

<style lang="scss" scoped>
.device-card {
  position: relative;
  background: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  
  &--clickable {
    &:active {
      transform: scale(0.98);
      box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.12);
    }
  }
  
  &--loading {
    opacity: 0.7;
    pointer-events: none;
  }
  
  &--offline {
    border-color: #ff4757;
    background: #fff5f5;
  }
  
  &--error {
    border-color: #ff6b6b;
    background: #fff0f0;
  }
  
  &--small {
    padding: 16rpx;
    
    .device-name {
      font-size: 28rpx;
    }
    
    .device-location {
      font-size: 22rpx;
    }
  }
  
  &--large {
    padding: 32rpx;
    
    .device-name {
      font-size: 36rpx;
    }
    
    .device-location {
      font-size: 28rpx;
    }
  }
}

.device-status-indicator {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  
  &.status-on {
    background: #2ed573;
    box-shadow: 0 0 8rpx rgba(46, 213, 115, 0.5);
  }
  
  &.status-off {
    background: #a4b0be;
  }
  
  &.status-error {
    background: #ff4757;
    box-shadow: 0 0 8rpx rgba(255, 71, 87, 0.5);
  }
  
  &.status-offline {
    background: #ff6b6b;
    animation: blink 1s infinite;
  }
  
  &.status-unknown {
    background: #ffa502;
  }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

.device-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
  
  .icon-image {
    width: 100%;
    height: 100%;
  }
}

.device-info {
  margin-bottom: 20rpx;
  
  .device-name {
    font-size: 32rpx;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8rpx;
    line-height: 1.2;
  }
  
  .device-location {
    font-size: 24rpx;
    color: #7f8c8d;
    margin-bottom: 4rpx;
  }
  
  .device-type {
    font-size: 22rpx;
    color: #95a5a6;
  }
}

.device-controls {
  margin-bottom: 20rpx;
  
  .control-item {
    margin-bottom: 16rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .brightness-control {
    display: flex;
    align-items: center;
    
    .control-label {
      font-size: 24rpx;
      color: #34495e;
      margin-right: 16rpx;
      min-width: 60rpx;
    }
    
    slider {
      flex: 1;
      margin: 0 16rpx;
    }
    
    .brightness-value {
      font-size: 22rpx;
      color: #7f8c8d;
      min-width: 60rpx;
      text-align: right;
    }
  }
}

.device-status {
  margin-bottom: 20rpx;
  
  .status-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .status-item {
    display: flex;
    align-items: center;
    flex: 1;
    
    .status-label {
      font-size: 22rpx;
      color: #7f8c8d;
      margin-right: 8rpx;
    }
    
    .status-value {
      font-size: 22rpx;
      color: #2c3e50;
      font-weight: 500;
    }
  }
}

.device-actions {
  display: flex;
  gap: 16rpx;
  
  .action-btn {
    flex: 1;
    height: 60rpx;
    line-height: 60rpx;
    border-radius: 8rpx;
    font-size: 24rpx;
    border: none;
    
    &.primary {
      background: #007AFF;
      color: #ffffff;
      
      &:disabled {
        background: #c7c7cc;
      }
    }
    
    &.secondary {
      background: #f8f9fa;
      color: #007AFF;
      border: 2rpx solid #007AFF;
      
      &:disabled {
        background: #f8f9fa;
        color: #c7c7cc;
        border-color: #c7c7cc;
      }
    }
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
}
</style>