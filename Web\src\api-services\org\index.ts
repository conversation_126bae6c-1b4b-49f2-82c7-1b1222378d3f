import { service as request } from '/@/utils/request';
import { useBaseApi } from '../base';

/**
 * 组织管理API接口集合
 * @method getPage 获取组织分页列表
 * @method getDetail 获取组织详情
 * @method add 添加组织
 * @method update 更新组织
 * @method delete 删除组织
 * @method getTree 获取组织树
 * @method getList 获取组织列表
 */
export function useOrgApi() {
	const baseApi = useBaseApi('sysOrg');
	
	return {
		...baseApi,
		// 获取组织树
		getTree: (parentId?: number, name?: string, code?: string, type?: number, tenantId?: number) => {
			return request({
				url: '/api/sysOrg/tree',
				method: 'get',
				params: { parentId, name, code, type, tenantId },
			});
		},
		// 获取组织列表
		getList: (parentId?: number, name?: string, code?: string, type?: number) => {
			return request({
				url: '/api/sysOrg/list',
				method: 'get',
				params: { parentId, name, code, type },
			});
		},
	};
}