<template>
  <view class="icon-loader" :style="containerStyle">
    <!-- 优先尝试加载SVG图标 -->
    <image 
      v-if="!useFallback"
      :src="svgPath" 
      :style="imageStyle"
      :mode="mode"
      @error="onSvgError"
      @load="onSvgLoad"
      class="icon-image"
    />
    
    <!-- SVG加载失败时的PNG备用图标 -->
    <image 
      v-else-if="pngPath"
      :src="pngPath" 
      :style="imageStyle"
      :mode="mode"
      @error="onPngError"
      @load="onPngLoad"
      class="icon-image"
    />
    
    <!-- 所有格式都失败时的默认图标 -->
    <view v-else class="icon-fallback" :style="fallbackStyle">
      <text class="fallback-text">{{ fallbackText }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'IconLoader',
  props: {
    // 图标名称（不包含扩展名）
    name: {
      type: String,
      required: true
    },
    // 图标尺寸
    size: {
      type: [String, Number],
      default: 24
    },
    // 图标颜色（仅对SVG有效）
    color: {
      type: String,
      default: '#666666'
    },
    // 图片模式
    mode: {
      type: String,
      default: 'aspectFit'
    },
    // 是否启用PNG备用
    enablePngFallback: {
      type: Boolean,
      default: true
    },
    // 自定义SVG路径
    customSvgPath: {
      type: String,
      default: ''
    },
    // 自定义PNG路径
    customPngPath: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      useFallback: false,
      svgLoaded: false,
      pngLoaded: false,
      loadError: false
    }
  },
  computed: {
    svgPath() {
      if (this.customSvgPath) {
        return this.customSvgPath
      }
      return `/static/icons/${this.name}.svg`
    },
    pngPath() {
      if (!this.enablePngFallback) {
        return ''
      }
      if (this.customPngPath) {
        return this.customPngPath
      }
      return `/static/icons/${this.name}.png`
    },
    containerStyle() {
      const size = typeof this.size === 'number' ? `${this.size}rpx` : this.size
      return {
        width: size,
        height: size,
        display: 'inline-block',
        verticalAlign: 'middle'
      }
    },
    imageStyle() {
      const size = typeof this.size === 'number' ? `${this.size}rpx` : this.size
      return {
        width: size,
        height: size
      }
    },
    fallbackStyle() {
      const size = typeof this.size === 'number' ? `${this.size}rpx` : this.size
      return {
        width: size,
        height: size,
        backgroundColor: '#f0f0f0',
        borderRadius: '4rpx',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }
    },
    fallbackText() {
      return this.name.charAt(0).toUpperCase()
    }
  },
  methods: {
    onSvgLoad() {
      this.svgLoaded = true
      this.$emit('load', { type: 'svg', path: this.svgPath })
    },
    onSvgError(e) {
      console.warn(`SVG图标加载失败: ${this.svgPath}`, e)
      if (this.enablePngFallback && this.pngPath) {
        this.useFallback = true
      } else {
        this.loadError = true
      }
      this.$emit('error', { type: 'svg', path: this.svgPath, error: e })
    },
    onPngLoad() {
      this.pngLoaded = true
      this.$emit('load', { type: 'png', path: this.pngPath })
    },
    onPngError(e) {
      console.warn(`PNG图标加载失败: ${this.pngPath}`, e)
      this.loadError = true
      this.$emit('error', { type: 'png', path: this.pngPath, error: e })
    },
    // 重新加载图标
    reload() {
      this.useFallback = false
      this.svgLoaded = false
      this.pngLoaded = false
      this.loadError = false
    }
  },
  watch: {
    name() {
      this.reload()
    }
  }
}
</script>

<style scoped>
.icon-loader {
  position: relative;
}

.icon-image {
  display: block;
}

.icon-fallback {
  border: 1rpx solid #d9d9d9;
}

.fallback-text {
  font-size: 20rpx;
  color: #999;
  font-weight: bold;
}
</style>