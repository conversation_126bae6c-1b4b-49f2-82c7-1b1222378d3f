// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System.Collections.Generic;

namespace Admin.NET.Core.Service;

/// <summary>
/// 设备分组管理服务 🧩
/// </summary>
[ApiDescriptionSettings(Order = 501)]
public class EnergyDeviceGroupService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<EnergyDeviceGroup> _energyDeviceGroupRep;
    private readonly SqlSugarRepository<EnergyDevice> _energyDeviceRep;
    private readonly SysCacheService _sysCacheService;
    private readonly UserManager _userManager;

    public EnergyDeviceGroupService(
        SqlSugarRepository<EnergyDeviceGroup> energyDeviceGroupRep,
        SqlSugarRepository<EnergyDevice> energyDeviceRep,
        SysCacheService sysCacheService,
        UserManager userManager)
    {
        _energyDeviceGroupRep = energyDeviceGroupRep;
        _energyDeviceRep = energyDeviceRep;
        _sysCacheService = sysCacheService;
        _userManager = userManager;
    }

    /// <summary>
    /// 获取设备分组分页列表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取设备分组分页列表")]
    public async Task<SqlSugarPagedList<EnergyDeviceGroupOutput>> GetPage([FromQuery] EnergyDeviceGroupInput input)
    {
        var query = _energyDeviceGroupRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.GroupName), u => u.GroupName.Contains(input.GroupName))
            .WhereIF(!string.IsNullOrWhiteSpace(input.GroupCode), u => u.GroupCode.Contains(input.GroupCode))
            .WhereIF(input.GroupType > 0, u => u.GroupType == input.GroupType)
            .WhereIF(input.Status > 0, u => u.Status == input.Status)
            .WhereIF(input.ParentId > 0, u => u.ParentId == input.ParentId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Location), u => u.Location.Contains(input.Location))
            .Select(u => new EnergyDeviceGroupOutput
            {
                Id = u.Id,
                GroupCode = u.GroupCode,
                GroupName = u.GroupName,
                GroupType = u.GroupType,
                ParentId = u.ParentId,
                Location = u.Location,
                Description = u.Description,
                Status = u.Status,
                OrderNo = u.OrderNo,
                CreateTime = u.CreateTime,
                TenantId = u.TenantId
            })
            .OrderBy(u => u.OrderNo)
            .OrderBy(u => u.CreateTime, OrderByType.Desc);

        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取设备分组树形列表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取设备分组树形列表")]
    public async Task<List<EnergyDeviceGroupOutput>> GetTree([FromQuery] EnergyDeviceGroupInput input)
    {
        var groupList = await _energyDeviceGroupRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.GroupName), u => u.GroupName.Contains(input.GroupName))
            .WhereIF(!string.IsNullOrWhiteSpace(input.GroupCode), u => u.GroupCode.Contains(input.GroupCode))
            .WhereIF(input.GroupType > 0, u => u.GroupType == input.GroupType)
            .WhereIF(input.Status > 0, u => u.Status == input.Status)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Location), u => u.Location.Contains(input.Location))
            .Select(u => new EnergyDeviceGroupOutput
            {
                Id = u.Id,
                GroupCode = u.GroupCode,
                GroupName = u.GroupName,
                GroupType = u.GroupType,
                ParentId = u.ParentId,
                Location = u.Location,
                Description = u.Description,
                Status = u.Status,
                OrderNo = u.OrderNo,
                CreateTime = u.CreateTime,
                TenantId = u.TenantId
            })
            .OrderBy(u => u.OrderNo)
            .OrderBy(u => u.CreateTime, OrderByType.Desc)
            .ToListAsync();

        // 构造树形结构
        return groupList.ToTree(it => it.Children, it => it.ParentId ?? 0L, 0).ToList();
    }

    /// <summary>
    /// 获取设备分组详情 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取设备分组详情")]
    public async Task<EnergyDeviceGroupDetailOutput> GetDetail([FromQuery] BaseIdInput input)
    {
        var group = await _energyDeviceGroupRep.GetByIdAsync(input.Id);
        if (group == null)
            throw Oops.Oh("设备分组不存在");

        var result = group.Adapt<EnergyDeviceGroupDetailOutput>();

        // 统计分组下的设备数量
        result.StatInfo.DeviceCount = await _energyDeviceRep.CountAsync(u => u.GroupId == input.Id);
        result.StatInfo.OnlineDeviceCount = await _energyDeviceRep.CountAsync(u => u.GroupId == input.Id && u.IsOnline == true);
        result.StatInfo.OfflineDeviceCount = result.StatInfo.DeviceCount - result.StatInfo.OnlineDeviceCount;
        result.StatInfo.FaultDeviceCount = await _energyDeviceRep.CountAsync(u => u.GroupId == input.Id && u.FaultCount > 0);

        // 获取子分组数量
        result.StatInfo.ChildGroupCount = await _energyDeviceGroupRep.CountAsync(u => u.ParentId == input.Id);

        return result;
    }

    /// <summary>
    /// 增加设备分组 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [DisplayName("增加设备分组")]
    public async Task<long> AddGroup(AddEnergyDeviceGroupInput input)
    {
        // 检查分组编码是否存在
        var isExist = await _energyDeviceGroupRep.IsAnyAsync(u => u.GroupCode == input.GroupCode);
        if (isExist)
            throw Oops.Oh("分组编码已存在");

        // 检查分组名称是否存在
        var nameExist = await _energyDeviceGroupRep.IsAnyAsync(u => u.GroupName == input.GroupName && u.ParentId == input.ParentId);
        if (nameExist)
            throw Oops.Oh("同级分组名称已存在");

        // 检查父分组是否存在
        if (input.ParentId > 0)
        {
            var parentExist = await _energyDeviceGroupRep.IsAnyAsync(u => u.Id == input.ParentId);
            if (!parentExist)
                throw Oops.Oh("父分组不存在");
        }

        var group = input.Adapt<EnergyDeviceGroup>();
        group.TenantId = _userManager.TenantId;
        group.CreateUserId = _userManager.UserId;
        group.CreateTime = DateTime.Now;
        group.Status = (int)StatusEnum.Enable;

        var newGroup = await _energyDeviceGroupRep.AsInsertable(group).ExecuteReturnEntityAsync();
        return newGroup.Id;
    }

    /// <summary>
    /// 更新设备分组 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [DisplayName("更新设备分组")]
    public async Task UpdateGroup(UpdateEnergyDeviceGroupInput input)
    {
        // 检查分组是否存在
        var group = await _energyDeviceGroupRep.GetByIdAsync(input.Id);
        if (group == null)
            throw Oops.Oh("设备分组不存在");

        // 检查是否设置自己为父分组
        if (input.Id == input.ParentId)
            throw Oops.Oh("不能设置自己为父分组");

        // 检查分组编码是否重复
        var isExist = await _energyDeviceGroupRep.IsAnyAsync(u => u.GroupCode == input.GroupCode && u.Id != input.Id);
        if (isExist)
            throw Oops.Oh("分组编码已存在");

        // 检查分组名称是否重复
        var nameExist = await _energyDeviceGroupRep.IsAnyAsync(u => u.GroupName == input.GroupName && u.ParentId == input.ParentId && u.Id != input.Id);
        if (nameExist)
            throw Oops.Oh("同级分组名称已存在");

        // 检查父分组是否存在
        if (input.ParentId > 0)
        {
            var parentExist = await _energyDeviceGroupRep.IsAnyAsync(u => u.Id == input.ParentId);
            if (!parentExist)
                throw Oops.Oh("父分组不存在");

            // 检查是否形成循环引用
            var childGroups = await GetChildGroupIds(input.Id);
            if (input.ParentId.HasValue && childGroups.Contains(input.ParentId.Value))
                throw Oops.Oh("不能设置子分组为父分组");
        }

        var updateGroup = input.Adapt<EnergyDeviceGroup>();
        updateGroup.UpdateUserId = _userManager.UserId;
        updateGroup.UpdateTime = DateTime.Now;

        await _energyDeviceGroupRep.AsUpdateable(updateGroup).ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除设备分组 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    [DisplayName("删除设备分组")]
    public async Task DeleteGroup(DeleteEnergyDeviceGroupInput input)
    {
        var group = await _energyDeviceGroupRep.GetByIdAsync(input.Id);
        if (group == null)
            throw Oops.Oh("设备分组不存在");

        // 检查是否有子分组
        var hasChild = await _energyDeviceGroupRep.IsAnyAsync(u => u.ParentId == input.Id);
        if (hasChild)
            throw Oops.Oh("存在子分组，无法删除");

        // 检查是否有关联设备
        var hasDevice = await _energyDeviceRep.IsAnyAsync(u => u.GroupId == input.Id);
        if (hasDevice)
            throw Oops.Oh("分组下存在设备，无法删除");

        // 软删除分组
        await _energyDeviceGroupRep.FakeDeleteAsync(group);
    }

    /// <summary>
    /// 设置分组状态 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置分组状态")]
    public async Task<int> SetStatus(EnergyDeviceGroupStatusInput input)
    {
        var group = await _energyDeviceGroupRep.GetByIdAsync(input.Id);
        if (group == null)
            throw Oops.Oh("设备分组不存在");

        group.Status = input.Status;
        group.UpdateUserId = _userManager.UserId;
        group.UpdateTime = DateTime.Now;

        return await _energyDeviceGroupRep.AsUpdateable(group)
            .UpdateColumns(u => new { u.Status, u.UpdateUserId, u.UpdateTime })
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取分组统计信息 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取分组统计信息")]
    public async Task<EnergyDeviceGroupStatOutput> GetGroupStat()
    {
        var totalCount = await _energyDeviceGroupRep.AsQueryable().CountAsync();
        var enableCount = await _energyDeviceGroupRep.AsQueryable().CountAsync(u => u.Status == (int)StatusEnum.Enable);
        var disableCount = await _energyDeviceGroupRep.AsQueryable().CountAsync(u => u.Status == (int)StatusEnum.Disable);
        var rootCount = await _energyDeviceGroupRep.AsQueryable().CountAsync(u => u.ParentId == 0);

        return new EnergyDeviceGroupStatOutput
        {
            TotalCount = totalCount,
            EnableCount = enableCount,
            DisableCount = disableCount,
            RootGroupCount = rootCount,
            ChildGroupCount = totalCount - rootCount
        };
    }

    /// <summary>
    /// 获取子分组ID列表（递归）
    /// </summary>
    /// <param name="parentId"></param>
    /// <returns></returns>
    [NonAction]
    private async Task<List<long>> GetChildGroupIds(long parentId)
    {
        var childIds = new List<long>();
        var directChildren = await _energyDeviceGroupRep.AsQueryable()
            .Where(u => u.ParentId == parentId)
            .Select(u => u.Id)
            .ToListAsync();

        childIds.AddRange(directChildren);

        foreach (var childId in directChildren)
        {
            var grandChildren = await GetChildGroupIds(childId);
            childIds.AddRange(grandChildren);
        }

        return childIds;
    }
}