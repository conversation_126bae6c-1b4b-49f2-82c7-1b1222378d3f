<template>
  <view class="error-logs-page">
    <view class="header">
      <text class="title">错误日志调试</text>
      <view class="actions">
        <button @click="refreshLogs" class="btn refresh">刷新</button>
        <button @click="clearLogs" class="btn clear">清除</button>
      </view>
    </view>
    
    <view class="stats">
      <view class="stat-item">
        <text class="stat-label">总错误数:</text>
        <text class="stat-value">{{ totalErrors }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">严重错误:</text>
        <text class="stat-value critical">{{ criticalErrors }}</text>
      </view>
    </view>
    
    <view class="error-list">
      <view v-for="(error, index) in errorLogs" :key="error.id" class="error-item" :class="error.error?.level?.toLowerCase()">
        <view class="error-header">
          <text class="error-index">#{{ index + 1 }}</text>
          <text class="error-level">{{ error.error?.level || 'UNKNOWN' }}</text>
          <text class="error-time">{{ formatTime(error.timestamp) }}</text>
        </view>
        
        <view class="error-content">
          <view class="error-message">
            <text class="label">消息:</text>
            <text class="value">{{ error.error?.message || '无消息' }}</text>
          </view>
          
          <view class="error-type" v-if="error.error?.type">
            <text class="label">类型:</text>
            <text class="value">{{ error.error.type }}</text>
          </view>
          
          <view class="error-context" v-if="error.context">
            <text class="label">上下文:</text>
            <text class="value">{{ JSON.stringify(error.context, null, 2) }}</text>
          </view>
          
          <view class="error-stack" v-if="error.error?.stack">
            <text class="label">堆栈:</text>
            <text class="value stack">{{ error.error.stack }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <view v-if="errorLogs.length === 0" class="empty">
      <text>暂无错误日志</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      errorLogs: [],
      totalErrors: 0,
      criticalErrors: 0
    }
  },
  
  onLoad() {
    this.loadErrorLogs()
  },
  
  methods: {
    loadErrorLogs() {
      try {
        const logs = uni.getStorageSync('error_logs')
        if (logs) {
          this.errorLogs = JSON.parse(logs)
          this.totalErrors = this.errorLogs.length
          this.criticalErrors = this.errorLogs.filter(log => log.error?.level === 'CRITICAL').length
        } else {
          this.errorLogs = []
          this.totalErrors = 0
          this.criticalErrors = 0
        }
      } catch (e) {
        console.error('加载错误日志失败:', e)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },
    
    refreshLogs() {
      this.loadErrorLogs()
      uni.showToast({
        title: '已刷新',
        icon: 'success'
      })
    },
    
    clearLogs() {
      uni.showModal({
        title: '确认清除',
        content: '确定要清除所有错误日志吗？',
        success: (res) => {
          if (res.confirm) {
            try {
              uni.removeStorageSync('error_logs')
              this.loadErrorLogs()
              uni.showToast({
                title: '已清除',
                icon: 'success'
              })
            } catch (e) {
              console.error('清除错误日志失败:', e)
              uni.showToast({
                title: '清除失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },
    
    formatTime(timestamp) {
      const date = new Date(timestamp)
      return date.toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.error-logs-page {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
  
  .actions {
    display: flex;
    gap: 20rpx;
  }
  
  .btn {
    padding: 10rpx 20rpx;
    border-radius: 8rpx;
    font-size: 28rpx;
    border: none;
    
    &.refresh {
      background-color: #007aff;
      color: white;
    }
    
    &.clear {
      background-color: #ff3b30;
      color: white;
    }
  }
}

.stats {
  display: flex;
  gap: 40rpx;
  margin-bottom: 30rpx;
  
  .stat-item {
    display: flex;
    align-items: center;
    gap: 10rpx;
    
    .stat-label {
      font-size: 28rpx;
      color: #666;
    }
    
    .stat-value {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      
      &.critical {
        color: #ff3b30;
      }
    }
  }
}

.error-list {
  .error-item {
    background-color: white;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    padding: 20rpx;
    border-left: 8rpx solid #ddd;
    
    &.critical {
      border-left-color: #ff3b30;
    }
    
    &.high {
      border-left-color: #ff9500;
    }
    
    &.medium {
      border-left-color: #ffcc00;
    }
    
    &.low {
      border-left-color: #34c759;
    }
  }
  
  .error-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15rpx;
    
    .error-index {
      font-size: 24rpx;
      color: #999;
    }
    
    .error-level {
      font-size: 24rpx;
      padding: 4rpx 12rpx;
      border-radius: 20rpx;
      background-color: #f0f0f0;
      color: #666;
    }
    
    .error-time {
      font-size: 24rpx;
      color: #999;
    }
  }
  
  .error-content {
    .label {
      font-size: 26rpx;
      color: #666;
      font-weight: bold;
    }
    
    .value {
      font-size: 28rpx;
      color: #333;
      margin-left: 10rpx;
      
      &.stack {
        font-family: monospace;
        font-size: 24rpx;
        white-space: pre-wrap;
        background-color: #f8f8f8;
        padding: 10rpx;
        border-radius: 6rpx;
        margin-top: 10rpx;
      }
    }
    
    .error-message,
    .error-type,
    .error-context,
    .error-stack {
      margin-bottom: 10rpx;
      display: flex;
      flex-direction: column;
    }
  }
}

.empty {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}
</style>