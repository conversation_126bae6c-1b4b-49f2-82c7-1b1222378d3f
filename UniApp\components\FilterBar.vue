<template>
  <view class="filter-bar" :class="barClass">
    <!-- 搜索框 -->
    <view class="search-section" v-if="showSearch">
      <view class="search-input-wrapper">
        <input 
          class="search-input"
          type="text"
          :placeholder="searchPlaceholder"
          :value="searchValue"
          @input="handleSearchInput"
          @confirm="handleSearchConfirm"
          :maxlength="searchMaxLength"
        />
        <view class="search-icon" @click="handleSearchConfirm">
          🔍
        </view>
        <view class="clear-icon" v-if="searchValue" @click="handleSearchClear">
          ✕
        </view>
      </view>
    </view>
    
    <!-- 筛选条件 -->
    <view class="filter-section" v-if="showFilters">
      <scroll-view class="filter-scroll" scroll-x="true" :show-scrollbar="false">
        <view class="filter-items">
          <!-- 筛选项 -->
          <view 
            class="filter-item"
            v-for="(filter, index) in filters"
            :key="filter.key || index"
          >
            <!-- 下拉选择 -->
            <view 
              class="filter-dropdown"
              v-if="filter.type === 'select'"
              @click="handleFilterClick(filter, index)"
            >
              <text class="filter-label">{{ filter.label }}</text>
              <text class="filter-value">{{ getFilterDisplayValue(filter) }}</text>
              <text class="filter-arrow">▼</text>
            </view>
            
            <!-- 日期选择 -->
            <picker 
              v-else-if="filter.type === 'date'"
              mode="date"
              :value="filter.value"
              @change="handleDateChange($event, filter)"
            >
              <view class="filter-date">
                <text class="filter-label">{{ filter.label }}</text>
                <text class="filter-value">{{ formatDate(filter.value) || '请选择' }}</text>
                <text class="filter-arrow">📅</text>
              </view>
            </picker>
            
            <!-- 时间范围选择 -->
            <view 
              class="filter-daterange"
              v-else-if="filter.type === 'daterange'"
              @click="handleDateRangeClick(filter, index)"
            >
              <text class="filter-label">{{ filter.label }}</text>
              <text class="filter-value">{{ getDateRangeDisplay(filter) }}</text>
              <text class="filter-arrow">📅</text>
            </view>
            
            <!-- 开关 -->
            <view class="filter-switch" v-else-if="filter.type === 'switch'">
              <text class="filter-label">{{ filter.label }}</text>
              <switch 
                :checked="filter.value"
                @change="handleSwitchChange($event, filter)"
                color="#007AFF"
              />
            </view>
            
            <!-- 标签选择 -->
            <view 
              class="filter-tags"
              v-else-if="filter.type === 'tags'"
            >
              <text class="filter-label">{{ filter.label }}</text>
              <view class="tag-list">
                <view 
                  class="tag-item"
                  :class="{ active: isTagActive(filter, tag) }"
                  v-for="tag in filter.options"
                  :key="tag.value"
                  @click="handleTagClick(filter, tag)"
                >
                  {{ tag.label }}
                </view>
              </view>
            </view>
          </view>
          
          <!-- 重置按钮 -->
          <view class="filter-reset" v-if="showReset && hasActiveFilters" @click="handleReset">
            <text class="reset-text">重置</text>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 排序选择 -->
    <view class="sort-section" v-if="showSort">
      <view class="sort-dropdown" @click="handleSortClick">
        <text class="sort-label">排序</text>
        <text class="sort-value">{{ getSortDisplayValue() }}</text>
        <text class="sort-arrow">▼</text>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-section" v-if="showActions">
      <slot name="actions">
        <button class="action-btn primary" @click="handleSearch" v-if="showSearchButton">
          搜索
        </button>
        <button class="action-btn secondary" @click="handleReset" v-if="showResetButton">
          重置
        </button>
      </slot>
    </view>
  </view>
  
  <!-- 筛选弹窗 -->
  <view class="filter-popup" v-if="showFilterPopup" @click="closeFilterPopup">
    <view class="popup-content" @click.stop>
      <view class="popup-header">
        <text class="popup-title">{{ currentFilter?.label }}</text>
        <view class="popup-close" @click="closeFilterPopup">✕</view>
      </view>
      <view class="popup-body">
        <view 
          class="option-item"
          :class="{ active: currentFilter?.value === option.value }"
          v-for="option in currentFilter?.options"
          :key="option.value"
          @click="handleOptionSelect(option)"
        >
          <text class="option-label">{{ option.label }}</text>
          <text class="option-check" v-if="currentFilter?.value === option.value">✓</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 排序弹窗 -->
  <view class="sort-popup" v-if="showSortPopup" @click="closeSortPopup">
    <view class="popup-content" @click.stop>
      <view class="popup-header">
        <text class="popup-title">选择排序方式</text>
        <view class="popup-close" @click="closeSortPopup">✕</view>
      </view>
      <view class="popup-body">
        <view 
          class="option-item"
          :class="{ active: sortValue === option.value }"
          v-for="option in sortOptions"
          :key="option.value"
          @click="handleSortSelect(option)"
        >
          <text class="option-label">{{ option.label }}</text>
          <text class="option-check" v-if="sortValue === option.value">✓</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { formatUtils } from '@/utils'

// Props定义
const props = defineProps({
  // 是否显示搜索框
  showSearch: {
    type: Boolean,
    default: true
  },
  // 搜索占位符
  searchPlaceholder: {
    type: String,
    default: '请输入搜索关键词'
  },
  // 搜索最大长度
  searchMaxLength: {
    type: Number,
    default: 50
  },
  // 搜索值
  searchValue: {
    type: String,
    default: ''
  },
  // 是否显示筛选条件
  showFilters: {
    type: Boolean,
    default: true
  },
  // 筛选条件配置
  filters: {
    type: Array,
    default: () => []
  },
  // 是否显示排序
  showSort: {
    type: Boolean,
    default: false
  },
  // 排序选项
  sortOptions: {
    type: Array,
    default: () => [
      { label: '默认排序', value: 'default' },
      { label: '时间升序', value: 'time_asc' },
      { label: '时间降序', value: 'time_desc' }
    ]
  },
  // 排序值
  sortValue: {
    type: String,
    default: 'default'
  },
  // 是否显示操作按钮
  showActions: {
    type: Boolean,
    default: false
  },
  // 是否显示搜索按钮
  showSearchButton: {
    type: Boolean,
    default: true
  },
  // 是否显示重置按钮
  showResetButton: {
    type: Boolean,
    default: true
  },
  // 是否显示重置
  showReset: {
    type: Boolean,
    default: true
  },
  // 筛选栏样式
  barClass: {
    type: [String, Array, Object],
    default: ''
  },
  // 是否紧凑模式
  compact: {
    type: Boolean,
    default: false
  }
})

// Events定义
const emit = defineEmits([
  'search',
  'filter-change',
  'sort-change',
  'reset',
  'update:searchValue',
  'update:sortValue'
])

// 响应式数据
const showFilterPopup = ref(false)
const showSortPopup = ref(false)
const currentFilter = ref(null)
const currentFilterIndex = ref(-1)

// 计算属性
const hasActiveFilters = computed(() => {
  return props.filters.some(filter => {
    if (filter.type === 'switch') {
      return filter.value !== filter.defaultValue
    }
    if (filter.type === 'tags') {
      return filter.value && filter.value.length > 0
    }
    return filter.value && filter.value !== filter.defaultValue
  })
})

// 方法
const handleSearchInput = (event) => {
  const value = event.detail.value
  emit('update:searchValue', value)
}

const handleSearchConfirm = () => {
  emit('search', {
    keyword: props.searchValue,
    filters: getFilterValues(),
    sort: props.sortValue
  })
}

const handleSearchClear = () => {
  emit('update:searchValue', '')
  handleSearchConfirm()
}

const handleSearch = () => {
  handleSearchConfirm()
}

const handleFilterClick = (filter, index) => {
  if (filter.type === 'select') {
    currentFilter.value = filter
    currentFilterIndex.value = index
    showFilterPopup.value = true
  }
}

const handleDateChange = (event, filter) => {
  const value = event.detail.value
  updateFilterValue(filter, value)
}

const handleDateRangeClick = () => {
  // 这里可以集成日期范围选择器
  uni.showToast({
    title: '日期范围选择功能待实现',
    icon: 'none'
  })
}

const handleSwitchChange = (event, filter) => {
  const value = event.detail.value
  updateFilterValue(filter, value)
}

const handleTagClick = (filter, tag) => {
  let newValue = filter.value || []
  
  if (filter.multiple) {
    // 多选模式
    const index = newValue.findIndex(item => item === tag.value)
    if (index > -1) {
      newValue = newValue.filter(item => item !== tag.value)
    } else {
      newValue = [...newValue, tag.value]
    }
  } else {
    // 单选模式
    newValue = newValue.includes(tag.value) ? [] : [tag.value]
  }
  
  updateFilterValue(filter, newValue)
}

const handleSortClick = () => {
  showSortPopup.value = true
}

const handleOptionSelect = (option) => {
  updateFilterValue(currentFilter.value, option.value)
  closeFilterPopup()
}

const handleSortSelect = (option) => {
  emit('update:sortValue', option.value)
  emit('sort-change', option.value)
  closeSortPopup()
}

const handleReset = () => {
  // 重置所有筛选条件
  props.filters.forEach(filter => {
    updateFilterValue(filter, filter.defaultValue)
  })
  
  // 重置搜索
  emit('update:searchValue', '')
  
  // 重置排序
  emit('update:sortValue', 'default')
  
  emit('reset')
}

const updateFilterValue = (filter, value) => {
  filter.value = value
  emit('filter-change', {
    filter,
    value,
    filters: getFilterValues()
  })
}

const getFilterValues = () => {
  const values = {}
  props.filters.forEach(filter => {
    if (filter.key) {
      values[filter.key] = filter.value
    }
  })
  return values
}

const getFilterDisplayValue = (filter) => {
  if (!filter.value && filter.value !== 0) {
    return filter.placeholder || '请选择'
  }
  
  if (filter.options) {
    const option = filter.options.find(opt => opt.value === filter.value)
    return option ? option.label : filter.value
  }
  
  return filter.value
}

const getSortDisplayValue = () => {
  const option = props.sortOptions.find(opt => opt.value === props.sortValue)
  return option ? option.label : '默认排序'
}

const formatDate = (date) => {
  if (!date) return ''
  return formatUtils.dateFormat.format(date, 'YYYY-MM-DD')
}

const getDateRangeDisplay = (filter) => {
  if (!filter.value || !filter.value.length) {
    return filter.placeholder || '请选择时间范围'
  }
  
  const [start, end] = filter.value
  return `${formatDate(start)} 至 ${formatDate(end)}`
}

const isTagActive = (filter, tag) => {
  if (!filter.value) return false
  return filter.value.includes(tag.value)
}

const closeFilterPopup = () => {
  showFilterPopup.value = false
  currentFilter.value = null
  currentFilterIndex.value = -1
}

const closeSortPopup = () => {
  showSortPopup.value = false
}

// 监听筛选条件变化
watch(
  () => props.filters,
  () => {
    // 筛选条件变化时的处理
  },
  { deep: true }
)
</script>

<style lang="scss" scoped>
.filter-bar {
  background: #ffffff;
  border-bottom: 2rpx solid #f1f2f6;
  
  &.compact {
    .search-section,
    .filter-section,
    .sort-section,
    .action-section {
      padding: 12rpx 16rpx;
    }
  }
}

.search-section {
  padding: 16rpx 20rpx;
  
  .search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 24rpx;
    padding: 0 16rpx;
    
    .search-input {
      flex: 1;
      height: 72rpx;
      font-size: 28rpx;
      color: #2c3e50;
      background: transparent;
      border: none;
      outline: none;
      
      &::placeholder {
        color: #95a5a6;
      }
    }
    
    .search-icon {
      width: 48rpx;
      height: 48rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      color: #7f8c8d;
      margin-left: 8rpx;
    }
    
    .clear-icon {
      width: 32rpx;
      height: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      color: #95a5a6;
      background: #dee2e6;
      border-radius: 50%;
      margin-left: 8rpx;
    }
  }
}

.filter-section {
  padding: 16rpx 0;
  
  .filter-scroll {
    white-space: nowrap;
  }
  
  .filter-items {
    display: flex;
    align-items: center;
    padding: 0 20rpx;
    gap: 16rpx;
  }
  
  .filter-item {
    flex-shrink: 0;
  }
  
  .filter-dropdown,
  .filter-date,
  .filter-daterange {
    display: flex;
    align-items: center;
    padding: 12rpx 16rpx;
    background: #f8f9fa;
    border-radius: 20rpx;
    border: 2rpx solid transparent;
    transition: all 0.2s ease;
    
    &:active {
      background: #e9ecef;
    }
    
    .filter-label {
      font-size: 24rpx;
      color: #7f8c8d;
      margin-right: 8rpx;
    }
    
    .filter-value {
      font-size: 24rpx;
      color: #2c3e50;
      margin-right: 8rpx;
      max-width: 120rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .filter-arrow {
      font-size: 20rpx;
      color: #95a5a6;
    }
  }
  
  .filter-switch {
    display: flex;
    align-items: center;
    padding: 12rpx 16rpx;
    background: #f8f9fa;
    border-radius: 20rpx;
    
    .filter-label {
      font-size: 24rpx;
      color: #7f8c8d;
      margin-right: 16rpx;
    }
  }
  
  .filter-tags {
    .filter-label {
      font-size: 24rpx;
      color: #7f8c8d;
      margin-bottom: 8rpx;
    }
    
    .tag-list {
      display: flex;
      gap: 8rpx;
      
      .tag-item {
        padding: 8rpx 16rpx;
        background: #f8f9fa;
        border: 2rpx solid #e9ecef;
        border-radius: 16rpx;
        font-size: 22rpx;
        color: #7f8c8d;
        transition: all 0.2s ease;
        
        &.active {
          background: #007AFF;
          border-color: #007AFF;
          color: #ffffff;
        }
        
        &:active {
          transform: scale(0.95);
        }
      }
    }
  }
  
  .filter-reset {
    padding: 12rpx 16rpx;
    background: #ff4757;
    border-radius: 20rpx;
    
    .reset-text {
      font-size: 24rpx;
      color: #ffffff;
    }
  }
}

.sort-section {
  padding: 16rpx 20rpx;
  border-top: 2rpx solid #f1f2f6;
  
  .sort-dropdown {
    display: flex;
    align-items: center;
    padding: 12rpx 16rpx;
    background: #f8f9fa;
    border-radius: 20rpx;
    
    .sort-label {
      font-size: 24rpx;
      color: #7f8c8d;
      margin-right: 8rpx;
    }
    
    .sort-value {
      font-size: 24rpx;
      color: #2c3e50;
      margin-right: 8rpx;
    }
    
    .sort-arrow {
      font-size: 20rpx;
      color: #95a5a6;
    }
  }
}

.action-section {
  padding: 16rpx 20rpx;
  display: flex;
  gap: 16rpx;
  
  .action-btn {
    flex: 1;
    height: 72rpx;
    border-radius: 8rpx;
    font-size: 28rpx;
    border: none;
    
    &.primary {
      background: #007AFF;
      color: #ffffff;
    }
    
    &.secondary {
      background: #f8f9fa;
      color: #007AFF;
      border: 2rpx solid #007AFF;
    }
  }
}

.filter-popup,
.sort-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
  
  .popup-content {
    width: 100%;
    max-height: 80vh;
    background: #ffffff;
    border-radius: 24rpx 24rpx 0 0;
    
    .popup-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32rpx 32rpx 16rpx;
      border-bottom: 2rpx solid #f1f2f6;
      
      .popup-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #2c3e50;
      }
      
      .popup-close {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        color: #95a5a6;
      }
    }
    
    .popup-body {
      max-height: 60vh;
      overflow-y: auto;
      
      .option-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24rpx 32rpx;
        border-bottom: 2rpx solid #f8f9fa;
        transition: background 0.2s ease;
        
        &:active {
          background: #f8f9fa;
        }
        
        &.active {
          background: #f0f8ff;
          
          .option-label {
            color: #007AFF;
          }
        }
        
        .option-label {
          font-size: 28rpx;
          color: #2c3e50;
        }
        
        .option-check {
          font-size: 28rpx;
          color: #007AFF;
        }
      }
    }
  }
}
</style>