using Admin.NET.Plugin.MQTT;
using System;

namespace Admin.NET.Plugin.MQTT.Test
{
    /// <summary>
    /// MQTT凭证测试程序入口
    /// 用于验证MqttOptions类的凭证生成功能
    /// </summary>
    public class TestProgram
    {
        /// <summary>
        /// 测试程序主入口
        /// </summary>
        public static void Main(string[] args)
        {
            Console.WriteLine("开始运行MQTT凭证生成测试...");
            Console.WriteLine();
            
            // 运行完整测试
            var testResult = MqttOptions.RunCompleteTest();
            Console.WriteLine(testResult);
            
            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}