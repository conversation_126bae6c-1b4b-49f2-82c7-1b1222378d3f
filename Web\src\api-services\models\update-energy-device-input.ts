/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 更新设备输入参数
 * @export
 * @interface UpdateEnergyDeviceInput
 */
export interface UpdateEnergyDeviceInput {
    /**
     * 设备编码
     * @type {string}
     * @memberof UpdateEnergyDeviceInput
     */
    deviceCode: string;
    /**
     * 设备名称
     * @type {string}
     * @memberof UpdateEnergyDeviceInput
     */
    deviceName: string;
    /**
     * 设备类型
     * @type {string}
     * @memberof UpdateEnergyDeviceInput
     */
    deviceType: string;
    /**
     * 设备型号
     * @type {string}
     * @memberof UpdateEnergyDeviceInput
     */
    deviceModel?: string | null;
    /**
     * 设备位置
     * @type {string}
     * @memberof UpdateEnergyDeviceInput
     */
    location?: string | null;
    /**
     * 分组ID
     * @type {number}
     * @memberof UpdateEnergyDeviceInput
     */
    groupId?: number | null;
    /**
     * IP地址
     * @type {string}
     * @memberof UpdateEnergyDeviceInput
     */
    ipAddress?: string | null;
    /**
     * MAC地址
     * @type {string}
     * @memberof UpdateEnergyDeviceInput
     */
    macAddress?: string | null;
    /**
     * 固件版本
     * @type {string}
     * @memberof UpdateEnergyDeviceInput
     */
    firmwareVersion?: string | null;
    /**
     * 硬件版本
     * @type {string}
     * @memberof UpdateEnergyDeviceInput
     */
    hardwareVersion?: string | null;
    /**
     * 额定功率
     * @type {number}
     * @memberof UpdateEnergyDeviceInput
     */
    ratedPower?: number | null;
    /**
     * 额定电压
     * @type {number}
     * @memberof UpdateEnergyDeviceInput
     */
    ratedVoltage?: number | null;
    /**
     * 额定电流
     * @type {number}
     * @memberof UpdateEnergyDeviceInput
     */
    ratedCurrent?: number | null;
    /**
     * 安装日期
     * @type {Date}
     * @memberof UpdateEnergyDeviceInput
     */
    installDate?: Date | null;
    /**
     * 保修期至
     * @type {Date}
     * @memberof UpdateEnergyDeviceInput
     */
    warrantyDate?: Date | null;
    /**
     * 供应商
     * @type {string}
     * @memberof UpdateEnergyDeviceInput
     */
    supplier?: string | null;
    /**
     * 备注
     * @type {string}
     * @memberof UpdateEnergyDeviceInput
     */
    remark?: string | null;
    /**
     * 排序
     * @type {number}
     * @memberof UpdateEnergyDeviceInput
     */
    sort?: number;
    /**
     * 状态
     * @type {number}
     * @memberof UpdateEnergyDeviceInput
     */
    status?: number;
    /**
     * 主键ID
     * @type {number}
     * @memberof UpdateEnergyDeviceInput
     */
    id: number;
}
