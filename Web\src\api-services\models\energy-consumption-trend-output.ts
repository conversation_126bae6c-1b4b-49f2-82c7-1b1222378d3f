/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 能耗趋势输出参数
 * @export
 * @interface EnergyConsumptionTrendOutput
 */
export interface EnergyConsumptionTrendOutput {
    /**
     * 时间点
     * @type {Date}
     * @memberof EnergyConsumptionTrendOutput
     */
    time?: Date;
    /**
     * 用电量(kWh)
     * @type {number}
     * @memberof EnergyConsumptionTrendOutput
     */
    energy?: number;
    /**
     * 能耗值
     * @type {number}
     * @memberof EnergyConsumptionTrendOutput
     */
    energyConsumption?: number | null;
    /**
     * 功率(W)
     * @type {number}
     * @memberof EnergyConsumptionTrendOutput
     */
    power?: number;
    /**
     * 频率
     * @type {number}
     * @memberof EnergyConsumptionTrendOutput
     */
    frequency?: number | null;
    /**
     * 温度(℃)
     * @type {number}
     * @memberof EnergyConsumptionTrendOutput
     */
    temperature?: number | null;
    /**
     * 湿度(%)
     * @type {number}
     * @memberof EnergyConsumptionTrendOutput
     */
    humidity?: number | null;
    /**
     * 设备数量
     * @type {number}
     * @memberof EnergyConsumptionTrendOutput
     */
    deviceCount?: number;
}
