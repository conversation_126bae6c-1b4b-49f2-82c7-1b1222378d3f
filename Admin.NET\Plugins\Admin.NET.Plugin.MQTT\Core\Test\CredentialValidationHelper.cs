using Microsoft.Extensions.Logging;
using System;
using System.Security.Cryptography;
using System.Text;

namespace Admin.NET.Plugin.MQTT.Test;

/// <summary>
/// MQTT凭证验证辅助类
/// 提供静态方法来验证MqttOptions生成的凭证与SysAuthService的一致性
/// </summary>
public static class CredentialValidationHelper
{
    /// <summary>
    /// 验证MQTT凭证生成的一致性
    /// 比较MqttOptions生成的凭证与SysAuthService算法的结果
    /// </summary>
    /// <param name="instanceId">实例ID</param>
    /// <param name="accessKeyId">访问密钥ID</param>
    /// <param name="accessKeySecret">访问密钥密码</param>
    /// <param name="deviceId">设备ID</param>
    /// <param name="logger">日志记录器</param>
    /// <returns>验证结果</returns>
    public static ValidationResult ValidateCredentialGeneration(
        string instanceId, 
        string accessKeyId, 
        string accessKeySecret, 
        string deviceId,
        ILogger? logger = null)
    {
        try
        {
            logger?.LogInformation("开始验证MQTT凭证生成一致性...");

            // 使用SysAuthService算法生成期望的凭证
            var expectedCredentials = GenerateExpectedCredentials(instanceId, accessKeyId, accessKeySecret, deviceId);
            
            // 使用MqttOptions生成实际凭证
            var mqttOptions = CreateTestMqttOptions(instanceId, accessKeyId, accessKeySecret);
            var actualCredentials = mqttOptions.GenerateCredentials(deviceId);

            // 比较结果
            var result = new ValidationResult
            {
                IsValid = true,
                ExpectedCredentials = expectedCredentials,
                ActualCredentials = actualCredentials,
                ValidationTime = DateTime.Now
            };

            var errors = new List<string>();

            // 验证ClientId
            if (expectedCredentials.ClientId != actualCredentials.ClientId)
            {
                errors.Add($"ClientId不匹配: 期望='{expectedCredentials.ClientId}', 实际='{actualCredentials.ClientId}'");
            }

            // 验证Username
            if (expectedCredentials.Username != actualCredentials.Username)
            {
                errors.Add($"Username不匹配: 期望='{expectedCredentials.Username}', 实际='{actualCredentials.Username}'");
            }

            // 验证Password
            if (expectedCredentials.Password != actualCredentials.Password)
            {
                errors.Add($"Password不匹配: 期望='{expectedCredentials.Password}', 实际='{actualCredentials.Password}'");
            }

            if (errors.Any())
            {
                result.IsValid = false;
                result.ErrorMessage = string.Join("; ", errors);
                logger?.LogError("MQTT凭证验证失败: {ErrorMessage}", result.ErrorMessage);
            }
            else
            {
                logger?.LogInformation("MQTT凭证验证成功，生成的凭证与SysAuthService算法完全一致");
            }

            return result;
        }
        catch (Exception ex)
        {
            logger?.LogError(ex, "MQTT凭证验证过程中发生异常");
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessage = $"验证异常: {ex.Message}",
                ValidationTime = DateTime.Now
            };
        }
    }

    /// <summary>
    /// 使用配置文件中的实际参数验证凭证生成
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <returns>验证结果</returns>
    public static ValidationResult ValidateWithConfigFileParameters(ILogger? logger = null)
    {
        // 使用MQTT.json中的实际配置参数
        var instanceId = "energylight";
        var accessKeyId = "ak_c371a2975f7c4c0eb672b4675bfd2";
        var accessKeySecret = "sk_5905b2a8fb62457691f22aa2446b7d49b9174283b3d64ec785f8557c5ed2d";
        var deviceId = "AdminNET_MQTT_Client";

        logger?.LogInformation("使用配置文件参数验证MQTT凭证生成");
        logger?.LogInformation("参数: InstanceId={InstanceId}, AccessKeyId={AccessKeyId}, DeviceId={DeviceId}", 
            instanceId, accessKeyId, deviceId);

        return ValidateCredentialGeneration(instanceId, accessKeyId, accessKeySecret, deviceId, logger);
    }

    /// <summary>
    /// 生成期望的凭证（基于SysAuthService算法）
    /// </summary>
    /// <param name="instanceId">实例ID</param>
    /// <param name="accessKeyId">访问密钥ID</param>
    /// <param name="accessKeySecret">访问密钥密码</param>
    /// <param name="deviceId">设备ID</param>
    /// <returns>期望的凭证</returns>
    private static MqttCredentials GenerateExpectedCredentials(string instanceId, string accessKeyId, string accessKeySecret, string deviceId)
    {
        // 基于SysAuthService.CreateMqttToken的实现
        var clientId = $"{instanceId}@@@{deviceId}";
        var username = $"DeviceCredential|{accessKeyId}|{clientId}";
        var password = ComputeHmacSha1Signature(clientId, accessKeySecret);

        return new MqttCredentials
        {
            ClientId = clientId,
            Username = username,
            Password = password
        };
    }

    /// <summary>
    /// 计算HMAC-SHA1签名
    /// 与SysAuthService.ComputeHmacSha1Signature保持一致
    /// </summary>
    /// <param name="data">待签名数据</param>
    /// <param name="key">签名密钥</param>
    /// <returns>Base64编码的签名</returns>
    private static string ComputeHmacSha1Signature(string data, string key)
    {
        using (var hmac = new HMACSHA1(Encoding.UTF8.GetBytes(key)))
        {
            var hashBytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
            return Convert.ToBase64String(hashBytes);
        }
    }

    /// <summary>
    /// 创建测试用的MqttOptions实例
    /// </summary>
    /// <param name="instanceId">实例ID</param>
    /// <param name="accessKeyId">访问密钥ID</param>
    /// <param name="accessKeySecret">访问密钥密码</param>
    /// <returns>配置的MqttOptions实例</returns>
    private static MqttOptions CreateTestMqttOptions(string instanceId, string accessKeyId, string accessKeySecret)
    {
        var options = new MqttOptions();
        
        // 设置必要的配置参数
        options.InstanceId = instanceId;
        options.AccessKeyId = accessKeyId;
        options.AccessKeySecret = accessKeySecret;
        
        return options;
    }
}

/// <summary>
/// 验证结果类
/// 包含凭证验证的详细结果信息
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// 验证是否通过
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误信息（验证失败时）
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 期望的凭证（SysAuthService算法生成）
    /// </summary>
    public MqttCredentials? ExpectedCredentials { get; set; }

    /// <summary>
    /// 实际的凭证（MqttOptions生成）
    /// </summary>
    public MqttCredentials? ActualCredentials { get; set; }

    /// <summary>
    /// 验证执行时间
    /// </summary>
    public DateTime ValidationTime { get; set; }

    /// <summary>
    /// 获取验证结果的详细描述
    /// </summary>
    /// <returns>结果描述</returns>
    public override string ToString()
    {
        var sb = new StringBuilder();
        sb.AppendLine($"验证结果: {(IsValid ? "通过" : "失败")}");
        sb.AppendLine($"验证时间: {ValidationTime:yyyy-MM-dd HH:mm:ss}");
        
        if (!IsValid && !string.IsNullOrEmpty(ErrorMessage))
        {
            sb.AppendLine($"错误信息: {ErrorMessage}");
        }
        
        if (ExpectedCredentials != null)
        {
            sb.AppendLine("期望凭证 (SysAuthService算法):");
            sb.AppendLine($"  ClientId: {ExpectedCredentials.ClientId}");
            sb.AppendLine($"  Username: {ExpectedCredentials.Username}");
            sb.AppendLine($"  Password: {ExpectedCredentials.Password}");
        }
        
        if (ActualCredentials != null)
        {
            sb.AppendLine("实际凭证 (MqttOptions生成):");
            sb.AppendLine($"  ClientId: {ActualCredentials.ClientId}");
            sb.AppendLine($"  Username: {ActualCredentials.Username}");
            sb.AppendLine($"  Password: {ActualCredentials.Password}");
        }
        
        return sb.ToString();
    }
}