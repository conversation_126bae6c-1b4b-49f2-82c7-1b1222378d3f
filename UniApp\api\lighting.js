/**
 * 照明控制相关API
 */

import request from '../utils/request.js'
import { createCachedApi } from '../src/utils/apiCache.js'

const BASE_URL = '/api/lighting'

export const getLightingScenes = () => {
  return request.get(`${BASE_URL}/scenes`, {}, {
    cache: true,
    cacheTime: 5 * 60 * 1000, // 5分钟缓存
    offline: true
  })
}

/**
 * 获取场景列表（别名函数）
 * @returns {Promise} 场景列表
 */
export const getScenes = () => {
  return getLightingScenes()
}

/**
 * 应用场景
 * @param {string} sceneId 场景ID
 * @param {Array} deviceIds 设备ID列表（可选）
 * @returns {Promise} 应用结果
 */
export const applyScene = (sceneId, deviceIds = []) => {
  return request.post(`${BASE_URL}/scenes/${sceneId}/apply`, { deviceIds }, {
    cache: false,
    offline: false
  })
}

/**
 * 获取场景详情
 * @param {string} sceneId 场景ID
 * @returns {Promise} 场景详情
 */
export const getSceneDetail = (sceneId) => {
  return request.get(`${BASE_URL}/scenes/${sceneId}`, {}, {
    cache: true,
    cacheTime: 5 * 60 * 1000, // 5分钟缓存
    offline: true
  })
}

export const createLightingScene = (data) => {
  return request.post(`${BASE_URL}/scenes`, data, {
    cache: false,
    offline: false
  })
}

export const updateLightingScene = (id, data) => {
  return request.put(`${BASE_URL}/scenes/${id}`, data, {
    cache: false,
    offline: false
  })
}

export const deleteLightingScene = (id) => {
  return request.delete(`${BASE_URL}/scenes/${id}`, {
    cache: false,
    offline: false
  })
}

/**
 * 执行照明场景
 * @param {string} sceneId 场景ID
 * @returns {Promise} 执行结果
 */
export const executeLightingScene = (sceneId) => {
  return request.post(`${BASE_URL}/scenes/${sceneId}/execute`, {}, {
    cache: false,
    offline: false
  })
}

/**
 * 获取定时任务列表
 * @param {Object} params 查询参数
 * @param {number} params.page 页码
 * @param {number} params.pageSize 每页数量
 * @param {string} params.status 任务状态
 * @returns {Promise} 定时任务列表
 */
export const getScheduleTasks = (params = {}) => {
  return request.get(`${BASE_URL}/schedules`, params, {
    cache: true,
    cacheTime: 5 * 60 * 1000, // 5分钟缓存
    offline: true
  })
}

/**
 * 获取定时任务详情
 * @param {string} taskId 任务ID
 * @returns {Promise} 任务详情
 */
export const getScheduleDetail = (taskId) => {
  return request.get(`${BASE_URL}/schedules/${taskId}`, {}, {
    cache: true,
    cacheTime: 5 * 60 * 1000, // 5分钟缓存
    offline: true
  })
}

/**
 * 创建定时任务
 * @param {Object} taskData 任务数据
 * @param {string} taskData.name 任务名称
 * @param {string} taskData.description 任务描述
 * @param {string} taskData.cronExpression Cron表达式
 * @param {string} taskData.sceneId 关联场景ID
 * @param {Array} taskData.deviceIds 关联设备ID列表
 * @param {Object} taskData.action 执行动作
 * @param {boolean} taskData.enabled 是否启用
 * @returns {Promise} 创建结果
 */
export const createScheduleTask = (taskData) => {
  return request.post(`${BASE_URL}/schedules`, taskData, {
    cache: false,
    offline: false
  })
}

/**
 * 更新定时任务
 * @param {string} taskId 任务ID
 * @param {Object} taskData 任务数据
 * @returns {Promise} 更新结果
 */
export const updateScheduleTask = (taskId, taskData) => {
  return request.put(`${BASE_URL}/schedules/${taskId}`, taskData, {
    cache: false,
    offline: false
  })
}

/**
 * 删除定时任务
 * @param {string} taskId 任务ID
 * @returns {Promise} 删除结果
 */
export const deleteScheduleTask = (taskId) => {
  return request.delete(`${BASE_URL}/schedules/${taskId}`, {
    cache: false,
    offline: false
  })
}

/**
 * 启用/禁用定时任务
 * @param {string} taskId 任务ID
 * @param {boolean} enabled 是否启用
 * @returns {Promise} 操作结果
 */
export const toggleScheduleTask = (taskId, enabled) => {
  return request.put(`${BASE_URL}/schedules/${taskId}/toggle`, { enabled }, {
    cache: false,
    offline: false
  })
}

/**
 * 手动执行定时任务
 * @param {string} taskId 任务ID
 * @returns {Promise} 执行结果
 */
export const executeScheduleTask = (taskId) => {
  return request.post(`${BASE_URL}/schedules/${taskId}/execute`, {}, {
    cache: false,
    offline: false
  })
}

/**
 * 获取照明预设模式
 * @returns {Promise} 预设模式列表
 */
export const getLightingPresets = () => {
  return request.get(`${BASE_URL}/presets`, {}, {
    cache: true,
    cacheTime: 10 * 60 * 1000, // 10分钟缓存
    offline: true
  })
}

/**
 * 应用照明预设模式
 * @param {string} presetId 预设ID
 * @param {Array} deviceIds 设备ID列表
 * @returns {Promise} 应用结果
 */
export const applyLightingPreset = (presetId, deviceIds) => {
  return request.post(`${BASE_URL}/presets/${presetId}/apply`, { deviceIds }, {
    cache: false,
    offline: false
  })
}

/**
 * 获取智能控制规则列表
 * @param {Object} params 查询参数
 * @returns {Promise} 规则列表
 */
export const getSmartRules = (params = {}) => {
  return request.get(`${BASE_URL}/smart-rules`, params, {
    cache: true,
    cacheTime: 5 * 60 * 1000, // 5分钟缓存
    offline: true
  })
}

/**
 * 创建智能控制规则
 * @param {Object} ruleData 规则数据
 * @param {string} ruleData.name 规则名称
 * @param {string} ruleData.description 规则描述
 * @param {Array} ruleData.conditions 触发条件
 * @param {Array} ruleData.actions 执行动作
 * @param {boolean} ruleData.enabled 是否启用
 * @returns {Promise} 创建结果
 */
export const createSmartRule = (ruleData) => {
  return request.post(`${BASE_URL}/smart-rules`, ruleData, {
    cache: false,
    offline: false
  })
}

/**
 * 更新智能控制规则
 * @param {string} ruleId 规则ID
 * @param {Object} ruleData 规则数据
 * @returns {Promise} 更新结果
 */
export const updateSmartRule = (ruleId, ruleData) => {
  return request.put(`${BASE_URL}/smart-rules/${ruleId}`, ruleData, {
    cache: false,
    offline: false
  })
}

/**
 * 删除智能控制规则
 * @param {string} ruleId 规则ID
 * @returns {Promise} 删除结果
 */
export const deleteSmartRule = (ruleId) => {
  return request.delete(`${BASE_URL}/smart-rules/${ruleId}`, {
    cache: false,
    offline: false
  })
}

/**
 * 启用/禁用智能控制规则
 * @param {string} ruleId 规则ID
 * @param {boolean} enabled 是否启用
 * @returns {Promise} 操作结果
 */
export const toggleSmartRule = (ruleId, enabled) => {
  return request.put(`${BASE_URL}/smart-rules/${ruleId}/toggle`, { enabled }, {
    cache: false,
    offline: false
  })
}

/**
 * 获取照明控制历史记录
 * @param {Object} params 查询参数
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @param {string} params.deviceId 设备ID
 * @param {string} params.action 操作类型
 * @returns {Promise} 历史记录
 */
export const getLightingHistory = (params = {}) => {
  return request.get(`${BASE_URL}/history`, params, {
    cache: true,
    cacheTime: 5 * 60 * 1000, // 5分钟缓存
    offline: true
  })
}

/**
 * 获取照明统计数据
 * @param {Object} params 查询参数
 * @param {string} params.period 统计周期 day/week/month/year
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @returns {Promise} 统计数据
 */
export const getLightingStatistics = (params = {}) => {
  return request.get(`${BASE_URL}/statistics`, params, {
    cache: true,
    cacheTime: 5 * 60 * 1000, // 5分钟缓存
    offline: true
  })
}

/**
 * 获取环境光感数据
 * @param {string} deviceId 设备ID
 * @param {Object} params 查询参数
 * @returns {Promise} 光感数据
 */
export const getAmbientLight = (deviceId, params = {}) => {
  return request.get(`${BASE_URL}/ambient/${deviceId}`, params, {
    cache: true,
    cacheTime: 1 * 60 * 1000, // 1分钟缓存
    offline: true
  })
}

/**
 * 设置自动调光模式
 * @param {Array} deviceIds 设备ID列表
 * @param {Object} config 配置参数
 * @param {boolean} config.enabled 是否启用
 * @param {number} config.sensitivity 灵敏度 1-10
 * @param {number} config.minBrightness 最小亮度
 * @param {number} config.maxBrightness 最大亮度
 * @returns {Promise} 设置结果
 */
export const setAutoDimming = (deviceIds, config) => {
  return request.post(`${BASE_URL}/auto-dimming`, {
    deviceIds,
    ...config
  }, {
    cache: false,
    offline: false
  })
}

/**
 * 获取色彩模板
 * @returns {Promise} 色彩模板列表
 */
export const getColorTemplates = () => {
  return request.get(`${BASE_URL}/color-templates`, {}, {
    cache: true,
    cacheTime: 10 * 60 * 1000, // 10分钟缓存
    offline: true
  })
}

/**
 * 应用色彩模板
 * @param {string} templateId 模板ID
 * @param {Array} deviceIds 设备ID列表
 * @returns {Promise} 应用结果
 */
export const applyColorTemplate = (templateId, deviceIds) => {
  return request.post(`${BASE_URL}/color-templates/${templateId}/apply`, { deviceIds }, {
    cache: false,
    offline: false
  })
}

/**
 * 创建自定义色彩模板
 * @param {Object} templateData 模板数据
 * @param {string} templateData.name 模板名称
 * @param {Array} templateData.colors 颜色列表
 * @param {string} templateData.description 模板描述
 * @returns {Promise} 创建结果
 */
export const createColorTemplate = (templateData) => {
  return request.post(`${BASE_URL}/color-templates`, templateData, {
    cache: false,
    offline: false
  })
}

// 创建缓存版本的API函数
const cachedGetLightingScenes = createCachedApi(getLightingScenes, 'lighting_scenes', 5 * 60 * 1000) // 5分钟缓存
const cachedGetSceneDetail = createCachedApi(getSceneDetail, 'scene_detail', 5 * 60 * 1000) // 5分钟缓存
const cachedGetScheduleTasks = createCachedApi(getScheduleTasks, 'schedule_tasks', 2 * 60 * 1000) // 2分钟缓存
const cachedGetScheduleDetail = createCachedApi(getScheduleDetail, 'schedule_detail', 2 * 60 * 1000) // 2分钟缓存
const cachedGetLightingPresets = createCachedApi(getLightingPresets, 'lighting_presets', 10 * 60 * 1000) // 10分钟缓存
const cachedGetSmartRules = createCachedApi(getSmartRules, 'smart_rules', 5 * 60 * 1000) // 5分钟缓存
const cachedGetLightingHistory = createCachedApi(getLightingHistory, 'lighting_history', 5 * 60 * 1000) // 5分钟缓存
const cachedGetLightingStatistics = createCachedApi(getLightingStatistics, 'lighting_statistics', 5 * 60 * 1000) // 5分钟缓存
const cachedGetAmbientLight = createCachedApi(getAmbientLight, 'ambient_light', 1 * 60 * 1000) // 1分钟缓存
const cachedGetColorTemplates = createCachedApi(getColorTemplates, 'color_templates', 10 * 60 * 1000) // 10分钟缓存
const cachedGetScenes = createCachedApi(getScenes, 'scenes', 5 * 60 * 1000) // 5分钟缓存

// 默认导出所有照明控制相关API
export default {
  // 原始API函数
  getLightingScenes,
  getSceneDetail,
  createLightingScene,
  updateLightingScene,
  deleteLightingScene,
  executeLightingScene,
  getScheduleTasks,
  getScheduleDetail,
  createScheduleTask,
  updateScheduleTask,
  deleteScheduleTask,
  toggleScheduleTask,
  executeScheduleTask,
  getLightingPresets,
  applyLightingPreset,
  getSmartRules,
  createSmartRule,
  updateSmartRule,
  deleteSmartRule,
  toggleSmartRule,
  getLightingHistory,
  getLightingStatistics,
  getAmbientLight,
  setAutoDimming,
  getColorTemplates,
  applyColorTemplate,
  createColorTemplate,
  // 新增API函数
  getScenes,
  applyScene,
  // 缓存版本API函数
  cachedGetLightingScenes,
  cachedGetSceneDetail,
  cachedGetScheduleTasks,
  cachedGetScheduleDetail,
  cachedGetLightingPresets,
  cachedGetSmartRules,
  cachedGetLightingHistory,
  cachedGetLightingStatistics,
  cachedGetAmbientLight,
  cachedGetColorTemplates,
  cachedGetScenes
}