// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System;
using System.Buffers;
using System.ComponentModel.DataAnnotations;
using System.Runtime.InteropServices;
using Admin.NET.Plugin.MQTT;

namespace Admin.NET.Plugin.MQTT;

/// <summary>
/// MQTT消息基类 - 优化内存使用和性能
/// 使用结构体布局优化内存对齐，减少内存占用
/// </summary>
[StructLayout(LayoutKind.Auto)]
public abstract class MqttMessageBase : IDisposable
{
    private static readonly ArrayPool<char> _charPool = ArrayPool<char>.Shared;
    private char[]? _messageIdBuffer;
    private char[]? _deviceNameBuffer;
    private char[]? _versionBuffer;
    private bool _disposed;

    /// <summary>
    /// 消息ID - 使用内存池优化字符串处理
    /// </summary>
    [Required(ErrorMessage = "消息ID不能为空")]
    [StringLength(64, ErrorMessage = "消息ID长度不能超过64个字符")]
    public string MessageId 
    { 
        get => _messageIdBuffer != null ? new string(_messageIdBuffer) : string.Empty;
        set => SetStringProperty(ref _messageIdBuffer, value);
    }

    /// <summary>
    /// 时间戳 - 使用UTC时间戳提高性能
    /// </summary>
    [Range(0, long.MaxValue, ErrorMessage = "时间戳必须为正数")]
    public long Timestamp { get; set; } = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

    /// <summary>
    /// 设备名称 - 使用内存池优化字符串处理
    /// </summary>
    [Required(ErrorMessage = "设备名称不能为空")]
    [StringLength(128, ErrorMessage = "设备名称长度不能超过128个字符")]
    public string DeviceName 
    { 
        get => _deviceNameBuffer != null ? new string(_deviceNameBuffer) : string.Empty;
        set => SetStringProperty(ref _deviceNameBuffer, value);
    }

    /// <summary>
    /// 协议版本 - 使用内存池优化字符串处理
    /// </summary>
    [StringLength(16, ErrorMessage = "协议版本长度不能超过16个字符")]
    public string version 
    { 
        get => _versionBuffer != null ? new string(_versionBuffer) : "1.0";
        set => SetStringProperty(ref _versionBuffer, value);
    }

    /// <summary>
    /// 消息创建时间 - 用于性能监控
    /// </summary>
    [JsonIgnore]
    public DateTime CreatedAt { get; } = DateTime.UtcNow;

    /// <summary>
    /// 消息大小估算 - 用于内存监控
    /// </summary>
    [JsonIgnore]
    public virtual int EstimatedSize => 
        (MessageId?.Length ?? 0) * 2 + 
        sizeof(long) + 
        (DeviceName?.Length ?? 0) * 2 + 
        (version?.Length ?? 0) * 2 + 
        sizeof(DateTime);

    /// <summary>
    /// 设置字符串属性并使用内存池优化
    /// </summary>
    /// <param name="buffer">字符缓冲区引用</param>
    /// <param name="value">要设置的值</param>
    private void SetStringProperty(ref char[]? buffer, string? value)
    {
        // 释放旧缓冲区
        if (buffer != null)
        {
            _charPool.Return(buffer);
            buffer = null;
        }

        // 设置新值
        if (!string.IsNullOrEmpty(value))
        {
            buffer = _charPool.Rent(value.Length);
            value.AsSpan().CopyTo(buffer.AsSpan());
        }
    }

    /// <summary>
    /// 验证消息数据的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public virtual bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(MessageId) && 
               !string.IsNullOrWhiteSpace(DeviceName) && 
               Timestamp > 0;
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public virtual void Dispose()
    {
        if (!_disposed)
        {
            if (_messageIdBuffer != null)
            {
                _charPool.Return(_messageIdBuffer);
                _messageIdBuffer = null;
            }
            if (_deviceNameBuffer != null)
            {
                _charPool.Return(_deviceNameBuffer);
                _deviceNameBuffer = null;
            }
            if (_versionBuffer != null)
            {
                _charPool.Return(_versionBuffer);
                _versionBuffer = null;
            }
            _disposed = true;
        }
    }

    /// <summary>
    /// 析构函数
    /// </summary>
    ~MqttMessageBase()
    {
        Dispose();
    }
}

/// <summary>
/// 设备控制指令消息 - 优化内存布局和数据验证
/// 使用枚举替代魔法数字，提高代码可读性和类型安全
/// </summary>
[StructLayout(LayoutKind.Auto)]
public sealed class DeviceControlMessage : IDisposable
{
    private static readonly ArrayPool<char> _charPool = ArrayPool<char>.Shared;
    private char[]? _deviceNameBuffer;
    private char[]? _areaBuffer;
    private char[]? _addressBuffer;
    private char[]? _actionBuffer;
    private char[]? _paramsBuffer;
    private char[]? _identityBuffer;
    private bool _disposed;

    /// <summary>
    /// 控制代码枚举 - 替代魔法数字
    /// </summary>
    public enum ControlCode
    {
        /// <summary>单灯控制</summary>
        SingleLight = 100,
        /// <summary>群组控制</summary>
        Group = 200,
        /// <summary>标签控制</summary>
        Tag = 300,
        /// <summary>全区控制</summary>
        AllArea = 400
    }

    /// <summary>
    /// 控制代码 (100=单灯, 200=群组, 300=标签, 400=全区)
    /// </summary>
    [Required(ErrorMessage = "控制代码不能为空")]
    public ControlCode code { get; set; }

    /// <summary>
    /// 设备名称 - 使用内存池优化
    /// </summary>
    [Required(ErrorMessage = "设备名称不能为空")]
    [StringLength(128, ErrorMessage = "设备名称长度不能超过128个字符")]
    public string deviceName 
    { 
        get => _deviceNameBuffer != null ? new string(_deviceNameBuffer) : string.Empty;
        set => SetStringProperty(ref _deviceNameBuffer, value);
    }

    /// <summary>
    /// 区域地址 - 使用内存池优化
    /// </summary>
    [StringLength(64, ErrorMessage = "区域地址长度不能超过64个字符")]
    public string area 
    { 
        get => _areaBuffer != null ? new string(_areaBuffer) : string.Empty;
        set => SetStringProperty(ref _areaBuffer, value);
    }

    /// <summary>
    /// 设备地址 - 使用内存池优化
    /// </summary>
    [StringLength(64, ErrorMessage = "设备地址长度不能超过64个字符")]
    public string address 
    { 
        get => _addressBuffer != null ? new string(_addressBuffer) : string.Empty;
        set => SetStringProperty(ref _addressBuffer, value);
    }

    /// <summary>
    /// 动作指令 - 使用内存池优化
    /// </summary>
    [Required(ErrorMessage = "动作指令不能为空")]
    [StringLength(32, ErrorMessage = "动作指令长度不能超过32个字符")]
    public string action 
    { 
        get => _actionBuffer != null ? new string(_actionBuffer) : string.Empty;
        set => SetStringProperty(ref _actionBuffer, value);
    }

    /// <summary>
    /// 指令参数 - 使用内存池优化
    /// </summary>
    [StringLength(512, ErrorMessage = "指令参数长度不能超过512个字符")]
    public string @params 
    { 
        get => _paramsBuffer != null ? new string(_paramsBuffer) : string.Empty;
        set => SetStringProperty(ref _paramsBuffer, value);
    }

    /// <summary>
    /// 身份标识 - 使用内存池优化
    /// </summary>
    [StringLength(64, ErrorMessage = "身份标识长度不能超过64个字符")]
    public string identity 
    { 
        get => _identityBuffer != null ? new string(_identityBuffer) : string.Empty;
        set => SetStringProperty(ref _identityBuffer, value);
    }

    /// <summary>
    /// 消息创建时间 - 用于性能监控
    /// </summary>
    [JsonIgnore]
    public DateTime CreatedAt { get; } = DateTime.UtcNow;

    /// <summary>
    /// 消息大小估算 - 用于内存监控
    /// </summary>
    [JsonIgnore]
    public int EstimatedSize => 
        sizeof(ControlCode) + 
        (deviceName?.Length ?? 0) * 2 + 
        (area?.Length ?? 0) * 2 + 
        (address?.Length ?? 0) * 2 + 
        (action?.Length ?? 0) * 2 + 
        (@params?.Length ?? 0) * 2 + 
        (identity?.Length ?? 0) * 2 + 
        sizeof(DateTime);

    /// <summary>
    /// 设置字符串属性并使用内存池优化
    /// </summary>
    /// <param name="buffer">字符缓冲区引用</param>
    /// <param name="value">要设置的值</param>
    private void SetStringProperty(ref char[]? buffer, string? value)
    {
        // 释放旧缓冲区
        if (buffer != null)
        {
            _charPool.Return(buffer);
            buffer = null;
        }

        // 设置新值
        if (!string.IsNullOrEmpty(value))
        {
            buffer = _charPool.Rent(value.Length);
            value.AsSpan().CopyTo(buffer.AsSpan());
        }
    }

    /// <summary>
    /// 验证控制消息的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public bool IsValid()
    {
        return Enum.IsDefined(typeof(ControlCode), code) && 
               !string.IsNullOrWhiteSpace(deviceName) && 
               !string.IsNullOrWhiteSpace(action);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            ReturnBuffer(ref _deviceNameBuffer);
            ReturnBuffer(ref _areaBuffer);
            ReturnBuffer(ref _addressBuffer);
            ReturnBuffer(ref _actionBuffer);
            ReturnBuffer(ref _paramsBuffer);
            ReturnBuffer(ref _identityBuffer);
            _disposed = true;
        }
    }

    /// <summary>
    /// 返回缓冲区到内存池
    /// </summary>
    /// <param name="buffer">要返回的缓冲区</param>
    private void ReturnBuffer(ref char[]? buffer)
    {
        if (buffer != null)
        {
            _charPool.Return(buffer);
            buffer = null;
        }
    }

    /// <summary>
    /// 析构函数
    /// </summary>
    ~DeviceControlMessage()
    {
        Dispose();
    }
}

/// <summary>
/// 设备事件消息 - 优化内存使用和类型安全
/// 使用泛型避免装箱，使用内存池优化字符串处理
/// </summary>
/// <typeparam name="T">事件数据类型</typeparam>
[StructLayout(LayoutKind.Auto)]
public sealed class DeviceEventMessage<T> : MqttMessageBase where T : class
{
    private static readonly ArrayPool<char> _charPool = ArrayPool<char>.Shared;
    private char[]? _eventTypeBuffer;
    private char[]? _statusBuffer;
    private char[]? _errorBuffer;
    private char[]? _areaBuffer;
    private char[]? _addressBuffer;
    private bool _disposed;

    /// <summary>
    /// 事件类型枚举 - 提高类型安全性
    /// </summary>
    public enum EventType
    {
        /// <summary>设备广播</summary>
        Beacon,
        /// <summary>照明参数</summary>
        Setting,
        /// <summary>传感器配置</summary>
        Sensor,
        /// <summary>能耗信息</summary>
        Consumption,
        /// <summary>心跳消息</summary>
        Heartbeat,
        /// <summary>触发事件</summary>
        Trigger,
        /// <summary>调光参数</summary>
        Dimming,
        /// <summary>网络配置</summary>
        Network,
        /// <summary>红外遥控</summary>
        Irc,
        /// <summary>配置事件</summary>
        Config,
        /// <summary>升级事件</summary>
        Upgrade,
        /// <summary>日志事件</summary>
        Log,
        /// <summary>状态事件</summary>
        Status,
        /// <summary>报警事件</summary>
        Alarm,
        /// <summary>未知事件</summary>
        Unknown
    }

    /// <summary>
    /// 设备状态枚举 - 提高类型安全性
    /// </summary>
    public enum DeviceStatus
    {
        /// <summary>在线</summary>
        Online,
        /// <summary>离线</summary>
        Offline,
        /// <summary>故障</summary>
        Fault,
        /// <summary>维护</summary>
        Maintenance,
        /// <summary>未知</summary>
        Unknown
    }

    /// <summary>
    /// 事件类型 - 使用枚举提高类型安全性
    /// </summary>
    [Required(ErrorMessage = "事件类型不能为空")]
    public EventType Type { get; set; } = EventType.Unknown;

    /// <summary>
    /// 事件类型字符串 - 向后兼容
    /// </summary>
    [JsonIgnore]
    public string EventTypeString 
    { 
        get => _eventTypeBuffer != null ? new string(_eventTypeBuffer) : Type.ToString();
        set 
        {
            SetStringProperty(ref _eventTypeBuffer, value);
            // 尝试解析为枚举
            if (Enum.TryParse<EventType>(value, true, out var eventType))
            {
                Type = eventType;
            }
        }
    }

    /// <summary>
    /// 事件数据 - 使用泛型避免装箱
    /// </summary>
    public T? Data { get; set; }

    /// <summary>
    /// 设备状态 - 使用枚举提高类型安全性
    /// </summary>
    public DeviceStatus Status { get; set; } = DeviceStatus.Unknown;

    /// <summary>
    /// 设备状态字符串 - 向后兼容
    /// </summary>
    [JsonIgnore]
    public string StatusString 
    { 
        get => _statusBuffer != null ? new string(_statusBuffer) : Status.ToString();
        set 
        {
            SetStringProperty(ref _statusBuffer, value);
            // 尝试解析为枚举
            if (Enum.TryParse<DeviceStatus>(value, true, out var status))
            {
                Status = status;
            }
        }
    }

    /// <summary>
    /// 错误信息 - 使用内存池优化
    /// </summary>
    [StringLength(512, ErrorMessage = "错误信息长度不能超过512个字符")]
    public string Error 
    { 
        get => _errorBuffer != null ? new string(_errorBuffer) : string.Empty;
        set => SetStringProperty(ref _errorBuffer, value);
    }

    /// <summary>
    /// 区域 - 使用内存池优化
    /// </summary>
    [StringLength(64, ErrorMessage = "区域长度不能超过64个字符")]
    public string Area 
    { 
        get => _areaBuffer != null ? new string(_areaBuffer) : string.Empty;
        set => SetStringProperty(ref _areaBuffer, value);
    }

    /// <summary>
    /// 地址 - 使用内存池优化
    /// </summary>
    [StringLength(64, ErrorMessage = "地址长度不能超过64个字符")]
    public string Address 
    { 
        get => _addressBuffer != null ? new string(_addressBuffer) : string.Empty;
        set => SetStringProperty(ref _addressBuffer, value);
    }

    /// <summary>
    /// 事件严重级别 - 用于日志和监控
    /// </summary>
    [JsonIgnore]
    public LogLevel Severity { get; set; } = LogLevel.Information;

    /// <summary>
    /// 是否需要确认 - 用于重要事件
    /// </summary>
    [JsonIgnore]
    public bool RequiresAcknowledgment { get; set; }

    /// <summary>
    /// 消息大小估算 - 重写基类方法
    /// </summary>
    [JsonIgnore]
    public override int EstimatedSize => 
        base.EstimatedSize + 
        sizeof(EventType) + 
        sizeof(DeviceStatus) + 
        (Error?.Length ?? 0) * 2 + 
        (Area?.Length ?? 0) * 2 + 
        (Address?.Length ?? 0) * 2 + 
        (Data != null ? 64 : 0); // 估算数据大小

    /// <summary>
    /// 设置字符串属性并使用内存池优化
    /// </summary>
    /// <param name="buffer">字符缓冲区引用</param>
    /// <param name="value">要设置的值</param>
    private void SetStringProperty(ref char[]? buffer, string? value)
    {
        // 释放旧缓冲区
        if (buffer != null)
        {
            _charPool.Return(buffer);
            buffer = null;
        }

        // 设置新值
        if (!string.IsNullOrEmpty(value))
        {
            buffer = _charPool.Rent(value.Length);
            value.AsSpan().CopyTo(buffer.AsSpan());
        }
    }

    /// <summary>
    /// 验证事件消息的有效性 - 重写基类方法
    /// </summary>
    /// <returns>验证结果</returns>
    public override bool IsValid()
    {
        return base.IsValid() && 
               Type != EventType.Unknown && 
               !string.IsNullOrWhiteSpace(Area);
    }

    /// <summary>
    /// 创建事件消息的工厂方法
    /// </summary>
    /// <param name="eventType">事件类型</param>
    /// <param name="deviceName">设备名称</param>
    /// <param name="area">区域</param>
    /// <param name="data">事件数据</param>
    /// <returns>事件消息实例</returns>
    public static DeviceEventMessage<T> Create(EventType eventType, string deviceName, string area, T? data = null)
    {
        return new DeviceEventMessage<T>
        {
            MessageId = Guid.NewGuid().ToString("N"),
            Type = eventType,
            DeviceName = deviceName,
            Area = area,
            Data = data,
            Status = DeviceStatus.Online
        };
    }

    /// <summary>
    /// 释放资源 - 重写基类方法
    /// </summary>
    public override void Dispose()
    {
        if (!_disposed)
        {
            ReturnBuffer(ref _eventTypeBuffer);
            ReturnBuffer(ref _statusBuffer);
            ReturnBuffer(ref _errorBuffer);
            ReturnBuffer(ref _areaBuffer);
            ReturnBuffer(ref _addressBuffer);
            
            // 如果数据实现了IDisposable，也要释放
            if (Data is IDisposable disposableData)
            {
                disposableData.Dispose();
            }
            
            _disposed = true;
            base.Dispose();
        }
    }

    /// <summary>
    /// 返回缓冲区到内存池
    /// </summary>
    /// <param name="buffer">要返回的缓冲区</param>
    private void ReturnBuffer(ref char[]? buffer)
    {
        if (buffer != null)
        {
            _charPool.Return(buffer);
            buffer = null;
        }
    }
}

/// <summary>
/// 非泛型设备事件消息 - 向后兼容
/// </summary>
public sealed class DeviceEventMessage : DeviceEventMessage<object>
{
    /// <summary>
    /// 默认构造函数
    /// </summary>
    public DeviceEventMessage() : base()
    {
    }

    /// <summary>
    /// 从泛型消息创建非泛型消息
    /// </summary>
    /// <param name="genericMessage">泛型消息</param>
    public DeviceEventMessage(DeviceEventMessage<object> genericMessage) : base()
    {
        MessageId = genericMessage.MessageId;
        Timestamp = genericMessage.Timestamp;
        DeviceName = genericMessage.DeviceName;
        version = genericMessage.version;
        Type = genericMessage.Type;
        Data = genericMessage.Data;
        Status = genericMessage.Status;
        Error = genericMessage.Error;
        Area = genericMessage.Area;
        Address = genericMessage.Address;
    }
}

/// <summary>
/// 日志级别枚举 - 用于事件严重性分类
/// </summary>
public enum LogLevel
{
    /// <summary>跟踪</summary>
    Trace = 0,
    /// <summary>调试</summary>
    Debug = 1,
    /// <summary>信息</summary>
    Information = 2,
    /// <summary>警告</summary>
    Warning = 3,
    /// <summary>错误</summary>
    Error = 4,
    /// <summary>严重错误</summary>
    Critical = 5,
    /// <summary>无</summary>
    None = 6
}
