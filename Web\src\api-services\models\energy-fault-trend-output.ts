/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 故障趋势输出参数
 * @export
 * @interface EnergyFaultTrendOutput
 */
export interface EnergyFaultTrendOutput {
    /**
     * 时间点
     * @type {Date}
     * @memberof EnergyFaultTrendOutput
     */
    timePoint?: Date;
    /**
     * 时间点
     * @type {Date}
     * @memberof EnergyFaultTrendOutput
     */
    time?: Date;
    /**
     * 故障总数
     * @type {number}
     * @memberof EnergyFaultTrendOutput
     */
    totalFaultCount?: number;
    /**
     * 故障总数
     * @type {number}
     * @memberof EnergyFaultTrendOutput
     */
    totalCount?: number;
    /**
     * 新增故障数
     * @type {number}
     * @memberof EnergyFaultTrendOutput
     */
    newFaultCount?: number;
    /**
     * 已解决故障数
     * @type {number}
     * @memberof EnergyFaultTrendOutput
     */
    resolvedFaultCount?: number;
    /**
     * 待处理故障数
     * @type {number}
     * @memberof EnergyFaultTrendOutput
     */
    pendingCount?: number;
    /**
     * 待处理故障数
     * @type {number}
     * @memberof EnergyFaultTrendOutput
     */
    pendingFaultCount?: number;
    /**
     * 处理中故障数
     * @type {number}
     * @memberof EnergyFaultTrendOutput
     */
    processingCount?: number;
    /**
     * 处理中故障数
     * @type {number}
     * @memberof EnergyFaultTrendOutput
     */
    processingFaultCount?: number;
    /**
     * 已修复故障数
     * @type {number}
     * @memberof EnergyFaultTrendOutput
     */
    repairedCount?: number;
    /**
     * 解决率(%)
     * @type {number}
     * @memberof EnergyFaultTrendOutput
     */
    resolveRate?: number;
    /**
     * 平均处理时长(小时)
     * @type {number}
     * @memberof EnergyFaultTrendOutput
     */
    avgProcessTime?: number | null;
    /**
     * 低级故障数
     * @type {number}
     * @memberof EnergyFaultTrendOutput
     */
    lowLevelCount?: number;
    /**
     * 中级故障数
     * @type {number}
     * @memberof EnergyFaultTrendOutput
     */
    mediumLevelCount?: number;
    /**
     * 高级故障数
     * @type {number}
     * @memberof EnergyFaultTrendOutput
     */
    highLevelCount?: number;
    /**
     * 严重级故障数
     * @type {number}
     * @memberof EnergyFaultTrendOutput
     */
    criticalLevelCount?: number;
    /**
     * 高级故障数
     * @type {number}
     * @memberof EnergyFaultTrendOutput
     */
    highLevelFaultCount?: number;
    /**
     * 中级故障数
     * @type {number}
     * @memberof EnergyFaultTrendOutput
     */
    mediumLevelFaultCount?: number;
    /**
     * 低级故障数
     * @type {number}
     * @memberof EnergyFaultTrendOutput
     */
    lowLevelFaultCount?: number;
    /**
     * 故障设备数
     * @type {number}
     * @memberof EnergyFaultTrendOutput
     */
    faultDeviceCount?: number;
    /**
     * 重复故障数
     * @type {number}
     * @memberof EnergyFaultTrendOutput
     */
    repeatFaultCount?: number;
}
