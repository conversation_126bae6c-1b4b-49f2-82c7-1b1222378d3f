/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { EnergyDeviceGroupOutput } from './energy-device-group-output';
import { EnergyDeviceOutput } from './energy-device-output';
import { GroupStatInfo } from './group-stat-info';
/**
 * 设备分组详情输出参数
 * @export
 * @interface EnergyDeviceGroupDetailOutput
 */
export interface EnergyDeviceGroupDetailOutput {
    /**
     * 主键ID
     * @type {number}
     * @memberof EnergyDeviceGroupDetailOutput
     */
    id?: number;
    /**
     * 分组编码
     * @type {string}
     * @memberof EnergyDeviceGroupDetailOutput
     */
    groupCode?: string | null;
    /**
     * 分组名称
     * @type {string}
     * @memberof EnergyDeviceGroupDetailOutput
     */
    groupName?: string | null;
    /**
     * 分组类型
     * @type {number}
     * @memberof EnergyDeviceGroupDetailOutput
     */
    groupType?: number;
    /**
     * 父级分组ID
     * @type {number}
     * @memberof EnergyDeviceGroupDetailOutput
     */
    parentId?: number | null;
    /**
     * 位置
     * @type {string}
     * @memberof EnergyDeviceGroupDetailOutput
     */
    location?: string | null;
    /**
     * 父级分组名称
     * @type {string}
     * @memberof EnergyDeviceGroupDetailOutput
     */
    parentName?: string | null;
    /**
     * 分组描述
     * @type {string}
     * @memberof EnergyDeviceGroupDetailOutput
     */
    description?: string | null;
    /**
     * 排序
     * @type {number}
     * @memberof EnergyDeviceGroupDetailOutput
     */
    sort?: number;
    /**
     * 排序号
     * @type {number}
     * @memberof EnergyDeviceGroupDetailOutput
     */
    orderNo?: number;
    /**
     * 租户ID
     * @type {number}
     * @memberof EnergyDeviceGroupDetailOutput
     */
    tenantId?: number | null;
    /**
     * 状态
     * @type {number}
     * @memberof EnergyDeviceGroupDetailOutput
     */
    status?: number;
    /**
     * 创建时间
     * @type {Date}
     * @memberof EnergyDeviceGroupDetailOutput
     */
    createTime?: Date;
    /**
     * 更新时间
     * @type {Date}
     * @memberof EnergyDeviceGroupDetailOutput
     */
    updateTime?: Date | null;
    /**
     * 子分组列表
     * @type {Array<EnergyDeviceGroupOutput>}
     * @memberof EnergyDeviceGroupDetailOutput
     */
    children?: Array<EnergyDeviceGroupOutput> | null;
    /**
     * 
     * @type {EnergyDeviceGroupOutput}
     * @memberof EnergyDeviceGroupDetailOutput
     */
    parent?: EnergyDeviceGroupOutput;
    /**
     * 分组下的设备列表
     * @type {Array<EnergyDeviceOutput>}
     * @memberof EnergyDeviceGroupDetailOutput
     */
    devices?: Array<EnergyDeviceOutput> | null;
    /**
     * 
     * @type {GroupStatInfo}
     * @memberof EnergyDeviceGroupDetailOutput
     */
    statInfo?: GroupStatInfo;
}
