/**
 * API接口统一导出文件
 * 提供所有API接口的统一入口
 */

// 导入各个模块的API
import authApi from './auth.js'
import deviceApi from './device.js'
import lightingApi from './lighting.js'
import energyApi from './energy.js'
import faultApi from './fault.js'
import { request, get, post, put, del, upload, config } from './request.js'

// 统一导出所有API接口
export {
  // 基础请求方法
  request,
  get,
  post,
  put,
  del,
  upload,
  config,
  
  // 认证相关API
  authApi,
  
  // 设备管理API
  deviceApi,
  
  // 照明控制API
  lightingApi,
  
  // 能耗监控API
  energyApi,
  
  // 故障管理API
  faultApi
}

// 默认导出API对象
export default {
  // 基础请求方法
  request,
  get,
  post,
  put,
  del,
  upload,
  config,
  
  // 各模块API
  auth: authApi,
  device: deviceApi,
  lighting: lightingApi,
  energy: energyApi,
  fault: faultApi
}

// 便捷的API调用方法
export const api = {
  // 认证相关
  login: authApi.login,
  logout: authApi.logout,
  getUserInfo: authApi.getUserInfo,
  
  // 设备管理
  getDeviceList: deviceApi.getDeviceList,
  getDeviceDetail: deviceApi.getDeviceDetail,
  getDeviceStatistics: deviceApi.getDeviceStatistics,
  getDeviceStats: deviceApi.getDeviceStats,
  controlDeviceSwitch: deviceApi.controlDeviceSwitch,
  
  // 照明控制
  getLightingScenes: lightingApi.getLightingScenes,
  executeLightingScene: lightingApi.executeLightingScene,
  getScheduleTasks: lightingApi.getScheduleTasks,
  
  // 能耗监控
  getRealTimeEnergy: energyApi.getRealTimeEnergy,
  getEnergyOverview: energyApi.getEnergyOverview,
  getEnergyTrend: energyApi.getEnergyTrend,
  
  // 故障管理
  getFaultList: faultApi.getFaultList,
  getFaultDetail: faultApi.getFaultDetail,
  reportFault: faultApi.reportFault
}
