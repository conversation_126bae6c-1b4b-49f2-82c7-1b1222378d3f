<template>
  <div class="system-control-device">
    <el-dialog
      v-model="visible"
      title="设备控制"
      width="600px"
      :before-close="handleClose"
      destroy-on-close
    >
      <div class="device-info">
        <h4>{{ deviceInfo.deviceName }}</h4>
        <p>设备编号：{{ deviceInfo.deviceCode }}</p>
        <p>设备类型：{{ getDeviceTypeLabel(deviceInfo.deviceType) }}</p>
        <p>安装位置：{{ deviceInfo.location }}</p>
      </div>
      
      <el-divider />
      
      <el-form label-width="100px">
        <el-form-item label="开关控制">
          <el-switch
            v-model="controlForm.isOn"
            active-text="开启"
            inactive-text="关闭"
            size="large"
            @change="handleSwitchChange"
          />
        </el-form-item>
        
        <el-form-item label="亮度调节" v-if="controlForm.isOn">
          <div class="brightness-control">
            <el-slider
              v-model="controlForm.brightness"
              :min="0"
              :max="100"
              :step="5"
              show-input
              :format-tooltip="formatTooltip"
              @change="handleBrightnessChange"
            />
          </div>
        </el-form-item>
        
        <el-form-item label="当前状态">
          <el-tag :type="getStatusType(deviceInfo.status)">{{ getStatusLabel(deviceInfo.status) }}</el-tag>
        </el-form-item>
        
        <el-form-item label="实时功率">
          <span class="power-display">{{ currentPower }}W</span>
        </el-form-item>
      </el-form>
      
      <el-divider />
      
      <div class="quick-actions">
        <h5>快捷操作</h5>
        <el-button-group>
          <el-button @click="setQuickBrightness(25)" :disabled="!controlForm.isOn">25%</el-button>
          <el-button @click="setQuickBrightness(50)" :disabled="!controlForm.isOn">50%</el-button>
          <el-button @click="setQuickBrightness(75)" :disabled="!controlForm.isOn">75%</el-button>
          <el-button @click="setQuickBrightness(100)" :disabled="!controlForm.isOn">100%</el-button>
        </el-button-group>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">关闭</el-button>
          <el-button type="primary" @click="handleApply" :loading="loading">应用设置</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="controlDevice">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useDeviceApi } from '/@/api-services/device'

const emits = defineEmits<{
  reloadTable: []
}>()

const visible = ref(false)
const loading = ref(false)

const deviceInfo = reactive({
  id: '',
  deviceName: '',
  deviceCode: '',
  deviceType: '',
  location: '',
  power: 0,
  status: '1'
})

const controlForm = reactive({
  isOn: true,
  brightness: 100
})

// 计算当前功率
const currentPower = computed(() => {
  if (!controlForm.isOn) return 0
  return Math.round((deviceInfo.power * controlForm.brightness) / 100)
})

// 获取设备类型标签
const getDeviceTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    'LED': 'LED灯',
    'CFL': '节能灯',
    'FL': '荧光灯',
    'HL': '卤素灯'
  }
  return typeMap[type] || type
}

// 获取状态标签
const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    '1': '正常',
    '2': '故障',
    '3': '维护',
    '4': '停用'
  }
  return statusMap[status] || '未知'
}

// 获取状态类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '1': 'success',
    '2': 'danger',
    '3': 'warning',
    '4': 'info'
  }
  return typeMap[status] || 'info'
}

// 格式化提示
const formatTooltip = (val: number) => {
  return `${val}%`
}

// 开关变化处理
const handleSwitchChange = (value: boolean) => {
  if (!value) {
    controlForm.brightness = 0
  } else {
    controlForm.brightness = 100
  }
}

// 亮度变化处理
const handleBrightnessChange = (value: number) => {
  if (value === 0) {
    controlForm.isOn = false
  } else if (!controlForm.isOn) {
    controlForm.isOn = true
  }
}

// 快捷亮度设置
const setQuickBrightness = (brightness: number) => {
  controlForm.brightness = brightness
  if (!controlForm.isOn) {
    controlForm.isOn = true
  }
}

// 打开弹窗
const openDialog = (row: any) => {
  visible.value = true
  Object.assign(deviceInfo, row)
  controlForm.isOn = row.isOn
  controlForm.brightness = row.brightness || 100
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}

// 应用设置
const handleApply = async () => {
  try {
    loading.value = true
    
    const controlData = {
      deviceId: deviceInfo.id,
      isOn: controlForm.isOn,
      brightness: controlForm.brightness,
      controlType: 'manual',
      controlTime: new Date().toISOString()
    }
    
    const deviceApi = useDeviceApi();
			await deviceApi.control(controlData);
    ElMessage.success('设备控制成功')
    
    handleClose()
    emits('reloadTable')
  } catch (error) {
    console.error('设备控制失败:', error)
    ElMessage.error('设备控制失败，请重试')
  } finally {
    loading.value = false
  }
}

defineExpose({
  openDialog
})
</script>

<style scoped lang="scss">
.system-control-device {
  .device-info {
    background: #f5f7fa;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 16px;
    
    h4 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 16px;
    }
    
    p {
      margin: 4px 0;
      color: #606266;
      font-size: 14px;
    }
  }
  
  .brightness-control {
    width: 100%;
    
    :deep(.el-slider) {
      margin-right: 16px;
    }
  }
  
  .power-display {
    font-size: 18px;
    font-weight: bold;
    color: #409eff;
  }
  
  .quick-actions {
    h5 {
      margin: 0 0 12px 0;
      color: #303133;
      font-size: 14px;
    }
  }
  
  .dialog-footer {
    text-align: right;
  }
}
</style>