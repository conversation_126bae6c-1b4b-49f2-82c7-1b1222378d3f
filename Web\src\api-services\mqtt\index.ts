import { service as request } from '/@/utils/request';

/**
 * MQTT设备管理API接口集合
 * 提供MQTT设备扫描、查询、控制等功能的前端API调用方法
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-01-27
 */
export function useMqttApi() {
	return {
		/**
		 * 获取MQTT连接状态
		 * @returns Promise<any> MQTT连接状态信息
		 */
		getConnectionStatus: () => {
			return request({
				url: '/api/mqtt/connection/status',
				method: 'get',
			});
		},

		/**
		 * 获取设备列表
		 * @returns Promise<any> 设备列表
		 */
		getDevices: () => {
			return request({
				url: '/api/mqtt/devices',
				method: 'get',
			});
		},

		/**
		 * 获取在线设备列表
		 * @param sessionId 扫描会话ID（可选）
		 * @returns Promise<any> 在线设备列表
		 */
		getOnlineDevices: (sessionId?: string) => {
			const params: any = {};
			if (sessionId) {
				params.sessionId = sessionId;
			}
			return request({
				url: '/api/mqtt/devices/online',
				method: 'get',
				params
			});
		},

		/**
		 * 获取设备信息
		 * @param deviceKey 设备键值
		 * @returns Promise<any> 设备信息
		 */
		getDevice: (deviceKey: string) => {
			return request({
				url: `/api/mqtt/devices/${deviceKey}`,
				method: 'get',
			});
		},

		/**
		 * 获取设备统计信息
		 * @returns Promise<any> 设备统计信息
		 */
		getDeviceStatistics: () => {
			return request({
				url: '/api/mqtt/devices/statistics',
				method: 'get',
			});
		},

		/**
		 * 设备扫描
		 * 通过MQTT服务接口在线查询绑定在网关下的智能设备
		 * @param gatewayName 网关名称
		 * @param area 区域，默认为"00 00"
		 * @param address 地址，默认为"00 00"
		 * @param timeoutSeconds 超时时间(秒)，默认10秒
		 * @returns Promise<any> 扫描请求ID
		 */
		scanDevices: (gatewayName: string, area: string = '00 00', address: string = '00 00', timeoutSeconds: number = 10) => {
			return request({
				url: '/api/mqtt/devices/scan',
				method: 'post',
				params: {
					GatewayName: gatewayName,
					area,
					address,
					timeoutSeconds
				}
			});
		},

		/**
		 * 获取设备状态
		 * @param area 区域
		 * @param address 地址
		 * @param timeoutSeconds 超时时间(秒)，默认10秒
		 * @returns Promise<any> 设备状态信息
		 */
		getDeviceStatus: (area: string, address: string, timeoutSeconds: number = 10) => {
			return request({
				url: '/api/mqtt/devices/status',
				method: 'post',
				params: {
					area,
					address,
					timeoutSeconds
				}
			});
		},

		/**
		 * 获取待处理请求
		 * @returns Promise<any> 待处理请求列表
		 */
		getPendingRequests: () => {
			return request({
				url: '/api/mqtt/devices/requests',
				method: 'get',
			});
		},

		/**
		 * 重新连接MQTT
		 * @returns Promise<any> 重连结果
		 */
		reconnect: () => {
			return request({
				url: '/api/mqtt/connection/reconnect',
				method: 'post',
			});
		},

		/**
		 * 断开MQTT连接
		 * @returns Promise<any> 断开连接结果
		 */
		disconnect: () => {
			return request({
				url: '/api/mqtt/connection/disconnect',
				method: 'post',
			});
		}
	};
}

/**
 * MQTT设备扫描结果接口
 * 定义设备扫描返回的数据结构
 */
export interface MqttDeviceScanResult {
	/** 请求ID */
	requestId: string;
	/** 扫描状态 */
	status: 'scanning' | 'completed' | 'timeout' | 'error';
	/** 发现的设备列表 */
	devices: MqttDeviceInfo[];
	/** 扫描开始时间 */
	startTime: string;
	/** 扫描结束时间 */
	endTime?: string;
	/** 错误信息 */
	error?: string;
}

/**
 * MQTT设备信息接口
 * 定义设备的基本信息结构
 */
export interface MqttDeviceInfo {
	/** 设备键值 */
	deviceKey: string;
	/** 设备名称 */
	deviceName: string;
	/** 设备类型 */
	deviceType: string;
	/** 设备地址 */
	address: string;
	/** 设备状态 */
	status: 'online' | 'offline' | 'error';
	/** 信号强度 */
	signalStrength?: number;
	/** 最后上线时间 */
	lastOnlineTime: string;
	/** 设备属性 */
	properties?: Record<string, any>;
	/** 父设备ID（网关ID） */
	parentDeviceId?: string;
	/** 设备层级 */
	level?: number;
}

/**
 * MQTT连接状态接口
 * 定义MQTT连接的状态信息
 */
export interface MqttConnectionStats {
	/** 是否已连接 */
	isConnected: boolean;
	/** 连接时间 */
	connectedTime?: string;
	/** 最后消息时间 */
	lastMessageTime?: string;
	/** 重连次数 */
	reconnectCount: number;
	/** 发送消息数 */
	sentMessages: number;
	/** 接收消息数 */
	receivedMessages: number;
}

/**
 * 设备统计信息接口
 * 定义设备的统计数据结构
 */
export interface DeviceStatistics {
	/** 设备总数 */
	totalDevices: number;
	/** 在线设备数 */
	onlineDevices: number;
	/** 离线设备数 */
	offlineDevices: number;
	/** 故障设备数 */
	errorDevices: number;
	/** 按类型分组的设备数量 */
	devicesByType: Record<string, number>;
}