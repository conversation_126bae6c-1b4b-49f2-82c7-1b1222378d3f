/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 翻译表数据导入实体
 * @export
 * @interface ImportSysLangTextInput
 */
export interface ImportSysLangTextInput {
    /**
     * 记录Id
     * @type {number}
     * @memberof ImportSysLangTextInput
     */
    id?: number;
    /**
     * 错误信息
     * @type {string}
     * @memberof ImportSysLangTextInput
     */
    error?: string | null;
    /**
     * 所属实体名
     * @type {string}
     * @memberof ImportSysLangTextInput
     */
    entityName?: string | null;
    /**
     * 所属实体ID
     * @type {number}
     * @memberof ImportSysLangTextInput
     */
    entityId?: number | null;
    /**
     * 字段名
     * @type {string}
     * @memberof ImportSysLangTextInput
     */
    fieldName?: string | null;
    /**
     * 语言代码
     * @type {string}
     * @memberof ImportSysLangTextInput
     */
    langCode?: string | null;
    /**
     * 翻译内容
     * @type {string}
     * @memberof ImportSysLangTextInput
     */
    content?: string | null;
}
