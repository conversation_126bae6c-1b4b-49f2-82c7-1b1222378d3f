import js from '@eslint/js';
import vue from 'eslint-plugin-vue';

export default [
  js.configs.recommended,
  ...vue.configs['flat/essential'],
  {
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      globals: {
        // uni-app全局变量
        uni: 'readonly',
        wx: 'readonly',
        plus: 'readonly',
        weex: 'readonly',
        getCurrentPages: 'readonly',
        getApp: 'readonly',
        // 浏览器环境
        window: 'readonly',
        document: 'readonly',
        console: 'readonly',
        setTimeout: 'readonly',
        clearTimeout: 'readonly',
        setInterval: 'readonly',
        clearInterval: 'readonly',
        requestAnimationFrame: 'readonly',
        cancelAnimationFrame: 'readonly',
        IntersectionObserver: 'readonly',
        Image: 'readonly',
        performance: 'readonly',
        atob: 'readonly',
        btoa: 'readonly',
        self: 'readonly',
        // Node.js环境
        process: 'readonly',
        Buffer: 'readonly',
        global: 'readonly',
        __dirname: 'readonly',
        __filename: 'readonly',
        require: 'readonly',
        module: 'readonly',
        exports: 'readonly'
      }
    },
    rules: {
      // 允许未使用的变量（因为uni-app有很多全局变量）
      'no-unused-vars': 'warn',
      // 允许console语句
      'no-console': 'off',
      // 允许debugger语句
      'no-debugger': 'warn',
      // Vue相关规则
      'vue/multi-word-component-names': 'off',
      'vue/no-unused-components': 'warn'
    }
  },
  {
    ignores: [
      'node_modules/**',
      'dist/**',
      'build/**',
      'unpackage/**',
      '*.min.js'
    ]
  }
];