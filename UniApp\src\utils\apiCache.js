/**
 * API缓存装饰器
 * 为API请求提供自动缓存功能
 */

import cacheManager from './cache.js'

/**
 * API缓存配置
 */
const API_CACHE_CONFIG = {
  // 能源数据缓存配置
  energy: {
    overview: { expire: 2 * 60 * 1000, key: 'energy_overview' }, // 2分钟
    todayStats: { expire: 5 * 60 * 1000, key: 'energy_today_stats' }, // 5分钟
    realtimeData: { expire: 30 * 1000, key: 'energy_realtime' }, // 30秒
    chartData: { expire: 10 * 60 * 1000, key: 'energy_chart' }, // 10分钟
    deviceRanking: { expire: 5 * 60 * 1000, key: 'energy_ranking' }, // 5分钟
    historyRecords: { expire: 15 * 60 * 1000, key: 'energy_history' } // 15分钟
  },
  // 设备数据缓存配置
  device: {
    list: { expire: 3 * 60 * 1000, key: 'device_list' }, // 3分钟
    groups: { expire: 10 * 60 * 1000, key: 'device_groups' }, // 10分钟
    detail: { expire: 2 * 60 * 1000, key: 'device_detail' } // 2分钟
  },
  // 照明数据缓存配置
  lighting: {
    scenes: { expire: 10 * 60 * 1000, key: 'lighting_scenes' } // 10分钟
  },
  // 用户数据缓存配置
  user: {
    info: { expire: 30 * 60 * 1000, key: 'user_info' } // 30分钟
  }
}

/**
 * 生成缓存键
 * @param {string} baseKey 基础键
 * @param {object} params 请求参数
 * @returns {string} 缓存键
 */
function generateCacheKey(baseKey, params = {}) {
  if (Object.keys(params).length === 0) {
    return baseKey
  }
  
  // 将参数排序后拼接到键中
  const sortedParams = Object.keys(params)
    .sort()
    .map(key => `${key}=${JSON.stringify(params[key])}`)
    .join('&')
  
  return `${baseKey}_${btoa(sortedParams).replace(/[+/=]/g, '')}`
}

/**
 * API缓存装饰器
 * @param {Function} apiFunction 原始API函数
 * @param {object} cacheConfig 缓存配置
 * @param {boolean} forceRefresh 是否强制刷新
 * @returns {Function} 包装后的API函数
 */
export function withCache(apiFunction, cacheConfig, forceRefresh = false) {
  return async function(...args) {
    const params = args[0] || {}
    const cacheKey = generateCacheKey(cacheConfig.key, params)
    
    // 如果不强制刷新，先尝试从缓存获取
    if (!forceRefresh) {
      const cachedData = cacheManager.getCache(cacheKey)
      if (cachedData !== null) {
        console.log(`从缓存获取数据: ${cacheKey}`)
        return cachedData
      }
    }
    
    try {
      // 调用原始API函数
      console.log(`调用API获取数据: ${cacheKey}`)
      const result = await apiFunction.apply(this, args)
      
      // 缓存结果
      if (result && result.success !== false) {
        cacheManager.setCache(cacheKey, result, cacheConfig.expire)
        console.log(`数据已缓存: ${cacheKey}`)
      }
      
      return result
    } catch (error) {
      // 网络错误时尝试返回缓存数据
      const cachedData = cacheManager.getCache(cacheKey)
      if (cachedData !== null) {
        console.warn(`网络错误，返回缓存数据: ${cacheKey}`, error)
        return cachedData
      }
      
      throw error
    }
  }
}

/**
 * 创建带缓存的API函数
 * @param {Function} apiFunction 原始API函数
 * @param {string} module 模块名
 * @param {string} method 方法名
 * @returns {Function} 带缓存的API函数
 */
export function createCachedApi(apiFunction, moduleOrKey, methodOrExpire) {
  // 支持两种调用方式：
  // 1. createCachedApi(fn, 'module', 'method') - 从配置中查找
  // 2. createCachedApi(fn, 'cache_key', expireTime) - 直接指定
  
  if (typeof methodOrExpire === 'string') {
    // 方式1：从配置中查找
    const cacheConfig = API_CACHE_CONFIG[moduleOrKey]?.[methodOrExpire]
    if (!cacheConfig) {
      console.warn(`未找到缓存配置: ${moduleOrKey}.${methodOrExpire}`)
      return apiFunction
    }
    return withCache(apiFunction, cacheConfig)
  } else {
    // 方式2：直接指定缓存配置
    const cacheConfig = {
      key: moduleOrKey,
      expire: methodOrExpire
    }
    return withCache(apiFunction, cacheConfig)
  }
}

/**
 * 清除指定模块的缓存
 * @param {string} module 模块名
 * @param {string} method 方法名（可选）
 */
export function clearModuleCache(module, method = null) {
  const moduleConfig = API_CACHE_CONFIG[module]
  if (!moduleConfig) {
    console.warn(`未找到模块配置: ${module}`)
    return
  }
  
  if (method) {
    // 清除指定方法的缓存
    const methodConfig = moduleConfig[method]
    if (methodConfig) {
      cacheManager.removeCache(methodConfig.key)
      console.log(`已清除缓存: ${module}.${method}`)
    }
  } else {
    // 清除整个模块的缓存
    Object.values(moduleConfig).forEach(config => {
      cacheManager.removeCache(config.key)
    })
    console.log(`已清除模块缓存: ${module}`)
  }
}

/**
 * 预加载数据到缓存
 * @param {Function} apiFunction 原始API函数
 * @param {object} cacheConfig 缓存配置
 * @param {object} params 请求参数
 */
export async function preloadCache(apiFunction, cacheConfig, params = {}) {
  try {
    const cacheKey = generateCacheKey(cacheConfig.key, params)
    
    // 检查缓存是否已存在
    const cachedData = cacheManager.getCache(cacheKey)
    if (cachedData !== null) {
      console.log(`缓存已存在，跳过预加载: ${cacheKey}`)
      return
    }
    
    // 预加载数据
    console.log(`预加载数据: ${cacheKey}`)
    const result = await apiFunction(params)
    
    if (result && result.success !== false) {
      cacheManager.setCache(cacheKey, result, cacheConfig.expire)
      console.log(`预加载完成: ${cacheKey}`)
    }
  } catch (error) {
    console.error('预加载失败:', error)
  }
}

/**
 * 批量预加载数据
 * @param {Array} preloadTasks 预加载任务列表
 */
export async function batchPreload(preloadTasks) {
  const tasks = preloadTasks.map(task => 
    preloadCache(task.apiFunction, task.cacheConfig, task.params)
  )
  
  try {
    await Promise.allSettled(tasks)
    console.log('批量预加载完成')
  } catch (error) {
    console.error('批量预加载失败:', error)
  }
}

/**
 * 检查缓存健康状态
 * @returns {object} 缓存健康状态
 */
export function getCacheHealth() {
  const stats = cacheManager.getCacheStats()
  const health = {
    ...stats,
    status: 'healthy',
    issues: []
  }
  
  // 检查缓存大小
  if (stats.totalSize > 100) {
    health.status = 'warning'
    health.issues.push('缓存数量过多，建议清理')
  }
  
  return health
}

export { API_CACHE_CONFIG }
export default {
  withCache,
  createCachedApi,
  clearModuleCache,
  preloadCache,
  batchPreload,
  getCacheHealth,
  API_CACHE_CONFIG
}