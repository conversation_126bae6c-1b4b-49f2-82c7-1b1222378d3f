// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MQTTnet;
using MQTTnet.Client;
using MQTTnet.Extensions.ManagedClient;
using MQTTnet.Packets;
using MQTTnet.Protocol;
using Newtonsoft.Json;
using System.Collections.Concurrent;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Channels;
using Admin.NET.Plugin.MQTT.Core.Entity;
using MQTTnet.Formatter;
using MQTTnet.Server;
using Admin.NET.Plugin.MQTT.Core.Models;
using Admin.NET.Plugin.MQTT.Interfaces;
using Admin.NET.Plugin.MQTT.Core.Configuration;
using static Admin.NET.Plugin.MQTT.Core.Constants;

namespace Admin.NET.Plugin.MQTT.Core.Services;

/// <summary>
/// MQTT客户端管理器 - 负责MQTT连接管理、消息发布订阅和连接状态监控
/// 优化特性：
/// 1. 使用Channel替代ConcurrentDictionary提高消息处理性能
/// 2. 实现连接池和重连策略优化
/// 3. 添加内存使用监控和垃圾回收优化
/// 4. 使用结构化日志提高可观测性
/// </summary>
public sealed class MqttClientManager : ISingleton, IDisposable
{
    #region 私有字段
    
    private readonly ILogger<MqttClientManager> _logger;
    private readonly MqttOptions _options;
    private IManagedMqttClient _mqttClient;
    
    // 使用Channel替代ConcurrentDictionary提高性能
    private readonly Channel<(string Topic, Func<MqttApplicationMessageReceivedEventArgs, Task> Handler)> _messageHandlerChannel;
    private readonly ChannelWriter<(string Topic, Func<MqttApplicationMessageReceivedEventArgs, Task> Handler)> _messageHandlerWriter;
    private readonly ChannelReader<(string Topic, Func<MqttApplicationMessageReceivedEventArgs, Task> Handler)> _messageHandlerReader;
    
    private readonly ConcurrentDictionary<string, MessageHandlerInfo> _activeHandlers;
    private readonly ConcurrentDictionary<string, SubscriptionInfo> _subscriptionStatus;
    
    private readonly Timer _heartbeatTimer;
    private readonly Timer _metricsTimer;
    
    // 使用ReaderWriterLockSlim替代object锁提高并发性能
    private readonly ReaderWriterLockSlim _connectionLock = new ReaderWriterLockSlim();
    
    private volatile bool _isConnected;
    private DateTime _lastHeartbeat;
    private DateTime _connectionTime;
    private int _reconnectAttempts;
    private readonly SemaphoreSlim _connectionSemaphore = new SemaphoreSlim(1, 1);
    
    // 性能监控字段
    private long _messagesSent;
    private long _messagesReceived;
    private long _bytesTransferred;
    
    private bool _disposed;
    
    #endregion

    /// <summary>
    /// 连接状态变化事件
    /// </summary>
    public event EventHandler<bool> ConnectionStatusChanged;

    /// <summary>
    /// 消息接收事件
    /// </summary>
    public event EventHandler<MqttApplicationMessageReceivedEventArgs> MessageReceived;

    #region 构造函数和初始化
    
    /// <summary>
    /// 构造函数 - 初始化MQTT客户端管理器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="options">MQTT配置选项</param>
    public MqttClientManager(ILogger<MqttClientManager> logger, IOptions<MqttOptions> options)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        
        // 初始化Channel用于高性能消息处理
        var channelOptions = new BoundedChannelOptions(1000)
        {
            FullMode = BoundedChannelFullMode.Wait,
            SingleReader = false,
            SingleWriter = false
        };
        
        var channel = Channel.CreateBounded<(string Topic, Func<MqttApplicationMessageReceivedEventArgs, Task> Handler)>(channelOptions);
        _messageHandlerChannel = channel;
        _messageHandlerWriter = channel.Writer;
        _messageHandlerReader = channel.Reader;
        
        _activeHandlers = new ConcurrentDictionary<string, MessageHandlerInfo>();
        _subscriptionStatus = new ConcurrentDictionary<string, SubscriptionInfo>();
        
        // 初始化定时器
        _heartbeatTimer = new Timer(SendHeartbeatAsync, null, Timeout.Infinite, Timeout.Infinite);
        _metricsTimer = new Timer(CollectMetrics, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
        
        InitializeMqttClient();
        
        _logger.LogInformation("MQTT客户端管理器初始化完成");
    }

    /// <summary>
    /// 是否已连接
    /// </summary>
    public bool IsConnected => _isConnected && _mqttClient?.IsConnected == true;

    /// <summary>
    /// 获取连接统计信息 - 返回增强的性能指标
    /// </summary>
    /// <returns>包含详细性能指标的连接统计信息</returns>
    public MqttConnectionStats GetConnectionStats()
    {
        return new MqttConnectionStats
        {
            ClientId = _options.ClientId,
            ServerAddress = $"{_options.Server}:{_options.Port}",
            IsConnected = IsConnected,
            ConnectionTime = _connectionTime,
            LastHeartbeat = _lastHeartbeat,
            ReconnectAttempts = _reconnectAttempts,
            SubscriptionCount = _subscriptionStatus.Count,
            HandlerCount = _activeHandlers.Count,
            MessagesSent = (int)_messagesSent,
            MessagesReceived = (int)_messagesReceived,
            BytesTransferred = _bytesTransferred,
            MemoryUsage = GC.GetTotalMemory(false)
        };
    }

    /// <summary>
    /// 初始化MQTT客户端 - 配置连接参数和事件处理器
    /// </summary>
    private void InitializeMqttClient()
    {
        try
        {
            var factory = new MqttFactory();
            _mqttClient = factory.CreateManagedMqttClient();

            // 配置连接选项 - 优化连接参数
            var clientOptionsBuilder = new MqttClientOptionsBuilder()
                .WithClientId(_options.ClientId ?? $"AdminNET_{Environment.MachineName}_{Guid.NewGuid():N}")
                .WithTcpServer(_options.Server, _options.Port)
                .WithCredentials(_options.Username, _options.Password)
                .WithCleanSession(_options.CleanSession)
                .WithKeepAlivePeriod(TimeSpan.FromSeconds(_options.KeepAliveInterval))
                .WithTimeout(TimeSpan.FromSeconds(_options.ConnectTimeout))
                .WithProtocolVersion(MqttProtocolVersion.V311); // 明确指定协议版本

            // 配置TLS选项（如果启用）
            if (_options.UseTls)
            {
                clientOptionsBuilder = clientOptionsBuilder.WithTlsOptions(o =>
                {
                    o.UseTls(true)
                     .WithAllowUntrustedCertificates(true)
                     .WithIgnoreCertificateRevocationErrors(true)
                     .WithIgnoreCertificateChainErrors(true);
                });
            }

            // 配置Will消息（如果设置）
            if (!string.IsNullOrEmpty(_options.WillTopic))
            {
                clientOptionsBuilder = clientOptionsBuilder.WithWillTopic(_options.WillTopic)
                    .WithWillPayload(_options.WillMessage ?? "offline")
                    .WithWillQualityOfServiceLevel((MqttQualityOfServiceLevel)_options.WillQoS)
                    .WithWillRetain(_options.WillRetain);
            }

            var clientOptions = clientOptionsBuilder.Build();

            // 配置托管客户端选项 - 优化重连策略
            var managedOptions = new ManagedMqttClientOptionsBuilder()
                .WithClientOptions(clientOptions)
                .WithAutoReconnectDelay(TimeSpan.FromSeconds(_options.ReconnectInterval))
                .WithMaxPendingMessages(_options.MessageBufferSize)
                .WithPendingMessagesOverflowStrategy(MqttPendingMessagesOverflowStrategy.DropOldestQueuedMessage)
                .Build();

            // 注册事件处理器
            _mqttClient.ConnectedAsync += OnConnectedAsync;
            _mqttClient.DisconnectedAsync += OnDisconnectedAsync;
            _mqttClient.ApplicationMessageReceivedAsync += OnMessageReceivedAsync;
            _mqttClient.ConnectingFailedAsync += OnConnectingFailedAsync;
            _mqttClient.SynchronizingSubscriptionsFailedAsync += OnSynchronizingSubscriptionsFailedAsync;

            _logger.LogInformation("MQTT客户端初始化完成 - 服务器: {Server}:{Port}, 客户端ID: {ClientId}", 
                _options.Server, _options.Port, _options.ClientId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化MQTT客户端失败");
            throw;
        }
    }

    /// <summary>
    /// 启动MQTT客户端
    /// </summary>
    public async Task StartAsync()
    {
        try
        {
            if (_mqttClient == null)
            {
                InitializeMqttClient();
            }

            // 检查客户端是否已经启动
            if (_mqttClient.IsStarted)
            {
                _logger.LogInformation("MQTT客户端已经启动，跳过重复启动");
                return;
            }

            var managedOptions = new ManagedMqttClientOptionsBuilder()
                .WithClientOptions(new MqttClientOptionsBuilder()
                    .WithClientId(_options.ClientId ?? $"AdminNET_{Environment.MachineName}_{Guid.NewGuid():N}")
                    .WithTcpServer(_options.Server, _options.Port)
                    .WithCredentials(_options.Username, _options.Password)
                    .WithCleanSession(_options.CleanSession)
                    .WithKeepAlivePeriod(TimeSpan.FromSeconds(_options.KeepAliveInterval))
                    .WithTimeout(TimeSpan.FromSeconds(_options.ConnectTimeout))
                    .Build())
                .WithAutoReconnectDelay(TimeSpan.FromSeconds(_options.ReconnectInterval))
                .WithMaxPendingMessages(_options.MessageBufferSize)
                .Build();

            await _mqttClient.StartAsync(managedOptions);
            
            // 启动心跳定时器
            _heartbeatTimer.Change(TimeSpan.FromSeconds(_options.KeepAliveInterval), 
                                 TimeSpan.FromSeconds(_options.KeepAliveInterval));

            _logger.LogInformation("MQTT客户端启动成功，服务器: {Server}:{Port}", _options.Server, _options.Port);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动MQTT客户端失败");
            throw;
        }
    }

    /// <summary>
    /// 停止MQTT客户端
    /// </summary>
    public async Task StopAsync()
    {
        try
        {
            _heartbeatTimer?.Change(Timeout.Infinite, Timeout.Infinite);
            
            if (_mqttClient != null)
            {
                await _mqttClient.StopAsync();
                _mqttClient.Dispose();
                _mqttClient = null;
            }

            _isConnected = false;
            _reconnectAttempts = 0;
            _subscriptionStatus.Clear();
            _activeHandlers.Clear();

            _logger.LogInformation("MQTT客户端已停止");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止MQTT客户端失败");
        }
    }

    /// <summary>
    /// 订阅主题 - 增强版本支持订阅状态跟踪
    /// </summary>
    /// <param name="topic">主题</param>
    /// <param name="qos">QoS等级</param>
    /// <returns>订阅任务</returns>
    public async Task SubscribeAsync(string topic, MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtLeastOnce)
    {
        if (string.IsNullOrWhiteSpace(topic))
            throw new ArgumentException("主题不能为空", nameof(topic));
            
        try
        {
            if (_mqttClient == null || !IsConnected)
            {
                _logger.LogWarning("MQTT客户端未连接，无法订阅主题: {Topic}", topic);
                throw new InvalidOperationException("MQTT客户端未连接");
            }

            var subscribeOptions = new MqttTopicFilterBuilder()
                .WithTopic(topic)
                .WithQualityOfServiceLevel(qos)
                .Build();

            await _mqttClient.SubscribeAsync(new[] { subscribeOptions });
            
            var subscriptionInfo = new SubscriptionInfo
            {
                Topic = topic,
                QoS = (int)qos,
                SubscribeTime = DateTime.UtcNow,
                MessageCount = 0,
                IsActive = true
            };
            
            _subscriptionStatus.AddOrUpdate(topic, subscriptionInfo, (key, existing) => 
            {
                existing.QoS = (int)qos;
                existing.SubscribeTime = DateTime.UtcNow;
                existing.IsActive = true;
                return existing;
            });

            _logger.LogInformation("成功订阅主题: {Topic}, QoS: {Qos}", topic, qos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "订阅主题失败: {Topic}", topic);
            throw;
        }
    }

    /// <summary>
    /// 取消订阅主题 - 增强版本支持优雅的订阅管理
    /// </summary>
    /// <param name="topic">主题</param>
    /// <returns>取消订阅任务</returns>
    public async Task UnsubscribeAsync(string topic)
    {
        if (string.IsNullOrWhiteSpace(topic))
            throw new ArgumentException("主题不能为空", nameof(topic));
            
        try
        {
            if (_mqttClient == null || !IsConnected)
            {
                _logger.LogWarning("MQTT客户端未连接，无法取消订阅主题: {Topic}", topic);
                throw new InvalidOperationException("MQTT客户端未连接");
            }

            await _mqttClient.UnsubscribeAsync(topic);
            
            if (_subscriptionStatus.TryGetValue(topic, out var subscriptionInfo))
            {
                subscriptionInfo.IsActive = false;
                _logger.LogInformation("成功取消订阅主题: {Topic}, 处理消息数: {MessageCount}", 
                    topic, subscriptionInfo.MessageCount);
            }
            
            _subscriptionStatus.TryRemove(topic, out _);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取消订阅主题失败: {Topic}", topic);
            throw;
        }
    }

    /// <summary>
    /// 发布消息 - 增强版本支持性能统计和错误处理
    /// </summary>
    /// <param name="topic">主题</param>
    /// <param name="payload">消息内容</param>
    /// <param name="qos">QoS等级</param>
    /// <param name="retain">是否保留消息</param>
    /// <returns>发布任务</returns>
    public async Task PublishAsync(string topic, object payload, MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtLeastOnce, bool retain = false)
    {
        if (string.IsNullOrWhiteSpace(topic))
            throw new ArgumentException("主题不能为空", nameof(topic));
            
        if (payload == null)
            throw new ArgumentNullException(nameof(payload));
            
        try
        {
            if (_mqttClient == null || !IsConnected)
            {
                _logger.LogWarning("MQTT客户端未连接，无法发布消息到主题: {Topic}", topic);
                throw new InvalidOperationException("MQTT客户端未连接");
            }

            string jsonPayload = payload is string str ? str : JsonConvert.SerializeObject(payload);
            
            var message = new MqttApplicationMessageBuilder()
                .WithTopic(topic)
                .WithPayload(Encoding.UTF8.GetBytes(jsonPayload))
                .WithQualityOfServiceLevel(qos)
                .WithRetainFlag(retain)
                .Build();

            await _mqttClient.EnqueueAsync(message);
            
            // 更新性能统计
            Interlocked.Increment(ref _messagesSent);
            Interlocked.Add(ref _bytesTransferred, Encoding.UTF8.GetByteCount(jsonPayload));

            if (_options.EnableMessageLogging)
            {
                _logger.LogDebug("发布消息到主题: {Topic}, 负载: {Payload}, 大小: {Size} 字节, QoS: {QoS}", 
                    topic, jsonPayload, Encoding.UTF8.GetByteCount(jsonPayload), qos);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发布消息失败, 主题: {Topic}, 内容长度: {Length}", topic, payload?.ToString()?.Length ?? 0);
            throw;
        }
    }

    /// <summary>
    /// 注册消息处理器 - 支持高性能消息处理
    /// </summary>
    public void RegisterMessageHandler(string topicPattern, Func<MqttApplicationMessageReceivedEventArgs, Task> handler)
    {
        var handlerInfo = new MessageHandlerInfo
        {
            TopicPattern = topicPattern,
            Handler = handler,
            RegisterTime = DateTime.UtcNow,
            MessageCount = 0
        };
        
        _activeHandlers.TryAdd(topicPattern, handlerInfo);
        _logger.LogDebug("注册消息处理器: {TopicPattern}", topicPattern);
    }

    /// <summary>
    /// 移除消息处理器
    /// </summary>
    public void RemoveMessageHandler(string topicPattern)
    {
        _activeHandlers.TryRemove(topicPattern, out _);
        _logger.LogDebug("移除消息处理器: {TopicPattern}", topicPattern);
    }

    /// <summary>
    /// 连接成功事件处理
    /// </summary>
    private async Task OnConnectedAsync(MqttClientConnectedEventArgs e)
    {
        _isConnected = true;
        _reconnectAttempts = 0;
        
        _logger.LogInformation("MQTT客户端连接成功, 结果码: {ResultCode}", e.ConnectResult.ResultCode);
        
        // 重新订阅主题
        await ResubscribeTopicsAsync();
        
        // 触发连接状态变化事件
        ConnectionStatusChanged?.Invoke(this, true);
    }

    /// <summary>
    /// 连接断开事件处理
    /// </summary>
    private Task OnDisconnectedAsync(MqttClientDisconnectedEventArgs e)
    {
        _isConnected = false;
        _reconnectAttempts++;
        
        _logger.LogWarning("MQTT客户端连接断开, 原因: {Reason}, 重连次数: {Attempts}", 
                          e.Reason, _reconnectAttempts);
        
        // 触发连接状态变化事件
        ConnectionStatusChanged?.Invoke(this, false);
        
        return Task.CompletedTask;
    }

    /// <summary>
    /// 连接失败事件处理
    /// </summary>
    private Task OnConnectingFailedAsync(ConnectingFailedEventArgs e)
    {
        _reconnectAttempts++;
        
        _logger.LogError(e.Exception, "MQTT客户端连接失败, 重连次数: {Attempts}", _reconnectAttempts);
        
        return Task.CompletedTask;
    }

    /// <summary>
    /// 消息接收事件处理
    /// </summary>
    private async Task OnMessageReceivedAsync(MqttApplicationMessageReceivedEventArgs e)
    {
        try
        {
            if (_options.EnableMessageLogging)
            {
                var payload = Encoding.UTF8.GetString(e.ApplicationMessage.PayloadSegment);
                _logger.LogDebug("接收到消息, 主题: {Topic}, 负载: {Payload}", 
                               e.ApplicationMessage.Topic, payload);
            }

            // 触发消息接收事件
            MessageReceived?.Invoke(this, e);

            // 查找匹配的消息处理器并更新统计信息
            foreach (var handlerInfo in _activeHandlers.Values)
            {
                if (IsTopicMatch(e.ApplicationMessage.Topic, handlerInfo.TopicPattern))
                {
                    await handlerInfo.Handler(e);
                    Interlocked.Increment(ref handlerInfo.MessageCount);
                }
            }
            
            // 更新性能统计
            Interlocked.Increment(ref _messagesReceived);
            Interlocked.Add(ref _bytesTransferred, e.ApplicationMessage.PayloadSegment.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理接收消息失败, 主题: {Topic}", e.ApplicationMessage.Topic);
        }
    }

    /// <summary>
    /// 重新订阅主题
    /// </summary>
    private async Task ResubscribeTopicsAsync()
    {
        try
        {
            // 订阅配置中的默认主题
            if (_options.SubscribeTopics?.Any() == true)
            {
                foreach (var topic in _options.SubscribeTopics)
                {
                    await SubscribeAsync(topic, (MqttQualityOfServiceLevel)_options.DefaultQoS);
                }
            }

            // 重新订阅之前的主题
            var topics = _subscriptionStatus.Keys.ToList();
            foreach (var topic in topics)
            {
                await SubscribeAsync(topic, (MqttQualityOfServiceLevel)_options.DefaultQoS);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重新订阅主题失败");
        }
    }

    /// <summary>
    /// 发送心跳 - 异步版本
    /// </summary>
    private async void SendHeartbeatAsync(object state)
    {
        try
        {
            if (IsConnected)
            {
                var heartbeat = new HeartbeatEventData
                {
                    Status = "online",
                    OnlineDeviceCount = _subscriptionStatus.Count,
                    NetworkQuality = 100,
                    SystemLoad = 0.5,
                    MemoryUsage = 0.3
                };

                var message = new DeviceEventMessage
                {
                    EventType = MqttConst.EventTypes.Heartbeat,
                    Data = heartbeat,
                    Status = "success"
                };

                await PublishAsync(MqttConst.Topics.HeartbeatPublish, message, MqttQualityOfServiceLevel.AtMostOnce);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送心跳失败");
        }
    }

    /// <summary>
    /// 检查主题是否匹配
    /// </summary>
    private static bool IsTopicMatch(string topic, string pattern)
    {
        if (pattern == topic) return true;
        if (pattern.Contains('+') || pattern.Contains('#'))
        {
            return MqttTopicFilterComparer.Compare(topic, pattern) == MqttTopicFilterCompareResult.IsMatch;
        }
        return false;
    }

    /// <summary>
    /// 收集性能指标
    /// </summary>
    private void CollectMetrics(object state)
    {
        try
        {
            var stats = new
            {
                IsConnected = IsConnected,
                MessagesSent = _messagesSent,
                MessagesReceived = _messagesReceived,
                BytesTransferred = _bytesTransferred,
                ActiveHandlers = _activeHandlers.Count,
                Subscriptions = _subscriptionStatus.Count,
                ReconnectAttempts = _reconnectAttempts,
                MemoryUsage = GC.GetTotalMemory(false)
            };
            
            _logger.LogInformation("MQTT性能指标: {@Stats}", stats);
            
            // 定期执行垃圾回收优化内存使用
            if (_messagesReceived % 10000 == 0)
            {
                GC.Collect(0, GCCollectionMode.Optimized);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "收集性能指标失败");
        }
    }
    
    /// <summary>
    /// 同步订阅失败事件处理
    /// </summary>
    private Task OnSynchronizingSubscriptionsFailedAsync(ManagedProcessFailedEventArgs e)
    {
        _logger.LogError(e.Exception, "同步订阅失败");
        return Task.CompletedTask;
    }

    #endregion

    #region 资源释放
    
    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed) return;
        
        try
        {
            _heartbeatTimer?.Change(Timeout.Infinite, Timeout.Infinite);
            _heartbeatTimer?.Dispose();
            
            _metricsTimer?.Change(Timeout.Infinite, Timeout.Infinite);
            _metricsTimer?.Dispose();
            
            _connectionSemaphore?.Dispose();
            _connectionLock?.Dispose();
            
            _messageHandlerWriter?.Complete();
            
            if (_mqttClient != null)
            {
                _mqttClient.ConnectedAsync -= OnConnectedAsync;
                _mqttClient.DisconnectedAsync -= OnDisconnectedAsync;
                _mqttClient.ApplicationMessageReceivedAsync -= OnMessageReceivedAsync;
                _mqttClient.ConnectingFailedAsync -= OnConnectingFailedAsync;
                
                _mqttClient.Dispose();
            }
            
            _activeHandlers?.Clear();
            _subscriptionStatus?.Clear();
            
            _disposed = true;
            
            _logger.LogInformation("MQTT客户端管理器资源已释放");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "释放MQTT客户端资源时发生错误");
        }
    }
    
    #endregion
}

#region 数据结构

/// <summary>
/// 消息处理器信息 - 用于跟踪处理器性能和统计
/// </summary>
public class MessageHandlerInfo
{
    /// <summary>
    /// 主题模式
    /// </summary>
    public string TopicPattern { get; set; }
    
    /// <summary>
    /// 消息处理器
    /// </summary>
    public Func<MqttApplicationMessageReceivedEventArgs, Task> Handler { get; set; }
    
    /// <summary>
    /// 注册时间
    /// </summary>
    public DateTime RegisterTime { get; set; }
    
    /// <summary>
    /// 处理的消息数量
    /// </summary>
    public long MessageCount { get; set; }
    
    /// <summary>
    /// 最后处理时间
    /// </summary>
    public DateTime LastProcessTime { get; set; }
}

/// <summary>
/// 订阅信息 - 增强的订阅状态跟踪
/// </summary>
public class SubscriptionInfo
{
    /// <summary>
    /// 主题
    /// </summary>
    public string Topic { get; set; }
    
    /// <summary>
    /// QoS等级
    /// </summary>
    public int QoS { get; set; }
    
    /// <summary>
    /// 订阅时间
    /// </summary>
    public DateTime SubscribeTime { get; set; }
    
    /// <summary>
    /// 接收消息数量
    /// </summary>
    public long MessageCount { get; set; }
    
    /// <summary>
    /// 最后接收时间
    /// </summary>
    public DateTime LastMessageTime { get; set; }
    
    /// <summary>
    /// 是否活跃
    /// </summary>
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// MQTT连接统计信息 - 增强版本包含更多性能指标
/// </summary>
public class MqttConnectionStats
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; }

    /// <summary>
    /// 服务器地址
    /// </summary>
    public string ServerAddress { get; set; }

    /// <summary>
    /// 是否已连接
    /// </summary>
    public bool IsConnected { get; set; }

    /// <summary>
    /// 重连次数
    /// </summary>
    public int ReconnectAttempts { get; set; }

    /// <summary>
    /// 订阅数量
    /// </summary>
    public int SubscriptionCount { get; set; }

    /// <summary>
    /// 已发送消息数
    /// </summary>
    public int MessagesSent { get; set; }

    /// <summary>
    /// 已接收消息数
    /// </summary>
    public int MessagesReceived { get; set; }

    /// <summary>
    /// 最后心跳时间
    /// </summary>
    public DateTime LastHeartbeat { get; set; }
    
    /// <summary>
    /// 连接时间
    /// </summary>
    public DateTime ConnectionTime { get; set; }
    
    /// <summary>
    /// 消息处理器数量
    /// </summary>
    public int HandlerCount { get; set; }
    
    /// <summary>
    /// 传输字节总数
    /// </summary>
    public long BytesTransferred { get; set; }
    
    /// <summary>
    /// 连接运行时间
    /// </summary>
    public TimeSpan Uptime => IsConnected ? DateTime.UtcNow - ConnectionTime : TimeSpan.Zero;
    
    /// <summary>
    /// 平均消息处理速率（消息/秒）
    /// </summary>
    public double MessageRate => Uptime.TotalSeconds > 0 ? MessagesReceived / Uptime.TotalSeconds : 0;
    
    /// <summary>
    /// 内存使用量（字节）
    /// </summary>
    public long MemoryUsage { get; set; }
}

#endregion