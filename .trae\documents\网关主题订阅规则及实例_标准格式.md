# 网关主题订阅规则及实例

## 1. 主题规则概览

### 1.1 订阅流向架构

#### 1.1.1 消息流向图

```
云端平台 ←→ 厂家网关 ←→ 智能设备
    ↑           ↑           ↑
订阅指令     转发路由     事件上报
下发控制     消息处理     状态反馈
```

#### 1.1.2 订阅流向说明

**下行流向（云端→设备）**

1. **云端平台** → **厂家网关订阅**

   * 主题：`/productKey/deviceName/user/get`

   * 流向：云端通过MQTT向厂家网关发送控制指令

   * 用途：设备控制、参数配置、状态查询

2. **厂家网关** → **智能设备**

   * 协议：厂家私有协议（如Zigbee、LoRa、WiFi等）

   * 流向：网关将云端指令转换为设备可理解的协议格式

   * 处理：指令解析、协议转换、设备寻址

**上行流向（设备→云端）**

1. **智能设备** → **厂家网关**

   * 协议：厂家私有协议

   * 流向：设备主动上报或被动响应网关查询

   * 内容：状态数据、传感器数据、告警信息

2. **厂家网关发布** → **云端平台**

   * 主题：`/sys/productKey/deviceName/thing/event/{eventType}/post`

   * 流向：网关通过MQTT向云端发布设备事件

   * 类型：11种事件类型（beacon、setting、sensor等）

#### 1.1.3 订阅关系映射

| **订阅方**  | **发布方**  | **主题模式**                                        | **数据流向**     |
| -------- | -------- | ----------------------------------------------- | ------------ |
| **厂家网关** | **云端平台** | `/productKey/deviceName/user/get`               | 云端 → 网关 → 设备 |
| **云端平台** | **厂家网关** | `/sys/productKey/deviceName/thing/event/+/post` | 设备 → 网关 → 云端 |

#### 1.1.4 流向控制机制

**订阅控制**

* **QoS等级**：0（最多一次）、1（至少一次）、2（恰好一次）

* **消息持久化**：支持离线消息存储和重发

* **订阅权限**：基于productKey和deviceName的访问控制

**发布控制**

* **事件分类**：主动上报（heartbeat、trigger）vs 被动轮询（beacon、setting等）

* **频率控制**：心跳30秒、能耗30分钟、照明参数1小时等

* **数据压缩**：支持JSON数据的压缩传输

### 1.2 基础规则表

# 网关主题订阅规则及实例

# 网关主题订阅规则及实例

## 1. 主题规则概览

### 1.1 基础规则表

| **类型**     | **主题**                                                    | **备注**         |
| ---------- | --------------------------------------------------------- | -------------- |
| **厂家网关订阅** | `/productKey/deviceName/user/get`                         | -              |
| **厂家网关发布** | `/sys/productKey/deviceName/thing/event/beacon/post`      | *广播消息*         |
| -          | `/sys/productKey/deviceName/thing/event/setting/post`     | *照明参数*         |
| -          | `/sys/productKey/deviceName/thing/event/sensor/post`      | *传感器配置*        |
| -          | `/sys/productKey/deviceName/thing/event/dimming/post`     | *调光参数*         |
| -          | `/sys/productKey/deviceName/thing/event/network/post`     | *mesh组网参数*     |
| -          | `/sys/productKey/deviceName/thing/event/irc/post`         | *红外遥控器配置*      |
| -          | `/sys/productKey/deviceName/thing/event/consumption/post` | *能耗信息*         |
| -          | `/sys/productKey/deviceName/thing/event/trigger/post`     | *感应消息*         |
| -          | `/sys/productKey/deviceName/thing/event/heartbeat/post`   | *心跳消息*         |
| -          | `/sys/productKey/deviceName/thing/event/scene/post`       | *获取情景模式里的照明参数* |
| -          | `/sys/productKey/deviceName/thing/event/current/post`     | *实时亮度、色温*      |

### 1.2 地址类型特别说明

**重要提示**：**area**即区是**必填项**，**address**是地址，共有**label**、**cluster**、**number**三种类型

#### 1.2.1 控制类型编码

* **code=100时**：address=number(**单灯**)

* **code=200时**：address=cluster(**群组**)

* **code=300时**：address=label(**标签**)

* **code=400时**：address=area，**全区控制**

## 2. 指令详细说明

### 2.1 不带参数指令

#### 2.1.1 设备扫描类指令

##### scan

* **主题**：`/productKey/deviceName/user/get`

* **报文示例**：

```json
{
  "code": 200,
  "deviceName": "guangzhouminrui@@@18C8E716B349",
  "area": "00 00",
  "address": "00 00",
  "action": "scan",
  "params": "",
  "identity": ""
}
```

* **参数说明**：

  * **code**：200,群体扫描

  * **deviceName**：网关名称

  * **area**：区域

  * **address**：网关地址（务必16进制数字）

  * **action**：动作指令

  * **params**：动作指令参数

  * **identity**：默认空

* **指令说明**：*扫描设备*

* **备注**：设备在5秒内随机延时，上传自身唯一号信息

##### stopScan

* **报文示例**：

```json
{
  "code": 200,
  "deviceName": "guangzhouminrui@@@18C8E716B349",
  "area": "00 00",
  "address": "00 00",
  "action": "stopScan",
  "params": "",
  "identity": ""
}
```

* **指令说明**：*停止扫描设备*

* **备注**：停止发送自身唯一号，建议执行scan之后务必再执行stopScan

#### 2.1.2 设备状态获取指令

##### getSetting

* **报文示例**：

```json
{
  "code": 200,
  "deviceName": "guangzhouminrui@@@18C8E716B349",
  "area": "00 00",
  "address": "00 00",
  "action": "getSetting",
  "params": "",
  "identity": ""
}
```

* **参数说明**：

  * **code**：200,群体；100，单机

  * **deviceName**：网关名称

  * **area**：区域

  * **address**：网关地址（务必16进制数字）

    * code为200时，地址为00 00

    * 若code为100时，指定具体网关地址

  * **action**：动作指令

  * **params**：动作指令参数

  * **identity**：默认空

* **指令说明**：*灯具指令，获取设备照明参数*

##### getCurrent

* **报文示例**：

```json
{
  "code": 200,
  "deviceName": "guangzhouminrui@@@18C8E716B349",
  "area": "00 00",
  "address": "00 00",
  "action": "getCurrent",
  "params": "",
  "identity": ""
}
```

* **指令说明**：通过getCurrent轮询获取实时亮度上报（用来表征设备状态）

##### getSensor

* **报文示例**：

```json
{
  "code": 100,
  "deviceName": "guangzhouminrui@@@18C8E716B349",
  "area": "00 00",
  "address": "61 47",
  "action": "getSensor",
  "params": "",
  "identity": ""
}
```

* **指令说明**：*灯具指令，获取设备传感器配置*

##### getDimming

* **报文示例**：

```json
{
  "code": 100,
  "deviceName": "guangzhouminrui@@@18C8E716B349",
  "area": "00 00",
  "address": "17 4A",
  "action": "getDimming",
  "params": "",
  "identity": ""
}
```

* **指令说明**：*灯具指令，获取设备调光参数*

##### getNetwork

* **报文示例**：

```json
{
  "code": 200,
  "deviceName": "guangzhouminrui@@@18C8E716B349",
  "area": "00 00",
  "address": "00 00",
  "action": "getNetwork",
  "params": "",
  "identity": ""
}
```

* **指令说明**：*灯具指令，获取设备MESH网络参数*

##### getIrc

* **报文示例**：

```json
{
  "code": 100,
  "deviceName": "guangzhouminrui@@@18C8E716B349",
  "area": "00 00",
  "address": "14 4A",
  "action": "getIrc",
  "params": "",
  "identity": ""
}
```

* **指令说明**：*灯具指令，获取设备遥控器状态和配置*

##### getConsumption

* **报文示例**：

```json
{
  "code": 200,
  "deviceName": "guangzhouminrui@@@18C8E716B349",
  "area": "00 00",
  "address": "00 00",
  "action": "getConsumption",
  "params": "",
  "identity": ""
}
```

* **指令说明**：*灯具指令，获取设备能耗*

#### 2.1.3 设备控制指令

##### blink

* **报文示例**：

```json
{
 "code": 100,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "blink",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，让设备闪一闪*

* **备注**：默认闪60下（约1分钟）

##### stopBlink

* **报文示例**：

```json
{
 "code": 100,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "stopBlink",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，让设备停止闪*

##### lightOn

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "lightOn",
 "params": "",
 "identity": ""
}
```

* **报文示例说明**：code=200，代表群组控制，指令含义为 1区3组的灯全部常亮，执行有人亮度

* **指令说明**：*灯具指令，让灯具进入常亮模式*

* **备注**：灯具执行有人亮度

##### lightOff

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "lightOff",
 "params": "",
 "identity": ""
}
```

* **报文示例说明**：code=200，代表群组控制，指令含义为 1区3组的灯全部常灭，亮度为0

* **指令说明**：*灯具指令，让灯具进入常灭模式*

##### lightSleep

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "lightSleep",
 "params": "",
 "identity": ""
}
```

* **报文示例说明**：code=200，代表群组控制，指令含义为 1区3组的灯全部休眠，执行无人亮度，无人亮度可以设置为0

* **指令说明**：*灯具指令，让灯具进入休眠模式*

* **备注**：灯具执行无人亮度

##### ssrControl

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "ssrControl",
 "params": "",
 "identity": ""
}
```

* **报文示例说明**：code=200，代表群组控制，指令含义为 1区3组的灯全部感应控制，执行人来有人亮度、人离开无人亮度

* **指令说明**：*灯具指令，让灯具进入感应模式*

#### 2.1.4 传感器控制指令

##### ssrOn

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "ssrOn",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，传感器开*

* **备注**：开启灯上的传感器

##### ssrOff

* **主题**：`/productKey/deviceName/user/get`

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "ssrOff",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，传感器关*

* **备注**：关闭灯上的传感器

#### 2.1.5 网络控制指令

##### netOn

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "netOn",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，组网开*

* **备注**：开启后同一群组内的灯进入MESH状态，灯和灯之间相互通信

##### netOff

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "netOff",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，组网关*

* **备注**：脱离MESH状态，灯和灯之间不通信

##### relayOn

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "relayOn",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，转发开*

* **备注**：开启后灯具进入手拉手接力传输状态

##### relayOff

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "relayOff",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，转发关*

* **备注**：关闭接力传输

#### 2.1.6 红外遥控器指令

##### ircOn

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "ircOn",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，红外遥控器开*

* **备注**：开启后可以使用红外遥控器控灯

##### ircOff

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "ircOff",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，红外遥控器关*

* **备注**：关闭红外遥控器控灯

#### 2.1.7 标签和邻组指令

##### getLabel

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "getLabel",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，获取标签地址*

##### getNeighbour

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "getNeighbour",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，获取邻组通知*

##### labelEnable

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "labelEnable",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，标签有效*

* **备注**：使用标签

##### labelDisable

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "labelDisable",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，标签功能关闭*

* **备注**：关闭标签功能

##### neighbourEnable

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "neighbourEnable",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，邻组通知有效*

* **备注**：邻组通知功能开启

##### neighbourDisable

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "neighbourDisable",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，邻组通知功能关闭*

* **备注**：邻组通知功能关闭

#### 2.1.8 自动上报指令

##### reportSetting

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "reportSetting",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，自动上报照明参数*

* **备注**：默认关闭，开启后1小时上报一次，间隔时间可定义

##### reportSettingAck

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "reportSettingAck",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，自动上报照明参数*

* **备注**：查询用，间隔10秒钟上报一次，上报3次

##### reportSettingStop

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "reportSettingStop",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，停止上报照明参数*

##### reportConsumption

* **主题**：`/productKey/deviceName/user/get`

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "reportConsumption",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，自动上报能耗*

* **备注**：默认关闭，开启后30分钟上报一次，间隔时间可定义

##### reportConsumptionAck

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "reportConsumptionAck",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，自动上报能耗*

* **备注**：查询用，间隔10秒钟上报一次，上报3次

##### reportConsumptionStop

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "reportConsumptionStop",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，停止上报能耗*

##### reportLabel

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "reportLabel",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，自动上报标签*

* **备注**：默认关闭，开启后1小时上报一次，间隔时间可定义

##### reportLabelStop

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "reportLabelStop",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，停止上报标签*

##### reportNeighbour

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "reportNeighbour",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，自动上报邻组*

* **备注**：默认关闭，开启后1小时上报一次，间隔时间可定义

##### reportNeighbourStop

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "reportNeighbourStop",
 "params": "",
 "identity": ""
}
```

* **指令说明**：*灯具指令，停止上报邻组*

### 2.2 带参数指令

#### 2.2.1 模式设置指令

##### setLightMode

* **参数范围**：常亮、常灭、感应、休眠

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "setLightMode",
 "params": "常亮",
 "identity": ""
}
```

* **指令说明**：*灯具指令，设置灯具亮灯模式（常亮、常灭、感应）*

* **备注**：Always\_Brt Always\_Off Sensor\_Control

##### setDelayMode

* **参数范围**：一段、二段

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "setDelayMode",
 "params": "一段",
 "identity": ""
}
```

* **指令说明**：*灯具指令，设置感应模式（一段、二段）*

* **备注**：PhaseI PhaseII

##### setAlsMode

* **参数范围**：无效、自控、被控

* **报文示例**：

```json
[{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "setAlsMode",
 "params": "无",
 "identity": ""
}, {
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "setAlsMode",
 "params": "自控",
 "identity": ""
}, {
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "setAlsMode",
 "params": "被控",
 "identity": ""
}]
```

* **报文示例说明**：

  * code=200，代表群组控制，指令含义为 1区3组的灯全部不受恒照度控制

  * code=200，代表群组控制，指令含义为 1区3组的灯全部由自身的恒照度传感器调节亮度（需要自身硬件支持）

  * code=200，代表群组控制，指令含义为 1区3组的灯全部由绑定的恒照度传感器调节亮度

* **指令说明**：*灯具指令，设置恒照度模式（无效、自控、被控）*

* **备注**：None Inner Ext

#### 2.2.2 情景模式指令

##### callScene

* **参数范围**：1 \~ 31

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "callScene",
 "params": "1",
 "identity": ""
}
```

* **报文示例说明**：code=200，代表群组控制，指令含义为 1区3组的灯全部进入到模式一

* **指令说明**：*灯具指令，切换情景模式*

##### readScene

* **参数范围**：1 \~ 31

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "readScene",
 "params": "1",
 "identity": ""
}
```

* **报文示例说明**：code=200，代表群组控制，指令含义为读取 1区3组的灯的模式一的具体配置

* **指令说明**：*灯具指令，读取某一个情景模式的配置*

#### 2.2.3 亮度和色温设置指令

##### setHighBright

* **参数范围**：1 \~ 100

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "setHighBright",
 "params": "90",
 "identity": ""
}
```

* **报文示例说明**：code=200，代表群组控制，指令含义为 1区3组的灯有人亮度设置成90%

* **指令说明**：*灯具指令，单位%，设置有人亮度*

##### setStandbyBright

* **参数范围**：0 \~ 100

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "setStandbyBright",
 "params": "20",
 "identity": ""
}
```

* **报文示例说明**：code=200，代表群组控制，指令含义为 1区3组的灯无人亮度设置成20%

* **指令说明**：*灯具指令，单位%，设置无人亮度*

##### setCctBright

* **参数范围**：0 \~ 100

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 03",
 "action": "setCctBright",
 "params": "70",
 "identity": ""
}
```

* **报文示例说明**：code=200，代表群组控制，指令含义为 1区3组的灯色温设置成70%

* **指令说明**：*灯具指令，单位%，设置色温*

* **备注**：0%是暖光约2700K，100%是冷光约6500K

#### 2.2.4 时间参数设置指令

##### setBrightRiseTime

* **主题**：`/productKey/deviceName/user/get`

* **参数范围**：0 \~ 9

* **指令说明**：*灯具指令，单位秒，设置亮度调高时间*

##### setBrightFallTime

* **参数范围**：0 \~ 9

* **指令说明**：*灯具指令，单位秒，设置亮度调低时间*

##### setCctRiseTime

* **参数范围**：0 \~ 9

* **指令说明**：*灯具指令，单位秒，设置色温调高时间*

##### setCctFallTime

* **参数范围**：0 \~ 9

* **指令说明**：*灯具指令，单位秒，设置色温调低时间*

##### setDelayTime

* **参数范围**：0 \~ 1800

* **指令说明**：*灯具指令，单位秒，设置第一段延时时间*

* **备注**：有人亮度保持的时间

##### setDelayTime2

* **参数范围**：0 \~ 1800

* **指令说明**：*灯具指令，单位秒，设置第二段延时时间*

* **备注**：无人亮度保持的时间

##### setSensorInterval

* **参数范围**：0 \~ 60

* **指令说明**：*灯具指令，设置传感器触发间隔*

##### setNetTtl

* **参数范围**：0 \~ 20

* **指令说明**：*灯具指令，设置网跳数*

##### setGroupTtl

* **参数范围**：0 \~ 20

* **指令说明**：*灯具指令，设置组跳数*

##### setTxTimes

* **参数范围**：0 \~ 10

* **指令说明**：*灯具指令，设置重发次数*

#### 2.2.5 地址设置指令

##### setNumberAddress

* **参数范围**：UUID\_新的编号

* **报文示例**：

```json
{
 "code": 100,
 "deviceName": "guangzhouminrui@@@18C8E716B349",
 "area": "00 00",
 "address": "3A 47",
 "action": "setNumberAddress",
 "params": "42 D5 05 47 00 9E_1F 57",
 "identity": ""
}
```

* **报文示例说明**：code=100，代表单灯控制，指令含义为 0区14919号的灯修改为8023号（0x1F57）。params参数：uuid\_新的编号

* **指令说明**：*灯具指令，设置编号*

##### setClusterAddress

* **参数范围**：新的组号

* **报文示例**：

```json
{
 "code": 200,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 02",
 "action": "setClusterAddress",
 "params": "00 08",
 "identity": ""
}
```

* **报文示例说明**：code=200，代表群组控制，指令含义为 1区2组的灯修改为8组

* **指令说明**：*灯具指令，设置组号*

##### setAreaAddress

* **参数范围**：新的区号

* **报文示例**：

```json
{
 "code": 100,
 "deviceName": "lampNode",
 "area": "00 01",
 "address": "00 01",
 "action": "setAreaAddress",
 "params": "00 08",
 "identity": ""
}
```

* **报文示例说明**：code=100，代表单灯控制，指令含义为 1区1号的灯修改为8区

* **指令说明**：*灯具指令，设置区号*

##### setLabel

* **参数范围**：标签名

* **指令说明**：*灯具指令，设置标签*

##### setNeighbour

* **参数范围**：组号

* **指令说明**：*灯具指令，设置邻组通知*

##### setScene

* **参数范围**：内容较长，见后续专页

* **指令说明**：*灯具指令，配置照明参数到模式x*

* **参数说明**：

  * **SceneNo**：代表需要设置到哪个模式号上

  * **HighBright**：代表有人亮度

  * **StandbyBright**：代表有人亮度

  * **CctBright**：代表色温

  * **DelayTime**：代表一段延时时长

  * **DelayTime2**：代表二段延时时长

  * **AlsControlMode**：代表恒照度模式

  * **DelayMode**：代表感应模式

  * **LightMode**：代表亮灯模式

##### savetoScene

* **主题**：`/productKey/deviceName/user/get`

* **参数范围**：1 \~ 31

* **指令说明**：*灯具指令，把当前照明参数存储到模式x*

##### deleteLabel

* **参数范围**：标签名

* **指令说明**：*灯具指令，删除标签*

##### clearLabel

* **参数范围**：参数为空

* **指令说明**：*灯具指令，清空标签*

##### deleteNeighbour

* **参数范围**：组号

* **指令说明**：*灯具指令，删除邻组通知*

##### clearNeighbour

* **参数范围**：参数为空

* **指令说明**：*灯具指令，清空邻组通知*

## 3. 事件上报数据格式

### 3.1 主动上报事件

#### 3.1.1 heartbeat（心跳消息）

* **类型**：厂家网关发布

* **数据分类**：主动上报

* **主题**：`/sys/guangzhouminrui/guangzhouminrui@@@18C8E716B349/thing/event/heartbeat/post`

* **示例**：

```json
{
  "id": "1756103727373",
  "method": "thing.event.heartbeat.post",
  "params": {
    "time": 1756103727373,
    "value": {
      "updated_time": "1756103727373",
      "device_name": "guangzhouminrui@@@18C8E716B349",
      "area": "00 00",
      "cluster": "C0 00",
      "number": "3A 47",
      "uuid": "42 D5 05 47 00 9E",
      "light_mode": "Sensor_Control",
      "current_bright": "0%",
      "current_cct": "0%"
    }
  },
  "version": "1.0"
}
```

* **字段说明**：

  * **area**：区，不同区相互间不通信

  * **number**：号，一个区内号唯一，同一个区内可通信

  * **uuid**：唯一号，全网唯一，用来做key关键字

  * **device\_name**：网关名称，标明数据来源

  * **updated\_time**：心跳上报时间，Unix时间戳，毫秒

  * **cluster**：感应区域

* **流向**：设备→网关→云端

#### 3.1.2 trigger（感应消息）

* **主题**：`/sys/guangzhouminrui/guangzhouminrui@@@18C8E716B349/thing/event/trigger/post`

* **示例**：

```json
{
  "id": "1756103610469",
  "method": "thing.event.trigger.post",
  "params": {
    "time": 1756103610469,
    "value": {
      "trig_time": "1756103610469",
      "device_name": "guangzhouminrui@@@18C8E716B349",
      "area": "00 00",
      "number": "4E D8",
      "uuid": "D1 FD 07 BE 00 13"
    }
  },
  "version": "1.0"
}
```

* **字段说明**：

  * **area**：区，不同区相互间不通信

  * **number**：号，一个区内号唯一，同一个区内可通信

  * **uuid**：唯一号，全网唯一，用来做key关键字

  * **device\_name**：网关名称，标明数据来源

  * **trig\_time**：传感器触发时间，Unix时间戳，毫秒

### 3.2 被动轮询上报事件

#### 3.2.1 beacon（广播消息）

* **主题**：`/sys/productKey/deviceName/thing/event/beacon/post`

* **示例**：

```json
{
  "id": "1756110419700",
  "method": "thing.event.beacon.post",
  "params": {
    "time": 1756110419700,
    "value": {
      "report_time": "1756110419700",
      "device_name": "guangzhouminrui@@@18C8E716B349",
      "area": "00 00",
      "cluster": "C0 00",
      "number": "0B 49",
      "uuid": "2B 90 18 E9 06 12",
      "type": "灯具",
      "version": "v5.11.1"
    }
  },
  "version": "1.0"
}
```

* **字段说明**：

  * **area**：区，不同区相互间不通信

  * **number**：号，一个区内号唯一，同一个区内可通信

  * **cluster**：组，联动的最小单位

  * **uuid**：唯一号，全网唯一，用来做key关键字

  * **version**：固件版本

  * **device\_name**：网关名称，标明数据来源

  * **type**：类型\[灯、面板、恒照度、独立传感器、电机模组...]

* **触发方式**：scan => beacon

#### 3.2.2 setting（照明参数）

* **主题**：`/sys/productKey/deviceName/thing/event/setting/post`

* **示例**：

```json
{
  "id": "1756114894441",
  "method": "thing.event.setting.post",
  "params": {
    "time": 1756114894441,
    "value": {
      "updated_time": "1756114894441",
      "device_name": "guangzhouminrui@@@18C8E716B349",
      "area": "00 00",
      "number": "1F 57",
      "uuid": "42 D5 05 47 00 9E",
      "alscontrol_mode": "Inner",
      "light_mode": "Sensor_Control",
      "cct_bright": "100%",
      "delay_mode": "PhaseI",
      "delay_time": "10s",
      "delay_time2": "10s",
      "scene_validity": "Valid",
      "high_bright": "100%",
      "scene_no": "1",
      "standby_bright": "1.6%"
    }
  },
  "version": "1.0"
}
```

* **字段说明**：

  * **area**：区，不同区相互间不通信

  * **number**：号，一个区内号唯一，同一个区内可通信

  * **uuid**：唯一号，全网唯一，用来做key关键字

  * **device\_name**：网关名称，标明数据来源

  * **scene\_no**：当前情景号

  * **high\_bright**：有人亮度，最高100%，最低1%

  * **standby\_bright**：无人亮度，最高100%，最低0%

  * **cct\_bright**：色温，最高100%，最低0%

  * **delay\_time**：一段延时

  * **delay\_time2**：二段延时

  * **light\_mode**：亮度模式\[常亮(Always Brt)、常灭(Always Off)、感应(Sensor Control)]

  * **delay\_mode**：感应模式\[一段(PhaseI)、二段(PhaseII)]

  * **alscontrol\_mode**：恒照度模式\[无效(None)、自控(Inner)、被控(Ext)]

* **触发方式**：getSetting => setting

#### 3.2.3 sensor（传感器配置）

* **主题**：`/sys/productKey/deviceName/thing/event/sensor/post`

* **示例**：

```json
{
  "id": "1756110244004",
  "method": "thing.event.sensor.post",
  "params": {
    "time": 1756110244004,
    "value": {
      "updated_time": "1756110244004",
      "device_name": "guangzhouminrui@@@18C8E716B349",
      "area": "00 00",
      "number": "14 4A",
      "uuid": "42 D5 08 29 00 96",
      "sensor_status": "On",
      "sensor_sync": "On",
      "sensor_interval": "1s"
    }
  },
  "version": "1.0"
}
```

* **字段说明**：

  * **device\_name**：网关名称，标明数据来源

  * **area**：区，不同区相互间不通信

  * **number**：号，一个区内号唯一，同一个区内可通信

  * **uuid**：唯一号，全网唯一，用来做key关键字

  * **sensor\_status**：传感器状态\[开（On）、关（Off）]

  * **sensor\_sync**：组网状态\[开（On）、关（Off）]

  * **sensor\_interval**：感应触发间隔，单位秒，即感应状态下多久发送一次数据

* **触发方式**：getSensor => sensor

#### 3.2.4 dimming（调光参数）

* **主题**：`/sys/productKey/deviceName/thing/event/dimming/post`

* **示例**：

```json
{
  "id": "1756110605493",
  "method": "thing.event.dimming.post",
  "params": {
    "time": 1756110605493,
    "value": {
      "updated_time": "1756110605493",
      "device_name": "guangzhouminrui@@@18C8E716B349",
      "area": "00 00",
      "number": "17 4A",
      "uuid": "42 D5 08 14 00 2E",
      "bright_falltime": "1s",
      "bright_risetime": "1s",
      "cct_falltime": "1s",
      "cct_risetime": "1s"
    }
  },
  "version": "1.0"
}
```

* **字段说明**：

  * **area**：区，不同区相互间不通信

  * **number**：号，一个区内号唯一，同一个区内可通信

  * **uuid**：唯一号，全网唯一，用来做key关键字

  * **device\_name**：网关名称，标明数据来源

  * **bright\_risetime**：亮灯速度，单位秒

  * **bright\_falltime**：灭灯速度，单位秒

  * **cct\_risetime**：色温增加速度，单位秒

  * **cct\_falltime**：色温降低速度，单位秒

* **触发方式**：getDimming => dimming

#### 3.2.5 network（mesh组网参数）

* **主题**：`/sys/productKey/deviceName/thing/event/network/post`

* **示例**：

```json
{
  "id": "1756110692450",
  "method": "thing.event.network.post",
  "params": {
    "time": 1756110692450,
    "value": {
      "updated_time": "1756110692450",
      "device_name": "guangzhouminrui@@@18C8E716B349",
      "area": "00 00",
      "number": "17 4A",
      "uuid": "42 D5 08 14 00 2E",
      "group_ttl": "3",
      "nwk_ttl": "3",
      "node_type": "RelayOn",
      "tx_times": "3次"
    }
  },
  "version": "1.0"
}
```

* **字段说明**：

  * **area**：区，不同区相互间不通信

  * **number**：号，一个区内号唯一，同一个区内可通信

  * **uuid**：唯一号，全网唯一，用来做key关键字

  * **device\_name**：网关名称，标明数据来源

  * **node\_type**：mesh开关\[RelayOn，RelayOff]

  * **group\_ttl**：组跳数

  * **nwk\_ttl**：网跳数

  * **tx\_times**：重发次数

#### 3.2.6 irc（红外遥控器配置）

* **主题**：`/sys/productKey/deviceName/thing/event/irc/post`

* **示例**：

```json
{
  "id": "1756110912849",
  "method": "thing.event.irc.post",
  "params": {
    "time": 1756110912849,
    "value": {
      "updated_time": "1756110912849",
      "device_name": "guangzhouminrui@@@18C8E716B349",
      "area": "00 00",
      "number": "14 4A",
      "uuid": "42 D5 08 29 00 96",
      "irc_status": "On"
    }
  },
  "version": "1.0"
}
```

* **字段说明**：

  * **area**：区，不同区相互间不通信

  * **number**：号，一个区内号唯一，同一个区内可通信

  * **uuid**：唯一号，全网唯一，用来做key关键字

  * **device\_name**：网关名称，标明数据来源

  * **irc\_status**：红外遥控器状态\[开（On）、关（Off）]

  * **irc\_company**：红外遥控器厂家码，暂不提供数据

  * **irc\_keyval**：红外遥控器键值，暂不提供数据

#### 3.2.7 consumption（能耗信息）

* **主题**：`/sys/productKey/deviceName/thing/event/consumption/post`

* **示例**：

```json
{
  "id": "1756111197274",
  "method": "thing.event.consumption.post",
  "params": {
    "time": 1756111197274,
    "value": {
      "acquisition_time": "1756111197274",
      "device_name": "guangzhouminrui@@@18C8E716B349",
      "area": "00 00",
      "number": "61 47",
      "uuid": "42 D5 05 68 00 24",
      "energy_dur": 17539,
      "lighting_dur": 251628,
      "power": 16,
      "sensor_dur": 3339,
      "time_dur": 255319
    }
  },
  "version": "1.0"
}
```

* **字段说明**：

  * **area**：区，不同区相互间不通信

  * **number**：号，一个区内号唯一，同一个区内可通信

  * **uuid**：唯一号，全网唯一，用来做key关键字

  * **device\_name**：网关名称，标明数据来源

  * **time\_dur**：通电时长，单位秒

  * **sensor\_dur**：感应时长，单位秒

  * **energy\_dur**：等效能耗（计时方式）

  * **power**：单灯功率

* **特别说明**：

  * 能耗计算公式为：**energy\_dur × power ÷ 3600 ÷ 1000**

  * **energy\_dur**为计时方式，是灯具的通电时长乘以了灯具亮度的百分比

#### 3.2.8 current（实时亮度、色温）

* **主题**：`/sys/productKey/deviceName/thing/event/current/post`

* **字段说明**：

  * **area**：区，不同区相互间不通信

  * **number**：号，一个区内号唯一，同一个区内可通信

  * **uuid**：唯一号，全网唯一，用来做key关键字

  * **device\_name**：网关名称，标明数据来源

  * **current\_bright**：当前亮度

  * **current\_cct**：当前色温

***

## 附录

### A. 参数对照表

| **中文参数** | **英文参数**        | **说明**  |
| -------- | --------------- | ------- |
| 常亮       | Always\_Brt     | 灯具亮灯模式  |
| 常灭       | Always\_Off     | 灯具亮灭模式  |
| 感应       | Sensor\_Control | 灯具感应模式  |
| 一段       | PhaseI          | 感应延时模式  |
| 二段       | PhaseII         | 感应延时模式  |
| 无效       | None            | 恒照度控制模式 |
| 自控       | Inner           | 恒照度控制模式 |
| 被控       | Ext             | 恒照度控制模式 |

### B. 地址编码规范

| **控制类型** | **Code值** | **Address类型** | **应用场景** |
| -------- | --------- | ------------- | -------- |
| 单灯控制     | 100       | number        | 精确控制单个设备 |
| 群组控制     | 200       | cluster       | 批量控制同组设备 |
| 标签控制     | 300       | label         | 按标签分类控制  |
| 全区控制     | 400       | area          | 区域级统一控制  |

### C. 主题路径规范

* **订阅主题格式**：`/productKey/deviceName/user/get`

* **发布主题格式**：`/sys/productKey/deviceName/thing/event/{eventType}/post`

* **事件类型**：beacon、setting、sensor、dimming、network、irc、consumption、trigger、heartbeat、scene、current

***

**文档版本**：v1.0\
**创建时间**：2025年8月26日\
**数据来源**：网关主题订阅规则及实例.pdf\
**格式标准**：严格遵循Markdown语法规范
