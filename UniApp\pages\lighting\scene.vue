<template>
  <view class="scene-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">情景模式</text>
      <view class="header-actions">
        <button class="btn-add" @click="showCreateModal">
          <text class="icon">➕</text>
          <text>创建场景</text>
        </button>
      </view>
    </view>

    <!-- 当前场景状态 -->
    <view class="current-scene">
      <view class="scene-status">
        <text class="status-label">当前场景</text>
        <text class="status-value">{{ currentScene?.name || '无' }}</text>
      </view>
      <view class="scene-info">
        <text class="info-text">{{ currentScene?.description || '暂无激活场景' }}</text>
        <text class="info-time" v-if="currentScene">激活时间：{{ formatTime(currentScene.activeTime) }}</text>
      </view>
      <button 
        v-if="currentScene" 
        class="btn-deactivate" 
        @click="deactivateScene"
      >
        <text>关闭场景</text>
      </button>
    </view>

    <!-- 场景分类标签 -->
    <view class="scene-categories">
      <view 
        v-for="category in categories" 
        :key="category.value"
        class="category-tab"
        :class="{ active: activeCategory === category.value }"
        @click="switchCategory(category.value)"
      >
        <text class="category-icon">{{ category.icon }}</text>
        <text class="category-label">{{ category.label }}</text>
        <text class="category-count">({{ getCategoryCount(category.value) }})</text>
      </view>
    </view>

    <!-- 场景列表 -->
    <view class="scene-list">
      <view 
        v-for="scene in filteredScenes" 
        :key="scene.id"
        class="scene-card"
        :class="{ active: currentScene?.id === scene.id }"
        @click="previewScene(scene)"
      >
        <!-- 场景封面 -->
        <view class="scene-cover">
          <image 
            :src="scene.coverImage" 
            class="cover-image"
            mode="aspectFill"
          />
          <view class="scene-overlay">
            <view class="scene-type" :class="getTypeClass(scene.type)">
              <text>{{ getTypeText(scene.type) }}</text>
            </view>
            <view class="scene-actions">
              <button 
                class="btn-action btn-apply" 
                @click.stop="applyScene(scene)"
                :disabled="currentScene?.id === scene.id"
              >
                <text class="action-icon">{{ currentScene?.id === scene.id ? '✓' : '▶' }}</text>
              </button>
              <button 
                class="btn-action btn-edit" 
                @click.stop="editScene(scene)"
                v-if="scene.type === 'custom'"
              >
                <text class="action-icon">✏</text>
              </button>
              <button 
                class="btn-action btn-delete" 
                @click.stop="deleteScene(scene)"
                v-if="scene.type === 'custom'"
              >
                <text class="action-icon">🗑</text>
              </button>
            </view>
          </view>
        </view>

        <!-- 场景信息 -->
        <view class="scene-info">
          <view class="scene-header">
            <text class="scene-name">{{ scene.name }}</text>
            <view class="scene-rating">
              <text class="rating-stars">{{ '★'.repeat(scene.rating) }}{{ '☆'.repeat(5 - scene.rating) }}</text>
            </view>
          </view>
          <text class="scene-description">{{ scene.description }}</text>
          
          <view class="scene-details">
            <view class="detail-item">
              <text class="detail-icon">💡</text>
              <text class="detail-text">{{ scene.deviceCount }}个设备</text>
            </view>
            <view class="detail-item">
              <text class="detail-icon">⚡</text>
              <text class="detail-text">{{ scene.powerConsumption }}W</text>
            </view>
            <view class="detail-item">
              <text class="detail-icon">🕒</text>
              <text class="detail-text">{{ scene.duration || '持续' }}</text>
            </view>
          </view>
          
          <view class="scene-tags">
            <text 
              v-for="tag in scene.tags" 
              :key="tag"
              class="scene-tag"
            >
              {{ tag }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-if="filteredScenes.length === 0" class="empty-state">
      <text class="empty-icon">🎭</text>
      <text class="empty-text">暂无{{ getCategoryLabel(activeCategory) }}场景</text>
      <button class="btn-create" @click="showCreateModal">
        <text>创建第一个场景</text>
      </button>
    </view>

    <!-- 场景预览弹窗 -->
    <uni-popup ref="previewModal" type="center">
      <view class="preview-modal" v-if="previewScene">
        <view class="modal-header">
          <text class="modal-title">场景预览</text>
          <button class="btn-close" @click="closePreviewModal">✕</button>
        </view>
        
        <view class="modal-content">
          <view class="preview-cover">
            <image 
              :src="selectedScene.coverImage" 
              class="preview-image"
              mode="aspectFill"
            />
          </view>
          
          <view class="preview-info">
            <text class="preview-name">{{ selectedScene.name }}</text>
            <text class="preview-description">{{ selectedScene.description }}</text>
            
            <view class="preview-stats">
              <view class="stat-item">
                <text class="stat-label">设备数量</text>
                <text class="stat-value">{{ selectedScene.deviceCount }}</text>
              </view>
              <view class="stat-item">
                <text class="stat-label">功耗</text>
                <text class="stat-value">{{ selectedScene.powerConsumption }}W</text>
              </view>
              <view class="stat-item">
                <text class="stat-label">使用次数</text>
                <text class="stat-value">{{ selectedScene.usageCount }}</text>
              </view>
            </view>
          </view>
          
          <view class="device-preview">
            <text class="section-title">设备配置</text>
            <view class="device-list">
              <view 
                v-for="device in selectedScene.devices" 
                :key="device.id"
                class="device-item"
              >
                <view class="device-info">
                  <text class="device-name">{{ device.name }}</text>
                  <text class="device-location">{{ device.location }}</text>
                </view>
                <view class="device-settings">
                  <text class="setting-label">亮度: {{ device.brightness }}%</text>
                  <text class="setting-label">色温: {{ device.colorTemp }}K</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <view class="modal-actions">
          <button class="btn-cancel" @click="closePreviewModal">取消</button>
          <button 
            class="btn-apply" 
            @click="confirmApplyScene"
            :disabled="currentScene?.id === selectedScene.id"
          >
            <text>{{ currentScene?.id === selectedScene.id ? '已激活' : '应用场景' }}</text>
          </button>
        </view>
      </view>
    </uni-popup>

    <!-- 创建/编辑场景弹窗 -->
    <uni-popup ref="createModal" type="center">
      <view class="create-modal">
        <view class="modal-header">
          <text class="modal-title">{{ isEditing ? '编辑场景' : '创建场景' }}</text>
          <button class="btn-close" @click="closeCreateModal">✕</button>
        </view>
        
        <view class="modal-content">
          <view class="form-section">
            <text class="section-title">基本信息</text>
            
            <view class="form-item">
              <text class="form-label">场景名称</text>
              <input 
                v-model="sceneForm.name" 
                class="form-input" 
                placeholder="请输入场景名称"
                maxlength="20"
              />
            </view>
            
            <view class="form-item">
              <text class="form-label">场景描述</text>
              <textarea 
                v-model="sceneForm.description" 
                class="form-textarea" 
                placeholder="请输入场景描述"
                maxlength="100"
              />
            </view>
            
            <view class="form-item">
              <text class="form-label">场景分类</text>
              <picker 
                :value="sceneForm.category" 
                :range="categories" 
                range-key="label"
                @change="updateCategory"
              >
                <view class="picker-text">{{ getCategoryLabel(sceneForm.category) }}</view>
              </picker>
            </view>
            
            <view class="form-item">
              <text class="form-label">场景封面</text>
              <view class="cover-selector">
                <view 
                  v-for="cover in coverOptions" 
                  :key="cover.id"
                  class="cover-option"
                  :class="{ selected: sceneForm.coverImage === cover.url }"
                  @click="selectCover(cover.url)"
                >
                  <image :src="cover.url" class="cover-thumb" mode="aspectFill" />
                </view>
              </view>
            </view>
          </view>
          
          <view class="form-section">
            <text class="section-title">MQTT配置</text>
            
            <view class="form-item">
              <text class="form-label">设备名称 <text class="required">*</text></text>
              <input 
                v-model="sceneForm.deviceName" 
                class="form-input" 
                placeholder="请输入设备节点名称"
                maxlength="50"
              />
            </view>
            
            <view class="form-item">
              <text class="form-label">区域编码 <text class="required">*</text></text>
              <input 
                v-model="sceneForm.area" 
                class="form-input" 
                placeholder="请输入2字节十六进制区域编码（如：0001）"
                maxlength="4"
              />
            </view>
            
            <view class="form-item">
              <text class="form-label">设备地址编码 <text class="required">*</text></text>
              <input 
                v-model="sceneForm.address" 
                class="form-input" 
                placeholder="请输入2字节十六进制地址编码（如：0001）"
                maxlength="4"
              />
            </view>
            
            <view class="form-item">
              <text class="form-label">情景ID <text class="required">*</text></text>
              <input 
                v-model.number="sceneForm.sceneId" 
                class="form-input" 
                type="number" 
                placeholder="请输入情景唯一标识"
              />
            </view>
            
            <view class="form-item">
              <text class="form-label">身份标识</text>
              <input 
                v-model="sceneForm.identity" 
                class="form-input" 
                placeholder="请输入操作者身份标识（可选）"
                maxlength="50"
              />
            </view>
          </view>
          
          <view class="form-section">
            <text class="section-title">灯具组配置</text>
            <button class="btn-add-lamp-group" @click="addLampGroup">
              <text>添加灯具组</text>
              <text class="icon">+</text>
            </button>
            
            <view class="lamp-groups">
              <view 
                v-for="(group, groupIndex) in sceneForm.lampGroups" 
                :key="groupIndex"
                class="lamp-group-config"
              >
                <view class="group-header">
                  <text class="group-title">灯具组 {{ groupIndex + 1 }}</text>
                  <button class="btn-remove" @click="removeLampGroup(groupIndex)">✕</button>
                </view>
                
                <view class="form-item">
                  <text class="form-label">灯具分组标识 <text class="required">*</text></text>
                  <input 
                    v-model="group.lampId" 
                    class="form-input" 
                    placeholder="请输入灯具分组标识（如：#4）"
                    maxlength="10"
                  />
                </view>
                
                <view class="params-section">
                  <text class="params-title">情景参数</text>
                  
                  <view class="config-item">
                    <text class="config-label">有人亮度：{{ group.params.occupiedBrightness }}% <text class="required">*</text></text>
                    <slider 
                      :value="group.params.occupiedBrightness" 
                      min="0" 
                      max="100" 
                      step="1"
                      activeColor="#1890ff"
                      @change="updateGroupParam(groupIndex, 'occupiedBrightness', $event)"
                    />
                  </view>
                  
                  <view class="config-item">
                    <text class="config-label">无人亮度：{{ group.params.unoccupiedBrightness }}% <text class="required">*</text></text>
                    <slider 
                      :value="group.params.unoccupiedBrightness" 
                      min="0" 
                      max="100" 
                      step="1"
                      activeColor="#1890ff"
                      @change="updateGroupParam(groupIndex, 'unoccupiedBrightness', $event)"
                    />
                  </view>
                  
                  <view class="config-item">
                    <text class="config-label">色温：{{ group.params.colorTemp }}%</text>
                    <slider 
                      :value="group.params.colorTemp" 
                      min="0" 
                      max="100" 
                      step="1"
                      activeColor="#1890ff"
                      @change="updateGroupParam(groupIndex, 'colorTemp', $event)"
                    />
                  </view>
                  
                  <view class="form-item">
                    <text class="form-label">一段延时（秒） <text class="required">*</text></text>
                    <input 
                      v-model.number="group.params.firstDelay" 
                      class="form-input" 
                      type="number" 
                      placeholder="请输入一段延时秒数"
                      min="0"
                    />
                  </view>
                  
                  <view class="form-item">
                    <text class="form-label">二段延时（秒）</text>
                    <input 
                      v-model.number="group.params.secondDelay" 
                      class="form-input" 
                      type="number" 
                      placeholder="请输入二段延时秒数"
                      min="0"
                    />
                  </view>
                  
                  <view class="form-item">
                    <text class="form-label">亮灯模式 <text class="required">*</text></text>
                    <picker 
                      :value="group.params.lightMode" 
                      :range="lightModeOptions" 
                      @change="updateGroupModeParam(groupIndex, 'lightMode', $event)"
                    >
                      <view class="picker-text">{{ group.params.lightMode || '请选择亮灯模式' }}</view>
                    </picker>
                  </view>
                  
                  <view class="form-item">
                    <text class="form-label">感应模式 <text class="required">*</text></text>
                    <picker 
                      :value="group.params.sensorMode" 
                      :range="sensorModeOptions" 
                      @change="updateGroupModeParam(groupIndex, 'sensorMode', $event)"
                    >
                      <view class="picker-text">{{ group.params.sensorMode || '请选择感应模式' }}</view>
                    </picker>
                  </view>
                  
                  <view class="form-item">
                    <text class="form-label">恒照度模式</text>
                    <picker 
                      :value="group.params.alsMode" 
                      :range="alsModeOptions" 
                      @change="updateGroupModeParam(groupIndex, 'alsMode', $event)"
                    >
                      <view class="picker-text">{{ group.params.alsMode || '请选择恒照度模式' }}</view>
                    </picker>
                  </view>
                </view>
              </view>
            </view>
          </view>
          
          <view class="form-section">
            <text class="section-title">传统设备配置</text>
            <button class="btn-select-devices" @click="showDeviceSelector">
              <text>已选择 {{ sceneForm.devices.length }} 个设备</text>
              <text class="icon">▶</text>
            </button>
            
            <view class="selected-devices">
              <view 
                v-for="device in sceneForm.devices" 
                :key="device.id"
                class="device-config"
              >
                <view class="device-header">
                  <text class="device-name">{{ device.name }}</text>
                  <button class="btn-remove" @click="removeDevice(device.id)">✕</button>
                </view>
                
                <view class="config-item">
                  <text class="config-label">亮度：{{ device.brightness }}%</text>
                  <slider 
                    :value="device.brightness" 
                    min="10" 
                    max="100" 
                    step="10"
                    activeColor="#1890ff"
                    @change="updateDeviceBrightness(device.id, $event)"
                  />
                </view>
                
                <view class="config-item">
                  <text class="config-label">色温：{{ device.colorTemp }}K</text>
                  <slider 
                    :value="device.colorTemp" 
                    min="2700" 
                    max="6500" 
                    step="100"
                    activeColor="#1890ff"
                    @change="updateDeviceColorTemp(device.id, $event)"
                  />
                </view>
              </view>
            </view>
          </view>
          
          <view class="form-section">
            <text class="section-title">高级设置</text>
            
            <view class="form-item">
              <text class="form-label">场景标签</text>
              <view class="tag-input">
                <input 
                  v-model="newTag" 
                  class="tag-input-field" 
                  placeholder="输入标签后按回车添加"
                  @confirm="addTag"
                />
                <button class="btn-add-tag" @click="addTag">添加</button>
              </view>
              <view class="tag-list">
                <view 
                  v-for="(tag, index) in sceneForm.tags" 
                  :key="index"
                  class="tag-item"
                >
                  <text>{{ tag }}</text>
                  <button class="btn-remove-tag" @click="removeTag(index)">✕</button>
                </view>
              </view>
            </view>
            
            <view class="form-item">
              <view class="form-switch">
                <text class="form-label">自动关闭</text>
                <input type="checkbox" v-model="sceneForm.autoOff" class="form-switch-input" />
              </view>
            </view>
            
            <view v-if="sceneForm.autoOff" class="form-item">
              <text class="form-label">自动关闭时间（分钟）</text>
              <input 
                v-model.number="sceneForm.autoOffDuration" 
                class="form-input" 
                type="number" 
                placeholder="请输入分钟数"
              />
            </view>
          </view>
        </view>
        
        <view class="modal-actions">
          <button class="btn-cancel" @click="closeCreateModal">取消</button>
          <button class="btn-save" @click="saveScene">{{ isEditing ? '保存' : '创建' }}</button>
        </view>
      </view>
    </uni-popup>

    <!-- 设备选择弹窗 -->
    <uni-popup ref="deviceModal" type="center">
      <view class="device-modal">
        <view class="modal-header">
          <text class="modal-title">选择设备</text>
          <button class="btn-close" @click="closeDeviceModal">✕</button>
        </view>
        
        <view class="modal-content">
          <view class="device-list">
            <label 
              v-for="device in availableDevices" 
              :key="device.id"
              class="device-checkbox"
            >
              <checkbox 
                :value="device.id" 
                :checked="isDeviceSelected(device.id)"
                @change="toggleDeviceSelection(device.id)"
              />
              <view class="device-info">
                <text class="device-name">{{ device.name }}</text>
                <text class="device-location">{{ device.location }}</text>
              </view>
              <view class="device-status" :class="getStatusClass(device.status)">
                <text>{{ getStatusText(device.status) }}</text>
              </view>
            </label>
          </view>
        </view>
        
        <view class="modal-actions">
          <button class="btn-cancel" @click="closeDeviceModal">取消</button>
          <button class="btn-confirm" @click="confirmDeviceSelection">确定</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'

export default {
  name: 'ScenePage',
  setup() {
    // 响应式数据
    const scenes = ref([])
    const availableDevices = ref([])
    const currentScene = ref(null)
    const selectedScene = ref(null)
    const activeCategory = ref('all')
    const isEditing = ref(false)
    const newTag = ref('')
    
    // 定时器变量
    const applyTimer = ref(null)
    
    const categories = [
      { value: 'all', label: '全部', icon: '🏠' },
      { value: 'work', label: '工作', icon: '💼' },
      { value: 'rest', label: '休息', icon: '🛋️' },
      { value: 'meeting', label: '会议', icon: '👥' },
      { value: 'energy', label: '节能', icon: '🌱' },
      { value: 'custom', label: '自定义', icon: '⚙️' }
    ]
    
    const coverOptions = [
      { id: 1, url: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=modern%20office%20lighting%20warm%20atmosphere&image_size=square' },
      { id: 2, url: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=cozy%20living%20room%20soft%20lighting&image_size=square' },
      { id: 3, url: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=conference%20room%20bright%20professional%20lighting&image_size=square' },
      { id: 4, url: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=energy%20saving%20dim%20lighting%20green%20theme&image_size=square' }
    ]
    
    // 表单数据
    const sceneForm = reactive({
      id: null,
      name: '',
      description: '',
      category: 'work',
      coverImage: coverOptions[0].url,
      devices: [],
      lampGroups: [],
      tags: [],
      autoOff: false,
      autoOffDuration: 30,
      deviceName: '',
      area: '',
      address: '',
      sceneId: null,
      identity: ''
    })

    // 灯具组配置选项
      const lightModeOptions = ['常亮', '感应', '定时']
      const sensorModeOptions = ['一段', '二段', '关闭']
      const alsModeOptions = ['有效', '无效']

    // 计算属性
    const filteredScenes = computed(() => {
      if (activeCategory.value === 'all') {
        return scenes.value
      }
      return scenes.value.filter(scene => scene.category === activeCategory.value)
    })

    // 方法
    const initData = () => {
      // 模拟场景数据
      scenes.value = [
        {
          id: 1,
          name: '专注工作',
          description: '明亮的白光，提高工作效率',
          category: 'work',
          type: 'preset',
          coverImage: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=bright%20office%20workspace%20focused%20lighting&image_size=square',
          deviceCount: 6,
          powerConsumption: 240,
          duration: '持续',
          rating: 5,
          usageCount: 45,
          tags: ['工作', '专注', '明亮'],
          devices: [
            { id: 1, name: '办公室主灯', location: '办公楼A座201', brightness: 90, colorTemp: 5000 },
            { id: 2, name: '台灯', location: '办公楼A座201', brightness: 80, colorTemp: 4500 }
          ]
        },
        {
          id: 2,
          name: '温馨休息',
          description: '温暖的暖光，营造舒适氛围',
          category: 'rest',
          type: 'preset',
          coverImage: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=cozy%20warm%20lighting%20relaxing%20atmosphere&image_size=square',
          deviceCount: 4,
          powerConsumption: 120,
          duration: '2小时',
          rating: 4,
          usageCount: 32,
          tags: ['休息', '温馨', '暖光'],
          devices: [
            { id: 3, name: '休息区吊灯', location: '休息区', brightness: 60, colorTemp: 3000 },
            { id: 4, name: '氛围灯', location: '休息区', brightness: 40, colorTemp: 2700 }
          ]
        },
        {
          id: 3,
          name: '会议模式',
          description: '均匀明亮的照明，适合会议讨论',
          category: 'meeting',
          type: 'preset',
          coverImage: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20meeting%20room%20bright%20even%20lighting&image_size=square',
          deviceCount: 8,
          powerConsumption: 320,
          duration: '持续',
          rating: 5,
          usageCount: 28,
          tags: ['会议', '专业', '明亮'],
          devices: [
            { id: 5, name: '会议室主灯', location: '会议室A', brightness: 95, colorTemp: 4000 },
            { id: 6, name: '投影区补光', location: '会议室A', brightness: 70, colorTemp: 4000 }
          ]
        },
        {
          id: 4,
          name: '节能模式',
          description: '降低亮度，节约能源消耗',
          category: 'energy',
          type: 'preset',
          coverImage: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=energy%20efficient%20dim%20green%20lighting&image_size=square',
          deviceCount: 10,
          powerConsumption: 150,
          duration: '持续',
          rating: 4,
          usageCount: 67,
          tags: ['节能', '环保', '省电'],
          devices: [
            { id: 7, name: '走廊灯', location: '走廊', brightness: 30, colorTemp: 3500 },
            { id: 8, name: '楼梯灯', location: '楼梯间', brightness: 25, colorTemp: 3500 }
          ]
        },
        {
          id: 5,
          name: '我的自定义',
          description: '个人定制的照明方案',
          category: 'custom',
          type: 'custom',
          coverImage: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=custom%20personalized%20lighting%20setup&image_size=square',
          deviceCount: 5,
          powerConsumption: 180,
          duration: '1小时',
          rating: 3,
          usageCount: 12,
          tags: ['自定义', '个性化'],
          devices: [
            { id: 9, name: '个人工位灯', location: '工位001', brightness: 75, colorTemp: 4200 }
          ]
        }
      ]
      
      // 模拟设备数据
      availableDevices.value = [
        { id: 1, name: '办公室主灯', location: '办公楼A座201', status: 'online' },
        { id: 2, name: '台灯', location: '办公楼A座201', status: 'online' },
        { id: 3, name: '休息区吊灯', location: '休息区', status: 'online' },
        { id: 4, name: '氛围灯', location: '休息区', status: 'offline' },
        { id: 5, name: '会议室主灯', location: '会议室A', status: 'online' },
        { id: 6, name: '投影区补光', location: '会议室A', status: 'online' },
        { id: 7, name: '走廊灯', location: '走廊', status: 'online' },
        { id: 8, name: '楼梯灯', location: '楼梯间', status: 'online' },
        { id: 9, name: '个人工位灯', location: '工位001', status: 'online' },
        { id: 10, name: '储物间灯', location: '储物间', status: 'offline' }
      ]
      
      // 设置当前激活场景
      currentScene.value = scenes.value[0]
      currentScene.value.activeTime = new Date().toISOString()
    }

    const getCategoryCount = (category) => {
      if (category === 'all') {
        return scenes.value.length
      }
      return scenes.value.filter(scene => scene.category === category).length
    }

    const getCategoryLabel = (category) => {
      return categories.find(cat => cat.value === category)?.label || '未知'
    }

    const switchCategory = (category) => {
      activeCategory.value = category
    }

    const getTypeClass = (type) => {
      return {
        'type-preset': type === 'preset',
        'type-custom': type === 'custom'
      }
    }

    const getTypeText = (type) => {
      return type === 'preset' ? '预设' : '自定义'
    }

    const formatTime = (timeStr) => {
      if (!timeStr) return ''
      const date = new Date(timeStr)
      return date.toLocaleString()
    }

    const getStatusClass = (status) => {
      return {
        'status-online': status === 'online',
        'status-offline': status === 'offline'
      }
    }

    const getStatusText = (status) => {
      return status === 'online' ? '在线' : '离线'
    }

    const previewScene = (scene) => {
      selectedScene.value = scene
      uni.$refs.previewModal.open()
    }

    const closePreviewModal = () => {
      uni.$refs.previewModal.close()
    }

    const applyScene = (scene) => {
      if (currentScene.value?.id === scene.id) {
        uni.showToast({ title: '场景已激活', icon: 'none' })
        return
      }
      
      // 清除之前的定时器
      if (applyTimer.value) {
        clearTimeout(applyTimer.value)
        applyTimer.value = null
      }
      
      uni.showLoading({ title: '应用中...' })
      applyTimer.value = setTimeout(() => {
        currentScene.value = { ...scene, activeTime: new Date().toISOString() }
        scene.usageCount++
        uni.hideLoading()
        uni.showToast({ title: '场景应用成功', icon: 'success' })
        applyTimer.value = null
      }, 1500)
    }

    const confirmApplyScene = () => {
      applyScene(selectedScene.value)
      closePreviewModal()
    }

    const deactivateScene = () => {
      uni.showModal({
        title: '确认关闭',
        content: '确定要关闭当前场景吗？',
        success: (res) => {
          if (res.confirm) {
            currentScene.value = null
            uni.showToast({ title: '场景已关闭', icon: 'success' })
          }
        }
      })
    }

    const deleteScene = (scene) => {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除场景"${scene.name}"吗？`,
        success: (res) => {
          if (res.confirm) {
            const index = scenes.value.findIndex(s => s.id === scene.id)
            if (index > -1) {
              scenes.value.splice(index, 1)
              if (currentScene.value?.id === scene.id) {
                currentScene.value = null
              }
              uni.showToast({ title: '删除成功', icon: 'success' })
            }
          }
        }
      })
    }

    // 创建/编辑场景相关方法
    const showCreateModal = () => {
      isEditing.value = false
      resetSceneForm()
      uni.$refs.createModal.open()
    }

    const editScene = (scene) => {
      isEditing.value = true
      Object.assign(sceneForm, {
        id: scene.id,
        name: scene.name,
        description: scene.description,
        category: scene.category,
        coverImage: scene.coverImage,
        devices: [...scene.devices],
        lampGroups: scene.lampGroups ? [...scene.lampGroups] : [],
        tags: [...scene.tags],
        autoOff: scene.duration !== '持续',
        autoOffDuration: scene.duration === '持续' ? 30 : parseInt(scene.duration)
      })
      uni.$refs.createModal.open()
    }

    // 灯具组管理方法
    const addLampGroup = () => {
      sceneForm.lampGroups.push({
        lampId: '',
        params: {
          occupiedBrightness: 80,
          unoccupiedBrightness: 20,
          colorTemp: 50,
          firstDelay: 60,
          secondDelay: 30,
          lightMode: '',
          sensorMode: '',
          alsMode: ''
        }
      })
    }

    const removeLampGroup = (index) => {
      sceneForm.lampGroups.splice(index, 1)
    }

    const updateGroupParam = (groupIndex, paramName, event) => {
      const value = event.detail ? event.detail.value : event
      sceneForm.lampGroups[groupIndex].params[paramName] = value
    }

    const updateGroupModeParam = (groupIndex, paramName, event) => {
      const index = event.detail ? event.detail.value : event
      const options = {
        lightMode: lightModeOptions,
        sensorMode: sensorModeOptions,
        alsMode: alsModeOptions
      }
      sceneForm.lampGroups[groupIndex].params[paramName] = options[paramName][index]
    }

    const resetSceneForm = () => {
      Object.assign(sceneForm, {
        id: null,
        name: '',
        description: '',
        category: 'work',
        coverImage: coverOptions[0].url,
        devices: [],
        lampGroups: [],
        tags: [],
        autoOff: false,
        autoOffDuration: 30,
        deviceName: '',
        area: '',
        address: '',
        sceneId: null,
        identity: ''
      })
    }

    const closeCreateModal = () => {
      uni.$refs.createModal.close()
    }

    const validateSceneForm = () => {
      // 基础参数校验
      if (!sceneForm.name.trim()) {
        uni.showToast({ title: '请输入场景名称', icon: 'error' })
        return false
      }
      
      // MQTT配置校验
      if (sceneForm.lampGroups.length > 0) {
        if (!sceneForm.deviceName.trim()) {
          uni.showToast({ title: '请输入设备名称', icon: 'error' })
          return false
        }
        
        if (!sceneForm.area.trim()) {
          uni.showToast({ title: '请输入区域编码', icon: 'error' })
          return false
        }
        
        if (!sceneForm.address.trim()) {
          uni.showToast({ title: '请输入地址编码', icon: 'error' })
          return false
        }
        
        if (!sceneForm.sceneId) {
          uni.showToast({ title: '请输入情景ID', icon: 'error' })
          return false
        }
        
        // 灯具组参数校验
        for (let i = 0; i < sceneForm.lampGroups.length; i++) {
          const group = sceneForm.lampGroups[i]
          
          if (!group.lampId.trim()) {
            uni.showToast({ title: `灯具组${i + 1}：请输入灯具分组标识`, icon: 'error' })
            return false
          }
          
          // 亮度范围校验 (0-100)
          if (group.params.occupiedBrightness < 0 || group.params.occupiedBrightness > 100) {
            uni.showToast({ title: `灯具组${i + 1}：有人亮度必须在0-100%之间`, icon: 'error' })
            return false
          }
          
          if (group.params.unoccupiedBrightness < 0 || group.params.unoccupiedBrightness > 100) {
            uni.showToast({ title: `灯具组${i + 1}：无人亮度必须在0-100%之间`, icon: 'error' })
            return false
          }
          
          if (group.params.colorTemp < 0 || group.params.colorTemp > 100) {
            uni.showToast({ title: `灯具组${i + 1}：色温必须在0-100%之间`, icon: 'error' })
            return false
          }
          
          // 时间参数校验 (≥0)
          if (group.params.firstDelay < 0) {
            uni.showToast({ title: `灯具组${i + 1}：一段延时必须≥0秒`, icon: 'error' })
            return false
          }
          
          if (group.params.secondDelay < 0) {
            uni.showToast({ title: `灯具组${i + 1}：二段延时必须≥0秒`, icon: 'error' })
            return false
          }
          
          // 必填模式参数校验
          if (!group.params.lightMode) {
            uni.showToast({ title: `灯具组${i + 1}：请选择亮灯模式`, icon: 'error' })
            return false
          }
          
          if (!group.params.sensorMode) {
            uni.showToast({ title: `灯具组${i + 1}：请选择感应模式`, icon: 'error' })
            return false
          }
        }
      } else if (sceneForm.devices.length === 0) {
        uni.showToast({ title: '请选择设备或配置灯具组', icon: 'error' })
        return false
      }
      
      return true
    }

    const saveScene = async () => {
      if (!validateSceneForm()) {
        return
      }
      
      const sceneData = {
        id: sceneForm.id || Date.now(),
        name: sceneForm.name,
        description: sceneForm.description,
        category: sceneForm.category,
        type: 'custom',
        coverImage: sceneForm.coverImage,
        deviceCount: sceneForm.devices.length + sceneForm.lampGroups.length,
        powerConsumption: sceneForm.devices.reduce((sum, device) => sum + (device.brightness * 4), 0),
        duration: sceneForm.autoOff ? `${sceneForm.autoOffDuration}分钟` : '持续',
        rating: 3,
        usageCount: 0,
        tags: [...sceneForm.tags],
        devices: [...sceneForm.devices],
        lampGroups: [...sceneForm.lampGroups],
        mqttConfig: {
          deviceName: sceneForm.deviceName,
          area: sceneForm.area,
          address: sceneForm.address,
          sceneId: sceneForm.sceneId,
          identity: sceneForm.identity
        }
      }
      
      // 如果配置了灯具组，发送MQTT消息
      if (sceneForm.lampGroups.length > 0) {
        try {
          const { getMqttClient } = await import('@/utils/mqtt.js')
          const mqttClient = getMqttClient()
          
          const sceneConfig = {
            deviceName: sceneForm.deviceName,
            area: sceneForm.area,
            address: sceneForm.address,
            lampGroups: sceneForm.lampGroups.map(group => ({
              lamps: [group.lampId],
              params: {
                '有人亮度': `${group.params.occupiedBrightness}%`,
                '无人亮度': `${group.params.unoccupiedBrightness}%`,
                '色温': `${group.params.colorTemp}%`,
                '一段延时': `${group.params.firstDelay}秒`,
                '二段延时': `${group.params.secondDelay}秒`,
                '亮灯模式': group.params.lightMode,
                '感应模式': group.params.sensorMode,
                '恒照度模式': group.params.alsMode || '无效'
              }
            })),
            sceneId: sceneForm.sceneId,
            sceneName: sceneForm.name,
            identity: sceneForm.identity || ''
          }
          
          await mqttClient.setScene(sceneConfig)
          uni.showToast({ title: 'MQTT消息发送成功', icon: 'success' })
        } catch (error) {
          console.error('MQTT消息发送失败:', error)
          uni.showToast({ title: 'MQTT消息发送失败', icon: 'error' })
        }
      }
      
      if (isEditing.value) {
        const index = scenes.value.findIndex(s => s.id === sceneForm.id)
        if (index > -1) {
          scenes.value[index] = { ...scenes.value[index], ...sceneData }
        }
        uni.showToast({ title: '保存成功', icon: 'success' })
      } else {
        scenes.value.push(sceneData)
        uni.showToast({ title: '创建成功', icon: 'success' })
      }
      
      closeCreateModal()
    }

    const updateCategory = (event) => {
      sceneForm.category = categories[event.detail.value].value
    }

    const selectCover = (url) => {
      sceneForm.coverImage = url
    }

    const addTag = () => {
      if (newTag.value.trim() && !sceneForm.tags.includes(newTag.value.trim())) {
        sceneForm.tags.push(newTag.value.trim())
        newTag.value = ''
      }
    }

    const removeTag = (index) => {
      sceneForm.tags.splice(index, 1)
    }

    // 设备选择相关方法
    const showDeviceSelector = () => {
      uni.$refs.deviceModal.open()
    }

    const closeDeviceModal = () => {
      uni.$refs.deviceModal.close()
    }

    const isDeviceSelected = (deviceId) => {
      return sceneForm.devices.some(device => device.id === deviceId)
    }

    const toggleDeviceSelection = (deviceId) => {
      const index = sceneForm.devices.findIndex(device => device.id === deviceId)
      if (index > -1) {
        sceneForm.devices.splice(index, 1)
      } else {
        const device = availableDevices.value.find(d => d.id === deviceId)
        if (device) {
          sceneForm.devices.push({
            id: device.id,
            name: device.name,
            location: device.location,
            brightness: 80,
            colorTemp: 4000
          })
        }
      }
    }

    const confirmDeviceSelection = () => {
      closeDeviceModal()
    }

    const removeDevice = (deviceId) => {
      const index = sceneForm.devices.findIndex(device => device.id === deviceId)
      if (index > -1) {
        sceneForm.devices.splice(index, 1)
      }
    }

    const updateDeviceBrightness = (deviceId, event) => {
      const device = sceneForm.devices.find(d => d.id === deviceId)
      if (device) {
        device.brightness = event.detail.value
      }
    }

    const updateDeviceColorTemp = (deviceId, event) => {
      const device = sceneForm.devices.find(d => d.id === deviceId)
      if (device) {
        device.colorTemp = event.detail.value
      }
    }

    // 生命周期
    onMounted(() => {
      initData()
    })
    
    onUnmounted(() => {
      // 清理定时器
      if (applyTimer.value) {
        clearTimeout(applyTimer.value)
        applyTimer.value = null
      }
    })

    return {
      scenes,
      availableDevices,
      currentScene,
      selectedScene,
      activeCategory,
      isEditing,
      newTag,
      categories,
      coverOptions,
      sceneForm,
      lightModeOptions,
      sensorModeOptions,
      alsModeOptions,
      filteredScenes,
      getCategoryCount,
      getCategoryLabel,
      switchCategory,
      getTypeClass,
      getTypeText,
      formatTime,
      getStatusClass,
      getStatusText,
      previewScene,
      closePreviewModal,
      applyScene,
      confirmApplyScene,
      deactivateScene,
      deleteScene,
      showCreateModal,
      editScene,
      addLampGroup,
      removeLampGroup,
      updateGroupParam,
      updateGroupModeParam,
      validateSceneForm,
      closeCreateModal,
      saveScene,
      updateCategory,
      selectCover,
      addTag,
      removeTag,
      showDeviceSelector,
      closeDeviceModal,
      isDeviceSelected,
      toggleDeviceSelection,
      confirmDeviceSelection,
      removeDevice,
      updateDeviceBrightness,
      updateDeviceColorTemp
    }
  }
}
</script>

<style scoped>
.scene-page {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

.btn-add {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 15rpx 25rpx;
  background-color: #1890ff;
  color: white;
  border-radius: 8rpx;
  border: none;
  font-size: 28rpx;
}

/* 当前场景状态 */
.current-scene {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  color: white;
  position: relative;
  overflow: hidden;
}

.scene-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.status-label {
  font-size: 26rpx;
  opacity: 0.9;
}

.status-value {
  font-size: 32rpx;
  font-weight: bold;
}

.scene-info {
  margin-bottom: 20rpx;
}

.info-text {
  display: block;
  font-size: 28rpx;
  margin-bottom: 10rpx;
  opacity: 0.9;
}

.info-time {
  font-size: 24rpx;
  opacity: 0.8;
}

.btn-deactivate {
  padding: 12rpx 24rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  color: white;
  font-size: 26rpx;
}

/* 场景分类 */
.scene-categories {
  display: flex;
  gap: 15rpx;
  margin-bottom: 30rpx;
  overflow-x: auto;
  padding: 10rpx 0;
}

.category-tab {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120rpx;
  padding: 20rpx 15rpx;
  background-color: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s;
}

.category-tab.active {
  background-color: #1890ff;
  color: white;
  transform: translateY(-2rpx);
}

.category-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.category-label {
  font-size: 24rpx;
  margin-bottom: 4rpx;
}

.category-count {
  font-size: 20rpx;
  opacity: 0.7;
}

/* 场景列表 */
.scene-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.scene-card {
  background-color: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s;
}

.scene-card.active {
  border: 2rpx solid #1890ff;
  box-shadow: 0 4rpx 20rpx rgba(24, 144, 255, 0.3);
}

.scene-cover {
  position: relative;
  height: 300rpx;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.scene-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.7) 100%);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20rpx;
}

.scene-type {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: white;
}

.type-preset {
  background-color: rgba(24, 144, 255, 0.8);
}

.type-custom {
  background-color: rgba(82, 196, 26, 0.8);
}

.scene-actions {
  display: flex;
  gap: 10rpx;
}

.btn-action {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-apply {
  background-color: rgba(24, 144, 255, 0.9);
  color: white;
}

.btn-apply:disabled {
  background-color: rgba(82, 196, 26, 0.9);
}

.btn-edit {
  background-color: rgba(255, 193, 7, 0.9);
  color: white;
}

.btn-delete {
  background-color: rgba(255, 77, 79, 0.9);
  color: white;
}

.action-icon {
  font-size: 20rpx;
}

/* 场景信息 */
.scene-info {
  padding: 30rpx;
}

.scene-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.scene-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.scene-rating {
  font-size: 24rpx;
  color: #faad14;
}

.scene-description {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.scene-details {
  display: flex;
  gap: 30rpx;
  margin-bottom: 20rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #666;
}

.detail-icon {
  font-size: 20rpx;
}

.scene-tags {
  display: flex;
  gap: 10rpx;
  flex-wrap: wrap;
}

.scene-tag {
  padding: 6rpx 12rpx;
  background-color: #f0f0f0;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: #666;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 50rpx;
  background-color: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.btn-create {
  padding: 20rpx 40rpx;
  background-color: #1890ff;
  color: white;
  border-radius: 8rpx;
  border: none;
  font-size: 28rpx;
}

/* 弹窗样式 */
.preview-modal,
.create-modal,
.device-modal {
  background-color: white;
  border-radius: 16rpx;
  width: 700rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.btn-close {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f8f9fa;
  border: none;
  font-size: 28rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  padding: 30rpx;
  max-height: 600rpx;
  overflow-y: auto;
}

/* 预览弹窗 */
.preview-cover {
  height: 300rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-info {
  margin-bottom: 30rpx;
}

.preview-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.preview-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.preview-stats {
  display: flex;
  gap: 40rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.stat-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #1890ff;
}

.device-preview {
  border-top: 1rpx solid #e9ecef;
  padding-top: 30rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.device-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  max-height: 300rpx;
  overflow-y: auto;
}

.device-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.device-info {
  flex: 1;
}

.device-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.device-location {
  font-size: 22rpx;
  color: #666;
}

.device-settings {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
  align-items: flex-end;
}

.setting-label {
  font-size: 22rpx;
  color: #666;
}

/* 创建弹窗表单 */
.form-section {
  margin-bottom: 40rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.form-textarea {
  height: 120rpx;
  resize: none;
}

.picker-text {
  padding: 20rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.form-switch {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 封面选择 */
.cover-selector {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
}

.cover-option {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  overflow: hidden;
  border: 3rpx solid transparent;
  cursor: pointer;
  transition: all 0.3s;
}

.cover-option.selected {
  border-color: #1890ff;
}

.cover-thumb {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 灯具组配置 */
.btn-add-lamp-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #1890ff;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #f0f8ff;
  color: #1890ff;
  margin-bottom: 20rpx;
}

.lamp-groups {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.lamp-group-config {
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.group-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.params-section {
  margin-top: 20rpx;
}

.params-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #666;
  margin-bottom: 15rpx;
  padding-bottom: 10rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.required {
  color: #ff4d4f;
  font-weight: bold;
}

.picker-text {
  color: #333;
  font-size: 28rpx;
}

/* 设备配置 */
.btn-select-devices {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
  color: #333;
  margin-bottom: 20rpx;
}

.selected-devices {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.device-config {
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e9ecef;
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.btn-remove {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #ff4d4f;
  color: white;
  border: none;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.config-item {
  margin-bottom: 15rpx;
}

.config-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

/* 标签输入 */
.tag-input {
  display: flex;
  gap: 15rpx;
  margin-bottom: 15rpx;
}

.tag-input-field {
  flex: 1;
  padding: 15rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 6rpx;
  font-size: 26rpx;
}

.btn-add-tag {
  padding: 15rpx 25rpx;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 6rpx;
  font-size: 26rpx;
}

.tag-list {
  display: flex;
  gap: 10rpx;
  flex-wrap: wrap;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 12rpx;
  background-color: #e6f7ff;
  border: 1rpx solid #91d5ff;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #1890ff;
}

.btn-remove-tag {
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  background-color: #ff4d4f;
  color: white;
  border: none;
  font-size: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 设备选择弹窗 */
.device-checkbox {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  border: 1rpx solid #e9ecef;
  border-radius: 8rpx;
  cursor: pointer;
  margin-bottom: 15rpx;
}

.device-checkbox:hover {
  background-color: #f8f9fa;
}

.device-status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.status-online {
  background-color: #d4edda;
  color: #155724;
}

.status-offline {
  background-color: #f8d7da;
  color: #721c24;
}

.modal-actions {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #e9ecef;
}

.btn-cancel {
  flex: 1;
  padding: 25rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #dee2e6;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #495057;
}

.btn-save,
.btn-apply,
.btn-confirm {
  flex: 1;
  padding: 25rpx;
  background-color: #1890ff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: white;
}

.btn-apply:disabled {
  background-color: #52c41a;
}

.icon {
  font-size: 24rpx;
}
</style>