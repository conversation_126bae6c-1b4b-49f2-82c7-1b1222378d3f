/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 设备控制记录输出参数
 * @export
 * @interface EnergyControlOutput
 */
export interface EnergyControlOutput {
    /**
     * 主键ID
     * @type {number}
     * @memberof EnergyControlOutput
     */
    id?: number;
    /**
     * 设备ID
     * @type {number}
     * @memberof EnergyControlOutput
     */
    deviceId?: number;
    /**
     * 设备编码
     * @type {string}
     * @memberof EnergyControlOutput
     */
    deviceCode?: string | null;
    /**
     * 设备名称
     * @type {string}
     * @memberof EnergyControlOutput
     */
    deviceName?: string | null;
    /**
     * 控制类型
     * @type {string}
     * @memberof EnergyControlOutput
     */
    controlType?: string | null;
    /**
     * 控制指令
     * @type {string}
     * @memberof EnergyControlOutput
     */
    controlCommand?: string | null;
    /**
     * 控制参数
     * @type {string}
     * @memberof EnergyControlOutput
     */
    controlParams?: string | null;
    /**
     * 控制时间
     * @type {Date}
     * @memberof EnergyControlOutput
     */
    controlTime?: Date;
    /**
     * 控制结果
     * @type {number}
     * @memberof EnergyControlOutput
     */
    controlResult?: number;
    /**
     * 响应时间(ms)
     * @type {number}
     * @memberof EnergyControlOutput
     */
    responseTime?: number | null;
    /**
     * 错误信息
     * @type {string}
     * @memberof EnergyControlOutput
     */
    errorMessage?: string | null;
    /**
     * 操作人员
     * @type {string}
     * @memberof EnergyControlOutput
     */
    operatorName?: string | null;
    /**
     * 备注
     * @type {string}
     * @memberof EnergyControlOutput
     */
    remark?: string | null;
    /**
     * 创建时间
     * @type {Date}
     * @memberof EnergyControlOutput
     */
    createTime?: Date;
    /**
     * 更新时间
     * @type {Date}
     * @memberof EnergyControlOutput
     */
    updateTime?: Date | null;
    /**
     * 租户ID
     * @type {number}
     * @memberof EnergyControlOutput
     */
    tenantId?: number | null;
    /**
     * 执行状态名称
     * @type {string}
     * @memberof EnergyControlOutput
     */
    executeStatusName?: string | null;
    /**
     * 执行结果
     * @type {string}
     * @memberof EnergyControlOutput
     */
    executeResult?: string | null;
    /**
     * 控制来源
     * @type {number}
     * @memberof EnergyControlOutput
     */
    controlSource?: number | null;
    /**
     * 场景ID
     * @type {number}
     * @memberof EnergyControlOutput
     */
    sceneId?: number | null;
    /**
     * 场景名称
     * @type {string}
     * @memberof EnergyControlOutput
     */
    sceneName?: string | null;
    /**
     * 执行时长(ms)
     * @type {number}
     * @memberof EnergyControlOutput
     */
    executeDuration?: number | null;
    /**
     * 设备位置
     * @type {string}
     * @memberof EnergyControlOutput
     */
    deviceLocation?: string | null;
    /**
     * 设备类型
     * @type {string}
     * @memberof EnergyControlOutput
     */
    deviceType?: string | null;
    /**
     * 设备型号
     * @type {string}
     * @memberof EnergyControlOutput
     */
    deviceModel?: string | null;
    /**
     * 控制参数
     * @type {string}
     * @memberof EnergyControlOutput
     */
    controlParameters?: string | null;
    /**
     * 执行状态
     * @type {number}
     * @memberof EnergyControlOutput
     */
    executeStatus?: number;
}
