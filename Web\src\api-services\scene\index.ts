import { service as request } from '/@/utils/request';
import { useBase<PERSON>pi } from '../base';

/**
 * 场景管理API接口集合
 * @method getPage 获取场景分页列表
 * @method getList 获取场景列表
 * @method add 添加场景
 * @method update 更新场景
 * @method delete 删除场景
 * @method execute 执行场景
 */
export function useSceneApi() {
	return {
		...useBaseApi('energyScene'),
		
		// 执行场景
		execute: (data: any) => {
			return request({
				url: '/api/energyScene/execute',
				method: 'post',
				data,
			});
		},
	};
}