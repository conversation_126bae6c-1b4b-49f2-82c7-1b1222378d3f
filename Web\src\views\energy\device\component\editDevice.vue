<template>
  <div class="system-edit-device">
    <el-dialog
      v-model="visible"
      :title="title"
      width="800px"
      :before-close="handleClose"
      destroy-on-close
    >
      <el-form
        ref="editDeviceFormRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        status-icon
      >
        <el-row :gutter="35">
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="设备名称" prop="deviceName">
              <el-input v-model="form.deviceName" placeholder="请输入设备名称" clearable />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="设备编号" prop="deviceCode">
              <el-input v-model="form.deviceCode" placeholder="请输入设备编号" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="35">
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="设备类型" prop="deviceType">
              <el-select v-model="form.deviceType" placeholder="请选择设备类型" clearable style="width: 100%">
                <el-option 
                  v-for="option in deviceTypeOptions" 
                  :key="option.value" 
                  :label="option.label" 
                  :value="option.value" 
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="安装位置" prop="location">
              <el-input v-model="form.location" placeholder="请输入安装位置" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="35">
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="功率(W)" prop="power">
              <el-input-number v-model="form.power" :min="0" :max="1000" placeholder="请输入功率" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="亮度(%)" prop="brightness">
              <el-input-number v-model="form.brightness" :min="0" :max="100" placeholder="请输入亮度" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="35">
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="设备状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择设备状态" clearable style="width: 100%">
                <el-option label="正常" value="1" />
                <el-option label="故障" value="2" />
                <el-option label="维护" value="3" />
                <el-option label="停用" value="4" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="开关状态" prop="isOn">
              <el-switch v-model="form.isOn" active-text="开启" inactive-text="关闭" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="35">
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="安装日期" prop="installDate">
              <el-date-picker
                v-model="form.installDate"
                type="date"
                placeholder="请选择安装日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="IP地址" prop="ipAddress">
              <el-input v-model="form.ipAddress" placeholder="请输入IP地址" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="35">
          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="loading">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="editDevice">
import { ref, reactive, nextTick } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { useDeviceApi } from '/@/api-services/device'
import { useDeviceTypeDict } from '@/composables/useDeviceTypeDict'

const props = defineProps<{
  title?: string
}>()

const emits = defineEmits<{
  reloadTable: []
}>()

const editDeviceFormRef = ref<FormInstance>()
const visible = ref(false)
const loading = ref(false)
const title = ref('')

// 设备类型字典数据
const { deviceTypeOptions } = useDeviceTypeDict()

const form = reactive({
  id: undefined,
  deviceName: '',
  deviceCode: '',
  deviceType: '',
  location: '',
  power: 0,
  brightness: 100,
  status: '1',
  isOn: true,
  installDate: '',
  ipAddress: '',
  remark: ''
})

const rules = reactive<FormRules>({
  deviceName: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
  deviceCode: [{ required: true, message: '请输入设备编号', trigger: 'blur' }],
  deviceType: [{ required: true, message: '请选择设备类型', trigger: 'change' }],
  location: [{ required: true, message: '请输入安装位置', trigger: 'blur' }],
  power: [{ required: true, message: '请输入功率', trigger: 'blur' }],
  status: [{ required: true, message: '请选择设备状态', trigger: 'change' }],
  installDate: [{ required: true, message: '请选择安装日期', trigger: 'change' }]
})

// 打开弹窗
const openDialog = async (type: string, row?: any) => {
  visible.value = true
  title.value = type === 'add' ? '新增设备' : '编辑设备'
  
  await nextTick()
  editDeviceFormRef.value?.resetFields()
  
  if (type === 'edit' && row) {
    Object.assign(form, row)
  } else {
    // 重置表单
    Object.assign(form, {
      id: undefined,
      deviceName: '',
      deviceCode: '',
      deviceType: '',
      location: '',
      power: 0,
      brightness: 100,
      status: '1',
      isOn: true,
      installDate: '',
      ipAddress: '',
      remark: ''
    })
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  editDeviceFormRef.value?.resetFields()
}

// 提交表单
const handleSubmit = async () => {
  if (!editDeviceFormRef.value) return
  
  const valid = await editDeviceFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  try {
    loading.value = true
    const deviceApi = useDeviceApi();
    
    if (form.id) {
      // 编辑
      await deviceApi.update(form);
      ElMessage.success('更新成功');
    } else {
      // 新增
      await deviceApi.add(form);
      ElMessage.success('新增成功');
    }
    
    handleClose()
    emits('reloadTable')
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    loading.value = false
  }
}

defineExpose({
  openDialog
})
</script>

<style scoped lang="scss">
.system-edit-device {
  .dialog-footer {
    text-align: right;
  }
}
</style>