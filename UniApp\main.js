import { createSSRApp } from 'vue'
import App from './App.vue'

// 导入全局样式
import './static/css/common.css'

// 导入Vuex store
import store from './store/index.js'

// 导入工具函数
import * as utils from './utils/index.js'

// 导入API接口
import * as api from './api/index.js'
import deviceApi from './api/device.js'

// 导入MQTT客户端
import mqtt from './utils/mqtt.js'

// 导入错误处理工具
import errorHandler, { handleError, handleCriticalError } from './utils/errorHandler.js'

// 导入错误处理混入
import errorHandlingMixin from './mixins/errorHandling.js'

// 导入全局加载状态管理器
import { Loading, LoadingPlugin } from './utils/loadingManager.js'

// 导入polyfill
import { initPolyfill, checkFunctionAvailability } from './utils/polyfill.js'

export function createApp() {
  const app = createSSRApp(App)

  // 初始化polyfill
  initPolyfill();
  
  // 检查函数可用性
  checkFunctionAvailability();

  // 初始化错误处理器
  errorHandler.initialize();

  // 配置Vuex store
  app.use(store)
  
  // 配置全局加载状态管理器
  app.use(LoadingPlugin)
  
  // 全局属性配置
  app.config.globalProperties.$utils = utils
  app.config.globalProperties.$api = api
  app.config.globalProperties.$mqtt = mqtt
  app.config.globalProperties.$store = store
  app.config.globalProperties.$loading = Loading
  
  // 确保设备统计函数在全局作用域可用 - 直接使用deviceApi
  if (deviceApi && deviceApi.getDeviceStatistics) {
    app.config.globalProperties.getDeviceStatistics = deviceApi.getDeviceStatistics
    app.config.globalProperties.getDeviceStats = deviceApi.getDeviceStats
  } else {
    // 提供降级函数
    const fallbackFn = (params = {}) => {
      console.warn('getDeviceStatistics API不可用，返回模拟数据')
      return Promise.resolve({
        code: 200,
        message: 'success',
        data: {
          totalDevices: 0,
          onlineDevices: 0,
          offlineDevices: 0,
          energyConsumption: 0,
          lastUpdateTime: new Date().toISOString()
        }
      })
    }
    app.config.globalProperties.getDeviceStatistics = fallbackFn
    app.config.globalProperties.getDeviceStats = fallbackFn
  }
  
  // 全局错误处理
  app.config.errorHandler = (err, vm, info) => {
    // 安全获取组件信息
    let componentName = 'Unknown'
    try {
      if (vm && vm.$options && vm.$options.name) {
        componentName = vm.$options.name
      } else if (vm && vm.$.type && vm.$.type.name) {
        componentName = vm.$.type.name
      }
    } catch (e) {
      // 忽略获取组件名称时的错误
    }
    
    const context = {
      component: componentName,
      errorInfo: info,
      route: getCurrentPages().pop()?.route
    }
    
    // 判断错误严重程度，只有真正的严重错误才使用严重错误处理
    const isCriticalError = err.level === 'CRITICAL' || 
                           err.code === 'CRITICAL_ERROR' ||
                           err.message?.includes('严重') ||
                           info === 'errorCaptured' // Vue组件错误捕获
    
    if (isCriticalError) {
      handleCriticalError(err, context)
    } else {
      // 普通错误使用常规处理
      handleError(err, context, {
        showToast: true,
        reportError: true,
        logError: true
      })
    }
  }
  
  // 全局警告处理
  app.config.warnHandler = (msg, vm, trace) => {
    console.warn('全局警告:', msg)
    console.warn('警告追踪:', trace)
    
    // 记录警告到错误处理系统（低级别）
    handleError(new Error(msg), {
      type: 'warning',
      component: vm?.$options.name,
      trace
    }, {
      showToast: false,
      reportError: false,
      logError: true
    })
  }
  
  // 全局属性配置 - 添加错误处理工具
  app.config.globalProperties.$errorHandler = errorHandler
  app.config.globalProperties.$handleError = handleError
  
  // 应用全局混入
  app.mixin(globalMixin)
  
  return {
    app
  }
}

// 全局混入
const globalMixin = {
  // 合并错误处理混入
  mixins: [errorHandlingMixin],
  
  data() {
    return {
      // 全局数据
      globalLoading: false,
      userInfo: null
    }
  },
  
  onLoad() {
    // 页面加载时获取用户信息
    this.getUserInfo()
  },
  
  methods: {
    // 获取用户信息
    getUserInfo() {
      const userInfo = uni.getStorageSync('userInfo')
      if (userInfo) {
        this.userInfo = JSON.parse(userInfo)
      }
    },
    
    // 显示加载提示（保持向后兼容）
    showLoading(options = '加载中...') {
      this.globalLoading = true
      
      if (typeof options === 'string') {
        return Loading.showGlobal({ text: options })
      } else {
        return Loading.showGlobal(options)
      }
    },
    
    // 隐藏加载提示
    hideLoading(loadingId) {
      this.globalLoading = false
      
      if (loadingId) {
        Loading.hide(loadingId)
      } else {
        Loading.hideGlobal()
      }
    },
    
    // 显示骨架屏
    showSkeleton(options = {}) {
      return Loading.showSkeleton(options)
    },
    
    // 显示进度加载
    showProgress(options = {}) {
      return Loading.showProgress(options)
    },
    
    // 更新进度
    updateProgress(loadingId, progress) {
      Loading.updateProgress(loadingId, progress)
    },
    
    // 显示成功提示
    showSuccess(title = '操作成功') {
      uni.showToast({
        title: title,
        icon: 'success',
        duration: 2000
      })
    },
    
    // 显示错误提示
    showError(title = '操作失败') {
      uni.showToast({
        title: title,
        icon: 'none',
        duration: 2000
      })
    },
    
    // API请求包装器（带错误处理）
    async apiRequest(apiCall, options = {}) {
      const {
        showLoading = false,
        loadingText = '请求中...',
        retryable = true,
        apiName = 'unknown'
      } = options
      
      return this.safeExecute(apiCall, {
        showLoading,
        loadingText,
        retryable,
        onError: (error) => this.handleApiError(error, apiName)
      })
    },
    
    // 显示确认对话框
    showConfirm(content, title = '提示') {
      return new Promise((resolve, reject) => {
        uni.showModal({
          title: title,
          content: content,
          success: (res) => {
            if (res.confirm) {
              resolve(true)
            } else {
              resolve(false)
            }
          },
          fail: (err) => {
            reject(err)
          }
        })
      })
    },
    
    // 页面跳转
    navigateTo(url, params = {}) {
      let queryString = ''
      if (Object.keys(params).length > 0) {
        queryString = '?' + Object.keys(params).map(key => 
          `${key}=${encodeURIComponent(params[key])}`
        ).join('&')
      }
      
      uni.navigateTo({
        url: url + queryString,
        fail: (err) => {
          console.error('页面跳转失败:', err)
          this.showError('页面跳转失败')
        }
      })
    },
    
    // 返回上一页
    navigateBack(delta = 1) {
      uni.navigateBack({
        delta: delta,
        fail: (err) => {
          console.error('返回失败:', err)
          // 如果返回失败，跳转到首页
          uni.reLaunch({
            url: '/pages/index/index'
          })
        }
      })
    },
    
    // 格式化日期
    formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
      if (!date) return ''
      
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      const hours = String(d.getHours()).padStart(2, '0')
      const minutes = String(d.getMinutes()).padStart(2, '0')
      const seconds = String(d.getSeconds()).padStart(2, '0')
      
      return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds)
    },
    
    // 格式化数字
    formatNumber(num, decimals = 2) {
      if (isNaN(num)) return '0'
      return Number(num).toFixed(decimals)
    },
    
    // 检查网络状态
    checkNetworkStatus() {
      return new Promise((resolve) => {
        uni.getNetworkType({
          success: (res) => {
            resolve(res.networkType !== 'none')
          },
          fail: () => {
            resolve(false)
          }
        })
      })
    }
  }
}

// 导出 globalMixin 供 createApp 函数使用
export { globalMixin }