<template>
  <view class="forgot-container">
    <!-- 背景装饰 -->
    <view class="bg-decoration">
      <view class="bg-circle circle-1"></view>
      <view class="bg-circle circle-2"></view>
      <view class="bg-circle circle-3"></view>
    </view>

    <!-- 找回密码表单 -->
    <view class="forgot-form">
      <!-- 头部区域 -->
      <view class="header-section">
        <view class="logo">
          <image 
            src="https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=smart%20lighting%20system%20logo%20modern%20minimalist&image_size=square" 
            class="logo-image" 
            mode="aspectFit"
          />
        </view>
        <text class="app-title">找回密码</text>
        <text class="app-subtitle">重置您的登录密码</text>
      </view>

      <!-- 步骤指示器 -->
      <view class="step-indicator">
        <view class="step-item" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
          <view class="step-number">1</view>
          <text class="step-text">验证身份</text>
        </view>
        <view class="step-line" :class="{ active: currentStep > 1 }"></view>
        <view class="step-item" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
          <view class="step-number">2</view>
          <text class="step-text">重置密码</text>
        </view>
        <view class="step-line" :class="{ active: currentStep > 2 }"></view>
        <view class="step-item" :class="{ active: currentStep >= 3 }">
          <view class="step-number">3</view>
          <text class="step-text">完成</text>
        </view>
      </view>

      <!-- 表单内容 -->
      <view class="form-section">
        <!-- 步骤1：验证身份 -->
        <view v-if="currentStep === 1" class="step-content">
          <view class="step-title">
            <text class="title-icon">🔍</text>
            <text class="title-text">验证您的身份</text>
          </view>
          
          <!-- 验证方式选择 -->
          <view class="verify-method">
            <view class="method-tabs">
              <view 
                class="method-tab"
                :class="{ active: verifyMethod === 'phone' }"
                @click="setVerifyMethod('phone')"
              >
                <text class="tab-icon">📱</text>
                <text class="tab-text">手机验证</text>
              </view>
              <view 
                class="method-tab"
                :class="{ active: verifyMethod === 'email' }"
                @click="setVerifyMethod('email')"
              >
                <text class="tab-icon">📧</text>
                <text class="tab-text">邮箱验证</text>
              </view>
            </view>
          </view>

          <!-- 手机验证 -->
          <view v-if="verifyMethod === 'phone'" class="verify-form">
            <view class="input-group">
              <view class="input-label">
                <text class="label-icon">📱</text>
                <text class="label-text">手机号码</text>
              </view>
              <input 
                class="form-input"
                type="number"
                v-model="verifyForm.phone"
                placeholder="请输入注册时的手机号"
                :class="{ error: errors.phone }"
                @blur="validatePhone"
              />
              <text class="error-text" v-if="errors.phone">{{ errors.phone }}</text>
            </view>

            <view class="input-group">
              <view class="input-label">
                <text class="label-icon">🔢</text>
                <text class="label-text">验证码</text>
              </view>
              <view class="sms-input">
                <input 
                  class="form-input sms-field"
                  type="number"
                  v-model="verifyForm.smsCode"
                  placeholder="请输入验证码"
                  :class="{ error: errors.smsCode }"
                  @blur="validateSmsCode"
                />
                <button 
                  class="sms-btn"
                  :disabled="smsCountdown > 0 || !verifyForm.phone || errors.phone"
                  @click="sendSmsCode"
                >
                  {{ smsCountdown > 0 ? `${smsCountdown}s` : '发送验证码' }}
                </button>
              </view>
              <text class="error-text" v-if="errors.smsCode">{{ errors.smsCode }}</text>
            </view>
          </view>

          <!-- 邮箱验证 -->
          <view v-if="verifyMethod === 'email'" class="verify-form">
            <view class="input-group">
              <view class="input-label">
                <text class="label-icon">📧</text>
                <text class="label-text">邮箱地址</text>
              </view>
              <input 
                class="form-input"
                type="text"
                v-model="verifyForm.email"
                placeholder="请输入注册时的邮箱"
                :class="{ error: errors.email }"
                @blur="validateEmail"
              />
              <text class="error-text" v-if="errors.email">{{ errors.email }}</text>
            </view>

            <view class="input-group">
              <view class="input-label">
                <text class="label-icon">🔢</text>
                <text class="label-text">验证码</text>
              </view>
              <view class="sms-input">
                <input 
                  class="form-input sms-field"
                  type="text"
                  v-model="verifyForm.emailCode"
                  placeholder="请输入邮箱验证码"
                  :class="{ error: errors.emailCode }"
                  @blur="validateEmailCode"
                />
                <button 
                  class="sms-btn"
                  :disabled="emailCountdown > 0 || !verifyForm.email || errors.email"
                  @click="sendEmailCode"
                >
                  {{ emailCountdown > 0 ? `${emailCountdown}s` : '发送验证码' }}
                </button>
              </view>
              <text class="error-text" v-if="errors.emailCode">{{ errors.emailCode }}</text>
            </view>
          </view>

          <button 
            class="next-btn"
            :class="{ loading: isLoading }"
            :disabled="isLoading"
            @click="verifyIdentity"
          >
            <text v-if="!isLoading">验证身份</text>
            <text v-else>验证中...</text>
          </button>
        </view>

        <!-- 步骤2：重置密码 -->
        <view v-if="currentStep === 2" class="step-content">
          <view class="step-title">
            <text class="title-icon">🔐</text>
            <text class="title-text">设置新密码</text>
          </view>

          <view class="input-group">
            <view class="input-label">
              <text class="label-icon">🔒</text>
              <text class="label-text">新密码</text>
            </view>
            <view class="password-input">
              <input 
                class="form-input"
                :type="showPassword ? 'text' : 'password'"
                v-model="resetForm.password"
                placeholder="请输入新密码（6-20个字符）"
                :class="{ error: errors.password }"
                @blur="validatePassword"
              />
              <text 
                class="password-toggle"
                @click="togglePassword"
              >
                {{ showPassword ? '👁️' : '👁️‍🗨️' }}
              </text>
            </view>
            <text class="error-text" v-if="errors.password">{{ errors.password }}</text>
          </view>

          <view class="input-group">
            <view class="input-label">
              <text class="label-icon">🔐</text>
              <text class="label-text">确认密码</text>
            </view>
            <view class="password-input">
              <input 
                class="form-input"
                :type="showConfirmPassword ? 'text' : 'password'"
                v-model="resetForm.confirmPassword"
                placeholder="请再次输入新密码"
                :class="{ error: errors.confirmPassword }"
                @blur="validateConfirmPassword"
              />
              <text 
                class="password-toggle"
                @click="toggleConfirmPassword"
              >
                {{ showConfirmPassword ? '👁️' : '👁️‍🗨️' }}
              </text>
            </view>
            <text class="error-text" v-if="errors.confirmPassword">{{ errors.confirmPassword }}</text>
          </view>

          <button 
            class="next-btn"
            :class="{ loading: isLoading }"
            :disabled="isLoading"
            @click="resetPassword"
          >
            <text v-if="!isLoading">重置密码</text>
            <text v-else>重置中...</text>
          </button>
        </view>

        <!-- 步骤3：完成 -->
        <view v-if="currentStep === 3" class="step-content">
          <view class="success-content">
            <text class="success-icon">✅</text>
            <text class="success-title">密码重置成功</text>
            <text class="success-desc">您的密码已成功重置，请使用新密码登录</text>
            
            <button class="login-btn" @click="goLogin">
              立即登录
            </button>
          </view>
        </view>
      </view>

      <!-- 返回登录 -->
      <view class="back-login" v-if="currentStep < 3">
        <text class="back-text">记起密码了？</text>
        <text class="back-btn-text" @click="goLogin">返回登录</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { authApi } from '@/api/auth'
import { encryptPassword } from '@/utils/crypto'

// 响应式数据
const currentStep = ref(1)
const verifyMethod = ref('phone')
const isLoading = ref(false)
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const smsCountdown = ref(0)
const emailCountdown = ref(0)
const smsTimer = ref(null)
const emailTimer = ref(null)
const resetToken = ref('')

const verifyForm = reactive({
  phone: '',
  email: '',
  smsCode: '',
  emailCode: ''
})

const resetForm = reactive({
  password: '',
  confirmPassword: ''
})

const errors = reactive({
  phone: '',
  email: '',
  smsCode: '',
  emailCode: '',
  password: '',
  confirmPassword: ''
})

// 验证方法
const validatePhone = () => {
  if (!verifyForm.phone) {
    errors.phone = '请输入手机号码'
    return false
  }
  if (!/^1[3-9]\d{9}$/.test(verifyForm.phone)) {
    errors.phone = '请输入正确的手机号码'
    return false
  }
  errors.phone = ''
  return true
}

const validateEmail = () => {
  if (!verifyForm.email) {
    errors.email = '请输入邮箱地址'
    return false
  }
  if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(verifyForm.email)) {
    errors.email = '请输入正确的邮箱地址'
    return false
  }
  errors.email = ''
  return true
}

const validateSmsCode = () => {
  if (!verifyForm.smsCode) {
    errors.smsCode = '请输入验证码'
    return false
  }
  if (!/^\d{6}$/.test(verifyForm.smsCode)) {
    errors.smsCode = '请输入6位数字验证码'
    return false
  }
  errors.smsCode = ''
  return true
}

const validateEmailCode = () => {
  if (!verifyForm.emailCode) {
    errors.emailCode = '请输入验证码'
    return false
  }
  if (!/^[A-Za-z0-9]{6}$/.test(verifyForm.emailCode)) {
    errors.emailCode = '请输入6位验证码'
    return false
  }
  errors.emailCode = ''
  return true
}

const validatePassword = () => {
  if (!resetForm.password) {
    errors.password = '请输入新密码'
    return false
  }
  if (resetForm.password.length < 6 || resetForm.password.length > 20) {
    errors.password = '密码长度为6-20个字符'
    return false
  }
  errors.password = ''
  return true
}

const validateConfirmPassword = () => {
  if (!resetForm.confirmPassword) {
    errors.confirmPassword = '请确认密码'
    return false
  }
  if (resetForm.confirmPassword !== resetForm.password) {
    errors.confirmPassword = '两次输入的密码不一致'
    return false
  }
  errors.confirmPassword = ''
  return true
}

// 方法
const setVerifyMethod = (method) => {
  verifyMethod.value = method
  // 清空错误信息
  Object.keys(errors).forEach(key => {
    errors[key] = ''
  })
}

const togglePassword = () => {
  showPassword.value = !showPassword.value
}

const toggleConfirmPassword = () => {
  showConfirmPassword.value = !showConfirmPassword.value
}

const sendSmsCode = async () => {
  if (!validatePhone()) {
    return
  }
  
  try {
    await authApi.sendSmsCode({
      phone: verifyForm.phone,
      type: 'forgot'
    })
    
    // 开始倒计时
    smsCountdown.value = 60
    smsTimer.value = setInterval(() => {
      smsCountdown.value--
      if (smsCountdown.value <= 0) {
        clearInterval(smsTimer.value)
      }
    }, 1000)
    
    uni.showToast({
      title: '验证码已发送',
      icon: 'success'
    })
  } catch (err) {
    console.error('发送短信验证码失败:', err)
    uni.showToast({
      title: err.message || '发送失败，请重试',
      icon: 'error'
    })
  }
}

const sendEmailCode = async () => {
  if (!validateEmail()) {
    return
  }
  
  try {
    await authApi.sendEmailCode({
      email: verifyForm.email,
      type: 'forgot'
    })
    
    // 开始倒计时
    emailCountdown.value = 60
    emailTimer.value = setInterval(() => {
      emailCountdown.value--
      if (emailCountdown.value <= 0) {
        clearInterval(emailTimer.value)
      }
    }, 1000)
    
    uni.showToast({
      title: '验证码已发送',
      icon: 'success'
    })
  } catch (err) {
    console.error('发送邮箱验证码失败:', err)
    uni.showToast({
      title: err.message || '发送失败，请重试',
      icon: 'error'
    })
  }
}

const verifyIdentity = async () => {
  let isValid = false
  let verifyData = {}
  
  if (verifyMethod.value === 'phone') {
    isValid = validatePhone() && validateSmsCode()
    verifyData = {
      phone: verifyForm.phone,
      smsCode: verifyForm.smsCode,
      type: 'phone'
    }
  } else {
    isValid = validateEmail() && validateEmailCode()
    verifyData = {
      email: verifyForm.email,
      emailCode: verifyForm.emailCode,
      type: 'email'
    }
  }
  
  if (!isValid) {
    return
  }
  
  isLoading.value = true
  
  try {
    const result = await authApi.verifyResetCode(verifyData)
    resetToken.value = result.token
    currentStep.value = 2
    
    uni.showToast({
      title: '验证成功',
      icon: 'success'
    })
  } catch (err) {
    console.error('身份验证失败:', err)
    if (err.message && err.message.includes('验证码')) {
      if (verifyMethod.value === 'phone') {
        errors.smsCode = '验证码错误或已过期'
      } else {
        errors.emailCode = '验证码错误或已过期'
      }
    } else {
      uni.showToast({
        title: err.message || '验证失败，请重试',
        icon: 'error'
      })
    }
  } finally {
    isLoading.value = false
  }
}

const resetPassword = async () => {
  if (!validatePassword() || !validateConfirmPassword()) {
    return
  }
  
  isLoading.value = true
  
  try {
    // SM2加密密码
    const encryptedPassword = encryptPassword(resetForm.password)
    
    await authApi.resetPassword({
      token: resetToken.value,
      password: encryptedPassword
    })
    
    currentStep.value = 3
    
    uni.showToast({
      title: '密码重置成功',
      icon: 'success'
    })
  } catch (err) {
    console.error('密码重置失败:', err)
    uni.showToast({
      title: err.message || '重置失败，请重试',
      icon: 'error'
    })
  } finally {
    isLoading.value = false
  }
}

const goLogin = () => {
  uni.navigateBack()
}

// 生命周期
onMounted(() => {
  // 页面初始化
})

// 页面卸载时清理定时器
onUnmounted(() => {
  if (smsTimer.value) {
    clearInterval(smsTimer.value)
    smsTimer.value = null
  }
  if (emailTimer.value) {
    clearInterval(emailTimer.value)
    emailTimer.value = null
  }
})
</script>

<style scoped>
.forgot-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.circle-1 {
  width: 300rpx;
  height: 300rpx;
  top: -150rpx;
  right: -150rpx;
}

.circle-2 {
  width: 200rpx;
  height: 200rpx;
  bottom: 200rpx;
  left: -100rpx;
}

.circle-3 {
  width: 150rpx;
  height: 150rpx;
  top: 300rpx;
  left: 50rpx;
}

/* 找回密码表单 */
.forgot-form {
  position: relative;
  z-index: 1;
  padding: 60rpx 40rpx 40rpx;
}

/* 头部区域 */
.header-section {
  text-align: center;
  margin-bottom: 50rpx;
}

.logo {
  margin-bottom: 30rpx;
}

.logo-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 20rpx;
}

.app-title {
  display: block;
  font-size: 44rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 10rpx;
}

.app-subtitle {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 步骤指示器 */
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 50rpx;
  padding: 0 20rpx;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.5;
}

.step-item.active {
  opacity: 1;
}

.step-item.completed {
  opacity: 1;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.step-item.active .step-number {
  background: white;
  color: #667eea;
}

.step-item.completed .step-number {
  background: #4CAF50;
  color: white;
}

.step-text {
  font-size: 20rpx;
  color: white;
}

.step-line {
  flex: 1;
  height: 2rpx;
  background: rgba(255, 255, 255, 0.3);
  margin: 0 20rpx;
}

.step-line.active {
  background: white;
}

/* 表单区域 */
.form-section {
  background: white;
  border-radius: 20rpx;
  padding: 50rpx 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}

.step-content {
  min-height: 400rpx;
}

.step-title {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
}

.title-icon {
  font-size: 40rpx;
  margin-right: 15rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 验证方式选择 */
.verify-method {
  margin-bottom: 40rpx;
}

.method-tabs {
  display: flex;
  background: #f5f5f5;
  border-radius: 12rpx;
  padding: 8rpx;
}

.method-tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 8rpx;
  cursor: pointer;
}

.method-tab.active {
  background: white;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.tab-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.tab-text {
  font-size: 24rpx;
  color: #666;
}

.method-tab.active .tab-text {
  color: #333;
  font-weight: bold;
}

/* 表单输入 */
.verify-form {
  margin-bottom: 40rpx;
}

.input-group {
  margin-bottom: 35rpx;
}

.input-label {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.label-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.label-text {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 25rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  font-size: 26rpx;
  background: #f8f8f8;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #667eea;
  background: white;
}

.form-input.error {
  border-color: #ff3b30;
}

.error-text {
  font-size: 22rpx;
  color: #ff3b30;
  margin-top: 8rpx;
}

.password-input {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 25rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #999;
}

.sms-input {
  display: flex;
  gap: 20rpx;
}

.sms-field {
  flex: 1;
}

.sms-btn {
  width: 200rpx;
  height: 80rpx;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 10rpx;
  font-size: 24rpx;
}

.sms-btn:disabled {
  background: #ccc;
}

.next-btn {
  width: 100%;
  height: 90rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.next-btn.loading {
  opacity: 0.7;
}

.next-btn:disabled {
  opacity: 0.5;
}

/* 成功页面 */
.success-content {
  text-align: center;
  padding: 60rpx 0;
}

.success-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.success-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.success-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 50rpx;
}

.login-btn {
  width: 300rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: bold;
}

/* 返回登录 */
.back-login {
  text-align: center;
  margin-top: 40rpx;
}

.back-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

.back-btn-text {
  font-size: 26rpx;
  color: white;
  font-weight: bold;
  margin-left: 10rpx;
}
</style>