import { storage } from '../../utils'
import * as authApi from '../../api/auth'
import { cacheManager } from '../../utils/cache'
import { clearErrorDeduplicationCache } from '../../utils/errorHandler'

// 初始状态
const state = {
  // 用户token
  token: storage.get('token') || '',
  // 用户信息
  userInfo: storage.get('userInfo') || null,
  // 用户权限
  permissions: storage.get('permissions') || [],
  // 用户角色
  roles: storage.get('roles') || [],
  // 用户组织信息
  orgInfo: storage.get('orgInfo') || null,
  // 登录状态
  isLoggedIn: false,
  // 加载状态
  loading: false,
  // 错误信息
  error: null,
  // 登录时间
  loginTime: storage.get('loginTime') || null,
  // 最后活动时间
  lastActiveTime: Date.now(),
  // 用户统计信息
  userStats: {
    devices: 0,
    operations: 0,
    energy: '0%',
    alerts: 0
  }
}

// Getters
const getters = {
  // 是否已登录
  isLoggedIn: (state) => {
    return !!(state.token && state.userInfo)
  },
  
  // 用户头像
  avatar: (state) => {
    return state.userInfo?.avatar || '/static/images/default-avatar.png'
  },
  
  // 用户昵称
  nickname: (state) => {
    return state.userInfo?.realName || state.userInfo?.nickname || state.userInfo?.account || '未知用户'
  },
  
  // 用户真实姓名
  realName: (state) => {
    return state.userInfo?.realName || '未设置'
  },
  
  // 用户账号
  account: (state) => {
    return state.userInfo?.account || ''
  },
  
  // 用户手机号
  phone: (state) => {
    return state.userInfo?.phone || ''
  },
  
  // 用户邮箱
  email: (state) => {
    return state.userInfo?.email || ''
  },
  
  // 用户组织名称
  orgName: (state) => {
    return state.orgInfo?.name || '未分配'
  },
  
  // 用户角色名称
  roleNames: (state) => {
    return state.roles.map(role => {
      const roleMap = {
        'SuperAdmin': '超级管理员',
        'Admin': '系统管理员',
        'Manager': '管理员',
        'Operator': '操作员',
        'Viewer': '查看者'
      }
      return roleMap[role] || role
    })
  },
  
  // 主要角色
  primaryRole: (state) => {
    if (!state.roles || state.roles.length === 0) return '普通用户'
    const roleMap = {
      'SuperAdmin': '超级管理员',
      'Admin': '系统管理员',
      'Manager': '管理员',
      'Operator': '操作员',
      'Viewer': '查看者'
    }
    return roleMap[state.roles[0]] || state.roles[0]
  },
  
  // 是否有指定权限
  hasPermission: (state) => (permission) => {
    if (!state.permissions || state.permissions.length === 0) {
      return false
    }
    return state.permissions.includes(permission)
  },
  
  // 是否有指定角色
  hasRole: (state) => (role) => {
    if (!state.roles || state.roles.length === 0) {
      return false
    }
    return state.roles.includes(role)
  },
  
  // 是否为管理员
  isAdmin: (state) => {
    return state.roles.includes('SuperAdmin') || state.roles.includes('Admin')
  },
  
  // 是否可以管理设备
  canManageDevice: (state) => {
    return state.permissions.includes('device:add') ||
           state.permissions.includes('device:edit') ||
           state.permissions.includes('device:delete') ||
           state.roles.includes('SuperAdmin') ||
           state.roles.includes('Admin')
  },
  
  // 是否可以控制设备
  canControlDevice: (state) => {
    return state.permissions.includes('device:control') ||
           state.roles.includes('SuperAdmin') ||
           state.roles.includes('Admin') ||
           state.roles.includes('Operator')
  },
  
  // 是否在线
  isOnline: (state) => {
    if (!state.lastActiveTime) return false
    const now = Date.now()
    const fiveMinutes = 5 * 60 * 1000
    return (now - state.lastActiveTime) < fiveMinutes
  },
  
  // Token是否即将过期（30分钟内）
  isTokenExpiringSoon: (state) => {
    if (!state.loginTime) return false
    const now = Date.now()
    const loginTime = new Date(state.loginTime).getTime()
    const expireTime = loginTime + (24 * 60 * 60 * 1000) // 24小时
    const warningTime = expireTime - (30 * 60 * 1000) // 提前30分钟警告
    return now >= warningTime
  },
  
  // 用户统计信息
  stats: (state) => {
    return state.userStats
  }
}

// Mutations
const mutations = {
  // 设置Token
  SET_TOKEN(state, token) {
    state.token = token
    if (token) {
      storage.set('token', token)
      // 设置请求头
      uni.setStorageSync('Authorization', `Bearer ${token}`)
    } else {
      storage.remove('token')
      uni.removeStorageSync('Authorization')
    }
  },
  
  // 设置用户信息
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo
    if (userInfo) {
      storage.set('userInfo', userInfo)
    } else {
      storage.remove('userInfo')
    }
  },
  
  // 设置权限
  SET_PERMISSIONS(state, permissions) {
    state.permissions = permissions || []
    storage.set('permissions', state.permissions)
  },
  
  // 设置角色
  SET_ROLES(state, roles) {
    state.roles = roles || []
    storage.set('roles', state.roles)
  },
  
  // 设置组织信息
  SET_ORG_INFO(state, orgInfo) {
    state.orgInfo = orgInfo
    if (orgInfo) {
      storage.set('orgInfo', orgInfo)
    } else {
      storage.remove('orgInfo')
    }
  },
  
  // 设置用户统计信息
  SET_USER_STATS(state, stats) {
    state.userStats = { ...state.userStats, ...stats }
  },
  
  // 设置登录状态
  SET_LOGIN_STATUS(state, status) {
    state.isLoggedIn = status
    if (status) {
      state.loginTime = new Date().toISOString()
      storage.set('loginTime', state.loginTime)
    } else {
      state.loginTime = null
      storage.remove('loginTime')
    }
  },
  
  // 设置加载状态
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  
  // 清除用户数据
  CLEAR_USER_DATA(state) {
    // 清除状态数据
    state.token = ''
    state.userInfo = null
    state.permissions = []
    state.roles = []
    state.orgInfo = null
    state.isLoggedIn = false
    state.loginTime = null
    state.error = null
    state.userStats = {
      devices: 0,
      operations: 0,
      energy: '0%',
      alerts: 0
    }
    
    // 清除本地存储的用户数据
    storage.remove('token')
    storage.remove('userInfo')
    storage.remove('permissions')
    storage.remove('roles')
    storage.remove('orgInfo')
    storage.remove('loginTime')
    uni.removeStorageSync('Authorization')
    
    // 清除所有应用缓存数据
    try {
      cacheManager.clear()
      console.log('应用缓存已清除')
    } catch (error) {
      console.error('清除应用缓存失败:', error)
    }
    
    // 清除错误去重缓存
    try {
      clearErrorDeduplicationCache()
      console.log('错误去重缓存已清除')
    } catch (error) {
      console.error('清除错误去重缓存失败:', error)
    }
    
    // 清除其他可能的用户相关存储
    try {
      // 清除可能的设备相关缓存
      uni.removeStorageSync('deviceList')
      uni.removeStorageSync('deviceStatus')
      uni.removeStorageSync('lightingConfig')
      uni.removeStorageSync('energyData')
      uni.removeStorageSync('faultRecords')
      
      // 清除用户偏好设置
      uni.removeStorageSync('userPreferences')
      uni.removeStorageSync('appSettings')
      
      // 清除临时数据
      uni.removeStorageSync('tempData')
      uni.removeStorageSync('formData')
      
      console.log('所有用户相关数据已清除')
    } catch (error) {
      console.error('清除用户相关数据时出错:', error)
    }
  },
  
  // 更新最后活动时间
  UPDATE_LAST_ACTIVE_TIME(state) {
    state.lastActiveTime = Date.now()
  },
  
  // 设置错误信息
  SET_ERROR(state, error) {
    state.error = error
  }
}

// Actions
const actions = {
  // 用户登录
  async login({ commit, dispatch }, loginData) {
    try {
      commit('SET_LOADING', true)
      commit('SET_ERROR', null)
      
      // 调用登录API
      const response = await authApi.login(loginData)
      //console.log(response)
      if (response.code === 200 || response.data) {
        const data = response.data.result 
        const accessToken = data.accessToken
        
        // 设置token
        commit('SET_TOKEN', accessToken)
        commit('SET_LOGIN_STATUS', true)
        
        // 获取用户信息
        await dispatch('getUserInfo', { token: accessToken })
        
        // 注释掉权限查询，登录后可操作所有功能
        // await dispatch('getPermissions')
        
        // 获取用户组织信息
        await dispatch('getOrgInfo')
        
        // 登录成功后的页面跳转逻辑
        setTimeout(() => {
          const redirectPath = uni.getStorageSync('redirectAfterLogin')
          if (redirectPath) {
            // 清除保存的跳转路径
            uni.removeStorageSync('redirectAfterLogin')
            // 跳转到之前访问的页面
            uni.reLaunch({
              url: `/${redirectPath}`
            })
          } else {
            // 默认跳转到首页
            uni.reLaunch({
              url: '/pages/index/index'
            })
          }
        }, 500) // 延迟500ms，确保数据加载完成
        
        return { success: true, data }
      } else {
        throw new Error(response.message || response.msg || '登录失败')
      }
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },

  
  // 用户登出
  async logout({ commit, state }, options = {}) {
    try {
      commit('SET_LOADING', true)
      
      // 调用登出API
      if (state.token) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('登出接口调用失败:', error)
    } finally {
      // 无论接口是否成功，都清除本地数据
      commit('CLEAR_USER_DATA')
      commit('SET_LOADING', false)
      
      // 显示退出成功提示
      uni.showToast({
        title: '退出成功',
        icon: 'success',
        duration: 1500
      })
      
      // 延迟跳转，让用户看到提示信息
      setTimeout(() => {
        const { redirectTo = 'welcome' } = options
        
        if (redirectTo === 'login') {
          // 直接跳转到登录页
          uni.reLaunch({
            url: '/pages/user/login'
          })
        } else {
          // 默认跳转到首页（欢迎页面）
          uni.reLaunch({
            url: '/pages/index/index'
          })
        }
      }, 1500)
    }
  },
  
  // 获取用户信息
  async getUserInfo({ commit, state }, payload) {
    try {
      const token = (payload && payload.token) || state.token
      if (!token) {
        throw new Error('未登录')
      }
      
      // 调用获取用户信息API
      const response = await authApi.getUserInfo()
      
      if (response.code === 200 || response.success) {
        const userInfo = response.data || response.result
        commit('SET_USER_INFO', userInfo)
        return userInfo
      } else {
        throw new Error(response.message || response.msg || '获取用户信息失败')
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果是token过期，清除登录状态
      if (error.message.includes('401') || error.message.includes('token')) {
        commit('CLEAR_USER_DATA')
      }
      throw error
    }
  },
  
  // 更新用户信息
  async updateUserInfo({ commit, state }, userInfo) {
    try {
      if (!state.token) {
        throw new Error('未登录')
      }
      
      // 调用更新用户信息API
      const response = await authApi.updateUserInfo(userInfo)
      
      if (response.code === 200 || response.success) {
        const updatedUserInfo = response.data || response.result
        commit('SET_USER_INFO', updatedUserInfo)
        return updatedUserInfo
      } else {
        throw new Error(response.message || response.msg || '更新用户信息失败')
      }
    } catch (error) {
      console.error('更新用户信息失败:', error)
      throw error
    }
  },
  
  // 获取用户权限和角色
  async getPermissions({ commit, state }) {
    try {
      if (!state.token) {
        throw new Error('未登录')
      }
      
      // 获取用户菜单权限
      const menuResponse = await authApi.getUserMenus()
      // 获取用户按钮权限
      const buttonResponse = await authApi.getUserButtons()
      // 获取用户角色
      const roleResponse = await authApi.getUserRoles()
      
      if (menuResponse.code === 200 || menuResponse.success) {
        const menus = menuResponse.data || menuResponse.result || []
        const buttons = (buttonResponse.data || buttonResponse.result || [])
        const roles = (roleResponse.data || roleResponse.result || [])
        
        // 提取权限码
        const permissions = [...menus.map(m => m.permission).filter(Boolean), 
                           ...buttons.map(b => b.permission).filter(Boolean)]
        
        commit('SET_PERMISSIONS', permissions)
        commit('SET_ROLES', roles)
        
        return { permissions, roles, menus, buttons }
      } else {
        throw new Error(menuResponse.message || menuResponse.msg || '获取权限信息失败')
      }
    } catch (error) {
      console.error('获取权限信息失败:', error)
      throw error
    }
  },
  
  // 获取组织信息
  async getOrgInfo({ commit, state }) {
    try {
      if (!state.token) {
        throw new Error('未登录')
      }
      
      // 调用获取组织信息API
      const response = await authApi.getUserOrg()
      
      if (response.code === 200 || response.success) {
        const orgInfo = response.data || response.result
        commit('SET_ORG_INFO', orgInfo)
        return orgInfo
      } else {
        console.warn('获取组织信息失败:', response.message || response.msg)
        return null
      }
    } catch (error) {
      console.error('获取组织信息失败:', error)
      return null
    }
  },

  // 刷新Token
  async refreshToken({ commit, state }) {
    try {
      if (!state.token) {
        throw new Error('未登录')
      }
      
      // 调用刷新token API
      const response = await authApi.refreshToken()
      
      if (response.code === 200 || response.success) {
        const data = response.data || response.result
        const newToken = data.accessToken || data.token
        commit('SET_TOKEN', newToken)
        return newToken
      } else {
        throw new Error(response.message || response.msg || '刷新Token失败')
      }
    } catch (error) {
      console.error('刷新Token失败:', error)
      // Token刷新失败，清除登录状态
      commit('CLEAR_USER_DATA')
      throw error
    }
  },
  
  // 修改密码
  async changePassword({ state }, passwordData) {
    try {
      if (!state.token) {
        throw new Error('未登录')
      }
      
      // 调用修改密码API
      const response = await authApi.changePassword(passwordData)
      
      if (response.code === 200 || response.success) {
        return { success: true, message: response.message || response.msg || '密码修改成功' }
      } else {
        throw new Error(response.message || response.msg || '修改密码失败')
      }
    } catch (error) {
      console.error('修改密码失败:', error)
      throw error
    }
  },
  
  // 检查登录状态
  async checkLoginStatus({ commit, state }) {
    try {
      if (!state.token) {
        return false
      }
      
      // 调用检查token有效性API
      const response = await authApi.checkToken()
      
      if (response.code === 200 || response.success) {
        // token有效，更新最后活跃时间
        commit('UPDATE_LAST_ACTIVE_TIME')
        return true
      } else {
        // token无效，清除登录状态
        commit('CLEAR_USER_DATA')
        return false
      }
    } catch (error) {
      console.error('检查登录状态失败:', error)
      // 网络错误或其他异常，暂时保持登录状态
      if (error.message.includes('401') || error.message.includes('token')) {
        commit('CLEAR_USER_DATA')
        return false
      }
      return true
    }
  },
  
  // 更新活跃时间
  updateActiveTime({ commit }) {
    commit('UPDATE_LAST_ACTIVE_TIME')
  },

  // 获取用户统计数据
  async getUserStats({ commit, state }) {
    try {
      if (!state.token) {
        throw new Error('未登录')
      }
      
      // 调用获取用户统计数据API
      const response = await authApi.getUserStats()
      
      if (response.code === 200 || response.success) {
        const stats = response.data || response.result || {
          devices: 0,
          operations: 0,
          energy: 0,
          alerts: 0
        }
        commit('SET_USER_STATS', stats)
        return stats
      } else {
        console.warn('获取用户统计数据失败:', response.message || response.msg)
        return state.userStats
      }
    } catch (error) {
      console.error('获取用户统计数据失败:', error)
      return state.userStats
    }
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}