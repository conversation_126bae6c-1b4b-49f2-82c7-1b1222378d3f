// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System.Linq;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.Drawing;
using Microsoft.AspNetCore.Mvc;

namespace Admin.NET.Core.Service;

/// <summary>
/// 能耗监控服务 🧩
/// </summary>
[ApiDescriptionSettings(Order = 502)]
public class EnergyConsumptionService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<EnergyConsumption> _energyConsumptionRep;
    private readonly SqlSugarRepository<EnergyDevice> _energyDeviceRep;
    private readonly SysCacheService _sysCacheService;
    private readonly UserManager _userManager;

    public EnergyConsumptionService(
        SqlSugarRepository<EnergyConsumption> energyConsumptionRep,
        SqlSugarRepository<EnergyDevice> energyDeviceRep,
        SysCacheService sysCacheService,
        UserManager userManager)
    {
        _energyConsumptionRep = energyConsumptionRep;
        _energyDeviceRep = energyDeviceRep;
        _sysCacheService = sysCacheService;
        _userManager = userManager;
    }

    /// <summary>
    /// 获取能耗记录分页列表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取能耗记录分页列表")]
    public async Task<SqlSugarPagedList<EnergyConsumptionOutput>> GetPage([FromQuery] EnergyConsumptionInput input)
    {
        var query = _energyConsumptionRep.AsQueryable()
            .LeftJoin<EnergyDevice>((c, d) => c.DeviceId == d.Id)
            .WhereIF(input.DeviceId > 0, (c, d) => c.DeviceId == input.DeviceId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeviceCode), (c, d) => d.DeviceCode.Contains(input.DeviceCode))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeviceCode), (c, d) => d.DeviceName.Contains(input.DeviceCode))
            .WhereIF(input.StartTime.HasValue, (c, d) => c.RecordTime >= input.StartTime)
            .WhereIF(input.EndTime.HasValue, (c, d) => c.RecordTime <= input.EndTime)
            .Select((c, d) => new EnergyConsumptionOutput
            {
                Id = c.Id,
                DeviceId = c.DeviceId,
                DeviceCode = d.DeviceCode,
                DeviceName = d.DeviceName,
                DeviceLocation = d.Location,
                RecordTime = c.RecordTime,
                Power = c.PowerConsumption,
                Voltage = c.Voltage,
                Current = c.Current,
                PowerFactor = c.PowerFactor,
                Frequency = c.Frequency,
                EnergyConsumption = c.EnergyValue,
                Temperature = c.Temperature,
                Humidity = c.Humidity,
                CreateTime = c.CreateTime,
                TenantId = c.TenantId
            })
            .OrderBy(c => c.RecordTime, OrderByType.Desc);

        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取能耗统计数据 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取能耗统计数据")]
    public async Task<EnergyConsumptionStatOutput> GetStat([FromQuery] EnergyConsumptionStatInput input)
    {
        var query = _energyConsumptionRep.AsQueryable()
            .LeftJoin<EnergyDevice>((c, d) => c.DeviceId == d.Id)
            .WhereIF(input.DeviceId > 0, (c, d) => c.DeviceId == input.DeviceId)
            .WhereIF(input.GroupId > 0, (c, d) => d.GroupId == input.GroupId)
            .WhereIF(input.StartTime.HasValue, (c, d) => c.RecordTime >= input.StartTime)
            .WhereIF(input.EndTime.HasValue, (c, d) => c.RecordTime <= input.EndTime);

        var statData = await query.Select((c, d) => new
        {
            PowerConsumption = c.PowerConsumption,
            EnergyValue = c.EnergyValue,
            c.DeviceId
        }).ToListAsync();

        if (!statData.Any())
        {
            return new EnergyConsumptionStatOutput
            {
                StatTime = DateTime.Now,
                DeviceCount = 0,
                TotalPower = 0,
                AveragePower = 0,
                MaxPower = 0,
                MinPower = 0,
                TotalEnergyConsumption = 0,
                AverageEnergyConsumption = 0,
                EnergySavingRate = 0,
                TotalCost = 0
            };
        }

        var deviceCount = statData.Select(x => x.DeviceId).Distinct().Count();
        var totalPower = statData.Sum(x => x.PowerConsumption);
        var totalEnergy = statData.Sum(x => x.EnergyValue ?? 0);
        var powers = statData.Select(x => x.PowerConsumption).ToList();
        var energies = statData.Where(x => x.EnergyValue.HasValue).Select(x => x.EnergyValue.Value).ToList();

        // 电费单价（元/kWh）
        const decimal electricityPrice = 0.6m;

        return new EnergyConsumptionStatOutput
        {
            StatTime = DateTime.Now,
            DeviceCount = deviceCount,
            TotalPower = totalPower,
            AveragePower = powers.Any() ? powers.Average() : 0,
            MaxPower = powers.Any() ? powers.Max() : 0,
            MinPower = powers.Any() ? powers.Min() : 0,
            TotalEnergyConsumption = totalEnergy,
            AverageEnergyConsumption = energies.Any() ? energies.Average() : 0,
            EnergySavingRate = await CalculateEnergySavingRate(input.DeviceId, input.StartTime, input.EndTime),
            TotalCost = totalEnergy * electricityPrice
        };
    }

    /// <summary>
    /// 获取能耗趋势数据 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取能耗趋势数据")]
    public async Task<List<EnergyConsumptionTrendOutput>> GetTrend([FromQuery] EnergyConsumptionStatInput input)
    {
        var query = _energyConsumptionRep.AsQueryable()
            .LeftJoin<EnergyDevice>((c, d) => c.DeviceId == d.Id)
            .WhereIF(input.DeviceId > 0, (c, d) => c.DeviceId == input.DeviceId)
            .WhereIF(input.GroupId > 0, (c, d) => d.GroupId == input.GroupId)
            .WhereIF(input.StartTime.HasValue, (c, d) => c.RecordTime >= input.StartTime)
            .WhereIF(input.EndTime.HasValue, (c, d) => c.RecordTime <= input.EndTime);

        // 按小时分组统计
        var trendData = await query
            .GroupBy((c, d) => new { Hour = SqlFunc.DateValue(c.RecordTime, DateType.Hour) })
            .Select((c, d) => new EnergyConsumptionTrendOutput
            {
                Time = c.RecordTime.Date.AddHours(c.RecordTime.Hour),
                Power = SqlFunc.AggregateAvg(c.PowerConsumption),
                Energy = SqlFunc.AggregateSum(c.EnergyValue) ?? 0m,
                Temperature = SqlFunc.AggregateAvg(c.Temperature) ?? 25.0m,
                Humidity = SqlFunc.AggregateAvg(c.Humidity) ?? 60.0m
            })
            .OrderBy(x => x.Time)
            .ToListAsync();

        return trendData;
    }

    /// <summary>
    /// 获取设备能耗排行 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取设备能耗排行")]
    public async Task<List<EnergyConsumptionRankOutput>> GetRank([FromQuery] EnergyConsumptionStatInput input)
    {
        var query = _energyConsumptionRep.AsQueryable()
            .LeftJoin<EnergyDevice>((c, d) => c.DeviceId == d.Id)
            .WhereIF(input.GroupId > 0, (c, d) => d.GroupId == input.GroupId)
            .WhereIF(input.StartTime.HasValue, (c, d) => c.RecordTime >= input.StartTime)
            .WhereIF(input.EndTime.HasValue, (c, d) => c.RecordTime <= input.EndTime);

        var rankData = await query
            .GroupBy((c, d) => new { c.DeviceId, d.DeviceCode, d.DeviceName, d.Location })
            .Select((c, d) => new EnergyConsumptionRankOutput
            {
                DeviceId = c.DeviceId,
                DeviceCode = d.DeviceCode,
                DeviceName = d.DeviceName,
                DeviceLocation = d.Location,
                TotalEnergyConsumption = SqlFunc.AggregateSum(c.EnergyValue) ?? 0m,
                AveragePower = SqlFunc.AggregateAvg(c.PowerConsumption),
                RunningTime = SqlFunc.AggregateCount(c.Id), // 记录数量作为运行时长的近似值
                TotalCost = (SqlFunc.AggregateSum(c.EnergyValue) ?? 0m) * 0.6m // 电费单价
            })
            .OrderBy(x => x.TotalEnergyConsumption, OrderByType.Desc)
            .ToListAsync();

        // 添加排名
        for (int i = 0; i < rankData.Count; i++)
        {
            rankData[i].Rank = i + 1;
        }

        return rankData;
    }

    /// <summary>
    /// 增加能耗记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [DisplayName("增加能耗记录")]
    public async Task<long> AddConsumption(AddEnergyConsumptionInput input)
    {
        // 检查设备是否存在
        var deviceExist = await _energyDeviceRep.IsAnyAsync(u => u.Id == input.DeviceId);
        if (!deviceExist)
            throw Oops.Oh("设备不存在");

        var consumption = input.Adapt<EnergyConsumption>();
        consumption.TenantId = _userManager.TenantId;
        consumption.CreateUserId = _userManager.UserId;
        consumption.CreateTime = DateTime.Now;

        var newConsumption = await _energyConsumptionRep.AsInsertable(consumption).ExecuteReturnEntityAsync();
        return newConsumption.Id;
    }

    /// <summary>
    /// 批量增加能耗记录 🔖
    /// </summary>
    /// <param name="inputList"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "BatchAdd"), HttpPost]
    [DisplayName("批量增加能耗记录")]
    public async Task<int> BatchAddConsumption(List<AddEnergyConsumptionInput> inputList)
    {
        if (!inputList.Any())
            return 0;

        // 检查设备是否存在
        var deviceIds = inputList.Select(x => x.DeviceId).Distinct().ToList();
        var existDeviceIds = await _energyDeviceRep.AsQueryable()
            .Where(u => deviceIds.Contains(u.Id))
            .Select(u => u.Id)
            .ToListAsync();

        var invalidDeviceIds = deviceIds.Except(existDeviceIds).ToList();
        if (invalidDeviceIds.Any())
            throw Oops.Oh($"设备不存在：{string.Join(",", invalidDeviceIds)}");

        var consumptions = inputList.Select(input =>
        {
            var consumption = input.Adapt<EnergyConsumption>();
            consumption.TenantId = _userManager.TenantId;
            consumption.CreateUserId = _userManager.UserId;
            consumption.CreateTime = DateTime.Now;
            return consumption;
        }).ToList();

        return await _energyConsumptionRep.AsInsertable(consumptions).ExecuteCommandAsync();
    }

    /// <summary>
    /// 更新能耗记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [DisplayName("更新能耗记录")]
    public async Task UpdateConsumption(UpdateEnergyConsumptionInput input)
    {
        // 检查记录是否存在
        var consumption = await _energyConsumptionRep.GetByIdAsync(input.Id);
        if (consumption == null)
            throw Oops.Oh("能耗记录不存在");

        // 检查设备是否存在
        var deviceExist = await _energyDeviceRep.IsAnyAsync(u => u.Id == input.DeviceId);
        if (!deviceExist)
            throw Oops.Oh("设备不存在");

        var updateConsumption = input.Adapt<EnergyConsumption>();
        updateConsumption.UpdateUserId = _userManager.UserId;
        updateConsumption.UpdateTime = DateTime.Now;

        await _energyConsumptionRep.AsUpdateable(updateConsumption).ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除能耗记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    [DisplayName("删除能耗记录")]
    public async Task DeleteConsumption(DeleteEnergyConsumptionInput input)
    {
        var consumption = await _energyConsumptionRep.GetByIdAsync(input.Id);
        if (consumption == null)
            throw Oops.Oh("能耗记录不存在");

        await _energyConsumptionRep.DeleteAsync(consumption);
    }

    /// <summary>
    /// 清理历史数据 🔖
    /// </summary>
    /// <param name="days">保留天数</param>
    /// <returns></returns>
    [DisplayName("清理历史数据")]
    public async Task<int> CleanHistoryData(int days = 90)
    {
        var cutoffDate = DateTime.Now.AddDays(-days);
        return await _energyConsumptionRep.AsDeleteable()
            .Where(u => u.CreateTime < cutoffDate)
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 导出能耗数据 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出能耗数据")]
    public async Task<byte[]> ExportData([FromQuery] EnergyConsumptionInput input)
    {
        var data = await _energyConsumptionRep.AsQueryable()
            .LeftJoin<EnergyDevice>((c, d) => c.DeviceId == d.Id)
            .WhereIF(input.DeviceId > 0, (c, d) => c.DeviceId == input.DeviceId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeviceCode), (c, d) => d.DeviceCode.Contains(input.DeviceCode))
            .WhereIF(input.StartTime.HasValue, (c, d) => c.RecordTime >= input.StartTime)
            .WhereIF(input.EndTime.HasValue, (c, d) => c.RecordTime <= input.EndTime)
            .OrderBy((c, d) => c.RecordTime, OrderByType.Desc)
            .Select((c, d) => new
            {
                设备编码 = d.DeviceCode,
                设备名称 = d.DeviceName,
                设备位置 = d.Location,
                记录时间 = c.RecordTime,
                功率_W = c.PowerConsumption,
                电压_V = c.Voltage,
                电流_A = c.Current,
                功率因数 = c.PowerFactor,
                频率_Hz = c.Frequency,
                能耗_kWh = c.EnergyValue,
                温度_C = c.Temperature,
                湿度_Percent = c.Humidity
            })
            .ToListAsync();

        // 实现Excel导出功能
        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add("能耗数据");
        
        // 设置表头
        var headers = new string[]
        {
            "设备编码", "设备名称", "设备位置", "记录时间", "功率(W)", 
            "电压(V)", "电流(A)", "功率因数", "频率(Hz)", "能耗(kWh)", "温度(°C)", "湿度(%)"
        };
        
        for (int i = 0; i < headers.Length; i++)
        {
            worksheet.Cells[1, i + 1].Value = headers[i];
            worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            worksheet.Cells[1, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
            worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(211, 211, 211, 255);
        }
        
        // 填充数据
        for (int row = 0; row < data.Count; row++)
        {
            var item = data[row];
            worksheet.Cells[row + 2, 1].Value = item.设备编码;
            worksheet.Cells[row + 2, 2].Value = item.设备名称;
            worksheet.Cells[row + 2, 3].Value = item.设备位置;
            worksheet.Cells[row + 2, 4].Value = item.记录时间.ToString("yyyy-MM-dd HH:mm:ss");
            worksheet.Cells[row + 2, 5].Value = item.功率_W;
            worksheet.Cells[row + 2, 6].Value = item.电压_V;
            worksheet.Cells[row + 2, 7].Value = item.电流_A;
            worksheet.Cells[row + 2, 8].Value = item.功率因数;
            worksheet.Cells[row + 2, 9].Value = item.频率_Hz;
            worksheet.Cells[row + 2, 10].Value = item.能耗_kWh;
            worksheet.Cells[row + 2, 11].Value = item.温度_C;
            worksheet.Cells[row + 2, 12].Value = item.湿度_Percent;
        }
        
        // 自动调整列宽
        worksheet.Cells.AutoFitColumns();
        
        // 设置边框
        var range = worksheet.Cells[1, 1, data.Count + 1, headers.Length];
        range.Style.Border.Top.Style = ExcelBorderStyle.Thin;
        range.Style.Border.Left.Style = ExcelBorderStyle.Thin;
        range.Style.Border.Right.Style = ExcelBorderStyle.Thin;
        range.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
        
        return package.GetAsByteArray();
    }

    /// <summary>
    /// 导出能耗数据到Excel 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "ExportExcel"), HttpPost]
    [DisplayName("导出能耗数据到Excel")]
    public async Task<IActionResult> ExportExcel(EnergyConsumptionInput input)
    {
        var excelData = await ExportData(input);
        var fileName = $"能耗数据_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
        
        return new FileContentResult(excelData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        {
            FileDownloadName = fileName
        };
    }

    /// <summary>
    /// 计算节能率
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <returns>节能率(%)</returns>
    private async Task<decimal> CalculateEnergySavingRate(long? deviceId, DateTime? startTime, DateTime? endTime)
    {
        if (!startTime.HasValue || !endTime.HasValue)
            return 0;

        // 计算当前周期的能耗
        var currentQuery = _energyConsumptionRep.AsQueryable()
            .WhereIF(deviceId > 0, c => c.DeviceId == deviceId)
            .Where(c => c.RecordTime >= startTime && c.RecordTime <= endTime);

        var currentConsumption = await currentQuery.SumAsync(c => c.EnergyValue ?? 0);

        // 计算同比周期（去年同期）的能耗
        var yearAgoStart = startTime.Value.AddYears(-1);
        var yearAgoEnd = endTime.Value.AddYears(-1);
        
        var yearAgoQuery = _energyConsumptionRep.AsQueryable()
            .WhereIF(deviceId > 0, c => c.DeviceId == deviceId)
            .Where(c => c.RecordTime >= yearAgoStart && c.RecordTime <= yearAgoEnd);

        var yearAgoConsumption = await yearAgoQuery.SumAsync(c => c.EnergyValue ?? 0);

        // 如果去年同期没有数据，计算环比（上个周期）
        if (yearAgoConsumption == 0)
        {
            var periodDays = (endTime.Value - startTime.Value).Days;
            var lastPeriodStart = startTime.Value.AddDays(-periodDays);
            var lastPeriodEnd = startTime.Value.AddDays(-1);
            
            var lastPeriodQuery = _energyConsumptionRep.AsQueryable()
                .WhereIF(deviceId > 0, c => c.DeviceId == deviceId)
                .Where(c => c.RecordTime >= lastPeriodStart && c.RecordTime <= lastPeriodEnd);

            var lastPeriodConsumption = await lastPeriodQuery.SumAsync(c => c.EnergyValue ?? 0);
            
            if (lastPeriodConsumption == 0)
                return 0;

            // 环比节能率 = (上期能耗 - 本期能耗) / 上期能耗 * 100
            return Math.Round((lastPeriodConsumption - currentConsumption) / lastPeriodConsumption * 100, 2);
        }

        // 同比节能率 = (去年同期能耗 - 本期能耗) / 去年同期能耗 * 100
        return Math.Round((yearAgoConsumption - currentConsumption) / yearAgoConsumption * 100, 2);
    }

    /// <summary>
    /// 获取节能率分析报告 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "EnergySavingAnalysis"), HttpPost]
    [DisplayName("获取节能率分析报告")]
    public async Task<EnergySavingAnalysisOutput> GetEnergySavingAnalysis(EnergyConsumptionStatInput input)
    {
        var currentPeriodStart = input.StartTime ?? DateTime.Now.Date.AddDays(-30);
        var currentPeriodEnd = input.EndTime ?? DateTime.Now.Date;
        
        // 当前周期能耗
        var currentQuery = _energyConsumptionRep.AsQueryable()
            .LeftJoin<EnergyDevice>((c, d) => c.DeviceId == d.Id)
            .WhereIF(input.DeviceId > 0, (c, d) => c.DeviceId == input.DeviceId)
            .WhereIF(input.GroupId > 0, (c, d) => d.GroupId == input.GroupId)
            .Where((c, d) => c.RecordTime >= currentPeriodStart && c.RecordTime <= currentPeriodEnd);

        var currentData = await currentQuery.Select((c, d) => new
        {
            EnergyValue = c.EnergyValue ?? 0,
            PowerConsumption = c.PowerConsumption,
            DeviceId = c.DeviceId
        }).ToListAsync();

        var currentTotalEnergy = currentData.Sum(x => x.EnergyValue);
        var currentAvgPower = currentData.Any() ? currentData.Average(x => x.PowerConsumption) : 0;
        var currentDeviceCount = currentData.Select(x => x.DeviceId).Distinct().Count();

        // 对比周期（去年同期或上个周期）
        var periodDays = (currentPeriodEnd - currentPeriodStart).Days;
        var comparePeriodStart = currentPeriodStart.AddYears(-1);
        var comparePeriodEnd = currentPeriodEnd.AddYears(-1);
        
        var compareQuery = _energyConsumptionRep.AsQueryable()
            .LeftJoin<EnergyDevice>((c, d) => c.DeviceId == d.Id)
            .WhereIF(input.DeviceId > 0, (c, d) => c.DeviceId == input.DeviceId)
            .WhereIF(input.GroupId > 0, (c, d) => d.GroupId == input.GroupId)
            .Where((c, d) => c.RecordTime >= comparePeriodStart && c.RecordTime <= comparePeriodEnd);

        var compareData = await compareQuery.Select((c, d) => new
        {
            EnergyValue = c.EnergyValue ?? 0,
            PowerConsumption = c.PowerConsumption,
            DeviceId = c.DeviceId
        }).ToListAsync();

        var compareTotalEnergy = compareData.Sum(x => x.EnergyValue);
        var compareAvgPower = compareData.Any() ? compareData.Average(x => x.PowerConsumption) : 0;
        
        // 如果去年同期没有数据，使用上个周期数据
        if (compareTotalEnergy == 0)
        {
            comparePeriodStart = currentPeriodStart.AddDays(-periodDays);
            comparePeriodEnd = currentPeriodStart.AddDays(-1);
            
            compareQuery = _energyConsumptionRep.AsQueryable()
                .LeftJoin<EnergyDevice>((c, d) => c.DeviceId == d.Id)
                .WhereIF(input.DeviceId > 0, (c, d) => c.DeviceId == input.DeviceId)
                .WhereIF(input.GroupId > 0, (c, d) => d.GroupId == input.GroupId)
                .Where((c, d) => c.RecordTime >= comparePeriodStart && c.RecordTime <= comparePeriodEnd);

            compareData = await compareQuery.Select((c, d) => new
            {
                EnergyValue = c.EnergyValue ?? 0,
                PowerConsumption = c.PowerConsumption,
                DeviceId = c.DeviceId
            }).ToListAsync();

            compareTotalEnergy = compareData.Sum(x => x.EnergyValue);
            compareAvgPower = compareData.Any() ? compareData.Average(x => x.PowerConsumption) : 0;
        }

        // 计算节能指标
        var energySavingRate = compareTotalEnergy > 0 ? 
            Math.Round((compareTotalEnergy - currentTotalEnergy) / compareTotalEnergy * 100, 2) : 0;
        var powerSavingRate = compareAvgPower > 0 ? 
            Math.Round((compareAvgPower - currentAvgPower) / compareAvgPower * 100, 2) : 0;
        var energySaved = compareTotalEnergy - currentTotalEnergy;
        var costSaved = energySaved * 0.6m; // 电费单价

        return new EnergySavingAnalysisOutput
        {
            AnalysisTime = DateTime.Now,
            CurrentPeriod = new PeriodData
            {
                StartTime = currentPeriodStart,
                EndTime = currentPeriodEnd,
                TotalEnergy = currentTotalEnergy,
                AveragePower = currentAvgPower,
                DeviceCount = currentDeviceCount,
                TotalCost = currentTotalEnergy * 0.6m
            },
            ComparePeriod = new PeriodData
            {
                StartTime = comparePeriodStart,
                EndTime = comparePeriodEnd,
                TotalEnergy = compareTotalEnergy,
                AveragePower = compareAvgPower,
                DeviceCount = currentDeviceCount,
                TotalCost = compareTotalEnergy * 0.6m
            },
            EnergySavingRate = energySavingRate,
            PowerSavingRate = powerSavingRate,
            EnergySaved = energySaved,
            CostSaved = costSaved,
            ComparisonType = compareTotalEnergy > 0 && comparePeriodStart.Year < currentPeriodStart.Year ? "同比" : "环比"
        };
    }
}