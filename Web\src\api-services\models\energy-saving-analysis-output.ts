/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { PeriodData } from './period-data';
/**
 * 节能率分析输出参数
 * @export
 * @interface EnergySavingAnalysisOutput
 */
export interface EnergySavingAnalysisOutput {
    /**
     * 分析时间
     * @type {Date}
     * @memberof EnergySavingAnalysisOutput
     */
    analysisTime?: Date;
    /**
     * 
     * @type {PeriodData}
     * @memberof EnergySavingAnalysisOutput
     */
    currentPeriod?: PeriodData;
    /**
     * 
     * @type {PeriodData}
     * @memberof EnergySavingAnalysisOutput
     */
    comparePeriod?: PeriodData;
    /**
     * 节能率(%)
     * @type {number}
     * @memberof EnergySavingAnalysisOutput
     */
    energySavingRate?: number;
    /**
     * 功率节约率(%)
     * @type {number}
     * @memberof EnergySavingAnalysisOutput
     */
    powerSavingRate?: number;
    /**
     * 节约能耗(kWh)
     * @type {number}
     * @memberof EnergySavingAnalysisOutput
     */
    energySaved?: number;
    /**
     * 节约费用(元)
     * @type {number}
     * @memberof EnergySavingAnalysisOutput
     */
    costSaved?: number;
    /**
     * 对比类型（同比/环比）
     * @type {string}
     * @memberof EnergySavingAnalysisOutput
     */
    comparisonType?: string | null;
    /**
     * 节能建议
     * @type {Array<string>}
     * @memberof EnergySavingAnalysisOutput
     */
    suggestions?: Array<string> | null;
}
