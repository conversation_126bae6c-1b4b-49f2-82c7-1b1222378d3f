<template>
  <view class="schedule-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">定时任务</text>
      <view class="header-actions">
        <button class="btn-add" @click="showAddModal">
          <text class="icon">➕</text>
          <text>新建任务</text>
        </button>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-container">
      <view class="stat-card">
        <text class="stat-value">{{ stats.totalTasks }}</text>
        <text class="stat-label">总任务数</text>
      </view>
      <view class="stat-card">
        <text class="stat-value">{{ stats.activeTasks }}</text>
        <text class="stat-label">启用任务</text>
      </view>
      <view class="stat-card">
        <text class="stat-value">{{ stats.todayExecuted }}</text>
        <text class="stat-label">今日执行</text>
      </view>
      <view class="stat-card">
        <text class="stat-value">{{ stats.nextExecution }}</text>
        <text class="stat-label">下次执行</text>
      </view>
    </view>

    <!-- 筛选和排序 -->
    <view class="filter-container">
      <view class="filter-tabs">
        <view 
          v-for="tab in filterTabs" 
          :key="tab.value"
          class="filter-tab"
          :class="{ active: activeFilter === tab.value }"
          @click="switchFilter(tab.value)"
        >
          <text>{{ tab.label }}</text>
          <text class="tab-count">({{ getFilterCount(tab.value) }})</text>
        </view>
      </view>
      <view class="sort-actions">
        <button class="btn-sort" @click="toggleSort">
          <text class="icon">{{ sortOrder === 'asc' ? '⬆️' : '⬇️' }}</text>
          <text>{{ sortOrder === 'asc' ? '升序' : '降序' }}</text>
        </button>
      </view>
    </view>

    <!-- 任务列表 -->
    <view class="task-list">
      <view 
        v-for="task in filteredTasks" 
        :key="task.id"
        class="task-item"
        @click="viewTaskDetail(task)"
      >
        <view class="task-header">
          <view class="task-info">
            <text class="task-name">{{ task.name }}</text>
            <view class="task-status" :class="getStatusClass(task.status)">
              <text>{{ getStatusText(task.status) }}</text>
            </view>
          </view>
          <view class="task-actions">
            <switch 
              :checked="task.enabled" 
              @change="toggleTask(task)"
              @click.stop
            />
          </view>
        </view>
        
        <view class="task-details">
          <view class="task-schedule">
            <text class="schedule-icon">⏰</text>
            <text class="schedule-text">{{ formatSchedule(task.schedule) }}</text>
          </view>
          <view class="task-devices">
            <text class="devices-icon">💡</text>
            <text class="devices-text">{{ task.deviceCount }}个设备</text>
          </view>
        </view>
        
        <view class="task-execution">
          <view class="execution-info">
            <text class="last-execution">上次执行：{{ formatTime(task.lastExecution) }}</text>
            <text class="next-execution">下次执行：{{ formatTime(task.nextExecution) }}</text>
          </view>
          <view class="execution-result" :class="getResultClass(task.lastResult)">
            <text>{{ getResultText(task.lastResult) }}</text>
          </view>
        </view>
        
        <view class="task-controls">
          <button class="btn-edit" @click.stop="editTask(task)">
            <text>编辑</text>
          </button>
          <button class="btn-execute" @click.stop="executeTask(task)" :disabled="!task.enabled">
            <text>立即执行</text>
          </button>
          <button class="btn-delete" @click.stop="deleteTask(task)">
            <text>删除</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-if="filteredTasks.length === 0" class="empty-state">
      <text class="empty-icon">📅</text>
      <text class="empty-text">暂无定时任务</text>
      <button class="btn-create" @click="showAddModal">
        <text>创建第一个任务</text>
      </button>
    </view>

    <!-- 新建/编辑任务弹窗 -->
    <uni-popup ref="taskModal" type="center">
      <view class="task-modal">
        <view class="modal-header">
          <text class="modal-title">{{ isEditing ? '编辑任务' : '新建任务' }}</text>
          <button class="btn-close" @click="closeTaskModal">✕</button>
        </view>
        
        <view class="modal-content">
          <view class="form-item">
            <text class="form-label">任务名称</text>
            <input 
              v-model="taskForm.name" 
              class="form-input" 
              placeholder="请输入任务名称"
              maxlength="50"
            />
          </view>
          
          <view class="form-item">
            <text class="form-label">任务描述</text>
            <textarea 
              v-model="taskForm.description" 
              class="form-textarea" 
              placeholder="请输入任务描述（可选）"
              maxlength="200"
            />
          </view>
          
          <view class="form-item">
            <text class="form-label">执行类型</text>
            <picker 
              :value="taskForm.scheduleType" 
              :range="scheduleTypes" 
              range-key="label"
              @change="updateScheduleType"
            >
              <view class="picker-text">{{ getScheduleTypeLabel(taskForm.scheduleType) }}</view>
            </picker>
          </view>
          
          <!-- 一次性任务 -->
          <view v-if="taskForm.scheduleType === 'once'" class="form-item">
            <text class="form-label">执行时间</text>
            <picker 
              mode="multiSelector" 
              :value="[dateIndex, timeIndex]" 
              :range="[dateRange, timeRange]"
              @change="updateDateTime"
            >
              <view class="picker-text">{{ formatDateTime() }}</view>
            </picker>
          </view>
          
          <!-- 每日任务 -->
          <view v-if="taskForm.scheduleType === 'daily'" class="form-item">
            <text class="form-label">执行时间</text>
            <picker 
              mode="time" 
              :value="taskForm.time" 
              @change="updateTime"
            >
              <view class="picker-text">{{ taskForm.time || '请选择时间' }}</view>
            </picker>
          </view>
          
          <!-- 每周任务 -->
          <view v-if="taskForm.scheduleType === 'weekly'" class="form-item">
            <text class="form-label">执行星期</text>
            <view class="weekday-selector">
              <view 
                v-for="(day, index) in weekdays" 
                :key="index"
                class="weekday-item"
                :class="{ active: taskForm.weekdays.includes(index) }"
                @click="toggleWeekday(index)"
              >
                <text>{{ day }}</text>
              </view>
            </view>
            <text class="form-label">执行时间</text>
            <picker 
              mode="time" 
              :value="taskForm.time" 
              @change="updateTime"
            >
              <view class="picker-text">{{ taskForm.time || '请选择时间' }}</view>
            </picker>
          </view>
          
          <!-- 每月任务 -->
          <view v-if="taskForm.scheduleType === 'monthly'" class="form-item">
            <text class="form-label">执行日期</text>
            <picker 
              :value="taskForm.monthDay - 1" 
              :range="monthDays"
              @change="updateMonthDay"
            >
              <view class="picker-text">每月{{ taskForm.monthDay }}日</view>
            </picker>
            <text class="form-label">执行时间</text>
            <picker 
              mode="time" 
              :value="taskForm.time" 
              @change="updateTime"
            >
              <view class="picker-text">{{ taskForm.time || '请选择时间' }}</view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="form-label">执行动作</text>
            <picker 
              :value="taskForm.action" 
              :range="actionTypes" 
              range-key="label"
              @change="updateAction"
            >
              <view class="picker-text">{{ getActionLabel(taskForm.action) }}</view>
            </picker>
          </view>
          
          <!-- 亮度设置 -->
          <view v-if="taskForm.action === 'setBrightness'" class="form-item">
            <text class="form-label">目标亮度：{{ taskForm.brightness }}%</text>
            <slider 
              :value="taskForm.brightness" 
              min="10" 
              max="100" 
              step="10"
              activeColor="#1890ff"
              @change="updateBrightness"
            />
          </view>
          
          <!-- 情景模式选择 -->
          <view v-if="taskForm.action === 'setScene'" class="form-item">
            <text class="form-label">选择情景模式</text>
            <picker 
              :value="taskForm.sceneId" 
              :range="availableScenes" 
              range-key="name"
              @change="updateScene"
            >
              <view class="picker-text">{{ getSceneLabel(taskForm.sceneId) }}</view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="form-label">选择设备</text>
            <button class="btn-select-devices" @click="showDeviceSelector">
              <text>已选择 {{ taskForm.deviceIds.length }} 个设备</text>
              <text class="icon">▶</text>
            </button>
          </view>
          
          <view class="form-item">
            <view class="form-switch">
              <text class="form-label">启用任务</text>
              <input type="checkbox" v-model="taskForm.enabled" class="form-switch-input" />
            </view>
          </view>
        </view>
        
        <view class="modal-actions">
          <button class="btn-cancel" @click="closeTaskModal">取消</button>
          <button class="btn-save" @click="saveTask">{{ isEditing ? '保存' : '创建' }}</button>
        </view>
      </view>
    </uni-popup>

    <!-- 设备选择弹窗 -->
    <uni-popup ref="deviceModal" type="center">
      <view class="device-modal">
        <view class="modal-header">
          <text class="modal-title">选择设备</text>
          <button class="btn-close" @click="closeDeviceModal">✕</button>
        </view>
        
        <view class="modal-content">
          <view class="device-list">
            <label 
              v-for="device in availableDevices" 
              :key="device.id"
              class="device-checkbox"
            >
              <checkbox 
                :value="device.id" 
                :checked="taskForm.deviceIds.includes(device.id)"
                @change="toggleDeviceSelection(device.id)"
              />
              <view class="device-info">
                <text class="device-name">{{ device.name }}</text>
                <text class="device-location">{{ device.location }}</text>
              </view>
              <view class="device-status" :class="getStatusClass(device.status)">
                <text>{{ getStatusText(device.status) }}</text>
              </view>
            </label>
          </view>
        </view>
        
        <view class="modal-actions">
          <button class="btn-cancel" @click="closeDeviceModal">取消</button>
          <button class="btn-confirm" @click="confirmDeviceSelection">确定</button>
        </view>
      </view>
    </uni-popup>

    <!-- 任务详情弹窗 -->
    <uni-popup ref="detailModal" type="center">
      <view class="detail-modal">
        <view class="modal-header">
          <text class="modal-title">任务详情</text>
          <button class="btn-close" @click="closeDetailModal">✕</button>
        </view>
        
        <view class="modal-content" v-if="selectedTask">
          <view class="detail-section">
            <text class="section-title">基本信息</text>
            <view class="detail-item">
              <text class="detail-label">任务名称：</text>
              <text class="detail-value">{{ selectedTask.name }}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">任务描述：</text>
              <text class="detail-value">{{ selectedTask.description || '无' }}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">执行计划：</text>
              <text class="detail-value">{{ formatSchedule(selectedTask.schedule) }}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">执行动作：</text>
              <text class="detail-value">{{ getActionLabel(selectedTask.action) }}</text>
            </view>
          </view>
          
          <view class="detail-section">
            <text class="section-title">执行历史</text>
            <view class="execution-history">
              <view 
                v-for="record in selectedTask.executionHistory" 
                :key="record.id"
                class="history-item"
              >
                <view class="history-time">{{ formatTime(record.executeTime) }}</view>
                <view class="history-result" :class="getResultClass(record.result)">
                  <text>{{ getResultText(record.result) }}</text>
                </view>
                <view class="history-details">{{ record.details }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnload } from 'vue'
import mqttClient from '@/utils/mqtt.js'

export default {
  name: 'SchedulePage',
  setup() {
    // 响应式数据
    const stats = reactive({
      totalTasks: 0,
      activeTasks: 0,
      todayExecuted: 0,
      nextExecution: '2小时后'
    })

    const tasks = ref([])
    const availableDevices = ref([])
    const availableScenes = ref([])
    const activeFilter = ref('all')
    const sortOrder = ref('desc')
    const isEditing = ref(false)
    const selectedTask = ref(null)
    
    const filterTabs = [
      { value: 'all', label: '全部' },
      { value: 'enabled', label: '已启用' },
      { value: 'disabled', label: '已禁用' },
      { value: 'success', label: '执行成功' },
      { value: 'failed', label: '执行失败' }
    ]
    
    const scheduleTypes = [
      { value: 'once', label: '一次性' },
      { value: 'daily', label: '每日' },
      { value: 'weekly', label: '每周' },
      { value: 'monthly', label: '每月' }
    ]
    
    const actionTypes = [
      { value: 'turnOn', label: '开启设备' },
      { value: 'turnOff', label: '关闭设备' },
      { value: 'setBrightness', label: '设置亮度' },
      { value: 'toggle', label: '切换状态' },
      { value: 'setScene', label: '触发情景模式' }
    ]
    
    const weekdays = ['日', '一', '二', '三', '四', '五', '六']
    const monthDays = Array.from({ length: 31 }, (_, i) => `${i + 1}日`)
    
    // 表单数据
    const taskForm = reactive({
      id: null,
      name: '',
      description: '',
      scheduleType: 'daily',
      time: '',
      weekdays: [],
      monthDay: 1,
      action: 'turnOn',
      brightness: 80,
      sceneId: null,
      deviceIds: [],
      enabled: true
    })
    
    const dateIndex = ref(0)
    const timeIndex = ref(0)
    const dateRange = ref([])
    const timeRange = ref([])

    // 计算属性
    const filteredTasks = computed(() => {
      let result = tasks.value
      
      // 按状态筛选
      switch (activeFilter.value) {
        case 'enabled':
          result = result.filter(t => t.enabled)
          break
        case 'disabled':
          result = result.filter(t => !t.enabled)
          break
        case 'success':
          result = result.filter(t => t.lastResult === 'success')
          break
        case 'failed':
          result = result.filter(t => t.lastResult === 'failed')
          break
      }
      
      // 排序
      result.sort((a, b) => {
        const aTime = new Date(a.nextExecution).getTime()
        const bTime = new Date(b.nextExecution).getTime()
        return sortOrder.value === 'asc' ? aTime - bTime : bTime - aTime
      })
      
      return result
    })

    // 方法
    const initData = () => {
      // 模拟任务数据
      tasks.value = [
        {
          id: 1,
          name: '早晨开灯',
          description: '每天早上7点自动开启办公区域照明',
          scheduleType: 'daily',
          schedule: { type: 'daily', time: '07:00' },
          action: 'turnOn',
          deviceCount: 5,
          enabled: true,
          status: 'active',
          lastExecution: '2024-01-15 07:00:00',
          nextExecution: '2024-01-16 07:00:00',
          lastResult: 'success',
          executionHistory: [
            {
              id: 1,
              executeTime: '2024-01-15 07:00:00',
              result: 'success',
              details: '成功开启5个设备'
            },
            {
              id: 2,
              executeTime: '2024-01-14 07:00:00',
              result: 'success',
              details: '成功开启5个设备'
            }
          ]
        },
        {
          id: 2,
          name: '晚间关灯',
          description: '每天晚上22点自动关闭所有照明',
          scheduleType: 'daily',
          schedule: { type: 'daily', time: '22:00' },
          action: 'turnOff',
          deviceCount: 8,
          enabled: true,
          status: 'active',
          lastExecution: '2024-01-15 22:00:00',
          nextExecution: '2024-01-16 22:00:00',
          lastResult: 'success',
          executionHistory: [
            {
              id: 3,
              executeTime: '2024-01-15 22:00:00',
              result: 'success',
              details: '成功关闭8个设备'
            }
          ]
        },
        {
          id: 3,
          name: '周末节能模式',
          description: '周末降低亮度以节约能源',
          scheduleType: 'weekly',
          schedule: { type: 'weekly', weekdays: [0, 6], time: '08:00' },
          action: 'setBrightness',
          brightness: 60,
          deviceCount: 10,
          enabled: false,
          status: 'inactive',
          lastExecution: '2024-01-14 08:00:00',
          nextExecution: '2024-01-20 08:00:00',
          lastResult: 'failed',
          executionHistory: [
            {
              id: 4,
              executeTime: '2024-01-14 08:00:00',
              result: 'failed',
              details: '2个设备离线，无法执行'
            }
          ]
        }
      ]
      
      // 模拟设备数据
      availableDevices.value = [
        { id: 1, name: '办公室主灯', location: '办公楼A座201', status: 'online' },
        { id: 2, name: '会议室灯光', location: '办公楼A座301', status: 'online' },
        { id: 3, name: '走廊照明', location: '办公楼A座走廊', status: 'offline' },
        { id: 4, name: '车间照明1', location: '生产车间1号', status: 'online' }
      ]
      
      // 模拟情景模式数据
      availableScenes.value = [
        { id: 1, name: '办公模式', description: '适合日常办公的照明配置' },
        { id: 2, name: '会议模式', description: '会议室专用照明配置' },
        { id: 3, name: '节能模式', description: '低功耗节能照明配置' },
        { id: 4, name: '夜间模式', description: '夜间值班照明配置' },
        { id: 5, name: '展示模式', description: '展厅展示专用照明配置' }
      ]
      
      // 生成日期和时间选项
      generateDateTimeOptions()
      
      updateStats()
    }
    
    const generateDateTimeOptions = () => {
      // 生成未来7天的日期选项
      const dates = []
      const times = []
      
      for (let i = 0; i < 7; i++) {
        const date = new Date()
        date.setDate(date.getDate() + i)
        dates.push(date.toLocaleDateString())
      }
      
      // 生成24小时时间选项
      for (let h = 0; h < 24; h++) {
        for (let m = 0; m < 60; m += 30) {
          const hour = h.toString().padStart(2, '0')
          const minute = m.toString().padStart(2, '0')
          times.push(`${hour}:${minute}`)
        }
      }
      
      dateRange.value = dates
      timeRange.value = times
    }

    const updateStats = () => {
      stats.totalTasks = tasks.value.length
      stats.activeTasks = tasks.value.filter(t => t.enabled).length
      stats.todayExecuted = tasks.value.filter(t => {
        const today = new Date().toDateString()
        return new Date(t.lastExecution).toDateString() === today
      }).length
    }

    const getFilterCount = (filter) => {
      switch (filter) {
        case 'all': return tasks.value.length
        case 'enabled': return tasks.value.filter(t => t.enabled).length
        case 'disabled': return tasks.value.filter(t => !t.enabled).length
        case 'success': return tasks.value.filter(t => t.lastResult === 'success').length
        case 'failed': return tasks.value.filter(t => t.lastResult === 'failed').length
        default: return 0
      }
    }

    const switchFilter = (filter) => {
      activeFilter.value = filter
    }

    const toggleSort = () => {
      sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
    }

    const formatSchedule = (schedule) => {
      switch (schedule.type) {
        case 'once':
          return `一次性 ${schedule.datetime}`
        case 'daily':
          return `每日 ${schedule.time}`
        case 'weekly': {
          const days = schedule.weekdays.map(d => weekdays[d]).join('、')
          return `每周${days} ${schedule.time}`
        }
        case 'monthly':
          return `每月${schedule.day}日 ${schedule.time}`
        default:
          return '未知'
      }
    }

    const formatTime = (timeStr) => {
      if (!timeStr) return '未执行'
      const date = new Date(timeStr)
      return date.toLocaleString()
    }

    const getStatusClass = (status) => {
      return {
        'status-active': status === 'active' || status === 'online',
        'status-inactive': status === 'inactive' || status === 'offline',
        'status-fault': status === 'fault'
      }
    }

    const getStatusText = (status) => {
      const statusMap = {
        active: '活跃',
        inactive: '非活跃',
        online: '在线',
        offline: '离线',
        fault: '故障'
      }
      return statusMap[status] || '未知'
    }

    const getResultClass = (result) => {
      return {
        'result-success': result === 'success',
        'result-failed': result === 'failed',
        'result-pending': result === 'pending'
      }
    }

    const getResultText = (result) => {
      const resultMap = {
        success: '成功',
        failed: '失败',
        pending: '等待中'
      }
      return resultMap[result] || '未知'
    }

    const toggleTask = (task) => {
      task.enabled = !task.enabled
      task.status = task.enabled ? 'active' : 'inactive'
      updateStats()
      uni.showToast({ 
        title: `任务已${task.enabled ? '启用' : '禁用'}`, 
        icon: 'success' 
      })
    }

    const executeTask = (task) => {
      uni.showLoading({ title: '执行中...' })
      
      // 清除之前的定时器
      if (executeTimer.value) {
        clearTimeout(executeTimer.value)
        executeTimer.value = null
      }
      
      executeTimer.value = setTimeout(async () => {
        try {
          let executionDetails = `手动执行成功，影响${task.deviceCount}个设备`
          
          // 如果是情景模式任务，发送MQTT消息
          if (task.action === 'setScene' && task.sceneId) {
            const scene = availableScenes.value.find(s => s.id === task.sceneId)
            if (scene) {
              // 这里应该根据实际的情景配置构建sceneConfig
              // 暂时使用模拟数据
              const sceneConfig = {
                deviceName: 'ScheduleTask',
                area: '0001',
                address: '0001',
                sceneId: task.sceneId.toString().padStart(2, '0'),
                identity: '01',
                lampGroups: [{
                  groupId: '01',
                  occupiedBrightness: 80,
                  unoccupiedBrightness: 30,
                  colorTemperature: 4000,
                  firstDelay: 60,
                  secondDelay: 30,
                  lightMode: 1,
                  sensorMode: 1,
                  alsMode: 1
                }]
              }
              
              await mqttClient.setScene(sceneConfig)
              executionDetails = `成功触发情景模式：${scene.name}`
            }
          }
          
          task.lastExecution = new Date().toISOString()
          task.lastResult = 'success'
          task.executionHistory.unshift({
            id: Date.now(),
            executeTime: task.lastExecution,
            result: 'success',
            details: executionDetails
          })
          updateStats()
          uni.hideLoading()
          uni.showToast({ title: '执行成功', icon: 'success' })
        } catch (error) {
          console.error('任务执行失败:', error)
          task.lastExecution = new Date().toISOString()
          task.lastResult = 'failed'
          task.executionHistory.unshift({
            id: Date.now(),
            executeTime: task.lastExecution,
            result: 'failed',
            details: `执行失败：${error.message || '未知错误'}`
          })
          updateStats()
          uni.hideLoading()
          uni.showToast({ title: '执行失败', icon: 'error' })
        }
        executeTimer.value = null
      }, 2000)
    }

    const deleteTask = (task) => {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除任务"${task.name}"吗？`,
        success: (res) => {
          if (res.confirm) {
            const index = tasks.value.findIndex(t => t.id === task.id)
            if (index > -1) {
              tasks.value.splice(index, 1)
              updateStats()
              uni.showToast({ title: '删除成功', icon: 'success' })
            }
          }
        }
      })
    }

    const viewTaskDetail = (task) => {
      selectedTask.value = task
      uni.$refs.detailModal.open()
    }

    // 弹窗相关方法
    const showAddModal = () => {
      isEditing.value = false
      resetTaskForm()
      uni.$refs.taskModal.open()
    }

    const editTask = (task) => {
      isEditing.value = true
      Object.assign(taskForm, {
        id: task.id,
        name: task.name,
        description: task.description,
        scheduleType: task.schedule.type,
        time: task.schedule.time,
        weekdays: task.schedule.weekdays || [],
        monthDay: task.schedule.day || 1,
        action: task.action,
        brightness: task.brightness || 80,
        sceneId: task.sceneId || null,
        deviceIds: [], // 这里应该从任务中获取实际的设备ID
        enabled: task.enabled
      })
      uni.$refs.taskModal.open()
    }

    const resetTaskForm = () => {
      Object.assign(taskForm, {
        id: null,
        name: '',
        description: '',
        scheduleType: 'daily',
        time: '',
        weekdays: [],
        monthDay: 1,
        action: 'turnOn',
        brightness: 80,
        sceneId: null,
        deviceIds: [],
        enabled: true
      })
    }

    const closeTaskModal = () => {
      uni.$refs.taskModal.close()
    }

    const closeDetailModal = () => {
      uni.$refs.detailModal.close()
    }

    const closeDeviceModal = () => {
      uni.$refs.deviceModal.close()
    }

    const saveTask = () => {
      if (!taskForm.name.trim()) {
        uni.showToast({ title: '请输入任务名称', icon: 'error' })
        return
      }
      
      if (!taskForm.time && taskForm.scheduleType !== 'once') {
        uni.showToast({ title: '请选择执行时间', icon: 'error' })
        return
      }
      
      if (taskForm.deviceIds.length === 0) {
        uni.showToast({ title: '请选择执行设备', icon: 'error' })
        return
      }
      
      // 情景模式校验
      if (taskForm.action === 'setScene' && !taskForm.sceneId) {
        uni.showToast({ title: '请选择情景模式', icon: 'error' })
        return
      }
      
      const taskData = {
        id: taskForm.id || Date.now(),
        name: taskForm.name,
        description: taskForm.description,
        scheduleType: taskForm.scheduleType,
        schedule: buildSchedule(),
        action: taskForm.action,
        brightness: taskForm.brightness,
        sceneId: taskForm.sceneId,
        deviceCount: taskForm.deviceIds.length,
        enabled: taskForm.enabled,
        status: taskForm.enabled ? 'active' : 'inactive',
        lastExecution: null,
        nextExecution: calculateNextExecution(),
        lastResult: 'pending',
        executionHistory: []
      }
      
      if (isEditing.value) {
        const index = tasks.value.findIndex(t => t.id === taskForm.id)
        if (index > -1) {
          tasks.value[index] = { ...tasks.value[index], ...taskData }
        }
        uni.showToast({ title: '保存成功', icon: 'success' })
      } else {
        tasks.value.push(taskData)
        uni.showToast({ title: '创建成功', icon: 'success' })
      }
      
      updateStats()
      closeTaskModal()
    }

    const buildSchedule = () => {
      const schedule = { type: taskForm.scheduleType }
      
      switch (taskForm.scheduleType) {
        case 'once':
          schedule.datetime = formatDateTime()
          break
        case 'daily':
          schedule.time = taskForm.time
          break
        case 'weekly':
          schedule.weekdays = taskForm.weekdays
          schedule.time = taskForm.time
          break
        case 'monthly':
          schedule.day = taskForm.monthDay
          schedule.time = taskForm.time
          break
      }
      
      return schedule
    }

    const calculateNextExecution = () => {
      // 简化的下次执行时间计算
      const now = new Date()
      const tomorrow = new Date(now)
      tomorrow.setDate(tomorrow.getDate() + 1)
      return tomorrow.toISOString()
    }

    // 表单相关方法
    const updateScheduleType = (event) => {
      taskForm.scheduleType = scheduleTypes[event.detail.value].value
    }

    const updateDateTime = (event) => {
      dateIndex.value = event.detail.value[0]
      timeIndex.value = event.detail.value[1]
    }

    const updateTime = (event) => {
      taskForm.time = event.detail.value
    }

    const updateMonthDay = (event) => {
      taskForm.monthDay = event.detail.value + 1
    }

    const updateAction = (event) => {
      taskForm.action = actionTypes[event.detail.value].value
    }

    const updateBrightness = (event) => {
      taskForm.brightness = event.detail.value
    }

    const toggleWeekday = (dayIndex) => {
      const index = taskForm.weekdays.indexOf(dayIndex)
      if (index > -1) {
        taskForm.weekdays.splice(index, 1)
      } else {
        taskForm.weekdays.push(dayIndex)
      }
    }

    const formatDateTime = () => {
      if (dateRange.value.length === 0 || timeRange.value.length === 0) {
        return '请选择日期时间'
      }
      return `${dateRange.value[dateIndex.value]} ${timeRange.value[timeIndex.value]}`
    }

    const getScheduleTypeLabel = (value) => {
      return scheduleTypes.find(type => type.value === value)?.label || '请选择'
    }

    const getActionLabel = (value) => {
      return actionTypes.find(action => action.value === value)?.label || '请选择'
    }
    
    /**
     * 更新选择的情景模式
     * @param {Object} event - 选择器事件对象
     */
    const updateScene = (event) => {
      taskForm.sceneId = availableScenes.value[event.detail.value].id
    }
    
    /**
     * 获取情景模式标签
     * @param {number} sceneId - 情景模式ID
     * @returns {string} 情景模式名称
     */
    const getSceneLabel = (sceneId) => {
      const scene = availableScenes.value.find(s => s.id === sceneId)
      return scene ? scene.name : '请选择情景模式'
    }

    const showDeviceSelector = () => {
      uni.$refs.deviceModal.open()
    }

    const toggleDeviceSelection = (deviceId) => {
      const index = taskForm.deviceIds.indexOf(deviceId)
      if (index > -1) {
        taskForm.deviceIds.splice(index, 1)
      } else {
        taskForm.deviceIds.push(deviceId)
      }
    }

    const confirmDeviceSelection = () => {
      closeDeviceModal()
    }

    // 定时器变量
    const executeTimer = ref(null)

    // 生命周期
    onMounted(() => {
      initData()
    })

    onUnload(() => {
      // 清理定时器
      if (executeTimer.value) {
        clearTimeout(executeTimer.value)
        executeTimer.value = null
      }
    })

    return {
      stats,
      tasks,
      availableDevices,
      availableScenes,
      activeFilter,
      sortOrder,
      isEditing,
      selectedTask,
      filterTabs,
      scheduleTypes,
      actionTypes,
      weekdays,
      monthDays,
      taskForm,
      dateIndex,
      timeIndex,
      dateRange,
      timeRange,
      executeTimer,
      filteredTasks,
      getFilterCount,
      switchFilter,
      toggleSort,
      formatSchedule,
      formatTime,
      getStatusClass,
      getStatusText,
      getResultClass,
      getResultText,
      toggleTask,
      executeTask,
      deleteTask,
      viewTaskDetail,
      showAddModal,
      editTask,
      closeTaskModal,
      closeDetailModal,
      closeDeviceModal,
      saveTask,
      updateScheduleType,
      updateDateTime,
      updateTime,
      updateMonthDay,
      updateAction,
      updateBrightness,
      toggleWeekday,
      formatDateTime,
      getScheduleTypeLabel,
      getActionLabel,
      updateScene,
      getSceneLabel,
      showDeviceSelector,
      toggleDeviceSelection,
      confirmDeviceSelection
    }
  }
}
</script>

<style scoped>
.schedule-page {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

.btn-add {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 15rpx 25rpx;
  background-color: #1890ff;
  color: white;
  border-radius: 8rpx;
  border: none;
  font-size: 28rpx;
}

/* 统计信息 */
.stats-container {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.stat-card {
  flex: 1;
  background-color: white;
  padding: 30rpx 20rpx;
  border-radius: 12rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.stat-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 筛选和排序 */
.filter-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
  padding: 20rpx 30rpx;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.filter-tabs {
  display: flex;
  gap: 30rpx;
}

.filter-tab {
  padding: 15rpx 20rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  cursor: pointer;
  transition: all 0.3s;
}

.filter-tab.active {
  background-color: #1890ff;
  color: white;
}

.tab-count {
  font-size: 22rpx;
  opacity: 0.8;
}

.sort-actions {
  display: flex;
  gap: 15rpx;
}

.btn-sort {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #dee2e6;
  border-radius: 6rpx;
  font-size: 26rpx;
  color: #495057;
}

/* 任务列表 */
.task-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.task-item {
  background-color: white;
  padding: 30rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.task-info {
  flex: 1;
}

.task-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.task-status {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.status-active {
  background-color: #d4edda;
  color: #155724;
}

.status-inactive {
  background-color: #f8d7da;
  color: #721c24;
}

.status-fault {
  background-color: #fff3cd;
  color: #856404;
}

.task-details {
  display: flex;
  gap: 40rpx;
  margin-bottom: 20rpx;
  font-size: 26rpx;
  color: #666;
}

.task-schedule,
.task-devices {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.schedule-icon,
.devices-icon {
  font-size: 24rpx;
}

.task-execution {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.execution-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  font-size: 24rpx;
  color: #666;
}

.execution-result {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.result-success {
  background-color: #d4edda;
  color: #155724;
}

.result-failed {
  background-color: #f8d7da;
  color: #721c24;
}

.result-pending {
  background-color: #fff3cd;
  color: #856404;
}

.task-controls {
  display: flex;
  gap: 15rpx;
}

.btn-edit,
.btn-execute,
.btn-delete {
  padding: 12rpx 20rpx;
  border-radius: 6rpx;
  font-size: 26rpx;
  border: none;
}

.btn-edit {
  background-color: #e6f7ff;
  color: #1890ff;
}

.btn-execute {
  background-color: #f6ffed;
  color: #52c41a;
}

.btn-execute:disabled {
  background-color: #f5f5f5;
  color: #bfbfbf;
}

.btn-delete {
  background-color: #fff2f0;
  color: #ff4d4f;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 50rpx;
  background-color: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.btn-create {
  padding: 20rpx 40rpx;
  background-color: #1890ff;
  color: white;
  border-radius: 8rpx;
  border: none;
  font-size: 28rpx;
}

/* 弹窗样式 */
.task-modal,
.device-modal,
.detail-modal {
  background-color: white;
  border-radius: 12rpx;
  width: 700rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.btn-close {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f8f9fa;
  border: none;
  font-size: 28rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  padding: 30rpx;
  max-height: 600rpx;
  overflow-y: auto;
}

/* 表单样式 */
.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 6rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.form-textarea {
  height: 120rpx;
  resize: none;
}

.picker-text {
  padding: 20rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 6rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.form-switch {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.weekday-selector {
  display: flex;
  gap: 15rpx;
  margin-bottom: 20rpx;
}

.weekday-item {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #d9d9d9;
  border-radius: 50%;
  font-size: 26rpx;
  color: #666;
  cursor: pointer;
}

.weekday-item.active {
  background-color: #1890ff;
  border-color: #1890ff;
  color: white;
}

.btn-select-devices {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 6rpx;
  font-size: 28rpx;
  background-color: #fff;
  color: #333;
}

/* 设备选择 */
.device-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.device-checkbox {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  border: 1rpx solid #e9ecef;
  border-radius: 8rpx;
  cursor: pointer;
}

.device-checkbox:hover {
  background-color: #f8f9fa;
}

.device-info {
  flex: 1;
}

.device-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.device-location {
  font-size: 24rpx;
  color: #666;
}

/* 任务详情 */
.detail-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #e9ecef;
}

.detail-item {
  display: flex;
  margin-bottom: 15rpx;
  font-size: 26rpx;
}

.detail-label {
  min-width: 150rpx;
  color: #666;
}

.detail-value {
  flex: 1;
  color: #333;
}

.execution-history {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  max-height: 300rpx;
  overflow-y: auto;
}

.history-item {
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #1890ff;
}

.history-time {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.history-result {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  margin-bottom: 8rpx;
}

.history-details {
  font-size: 24rpx;
  color: #333;
}

.modal-actions {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #e9ecef;
}

.btn-cancel {
  flex: 1;
  padding: 25rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #dee2e6;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #495057;
}

.btn-save,
.btn-confirm {
  flex: 1;
  padding: 25rpx;
  background-color: #1890ff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: white;
}

.icon {
  font-size: 24rpx;
}
</style>