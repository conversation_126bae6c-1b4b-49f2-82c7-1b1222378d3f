// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core.Service;

/// <summary>
/// 场景设备输出参数
/// </summary>
public class EnergySceneDeviceOutput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 场景ID
    /// </summary>
    public long SceneId { get; set; }

    /// <summary>
    /// 场景名称
    /// </summary>
    public string? SceneName { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 设备编码
    /// </summary>
    public string? DeviceCode { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string? DeviceName { get; set; }

    /// <summary>
    /// 设备位置
    /// </summary>
    public string? DeviceLocation { get; set; }

    /// <summary>
    /// 控制类型
    /// </summary>
    public string ControlType { get; set; }

    /// <summary>
    /// 控制命令
    /// </summary>
    public string? ControlCommand { get; set; }

    /// <summary>
    /// 控制参数
    /// </summary>
    public string ControlParams { get; set; }

    /// <summary>
    /// 延迟时间(秒)
    /// </summary>
    public int DelayTime { get; set; }

    /// <summary>
    /// 延迟时间(秒) - 兼容字段
    /// </summary>
    public int DelaySeconds { get; set; }

    /// <summary>
    /// 执行次数
    /// </summary>
    public int ExecuteCount { get; set; }

    /// <summary>
    /// 成功次数
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public decimal SuccessRate { get; set; }

    /// <summary>
    /// 最后执行时间
    /// </summary>
    public DateTime? LastExecuteTime { get; set; }

    /// <summary>
    /// 平均执行时长(毫秒)
    /// </summary>
    public decimal? AvgExecuteTime { get; set; }

    /// <summary>
    /// 平均执行持续时间(毫秒)
    /// </summary>
    public decimal? AvgExecuteDuration { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// 状态名称
    /// </summary>
    public string StatusName { get; set; }

    /// <summary>
    /// 设备类型
    /// </summary>
    public string DeviceType { get; set; }

    /// <summary>
    /// 设备型号
    /// </summary>
    public string DeviceModel { get; set; }

    /// <summary>
    /// 租户ID
    /// </summary>
    public long? TenantId { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
}