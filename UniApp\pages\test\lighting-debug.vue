<template>
  <view class="debug-container">
    <view class="debug-header">
      <text class="debug-title">照明控制调试页面</text>
    </view>
    
    <view class="debug-section">
      <text class="section-title">基础数据测试</text>
      <view class="debug-item">
        <text>设备总数: {{ stats.totalDevices }}</text>
      </view>
      <view class="debug-item">
        <text>在线设备: {{ stats.onlineDevices }}</text>
      </view>
      <view class="debug-item">
        <text>开启设备: {{ stats.onDevices }}</text>
      </view>
      <view class="debug-item">
        <text>总功耗: {{ stats.totalPower }}W</text>
      </view>
    </view>
    
    <view class="debug-section">
      <text class="section-title">设备列表测试</text>
      <view v-for="device in devices" :key="device.id" class="device-debug-item">
        <text>{{ device.name }} - {{ device.status }} - {{ device.isOn ? '开启' : '关闭' }}</text>
      </view>
    </view>
    
    <view class="debug-section">
      <text class="section-title">操作测试</text>
      <button @click="testLoadData" class="debug-btn">测试加载数据</button>
      <button @click="testToggleDevice" class="debug-btn">测试切换设备</button>
    </view>
    
    <view class="debug-section">
      <text class="section-title">日志输出</text>
      <view class="debug-logs">
        <text v-for="(log, index) in logs" :key="index" class="log-item">{{ log }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { getDeviceList, getGroups } from '@/api/device.js'
import { getScenes } from '@/api/lighting.js'

export default {
  name: 'LightingDebug',
  setup() {
    // 响应式数据
    const stats = reactive({
      totalDevices: 0,
      onlineDevices: 0,
      onDevices: 0,
      totalPower: 0
    })
    
    const devices = ref([])
    const logs = ref([])
    
    // 添加日志
    const addLog = (message) => {
      const timestamp = new Date().toLocaleTimeString()
      logs.value.unshift(`[${timestamp}] ${message}`)
      console.log(message)
    }
    
    // 更新统计数据
    const updateStats = () => {
      stats.totalDevices = devices.value.length
      stats.onlineDevices = devices.value.filter(d => d.status === 'online').length
      stats.onDevices = devices.value.filter(d => d.isOn).length
      stats.totalPower = devices.value
        .filter(d => d.isOn)
        .reduce((sum, d) => sum + (d.power || 0), 0)
      addLog(`统计数据更新: 总数${stats.totalDevices}, 在线${stats.onlineDevices}, 开启${stats.onDevices}, 功耗${stats.totalPower}W`)
    }
    
    // 测试加载数据
    const testLoadData = async () => {
      try {
        addLog('开始测试加载数据...')
        
        // 设置测试数据
        devices.value = [
          {
            id: 'test-1',
            name: '测试灯具1',
            status: 'online',
            isOn: true,
            brightness: 80,
            power: 12,
            type: 'led'
          },
          {
            id: 'test-2',
            name: '测试灯具2',
            status: 'online',
            isOn: false,
            brightness: 0,
            power: 0,
            type: 'led'
          },
          {
            id: 'test-3',
            name: '测试灯具3',
            status: 'offline',
            isOn: false,
            brightness: 0,
            power: 0,
            type: 'led'
          }
        ]
        
        updateStats()
        addLog('测试数据加载成功')
        
        // 尝试调用真实API
        try {
          addLog('尝试调用真实API...')
          const devicesRes = await getDeviceList({ page: 1, pageSize: 10 })
          addLog(`API调用结果: ${JSON.stringify(devicesRes)}`)
        } catch (apiError) {
          addLog(`API调用失败: ${apiError.message}`)
        }
        
      } catch (error) {
        addLog(`加载数据失败: ${error.message}`)
      }
    }
    
    // 测试切换设备
    const testToggleDevice = () => {
      if (devices.value.length > 0) {
        const device = devices.value[0]
        device.isOn = !device.isOn
        device.power = device.isOn ? 12 : 0
        updateStats()
        addLog(`切换设备 ${device.name} 状态: ${device.isOn ? '开启' : '关闭'}`)
      } else {
        addLog('没有设备可以切换')
      }
    }
    
    // 页面加载时初始化
    onMounted(() => {
      addLog('调试页面加载完成')
      testLoadData()
    })
    
    return {
      stats,
      devices,
      logs,
      testLoadData,
      testToggleDevice
    }
  }
}
</script>

<style scoped>
.debug-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.debug-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.debug-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.debug-section {
  background-color: white;
  margin-bottom: 20rpx;
  padding: 20rpx;
  border-radius: 10rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #007AFF;
  margin-bottom: 15rpx;
  display: block;
}

.debug-item {
  padding: 10rpx 0;
  border-bottom: 1rpx solid #eee;
}

.debug-item:last-child {
  border-bottom: none;
}

.device-debug-item {
  padding: 15rpx;
  background-color: #f8f9fa;
  margin-bottom: 10rpx;
  border-radius: 8rpx;
}

.debug-btn {
  background-color: #007AFF;
  color: white;
  border: none;
  padding: 15rpx 30rpx;
  border-radius: 8rpx;
  margin-right: 15rpx;
  margin-bottom: 15rpx;
  font-size: 28rpx;
}

.debug-btn:active {
  background-color: #0056CC;
}

.debug-logs {
  max-height: 400rpx;
  overflow-y: auto;
  background-color: #f8f9fa;
  padding: 15rpx;
  border-radius: 8rpx;
}

.log-item {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  font-family: monospace;
}
</style>