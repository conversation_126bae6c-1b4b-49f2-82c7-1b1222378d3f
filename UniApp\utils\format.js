/**
 * 数据格式化工具
 * 提供常用的数据格式化方法
 */

/**
 * 时间格式化
 */
export const dateFormat = {
  /**
   * 格式化日期时间
   * @param {Date|string|number} date 日期
   * @param {string} format 格式字符串
   * @returns {string} 格式化后的日期字符串
   */
  format(date, format = 'YYYY-MM-DD HH:mm:ss') {
    if (!date) return ''
    
    const d = new Date(date)
    if (isNaN(d.getTime())) return ''
    
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hour = String(d.getHours()).padStart(2, '0')
    const minute = String(d.getMinutes()).padStart(2, '0')
    const second = String(d.getSeconds()).padStart(2, '0')
    
    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hour)
      .replace('mm', minute)
      .replace('ss', second)
  },

  /**
   * 格式化为相对时间
   * @param {Date|string|number} date 日期
   * @returns {string} 相对时间字符串
   */
  relative(date) {
    if (!date) return ''
    
    const d = new Date(date)
    if (isNaN(d.getTime())) return ''
    
    const now = new Date()
    const diff = now.getTime() - d.getTime()
    const seconds = Math.floor(diff / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)
    
    if (seconds < 60) return '刚刚'
    if (minutes < 60) return `${minutes}分钟前`
    if (hours < 24) return `${hours}小时前`
    if (days < 7) return `${days}天前`
    if (days < 30) return `${Math.floor(days / 7)}周前`
    if (days < 365) return `${Math.floor(days / 30)}个月前`
    return `${Math.floor(days / 365)}年前`
  },

  /**
   * 格式化时间段
   * @param {number} seconds 秒数
   * @returns {string} 时间段字符串
   */
  duration(seconds) {
    if (!seconds || seconds < 0) return '0秒'
    
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    
    const parts = []
    if (hours > 0) parts.push(`${hours}小时`)
    if (minutes > 0) parts.push(`${minutes}分钟`)
    if (secs > 0 || parts.length === 0) parts.push(`${secs}秒`)
    
    return parts.join('')
  }
}

/**
 * 数字格式化
 */
export const numberFormat = {
  /**
   * 格式化数字，添加千分位分隔符
   * @param {number} num 数字
   * @param {number} decimals 小数位数
   * @returns {string} 格式化后的数字字符串
   */
  thousands(num, decimals = 2) {
    if (num === null || num === undefined || isNaN(num)) return '0'
    
    const number = Number(num)
    return number.toLocaleString('zh-CN', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    })
  },

  /**
   * 格式化百分比
   * @param {number} num 数字
   * @param {number} decimals 小数位数
   * @returns {string} 百分比字符串
   */
  percentage(num, decimals = 1) {
    if (num === null || num === undefined || isNaN(num)) return '0%'
    
    const number = Number(num) * 100
    return `${number.toFixed(decimals)}%`
  },

  /**
   * 格式化文件大小
   * @param {number} bytes 字节数
   * @param {number} decimals 小数位数
   * @returns {string} 文件大小字符串
   */
  fileSize(bytes, decimals = 2) {
    if (bytes === 0) return '0 B'
    if (!bytes || bytes < 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return `${(bytes / Math.pow(k, i)).toFixed(decimals)} ${sizes[i]}`
  },

  /**
   * 格式化能耗单位
   * @param {number} value 能耗值
   * @param {string} unit 单位
   * @param {number} decimals 小数位数
   * @returns {string} 能耗字符串
   */
  energy(value, unit = 'kWh', decimals = 2) {
    if (value === null || value === undefined || isNaN(value)) return `0 ${unit}`
    
    const number = Number(value)
    if (number >= 1000000) {
      return `${(number / 1000000).toFixed(decimals)} M${unit}`
    } else if (number >= 1000) {
      return `${(number / 1000).toFixed(decimals)} k${unit}`
    }
    return `${number.toFixed(decimals)} ${unit}`
  },

  /**
   * 格式化货币
   * @param {number} amount 金额
   * @param {string} currency 货币符号
   * @param {number} decimals 小数位数
   * @returns {string} 货币字符串
   */
  currency(amount, currency = '¥', decimals = 2) {
    if (amount === null || amount === undefined || isNaN(amount)) return `${currency}0.00`
    
    const number = Number(amount)
    return `${currency}${number.toLocaleString('zh-CN', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    })}`
  }
}

/**
 * 字符串格式化
 */
export const stringFormat = {
  /**
   * 截断字符串
   * @param {string} str 字符串
   * @param {number} length 最大长度
   * @param {string} suffix 后缀
   * @returns {string} 截断后的字符串
   */
  truncate(str, length = 50, suffix = '...') {
    if (!str || typeof str !== 'string') return ''
    if (str.length <= length) return str
    return str.substring(0, length) + suffix
  },

  /**
   * 首字母大写
   * @param {string} str 字符串
   * @returns {string} 首字母大写的字符串
   */
  capitalize(str) {
    if (!str || typeof str !== 'string') return ''
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
  },

  /**
   * 驼峰命名转换
   * @param {string} str 字符串
   * @returns {string} 驼峰命名字符串
   */
  camelCase(str) {
    if (!str || typeof str !== 'string') return ''
    return str.replace(/[-_\s]+(.)?/g, (_, c) => c ? c.toUpperCase() : '')
  },

  /**
   * 隐藏敏感信息
   * @param {string} str 字符串
   * @param {number} start 开始位置
   * @param {number} end 结束位置
   * @param {string} mask 掩码字符
   * @returns {string} 隐藏后的字符串
   */
  mask(str, start = 3, end = 4, mask = '*') {
    if (!str || typeof str !== 'string') return ''
    if (str.length <= start + end) return str
    
    const maskLength = str.length - start - end
    const maskStr = mask.repeat(maskLength)
    return str.substring(0, start) + maskStr + str.substring(str.length - end)
  },

  /**
   * 格式化手机号
   * @param {string} mobile 手机号
   * @returns {string} 格式化后的手机号
   */
  mobile(mobile) {
    if (!mobile || typeof mobile !== 'string') return ''
    const cleaned = mobile.replace(/\D/g, '')
    if (cleaned.length === 11) {
      return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3')
    }
    return mobile
  },

  /**
   * 格式化身份证号
   * @param {string} idCard 身份证号
   * @returns {string} 格式化后的身份证号
   */
  idCard(idCard) {
    if (!idCard || typeof idCard !== 'string') return ''
    if (idCard.length === 18) {
      return idCard.replace(/(\d{6})(\d{8})(\d{4})/, '$1 $2 $3')
    }
    return idCard
  }
}

/**
 * 设备状态格式化
 */
export const deviceFormat = {
  /**
   * 格式化设备状态
   * @param {number|string} status 状态值
   * @returns {object} 状态信息对象
   */
  status(status) {
    const statusMap = {
      0: { text: '离线', color: '#999999', type: 'offline' },
      1: { text: '在线', color: '#52c41a', type: 'online' },
      2: { text: '故障', color: '#ff4d4f', type: 'fault' },
      3: { text: '维护', color: '#faad14', type: 'maintenance' }
    }
    return statusMap[status] || { text: '未知', color: '#999999', type: 'unknown' }
  },

  /**
   * 格式化设备类型
   * @param {string} type 设备类型
   * @returns {string} 设备类型名称
   */
  type(type) {
    const typeMap = {
      'led_light': 'LED灯',
      'fluorescent_light': '荧光灯',
      'halogen_light': '卤素灯',
      'smart_switch': '智能开关',
      'sensor': '传感器',
      'controller': '控制器'
    }
    return typeMap[type] || type
  },

  /**
   * 格式化亮度值
   * @param {number} brightness 亮度值(0-100)
   * @returns {string} 亮度字符串
   */
  brightness(brightness) {
    if (brightness === null || brightness === undefined || isNaN(brightness)) return '0%'
    const value = Math.max(0, Math.min(100, Number(brightness)))
    return `${value}%`
  },

  /**
   * 格式化色温值
   * @param {number} temperature 色温值(K)
   * @returns {string} 色温字符串
   */
  colorTemperature(temperature) {
    if (temperature === null || temperature === undefined || isNaN(temperature)) return '0K'
    return `${Number(temperature)}K`
  }
}

/**
 * 故障等级格式化
 */
export const faultFormat = {
  /**
   * 格式化故障等级
   * @param {number|string} level 故障等级
   * @returns {object} 等级信息对象
   */
  level(level) {
    const levelMap = {
      1: { text: '低', color: '#52c41a', type: 'low' },
      2: { text: '中', color: '#faad14', type: 'medium' },
      3: { text: '高', color: '#ff7a45', type: 'high' },
      4: { text: '紧急', color: '#ff4d4f', type: 'urgent' }
    }
    return levelMap[level] || { text: '未知', color: '#999999', type: 'unknown' }
  },

  /**
   * 格式化故障状态
   * @param {number|string} status 故障状态
   * @returns {object} 状态信息对象
   */
  status(status) {
    const statusMap = {
      0: { text: '待处理', color: '#faad14', type: 'pending' },
      1: { text: '处理中', color: '#1890ff', type: 'processing' },
      2: { text: '已解决', color: '#52c41a', type: 'resolved' },
      3: { text: '已关闭', color: '#999999', type: 'closed' }
    }
    return statusMap[status] || { text: '未知', color: '#999999', type: 'unknown' }
  }
}

/**
 * 通用格式化方法
 */
export const format = {
  /**
   * 格式化空值
   * @param {any} value 值
   * @param {string} placeholder 占位符
   * @returns {any} 格式化后的值
   */
  empty(value, placeholder = '--') {
    if (value === null || value === undefined || value === '') {
      return placeholder
    }
    return value
  },

  /**
   * 格式化数组为字符串
   * @param {array} arr 数组
   * @param {string} separator 分隔符
   * @returns {string} 字符串
   */
  arrayToString(arr, separator = ', ') {
    if (!Array.isArray(arr)) return ''
    return arr.join(separator)
  },

  /**
   * 格式化对象为键值对字符串
   * @param {object} obj 对象
   * @param {string} separator 分隔符
   * @returns {string} 字符串
   */
  objectToString(obj, separator = ', ') {
    if (!obj || typeof obj !== 'object') return ''
    return Object.entries(obj)
      .map(([key, value]) => `${key}: ${value}`)
      .join(separator)
  }
}

export default {
  dateFormat,
  numberFormat,
  stringFormat,
  deviceFormat,
  faultFormat,
  format
}