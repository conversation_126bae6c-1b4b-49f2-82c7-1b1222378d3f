/**
 * HTTP请求封装
 * 基于uni.request封装，提供统一的请求和响应处理
 * 支持请求拦截、响应拦截、错误处理、重试机制、缓存、请求去重等功能
 */

// 导入工具函数
import storage from '../utils/storage.js'

// 获取基础URL
const getBaseURL = () => {
  // #ifdef APP-PLUS
  // App平台使用实际服务器地址
  return 'https://api.energy-light.com/api'
  // #endif
  
  // #ifdef H5
  // H5平台可以使用localhost
  return 'http://localhost:5005/api'
  // #endif
  
  // #ifdef MP
  // 小程序平台使用开发机IP地址
  return 'http://*************:5005/api'
  // #endif
}

// 基础配置
const config = {
  baseURL: getBaseURL(), // 后端API地址
  timeout: 10000, // 请求超时时间
  retryCount: 3, // 重试次数
  retryDelay: 1000, // 重试延迟(ms)
  cacheEnabled: true, // 是否启用缓存
  cacheExpiry: 300000, // 缓存过期时间(ms) 5分钟
  header: {
    'Content-Type': 'application/json'
  }
}

// 缓存存储
const requestCache = new Map()
// 正在进行的请求
const pendingRequests = new Map()

// 生成请求唯一标识
const generateRequestKey = (options) => {
  const { url, method = 'GET', data = {} } = options
  return `${method}:${url}:${JSON.stringify(data)}`
}

// 检查缓存
const checkCache = (key) => {
  if (!config.cacheEnabled) return null
  
  const cached = requestCache.get(key)
  if (cached && Date.now() - cached.timestamp < config.cacheExpiry) {
    return cached.data
  }
  
  // 清除过期缓存
  if (cached) {
    requestCache.delete(key)
  }
  
  return null
}

// 设置缓存
const setCache = (key, data) => {
  if (!config.cacheEnabled) return
  
  requestCache.set(key, {
    data,
    timestamp: Date.now()
  })
}

// 请求拦截器
const requestInterceptor = (options) => {
  // 生成请求标识
  const requestKey = generateRequestKey(options)
  
  // 检查是否为GET请求且启用缓存
  if (options.method === 'GET' || !options.method) {
    const cachedData = checkCache(requestKey)
    if (cachedData) {
      return Promise.resolve(cachedData)
    }
  }
  
  // 检查是否有相同请求正在进行（请求去重）
  if (pendingRequests.has(requestKey)) {
    return pendingRequests.get(requestKey)
  }
  
  // 显示加载提示（避免重复显示）
  if (!options.silent) {
    uni.showLoading({
      title: options.loadingText || '加载中...',
      mask: true
    })
  }

  // 添加基础URL
  if (!options.url.startsWith('http')) {
    options.url = config.baseURL + options.url
  }

  // 添加默认header
  options.header = {
    ...config.header,
    ...options.header
  }

  // 添加token
  const token = storage.get('token') || uni.getStorageSync('token')
  if (token) {
    options.header.Authorization = `Bearer ${token}`
  }

  // 添加租户ID
  const tenantId = storage.get('tenantId') || uni.getStorageSync('tenantId')
  if (tenantId) {
    options.header['Tenant-Id'] = tenantId
  }

  // 添加设备信息
  const deviceId = storage.get('deviceId')
  if (deviceId) {
    options.header['Device-Id'] = deviceId
  }

  // 添加时间戳（防止缓存）
  if (options.preventCache) {
    const separator = options.url.includes('?') ? '&' : '?'
    options.url += `${separator}_t=${Date.now()}`
  }

  // 设置超时时间
  options.timeout = options.timeout || config.timeout
  
  // 设置重试配置
  options.retryCount = options.retryCount !== undefined ? options.retryCount : config.retryCount
  options.retryDelay = options.retryDelay || config.retryDelay
  
  // 添加请求标识
  options._requestKey = requestKey

  return options
}

// 响应拦截器
const responseInterceptor = (response, options) => {
  const { statusCode, data } = response
  const requestKey = options._requestKey
  
  // 从正在进行的请求中移除
  if (requestKey) {
    pendingRequests.delete(requestKey)
  }
  
  // 隐藏加载提示
  if (!options.silent) {
    uni.hideLoading()
  }

  // HTTP状态码检查
  if (statusCode !== 200) {
    handleHttpError(statusCode, data, options)
    return Promise.reject(new Error(`HTTP Error: ${statusCode}`))
  }

  // 业务状态码检查
  if (data && typeof data === 'object') {
    // Admin.NET框架响应格式
    if (data.success === false || data.code !== undefined && data.code !== 200) {
      handleBusinessError(data, options)
      return Promise.reject(new Error(data.message || '请求失败'))
    }
    
    // 获取实际数据
    const result = data.result || data.data || data
    
    // 缓存GET请求的成功响应
    if (requestKey && (options.method === 'GET' || !options.method)) {
      setCache(requestKey, result)
    }
    
    return result
  }

  return data
}

// HTTP错误处理
const handleHttpError = (statusCode, data, options = {}) => {
  let message = '网络请求失败'
  
  switch (statusCode) {
    case 400:
      message = data?.message || '请求参数错误'
      break
    case 401: {
      message = '登录已过期，请重新登录'
      // 清除token并跳转到登录页
      storage.remove('token')
      storage.remove('userInfo')
      uni.removeStorageSync('token')
      uni.removeStorageSync('userInfo')
      
      // 避免重复跳转
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      if (currentPage && !currentPage.route.includes('login')) {
        uni.reLaunch({
          url: '/pages/login/login'
        })
      }
      break
    }
    case 403:
      message = data?.message || '权限不足，拒绝访问'
      break
    case 404:
      message = '请求的资源不存在'
      break
    case 408:
      message = '请求超时'
      break
    case 500:
      message = '服务器内部错误'
      break
    case 502:
      message = '网关错误'
      break
    case 503:
      message = '服务暂时不可用'
      break
    case 504:
      message = '网关超时'
      break
    default:
      message = `网络错误 ${statusCode}`
  }
  
  // 不显示toast的情况
  if (options.silent || options.hideError) {
    return
  }
  
  uni.showToast({
    title: message,
    icon: 'none',
    duration: 3000
  })
}

// 业务错误处理
const handleBusinessError = (data, options = {}) => {
  const message = data.message || data.msg || '操作失败'
  const code = data.code || data.status
  
  // 特殊错误码处理
  switch (code) {
    case 401:
    case 'UNAUTHORIZED':
    case 'TOKEN_EXPIRED': {
      // token过期，清除本地数据并跳转登录
      storage.remove('token')
      storage.remove('userInfo')
      uni.removeStorageSync('token')
      uni.removeStorageSync('userInfo')
      
      // 避免重复跳转
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      if (currentPage && !currentPage.route.includes('login')) {
        uni.reLaunch({
          url: '/pages/login/login'
        })
      }
      return
    }
      
    case 403:
    case 'FORBIDDEN':
      uni.showModal({
        title: '权限不足',
        content: message || '您没有权限执行此操作',
        showCancel: false
      })
      return
      
    case 429:
    case 'TOO_MANY_REQUESTS':
      uni.showToast({
        title: '请求过于频繁，请稍后再试',
        icon: 'none',
        duration: 3000
      })
      return
      
    case 'NETWORK_ERROR':
      uni.showToast({
        title: '网络连接异常，请检查网络设置',
        icon: 'none',
        duration: 3000
      })
      return
  }
  
  // 不显示toast的情况
  if (options.silent || options.hideError) {
    return
  }
  
  // 显示错误信息
  if (options.showModal) {
    uni.showModal({
      title: '提示',
      content: message,
      showCancel: false
    })
  } else {
    uni.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    })
  }
}

// 延迟函数
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))

// 重试请求
const retryRequest = async (options, retryCount = 0) => {
  try {
    const result = await executeRequest(options)
    return result
  } catch (error) {
    const maxRetries = options.retryCount || 0
    const shouldRetry = retryCount < maxRetries && 
                       (error.statusCode >= 500 || error.statusCode === 0 || error.code === 'NETWORK_ERROR')
    
    if (shouldRetry) {
      const delayTime = options.retryDelay * Math.pow(2, retryCount) // 指数退避
      await delay(delayTime)
      return retryRequest(options, retryCount + 1)
    }
    
    throw error
  }
}

// 执行请求
const executeRequest = (options) => {
  return new Promise((resolve, reject) => {
    uni.request({
      ...options,
      success: (response) => {
        try {
          const result = responseInterceptor(response, options)
          resolve(result)
        } catch (error) {
          reject(error)
        }
      },
      fail: (error) => {
        // 清理pending请求
        if (options._requestKey) {
          pendingRequests.delete(options._requestKey)
        }
        
        if (!options.silent) {
          uni.hideLoading()
        }
        
        // 网络错误
        const networkError = {
          ...error,
          statusCode: 0,
          code: 'NETWORK_ERROR'
        }
        
        handleHttpError(0, networkError, options)
        reject(networkError)
      }
    })
  })
}

// 主要请求方法
const request = async (options) => {
  // 请求拦截
  const processedOptions = requestInterceptor(options)
  
  // 如果拦截器返回Promise（缓存命中），直接返回
  if (processedOptions && typeof processedOptions.then === 'function') {
    return await processedOptions
  }
  
  // 添加到pending请求
  const requestKey = processedOptions._requestKey
  if (requestKey && !pendingRequests.has(requestKey)) {
    const requestPromise = retryRequest(processedOptions)
    pendingRequests.set(requestKey, requestPromise)
    
    try {
      const result = await requestPromise
      return result
    } finally {
      pendingRequests.delete(requestKey)
    }
  }
  
  // 如果已有相同请求，返回该请求的Promise
  if (requestKey && pendingRequests.has(requestKey)) {
    return await pendingRequests.get(requestKey)
  }
  
  // 执行请求
  return await retryRequest(processedOptions)
}

// 清理缓存
const clearCache = (pattern) => {
  if (!pattern) {
    requestCache.clear()
    return
  }
  
  for (const [key] of requestCache) {
    if (key.includes(pattern)) {
      requestCache.delete(key)
    }
  }
}

// 取消请求
const cancelRequest = (pattern) => {
  if (!pattern) {
    pendingRequests.clear()
    return
  }
  
  for (const [key] of pendingRequests) {
    if (key.includes(pattern)) {
      pendingRequests.delete(key)
    }
  }
}

// 便捷方法
const get = (url, params = {}, options = {}) => {
  // 处理查询参数
  const queryString = Object.keys(params)
    .filter(key => params[key] !== undefined && params[key] !== null && params[key] !== '')
    .map(key => {
      const value = params[key]
      if (Array.isArray(value)) {
        return value.map(v => `${encodeURIComponent(key)}=${encodeURIComponent(v)}`).join('&')
      }
      return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`
    })
    .join('&')
  
  const fullUrl = queryString ? `${url}?${queryString}` : url
  
  return request({
    url: fullUrl,
    method: 'GET',
    ...options
  })
}

const post = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'POST',
    data,
    header: {
      'Content-Type': 'application/json',
      ...options.header
    },
    ...options
  })
}

const put = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'PUT',
    data,
    header: {
      'Content-Type': 'application/json',
      ...options.header
    },
    ...options
  })
}

const patch = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'PATCH',
    data,
    header: {
      'Content-Type': 'application/json',
      ...options.header
    },
    ...options
  })
}

const del = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'DELETE',
    data,
    ...options
  })
}

// 表单提交
const postForm = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'POST',
    data,
    header: {
      'Content-Type': 'application/x-www-form-urlencoded',
      ...options.header
    },
    ...options
  })
}

// 文件上传
const upload = (url, filePath, options = {}) => {
  return new Promise((resolve, reject) => {
    if (!options.silent) {
      uni.showLoading({
        title: options.loadingText || '上传中...',
        mask: true
      })
    }
    
    const token = storage.get('token') || uni.getStorageSync('token')
    const tenantId = storage.get('tenantId') || uni.getStorageSync('tenantId')
    
    const uploadTask = uni.uploadFile({
      url: config.baseURL + url,
      filePath,
      name: options.name || 'file',
      header: {
        'Authorization': token ? `Bearer ${token}` : '',
        'Tenant-Id': tenantId || '',
        ...options.header
      },
      formData: options.formData || {},
      success: (response) => {
        if (!options.silent) {
          uni.hideLoading()
        }
        
        try {
          const data = JSON.parse(response.data)
          if (data.success !== false && response.statusCode === 200) {
            resolve(data.result || data.data || data)
          } else {
            handleBusinessError(data, options)
            reject(new Error(data.message || '上传失败'))
          }
        } catch (error) {
          reject(new Error('上传响应解析失败'))
        }
      },
      fail: (error) => {
        if (!options.silent) {
          uni.hideLoading()
        }
        
        if (!options.hideError) {
          uni.showToast({
            title: '上传失败',
            icon: 'none',
            duration: 2000
          })
        }
        reject(error)
      }
    })
    
    // 上传进度
    if (options.onProgress && typeof options.onProgress === 'function') {
      uploadTask.onProgressUpdate(options.onProgress)
    }
  })
}

// 导出
export default {
  // 核心方法
  request,
  
  // HTTP方法
  get,
  post,
  put,
  patch,
  del,
  postForm,
  upload,
  
  // 工具方法
  clearCache,
  cancelRequest,
  
  // 配置
  config,
  
  // 拦截器（供外部扩展使用）
  requestInterceptor,
  responseInterceptor
}

// 同时支持具名导出
export {
  request,
  get,
  post,
  put,
  patch,
  del,
  postForm,
  upload,
  clearCache,
  cancelRequest,
  config
}