using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Admin.NET.Plugin.MQTT.Interfaces
{
    /// <summary>
    /// MQTT配置管理器接口
    /// 定义配置加载、验证和热更新的核心功能
    /// </summary>
    public interface IMqttConfigurationManager
    {
        /// <summary>
        /// 配置变化事件
        /// </summary>
        event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged;
        
        /// <summary>
        /// 获取MQTT客户端配置
        /// </summary>
        /// <returns>MQTT客户端配置</returns>
        MqttClientConfiguration GetClientConfiguration();
        
        /// <summary>
        /// 获取消息路由配置
        /// </summary>
        /// <returns>消息路由配置</returns>
        MessageRouterConfiguration GetRouterConfiguration();
        
        /// <summary>
        /// 获取设备控制配置
        /// </summary>
        /// <returns>设备控制配置</returns>
        DeviceControlConfiguration GetDeviceControlConfiguration();
        
        /// <summary>
        /// 获取性能监控配置
        /// </summary>
        /// <returns>性能监控配置</returns>
        PerformanceMonitoringConfiguration GetPerformanceConfiguration();
        
        /// <summary>
        /// 更新配置
        /// </summary>
        /// <param name="configuration">新配置</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task UpdateConfigurationAsync(MqttPluginConfiguration configuration, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 验证配置
        /// </summary>
        /// <param name="configuration">待验证的配置</param>
        /// <returns>验证结果</returns>
        ConfigurationValidationResult ValidateConfiguration(MqttPluginConfiguration configuration);
        
        /// <summary>
        /// 重新加载配置
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task ReloadConfigurationAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 获取完整配置
        /// </summary>
        /// <returns>完整的MQTT插件配置</returns>
        MqttPluginConfiguration GetConfiguration();
    }
    
    /// <summary>
    /// 配置变化事件参数
    /// </summary>
    public class ConfigurationChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 旧配置
        /// </summary>
        public MqttPluginConfiguration OldConfiguration { get; set; }
        
        /// <summary>
        /// 新配置
        /// </summary>
        public MqttPluginConfiguration NewConfiguration { get; set; }
        
        /// <summary>
        /// 变化的配置节
        /// </summary>
        public List<string> ChangedSections { get; set; } = new List<string>();
        
        /// <summary>
        /// 变化时间
        /// </summary>
        public DateTime ChangedAt { get; set; } = DateTime.UtcNow;
    }
    
    /// <summary>
    /// MQTT插件完整配置
    /// </summary>
    public class MqttPluginConfiguration
    {
        /// <summary>
        /// 客户端配置
        /// </summary>
        public MqttClientConfiguration Client { get; set; } = new MqttClientConfiguration();
        
        /// <summary>
        /// 消息路由配置
        /// </summary>
        public MessageRouterConfiguration Router { get; set; } = new MessageRouterConfiguration();
        
        /// <summary>
        /// 设备控制配置
        /// </summary>
        public DeviceControlConfiguration DeviceControl { get; set; } = new DeviceControlConfiguration();
        
        /// <summary>
        /// 性能监控配置
        /// </summary>
        public PerformanceMonitoringConfiguration Performance { get; set; } = new PerformanceMonitoringConfiguration();
        
        /// <summary>
        /// 日志配置
        /// </summary>
        public LoggingConfiguration Logging { get; set; } = new LoggingConfiguration();
    }
    
    /// <summary>
    /// MQTT客户端配置
    /// </summary>
    public class MqttClientConfiguration
    {
        /// <summary>
        /// 服务器地址
        /// </summary>
        public string Server { get; set; } = "localhost";
        
        /// <summary>
        /// 服务器端口
        /// </summary>
        public int Port { get; set; } = 1883;
        
        /// <summary>
        /// 客户端ID
        /// </summary>
        public string ClientId { get; set; }
        
        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; }
        
        /// <summary>
        /// 密码
        /// </summary>
        public string Password { get; set; }
        
        /// <summary>
        /// 是否使用TLS
        /// </summary>
        public bool UseTls { get; set; } = false;
        
        /// <summary>
        /// 是否清理会话
        /// </summary>
        public bool CleanSession { get; set; } = true;
        
        /// <summary>
        /// 保持连接间隔（秒）
        /// </summary>
        public int KeepAliveInterval { get; set; } = 60;
        
        /// <summary>
        /// 连接超时（秒）
        /// </summary>
        public int ConnectTimeout { get; set; } = 30;
        
        /// <summary>
        /// 自动重连
        /// </summary>
        public bool AutoReconnect { get; set; } = true;
        
        /// <summary>
        /// 重连间隔（秒）
        /// </summary>
        public int ReconnectInterval { get; set; } = 5;
        
        /// <summary>
        /// 最大重连次数
        /// </summary>
        public int MaxReconnectAttempts { get; set; } = -1; // -1表示无限重连
        
        /// <summary>
        /// 遗嘱消息配置
        /// </summary>
        public WillMessageConfiguration WillMessage { get; set; }
        
        /// <summary>
        /// 默认订阅主题
        /// </summary>
        public List<string> DefaultSubscriptions { get; set; } = new List<string>();
    }
    
    /// <summary>
    /// 遗嘱消息配置
    /// </summary>
    public class WillMessageConfiguration
    {
        /// <summary>
        /// 遗嘱主题
        /// </summary>
        public string Topic { get; set; }
        
        /// <summary>
        /// 遗嘱消息内容
        /// </summary>
        public string Message { get; set; }
        
        /// <summary>
        /// 是否保留
        /// </summary>
        public bool Retain { get; set; } = false;
        
        /// <summary>
        /// 服务质量等级
        /// </summary>
        public int QoS { get; set; } = 0;
    }
    
    /// <summary>
    /// 消息路由配置
    /// </summary>
    public class MessageRouterConfiguration
    {
        /// <summary>
        /// 消息队列容量
        /// </summary>
        public int QueueCapacity { get; set; } = 10000;
        
        /// <summary>
        /// 最大并发处理数
        /// </summary>
        public int MaxConcurrentHandlers { get; set; } = 10;
        
        /// <summary>
        /// 处理超时时间（毫秒）
        /// </summary>
        public int HandlerTimeoutMs { get; set; } = 30000;
        
        /// <summary>
        /// 是否启用性能监控
        /// </summary>
        public bool EnablePerformanceMonitoring { get; set; } = true;
        
        /// <summary>
        /// 性能监控间隔（秒）
        /// </summary>
        public int PerformanceMonitoringInterval { get; set; } = 60;
        
        /// <summary>
        /// 是否启用内存池优化
        /// </summary>
        public bool EnableMemoryPoolOptimization { get; set; } = true;
        
        /// <summary>
        /// 内存池最大数组长度
        /// </summary>
        public int MemoryPoolMaxArrayLength { get; set; } = 1024 * 1024; // 1MB
    }
    
    /// <summary>
    /// 设备控制配置
    /// </summary>
    public class DeviceControlConfiguration
    {
        /// <summary>
        /// 默认超时时间（秒）
        /// </summary>
        public int DefaultTimeoutSeconds { get; set; } = 30;
        
        /// <summary>
        /// 扫描超时时间（秒）
        /// </summary>
        public int ScanTimeoutSeconds { get; set; } = 60;
        
        /// <summary>
        /// 最大并发扫描数
        /// </summary>
        public int MaxConcurrentScans { get; set; } = 5;
        
        /// <summary>
        /// 设备缓存过期时间（分钟）
        /// </summary>
        public int DeviceCacheExpirationMinutes { get; set; } = 30;
        
        /// <summary>
        /// 是否启用设备状态缓存
        /// </summary>
        public bool EnableDeviceStatusCache { get; set; } = true;
        
        /// <summary>
        /// 心跳间隔（秒）
        /// </summary>
        public int HeartbeatInterval { get; set; } = 30;
        
        /// <summary>
        /// 网关名称
        /// </summary>
        public string GatewayName { get; set; } = "Gateway";
        
        /// <summary>
        /// 实例ID
        /// </summary>
        public string InstanceId { get; set; }
    }
    
    /// <summary>
    /// 性能监控配置
    /// </summary>
    public class PerformanceMonitoringConfiguration
    {
        /// <summary>
        /// 是否启用性能监控
        /// </summary>
        public bool Enabled { get; set; } = true;
        
        /// <summary>
        /// 监控间隔（秒）
        /// </summary>
        public int MonitoringInterval { get; set; } = 60;
        
        /// <summary>
        /// 是否启用内存监控
        /// </summary>
        public bool EnableMemoryMonitoring { get; set; } = true;
        
        /// <summary>
        /// 内存监控阈值（MB）
        /// </summary>
        public int MemoryThresholdMB { get; set; } = 500;
        
        /// <summary>
        /// 是否启用垃圾回收
        /// </summary>
        public bool EnableGarbageCollection { get; set; } = true;
        
        /// <summary>
        /// 垃圾回收间隔（秒）
        /// </summary>
        public int GarbageCollectionInterval { get; set; } = 300;
        
        /// <summary>
        /// 是否记录详细统计
        /// </summary>
        public bool EnableDetailedStats { get; set; } = false;
    }
    
    /// <summary>
    /// 日志配置
    /// </summary>
    public class LoggingConfiguration
    {
        /// <summary>
        /// 日志级别
        /// </summary>
        public string LogLevel { get; set; } = "Information";
        
        /// <summary>
        /// 是否启用结构化日志
        /// </summary>
        public bool EnableStructuredLogging { get; set; } = true;
        
        /// <summary>
        /// 是否记录性能日志
        /// </summary>
        public bool EnablePerformanceLogs { get; set; } = true;
        
        /// <summary>
        /// 是否记录调试日志
        /// </summary>
        public bool EnableDebugLogs { get; set; } = false;
        
        /// <summary>
        /// 日志文件路径
        /// </summary>
        public string LogFilePath { get; set; }
        
        /// <summary>
        /// 日志文件最大大小（MB）
        /// </summary>
        public int MaxLogFileSizeMB { get; set; } = 100;
        
        /// <summary>
        /// 保留日志文件数量
        /// </summary>
        public int RetainedLogFileCount { get; set; } = 10;
    }
    
    /// <summary>
    /// 配置验证结果
    /// </summary>
    public class ConfigurationValidationResult
    {
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; set; }
        
        /// <summary>
        /// 验证错误列表
        /// </summary>
        public List<ConfigurationValidationError> Errors { get; set; } = new List<ConfigurationValidationError>();
        
        /// <summary>
        /// 验证警告列表
        /// </summary>
        public List<ConfigurationValidationWarning> Warnings { get; set; } = new List<ConfigurationValidationWarning>();
    }
    
    /// <summary>
    /// 配置验证错误
    /// </summary>
    public class ConfigurationValidationError
    {
        /// <summary>
        /// 错误路径
        /// </summary>
        public string Path { get; set; }
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string Message { get; set; }
        
        /// <summary>
        /// 错误代码
        /// </summary>
        public string ErrorCode { get; set; }
    }
    
    /// <summary>
    /// 配置验证警告
    /// </summary>
    public class ConfigurationValidationWarning
    {
        /// <summary>
        /// 警告路径
        /// </summary>
        public string Path { get; set; }
        
        /// <summary>
        /// 警告消息
        /// </summary>
        public string Message { get; set; }
        
        /// <summary>
        /// 警告代码
        /// </summary>
        public string WarningCode { get; set; }
    }
}