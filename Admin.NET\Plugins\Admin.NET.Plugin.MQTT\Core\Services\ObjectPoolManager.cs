// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.ObjectPool;
using Admin.NET.Plugin.MQTT.Core.Services;
using static Admin.NET.Plugin.MQTT.Core.Services.PerformanceOptimizationService;

namespace Admin.NET.Plugin.MQTT.Services;

/// <summary>
/// 对象池管理器实现
/// 提供高效的对象复用机制，支持多种类型的对象池化管理
/// </summary>
public class ObjectPoolManager : IObjectPoolManager, IDisposable
{
    #region 私有字段
    
    private readonly ILogger<ObjectPoolManager> _logger;
    private readonly PerformanceConfiguration _configuration;
    
    // 对象池字典
    private readonly ConcurrentDictionary<Type, IObjectPool> _objectPools;
    private readonly ConcurrentDictionary<Type, ObjectPoolPolicy> _poolPolicies;
    
    // 统计信息
    private readonly ConcurrentDictionary<Type, ObjectPoolStatistics> _statistics;
    
    // 清理定时器
    private readonly Timer _cleanupTimer;
    
    private volatile bool _disposed;
    
    #endregion
    
    #region 构造函数
    
    /// <summary>
    /// 构造函数 - 初始化对象池管理器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="configuration">性能配置</param>
    public ObjectPoolManager(ILogger<ObjectPoolManager> logger, PerformanceConfiguration configuration)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        
        _objectPools = new ConcurrentDictionary<Type, IObjectPool>();
        _poolPolicies = new ConcurrentDictionary<Type, ObjectPoolPolicy>();
        _statistics = new ConcurrentDictionary<Type, ObjectPoolStatistics>();
        
        // 启动清理定时器
        var cleanupInterval = TimeSpan.FromMinutes(_configuration.ObjectPoolCleanupInterval);
        _cleanupTimer = new Timer(CleanupCallback, null, cleanupInterval, cleanupInterval);
        
        _logger.LogInformation("对象池管理器已初始化，清理间隔: {Interval} 分钟", _configuration.ObjectPoolCleanupInterval);
    }
    
    #endregion
    
    #region 公共方法
    
    /// <summary>
    /// 获取对象池
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <returns>对象池</returns>
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public IObjectPool<T> GetPool<T>() where T : class, new()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(ObjectPoolManager));
        
        var type = typeof(T);
        
        if (_objectPools.TryGetValue(type, out var existingPool))
        {
            return (ObjectPool<T>)existingPool;
        }
        
        // 创建新的对象池
        var policy = new DefaultPooledObjectPolicy<T>();
        var provider = new DefaultObjectPoolProvider
        {
            MaximumRetained = _configuration.ObjectPoolMaxSize
        };
        
        var pool = provider.Create(policy);
        
        // 添加到字典
        _objectPools.TryAdd(type, pool);
        _poolPolicies.TryAdd(type, policy);
        _statistics.TryAdd(type, new ObjectPoolStatistics { ObjectType = type.Name });
        
        _logger.LogDebug("创建对象池: {Type}，最大保留数: {MaxRetained}", type.Name, _configuration.ObjectPoolMaxSize);
        
        return pool;
    }
    
    /// <summary>
    /// 注册对象池
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="factory">对象工厂</param>
    /// <param name="maxSize">最大池大小</param>
    public void RegisterPool<T>(Func<T> factory, int maxSize = 100) where T : class
    {
        if (factory == null)
            throw new ArgumentNullException(nameof(factory));
        
        var type = typeof(T);
        
        if (_objectPools.ContainsKey(type))
        {
            _logger.LogWarning("对象池已存在: {Type}", type.Name);
            return;
        }
        
        var policy = new FactoryPooledObjectPolicy<T>(factory);
        var provider = new DefaultObjectPoolProvider
        {
            MaximumRetained = maxSize
        };
        
        var pool = provider.Create(policy);
        
        _objectPools.TryAdd(type, pool);
        _poolPolicies.TryAdd(type, policy);
        _statistics.TryAdd(type, new ObjectPoolStatistics { ObjectType = type.Name });
        
        _logger.LogDebug("注册对象池: {Type}，最大保留数: {MaxRetained}", type.Name, maxSize);
    }
    
    /// <summary>
    /// 获取对象池（带自定义策略）
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="policy">池化策略</param>
    /// <returns>对象池</returns>
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public IObjectPool<T> GetPoolWithPolicy<T>(IPooledObjectPolicy<T> policy) where T : class
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(ObjectPoolManager));
        
        if (policy == null)
            throw new ArgumentNullException(nameof(policy));
        
        var type = typeof(T);
        
        if (_objectPools.TryGetValue(type, out var existingPool))
        {
            return (ObjectPool<T>)existingPool;
        }
        
        // 创建新的对象池
        var provider = new DefaultObjectPoolProvider
        {
            MaximumRetained = _configuration.ObjectPoolMaxSize
        };
        
        var pool = provider.Create(policy);
        
        // 添加到字典
        _objectPools.TryAdd(type, pool);
        _poolPolicies.TryAdd(type, policy as ObjectPoolPolicy);
        _statistics.TryAdd(type, new ObjectPoolStatistics { ObjectType = type.Name });
        
        _logger.LogDebug("创建自定义策略对象池: {Type}，策略: {Policy}", type.Name, policy.GetType().Name);
        
        return pool;
    }
    
    /// <summary>
    /// 获取对象
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <returns>对象实例</returns>
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public T Get<T>() where T : class, new()
    {
        var pool = GetPool<T>();
        var obj = pool.Get();
        
        // 更新统计信息
        UpdateStatistics<T>(true);
        
        _logger.LogTrace("从对象池获取对象: {Type}", typeof(T).Name);
        
        return obj;
    }
    
    /// <summary>
    /// 归还对象
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="obj">要归还的对象</param>
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public void Return<T>(T obj) where T : class, new()
    {
        if (_disposed || obj == null)
            return;
        
        var pool = GetPool<T>();
        pool.Return(obj);
        
        // 更新统计信息
        UpdateStatistics<T>(false);
        
        _logger.LogTrace("归还对象到对象池: {Type}", typeof(T).Name);
    }
    
    /// <summary>
    /// 获取或创建对象
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="factory">对象工厂方法</param>
    /// <returns>对象实例</returns>
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public T GetOrCreate<T>(Func<T> factory) where T : class
    {
        if (factory == null)
            throw new ArgumentNullException(nameof(factory));
        
        try
        {
            // 尝试从池中获取
            if (typeof(T).GetConstructor(Type.EmptyTypes) != null)
            {
                var pool = GetPool<T>();
                var obj = pool.Get();
                
                if (obj != null)
                {
                    UpdateStatistics<T>(true);
                    _logger.LogTrace("从对象池获取对象: {Type}", typeof(T).Name);
                    return obj;
                }
            }
            
            // 使用工厂方法创建
            var newObj = factory();
            UpdateStatistics<T>(true, false);
            
            _logger.LogTrace("使用工厂方法创建对象: {Type}", typeof(T).Name);
            
            return newObj;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取或创建对象失败: {Type}", typeof(T).Name);
            throw;
        }
    }
    
    /// <summary>
    /// 使用对象（自动归还）
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <typeparam name="TResult">结果类型</typeparam>
    /// <param name="action">使用对象的操作</param>
    /// <returns>操作结果</returns>
    public TResult Use<T, TResult>(Func<T, TResult> action) where T : class, new()
    {
        if (action == null)
            throw new ArgumentNullException(nameof(action));
        
        var obj = Get<T>();
        try
        {
            return action(obj);
        }
        finally
        {
            Return(obj);
        }
    }
    
    /// <summary>
    /// 使用对象（自动归还，无返回值）
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="action">使用对象的操作</param>
    public void Use<T>(Action<T> action) where T : class, new()
    {
        if (action == null)
            throw new ArgumentNullException(nameof(action));
        
        var obj = Get<T>();
        try
        {
            action(obj);
        }
        finally
        {
            Return(obj);
        }
    }
    
    /// <summary>
    /// 异步使用对象（自动归还）
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <typeparam name="TResult">结果类型</typeparam>
    /// <param name="action">使用对象的异步操作</param>
    /// <returns>操作结果</returns>
    public async Task<TResult> UseAsync<T, TResult>(Func<T, Task<TResult>> action) where T : class, new()
    {
        if (action == null)
            throw new ArgumentNullException(nameof(action));
        
        var obj = Get<T>();
        try
        {
            return await action(obj).ConfigureAwait(false);
        }
        finally
        {
            Return(obj);
        }
    }
    
    /// <summary>
    /// 异步使用对象（自动归还，无返回值）
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="action">使用对象的异步操作</param>
    public async Task UseAsync<T>(Func<T, Task> action) where T : class, new()
    {
        if (action == null)
            throw new ArgumentNullException(nameof(action));
        
        var obj = Get<T>();
        try
        {
            await action(obj).ConfigureAwait(false);
        }
        finally
        {
            Return(obj);
        }
    }
    
    /// <summary>
    /// 清空指定类型的对象池
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    public void ClearPool<T>() where T : class
    {
        var type = typeof(T);
        
        if (_objectPools.TryRemove(type, out var pool))
        {
            _poolPolicies.TryRemove(type, out _);
            
            // 重置统计信息
            if (_statistics.TryGetValue(type, out var stats))
            {
                stats.Reset();
            }
            
            _logger.LogDebug("清空对象池: {Type}", type.Name);
        }
    }
    
    /// <summary>
    /// 获取对象池统计信息
    /// </summary>
    /// <returns>对象池统计信息</returns>
    public ObjectPoolStatistics GetStatistics()
    {
        var totalGets = _statistics.Values.Sum(s => s.TotalGets);
        var totalReturns = _statistics.Values.Sum(s => s.TotalReturns);
        var totalHits = _statistics.Values.Sum(s => s.PoolHits);
        var totalMisses = _statistics.Values.Sum(s => s.PoolMisses);
        
        return new ObjectPoolStatistics
        {
            ObjectType = "Overall",
            TotalGets = totalGets,
            TotalReturns = totalReturns,
            PoolHits = totalHits,
            PoolMisses = totalMisses
        };
    }
    
    /// <summary>
    /// 获取所有对象池的详细统计信息
    /// </summary>
    /// <returns>所有对象池的统计信息</returns>
    public Dictionary<string, ObjectPoolStatistics> GetDetailedStatistics()
    {
        var result = new Dictionary<string, ObjectPoolStatistics>();
        
        foreach (var kvp in _statistics)
        {
            var stats = kvp.Value;
            result[kvp.Key.Name] = new ObjectPoolStatistics
            {
                ObjectType = stats.ObjectType,
                TotalGets = stats.TotalGets,
                TotalReturns = stats.TotalReturns,
                PoolHits = stats.PoolHits,
                PoolMisses = stats.PoolMisses,
                HitRate = stats.HitRate
            };
        }
        
        return result;
    }
    
    /// <summary>
    /// 获取指定类型的统计信息
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <returns>统计信息</returns>
    public ObjectPoolStatistics GetStatistics<T>() where T : class
    {
        var type = typeof(T);
        
        if (_statistics.TryGetValue(type, out var stats))
        {
            return new ObjectPoolStatistics
            {
                ObjectType = stats.ObjectType,
                TotalGets = stats.TotalGets,
                TotalReturns = stats.TotalReturns,
                PoolHits = stats.PoolHits,
                PoolMisses = stats.PoolMisses,
                HitRate = stats.HitRate
            };
        }
        
        return new ObjectPoolStatistics { ObjectType = type.Name };
    }
    
    #endregion
    
    #region 私有方法
    
    /// <summary>
    /// 更新统计信息
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="isGet">是否为获取操作</param>
    /// <param name="isPoolHit">是否为池命中</param>
    private void UpdateStatistics<T>(bool isGet, bool isPoolHit = true) where T : class
    {
        var type = typeof(T);
        
        if (_statistics.TryGetValue(type, out var stats))
        {
            if (isGet)
            {
                Interlocked.Increment(ref stats.TotalGets);
                
                if (isPoolHit)
                {
                    Interlocked.Increment(ref stats.PoolHits);
                }
                else
                {
                    Interlocked.Increment(ref stats.PoolMisses);
                }
            }
            else
            {
                Interlocked.Increment(ref stats.TotalReturns);
            }
        }
    }
    
    /// <summary>
    /// 清理回调
    /// </summary>
    /// <param name="state">状态</param>
    private void CleanupCallback(object state)
    {
        if (_disposed)
            return;
        
        try
        {
            var lowHitRateTypes = new List<Type>();
            
            // 检查命中率低的对象池
            foreach (var kvp in _statistics)
            {
                var stats = kvp.Value;
                if (stats.HitRate < _configuration.ObjectPoolHitRateThreshold && stats.TotalGets > 100)
                {
                    lowHitRateTypes.Add(kvp.Key);
                }
            }
            
            // 清理命中率低的对象池
            foreach (var type in lowHitRateTypes)
            {
                if (_objectPools.TryRemove(type, out _))
                {
                    _poolPolicies.TryRemove(type, out _);
                    
                    if (_statistics.TryGetValue(type, out var stats))
                    {
                        stats.Reset();
                    }
                    
                    _logger.LogDebug("清理低命中率对象池: {Type}，命中率: {HitRate:P2}", 
                        type.Name, stats?.HitRate ?? 0);
                }
            }
            
            _logger.LogTrace("对象池清理完成，清理了 {Count} 个低命中率对象池", lowHitRateTypes.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "对象池清理失败");
        }
    }
    
    #endregion
    
    #region IDisposable实现
    
    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;
            
            // 停止清理定时器
            _cleanupTimer?.Dispose();
            
            // 清空所有对象池
            _objectPools.Clear();
            _poolPolicies.Clear();
            
            // 记录最终统计信息
            var totalGets = _statistics.Values.Sum(s => s.TotalGets);
            var totalReturns = _statistics.Values.Sum(s => s.TotalReturns);
            var totalHits = _statistics.Values.Sum(s => s.PoolHits);
            var totalMisses = _statistics.Values.Sum(s => s.PoolMisses);
            
            _logger.LogInformation("对象池管理器已释放资源，总获取: {TotalGets}，总归还: {TotalReturns}，" +
                "总命中: {TotalHits}，总未命中: {TotalMisses}，整体命中率: {OverallHitRate:P2}", 
                totalGets, totalReturns, totalHits, totalMisses, 
                totalGets > 0 ? (double)totalHits / totalGets : 0);
            
            _statistics.Clear();
        }
    }
    
    #endregion
}

/// <summary>
/// 工厂池化对象策略
/// </summary>
/// <typeparam name="T">对象类型</typeparam>
public class FactoryPooledObjectPolicy<T> : IPooledObjectPolicy<T> where T : class
{
    private readonly Func<T> _factory;
    
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="factory">对象工厂</param>
    public FactoryPooledObjectPolicy(Func<T> factory)
    {
        _factory = factory ?? throw new ArgumentNullException(nameof(factory));
    }
    
    /// <summary>
    /// 创建对象
    /// </summary>
    /// <returns>新对象</returns>
    public T Create()
    {
        return _factory();
    }
    
    /// <summary>
    /// 返回对象到池中
    /// </summary>
    /// <param name="obj">要返回的对象</param>
    /// <returns>是否成功返回</returns>
    public bool Return(T obj)
    {
        if (obj is IResettable resettable)
        {
            resettable.Reset();
        }
        
        return true;
    }
}

/// <summary>
/// 默认池化对象策略
/// 提供对象的创建和重置逻辑
/// </summary>
public class DefaultPooledObjectPolicy<T> : PooledObjectPolicy<T> where T : class, new()
{
    /// <summary>
    /// 创建对象
    /// </summary>
    /// <returns>新对象实例</returns>
    public override T Create()
    {
        return new T();
    }
    
    /// <summary>
    /// 归还对象时的处理
    /// </summary>
    /// <param name="obj">要归还的对象</param>
    /// <returns>是否成功归还</returns>
    public override bool Return(T obj)
    {
        // 如果对象实现了IResettable接口，则重置对象
        if (obj is IResettable resettable)
        {
            try
            {
                resettable.Reset();
                return true;
            }
            catch
            {
                return false;
            }
        }
        
        return true;
    }
}

/// <summary>
/// 可重置接口
/// 对象池中的对象可以实现此接口来提供重置逻辑
/// </summary>
public interface IResettable
{
    /// <summary>
    /// 重置对象状态
    /// </summary>
    void Reset();
}

/// <summary>
/// 对象池统计信息
/// 记录对象池的使用情况和性能指标
/// </summary>
public class ObjectPoolStatistics
{
    private long _totalGets;
    private long _totalReturns;
    private long _poolHits;
    private long _poolMisses;
    
    /// <summary>
    /// 对象类型名称
    /// </summary>
    public string ObjectType { get; set; } = string.Empty;
    
    /// <summary>
    /// 总获取次数
    /// </summary>
    public long TotalGets
    {
        get => Interlocked.Read(ref _totalGets);
        internal set => Interlocked.Exchange(ref _totalGets, value);
    }
    
    /// <summary>
    /// 总归还次数
    /// </summary>
    public long TotalReturns
    {
        get => Interlocked.Read(ref _totalReturns);
        internal set => Interlocked.Exchange(ref _totalReturns, value);
    }
    
    /// <summary>
    /// 池命中次数
    /// </summary>
    public long PoolHits
    {
        get => Interlocked.Read(ref _poolHits);
        internal set => Interlocked.Exchange(ref _poolHits, value);
    }
    
    /// <summary>
    /// 池未命中次数
    /// </summary>
    public long PoolMisses
    {
        get => Interlocked.Read(ref _poolMisses);
        internal set => Interlocked.Exchange(ref _poolMisses, value);
    }
    
    /// <summary>
    /// 命中率
    /// </summary>
    public double HitRate
    {
        get
        {
            var gets = TotalGets;
            return gets > 0 ? (double)PoolHits / gets : 0;
        }
    }
    
    /// <summary>
    /// 重置统计信息
    /// </summary>
    internal void Reset()
    {
        Interlocked.Exchange(ref _totalGets, 0);
        Interlocked.Exchange(ref _totalReturns, 0);
        Interlocked.Exchange(ref _poolHits, 0);
        Interlocked.Exchange(ref _poolMisses, 0);
    }
}