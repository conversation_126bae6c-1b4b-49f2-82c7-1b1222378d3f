/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { ControlTypeStatItem } from './control-type-stat-item';
import { DeviceControlStatItem } from './device-control-stat-item';
/**
 * 控制统计输出参数
 * @export
 * @interface EnergyControlStatOutput
 */
export interface EnergyControlStatOutput {
    /**
     * 统计时间
     * @type {Date}
     * @memberof EnergyControlStatOutput
     */
    statTime?: Date;
    /**
     * 控制总次数
     * @type {number}
     * @memberof EnergyControlStatOutput
     */
    totalCount?: number;
    /**
     * 成功次数
     * @type {number}
     * @memberof EnergyControlStatOutput
     */
    successCount?: number;
    /**
     * 失败次数
     * @type {number}
     * @memberof EnergyControlStatOutput
     */
    failedCount?: number;
    /**
     * 超时次数
     * @type {number}
     * @memberof EnergyControlStatOutput
     */
    timeoutCount?: number;
    /**
     * 执行中次数
     * @type {number}
     * @memberof EnergyControlStatOutput
     */
    executingCount?: number;
    /**
     * 手动控制次数
     * @type {number}
     * @memberof EnergyControlStatOutput
     */
    manualCount?: number;
    /**
     * 自动控制次数
     * @type {number}
     * @memberof EnergyControlStatOutput
     */
    autoCount?: number;
    /**
     * 场景控制次数
     * @type {number}
     * @memberof EnergyControlStatOutput
     */
    sceneCount?: number;
    /**
     * 定时控制次数
     * @type {number}
     * @memberof EnergyControlStatOutput
     */
    timerCount?: number;
    /**
     * 成功率
     * @type {number}
     * @memberof EnergyControlStatOutput
     */
    successRate?: number;
    /**
     * 今日控制次数
     * @type {number}
     * @memberof EnergyControlStatOutput
     */
    todayCount?: number;
    /**
     * 本月控制次数
     * @type {number}
     * @memberof EnergyControlStatOutput
     */
    monthCount?: number;
    /**
     * 平均响应时间(ms)
     * @type {number}
     * @memberof EnergyControlStatOutput
     */
    avgResponseTime?: number;
    /**
     * 平均执行时长(ms)
     * @type {number}
     * @memberof EnergyControlStatOutput
     */
    avgExecuteDuration?: number;
    /**
     * 按控制类型统计
     * @type {Array<ControlTypeStatItem>}
     * @memberof EnergyControlStatOutput
     */
    typeStats?: Array<ControlTypeStatItem> | null;
    /**
     * 按设备统计
     * @type {Array<DeviceControlStatItem>}
     * @memberof EnergyControlStatOutput
     */
    deviceStats?: Array<DeviceControlStatItem> | null;
}
