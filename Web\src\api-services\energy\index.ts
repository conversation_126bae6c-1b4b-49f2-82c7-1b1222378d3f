import { service as request } from '/@/utils/request';
import { useBaseApi } from '../base';

/**
 * 节能灯管理API接口集合
 * @method getDevicePage 获取设备分页列表
 * @method getDeviceDetail 获取设备详情
 * @method addDevice 添加设备
 * @method updateDevice 更新设备
 * @method deleteDevice 删除设备
 * @method getConsumptionPage 获取能耗分页列表
 * @method getFaultPage 获取故障分页列表
 * @method getScenePage 获取场景分页列表
 * @method getAlarmPage 获取报警分页列表
 */
export function useEnergyApi() {
	return {
		// 设备管理
		device: {
			...useBaseApi('energyDevice'),
		},
		// 能耗管理
		consumption: {
			...useBaseApi('energyConsumption'),
			// 获取能耗统计
			getStats: (data: any) => {
				return request({
					url: '/api/energyConsumption/stat',
					method: 'get',
					params: data,
				});
			},
			// 获取能耗趋势
			getTrend: (data: any) => {
				return request({
					url: '/api/EnergyConsumption/GetTrend',
					method: 'get',
					params: data,
				});
			},
		},
		// 故障管理
		fault: {
			...useBaseApi('energyFault'),
			// 处理故障
			handleFault: (data: any) => {
				return request({
					url: '/api/energyFault/handle',
					method: 'post',
					data,
				});
			},
		},
		// 场景管理
		scene: {
			...useBaseApi('energyScene'),
			// 执行场景
			executeScene: (data: any) => {
				return request({
					url: '/api/energyScene/execute',
					method: 'post',
					data,
				});
			},
		},
		// 报警管理
		alarm: {
			...useBaseApi('alarm'),
			// 确认报警
			confirmAlarm: (data: any) => {
				return request({
					url: '/api/alarm/confirm',
					method: 'post',
					data,
				});
			},
		},
	};
}