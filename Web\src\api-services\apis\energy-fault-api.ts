/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AddEnergyFaultInput } from '../models';
import { AdminResultEnergyFaultDetailOutput } from '../models';
import { AdminResultEnergyFaultStatOutput } from '../models';
import { AdminResultFileStreamResult } from '../models';
import { AdminResultIActionResult } from '../models';
import { AdminResultInt64 } from '../models';
import { AdminResultListEnergyFaultRankOutput } from '../models';
import { AdminResultListEnergyFaultTrendOutput } from '../models';
import { AdminResultSqlSugarPagedListEnergyFaultOutput } from '../models';
import { DeleteEnergyFaultInput } from '../models';
import { EnergyFaultInput } from '../models';
import { EnergyFaultStatusInput } from '../models';
import { Filter } from '../models';
import { FilterLogicEnum } from '../models';
import { FilterOperatorEnum } from '../models';
import { RepairFaultInput } from '../models';
import { UpdateEnergyFaultInput } from '../models';
/**
 * EnergyFaultApi - axios parameter creator
 * @export
 */
export const EnergyFaultApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加故障记录 🔖
         * @param {AddEnergyFaultInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyFaultAddPost: async (body?: AddEnergyFaultInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyFault/add`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 删除故障记录 🔖
         * @param {DeleteEnergyFaultInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyFaultDeletePost: async (body?: DeleteEnergyFaultInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyFault/delete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取故障详情 🔖
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyFaultDetailGet: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiEnergyFaultDetailGet.');
            }
            const localVarPath = `/api/energyFault/detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (id !== undefined) {
                localVarQueryParameter['Id'] = id;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 导出故障数据 🔖
         * @param {number} [deviceId] 设备ID
         * @param {string} [faultCode] 故障编码
         * @param {number} [faultLevel] 故障等级
         * @param {string} [faultType] 故障类型
         * @param {number} [faultStatus] 故障状态
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyFaultExportDataPost: async (deviceId?: number, faultCode?: string, faultLevel?: number, faultType?: string, faultStatus?: number, startTime?: Date, endTime?: Date, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyFault/exportData`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (deviceId !== undefined) {
                localVarQueryParameter['DeviceId'] = deviceId;
            }

            if (faultCode !== undefined) {
                localVarQueryParameter['FaultCode'] = faultCode;
            }

            if (faultLevel !== undefined) {
                localVarQueryParameter['FaultLevel'] = faultLevel;
            }

            if (faultType !== undefined) {
                localVarQueryParameter['FaultType'] = faultType;
            }

            if (faultStatus !== undefined) {
                localVarQueryParameter['FaultStatus'] = faultStatus;
            }

            if (startTime !== undefined) {
                localVarQueryParameter['StartTime'] = (startTime as any instanceof Date) ?
                    (startTime as any).toISOString() :
                    startTime;
            }

            if (endTime !== undefined) {
                localVarQueryParameter['EndTime'] = (endTime as any instanceof Date) ?
                    (endTime as any).toISOString() :
                    endTime;
            }

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            if (field !== undefined) {
                localVarQueryParameter['Field'] = field;
            }

            if (order !== undefined) {
                localVarQueryParameter['Order'] = order;
            }

            if (descStr !== undefined) {
                localVarQueryParameter['DescStr'] = descStr;
            }

            if (searchFields) {
                localVarQueryParameter['Search.Fields'] = searchFields;
            }

            if (searchKeyword !== undefined) {
                localVarQueryParameter['Search.Keyword'] = searchKeyword;
            }

            if (keyword !== undefined) {
                localVarQueryParameter['Keyword'] = keyword;
            }

            if (filterLogic !== undefined) {
                localVarQueryParameter['Filter.Logic'] = filterLogic;
            }

            if (filterFilters) {
                localVarQueryParameter['Filter.Filters'] = filterFilters;
            }

            if (filterField !== undefined) {
                localVarQueryParameter['Filter.Field'] = filterField;
            }

            if (filterOperator !== undefined) {
                localVarQueryParameter['Filter.Operator'] = filterOperator;
            }

            if (filterValue !== undefined) {
                localVarQueryParameter['Filter.Value'] = filterValue;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 导出故障数据到Excel 🔖
         * @param {EnergyFaultInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyFaultExportExcelPost: async (body?: EnergyFaultInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyFault/exportExcel`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取故障记录分页列表 🔖
         * @param {number} [deviceId] 设备ID
         * @param {string} [faultCode] 故障编码
         * @param {number} [faultLevel] 故障等级
         * @param {string} [faultType] 故障类型
         * @param {number} [faultStatus] 故障状态
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyFaultPageGet: async (deviceId?: number, faultCode?: string, faultLevel?: number, faultType?: string, faultStatus?: number, startTime?: Date, endTime?: Date, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyFault/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (deviceId !== undefined) {
                localVarQueryParameter['DeviceId'] = deviceId;
            }

            if (faultCode !== undefined) {
                localVarQueryParameter['FaultCode'] = faultCode;
            }

            if (faultLevel !== undefined) {
                localVarQueryParameter['FaultLevel'] = faultLevel;
            }

            if (faultType !== undefined) {
                localVarQueryParameter['FaultType'] = faultType;
            }

            if (faultStatus !== undefined) {
                localVarQueryParameter['FaultStatus'] = faultStatus;
            }

            if (startTime !== undefined) {
                localVarQueryParameter['StartTime'] = (startTime as any instanceof Date) ?
                    (startTime as any).toISOString() :
                    startTime;
            }

            if (endTime !== undefined) {
                localVarQueryParameter['EndTime'] = (endTime as any instanceof Date) ?
                    (endTime as any).toISOString() :
                    endTime;
            }

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            if (field !== undefined) {
                localVarQueryParameter['Field'] = field;
            }

            if (order !== undefined) {
                localVarQueryParameter['Order'] = order;
            }

            if (descStr !== undefined) {
                localVarQueryParameter['DescStr'] = descStr;
            }

            if (searchFields) {
                localVarQueryParameter['Search.Fields'] = searchFields;
            }

            if (searchKeyword !== undefined) {
                localVarQueryParameter['Search.Keyword'] = searchKeyword;
            }

            if (keyword !== undefined) {
                localVarQueryParameter['Keyword'] = keyword;
            }

            if (filterLogic !== undefined) {
                localVarQueryParameter['Filter.Logic'] = filterLogic;
            }

            if (filterFilters) {
                localVarQueryParameter['Filter.Filters'] = filterFilters;
            }

            if (filterField !== undefined) {
                localVarQueryParameter['Filter.Field'] = filterField;
            }

            if (filterOperator !== undefined) {
                localVarQueryParameter['Filter.Operator'] = filterOperator;
            }

            if (filterValue !== undefined) {
                localVarQueryParameter['Filter.Value'] = filterValue;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取设备故障排行 🔖
         * @param {string} statType 统计类型
         * @param {number} [deviceId] 设备ID
         * @param {Array<number>} [deviceIds] 设备ID列表
         * @param {number} [groupId] 分组ID
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyFaultRankGet: async (statType: string, deviceId?: number, deviceIds?: Array<number>, groupId?: number, startTime?: Date, endTime?: Date, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'statType' is not null or undefined
            if (statType === null || statType === undefined) {
                throw new RequiredError('statType','Required parameter statType was null or undefined when calling apiEnergyFaultRankGet.');
            }
            const localVarPath = `/api/energyFault/rank`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (deviceId !== undefined) {
                localVarQueryParameter['DeviceId'] = deviceId;
            }

            if (deviceIds) {
                localVarQueryParameter['DeviceIds'] = deviceIds;
            }

            if (groupId !== undefined) {
                localVarQueryParameter['GroupId'] = groupId;
            }

            if (startTime !== undefined) {
                localVarQueryParameter['StartTime'] = (startTime as any instanceof Date) ?
                    (startTime as any).toISOString() :
                    startTime;
            }

            if (endTime !== undefined) {
                localVarQueryParameter['EndTime'] = (endTime as any instanceof Date) ?
                    (endTime as any).toISOString() :
                    endTime;
            }

            if (statType !== undefined) {
                localVarQueryParameter['StatType'] = statType;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 故障维修 🔖
         * @param {RepairFaultInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyFaultRepairPost: async (body?: RepairFaultInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyFault/repair`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 设置故障状态 🔖
         * @param {EnergyFaultStatusInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyFaultSetStatusPost: async (body?: EnergyFaultStatusInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyFault/setStatus`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取故障统计数据 🔖
         * @param {string} statType 统计类型
         * @param {number} [deviceId] 设备ID
         * @param {Array<number>} [deviceIds] 设备ID列表
         * @param {number} [groupId] 分组ID
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyFaultStatGet: async (statType: string, deviceId?: number, deviceIds?: Array<number>, groupId?: number, startTime?: Date, endTime?: Date, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'statType' is not null or undefined
            if (statType === null || statType === undefined) {
                throw new RequiredError('statType','Required parameter statType was null or undefined when calling apiEnergyFaultStatGet.');
            }
            const localVarPath = `/api/energyFault/stat`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (deviceId !== undefined) {
                localVarQueryParameter['DeviceId'] = deviceId;
            }

            if (deviceIds) {
                localVarQueryParameter['DeviceIds'] = deviceIds;
            }

            if (groupId !== undefined) {
                localVarQueryParameter['GroupId'] = groupId;
            }

            if (startTime !== undefined) {
                localVarQueryParameter['StartTime'] = (startTime as any instanceof Date) ?
                    (startTime as any).toISOString() :
                    startTime;
            }

            if (endTime !== undefined) {
                localVarQueryParameter['EndTime'] = (endTime as any instanceof Date) ?
                    (endTime as any).toISOString() :
                    endTime;
            }

            if (statType !== undefined) {
                localVarQueryParameter['StatType'] = statType;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取故障趋势数据 🔖
         * @param {string} statType 统计类型
         * @param {number} [deviceId] 设备ID
         * @param {Array<number>} [deviceIds] 设备ID列表
         * @param {number} [groupId] 分组ID
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyFaultTrendGet: async (statType: string, deviceId?: number, deviceIds?: Array<number>, groupId?: number, startTime?: Date, endTime?: Date, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'statType' is not null or undefined
            if (statType === null || statType === undefined) {
                throw new RequiredError('statType','Required parameter statType was null or undefined when calling apiEnergyFaultTrendGet.');
            }
            const localVarPath = `/api/energyFault/trend`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (deviceId !== undefined) {
                localVarQueryParameter['DeviceId'] = deviceId;
            }

            if (deviceIds) {
                localVarQueryParameter['DeviceIds'] = deviceIds;
            }

            if (groupId !== undefined) {
                localVarQueryParameter['GroupId'] = groupId;
            }

            if (startTime !== undefined) {
                localVarQueryParameter['StartTime'] = (startTime as any instanceof Date) ?
                    (startTime as any).toISOString() :
                    startTime;
            }

            if (endTime !== undefined) {
                localVarQueryParameter['EndTime'] = (endTime as any instanceof Date) ?
                    (endTime as any).toISOString() :
                    endTime;
            }

            if (statType !== undefined) {
                localVarQueryParameter['StatType'] = statType;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新故障记录 🔖
         * @param {UpdateEnergyFaultInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyFaultUpdatePost: async (body?: UpdateEnergyFaultInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyFault/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * EnergyFaultApi - functional programming interface
 * @export
 */
export const EnergyFaultApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加故障记录 🔖
         * @param {AddEnergyFaultInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyFaultAddPost(body?: AddEnergyFaultInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt64>>> {
            const localVarAxiosArgs = await EnergyFaultApiAxiosParamCreator(configuration).apiEnergyFaultAddPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 删除故障记录 🔖
         * @param {DeleteEnergyFaultInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyFaultDeletePost(body?: DeleteEnergyFaultInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await EnergyFaultApiAxiosParamCreator(configuration).apiEnergyFaultDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取故障详情 🔖
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyFaultDetailGet(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultEnergyFaultDetailOutput>>> {
            const localVarAxiosArgs = await EnergyFaultApiAxiosParamCreator(configuration).apiEnergyFaultDetailGet(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 导出故障数据 🔖
         * @param {number} [deviceId] 设备ID
         * @param {string} [faultCode] 故障编码
         * @param {number} [faultLevel] 故障等级
         * @param {string} [faultType] 故障类型
         * @param {number} [faultStatus] 故障状态
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyFaultExportDataPost(deviceId?: number, faultCode?: string, faultLevel?: number, faultType?: string, faultStatus?: number, startTime?: Date, endTime?: Date, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultFileStreamResult>>> {
            const localVarAxiosArgs = await EnergyFaultApiAxiosParamCreator(configuration).apiEnergyFaultExportDataPost(deviceId, faultCode, faultLevel, faultType, faultStatus, startTime, endTime, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 导出故障数据到Excel 🔖
         * @param {EnergyFaultInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyFaultExportExcelPost(body?: EnergyFaultInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultIActionResult>>> {
            const localVarAxiosArgs = await EnergyFaultApiAxiosParamCreator(configuration).apiEnergyFaultExportExcelPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取故障记录分页列表 🔖
         * @param {number} [deviceId] 设备ID
         * @param {string} [faultCode] 故障编码
         * @param {number} [faultLevel] 故障等级
         * @param {string} [faultType] 故障类型
         * @param {number} [faultStatus] 故障状态
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyFaultPageGet(deviceId?: number, faultCode?: string, faultLevel?: number, faultType?: string, faultStatus?: number, startTime?: Date, endTime?: Date, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSqlSugarPagedListEnergyFaultOutput>>> {
            const localVarAxiosArgs = await EnergyFaultApiAxiosParamCreator(configuration).apiEnergyFaultPageGet(deviceId, faultCode, faultLevel, faultType, faultStatus, startTime, endTime, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取设备故障排行 🔖
         * @param {string} statType 统计类型
         * @param {number} [deviceId] 设备ID
         * @param {Array<number>} [deviceIds] 设备ID列表
         * @param {number} [groupId] 分组ID
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyFaultRankGet(statType: string, deviceId?: number, deviceIds?: Array<number>, groupId?: number, startTime?: Date, endTime?: Date, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultListEnergyFaultRankOutput>>> {
            const localVarAxiosArgs = await EnergyFaultApiAxiosParamCreator(configuration).apiEnergyFaultRankGet(statType, deviceId, deviceIds, groupId, startTime, endTime, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 故障维修 🔖
         * @param {RepairFaultInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyFaultRepairPost(body?: RepairFaultInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await EnergyFaultApiAxiosParamCreator(configuration).apiEnergyFaultRepairPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 设置故障状态 🔖
         * @param {EnergyFaultStatusInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyFaultSetStatusPost(body?: EnergyFaultStatusInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await EnergyFaultApiAxiosParamCreator(configuration).apiEnergyFaultSetStatusPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取故障统计数据 🔖
         * @param {string} statType 统计类型
         * @param {number} [deviceId] 设备ID
         * @param {Array<number>} [deviceIds] 设备ID列表
         * @param {number} [groupId] 分组ID
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyFaultStatGet(statType: string, deviceId?: number, deviceIds?: Array<number>, groupId?: number, startTime?: Date, endTime?: Date, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultEnergyFaultStatOutput>>> {
            const localVarAxiosArgs = await EnergyFaultApiAxiosParamCreator(configuration).apiEnergyFaultStatGet(statType, deviceId, deviceIds, groupId, startTime, endTime, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取故障趋势数据 🔖
         * @param {string} statType 统计类型
         * @param {number} [deviceId] 设备ID
         * @param {Array<number>} [deviceIds] 设备ID列表
         * @param {number} [groupId] 分组ID
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyFaultTrendGet(statType: string, deviceId?: number, deviceIds?: Array<number>, groupId?: number, startTime?: Date, endTime?: Date, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultListEnergyFaultTrendOutput>>> {
            const localVarAxiosArgs = await EnergyFaultApiAxiosParamCreator(configuration).apiEnergyFaultTrendGet(statType, deviceId, deviceIds, groupId, startTime, endTime, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新故障记录 🔖
         * @param {UpdateEnergyFaultInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyFaultUpdatePost(body?: UpdateEnergyFaultInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await EnergyFaultApiAxiosParamCreator(configuration).apiEnergyFaultUpdatePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * EnergyFaultApi - factory interface
 * @export
 */
export const EnergyFaultApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 增加故障记录 🔖
         * @param {AddEnergyFaultInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyFaultAddPost(body?: AddEnergyFaultInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt64>> {
            return EnergyFaultApiFp(configuration).apiEnergyFaultAddPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 删除故障记录 🔖
         * @param {DeleteEnergyFaultInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyFaultDeletePost(body?: DeleteEnergyFaultInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return EnergyFaultApiFp(configuration).apiEnergyFaultDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取故障详情 🔖
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyFaultDetailGet(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultEnergyFaultDetailOutput>> {
            return EnergyFaultApiFp(configuration).apiEnergyFaultDetailGet(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 导出故障数据 🔖
         * @param {number} [deviceId] 设备ID
         * @param {string} [faultCode] 故障编码
         * @param {number} [faultLevel] 故障等级
         * @param {string} [faultType] 故障类型
         * @param {number} [faultStatus] 故障状态
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyFaultExportDataPost(deviceId?: number, faultCode?: string, faultLevel?: number, faultType?: string, faultStatus?: number, startTime?: Date, endTime?: Date, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultFileStreamResult>> {
            return EnergyFaultApiFp(configuration).apiEnergyFaultExportDataPost(deviceId, faultCode, faultLevel, faultType, faultStatus, startTime, endTime, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 导出故障数据到Excel 🔖
         * @param {EnergyFaultInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyFaultExportExcelPost(body?: EnergyFaultInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultIActionResult>> {
            return EnergyFaultApiFp(configuration).apiEnergyFaultExportExcelPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取故障记录分页列表 🔖
         * @param {number} [deviceId] 设备ID
         * @param {string} [faultCode] 故障编码
         * @param {number} [faultLevel] 故障等级
         * @param {string} [faultType] 故障类型
         * @param {number} [faultStatus] 故障状态
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyFaultPageGet(deviceId?: number, faultCode?: string, faultLevel?: number, faultType?: string, faultStatus?: number, startTime?: Date, endTime?: Date, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSqlSugarPagedListEnergyFaultOutput>> {
            return EnergyFaultApiFp(configuration).apiEnergyFaultPageGet(deviceId, faultCode, faultLevel, faultType, faultStatus, startTime, endTime, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取设备故障排行 🔖
         * @param {string} statType 统计类型
         * @param {number} [deviceId] 设备ID
         * @param {Array<number>} [deviceIds] 设备ID列表
         * @param {number} [groupId] 分组ID
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyFaultRankGet(statType: string, deviceId?: number, deviceIds?: Array<number>, groupId?: number, startTime?: Date, endTime?: Date, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultListEnergyFaultRankOutput>> {
            return EnergyFaultApiFp(configuration).apiEnergyFaultRankGet(statType, deviceId, deviceIds, groupId, startTime, endTime, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 故障维修 🔖
         * @param {RepairFaultInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyFaultRepairPost(body?: RepairFaultInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return EnergyFaultApiFp(configuration).apiEnergyFaultRepairPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 设置故障状态 🔖
         * @param {EnergyFaultStatusInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyFaultSetStatusPost(body?: EnergyFaultStatusInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return EnergyFaultApiFp(configuration).apiEnergyFaultSetStatusPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取故障统计数据 🔖
         * @param {string} statType 统计类型
         * @param {number} [deviceId] 设备ID
         * @param {Array<number>} [deviceIds] 设备ID列表
         * @param {number} [groupId] 分组ID
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyFaultStatGet(statType: string, deviceId?: number, deviceIds?: Array<number>, groupId?: number, startTime?: Date, endTime?: Date, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultEnergyFaultStatOutput>> {
            return EnergyFaultApiFp(configuration).apiEnergyFaultStatGet(statType, deviceId, deviceIds, groupId, startTime, endTime, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取故障趋势数据 🔖
         * @param {string} statType 统计类型
         * @param {number} [deviceId] 设备ID
         * @param {Array<number>} [deviceIds] 设备ID列表
         * @param {number} [groupId] 分组ID
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyFaultTrendGet(statType: string, deviceId?: number, deviceIds?: Array<number>, groupId?: number, startTime?: Date, endTime?: Date, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultListEnergyFaultTrendOutput>> {
            return EnergyFaultApiFp(configuration).apiEnergyFaultTrendGet(statType, deviceId, deviceIds, groupId, startTime, endTime, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新故障记录 🔖
         * @param {UpdateEnergyFaultInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyFaultUpdatePost(body?: UpdateEnergyFaultInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return EnergyFaultApiFp(configuration).apiEnergyFaultUpdatePost(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * EnergyFaultApi - object-oriented interface
 * @export
 * @class EnergyFaultApi
 * @extends {BaseAPI}
 */
export class EnergyFaultApi extends BaseAPI {
    /**
     * 
     * @summary 增加故障记录 🔖
     * @param {AddEnergyFaultInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyFaultApi
     */
    public async apiEnergyFaultAddPost(body?: AddEnergyFaultInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt64>> {
        return EnergyFaultApiFp(this.configuration).apiEnergyFaultAddPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 删除故障记录 🔖
     * @param {DeleteEnergyFaultInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyFaultApi
     */
    public async apiEnergyFaultDeletePost(body?: DeleteEnergyFaultInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return EnergyFaultApiFp(this.configuration).apiEnergyFaultDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取故障详情 🔖
     * @param {number} id 主键Id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyFaultApi
     */
    public async apiEnergyFaultDetailGet(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultEnergyFaultDetailOutput>> {
        return EnergyFaultApiFp(this.configuration).apiEnergyFaultDetailGet(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 导出故障数据 🔖
     * @param {number} [deviceId] 设备ID
     * @param {string} [faultCode] 故障编码
     * @param {number} [faultLevel] 故障等级
     * @param {string} [faultType] 故障类型
     * @param {number} [faultStatus] 故障状态
     * @param {Date} [startTime] 开始时间
     * @param {Date} [endTime] 结束时间
     * @param {number} [page] 当前页码
     * @param {number} [pageSize] 页码容量
     * @param {string} [field] 排序字段
     * @param {string} [order] 排序方向
     * @param {string} [descStr] 降序排序
     * @param {Array<string>} [searchFields] 字段名称集合
     * @param {string} [searchKeyword] 关键字
     * @param {string} [keyword] 模糊查询关键字
     * @param {FilterLogicEnum} [filterLogic] 过滤条件
     * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
     * @param {string} [filterField] 字段名称
     * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
     * @param {any} [filterValue] 字段值
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyFaultApi
     */
    public async apiEnergyFaultExportDataPost(deviceId?: number, faultCode?: string, faultLevel?: number, faultType?: string, faultStatus?: number, startTime?: Date, endTime?: Date, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultFileStreamResult>> {
        return EnergyFaultApiFp(this.configuration).apiEnergyFaultExportDataPost(deviceId, faultCode, faultLevel, faultType, faultStatus, startTime, endTime, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 导出故障数据到Excel 🔖
     * @param {EnergyFaultInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyFaultApi
     */
    public async apiEnergyFaultExportExcelPost(body?: EnergyFaultInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultIActionResult>> {
        return EnergyFaultApiFp(this.configuration).apiEnergyFaultExportExcelPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取故障记录分页列表 🔖
     * @param {number} [deviceId] 设备ID
     * @param {string} [faultCode] 故障编码
     * @param {number} [faultLevel] 故障等级
     * @param {string} [faultType] 故障类型
     * @param {number} [faultStatus] 故障状态
     * @param {Date} [startTime] 开始时间
     * @param {Date} [endTime] 结束时间
     * @param {number} [page] 当前页码
     * @param {number} [pageSize] 页码容量
     * @param {string} [field] 排序字段
     * @param {string} [order] 排序方向
     * @param {string} [descStr] 降序排序
     * @param {Array<string>} [searchFields] 字段名称集合
     * @param {string} [searchKeyword] 关键字
     * @param {string} [keyword] 模糊查询关键字
     * @param {FilterLogicEnum} [filterLogic] 过滤条件
     * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
     * @param {string} [filterField] 字段名称
     * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
     * @param {any} [filterValue] 字段值
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyFaultApi
     */
    public async apiEnergyFaultPageGet(deviceId?: number, faultCode?: string, faultLevel?: number, faultType?: string, faultStatus?: number, startTime?: Date, endTime?: Date, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSqlSugarPagedListEnergyFaultOutput>> {
        return EnergyFaultApiFp(this.configuration).apiEnergyFaultPageGet(deviceId, faultCode, faultLevel, faultType, faultStatus, startTime, endTime, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取设备故障排行 🔖
     * @param {string} statType 统计类型
     * @param {number} [deviceId] 设备ID
     * @param {Array<number>} [deviceIds] 设备ID列表
     * @param {number} [groupId] 分组ID
     * @param {Date} [startTime] 开始时间
     * @param {Date} [endTime] 结束时间
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyFaultApi
     */
    public async apiEnergyFaultRankGet(statType: string, deviceId?: number, deviceIds?: Array<number>, groupId?: number, startTime?: Date, endTime?: Date, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultListEnergyFaultRankOutput>> {
        return EnergyFaultApiFp(this.configuration).apiEnergyFaultRankGet(statType, deviceId, deviceIds, groupId, startTime, endTime, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 故障维修 🔖
     * @param {RepairFaultInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyFaultApi
     */
    public async apiEnergyFaultRepairPost(body?: RepairFaultInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return EnergyFaultApiFp(this.configuration).apiEnergyFaultRepairPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 设置故障状态 🔖
     * @param {EnergyFaultStatusInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyFaultApi
     */
    public async apiEnergyFaultSetStatusPost(body?: EnergyFaultStatusInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return EnergyFaultApiFp(this.configuration).apiEnergyFaultSetStatusPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取故障统计数据 🔖
     * @param {string} statType 统计类型
     * @param {number} [deviceId] 设备ID
     * @param {Array<number>} [deviceIds] 设备ID列表
     * @param {number} [groupId] 分组ID
     * @param {Date} [startTime] 开始时间
     * @param {Date} [endTime] 结束时间
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyFaultApi
     */
    public async apiEnergyFaultStatGet(statType: string, deviceId?: number, deviceIds?: Array<number>, groupId?: number, startTime?: Date, endTime?: Date, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultEnergyFaultStatOutput>> {
        return EnergyFaultApiFp(this.configuration).apiEnergyFaultStatGet(statType, deviceId, deviceIds, groupId, startTime, endTime, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取故障趋势数据 🔖
     * @param {string} statType 统计类型
     * @param {number} [deviceId] 设备ID
     * @param {Array<number>} [deviceIds] 设备ID列表
     * @param {number} [groupId] 分组ID
     * @param {Date} [startTime] 开始时间
     * @param {Date} [endTime] 结束时间
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyFaultApi
     */
    public async apiEnergyFaultTrendGet(statType: string, deviceId?: number, deviceIds?: Array<number>, groupId?: number, startTime?: Date, endTime?: Date, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultListEnergyFaultTrendOutput>> {
        return EnergyFaultApiFp(this.configuration).apiEnergyFaultTrendGet(statType, deviceId, deviceIds, groupId, startTime, endTime, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新故障记录 🔖
     * @param {UpdateEnergyFaultInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyFaultApi
     */
    public async apiEnergyFaultUpdatePost(body?: UpdateEnergyFaultInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return EnergyFaultApiFp(this.configuration).apiEnergyFaultUpdatePost(body, options).then((request) => request(this.axios, this.basePath));
    }
}
