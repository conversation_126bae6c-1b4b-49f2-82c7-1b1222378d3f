<template>
  <view v-if="isFormReady" class="register-container">
    <!-- 背景装饰 -->
    <view class="bg-decoration">
      <view class="bg-circle circle-1"></view>
      <view class="bg-circle circle-2"></view>
      <view class="bg-circle circle-3"></view>
    </view>

    <!-- 注册表单 -->
    <view class="register-form">
      <!-- 头部区域 -->
      <view class="header-section">
        <view class="logo">
          <image 
            src="https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=smart%20lighting%20system%20logo%20modern%20minimalist&image_size=square" 
            class="logo-image" 
            mode="aspectFit"
          />
        </view>
        <text class="app-title">用户注册</text>
        <text class="app-subtitle">创建您的账户</text>
      </view>

      <!-- 注册表单 -->
      <view class="form-section">
        <!-- 用户名输入 -->
        <view class="input-group">
          <view class="input-label">
            <text class="label-icon">👤</text>
            <text class="label-text">用户名</text>
          </view>
          <input 
            class="form-input"
            type="text"
            v-model="registerForm.account"
            placeholder="请输入用户名（3-20个字符）"
            :class="{ error: errors.account }"
            @blur="validateAccount"
          />
          <text class="error-text" v-if="errors.account">{{ errors.account }}</text>
        </view>

        <!-- 真实姓名输入 -->
        <view class="input-group">
          <view class="input-label">
            <text class="label-icon">📝</text>
            <text class="label-text">真实姓名</text>
          </view>
          <input 
            class="form-input"
            type="text"
            v-model="registerForm.realName"
            placeholder="请输入真实姓名"
            :class="{ error: errors.realName }"
            @blur="validateRealName"
          />
          <text class="error-text" v-if="errors.realName">{{ errors.realName }}</text>
        </view>

        <!-- 手机号输入 -->
        <view class="input-group">
          <view class="input-label">
            <text class="label-icon">📱</text>
            <text class="label-text">手机号码</text>
          </view>
          <input 
            class="form-input"
            type="number"
            v-model="registerForm.phone"
            placeholder="请输入手机号码"
            :class="{ error: errors.phone }"
            @blur="validatePhone"
          />
          <text class="error-text" v-if="errors.phone">{{ errors.phone }}</text>
        </view>

        <!-- 邮箱输入 -->
        <view class="input-group">
          <view class="input-label">
            <text class="label-icon">📧</text>
            <text class="label-text">邮箱地址</text>
          </view>
          <input 
            class="form-input"
            type="text"
            v-model="registerForm.email"
            placeholder="请输入邮箱地址"
            :class="{ error: errors.email }"
            @blur="validateEmail"
          />
          <text class="error-text" v-if="errors.email">{{ errors.email }}</text>
        </view>

        <!-- 密码输入 -->
        <view class="input-group">
          <view class="input-label">
            <text class="label-icon">🔒</text>
            <text class="label-text">登录密码</text>
          </view>
          <view class="password-input">
            <input 
              class="form-input"
              :type="showPassword ? 'text' : 'password'"
              v-model="registerForm.password"
              placeholder="请输入密码（6-20个字符）"
              :class="{ error: errors.password }"
              @blur="validatePassword"
            />
            <text 
              class="password-toggle"
              @click="togglePassword"
            >
              {{ showPassword ? '👁️' : '👁️‍🗨️' }}
            </text>
          </view>
          <text class="error-text" v-if="errors.password">{{ errors.password }}</text>
        </view>

        <!-- 确认密码输入 -->
        <view class="input-group">
          <view class="input-label">
            <text class="label-icon">🔐</text>
            <text class="label-text">确认密码</text>
          </view>
          <view class="password-input">
            <input 
              class="form-input"
              :type="showConfirmPassword ? 'text' : 'password'"
              v-model="registerForm.confirmPassword"
              placeholder="请再次输入密码"
              :class="{ error: errors.confirmPassword }"
              @blur="validateConfirmPassword"
            />
            <text 
              class="password-toggle"
              @click="toggleConfirmPassword"
            >
              {{ showConfirmPassword ? '👁️' : '👁️‍🗨️' }}
            </text>
          </view>
          <text class="error-text" v-if="errors.confirmPassword">{{ errors.confirmPassword }}</text>
        </view>

        <!-- 验证码输入 -->
        <view class="input-group">
          <view class="input-label">
            <text class="label-icon">📱</text>
            <text class="label-text">短信验证码</text>
          </view>
          <view class="sms-input">
            <input 
              class="form-input sms-field"
              type="number"
              v-model="registerForm.smsCode"
              placeholder="请输入验证码"
              :class="{ error: errors.smsCode }"
              @blur="validateSmsCode"
            />
            <button 
              class="sms-btn"
              :disabled="smsCountdown > 0 || !registerForm.phone || errors.phone"
              @click="sendSmsCode"
            >
              {{ smsCountdown > 0 ? `${smsCountdown}s` : '发送验证码' }}
            </button>
          </view>
          <text class="error-text" v-if="errors.smsCode">{{ errors.smsCode }}</text>
        </view>

        <!-- 用户协议 -->
        <view class="agreement-section">
          <label class="agreement-checkbox">
            <input 
              type="checkbox"
              v-model="registerForm.agreeTerms"
              class="checkbox"
            />
            <text class="checkbox-text">
              我已阅读并同意
              <text class="link-text" @click="showTerms">《用户协议》</text>
              和
              <text class="link-text" @click="showPrivacy">《隐私政策》</text>
            </text>
          </label>
          <text class="error-text" v-if="errors.agreeTerms">{{ errors.agreeTerms }}</text>
        </view>

        <!-- 注册按钮 -->
        <button 
          class="register-btn"
          :class="{ loading: isLoading }"
          :disabled="isLoading"
          @click="handleRegister"
        >
          <text v-if="!isLoading">立即注册</text>
          <text v-else>注册中...</text>
        </button>

        <!-- 登录链接 -->
        <view class="login-link">
          <text class="login-text">已有账号？</text>
          <text class="login-btn-text" @click="goLogin">立即登录</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { register, sendSmsCode as apiSendSmsCode } from '@/api/auth'
import { onUnload } from '@dcloudio/uni-app'
import { encryptPassword } from '@/utils/crypto'

// 响应式数据
const registerForm = reactive({
  account: '',
  realName: '',
  phone: '',
  email: '',
  password: '',
  confirmPassword: '',
  smsCode: '',
  agreeTerms: false
})

const isFormReady = ref(false)

const errors = reactive({
  account: '',
  realName: '',
  phone: '',
  email: '',
  password: '',
  confirmPassword: '',
  smsCode: '',
  agreeTerms: ''
})

const isLoading = ref(false)
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const smsCountdown = ref(0)
const smsTimer = ref(null)

// 表单验证方法
const validateAccount = () => {
  if (!registerForm.account) {
    errors.account = '请输入用户名'
    return false
  }
  if (registerForm.account.length < 3 || registerForm.account.length > 20) {
    errors.account = '用户名长度为3-20个字符'
    return false
  }
  if (!/^[a-zA-Z0-9_]+$/.test(registerForm.account)) {
    errors.account = '用户名只能包含字母、数字和下划线'
    return false
  }
  errors.account = ''
  return true
}

const validateRealName = () => {
  if (!registerForm.realName) {
    errors.realName = '请输入真实姓名'
    return false
  }
  if (registerForm.realName.length < 2 || registerForm.realName.length > 10) {
    errors.realName = '姓名长度为2-10个字符'
    return false
  }
  errors.realName = ''
  return true
}

const validatePhone = () => {
  if (!registerForm.phone) {
    errors.phone = '请输入手机号码'
    return false
  }
  if (!/^1[3-9]\d{9}$/.test(registerForm.phone)) {
    errors.phone = '请输入正确的手机号码'
    return false
  }
  errors.phone = ''
  return true
}

const validateEmail = () => {
  if (!registerForm.email) {
    errors.email = '请输入邮箱地址'
    return false
  }
  if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(registerForm.email)) {
    errors.email = '请输入正确的邮箱地址'
    return false
  }
  errors.email = ''
  return true
}

const validatePassword = () => {
  if (!registerForm.password) {
    errors.password = '请输入密码'
    return false
  }
  if (registerForm.password.length < 6 || registerForm.password.length > 20) {
    errors.password = '密码长度为6-20个字符'
    return false
  }
  errors.password = ''
  return true
}

const validateConfirmPassword = () => {
  if (!registerForm.confirmPassword) {
    errors.confirmPassword = '请确认密码'
    return false
  }
  if (registerForm.confirmPassword !== registerForm.password) {
    errors.confirmPassword = '两次输入的密码不一致'
    return false
  }
  errors.confirmPassword = ''
  return true
}

const validateSmsCode = () => {
  if (!registerForm.smsCode) {
    errors.smsCode = '请输入验证码'
    return false
  }
  if (!/^\d{6}$/.test(registerForm.smsCode)) {
    errors.smsCode = '请输入6位数字验证码'
    return false
  }
  errors.smsCode = ''
  return true
}

const validateForm = () => {
  const isAccountValid = validateAccount()
  const isRealNameValid = validateRealName()
  const isPhoneValid = validatePhone()
  const isEmailValid = validateEmail()
  const isPasswordValid = validatePassword()
  const isConfirmPasswordValid = validateConfirmPassword()
  const isSmsCodeValid = validateSmsCode()
  
  if (!registerForm.agreeTerms) {
    errors.agreeTerms = '请同意用户协议和隐私政策'
    return false
  }
  errors.agreeTerms = ''
  
  return isAccountValid && isRealNameValid && isPhoneValid && isEmailValid && 
         isPasswordValid && isConfirmPasswordValid && isSmsCodeValid
}

// 方法
const togglePassword = () => {
  showPassword.value = !showPassword.value
}

const toggleConfirmPassword = () => {
  showConfirmPassword.value = !showConfirmPassword.value
}

const sendSmsCode = async () => {
  if (!validatePhone()) {
    return
  }
  
  try {
    // 调用发送验证码API
    await apiSendSmsCode({
      phone: registerForm.phone,
      type: 'register'
    })
    
    // 开始倒计时
    smsCountdown.value = 60
    smsTimer.value = setInterval(() => {
      smsCountdown.value--
      if (smsCountdown.value <= 0) {
        clearInterval(smsTimer.value)
      }
    }, 1000)
    
    uni.showToast({
      title: '验证码已发送',
      icon: 'success'
    })
  } catch (err) {
    console.error('发送验证码失败:', err)
    uni.showToast({
      title: err.message || '发送失败，请重试',
      icon: 'error'
    })
  }
}

const handleRegister = async () => {
  if (!validateForm()) {
    return
  }
  
  isLoading.value = true
  
  try {
    // SM2加密密码
    const encryptedPassword = encryptPassword(registerForm.password)
    
    // 构建注册数据
    const registerData = {
      account: registerForm.account,
      realName: registerForm.realName,
      phone: registerForm.phone,
      password: encryptedPassword,
      code: registerForm.smsCode,
      tenantId: ************* // 默认租户ID，与数据库配置一致
    }
    
    // 调用注册API
    await register(registerData)
    
    uni.showToast({
      title: '注册成功',
      icon: 'success'
    })
    
    // 跳转到登录页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
    
  } catch (error) {
    console.error('注册失败:', error)
    
    // 根据错误类型处理
    if (error.message && error.message.includes('用户名')) {
      errors.account = '用户名已存在'
    } else if (error.message && error.message.includes('手机号')) {
      errors.phone = '手机号已被注册'
    } else if (error.message && error.message.includes('邮箱')) {
      errors.email = '邮箱已被注册'
    } else if (error.message && error.message.includes('验证码')) {
      errors.smsCode = '验证码错误或已过期'
    } else {
      uni.showToast({
        title: error.message || '注册失败，请重试',
        icon: 'error'
      })
    }
  } finally {
    isLoading.value = false
  }
}

const goLogin = () => {
  uni.navigateBack()
}

const showTerms = () => {
  uni.showModal({
    title: '用户协议',
    content: '这里是用户协议的内容...',
    showCancel: false
  })
}

const showPrivacy = () => {
  uni.showModal({
    title: '隐私政策',
    content: '这里是隐私政策的内容...',
    showCancel: false
  })
}

// 生命周期
onMounted(() => {
  // 页面初始化
  isFormReady.value = true
})

// 页面卸载时清理定时器
onUnload(() => {
  if (smsTimer.value) {
    clearInterval(smsTimer.value)
    smsTimer.value = null
  }
})
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.circle-1 {
  width: 300rpx;
  height: 300rpx;
  top: -150rpx;
  right: -150rpx;
}

.circle-2 {
  width: 200rpx;
  height: 200rpx;
  bottom: 200rpx;
  left: -100rpx;
}

.circle-3 {
  width: 150rpx;
  height: 150rpx;
  top: 300rpx;
  left: 50rpx;
}

/* 注册表单 */
.register-form {
  position: relative;
  z-index: 1;
  padding: 60rpx 40rpx 40rpx;
}

/* 头部区域 */
.header-section {
  text-align: center;
  margin-bottom: 60rpx;
}

.logo {
  margin-bottom: 30rpx;
}

.logo-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 20rpx;
}

.app-title {
  display: block;
  font-size: 44rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 10rpx;
}

.app-subtitle {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 表单区域 */
.form-section {
  background: white;
  border-radius: 20rpx;
  padding: 50rpx 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}

.input-group {
  margin-bottom: 35rpx;
}

.input-label {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.label-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.label-text {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 25rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  font-size: 26rpx;
  background: #f8f8f8;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #667eea;
  background: white;
}

.form-input.error {
  border-color: #ff3b30;
}

.error-text {
  font-size: 22rpx;
  color: #ff3b30;
  margin-top: 8rpx;
}

.password-input {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 25rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #999;
}

.sms-input {
  display: flex;
  gap: 20rpx;
}

.sms-field {
  flex: 1;
}

.sms-btn {
  width: 200rpx;
  height: 80rpx;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 10rpx;
  font-size: 24rpx;
}

.sms-btn:disabled {
  background: #ccc;
}

.agreement-section {
  margin-bottom: 40rpx;
}

.agreement-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 15rpx;
}

.checkbox {
  width: 30rpx;
  height: 30rpx;
  margin-top: 5rpx;
}

.checkbox-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

.link-text {
  color: #667eea;
  text-decoration: underline;
}

.register-btn {
  width: 100%;
  height: 90rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.register-btn.loading {
  opacity: 0.7;
}

.register-btn:disabled {
  opacity: 0.5;
}

.login-link {
  text-align: center;
}

.login-text {
  font-size: 26rpx;
  color: #666;
}

.login-btn-text {
  font-size: 26rpx;
  color: #667eea;
  font-weight: bold;
  margin-left: 10rpx;
}
</style>