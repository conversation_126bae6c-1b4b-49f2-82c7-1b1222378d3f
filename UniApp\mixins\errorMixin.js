/**
 * 错误处理混入
 * 为页面组件提供统一的错误处理功能
 */

import { handleError } from '../utils/errorHandler.js'

const errorMixin = {
  data() {
    return {
      // 错误状态
      hasError: false,
      errorMessage: '',
      isRetrying: false,
      retryCount: 0,
      maxRetries: 3
    }
  },
  
  methods: {
    /**
     * 统一错误处理入口
     * @param {Error|Object} error 错误对象
     * @param {Object} context 错误上下文
     * @param {Object} options 处理选项
     */
    $handleError(error, context = {}, options = {}) {
      let currentRoute = 'Unknown'
      try {
        const pages = getCurrentPages()
        currentRoute = this.$route?.path || (pages.length > 0 ? pages.pop()?.route : 'Unknown')
      } catch (e) {
        console.warn('获取当前页面路由失败:', e)
      }
      
      const pageContext = {
        page: this.$options.name || this.$route?.name || 'Unknown',
        route: currentRoute,
        timestamp: new Date().toISOString(),
        ...context
      }
      
      // 更新页面错误状态
      this.hasError = true
      this.errorMessage = error.message || '操作失败'
      
      return handleError(error, pageContext, {
        showToast: true,
        logError: true,
        reportError: true,
        ...options
      })
    },
    
    /**
     * 处理网络错误
     * @param {Error|Object} error 错误对象
     * @param {Object} context 错误上下文
     */
    $handleNetworkError(error, context = {}) {
      return this.$handleError(error, context, {
        showToast: true,
        retryable: true,
        onRetry: this.$retryLastOperation
      })
    },
    
    /**
     * 处理业务错误
     * @param {Error|Object} error 错误对象
     * @param {Object} context 错误上下文
     */
    $handleBusinessError(error, context = {}) {
      return this.$handleError(error, context, {
        showToast: true,
        reportError: false
      })
    },
    
    /**
     * 处理严重错误
     * @param {Error|Object} error 错误对象
     * @param {Object} context 错误上下文
     */
    $handleCriticalError(error, context = {}) {
      return this.$handleError(error, context, {
        showModal: true,
        logError: true,
        reportError: true
      })
    },
    
    /**
     * 处理API请求错误
     * @param {Error|Object} error 错误对象
     * @param {string} apiName API名称
     * @param {Object} context 错误上下文
     */
    $handleApiError(error, apiName = '', context = {}) {
      const errorContext = {
        api: apiName,
        ...context
      }
      
      // 根据错误类型选择处理方式
      if (error.code === 'NETWORK_ERROR' || error.code === 'TIMEOUT') {
        return this.$handleNetworkError(error, errorContext)
      } else if (error.statusCode >= 400 && error.statusCode < 500) {
        return this.$handleBusinessError(error, errorContext)
      } else {
        return this.$handleError(error, errorContext)
      }
    },
    
    /**
     * 安全执行异步操作
     * @param {Function} asyncFn 异步函数
     * @param {Object} options 选项
     */
    async $safeExecute(asyncFn, options = {}) {
      const {
        showLoading = true,
        loadingText = '加载中...',
        onError = null,
        context = {}
      } = options
      
      try {
        if (showLoading) {
          this.showLoading(loadingText)
        }
        
        const result = await asyncFn()
        
        // 清除错误状态
        this.hasError = false
        this.errorMessage = ''
        this.retryCount = 0
        
        return result
        
      } catch (error) {
        // 使用自定义错误处理或默认处理
        if (onError && typeof onError === 'function') {
          onError(error)
        } else {
          this.$handleApiError(error, context.apiName, context)
        }
        
        throw error
        
      } finally {
        if (showLoading) {
          this.hideLoading()
        }
      }
    },
    
    /**
     * 带重试的安全执行
     * @param {Function} asyncFn 异步函数
     * @param {Object} options 选项
     */
    async $safeExecuteWithRetry(asyncFn, options = {}) {
      const {
        maxRetries = this.maxRetries,
        retryDelay = 1000,
        retryCondition = (error) => error.code === 'NETWORK_ERROR' || error.code === 'TIMEOUT',
        ...otherOptions
      } = options
      
      let lastError = null
      
      for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
          this.retryCount = attempt
          this.isRetrying = attempt > 0
          
          const result = await this.$safeExecute(asyncFn, {
            ...otherOptions,
            showLoading: attempt === 0 || otherOptions.showLoading !== false,
            loadingText: attempt > 0 ? `重试中... (${attempt}/${maxRetries})` : otherOptions.loadingText
          })
          
          this.isRetrying = false
          return result
          
        } catch (error) {
          lastError = error
          
          // 检查是否应该重试
          if (attempt < maxRetries && retryCondition(error)) {
            console.log(`操作失败，${retryDelay}ms后重试 (${attempt + 1}/${maxRetries})`)
            await this.$delay(retryDelay)
            continue
          }
          
          break
        }
      }
      
      this.isRetrying = false
      throw lastError
    },
    
    /**
     * 重试上次操作
     */
    $retryLastOperation() {
      if (this.lastOperation && typeof this.lastOperation === 'function') {
        return this.lastOperation()
      }
    },
    
    /**
     * 清除错误状态
     */
    $clearError() {
      this.hasError = false
      this.errorMessage = ''
      this.retryCount = 0
      this.isRetrying = false
    },
    
    /**
     * 延迟函数
     * @param {number} ms 延迟毫秒数
     */
    $delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    },
    
    /**
     * 显示错误重试对话框
     * @param {string} message 错误消息
     * @param {Function} retryFn 重试函数
     */
    $showRetryDialog(message, retryFn) {
      return new Promise((resolve) => {
        uni.showModal({
          title: '操作失败',
          content: message + '\n\n是否重试？',
          showCancel: true,
          confirmText: '重试',
          cancelText: '取消',
          success: async (res) => {
            if (res.confirm && retryFn) {
              try {
                const result = await retryFn()
                resolve(result)
              } catch (error) {
                this.$handleError(error)
                resolve(null)
              }
            } else {
              resolve(null)
            }
          }
        })
      })
    },
    
    /**
     * 网络状态检查
     */
    async $checkNetworkAndExecute(asyncFn, options = {}) {
      const isOnline = await this.checkNetworkStatus()
      
      if (!isOnline) {
        const error = {
          code: 'NETWORK_ERROR',
          message: '网络连接不可用，请检查网络设置'
        }
        
        this.$handleNetworkError(error, { operation: 'networkCheck' })
        throw error
      }
      
      return this.$safeExecute(asyncFn, options)
    }
  },
  
  // 页面生命周期
  onLoad() {
    // 清除错误状态
    this.$clearError()
  },
  
  onShow() {
    // 页面显示时检查是否有未处理的错误
    if (this.hasError && this.retryCount < this.maxRetries) {
      console.log('页面重新显示，检查错误状态')
    }
  },
  
  onHide() {
    // 页面隐藏时保存错误状态
    if (this.hasError) {
      console.log('页面隐藏，保存错误状态')
    }
  },
  
  onUnload() {
    // 页面卸载时清理错误状态
    this.$clearError()
  }
}

// 提供命名导出和默认导出
export { errorMixin }
export default errorMixin