/**
 * 故障管理Vuex模块
 */

const state = {
  // 故障列表
  faultList: [],
  // 最近告警
  recentAlerts: [],
  // 故障统计
  faultStats: {
    total: 0,
    pending: 0,
    processing: 0,
    resolved: 0
  },
  // 加载状态
  loading: false,
  // 错误信息
  error: null,
  // 最后更新时间
  lastUpdateTime: null,
  // 分页信息
  pagination: {
    page: 1,
    pageSize: 10,
    total: 0
  }
}

const getters = {
  // 获取待处理告警数量
  pendingAlertsCount: (state) => {
    return state.recentAlerts.filter(alert => alert.status === 'pending' || alert.status === '待处理').length
  },
  
  // 获取格式化的最近告警
  formattedRecentAlerts: (state) => {
    return state.recentAlerts.map(alert => ({
      id: alert.id,
      title: alert.title || alert.description || '设备告警',
      description: alert.description || alert.message || alert.content,
      level: alert.level || 'medium',
      levelClass: `alert-${alert.level || 'medium'}`,
      icon: alert.level === 'high' ? '🚨' : alert.level === 'low' ? '💡' : '⚠️',
      status: alert.status === 'resolved' ? '已处理' : alert.status === 'processing' ? '处理中' : '待处理',
      statusClass: alert.status === 'resolved' ? 'status-resolved' : alert.status === 'processing' ? 'status-processing' : 'status-pending',
      createTime: new Date(alert.createTime || alert.createdAt || alert.time || Date.now())
    }))
  },
  
  // 获取故障统计信息
  faultStatsSummary: (state) => {
    return {
      total: state.faultStats.total,
      pending: state.faultStats.pending,
      processing: state.faultStats.processing,
      resolved: state.faultStats.resolved,
      pendingRate: state.faultStats.total > 0 ? Math.round((state.faultStats.pending / state.faultStats.total) * 100) : 0
    }
  }
}

const mutations = {
  // 设置故障列表
  SET_FAULT_LIST(state, { list, pagination }) {
    state.faultList = list || []
    if (pagination) {
      state.pagination = { ...state.pagination, ...pagination }
    }
    state.lastUpdateTime = new Date().toISOString()
  },
  
  // 设置最近告警
  SET_RECENT_ALERTS(state, alerts) {
    state.recentAlerts = alerts || []
  },
  
  // 设置故障统计
  SET_FAULT_STATS(state, stats) {
    state.faultStats = { ...state.faultStats, ...stats }
  },
  
  // 设置加载状态
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  
  // 设置错误信息
  SET_ERROR(state, error) {
    state.error = error
  },
  
  // 添加新故障
  ADD_FAULT(state, fault) {
    state.faultList.unshift(fault)
    state.faultStats.total += 1
    state.faultStats.pending += 1
  },
  
  // 更新故障状态
  UPDATE_FAULT_STATUS(state, { faultId, status }) {
    const fault = state.faultList.find(f => f.id === faultId)
    if (fault) {
      const oldStatus = fault.status
      fault.status = status
      fault.updateTime = new Date().toISOString()
      
      // 更新统计数据
      if (oldStatus === 'pending') state.faultStats.pending -= 1
      if (oldStatus === 'processing') state.faultStats.processing -= 1
      if (oldStatus === 'resolved') state.faultStats.resolved -= 1
      
      if (status === 'pending') state.faultStats.pending += 1
      if (status === 'processing') state.faultStats.processing += 1
      if (status === 'resolved') state.faultStats.resolved += 1
    }
  },
  
  // 清空数据
  CLEAR_DATA(state) {
    state.faultList = []
    state.recentAlerts = []
    state.faultStats = {
      total: 0,
      pending: 0,
      processing: 0,
      resolved: 0
    }
    state.lastUpdateTime = null
  }
}

const actions = {
  // 获取故障列表
  async getFaultList({ commit }, params = {}) {
    try {
      commit('SET_LOADING', true)
      commit('SET_ERROR', null)
      
      // 动态导入API函数
      const { getFaultList } = await import('../../api/fault.js')
      const response = await getFaultList(params)
      
      if (response.code === 200 || response.success) {
        const data = response.data || {}
        const list = data.list || data.records || []
        const pagination = {
          page: data.page || params.page || 1,
          pageSize: data.pageSize || params.pageSize || 10,
          total: data.total || data.totalCount || list.length
        }
        
        commit('SET_FAULT_LIST', { list, pagination })
        
        // 如果是获取最近告警（pageSize <= 5），更新最近告警列表
        if (params.pageSize <= 5 || params.status === 'pending') {
          commit('SET_RECENT_ALERTS', list.slice(0, 3))
        }
        
        // 缓存数据
        uni.setStorageSync('cached_fault_list', {
          data: { list, pagination },
          timestamp: Date.now()
        })
        
        return { success: true, data: { list, pagination } }
      } else {
        throw new Error(response.message || '获取故障列表失败')
      }
    } catch (error) {
      console.error('获取故障列表失败:', error)
      commit('SET_ERROR', error.message)
      
      // 尝试使用缓存数据
      const cachedData = uni.getStorageSync('cached_fault_list')
      if (cachedData && (Date.now() - cachedData.timestamp < 10 * 60 * 1000)) {
        commit('SET_FAULT_LIST', cachedData.data)
        return { success: true, data: cachedData.data, fromCache: true }
      }
      
      // 返回默认数据
      const defaultAlerts = [
        {
          id: 1,
          title: '设备离线告警',
          description: '会议室A-001照明设备失去连接',
          level: 'high',
          status: 'pending',
          createTime: new Date(Date.now() - 10 * 60 * 1000)
        },
        {
          id: 2,
          title: '功耗异常告警',
          description: '办公区B-205功耗超出正常范围',
          level: 'medium',
          status: 'processing',
          createTime: new Date(Date.now() - 30 * 60 * 1000)
        },
        {
          id: 3,
          title: '定时任务执行失败',
          description: '夜间自动关灯任务执行异常',
          level: 'low',
          status: 'resolved',
          createTime: new Date(Date.now() - 2 * 60 * 60 * 1000)
        }
      ]
      
      const defaultData = {
        list: defaultAlerts,
        pagination: { page: 1, pageSize: 10, total: 3 }
      }
      
      commit('SET_FAULT_LIST', defaultData)
      commit('SET_RECENT_ALERTS', defaultAlerts)
      
      return { success: true, data: defaultData, isDefault: true }
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 获取故障统计
  async getFaultStats({ commit }, params = {}) {
    try {
      // 动态导入API函数
      const { getFaultStatistics } = await import('../../api/fault.js')
      const response = await getFaultStatistics(params)
      
      if (response.code === 200 || response.success) {
        const stats = response.data || {}
        commit('SET_FAULT_STATS', stats)
        
        // 缓存统计数据
        uni.setStorageSync('cached_fault_stats', {
          data: stats,
          timestamp: Date.now()
        })
        
        return { success: true, data: stats }
      } else {
        throw new Error(response.message || '获取故障统计失败')
      }
    } catch (error) {
      console.error('获取故障统计失败:', error)
      
      // 尝试使用缓存数据
      const cachedStats = uni.getStorageSync('cached_fault_stats')
      if (cachedStats && (Date.now() - cachedStats.timestamp < 10 * 60 * 1000)) {
        commit('SET_FAULT_STATS', cachedStats.data)
        return { success: true, data: cachedStats.data, fromCache: true }
      }
      
      // 返回默认统计数据
      const defaultStats = {
        total: 3,
        pending: 1,
        processing: 1,
        resolved: 1
      }
      
      commit('SET_FAULT_STATS', defaultStats)
      return { success: true, data: defaultStats, isDefault: true }
    }
  },
  
  // 创建新故障
  async createFault({ commit, dispatch }, faultData) {
    try {
      commit('SET_LOADING', true)
      
      const { createFault } = await import('../../api/fault.js')
      const response = await createFault(faultData)
      
      if (response.code === 200 || response.success) {
        const newFault = response.data
        commit('ADD_FAULT', newFault)
        
        uni.showToast({
          title: '故障报告成功',
          icon: 'success'
        })
        
        // 刷新故障列表
        await dispatch('getFaultList', { pageSize: 3, status: 'pending' })
        
        return { success: true, data: newFault }
      } else {
        throw new Error(response.message || '创建故障失败')
      }
    } catch (error) {
      console.error('创建故障失败:', error)
      commit('SET_ERROR', error.message)
      
      uni.showToast({
        title: '故障报告失败',
        icon: 'error'
      })
      
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 更新故障状态
  async updateFaultStatus({ commit }, { faultId, status }) {
    try {
      const { updateFault } = await import('../../api/fault.js')
      const response = await updateFault(faultId, { status })
      
      if (response.code === 200 || response.success) {
        commit('UPDATE_FAULT_STATUS', { faultId, status })
        
        uni.showToast({
          title: '状态更新成功',
          icon: 'success'
        })
        
        return { success: true }
      } else {
        throw new Error(response.message || '更新状态失败')
      }
    } catch (error) {
      console.error('更新故障状态失败:', error)
      
      uni.showToast({
        title: '状态更新失败',
        icon: 'error'
      })
      
      throw error
    }
  },
  
  // 清空所有数据
  clearAllData({ commit }) {
    commit('CLEAR_DATA')
    commit('SET_ERROR', null)
    
    // 清除本地缓存
    uni.removeStorageSync('cached_fault_list')
    uni.removeStorageSync('cached_fault_stats')
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}