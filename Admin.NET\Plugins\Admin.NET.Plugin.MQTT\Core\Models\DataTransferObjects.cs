using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Admin.NET.Plugin.MQTT.Models
{
    /// <summary>
    /// MQTT消息数据传输对象
    /// </summary>
    public class MqttMessageDto
    {
        /// <summary>
        /// 消息ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();
        
        /// <summary>
        /// 主题
        /// </summary>
        [Required]
        public string Topic { get; set; }
        
        /// <summary>
        /// 消息内容
        /// </summary>
        public byte[] Payload { get; set; }
        
        /// <summary>
        /// 消息内容（字符串形式）
        /// </summary>
        public string PayloadString { get; set; }
        
        /// <summary>
        /// 服务质量等级
        /// </summary>
        [Range(0, 2)]
        public int QualityOfServiceLevel { get; set; } = 0;
        
        /// <summary>
        /// 是否保留消息
        /// </summary>
        public bool Retain { get; set; } = false;
        
        /// <summary>
        /// 消息时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// 消息来源
        /// </summary>
        public string Source { get; set; }
        
        /// <summary>
        /// 消息类型
        /// </summary>
        public string MessageType { get; set; }
        
        /// <summary>
        /// 消息头信息
        /// </summary>
        public Dictionary<string, object> Headers { get; set; } = new Dictionary<string, object>();
        
        /// <summary>
        /// 消息元数据
        /// </summary>
        public MessageMetadata Metadata { get; set; } = new MessageMetadata();
    }
    
    /// <summary>
    /// 消息元数据
    /// </summary>
    public class MessageMetadata
    {
        /// <summary>
        /// 消息大小（字节）
        /// </summary>
        public int Size { get; set; }
        
        /// <summary>
        /// 消息编码
        /// </summary>
        public string Encoding { get; set; } = "UTF-8";
        
        /// <summary>
        /// 消息格式
        /// </summary>
        public string Format { get; set; } = "JSON";
        
        /// <summary>
        /// 消息版本
        /// </summary>
        public string Version { get; set; } = "1.0";
        
        /// <summary>
        /// 消息校验和
        /// </summary>
        public string Checksum { get; set; }
        
        /// <summary>
        /// 消息压缩类型
        /// </summary>
        public string CompressionType { get; set; }
        
        /// <summary>
        /// 消息过期时间
        /// </summary>
        public DateTime? ExpiresAt { get; set; }
        
        /// <summary>
        /// 消息优先级
        /// </summary>
        [Range(0, 9)]
        public int Priority { get; set; } = 5;
    }
    
    /// <summary>
    /// 设备信息数据传输对象
    /// </summary>
    public class DeviceInfo
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        [Required]
        public string DeviceId { get; set; }
        
        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName { get; set; }
        
        /// <summary>
        /// 设备类型
        /// </summary>
        public string DeviceType { get; set; }
        
        /// <summary>
        /// 设备型号
        /// </summary>
        public string Model { get; set; }
        
        /// <summary>
        /// 制造商
        /// </summary>
        public string Manufacturer { get; set; }
        
        /// <summary>
        /// 固件版本
        /// </summary>
        public string FirmwareVersion { get; set; }
        
        /// <summary>
        /// 硬件版本
        /// </summary>
        public string HardwareVersion { get; set; }
        
        /// <summary>
        /// 设备状态
        /// </summary>
        public DeviceStatus Status { get; set; } = DeviceStatus.Unknown;
        
        /// <summary>
        /// 是否在线
        /// </summary>
        public bool IsOnline { get; set; }
        
        /// <summary>
        /// 最后在线时间
        /// </summary>
        public DateTime? LastOnlineTime { get; set; }
        
        /// <summary>
        /// 设备位置
        /// </summary>
        public DeviceLocation Location { get; set; }
        
        /// <summary>
        /// 设备属性
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new Dictionary<string, object>();
        
        /// <summary>
        /// 设备标签
        /// </summary>
        public List<string> Tags { get; set; } = new List<string>();
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
    
    /// <summary>
    /// 设备位置信息
    /// </summary>
    public class DeviceLocation
    {
        /// <summary>
        /// 纬度
        /// </summary>
        [Range(-90, 90)]
        public double? Latitude { get; set; }
        
        /// <summary>
        /// 经度
        /// </summary>
        [Range(-180, 180)]
        public double? Longitude { get; set; }
        
        /// <summary>
        /// 海拔高度（米）
        /// </summary>
        public double? Altitude { get; set; }
        
        /// <summary>
        /// 地址描述
        /// </summary>
        public string Address { get; set; }
        
        /// <summary>
        /// 建筑物
        /// </summary>
        public string Building { get; set; }
        
        /// <summary>
        /// 楼层
        /// </summary>
        public string Floor { get; set; }
        
        /// <summary>
        /// 房间
        /// </summary>
        public string Room { get; set; }
    }
    
    /// <summary>
    /// 设备控制结果
    /// </summary>
    public class DeviceControlResult
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId { get; set; }
        
        /// <summary>
        /// 控制命令
        /// </summary>
        public string Command { get; set; }
        
        /// <summary>
        /// 控制参数
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();
        
        /// <summary>
        /// 执行结果
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// 错误代码
        /// </summary>
        public string ErrorCode { get; set; }
        
        /// <summary>
        /// 执行时间
        /// </summary>
        public DateTime ExecutedAt { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// 执行耗时（毫秒）
        /// </summary>
        public long ExecutionTime { get; set; }
        
        /// <summary>
        /// 响应数据
        /// </summary>
        public object ResponseData { get; set; }
    }
    
    /// <summary>
    /// 扫描会话状态
    /// </summary>
    public class ScanSessionStatus
    {
        /// <summary>
        /// 会话ID
        /// </summary>
        public string SessionId { get; set; }
        
        /// <summary>
        /// 扫描状态
        /// </summary>
        public ScanStatus Status { get; set; }
        
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }
        
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
        
        /// <summary>
        /// 扫描进度（百分比）
        /// </summary>
        [Range(0, 100)]
        public int Progress { get; set; }
        
        /// <summary>
        /// 已发现设备数量
        /// </summary>
        public int DiscoveredDeviceCount { get; set; }
        
        /// <summary>
        /// 扫描范围
        /// </summary>
        public string ScanRange { get; set; }
        
        /// <summary>
        /// 扫描参数
        /// </summary>
        public Dictionary<string, object> ScanParameters { get; set; } = new Dictionary<string, object>();
        
        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }
    }
    
    /// <summary>
    /// 订阅统计信息
    /// </summary>
    public class SubscriptionStats
    {
        /// <summary>
        /// 主题模式
        /// </summary>
        public string TopicPattern { get; set; }
        
        /// <summary>
        /// 订阅时间
        /// </summary>
        public DateTime SubscribedAt { get; set; }
        
        /// <summary>
        /// 接收消息总数
        /// </summary>
        public long TotalMessagesReceived { get; set; }
        
        /// <summary>
        /// 最后接收消息时间
        /// </summary>
        public DateTime? LastMessageReceivedAt { get; set; }
        
        /// <summary>
        /// 平均消息大小（字节）
        /// </summary>
        public double AverageMessageSize { get; set; }
        
        /// <summary>
        /// 消息接收速率（消息/秒）
        /// </summary>
        public double MessageRate { get; set; }
        
        /// <summary>
        /// 是否活跃
        /// </summary>
        public bool IsActive { get; set; }
        
        /// <summary>
        /// 服务质量等级
        /// </summary>
        public int QualityOfServiceLevel { get; set; }
    }
    
    /// <summary>
    /// 处理器统计信息
    /// </summary>
    public class HandlerStats
    {
        /// <summary>
        /// 处理器ID
        /// </summary>
        public string HandlerId { get; set; }
        
        /// <summary>
        /// 处理器类型
        /// </summary>
        public string HandlerType { get; set; }
        
        /// <summary>
        /// 主题模式
        /// </summary>
        public string TopicPattern { get; set; }
        
        /// <summary>
        /// 注册时间
        /// </summary>
        public DateTime RegisteredAt { get; set; }
        
        /// <summary>
        /// 处理消息总数
        /// </summary>
        public long TotalMessagesProcessed { get; set; }
        
        /// <summary>
        /// 成功处理数量
        /// </summary>
        public long SuccessfulProcessed { get; set; }
        
        /// <summary>
        /// 失败处理数量
        /// </summary>
        public long FailedProcessed { get; set; }
        
        /// <summary>
        /// 平均处理时间（毫秒）
        /// </summary>
        public double AverageProcessingTime { get; set; }
        
        /// <summary>
        /// 最大处理时间（毫秒）
        /// </summary>
        public long MaxProcessingTime { get; set; }
        
        /// <summary>
        /// 最小处理时间（毫秒）
        /// </summary>
        public long MinProcessingTime { get; set; }
        
        /// <summary>
        /// 最后处理时间
        /// </summary>
        public DateTime? LastProcessedAt { get; set; }
        
        /// <summary>
        /// 处理速率（消息/秒）
        /// </summary>
        public double ProcessingRate { get; set; }
        
        /// <summary>
        /// 错误率（百分比）
        /// </summary>
        public double ErrorRate { get; set; }
        
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; }
    }
    
    /// <summary>
    /// 路由器性能统计
    /// </summary>
    public class RouterPerformanceStats
    {
        /// <summary>
        /// 统计开始时间
        /// </summary>
        public DateTime StartTime { get; set; }
        
        /// <summary>
        /// 统计结束时间
        /// </summary>
        public DateTime EndTime { get; set; }
        
        /// <summary>
        /// 总处理消息数
        /// </summary>
        public long TotalMessagesProcessed { get; set; }
        
        /// <summary>
        /// 成功处理数
        /// </summary>
        public long SuccessfullyProcessed { get; set; }
        
        /// <summary>
        /// 失败处理数
        /// </summary>
        public long FailedToProcess { get; set; }
        
        /// <summary>
        /// 平均处理时间（毫秒）
        /// </summary>
        public double AverageProcessingTime { get; set; }
        
        /// <summary>
        /// 消息处理速率（消息/秒）
        /// </summary>
        public double MessageThroughput { get; set; }
        
        /// <summary>
        /// 队列当前大小
        /// </summary>
        public int CurrentQueueSize { get; set; }
        
        /// <summary>
        /// 队列最大大小
        /// </summary>
        public int MaxQueueSize { get; set; }
        
        /// <summary>
        /// 活跃处理器数量
        /// </summary>
        public int ActiveHandlers { get; set; }
        
        /// <summary>
        /// 活跃订阅数量
        /// </summary>
        public int ActiveSubscriptions { get; set; }
        
        /// <summary>
        /// 内存使用量（字节）
        /// </summary>
        public long MemoryUsage { get; set; }
        
        /// <summary>
        /// CPU使用率（百分比）
        /// </summary>
        public double CpuUsage { get; set; }
        
        /// <summary>
        /// 错误率（百分比）
        /// </summary>
        public double ErrorRate { get; set; }
    }
    
    /// <summary>
    /// MQTT连接统计信息
    /// </summary>
    public class MqttConnectionStats
    {
        /// <summary>
        /// 连接状态
        /// </summary>
        public bool IsConnected { get; set; }
        
        /// <summary>
        /// 连接时间
        /// </summary>
        public DateTime? ConnectedAt { get; set; }
        
        /// <summary>
        /// 断开时间
        /// </summary>
        public DateTime? DisconnectedAt { get; set; }
        
        /// <summary>
        /// 连接持续时间
        /// </summary>
        public TimeSpan? ConnectionDuration { get; set; }
        
        /// <summary>
        /// 重连次数
        /// </summary>
        public int ReconnectionCount { get; set; }
        
        /// <summary>
        /// 发送消息总数
        /// </summary>
        public long TotalMessagesSent { get; set; }
        
        /// <summary>
        /// 接收消息总数
        /// </summary>
        public long TotalMessagesReceived { get; set; }
        
        /// <summary>
        /// 发送字节总数
        /// </summary>
        public long TotalBytesSent { get; set; }
        
        /// <summary>
        /// 接收字节总数
        /// </summary>
        public long TotalBytesReceived { get; set; }
        
        /// <summary>
        /// 最后心跳时间
        /// </summary>
        public DateTime? LastHeartbeatAt { get; set; }
        
        /// <summary>
        /// 网络延迟（毫秒）
        /// </summary>
        public double? NetworkLatency { get; set; }
        
        /// <summary>
        /// 连接质量评分（0-100）
        /// </summary>
        [Range(0, 100)]
        public int ConnectionQuality { get; set; }
        
        /// <summary>
        /// 错误总数
        /// </summary>
        public long TotalErrors { get; set; }
    }
    
    /// <summary>
    /// 设备发现事件参数
    /// </summary>
    public class DeviceDiscoveredEventArgs : EventArgs
    {
        /// <summary>
        /// 设备信息
        /// </summary>
        public DeviceInfo Device { get; set; }
        
        /// <summary>
        /// 发现时间
        /// </summary>
        public DateTime DiscoveredAt { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// 发现方式
        /// </summary>
        public string DiscoveryMethod { get; set; }
        
        /// <summary>
        /// 信号强度
        /// </summary>
        public int? SignalStrength { get; set; }
    }
    
    /// <summary>
    /// 设备状态变更事件参数
    /// </summary>
    public class DeviceStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId { get; set; }
        
        /// <summary>
        /// 旧状态
        /// </summary>
        public DeviceStatus OldStatus { get; set; }
        
        /// <summary>
        /// 新状态
        /// </summary>
        public DeviceStatus NewStatus { get; set; }
        
        /// <summary>
        /// 变更时间
        /// </summary>
        public DateTime ChangedAt { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// 变更原因
        /// </summary>
        public string Reason { get; set; }
    }
    
    /// <summary>
    /// 设备状态枚举
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum DeviceStatus
    {
        /// <summary>
        /// 未知状态
        /// </summary>
        Unknown,
        
        /// <summary>
        /// 在线
        /// </summary>
        Online,
        
        /// <summary>
        /// 离线
        /// </summary>
        Offline,
        
        /// <summary>
        /// 错误
        /// </summary>
        Error,
        
        /// <summary>
        /// 维护中
        /// </summary>
        Maintenance,
        
        /// <summary>
        /// 已禁用
        /// </summary>
        Disabled,
        
        /// <summary>
        /// 正在连接
        /// </summary>
        Connecting,
        
        /// <summary>
        /// 正在断开
        /// </summary>
        Disconnecting
    }
    
    /// <summary>
    /// 扫描状态枚举
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum ScanStatus
    {
        /// <summary>
        /// 未开始
        /// </summary>
        NotStarted,
        
        /// <summary>
        /// 正在进行
        /// </summary>
        InProgress,
        
        /// <summary>
        /// 已完成
        /// </summary>
        Completed,
        
        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled,
        
        /// <summary>
        /// 失败
        /// </summary>
        Failed,
        
        /// <summary>
        /// 超时
        /// </summary>
        Timeout
    }
    
    /// <summary>
    /// 重试策略类型枚举
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum RetryPolicyType
    {
        /// <summary>
        /// 无重试
        /// </summary>
        None,
        
        /// <summary>
        /// 固定间隔
        /// </summary>
        FixedInterval,
        
        /// <summary>
        /// 线性退避
        /// </summary>
        LinearBackoff,
        
        /// <summary>
        /// 指数退避
        /// </summary>
        ExponentialBackoff,
        
        /// <summary>
        /// 自定义
        /// </summary>
        Custom
    }
}