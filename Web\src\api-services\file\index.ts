import { service as request } from '/@/utils/request';
import { useBaseApi } from '../base';

/**
 * 文件管理API接口集合
 * @method getPage 获取文件分页列表
 * @method getDetail 获取文件详情
 * @method add 添加文件
 * @method update 更新文件
 * @method delete 删除文件
 * @method upload 上传文件
 * @method download 下载文件
 * @method getList 获取文件列表
 */
export function useFileApi() {
	const baseApi = useBaseApi('sysFile');
	
	return {
		...baseApi,
		// 上传文件
		upload: (formData: FormData | File) => {
			const data = formData instanceof File ? (() => {
				const fd = new FormData();
				fd.append('file', formData);
				return fd;
			})() : formData;
			return request({
				url: '/api/sysFile/upload',
				method: 'post',
				data,
				headers: {
					'Content-Type': 'multipart/form-data',
				},
			});
		},
		// 下载文件
		download: (fileId: number) => {
			return request({
				url: '/api/sysFile/download',
				method: 'get',
				params: { fileId },
				responseType: 'blob',
			});
		},
		// 获取文件列表
		getList: (fileName?: string, startTime?: string, endTime?: string) => {
			return request({
				url: '/api/sysFile/list',
				method: 'get',
				params: { fileName, startTime, endTime },
			});
		},
	};
}