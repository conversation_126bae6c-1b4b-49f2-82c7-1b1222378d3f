/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { TriggerStatus } from './trigger-status';
/**
 * 系统作业触发器运行记录表
 * @export
 * @interface SysJobTriggerRecord
 */
export interface SysJobTriggerRecord {
    /**
     * 雪花Id
     * @type {number}
     * @memberof SysJobTriggerRecord
     */
    id?: number;
    /**
     * 作业Id
     * @type {string}
     * @memberof SysJobTriggerRecord
     */
    jobId: string;
    /**
     * 触发器Id
     * @type {string}
     * @memberof SysJobTriggerRecord
     */
    triggerId: string;
    /**
     * 当前运行次数
     * @type {number}
     * @memberof SysJobTriggerRecord
     */
    numberOfRuns?: number;
    /**
     * 最近运行时间
     * @type {Date}
     * @memberof SysJobTriggerRecord
     */
    lastRunTime?: Date | null;
    /**
     * 下一次运行时间
     * @type {Date}
     * @memberof SysJobTriggerRecord
     */
    nextRunTime?: Date | null;
    /**
     * 
     * @type {TriggerStatus}
     * @memberof SysJobTriggerRecord
     */
    status?: TriggerStatus;
    /**
     * 本次执行结果
     * @type {string}
     * @memberof SysJobTriggerRecord
     */
    result?: string | null;
    /**
     * 本次执行耗时
     * @type {number}
     * @memberof SysJobTriggerRecord
     */
    elapsedTime?: number;
    /**
     * 创建时间
     * @type {Date}
     * @memberof SysJobTriggerRecord
     */
    createdTime?: Date | null;
}
