/**
 * 本地存储封装工具
 * 提供统一的本地存储接口，支持同步和异步操作
 */

/**
 * 存储键名常量
 */
export const StorageKeys = {
  // 用户相关
  USER_TOKEN: 'user_token',
  USER_INFO: 'user_info',
  USER_PERMISSIONS: 'user_permissions',
  
  // 设备相关
  DEVICE_LIST: 'device_list',
  DEVICE_GROUPS: 'device_groups',
  LAST_SELECTED_DEVICE: 'last_selected_device',
  
  // 系统设置
  SYSTEM_CONFIG: 'system_config',
  THEME_MODE: 'theme_mode',
  LANGUAGE: 'language',
  
  // 缓存数据
  ENERGY_CACHE: 'energy_cache',
  FAULT_CACHE: 'fault_cache',
  LIGHTING_SCENES: 'lighting_scenes'
}

// 保持向后兼容
export const STORAGE_KEYS = {
  // 用户相关
  USER_TOKEN: 'user_token',
  USER_INFO: 'user_info',
  USER_PERMISSIONS: 'user_permissions',
  
  // 设备相关
  DEVICE_LIST: 'device_list',
  DEVICE_GROUPS: 'device_groups',
  LAST_SELECTED_DEVICE: 'last_selected_device',
  
  // 系统设置
  SYSTEM_CONFIG: 'system_config',
  THEME_MODE: 'theme_mode',
  LANGUAGE: 'language',
  
  // 缓存数据
  ENERGY_CACHE: 'energy_cache',
  FAULT_CACHE: 'fault_cache',
  LIGHTING_SCENES: 'lighting_scenes'
}

/**
 * 同步存储操作
 */
export const storage = {
  /**
   * 设置存储数据
   * @param {string} key 存储键
   * @param {any} value 存储值
   * @returns {boolean} 是否成功
   */
  set(key, value) {
    try {
      const data = typeof value === 'object' ? JSON.stringify(value) : String(value)
      uni.setStorageSync(key, data)
      return true
    } catch (error) {
      console.error('Storage set error:', error)
      return false
    }
  },

  /**
   * 获取存储数据
   * @param {string} key 存储键
   * @param {any} defaultValue 默认值
   * @returns {any} 存储值
   */
  get(key, defaultValue = null) {
    try {
      const data = uni.getStorageSync(key)
      if (!data) return defaultValue
      
      // 尝试解析JSON
      try {
        return JSON.parse(data)
      } catch {
        return data
      }
    } catch (error) {
      console.error('Storage get error:', error)
      return defaultValue
    }
  },

  /**
   * 删除存储数据
   * @param {string} key 存储键
   * @returns {boolean} 是否成功
   */
  remove(key) {
    try {
      uni.removeStorageSync(key)
      return true
    } catch (error) {
      console.error('Storage remove error:', error)
      return false
    }
  },

  /**
   * 清空所有存储
   * @returns {boolean} 是否成功
   */
  clear() {
    try {
      uni.clearStorageSync()
      return true
    } catch (error) {
      console.error('Storage clear error:', error)
      return false
    }
  },

  /**
   * 获取存储信息
   * @returns {object} 存储信息
   */
  getInfo() {
    try {
      return uni.getStorageInfoSync()
    } catch (error) {
      console.error('Storage getInfo error:', error)
      return { keys: [], currentSize: 0, limitSize: 0 }
    }
  }
}

/**
 * 异步存储操作
 */
export const asyncStorage = {
  /**
   * 异步设置存储数据
   * @param {string} key 存储键
   * @param {any} value 存储值
   * @returns {Promise<boolean>} 是否成功
   */
  set(key, value) {
    return new Promise((resolve) => {
      try {
        const data = typeof value === 'object' ? JSON.stringify(value) : String(value)
        uni.setStorage({
          key,
          data,
          success: () => resolve(true),
          fail: (error) => {
            console.error('Async storage set error:', error)
            resolve(false)
          }
        })
      } catch (error) {
        console.error('Async storage set error:', error)
        resolve(false)
      }
    })
  },

  /**
   * 异步获取存储数据
   * @param {string} key 存储键
   * @param {any} defaultValue 默认值
   * @returns {Promise<any>} 存储值
   */
  get(key, defaultValue = null) {
    return new Promise((resolve) => {
      uni.getStorage({
        key,
        success: (res) => {
          try {
            const data = res.data
            if (!data) {
              resolve(defaultValue)
              return
            }
            
            // 尝试解析JSON
            try {
              resolve(JSON.parse(data))
            } catch {
              resolve(data)
            }
          } catch (error) {
            console.error('Async storage get error:', error)
            resolve(defaultValue)
          }
        },
        fail: (error) => {
          console.error('Async storage get error:', error)
          resolve(defaultValue)
        }
      })
    })
  },

  /**
   * 异步删除存储数据
   * @param {string} key 存储键
   * @returns {Promise<boolean>} 是否成功
   */
  remove(key) {
    return new Promise((resolve) => {
      uni.removeStorage({
        key,
        success: () => resolve(true),
        fail: (error) => {
          console.error('Async storage remove error:', error)
          resolve(false)
        }
      })
    })
  },

  /**
   * 异步清空所有存储
   * @returns {Promise<boolean>} 是否成功
   */
  clear() {
    return new Promise((resolve) => {
      uni.clearStorage({
        success: () => resolve(true),
        fail: (error) => {
          console.error('Async storage clear error:', error)
          resolve(false)
        }
      })
    })
  }
}

/**
 * 便捷的用户数据操作
 */
export const userStorage = {
  /**
   * 设置用户token
   * @param {string} token 用户token
   */
  setToken(token) {
    return storage.set(STORAGE_KEYS.USER_TOKEN, token)
  },

  /**
   * 获取用户token
   * @returns {string|null} 用户token
   */
  getToken() {
    return storage.get(STORAGE_KEYS.USER_TOKEN)
  },

  /**
   * 设置用户信息
   * @param {object} userInfo 用户信息
   */
  setUserInfo(userInfo) {
    return storage.set(STORAGE_KEYS.USER_INFO, userInfo)
  },

  /**
   * 获取用户信息
   * @returns {object|null} 用户信息
   */
  getUserInfo() {
    return storage.get(STORAGE_KEYS.USER_INFO)
  },

  /**
   * 清除用户数据
   */
  clearUserData() {
    storage.remove(STORAGE_KEYS.USER_TOKEN)
    storage.remove(STORAGE_KEYS.USER_INFO)
    storage.remove(STORAGE_KEYS.USER_PERMISSIONS)
  }
}

/**
 * 便捷的设备数据操作
 */
export const deviceStorage = {
  /**
   * 设置设备列表缓存
   * @param {array} deviceList 设备列表
   */
  setDeviceList(deviceList) {
    return storage.set(STORAGE_KEYS.DEVICE_LIST, {
      data: deviceList,
      timestamp: Date.now()
    })
  },

  /**
   * 获取设备列表缓存
   * @param {number} maxAge 最大缓存时间(毫秒)
   * @returns {array|null} 设备列表
   */
  getDeviceList(maxAge = 5 * 60 * 1000) {
    const cache = storage.get(STORAGE_KEYS.DEVICE_LIST)
    if (!cache || !cache.timestamp) return null
    
    const isExpired = Date.now() - cache.timestamp > maxAge
    return isExpired ? null : cache.data
  },

  /**
   * 设置最后选择的设备
   * @param {string} deviceId 设备ID
   */
  setLastSelectedDevice(deviceId) {
    return storage.set(STORAGE_KEYS.LAST_SELECTED_DEVICE, deviceId)
  },

  /**
   * 获取最后选择的设备
   * @returns {string|null} 设备ID
   */
  getLastSelectedDevice() {
    return storage.get(STORAGE_KEYS.LAST_SELECTED_DEVICE)
  }
}

// 导出便捷函数
export const setUserInfo = (userInfo) => userStorage.setUserInfo(userInfo)
export const getUserInfo = () => userStorage.getUserInfo()
export const clearUserInfo = () => userStorage.clearUserData()

export const setDeviceList = (deviceList) => deviceStorage.setDeviceList(deviceList)
export const getDeviceList = (maxAge) => deviceStorage.getDeviceList(maxAge)
export const clearDeviceList = () => {
  storage.remove(StorageKeys.DEVICE_LIST)
  storage.remove(StorageKeys.DEVICE_GROUPS)
  storage.remove(StorageKeys.LAST_SELECTED_DEVICE)
}

export default {
  storage,
  asyncStorage,
  userStorage,
  deviceStorage,
  STORAGE_KEYS,
  StorageKeys
}