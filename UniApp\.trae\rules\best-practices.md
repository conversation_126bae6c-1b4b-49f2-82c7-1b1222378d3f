---
description: Uni-App X 开发最佳实践
globs: *.uts,*.uvue,*.ts,*.js
alwaysApply: false
---

# Uni-App X 开发最佳实践

## 项目结构规范
```
project/
├── pages/                 # 页面文件
│   ├── index/
│   │   └── index.uvue
│   └── detail/
│       └── detail.uvue
├── components/            # 组件文件
│   ├── common/           # 通用组件
│   └── business/         # 业务组件
├── static/               # 静态资源
│   ├── images/
│   └── icons/
├── uni_modules/          # 插件模块
├── utils/                # 工具函数
├── types/                # 类型定义
├── stores/               # 状态管理
└── styles/               # 全局样式
    ├── common.css
    └── variables.css
```

## 代码规范

### 命名规范
```uts
// 文件命名：kebab-case
// user-profile.uvue
// api-service.uts

// 组件命名：PascalCase
const UserProfile = defineComponent({})

// 变量命名：camelCase
const userName = 'John'
const isLoggedIn = true

// 常量命名：UPPER_SNAKE_CASE
const API_BASE_URL = 'https://api.example.com'
const MAX_RETRY_COUNT = 3

// 类命名：PascalCase
class ApiService {
  // 私有属性：下划线前缀
  private _baseUrl: string
  
  // 公共方法：camelCase
  public getUserInfo(): UserInfo {
    return {}
  }
}

// 接口命名：PascalCase，I 前缀（可选）
interface IUserService {
  getUser(id: number): Promise<User>
}

type UserInfo = {
  id: number
  name: string
}
```

### 组件开发规范
```uvue
<template>
  <view class="user-profile">
    <!-- 使用语义化的类名 -->
    <view class="user-profile__header">
      <image :src="avatar" class="user-profile__avatar" />
      <text class="user-profile__name">{{ name }}</text>
    </view>
    
    <view class="user-profile__content">
      <!-- 组件内容 -->
    </view>
  </view>
</template>

<script lang="ts" setup>
// 1. 导入声明
import { ref, computed, onMounted } from 'vue'
import type { UserInfo } from '@/types/user'

// 2. Props 定义
type Props = {
  userId: number
  showAvatar?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showAvatar: true
})

// 3. Emits 定义
type Emits = {
  'user-click': [user: UserInfo]
  'avatar-click': []
}

const emit = defineEmits<Emits>()

// 4. 响应式数据
const user = ref<UserInfo | null>(null)
const loading = ref(false)

// 5. 计算属性
const avatar = computed(() => {
  return user.value?.avatar || '/static/default-avatar.png'
})

const name = computed(() => {
  return user.value?.name || '未知用户'
})

// 6. 方法定义
const fetchUser = async () => {
  try {
    loading.value = true
    // 获取用户信息
  } catch (error) {
    console.error('获取用户信息失败:', error)
  } finally {
    loading.value = false
  }
}

const handleUserClick = () => {
  if (user.value) {
    emit('user-click', user.value)
  }
}

// 7. 生命周期
onMounted(() => {
  fetchUser()
})

// 8. 暴露给父组件
defineExpose({
  fetchUser,
  user: readonly(user)
})
</script>

<style scoped>
/* 使用 BEM 命名规范 */
.user-profile {
  padding: 20rpx;
}

.user-profile__header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.user-profile__avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-profile__name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.user-profile__content {
  /* 内容样式 */
}
</style>
```

## 性能优化

### 组件优化
```uvue
<script lang="ts" setup>
import { ref, computed, shallowRef, markRaw } from 'vue'

// 使用 shallowRef 优化大对象
const largeData = shallowRef({})

// 使用 markRaw 标记不需要响应式的对象
const staticConfig = markRaw({
  apiUrl: 'https://api.example.com',
  timeout: 5000
})

// 缓存计算结果
const expensiveComputed = computed(() => {
  // 复杂计算逻辑
  return heavyCalculation()
})

// 避免在模板中使用复杂表达式
const formattedDate = computed(() => {
  return new Date().toLocaleDateString()
})
</script>

<template>
  <view>
    <!-- 好的做法 -->
    <text>{{ formattedDate }}</text>
    
    <!-- 避免这样做 -->
    <!-- <text>{{ new Date().toLocaleDateString() }}</text> -->
  </view>
</template>
```

### 列表优化
```uvue
<template>
  <list-view class="list" @scrolltolower="loadMore">
    <list-item 
      v-for="item in list" 
      :key="item.id"
      class="list-item"
    >
      <!-- 使用 v-show 而不是 v-if 来控制显示/隐藏 -->
      <view v-show="item.visible" class="item-content">
        <text>{{ item.title }}</text>
      </view>
    </list-item>
  </list-view>
</template>

<script lang="ts" setup>
import { ref, shallowRef } from 'vue'

// 使用 shallowRef 优化大列表
const list = shallowRef<ListItem[]>([])
const loading = ref(false)
const hasMore = ref(true)

// 分页加载
const loadMore = async () => {
  if (loading.value || !hasMore.value) return
  
  try {
    loading.value = true
    const newItems = await fetchListData(list.value.length)
    
    if (newItems.length === 0) {
      hasMore.value = false
    } else {
      list.value = [...list.value, ...newItems]
    }
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}
</script>
```

## 错误处理

### 全局错误处理
```uts
// utils/error-handler.uts
export class ErrorHandler {
  static handle(error: Error, context?: string): void {
    console.error(`[${context || 'Unknown'}] Error:`, error)
    
    // 根据错误类型进行不同处理
    if (error instanceof NetworkError) {
      uni.showToast({
        title: '网络连接失败',
        icon: 'none'
      })
    } else if (error instanceof ValidationError) {
      uni.showToast({
        title: error.message,
        icon: 'none'
      })
    } else {
      uni.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      })
    }
  }
}

// 自定义错误类
export class NetworkError extends Error {
  constructor(message: string) {
    super(message)
    this.name = 'NetworkError'
  }
}

export class ValidationError extends Error {
  constructor(message: string) {
    super(message)
    this.name = 'ValidationError'
  }
}
```

### 组件错误边界
```uvue
<script lang="ts" setup>
import { ref, onErrorCaptured } from 'vue'
import { ErrorHandler } from '@/utils/error-handler'

const hasError = ref(false)
const errorMessage = ref('')

// 捕获子组件错误
onErrorCaptured((error: Error, instance, info) => {
  hasError.value = true
  errorMessage.value = error.message
  
  ErrorHandler.handle(error, 'Component Error')
  
  // 返回 false 阻止错误继续传播
  return false
})

const retry = () => {
  hasError.value = false
  errorMessage.value = ''
}
</script>

<template>
  <view>
    <view v-if="hasError" class="error-boundary">
      <text class="error-message">{{ errorMessage }}</text>
      <button @click="retry">重试</button>
    </view>
    <slot v-else></slot>
  </view>
</template>
```

## 状态管理

### 使用 Pinia
```uts
// stores/user.uts
import { defineStore } from 'pinia'
import type { UserInfo } from '@/types/user'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<UserInfo | null>(null)
  const isLoggedIn = ref(false)
  const loading = ref(false)
  
  // 计算属性
  const userName = computed(() => user.value?.name || '')
  const userAvatar = computed(() => user.value?.avatar || '/static/default-avatar.png')
  
  // 方法
  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    try {
      loading.value = true
      const response = await authApi.login(credentials)
      
      user.value = response.user
      isLoggedIn.value = true
      
      // 保存登录状态
      uni.setStorageSync('token', response.token)
      
      return true
    } catch (error) {
      ErrorHandler.handle(error as Error, 'Login')
      return false
    } finally {
      loading.value = false
    }
  }
  
  const logout = () => {
    user.value = null
    isLoggedIn.value = false
    uni.removeStorageSync('token')
  }
  
  const updateProfile = async (profile: Partial<UserInfo>): Promise<boolean> => {
    try {
      loading.value = true
      const updatedUser = await userApi.updateProfile(profile)
      user.value = { ...user.value, ...updatedUser }
      return true
    } catch (error) {
      ErrorHandler.handle(error as Error, 'Update Profile')
      return false
    } finally {
      loading.value = false
    }
  }
  
  return {
    // 状态
    user: readonly(user),
    isLoggedIn: readonly(isLoggedIn),
    loading: readonly(loading),
    
    // 计算属性
    userName,
    userAvatar,
    
    // 方法
    login,
    logout,
    updateProfile
  }
})
```

## 工具函数

### 通用工具
```uts
// utils/common.uts

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: number | null = null
  
  return function(...args: Parameters<T>) {
    if (timeout !== null) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      func.apply(this, args)
    }, wait)
  }
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle = false
  
  return function(...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 深拷贝
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as T
  }
  
  if (typeof obj === 'object') {
    const cloned = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key])
      }
    }
    return cloned
  }
  
  return obj
}

// 格式化日期
export function formatDate(date: Date, format: string = 'YYYY-MM-DD'): string {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

// 验证工具
export const validators = {
  email: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  },
  
  phone: (phone: string): boolean => {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  },
  
  required: (value: any): boolean => {
    return value !== null && value !== undefined && value !== ''
  },
  
  minLength: (value: string, min: number): boolean => {
    return value.length >= min
  },
  
  maxLength: (value: string, max: number): boolean => {
    return value.length <= max
  }
}
```

## 测试规范

### 单元测试
```uts
// tests/utils/common.test.uts
import { describe, it, expect } from 'vitest'
import { debounce, throttle, formatDate, validators } from '@/utils/common'

describe('Common Utils', () => {
  describe('formatDate', () => {
    it('should format date correctly', () => {
      const date = new Date('2023-12-25 15:30:45')
      expect(formatDate(date, 'YYYY-MM-DD')).toBe('2023-12-25')
      expect(formatDate(date, 'YYYY-MM-DD HH:mm:ss')).toBe('2023-12-25 15:30:45')
    })
  })
  
  describe('validators', () => {
    it('should validate email correctly', () => {
      expect(validators.email('<EMAIL>')).toBe(true)
      expect(validators.email('invalid-email')).toBe(false)
    })
    
    it('should validate phone correctly', () => {
      expect(validators.phone('13812345678')).toBe(true)
      expect(validators.phone('12345678901')).toBe(false)
    })
  })
})
```

## 部署和构建

### 环境配置
```json
// package.json
{
  "scripts": {
    "dev": "uni build --watch",
    "build:app": "uni build app",
    "build:h5": "uni build h5",
    "build:mp-weixin": "uni build mp-weixin",
    "test": "vitest",
    "lint": "eslint . --ext .vue,.js,.ts,.uts",
    "lint:fix": "eslint . --ext .vue,.js,.ts,.uts --fix"
  }
}
```

### 代码质量检查
```json
// .eslintrc.json
{
  "extends": [
    "@dcloudio/eslint-config-uni"
  ],
  "rules": {
    "no-console": "warn",
    "no-debugger": "error",
    "prefer-const": "error",
    "no-var": "error"
  }
}
```