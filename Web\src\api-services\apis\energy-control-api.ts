/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AddEnergyControlInput } from '../models';
import { AdminResultBoolean } from '../models';
import { AdminResultEnergyControlOutput } from '../models';
import { AdminResultEnergyControlStatOutput } from '../models';
import { AdminResultFileStreamResult } from '../models';
import { AdminResultIActionResult } from '../models';
import { AdminResultInt32 } from '../models';
import { AdminResultInt64 } from '../models';
import { AdminResultListEnergyControlTrendOutput } from '../models';
import { AdminResultSqlSugarPagedListEnergyControlOutput } from '../models';
import { DeleteEnergyControlInput } from '../models';
import { DeviceControlInput } from '../models';
import { EnergyControlInput } from '../models';
import { Filter } from '../models';
import { FilterLogicEnum } from '../models';
import { FilterOperatorEnum } from '../models';
import { UpdateEnergyControlInput } from '../models';
/**
 * EnergyControlApi - axios parameter creator
 * @export
 */
export const EnergyControlApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加控制记录 🔖
         * @param {AddEnergyControlInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyControlAddPost: async (body?: AddEnergyControlInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyControl/add`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 批量删除历史记录 🔖
         * @param {number} days 保留天数
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyControlCleanHistoryDataDaysPost: async (days: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'days' is not null or undefined
            if (days === null || days === undefined) {
                throw new RequiredError('days','Required parameter days was null or undefined when calling apiEnergyControlCleanHistoryDataDaysPost.');
            }
            const localVarPath = `/api/energyControl/cleanHistoryData/{days}`
                .replace(`{${"days"}}`, encodeURIComponent(String(days)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新控制结果 🔖
         * @param {number} controlId 控制记录ID
         * @param {number} status 执行状态
         * @param {string} result 执行结果
         * @param {string} errorMessage 错误信息
         * @param {number} [duration] 执行耗时
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyControlControlResultControlIdStatusResultErrorMessageDurationPut: async (controlId: number, status: number, result: string, errorMessage: string, duration?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'controlId' is not null or undefined
            if (controlId === null || controlId === undefined) {
                throw new RequiredError('controlId','Required parameter controlId was null or undefined when calling apiEnergyControlControlResultControlIdStatusResultErrorMessageDurationPut.');
            }
            // verify required parameter 'status' is not null or undefined
            if (status === null || status === undefined) {
                throw new RequiredError('status','Required parameter status was null or undefined when calling apiEnergyControlControlResultControlIdStatusResultErrorMessageDurationPut.');
            }
            // verify required parameter 'result' is not null or undefined
            if (result === null || result === undefined) {
                throw new RequiredError('result','Required parameter result was null or undefined when calling apiEnergyControlControlResultControlIdStatusResultErrorMessageDurationPut.');
            }
            // verify required parameter 'errorMessage' is not null or undefined
            if (errorMessage === null || errorMessage === undefined) {
                throw new RequiredError('errorMessage','Required parameter errorMessage was null or undefined when calling apiEnergyControlControlResultControlIdStatusResultErrorMessageDurationPut.');
            }
            const localVarPath = `/api/energyControl/controlResult/{controlId}/{status}/{result}/{errorMessage}/{duration}`
                .replace(`{${"controlId"}}`, encodeURIComponent(String(controlId)))
                .replace(`{${"status"}}`, encodeURIComponent(String(status)))
                .replace(`{${"result"}}`, encodeURIComponent(String(result)))
                .replace(`{${"errorMessage"}}`, encodeURIComponent(String(errorMessage)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (duration !== undefined) {
                localVarQueryParameter['duration'] = duration;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 删除控制记录 🔖
         * @param {DeleteEnergyControlInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyControlDeletePost: async (body?: DeleteEnergyControlInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyControl/delete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取控制记录详情 🔖
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyControlDetailGet: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiEnergyControlDetailGet.');
            }
            const localVarPath = `/api/energyControl/detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (id !== undefined) {
                localVarQueryParameter['Id'] = id;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 设备控制指令 🔖
         * @param {DeviceControlInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyControlDeviceControlPost: async (body?: DeviceControlInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyControl/deviceControl`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 导出控制记录数据 🔖
         * @param {number} [deviceId] 设备ID
         * @param {number} [sceneId] 场景ID
         * @param {string} [controlType] 控制类型
         * @param {string} [controlSource] 控制来源
         * @param {number} [executeStatus] 执行状态
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {string} [deviceCode] 设备编码
         * @param {string} [deviceName] 设备名称
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyControlExportDataPost: async (deviceId?: number, sceneId?: number, controlType?: string, controlSource?: string, executeStatus?: number, startTime?: Date, endTime?: Date, deviceCode?: string, deviceName?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyControl/exportData`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (deviceId !== undefined) {
                localVarQueryParameter['DeviceId'] = deviceId;
            }

            if (sceneId !== undefined) {
                localVarQueryParameter['SceneId'] = sceneId;
            }

            if (controlType !== undefined) {
                localVarQueryParameter['ControlType'] = controlType;
            }

            if (controlSource !== undefined) {
                localVarQueryParameter['ControlSource'] = controlSource;
            }

            if (executeStatus !== undefined) {
                localVarQueryParameter['ExecuteStatus'] = executeStatus;
            }

            if (startTime !== undefined) {
                localVarQueryParameter['StartTime'] = (startTime as any instanceof Date) ?
                    (startTime as any).toISOString() :
                    startTime;
            }

            if (endTime !== undefined) {
                localVarQueryParameter['EndTime'] = (endTime as any instanceof Date) ?
                    (endTime as any).toISOString() :
                    endTime;
            }

            if (deviceCode !== undefined) {
                localVarQueryParameter['DeviceCode'] = deviceCode;
            }

            if (deviceName !== undefined) {
                localVarQueryParameter['DeviceName'] = deviceName;
            }

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            if (field !== undefined) {
                localVarQueryParameter['Field'] = field;
            }

            if (order !== undefined) {
                localVarQueryParameter['Order'] = order;
            }

            if (descStr !== undefined) {
                localVarQueryParameter['DescStr'] = descStr;
            }

            if (searchFields) {
                localVarQueryParameter['Search.Fields'] = searchFields;
            }

            if (searchKeyword !== undefined) {
                localVarQueryParameter['Search.Keyword'] = searchKeyword;
            }

            if (keyword !== undefined) {
                localVarQueryParameter['Keyword'] = keyword;
            }

            if (filterLogic !== undefined) {
                localVarQueryParameter['Filter.Logic'] = filterLogic;
            }

            if (filterFilters) {
                localVarQueryParameter['Filter.Filters'] = filterFilters;
            }

            if (filterField !== undefined) {
                localVarQueryParameter['Filter.Field'] = filterField;
            }

            if (filterOperator !== undefined) {
                localVarQueryParameter['Filter.Operator'] = filterOperator;
            }

            if (filterValue !== undefined) {
                localVarQueryParameter['Filter.Value'] = filterValue;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 导出控制记录数据到Excel 🔖
         * @param {EnergyControlInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyControlExportExcelPost: async (body?: EnergyControlInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyControl/exportExcel`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取控制记录分页列表 🔖
         * @param {number} [deviceId] 设备ID
         * @param {number} [sceneId] 场景ID
         * @param {string} [controlType] 控制类型
         * @param {string} [controlSource] 控制来源
         * @param {number} [executeStatus] 执行状态
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {string} [deviceCode] 设备编码
         * @param {string} [deviceName] 设备名称
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyControlPageGet: async (deviceId?: number, sceneId?: number, controlType?: string, controlSource?: string, executeStatus?: number, startTime?: Date, endTime?: Date, deviceCode?: string, deviceName?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyControl/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (deviceId !== undefined) {
                localVarQueryParameter['DeviceId'] = deviceId;
            }

            if (sceneId !== undefined) {
                localVarQueryParameter['SceneId'] = sceneId;
            }

            if (controlType !== undefined) {
                localVarQueryParameter['ControlType'] = controlType;
            }

            if (controlSource !== undefined) {
                localVarQueryParameter['ControlSource'] = controlSource;
            }

            if (executeStatus !== undefined) {
                localVarQueryParameter['ExecuteStatus'] = executeStatus;
            }

            if (startTime !== undefined) {
                localVarQueryParameter['StartTime'] = (startTime as any instanceof Date) ?
                    (startTime as any).toISOString() :
                    startTime;
            }

            if (endTime !== undefined) {
                localVarQueryParameter['EndTime'] = (endTime as any instanceof Date) ?
                    (endTime as any).toISOString() :
                    endTime;
            }

            if (deviceCode !== undefined) {
                localVarQueryParameter['DeviceCode'] = deviceCode;
            }

            if (deviceName !== undefined) {
                localVarQueryParameter['DeviceName'] = deviceName;
            }

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            if (field !== undefined) {
                localVarQueryParameter['Field'] = field;
            }

            if (order !== undefined) {
                localVarQueryParameter['Order'] = order;
            }

            if (descStr !== undefined) {
                localVarQueryParameter['DescStr'] = descStr;
            }

            if (searchFields) {
                localVarQueryParameter['Search.Fields'] = searchFields;
            }

            if (searchKeyword !== undefined) {
                localVarQueryParameter['Search.Keyword'] = searchKeyword;
            }

            if (keyword !== undefined) {
                localVarQueryParameter['Keyword'] = keyword;
            }

            if (filterLogic !== undefined) {
                localVarQueryParameter['Filter.Logic'] = filterLogic;
            }

            if (filterFilters) {
                localVarQueryParameter['Filter.Filters'] = filterFilters;
            }

            if (filterField !== undefined) {
                localVarQueryParameter['Filter.Field'] = filterField;
            }

            if (filterOperator !== undefined) {
                localVarQueryParameter['Filter.Operator'] = filterOperator;
            }

            if (filterValue !== undefined) {
                localVarQueryParameter['Filter.Value'] = filterValue;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 重试失败的控制指令 🔖
         * @param {number} controlId 控制记录ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyControlRetryControlIdPost: async (controlId: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'controlId' is not null or undefined
            if (controlId === null || controlId === undefined) {
                throw new RequiredError('controlId','Required parameter controlId was null or undefined when calling apiEnergyControlRetryControlIdPost.');
            }
            const localVarPath = `/api/energyControl/retry/{controlId}`
                .replace(`{${"controlId"}}`, encodeURIComponent(String(controlId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取控制统计数据 🔖
         * @param {string} statType 统计类型
         * @param {number} [deviceId] 设备ID
         * @param {number} [groupId] 设备分组ID
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyControlStatGet: async (statType: string, deviceId?: number, groupId?: number, startTime?: Date, endTime?: Date, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'statType' is not null or undefined
            if (statType === null || statType === undefined) {
                throw new RequiredError('statType','Required parameter statType was null or undefined when calling apiEnergyControlStatGet.');
            }
            const localVarPath = `/api/energyControl/stat`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (deviceId !== undefined) {
                localVarQueryParameter['DeviceId'] = deviceId;
            }

            if (groupId !== undefined) {
                localVarQueryParameter['GroupId'] = groupId;
            }

            if (startTime !== undefined) {
                localVarQueryParameter['StartTime'] = (startTime as any instanceof Date) ?
                    (startTime as any).toISOString() :
                    startTime;
            }

            if (endTime !== undefined) {
                localVarQueryParameter['EndTime'] = (endTime as any instanceof Date) ?
                    (endTime as any).toISOString() :
                    endTime;
            }

            if (statType !== undefined) {
                localVarQueryParameter['StatType'] = statType;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取控制趋势数据 🔖
         * @param {string} statType 统计类型
         * @param {number} [deviceId] 设备ID
         * @param {number} [groupId] 设备分组ID
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyControlTrendGet: async (statType: string, deviceId?: number, groupId?: number, startTime?: Date, endTime?: Date, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'statType' is not null or undefined
            if (statType === null || statType === undefined) {
                throw new RequiredError('statType','Required parameter statType was null or undefined when calling apiEnergyControlTrendGet.');
            }
            const localVarPath = `/api/energyControl/trend`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (deviceId !== undefined) {
                localVarQueryParameter['DeviceId'] = deviceId;
            }

            if (groupId !== undefined) {
                localVarQueryParameter['GroupId'] = groupId;
            }

            if (startTime !== undefined) {
                localVarQueryParameter['StartTime'] = (startTime as any instanceof Date) ?
                    (startTime as any).toISOString() :
                    startTime;
            }

            if (endTime !== undefined) {
                localVarQueryParameter['EndTime'] = (endTime as any instanceof Date) ?
                    (endTime as any).toISOString() :
                    endTime;
            }

            if (statType !== undefined) {
                localVarQueryParameter['StatType'] = statType;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新控制记录 🔖
         * @param {UpdateEnergyControlInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnergyControlUpdatePost: async (body?: UpdateEnergyControlInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/energyControl/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * EnergyControlApi - functional programming interface
 * @export
 */
export const EnergyControlApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加控制记录 🔖
         * @param {AddEnergyControlInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlAddPost(body?: AddEnergyControlInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt64>>> {
            const localVarAxiosArgs = await EnergyControlApiAxiosParamCreator(configuration).apiEnergyControlAddPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 批量删除历史记录 🔖
         * @param {number} days 保留天数
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlCleanHistoryDataDaysPost(days: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt32>>> {
            const localVarAxiosArgs = await EnergyControlApiAxiosParamCreator(configuration).apiEnergyControlCleanHistoryDataDaysPost(days, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新控制结果 🔖
         * @param {number} controlId 控制记录ID
         * @param {number} status 执行状态
         * @param {string} result 执行结果
         * @param {string} errorMessage 错误信息
         * @param {number} [duration] 执行耗时
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlControlResultControlIdStatusResultErrorMessageDurationPut(controlId: number, status: number, result: string, errorMessage: string, duration?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await EnergyControlApiAxiosParamCreator(configuration).apiEnergyControlControlResultControlIdStatusResultErrorMessageDurationPut(controlId, status, result, errorMessage, duration, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 删除控制记录 🔖
         * @param {DeleteEnergyControlInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlDeletePost(body?: DeleteEnergyControlInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await EnergyControlApiAxiosParamCreator(configuration).apiEnergyControlDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取控制记录详情 🔖
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlDetailGet(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultEnergyControlOutput>>> {
            const localVarAxiosArgs = await EnergyControlApiAxiosParamCreator(configuration).apiEnergyControlDetailGet(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 设备控制指令 🔖
         * @param {DeviceControlInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlDeviceControlPost(body?: DeviceControlInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt64>>> {
            const localVarAxiosArgs = await EnergyControlApiAxiosParamCreator(configuration).apiEnergyControlDeviceControlPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 导出控制记录数据 🔖
         * @param {number} [deviceId] 设备ID
         * @param {number} [sceneId] 场景ID
         * @param {string} [controlType] 控制类型
         * @param {string} [controlSource] 控制来源
         * @param {number} [executeStatus] 执行状态
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {string} [deviceCode] 设备编码
         * @param {string} [deviceName] 设备名称
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlExportDataPost(deviceId?: number, sceneId?: number, controlType?: string, controlSource?: string, executeStatus?: number, startTime?: Date, endTime?: Date, deviceCode?: string, deviceName?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultFileStreamResult>>> {
            const localVarAxiosArgs = await EnergyControlApiAxiosParamCreator(configuration).apiEnergyControlExportDataPost(deviceId, sceneId, controlType, controlSource, executeStatus, startTime, endTime, deviceCode, deviceName, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 导出控制记录数据到Excel 🔖
         * @param {EnergyControlInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlExportExcelPost(body?: EnergyControlInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultIActionResult>>> {
            const localVarAxiosArgs = await EnergyControlApiAxiosParamCreator(configuration).apiEnergyControlExportExcelPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取控制记录分页列表 🔖
         * @param {number} [deviceId] 设备ID
         * @param {number} [sceneId] 场景ID
         * @param {string} [controlType] 控制类型
         * @param {string} [controlSource] 控制来源
         * @param {number} [executeStatus] 执行状态
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {string} [deviceCode] 设备编码
         * @param {string} [deviceName] 设备名称
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlPageGet(deviceId?: number, sceneId?: number, controlType?: string, controlSource?: string, executeStatus?: number, startTime?: Date, endTime?: Date, deviceCode?: string, deviceName?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSqlSugarPagedListEnergyControlOutput>>> {
            const localVarAxiosArgs = await EnergyControlApiAxiosParamCreator(configuration).apiEnergyControlPageGet(deviceId, sceneId, controlType, controlSource, executeStatus, startTime, endTime, deviceCode, deviceName, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 重试失败的控制指令 🔖
         * @param {number} controlId 控制记录ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlRetryControlIdPost(controlId: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultBoolean>>> {
            const localVarAxiosArgs = await EnergyControlApiAxiosParamCreator(configuration).apiEnergyControlRetryControlIdPost(controlId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取控制统计数据 🔖
         * @param {string} statType 统计类型
         * @param {number} [deviceId] 设备ID
         * @param {number} [groupId] 设备分组ID
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlStatGet(statType: string, deviceId?: number, groupId?: number, startTime?: Date, endTime?: Date, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultEnergyControlStatOutput>>> {
            const localVarAxiosArgs = await EnergyControlApiAxiosParamCreator(configuration).apiEnergyControlStatGet(statType, deviceId, groupId, startTime, endTime, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取控制趋势数据 🔖
         * @param {string} statType 统计类型
         * @param {number} [deviceId] 设备ID
         * @param {number} [groupId] 设备分组ID
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlTrendGet(statType: string, deviceId?: number, groupId?: number, startTime?: Date, endTime?: Date, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultListEnergyControlTrendOutput>>> {
            const localVarAxiosArgs = await EnergyControlApiAxiosParamCreator(configuration).apiEnergyControlTrendGet(statType, deviceId, groupId, startTime, endTime, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新控制记录 🔖
         * @param {UpdateEnergyControlInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlUpdatePost(body?: UpdateEnergyControlInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await EnergyControlApiAxiosParamCreator(configuration).apiEnergyControlUpdatePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * EnergyControlApi - factory interface
 * @export
 */
export const EnergyControlApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 增加控制记录 🔖
         * @param {AddEnergyControlInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlAddPost(body?: AddEnergyControlInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt64>> {
            return EnergyControlApiFp(configuration).apiEnergyControlAddPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 批量删除历史记录 🔖
         * @param {number} days 保留天数
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlCleanHistoryDataDaysPost(days: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt32>> {
            return EnergyControlApiFp(configuration).apiEnergyControlCleanHistoryDataDaysPost(days, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新控制结果 🔖
         * @param {number} controlId 控制记录ID
         * @param {number} status 执行状态
         * @param {string} result 执行结果
         * @param {string} errorMessage 错误信息
         * @param {number} [duration] 执行耗时
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlControlResultControlIdStatusResultErrorMessageDurationPut(controlId: number, status: number, result: string, errorMessage: string, duration?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return EnergyControlApiFp(configuration).apiEnergyControlControlResultControlIdStatusResultErrorMessageDurationPut(controlId, status, result, errorMessage, duration, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 删除控制记录 🔖
         * @param {DeleteEnergyControlInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlDeletePost(body?: DeleteEnergyControlInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return EnergyControlApiFp(configuration).apiEnergyControlDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取控制记录详情 🔖
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlDetailGet(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultEnergyControlOutput>> {
            return EnergyControlApiFp(configuration).apiEnergyControlDetailGet(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 设备控制指令 🔖
         * @param {DeviceControlInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlDeviceControlPost(body?: DeviceControlInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt64>> {
            return EnergyControlApiFp(configuration).apiEnergyControlDeviceControlPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 导出控制记录数据 🔖
         * @param {number} [deviceId] 设备ID
         * @param {number} [sceneId] 场景ID
         * @param {string} [controlType] 控制类型
         * @param {string} [controlSource] 控制来源
         * @param {number} [executeStatus] 执行状态
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {string} [deviceCode] 设备编码
         * @param {string} [deviceName] 设备名称
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlExportDataPost(deviceId?: number, sceneId?: number, controlType?: string, controlSource?: string, executeStatus?: number, startTime?: Date, endTime?: Date, deviceCode?: string, deviceName?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultFileStreamResult>> {
            return EnergyControlApiFp(configuration).apiEnergyControlExportDataPost(deviceId, sceneId, controlType, controlSource, executeStatus, startTime, endTime, deviceCode, deviceName, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 导出控制记录数据到Excel 🔖
         * @param {EnergyControlInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlExportExcelPost(body?: EnergyControlInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultIActionResult>> {
            return EnergyControlApiFp(configuration).apiEnergyControlExportExcelPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取控制记录分页列表 🔖
         * @param {number} [deviceId] 设备ID
         * @param {number} [sceneId] 场景ID
         * @param {string} [controlType] 控制类型
         * @param {string} [controlSource] 控制来源
         * @param {number} [executeStatus] 执行状态
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {string} [deviceCode] 设备编码
         * @param {string} [deviceName] 设备名称
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlPageGet(deviceId?: number, sceneId?: number, controlType?: string, controlSource?: string, executeStatus?: number, startTime?: Date, endTime?: Date, deviceCode?: string, deviceName?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSqlSugarPagedListEnergyControlOutput>> {
            return EnergyControlApiFp(configuration).apiEnergyControlPageGet(deviceId, sceneId, controlType, controlSource, executeStatus, startTime, endTime, deviceCode, deviceName, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 重试失败的控制指令 🔖
         * @param {number} controlId 控制记录ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlRetryControlIdPost(controlId: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultBoolean>> {
            return EnergyControlApiFp(configuration).apiEnergyControlRetryControlIdPost(controlId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取控制统计数据 🔖
         * @param {string} statType 统计类型
         * @param {number} [deviceId] 设备ID
         * @param {number} [groupId] 设备分组ID
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlStatGet(statType: string, deviceId?: number, groupId?: number, startTime?: Date, endTime?: Date, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultEnergyControlStatOutput>> {
            return EnergyControlApiFp(configuration).apiEnergyControlStatGet(statType, deviceId, groupId, startTime, endTime, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取控制趋势数据 🔖
         * @param {string} statType 统计类型
         * @param {number} [deviceId] 设备ID
         * @param {number} [groupId] 设备分组ID
         * @param {Date} [startTime] 开始时间
         * @param {Date} [endTime] 结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlTrendGet(statType: string, deviceId?: number, groupId?: number, startTime?: Date, endTime?: Date, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultListEnergyControlTrendOutput>> {
            return EnergyControlApiFp(configuration).apiEnergyControlTrendGet(statType, deviceId, groupId, startTime, endTime, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新控制记录 🔖
         * @param {UpdateEnergyControlInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnergyControlUpdatePost(body?: UpdateEnergyControlInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return EnergyControlApiFp(configuration).apiEnergyControlUpdatePost(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * EnergyControlApi - object-oriented interface
 * @export
 * @class EnergyControlApi
 * @extends {BaseAPI}
 */
export class EnergyControlApi extends BaseAPI {
    /**
     * 
     * @summary 增加控制记录 🔖
     * @param {AddEnergyControlInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyControlApi
     */
    public async apiEnergyControlAddPost(body?: AddEnergyControlInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt64>> {
        return EnergyControlApiFp(this.configuration).apiEnergyControlAddPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 批量删除历史记录 🔖
     * @param {number} days 保留天数
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyControlApi
     */
    public async apiEnergyControlCleanHistoryDataDaysPost(days: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt32>> {
        return EnergyControlApiFp(this.configuration).apiEnergyControlCleanHistoryDataDaysPost(days, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新控制结果 🔖
     * @param {number} controlId 控制记录ID
     * @param {number} status 执行状态
     * @param {string} result 执行结果
     * @param {string} errorMessage 错误信息
     * @param {number} [duration] 执行耗时
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyControlApi
     */
    public async apiEnergyControlControlResultControlIdStatusResultErrorMessageDurationPut(controlId: number, status: number, result: string, errorMessage: string, duration?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return EnergyControlApiFp(this.configuration).apiEnergyControlControlResultControlIdStatusResultErrorMessageDurationPut(controlId, status, result, errorMessage, duration, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 删除控制记录 🔖
     * @param {DeleteEnergyControlInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyControlApi
     */
    public async apiEnergyControlDeletePost(body?: DeleteEnergyControlInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return EnergyControlApiFp(this.configuration).apiEnergyControlDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取控制记录详情 🔖
     * @param {number} id 主键Id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyControlApi
     */
    public async apiEnergyControlDetailGet(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultEnergyControlOutput>> {
        return EnergyControlApiFp(this.configuration).apiEnergyControlDetailGet(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 设备控制指令 🔖
     * @param {DeviceControlInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyControlApi
     */
    public async apiEnergyControlDeviceControlPost(body?: DeviceControlInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt64>> {
        return EnergyControlApiFp(this.configuration).apiEnergyControlDeviceControlPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 导出控制记录数据 🔖
     * @param {number} [deviceId] 设备ID
     * @param {number} [sceneId] 场景ID
     * @param {string} [controlType] 控制类型
     * @param {string} [controlSource] 控制来源
     * @param {number} [executeStatus] 执行状态
     * @param {Date} [startTime] 开始时间
     * @param {Date} [endTime] 结束时间
     * @param {string} [deviceCode] 设备编码
     * @param {string} [deviceName] 设备名称
     * @param {number} [page] 当前页码
     * @param {number} [pageSize] 页码容量
     * @param {string} [field] 排序字段
     * @param {string} [order] 排序方向
     * @param {string} [descStr] 降序排序
     * @param {Array<string>} [searchFields] 字段名称集合
     * @param {string} [searchKeyword] 关键字
     * @param {string} [keyword] 模糊查询关键字
     * @param {FilterLogicEnum} [filterLogic] 过滤条件
     * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
     * @param {string} [filterField] 字段名称
     * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
     * @param {any} [filterValue] 字段值
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyControlApi
     */
    public async apiEnergyControlExportDataPost(deviceId?: number, sceneId?: number, controlType?: string, controlSource?: string, executeStatus?: number, startTime?: Date, endTime?: Date, deviceCode?: string, deviceName?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultFileStreamResult>> {
        return EnergyControlApiFp(this.configuration).apiEnergyControlExportDataPost(deviceId, sceneId, controlType, controlSource, executeStatus, startTime, endTime, deviceCode, deviceName, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 导出控制记录数据到Excel 🔖
     * @param {EnergyControlInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyControlApi
     */
    public async apiEnergyControlExportExcelPost(body?: EnergyControlInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultIActionResult>> {
        return EnergyControlApiFp(this.configuration).apiEnergyControlExportExcelPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取控制记录分页列表 🔖
     * @param {number} [deviceId] 设备ID
     * @param {number} [sceneId] 场景ID
     * @param {string} [controlType] 控制类型
     * @param {string} [controlSource] 控制来源
     * @param {number} [executeStatus] 执行状态
     * @param {Date} [startTime] 开始时间
     * @param {Date} [endTime] 结束时间
     * @param {string} [deviceCode] 设备编码
     * @param {string} [deviceName] 设备名称
     * @param {number} [page] 当前页码
     * @param {number} [pageSize] 页码容量
     * @param {string} [field] 排序字段
     * @param {string} [order] 排序方向
     * @param {string} [descStr] 降序排序
     * @param {Array<string>} [searchFields] 字段名称集合
     * @param {string} [searchKeyword] 关键字
     * @param {string} [keyword] 模糊查询关键字
     * @param {FilterLogicEnum} [filterLogic] 过滤条件
     * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
     * @param {string} [filterField] 字段名称
     * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
     * @param {any} [filterValue] 字段值
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyControlApi
     */
    public async apiEnergyControlPageGet(deviceId?: number, sceneId?: number, controlType?: string, controlSource?: string, executeStatus?: number, startTime?: Date, endTime?: Date, deviceCode?: string, deviceName?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSqlSugarPagedListEnergyControlOutput>> {
        return EnergyControlApiFp(this.configuration).apiEnergyControlPageGet(deviceId, sceneId, controlType, controlSource, executeStatus, startTime, endTime, deviceCode, deviceName, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 重试失败的控制指令 🔖
     * @param {number} controlId 控制记录ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyControlApi
     */
    public async apiEnergyControlRetryControlIdPost(controlId: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultBoolean>> {
        return EnergyControlApiFp(this.configuration).apiEnergyControlRetryControlIdPost(controlId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取控制统计数据 🔖
     * @param {string} statType 统计类型
     * @param {number} [deviceId] 设备ID
     * @param {number} [groupId] 设备分组ID
     * @param {Date} [startTime] 开始时间
     * @param {Date} [endTime] 结束时间
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyControlApi
     */
    public async apiEnergyControlStatGet(statType: string, deviceId?: number, groupId?: number, startTime?: Date, endTime?: Date, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultEnergyControlStatOutput>> {
        return EnergyControlApiFp(this.configuration).apiEnergyControlStatGet(statType, deviceId, groupId, startTime, endTime, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取控制趋势数据 🔖
     * @param {string} statType 统计类型
     * @param {number} [deviceId] 设备ID
     * @param {number} [groupId] 设备分组ID
     * @param {Date} [startTime] 开始时间
     * @param {Date} [endTime] 结束时间
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyControlApi
     */
    public async apiEnergyControlTrendGet(statType: string, deviceId?: number, groupId?: number, startTime?: Date, endTime?: Date, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultListEnergyControlTrendOutput>> {
        return EnergyControlApiFp(this.configuration).apiEnergyControlTrendGet(statType, deviceId, groupId, startTime, endTime, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新控制记录 🔖
     * @param {UpdateEnergyControlInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnergyControlApi
     */
    public async apiEnergyControlUpdatePost(body?: UpdateEnergyControlInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return EnergyControlApiFp(this.configuration).apiEnergyControlUpdatePost(body, options).then((request) => request(this.axios, this.basePath));
    }
}
