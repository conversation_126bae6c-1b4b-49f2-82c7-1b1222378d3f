// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Plugin.MQTT.Core.Entity;
using Admin.NET.Core;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Furion.JsonSerialization;
using Admin.NET.Plugin.MQTT.Core.Models;

namespace Admin.NET.Plugin.MQTT;

/// <summary>
/// 设备事件处理服务
/// </summary>
public class DeviceEventService : MessageHandlerBase<DeviceEventMessage>, ISingleton
{
    private new readonly ILogger<DeviceEventService> _logger;
    private readonly ConcurrentDictionary<string, DeviceInfo> _deviceCache;
    private readonly ConcurrentDictionary<string, DateTime> _lastHeartbeat;
    private readonly Timer _heartbeatCheckTimer;
    private readonly object _lockObject = new object();

    /// <summary>
    /// 设备广播事件
    /// </summary>
    public event EventHandler<DeviceBeaconEventArgs> DeviceBeaconReceived;

    /// <summary>
    /// 照明参数事件
    /// </summary>
    public event EventHandler<DeviceLightingEventArgs> DeviceLightingReceived;

    /// <summary>
    /// 传感器配置事件
    /// </summary>
    public event EventHandler<DeviceSensorEventArgs> DeviceSensorReceived;

    /// <summary>
    /// 能耗信息事件
    /// </summary>
    public event EventHandler<DeviceConsumptionEventArgs> DeviceConsumptionReceived;

    /// <summary>
    /// 心跳消息事件
    /// </summary>
    public event EventHandler<DeviceHeartbeatEventArgs> DeviceHeartbeatReceived;

    /// <summary>
    /// 触发事件
    /// </summary>
    public event EventHandler<DeviceTriggerEventArgs> DeviceTriggerReceived;

    /// <summary>
    /// 设备状态变化事件
    /// </summary>
    public event EventHandler<DeviceStatusChangedEventArgs> DeviceStatusChanged;

    /// <summary>
    /// 设备状态事件
    /// </summary>
    public event EventHandler<DeviceStatusEventArgs> DeviceStatusReceived;

    /// <summary>
    /// 设备离线事件
    /// </summary>
    public event EventHandler<DeviceOfflineEventArgs> DeviceOffline;

    /// <summary>
    /// 构造函数
    /// </summary>
    public DeviceEventService(ILogger<DeviceEventService> logger) : base(logger)
    {
        _logger = logger;
        _deviceCache = new ConcurrentDictionary<string, DeviceInfo>();
        _lastHeartbeat = new ConcurrentDictionary<string, DateTime>();
        
        // 启动心跳检查定时器，每分钟检查一次
        _heartbeatCheckTimer = new Timer(CheckDeviceHeartbeat, null, 
                                       TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
    }

    /// <summary>
    /// 处理设备事件消息
    /// </summary>
    public override async Task HandleAsync(string topic, DeviceEventMessage message, MqttApplicationMessageReceivedEventArgs e)
    {
        // 这个方法现在通过HandleAsync(string, string, MqttApplicationMessageReceivedEventArgs)来处理
        // 因为我们需要从原始JSON解析SysMqttMessage
        var payload = System.Text.Encoding.UTF8.GetString(e.ApplicationMessage.PayloadSegment);
        await HandleAsync(topic, payload, e);
    }

    /// <summary>
    /// 处理原始JSON消息 - 使用SysMqttMessage泛型类解析
    /// </summary>
    public new async Task HandleAsync(string topic, string payload, MqttApplicationMessageReceivedEventArgs e)
    {
        try
        {
            _logger.LogDebug("收到原始消息, 主题: {Topic}, 负载: {Payload}", topic, payload);

            // 首先解析基本的MQTT消息结构以确定事件类型
            var jsonObject = JObject.Parse(payload);
            
            if (!jsonObject.TryGetValue("method", out var methodToken))
            {
                _logger.LogWarning("消息缺少method字段, 主题: {Topic}", topic);
                return;
            }

            var method = methodToken.Value<string>();
            
            // 根据method类型使用相应的泛型类解析消息
            switch (method)
            {
                case "thing.event.beacon.post":
                    await HandleTypedMessageAsync<BeaconEventData>(payload, topic, e);
                    break;
                case "thing.event.setting.post":
                    await HandleTypedMessageAsync<SettingEventData>(payload, topic, e);
                    break;
                case "thing.event.sensor.post":
                    await HandleTypedMessageAsync<SensorEventData>(payload, topic, e);
                    break;
                case "thing.event.consumption.post":
                    await HandleTypedMessageAsync<ConsumptionEventData>(payload, topic, e);
                    break;
                case "thing.event.heartbeat.post":
                    await HandleTypedMessageAsync<HeartbeatEventData>(payload, topic, e);
                    break;
                case "thing.event.trigger.post":
                    await HandleTypedMessageAsync<TriggerEventData>(payload, topic, e);
                    break;
                case "thing.event.dimming.post":
                    await HandleTypedMessageAsync<DimmingEventData>(payload, topic, e);
                    break;
                case "thing.event.network.post":
                    await HandleTypedMessageAsync<NetworkEventData>(payload, topic, e);
                    break;
                case "thing.event.irc.post":
                    await HandleTypedMessageAsync<IrcEventData>(payload, topic, e);
                    break;
                default:
                    _logger.LogWarning("未知的事件类型: {Method}, 主题: {Topic}", method, topic);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理原始消息失败, 主题: {Topic}, 负载: {Payload}", topic, payload);
        }
    }

    /// <summary>
    /// 处理特定类型的MQTT消息
    /// </summary>
    private async Task HandleTypedMessageAsync<T>(string payload, string topic, MqttApplicationMessageReceivedEventArgs e) where T : class
    {
        try
        {
            // 使用项目的JSON序列化提供器，支持JsonPropertyName特性
            var jsonSerializer = App.GetService<IJsonSerializerProvider>();
            var mqttMessage = jsonSerializer.Deserialize<SysMqttMessage<T>>(payload);
            
            if (mqttMessage?._params?.value == null)
            {
                _logger.LogWarning("消息缺少参数数据, 主题: {Topic}", topic);
                return;
            }

            var deviceEventMessage = CreateDeviceEventMessageFromMqtt(mqttMessage, mqttMessage._params.value);
            if (deviceEventMessage == null)
            {
                _logger.LogWarning("无法创建设备事件消息, 主题: {Topic}", topic);
                return;
            }
            
            var deviceKey = $"{deviceEventMessage.Area}_{deviceEventMessage.Address}";

            // 更新设备缓存
            UpdateDeviceCache(deviceKey, deviceEventMessage);

            // 根据事件类型处理消息
            await ProcessEventByTypeAsync(deviceEventMessage, deviceKey);
        }
        catch (Newtonsoft.Json.JsonException ex)
        {
            _logger.LogError(ex, "解析{Type}事件JSON失败, 主题: {Topic}, 消息内容: {Payload}", typeof(T).Name, topic, payload);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理{Type}事件失败, 主题: {Topic}", typeof(T).Name, topic);
        }
    }

    /// <summary>
    /// 根据事件类型处理消息
    /// </summary>
    private async Task ProcessEventByTypeAsync(DeviceEventMessage message, string deviceKey)
    {
        switch (message.EventType)
        {
            case MqttConst.EventTypes.Beacon:
                await HandleBeaconEventAsync(message, deviceKey);
                break;
            case MqttConst.EventTypes.Setting:
                await HandleSettingEventAsync(message, deviceKey);
                break;
            case MqttConst.EventTypes.Sensor:
                await HandleSensorEventAsync(message, deviceKey);
                break;
            case MqttConst.EventTypes.Consumption:
                await HandleConsumptionEventAsync(message, deviceKey);
                break;
            case MqttConst.EventTypes.Heartbeat:
                await HandleHeartbeatEventAsync(message, deviceKey);
                break;
            case MqttConst.EventTypes.Trigger:
                await HandleTriggerEventAsync(message, deviceKey);
                break;
            default:
                _logger.LogWarning("未知的事件类型: {EventType}, 设备: {DeviceKey}", message.EventType, deviceKey);
                break;
        }
    }


    /// <summary>
    /// 从SysMqttMessage创建设备事件消息
    /// </summary>
    private DeviceEventMessage CreateDeviceEventMessageFromMqtt<T>(SysMqttMessage<T> mqttMessage, T data)
    {
        // 从数据中提取区域和地址信息
        string area = "unknown";
        string address = "unknown";
        string deviceName = "unknown";
        
        if (data is BeaconEventData beaconData)
        {
            area = beaconData.area ?? "unknown";
            address = beaconData.number ?? "unknown";
            deviceName = beaconData.device_name ?? "unknown";
        }
        else if (data is SettingEventData settingData)
        {
            area = settingData.area ?? "unknown";
            address = settingData.number ?? "unknown";
            deviceName = settingData.device_name ?? "unknown";
        }
        else if (data is SensorEventData sensorData)
        {
            area = sensorData.area ?? "unknown";
            address = sensorData.number ?? "unknown";
            deviceName = sensorData.device_name ?? "unknown";
        }
        else if (data is ConsumptionEventData consumptionData)
        {
            area = consumptionData.area ?? "unknown";
            address = consumptionData.number ?? "unknown";
            deviceName = consumptionData.device_name ?? "unknown";
        }
        else if (data is HeartbeatEventData heartbeatData)
        {
            area = heartbeatData.area ?? "unknown";
            address = heartbeatData.number ?? "unknown";
            deviceName = heartbeatData.device_name ?? "unknown";
        }
        else if (data is TriggerEventData triggerData)
        {
            area = triggerData.area ?? "unknown";
            address = triggerData.number ?? "unknown";
            deviceName = triggerData.device_name ?? "unknown";
            
            // 设置向后兼容的属性
            triggerData.DeviceId = triggerData.device_name;
            triggerData.TriggerType = triggerData.trigger_type;
            triggerData.Value = triggerData.trigger_value;
        }

        // 处理时间戳：如果是毫秒级时间戳，转换为秒级
        long timestamp = mqttMessage._params?.time ?? DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        
        // 检查时间戳是否为毫秒级（通常大于10位数字）
        if (timestamp > 9999999999) // 大于10位数字，认为是毫秒级
        {
            timestamp = timestamp / 1000; // 转换为秒级
        }
        
        return new DeviceEventMessage
        {
            MessageId = mqttMessage.id ?? Guid.NewGuid().ToString(),
            Timestamp = timestamp,
            DeviceName = deviceName,
            EventType = GetEventTypeFromMethod(mqttMessage.method),
            Data = data,
            Status = "success",
            Area = area,
            Address = address
        };
    }

    /// <summary>
    /// 根据method获取事件类型
    /// </summary>
    private string GetEventTypeFromMethod(string method)
    {
        return method switch
        {
            "thing.event.beacon.post" => MqttConst.EventTypes.Beacon,
            "thing.event.setting.post" => MqttConst.EventTypes.Setting,
            "thing.event.sensor.post" => MqttConst.EventTypes.Sensor,
            "thing.event.consumption.post" => MqttConst.EventTypes.Consumption,
            "thing.event.heartbeat.post" => MqttConst.EventTypes.Heartbeat,
            "thing.event.trigger.post" => MqttConst.EventTypes.Trigger,
            _ => "unknown"
        };
    }

    /// <summary>
    /// 安全地将Unix时间戳转换为DateTime
    /// </summary>
    /// <param name="timestamp">Unix时间戳（秒）</param>
    /// <returns>转换后的DateTime，如果转换失败则返回当前时间</returns>
    private DateTime SafeConvertFromUnixTimeSeconds(long timestamp)
    {
        try
        {
            // 检查时间戳是否在有效范围内
            if (timestamp < -62135596800 || timestamp > 253402300799)
            {
                _logger.LogWarning("时间戳超出有效范围: {Timestamp}，使用当前时间", timestamp);
                return DateTime.UtcNow;
            }
            
            return DateTimeOffset.FromUnixTimeSeconds(timestamp).DateTime;
        }
        catch (ArgumentOutOfRangeException ex)
        {
            _logger.LogError(ex, "时间戳转换失败: {Timestamp}，使用当前时间", timestamp);
            return DateTime.UtcNow;
        }
    }



    /// <summary>
    /// 处理设备广播事件
    /// </summary>
    private async Task HandleBeaconEventAsync(DeviceEventMessage message, string deviceKey)
    {
        try
        {
            if (message.Data is not BeaconEventData beaconData)
            {
                _logger.LogWarning("Beacon事件数据类型不匹配, 设备: {DeviceKey}", deviceKey);
                return;
            }

            var eventArgs = new DeviceBeaconEventArgs
            {
                DeviceKey = deviceKey,
                Area = message.Area,
                Address = message.Address,
                BeaconData = beaconData,
                Timestamp = SafeConvertFromUnixTimeSeconds(message.Timestamp),
                MessageId = message.MessageId
            };

            DeviceBeaconReceived?.Invoke(this, eventArgs);
            
            _logger.LogInformation("收到设备广播, 设备: {DeviceKey}, 类型: {Type}, 版本: {Version}", 
                                 deviceKey, beaconData.type, beaconData.version);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理设备广播事件失败, 设备: {DeviceKey}", deviceKey);
        }
    }

    /// <summary>
    /// 处理照明参数事件
    /// </summary>
    private async Task HandleSettingEventAsync(DeviceEventMessage message, string deviceKey)
    {
        try
        {
            if (message.Data is not SettingEventData settingData)
            {
                _logger.LogWarning("Setting事件数据类型不匹配, 设备: {DeviceKey}", deviceKey);
                return;
            }

            var eventArgs = new DeviceLightingEventArgs
            {
                DeviceKey = deviceKey,
                Area = message.Area,
                Address = message.Address,
                LightingData = settingData,
                Timestamp = SafeConvertFromUnixTimeSeconds(message.Timestamp),
                MessageId = message.MessageId
            };

            DeviceLightingReceived?.Invoke(this, eventArgs);
            
            _logger.LogInformation("收到照明参数, 设备: {DeviceKey}, 亮度: {Brightness}%, 色温: {ColorTemp}K", 
                                 deviceKey, settingData.Brightness, settingData.ColorTemperature);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理照明参数事件失败, 设备: {DeviceKey}", deviceKey);
        }
    }

    /// <summary>
    /// 处理传感器配置事件
    /// </summary>
    private async Task HandleSensorEventAsync(DeviceEventMessage message, string deviceKey)
    {
        try
        {
            if (message.Data is not SensorEventData sensorData)
            {
                _logger.LogWarning("Sensor事件数据类型不匹配, 设备: {DeviceKey}", deviceKey);
                return;
            }

            var eventArgs = new DeviceSensorEventArgs
            {
                DeviceKey = deviceKey,
                Area = message.Area,
                Address = message.Address,
                SensorData = sensorData,
                Timestamp = SafeConvertFromUnixTimeSeconds(message.Timestamp),
                MessageId = message.MessageId
            };

            DeviceSensorReceived?.Invoke(this, eventArgs);
            
            _logger.LogInformation("收到传感器数据, 设备: {DeviceKey}, 温度: {Temperature}°C, 湿度: {Humidity}%", 
                                 deviceKey, sensorData.Temperature, sensorData.Humidity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理传感器配置事件失败, 设备: {DeviceKey}", deviceKey);
        }
    }

    /// <summary>
    /// 处理能耗信息事件
    /// </summary>
    private async Task HandleConsumptionEventAsync(DeviceEventMessage message, string deviceKey)
    {
        try
        {
            if (message.Data is not ConsumptionEventData consumptionData)
            {
                _logger.LogWarning("Consumption事件数据类型不匹配, 设备: {DeviceKey}", deviceKey);
                return;
            }

            var eventArgs = new DeviceConsumptionEventArgs
            {
                DeviceKey = deviceKey,
                Area = message.Area,
                Address = message.Address,
                ConsumptionData = consumptionData,
                Timestamp = SafeConvertFromUnixTimeSeconds(message.Timestamp),
                MessageId = message.MessageId
            };

            DeviceConsumptionReceived?.Invoke(this, eventArgs);
            
            _logger.LogInformation("收到能耗信息, 设备: {DeviceKey}, 功率: {Power}W, 电压: {Voltage}V, 电流: {Current}A", 
                                 deviceKey, consumptionData.Power, consumptionData.Voltage, consumptionData.Current);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理能耗信息事件失败, 设备: {DeviceKey}", deviceKey);
        }
    }

    /// <summary>
    /// 处理心跳消息事件
    /// </summary>
    private async Task HandleHeartbeatEventAsync(DeviceEventMessage message, string deviceKey)
    {
        try
        {
            if (message.Data is not HeartbeatEventData heartbeatData)
            {
                _logger.LogWarning("Heartbeat事件数据类型不匹配, 设备: {DeviceKey}", deviceKey);
                return;
            }

            // 更新心跳时间
            _lastHeartbeat.AddOrUpdate(deviceKey, DateTime.UtcNow, (key, oldValue) => DateTime.UtcNow);

            var eventArgs = new DeviceHeartbeatEventArgs
            {
                DeviceKey = deviceKey,
                Area = message.Area,
                Address = message.Address,
                HeartbeatData = heartbeatData,
                Timestamp = SafeConvertFromUnixTimeSeconds(message.Timestamp),
                MessageId = message.MessageId
            };

            DeviceHeartbeatReceived?.Invoke(this, eventArgs);
            
            _logger.LogDebug("收到心跳消息, 设备: {DeviceKey}, 状态: {Status}, 信号强度: {SignalStrength}", 
                           deviceKey, heartbeatData.Status, heartbeatData.SignalStrength);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理心跳消息事件失败, 设备: {DeviceKey}", deviceKey);
        }
    }

    /// <summary>
    /// 处理触发事件
    /// </summary>
    private async Task HandleTriggerEventAsync(DeviceEventMessage message, string deviceKey)
    {
        try
        {
            if (message.Data is not TriggerEventData triggerData)
            {
                _logger.LogWarning("Trigger事件数据类型不匹配, 设备: {DeviceKey}", deviceKey);
                return;
            }

            var eventArgs = new DeviceTriggerEventArgs
            {
                DeviceKey = deviceKey,
                Area = message.Area,
                Address = message.Address,
                TriggerData = triggerData,
                Timestamp = SafeConvertFromUnixTimeSeconds(message.Timestamp),
                MessageId = message.MessageId
            };

            DeviceTriggerReceived?.Invoke(this, eventArgs);
            
            _logger.LogInformation("收到触发事件, 设备: {DeviceKey}, 触发器: {TriggerType}, 值: {Value}", 
                                 deviceKey, triggerData.TriggerType, triggerData.Value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理触发事件失败, 设备: {DeviceKey}", deviceKey);
        }
    }

    /// <summary>
    /// 处理状态事件
    /// </summary>
    private async Task HandleStatusEventAsync(DeviceEventMessage message, string deviceKey)
    {
        try
        {
            if (message.Data is not StatusEventData statusData)
            {
                _logger.LogWarning("Status事件数据类型不匹配, 设备: {DeviceKey}", deviceKey);
                return;
            }

            var eventArgs = new DeviceStatusEventArgs
            {
                DeviceKey = deviceKey,
                Area = message.Area,
                Address = message.Address,
                StatusData = statusData,
                Timestamp = DateTimeOffset.FromUnixTimeSeconds(message.Timestamp).DateTime,
                MessageId = message.MessageId
            };

            DeviceStatusReceived?.Invoke(this, eventArgs);
            
            _logger.LogInformation("收到状态事件, 设备: {DeviceKey}, 状态: {Status}", 
                                 deviceKey, statusData.Status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理状态事件失败, 设备: {DeviceKey}", deviceKey);
        }
    }

    /// <summary>
    /// 处理告警事件
    /// </summary>
    private async Task HandleAlarmEventAsync(DeviceEventMessage message, string deviceKey)
    {
        try
        {
            if (message.Data is not AlarmEventData alarmData)
            {
                _logger.LogWarning("Alarm事件数据类型不匹配, 设备: {DeviceKey}", deviceKey);
                return;
            }

            _logger.LogWarning("收到设备告警, 设备: {DeviceKey}, 告警类型: {AlarmType}, 级别: {Level}", 
                             deviceKey, alarmData.AlarmType, alarmData.Level);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理告警事件失败, 设备: {DeviceKey}", deviceKey);
        }
    }

    /// <summary>
    /// 处理配置事件
    /// </summary>
    private async Task HandleConfigEventAsync(DeviceEventMessage message, string deviceKey)
    {
        try
        {
            if (message.Data is not ConfigEventData configData)
            {
                _logger.LogWarning("Config事件数据类型不匹配, 设备: {DeviceKey}", deviceKey);
                return;
            }

            _logger.LogInformation("收到配置更新, 设备: {DeviceKey}, 配置类型: {ConfigType}", 
                                 deviceKey, configData.ConfigType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理配置事件失败, 设备: {DeviceKey}", deviceKey);
        }
    }

    /// <summary>
    /// 处理升级事件
    /// </summary>
    private void HandleUpgradeEvent(DeviceEventMessage message, string deviceKey)
    {
        try
        {
            if (message.Data is not UpgradeEventData upgradeData)
            {
                _logger.LogWarning("Upgrade事件数据类型不匹配, 设备: {DeviceKey}", deviceKey);
                return;
            }

            _logger.LogInformation("收到升级信息, 设备: {DeviceKey}, 版本: {Version}, 状态: {Status}", 
                                 deviceKey, upgradeData.Version, upgradeData.Status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理升级事件失败, 设备: {DeviceKey}", deviceKey);
        }
    }

    /// <summary>
    /// 处理日志事件
    /// </summary>
    private void HandleLogEvent(DeviceEventMessage message, string deviceKey)
    {
        try
        {
            if (message.Data is not LogEventData logData)
            {
                _logger.LogWarning("Log事件数据类型不匹配, 设备: {DeviceKey}", deviceKey);
                return;
            }

            _logger.LogDebug("收到设备日志, 设备: {DeviceKey}, 级别: {Level}, 消息: {Message}", 
                           deviceKey, logData.Level, logData.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理日志事件失败, 设备: {DeviceKey}", deviceKey);
        }
    }

    /// <summary>
    /// 更新设备缓存
    /// </summary>
    private void UpdateDeviceCache(string deviceKey, DeviceEventMessage message)
    {
        lock (_lockObject)
        {
            _deviceCache.AddOrUpdate(deviceKey, 
                new DeviceInfo
                {
                    DeviceKey = deviceKey,
                    Area = message.Area,
                    Address = message.Address,
                    ProductKey = "", // 暂时设为空字符串
                    DeviceName = message.DeviceName,
                    LastSeen = DateTimeOffset.FromUnixTimeMilliseconds(message.Timestamp).DateTime,
                    IsOnline = true
                },
                (key, existing) =>
                {
                    existing.LastSeen = DateTimeOffset.FromUnixTimeMilliseconds(message.Timestamp).DateTime;
                    existing.IsOnline = true;
                    return existing;
                });
        }
    }

    /// <summary>
    /// 检查设备心跳
    /// </summary>
    private void CheckDeviceHeartbeat(object state)
    {
        try
        {
            var now = DateTime.UtcNow;
            var offlineThreshold = TimeSpan.FromMinutes(5); // 5分钟无心跳视为离线

            var offlineDevices = _lastHeartbeat
                .Where(kvp => (now - kvp.Value) > offlineThreshold)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var deviceKey in offlineDevices)
            {
                if (_deviceCache.TryGetValue(deviceKey, out var deviceInfo) && deviceInfo.IsOnline)
                {
                    deviceInfo.IsOnline = false;
                    
                    var eventArgs = new DeviceOfflineEventArgs
                    {
                        DeviceKey = deviceKey,
                        Area = deviceInfo.Area,
                        Address = deviceInfo.Address,
                        LastSeen = deviceInfo.LastSeen,
                        OfflineTime = now
                    };

                    DeviceOffline?.Invoke(this, eventArgs);
                    
                    _logger.LogWarning("设备离线, 设备: {DeviceKey}, 最后心跳: {LastHeartbeat}", 
                                     deviceKey, _lastHeartbeat[deviceKey]);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查设备心跳失败");
        }
    }

    /// <summary>
    /// 获取设备列表
    /// </summary>
    public List<DeviceInfo> GetDevices()
    {
        return _deviceCache.Values.ToList();
    }

    /// <summary>
    /// 获取在线设备列表
    /// </summary>
    public List<DeviceInfo> GetOnlineDevices()
    {
        return _deviceCache.Values.Where(d => d.IsOnline).ToList();
    }

    /// <summary>
    /// 获取设备信息
    /// </summary>
    public DeviceInfo GetDevice(string deviceKey)
    {
        _deviceCache.TryGetValue(deviceKey, out var device);
        return device;
    }

    /// <summary>
    /// 获取设备统计信息
    /// </summary>
    public DeviceStatistics GetDeviceStatistics()
    {
        var devices = _deviceCache.Values.ToList();
        return new DeviceStatistics
        {
            TotalDevices = devices.Count,
            OnlineDevices = devices.Count(d => d.IsOnline),
            OfflineDevices = devices.Count(d => !d.IsOnline),
            LastUpdateTime = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        _heartbeatCheckTimer?.Dispose();
    }
}

#region 事件参数类

/// <summary>
/// 设备广播事件参数
/// </summary>
public class DeviceBeaconEventArgs : EventArgs
{
    public string DeviceKey { get; set; }
    public string Area { get; set; }
    public string Address { get; set; }
    public BeaconEventData BeaconData { get; set; }
    public DateTime Timestamp { get; set; }
    public string MessageId { get; set; }
}

/// <summary>
/// 设备照明事件参数
/// </summary>
public class DeviceLightingEventArgs : EventArgs
{
    public string DeviceKey { get; set; }
    public string Area { get; set; }
    public string Address { get; set; }
    public SettingEventData LightingData { get; set; }
    public DateTime Timestamp { get; set; }
    public string MessageId { get; set; }
}

/// <summary>
/// 设备传感器事件参数
/// </summary>
public class DeviceSensorEventArgs : EventArgs
{
    public string DeviceKey { get; set; }
    public string Area { get; set; }
    public string Address { get; set; }
    public SensorEventData SensorData { get; set; }
    public DateTime Timestamp { get; set; }
    public string MessageId { get; set; }
}

/// <summary>
/// 设备能耗事件参数
/// </summary>
public class DeviceConsumptionEventArgs : EventArgs
{
    public string DeviceKey { get; set; }
    public string Area { get; set; }
    public string Address { get; set; }
    public ConsumptionEventData ConsumptionData { get; set; }
    public DateTime Timestamp { get; set; }
    public string MessageId { get; set; }
}

/// <summary>
/// 设备心跳事件参数
/// </summary>
public class DeviceHeartbeatEventArgs : EventArgs
{
    public string DeviceKey { get; set; }
    public string Area { get; set; }
    public string Address { get; set; }
    public HeartbeatEventData HeartbeatData { get; set; }
    public DateTime Timestamp { get; set; }
    public string MessageId { get; set; }
}

/// <summary>
/// 设备触发事件参数
/// </summary>
public class DeviceTriggerEventArgs : EventArgs
{
    public string DeviceKey { get; set; }
    public string Area { get; set; }
    public string Address { get; set; }
    public TriggerEventData TriggerData { get; set; }
    public DateTime Timestamp { get; set; }
    public string MessageId { get; set; }
}

/// <summary>
/// 设备状态变化事件参数
/// </summary>
public class DeviceStatusChangedEventArgs : EventArgs
{
    public string DeviceKey { get; set; }
    public string Area { get; set; }
    public string Address { get; set; }
    public Dictionary<string, object> StatusData { get; set; }
    public DateTime Timestamp { get; set; }
    public string MessageId { get; set; }
}

/// <summary>
/// 设备状态事件参数
/// </summary>
public class DeviceStatusEventArgs : EventArgs
{
    public string DeviceKey { get; set; }
    public string Area { get; set; }
    public string Address { get; set; }
    public StatusEventData StatusData { get; set; }
    public DateTime Timestamp { get; set; }
    public string MessageId { get; set; }
}

/// <summary>
/// 设备离线事件参数
/// </summary>
public class DeviceOfflineEventArgs : EventArgs
{
    public string DeviceKey { get; set; }
    public string Area { get; set; }
    public string Address { get; set; }
    public DateTime LastSeen { get; set; }
    public DateTime OfflineTime { get; set; }
}

#endregion

#region 数据模型

/// <summary>
/// 设备信息
/// </summary>
public class DeviceInfo
{
    /// <summary>
    /// 设备键值
    /// </summary>
    public string DeviceKey { get; set; }

    /// <summary>
    /// 区域
    /// </summary>
    public string Area { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string Address { get; set; }

    /// <summary>
    /// 产品键
    /// </summary>
    public string ProductKey { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string DeviceName { get; set; }

    /// <summary>
    /// 最后见到时间
    /// </summary>
    public DateTime LastSeen { get; set; }

    /// <summary>
    /// 是否在线
    /// </summary>
    public bool IsOnline { get; set; }
}

/// <summary>
/// 设备统计信息
/// </summary>
public class DeviceStatistics
{
    /// <summary>
    /// 总设备数
    /// </summary>
    public int TotalDevices { get; set; }

    /// <summary>
    /// 在线设备数
    /// </summary>
    public int OnlineDevices { get; set; }

    /// <summary>
    /// 离线设备数
    /// </summary>
    public int OfflineDevices { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdateTime { get; set; }
}

#endregion