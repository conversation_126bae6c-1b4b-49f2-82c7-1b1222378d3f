/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 能耗记录输出参数
 * @export
 * @interface EnergyConsumptionOutput
 */
export interface EnergyConsumptionOutput {
    /**
     * 主键ID
     * @type {number}
     * @memberof EnergyConsumptionOutput
     */
    id?: number;
    /**
     * 设备ID
     * @type {number}
     * @memberof EnergyConsumptionOutput
     */
    deviceId?: number;
    /**
     * 设备编码
     * @type {string}
     * @memberof EnergyConsumptionOutput
     */
    deviceCode?: string | null;
    /**
     * 设备名称
     * @type {string}
     * @memberof EnergyConsumptionOutput
     */
    deviceName?: string | null;
    /**
     * 设备位置
     * @type {string}
     * @memberof EnergyConsumptionOutput
     */
    deviceLocation?: string | null;
    /**
     * 记录时间
     * @type {Date}
     * @memberof EnergyConsumptionOutput
     */
    recordTime?: Date;
    /**
     * 电压(V)
     * @type {number}
     * @memberof EnergyConsumptionOutput
     */
    voltage?: number | null;
    /**
     * 电流(A)
     * @type {number}
     * @memberof EnergyConsumptionOutput
     */
    current?: number | null;
    /**
     * 功率(W)
     * @type {number}
     * @memberof EnergyConsumptionOutput
     */
    power?: number | null;
    /**
     * 功率因数
     * @type {number}
     * @memberof EnergyConsumptionOutput
     */
    powerFactor?: number | null;
    /**
     * 累计用电量(kWh)
     * @type {number}
     * @memberof EnergyConsumptionOutput
     */
    totalEnergy?: number | null;
    /**
     * 当日用电量(kWh)
     * @type {number}
     * @memberof EnergyConsumptionOutput
     */
    dailyEnergy?: number | null;
    /**
     * 能耗值
     * @type {number}
     * @memberof EnergyConsumptionOutput
     */
    energyConsumption?: number | null;
    /**
     * 温度(℃)
     * @type {number}
     * @memberof EnergyConsumptionOutput
     */
    temperature?: number | null;
    /**
     * 湿度(%)
     * @type {number}
     * @memberof EnergyConsumptionOutput
     */
    humidity?: number | null;
    /**
     * 创建时间
     * @type {Date}
     * @memberof EnergyConsumptionOutput
     */
    createTime?: Date;
    /**
     * 频率(Hz)
     * @type {number}
     * @memberof EnergyConsumptionOutput
     */
    frequency?: number | null;
    /**
     * 租户ID
     * @type {number}
     * @memberof EnergyConsumptionOutput
     */
    tenantId?: number | null;
}
