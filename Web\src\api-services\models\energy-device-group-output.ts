/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { EnergyDeviceGroupOutput } from './energy-device-group-output';
/**
 * 设备分组输出参数
 * @export
 * @interface EnergyDeviceGroupOutput
 */
export interface EnergyDeviceGroupOutput {
    /**
     * 主键ID
     * @type {number}
     * @memberof EnergyDeviceGroupOutput
     */
    id?: number;
    /**
     * 分组编码
     * @type {string}
     * @memberof EnergyDeviceGroupOutput
     */
    groupCode?: string | null;
    /**
     * 分组名称
     * @type {string}
     * @memberof EnergyDeviceGroupOutput
     */
    groupName?: string | null;
    /**
     * 分组类型
     * @type {number}
     * @memberof EnergyDeviceGroupOutput
     */
    groupType?: number;
    /**
     * 父级分组ID
     * @type {number}
     * @memberof EnergyDeviceGroupOutput
     */
    parentId?: number | null;
    /**
     * 位置
     * @type {string}
     * @memberof EnergyDeviceGroupOutput
     */
    location?: string | null;
    /**
     * 父级分组名称
     * @type {string}
     * @memberof EnergyDeviceGroupOutput
     */
    parentName?: string | null;
    /**
     * 分组描述
     * @type {string}
     * @memberof EnergyDeviceGroupOutput
     */
    description?: string | null;
    /**
     * 排序
     * @type {number}
     * @memberof EnergyDeviceGroupOutput
     */
    sort?: number;
    /**
     * 排序号
     * @type {number}
     * @memberof EnergyDeviceGroupOutput
     */
    orderNo?: number;
    /**
     * 租户ID
     * @type {number}
     * @memberof EnergyDeviceGroupOutput
     */
    tenantId?: number | null;
    /**
     * 状态
     * @type {number}
     * @memberof EnergyDeviceGroupOutput
     */
    status?: number;
    /**
     * 创建时间
     * @type {Date}
     * @memberof EnergyDeviceGroupOutput
     */
    createTime?: Date;
    /**
     * 更新时间
     * @type {Date}
     * @memberof EnergyDeviceGroupOutput
     */
    updateTime?: Date | null;
    /**
     * 子分组列表
     * @type {Array<EnergyDeviceGroupOutput>}
     * @memberof EnergyDeviceGroupOutput
     */
    children?: Array<EnergyDeviceGroupOutput> | null;
}
