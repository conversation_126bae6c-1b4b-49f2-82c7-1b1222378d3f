# 字体文件说明

## iconfont 字体文件

本目录包含项目所需的图标字体文件：

- `iconfont.ttf` - TrueType字体文件，兼容性最好
- `iconfont.woff` - Web字体文件，压缩率好
- `iconfont.woff2` - 新一代Web字体文件，压缩率更好
- `iconfont.css` - 字体样式定义文件

## 使用方法

1. 在页面中引入CSS文件：
```css
@import url('/static/fonts/iconfont.css');
```

2. 使用图标类：
```html
<text class="iconfont icon-home"></text>
<text class="iconfont icon-device iconfont-lg iconfont-primary"></text>
```

## 图标列表

### 导航图标
- `icon-home` - 首页
- `icon-device` - 设备
- `icon-lighting` - 照明
- `icon-energy` - 能耗
- `icon-user` - 用户

### 功能图标
- `icon-add` - 添加
- `icon-edit` - 编辑
- `icon-delete` - 删除
- `icon-search` - 搜索
- `icon-filter` - 筛选
- `icon-settings` - 设置

### 设备类型图标
- `icon-device-led` - LED灯
- `icon-device-sensor` - 传感器
- `icon-device-controller` - 控制器

### 状态图标
- `icon-status-online` - 在线
- `icon-status-offline` - 离线
- `icon-status-warning` - 警告

### 系统图标
- `icon-arrow-*` - 箭头方向
- `icon-close` - 关闭
- `icon-check` - 确认
- `icon-info` - 信息
- `icon-refresh` - 刷新
- `icon-more` - 更多

## 注意事项

1. 字体文件需要使用专业的图标字体生成工具（如 iconfont.cn）生成
2. 确保字体文件路径正确，支持跨平台访问
3. 建议使用相对路径引用字体文件
4. 在uni-app中使用时，注意平台兼容性