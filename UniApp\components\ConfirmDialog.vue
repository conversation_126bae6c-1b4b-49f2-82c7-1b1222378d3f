<template>
  <view class="confirm-dialog" v-if="visible" @click="handleMaskClick">
    <!-- 遮罩层 -->
    <view class="dialog-mask"></view>
    
    <!-- 对话框容器 -->
    <view 
      class="dialog-container"
      :class="[
        `type-${type}`,
        `size-${size}`,
        {
          'no-title': !title && !$slots.title,
          'no-content': !content && !$slots.default,
          'custom-buttons': $slots.buttons
        },
        customClass
      ]"
      @click.stop
    >
      <!-- 图标 -->
      <view class="dialog-icon" v-if="showIcon">
        <text class="icon-text" v-if="iconType === 'text'">{{ iconContent }}</text>
        <view class="icon-image" v-else-if="iconType === 'image'">
          <image :src="iconContent" mode="aspectFit" />
        </view>
        <view class="icon-default" v-else>
          <text class="default-icon">{{ getDefaultIcon() }}</text>
        </view>
      </view>
      
      <!-- 标题 -->
      <view class="dialog-title" v-if="title || $slots.title">
        <slot name="title">
          <text class="title-text">{{ title }}</text>
        </slot>
      </view>
      
      <!-- 内容 -->
      <view class="dialog-content" v-if="content || $slots.default">
        <slot>
          <text class="content-text" :class="{ 'rich-text': richText }">{{ content }}</text>
        </slot>
      </view>
      
      <!-- 输入框 -->
      <view class="dialog-input" v-if="showInput">
        <input 
          class="input-field"
          :type="inputType"
          :placeholder="inputPlaceholder"
          :value="inputValue"
          @input="handleInputChange"
          :maxlength="inputMaxLength"
          :focus="inputFocus"
        />
        <view class="input-error" v-if="inputError">
          <text class="error-text">{{ inputError }}</text>
        </view>
      </view>
      
      <!-- 按钮组 -->
      <view class="dialog-buttons">
        <slot name="buttons">
          <!-- 取消按钮 -->
          <button 
            class="dialog-btn cancel-btn"
            :class="{ 'single-btn': !showConfirm }"
            v-if="showCancel"
            @click="handleCancel"
            :disabled="loading"
          >
            {{ cancelText }}
          </button>
          
          <!-- 确认按钮 -->
          <button 
            class="dialog-btn confirm-btn"
            :class="{ 
              'single-btn': !showCancel,
              'loading': loading,
              [`btn-${type}`]: true
            }"
            v-if="showConfirm"
            @click="handleConfirm"
            :disabled="loading || (showInput && !isInputValid)"
          >
            <view class="btn-content">
              <view class="btn-loading" v-if="loading">
                <text class="loading-icon">⟳</text>
              </view>
              <text class="btn-text">{{ confirmText }}</text>
            </view>
          </button>
        </slot>
      </view>
      
      <!-- 关闭按钮 -->
      <view class="dialog-close" v-if="showClose" @click="handleClose">
        <text class="close-icon">✕</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed, ref, watch, nextTick } from 'vue'

// Props定义
const props = defineProps({
  // 是否显示
  visible: {
    type: Boolean,
    default: false
  },
  // 对话框类型
  type: {
    type: String,
    default: 'default',
    validator: (value) => {
      return ['default', 'info', 'success', 'warning', 'danger', 'confirm'].includes(value)
    }
  },
  // 尺寸
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  // 标题
  title: {
    type: String,
    default: ''
  },
  // 内容
  content: {
    type: String,
    default: ''
  },
  // 是否富文本
  richText: {
    type: Boolean,
    default: false
  },
  // 是否显示图标
  showIcon: {
    type: Boolean,
    default: true
  },
  // 图标类型
  iconType: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'text', 'image'].includes(value)
  },
  // 图标内容
  iconContent: {
    type: String,
    default: ''
  },
  // 是否显示输入框
  showInput: {
    type: Boolean,
    default: false
  },
  // 输入框类型
  inputType: {
    type: String,
    default: 'text'
  },
  // 输入框占位符
  inputPlaceholder: {
    type: String,
    default: '请输入内容'
  },
  // 输入框值
  inputValue: {
    type: String,
    default: ''
  },
  // 输入框最大长度
  inputMaxLength: {
    type: Number,
    default: 100
  },
  // 输入框自动聚焦
  inputFocus: {
    type: Boolean,
    default: true
  },
  // 输入验证规则
  inputRules: {
    type: Array,
    default: () => []
  },
  // 是否显示取消按钮
  showCancel: {
    type: Boolean,
    default: true
  },
  // 是否显示确认按钮
  showConfirm: {
    type: Boolean,
    default: true
  },
  // 取消按钮文本
  cancelText: {
    type: String,
    default: '取消'
  },
  // 确认按钮文本
  confirmText: {
    type: String,
    default: '确定'
  },
  // 是否显示关闭按钮
  showClose: {
    type: Boolean,
    default: false
  },
  // 点击遮罩是否关闭
  maskClosable: {
    type: Boolean,
    default: true
  },
  // 是否加载中
  loading: {
    type: Boolean,
    default: false
  },
  // 自定义样式类
  customClass: {
    type: [String, Array, Object],
    default: ''
  },
  // 是否阻止滚动穿透
  preventScroll: {
    type: Boolean,
    default: true
  }
})

// Events定义
const emit = defineEmits([
  'update:visible',
  'update:inputValue',
  'confirm',
  'cancel',
  'close',
  'input-change',
  'show',
  'hide'
])

// 响应式数据
const inputError = ref('')
const originalBodyOverflow = ref('')

// 计算属性
// 验证输入并更新错误信息
const validateInput = () => {
  if (!props.showInput) return true
  if (props.inputRules.length === 0) {
    inputError.value = ''
    return true
  }
  
  for (const rule of props.inputRules) {
    if (typeof rule === 'function') {
      const result = rule(props.inputValue)
      if (result !== true) {
        inputError.value = result || '输入不符合要求'
        return false
      }
    } else if (rule.required && !props.inputValue.trim()) {
      inputError.value = rule.message || '此项为必填项'
      return false
    } else if (rule.pattern && !rule.pattern.test(props.inputValue)) {
      inputError.value = rule.message || '格式不正确'
      return false
    } else if (rule.min && props.inputValue.length < rule.min) {
      inputError.value = rule.message || `最少输入${rule.min}个字符`
      return false
    } else if (rule.max && props.inputValue.length > rule.max) {
      inputError.value = rule.message || `最多输入${rule.max}个字符`
      return false
    }
  }
  
  inputError.value = ''
  return true
}

// 计算属性：输入是否有效（不包含副作用）
const isInputValid = computed(() => {
  if (!props.showInput) return true
  if (props.inputRules.length === 0) return true
  
  for (const rule of props.inputRules) {
    if (typeof rule === 'function') {
      const result = rule(props.inputValue)
      if (result !== true) {
        return false
      }
    } else if (rule.required && !props.inputValue.trim()) {
      return false
    } else if (rule.pattern && !rule.pattern.test(props.inputValue)) {
      return false
    } else if (rule.min && props.inputValue.length < rule.min) {
      return false
    } else if (rule.max && props.inputValue.length > rule.max) {
      return false
    }
  }
  
  return true
})

// 方法
const getDefaultIcon = () => {
  const iconMap = {
    info: 'ℹ️',
    success: '✅',
    warning: '⚠️',
    danger: '❌',
    confirm: '❓',
    default: '💬'
  }
  return iconMap[props.type] || iconMap.default
}

const handleMaskClick = () => {
  if (props.maskClosable && !props.loading) {
    handleClose()
  }
}

const handleInputChange = (event) => {
  const value = event.detail.value
  emit('update:inputValue', value)
  emit('input-change', value)
  
  // 重新验证
  nextTick(() => {
    validateInput()
  })
}

const handleConfirm = async () => {
  if (props.loading) return
  
  if (props.showInput && !isInputValid.value) {
    return
  }
  
  const result = {
    type: 'confirm',
    inputValue: props.showInput ? props.inputValue : undefined
  }
  
  emit('confirm', result)
}

const handleCancel = () => {
  if (props.loading) return
  
  emit('cancel', { type: 'cancel' })
  handleClose()
}

const handleClose = () => {
  if (props.loading) return
  
  emit('update:visible', false)
  emit('close', { type: 'close' })
}

const show = () => {
  if (props.preventScroll) {
    // 阻止背景滚动
    originalBodyOverflow.value = document.body.style.overflow
    document.body.style.overflow = 'hidden'
  }
  
  emit('show')
}

const hide = () => {
  if (props.preventScroll && originalBodyOverflow.value !== undefined) {
    // 恢复背景滚动
    document.body.style.overflow = originalBodyOverflow.value
  }
  
  // 清空输入错误
  inputError.value = ''
  
  emit('hide')
}

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      show()
    } else {
      hide()
    }
  },
  { immediate: true }
)

// 监听输入值变化
watch(
  () => props.inputValue,
  () => {
    if (props.showInput) {
      nextTick(() => {
        validateInput()
      })
    }
  }
)

// 暴露方法
defineExpose({
  show,
  hide,
  handleConfirm,
  handleCancel,
  handleClose
})
</script>

<style lang="scss" scoped>
.confirm-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 40rpx;
}

.dialog-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
}

.dialog-container {
  position: relative;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  max-width: 90vw;
  max-height: 80vh;
  overflow: hidden;
  animation: dialog-show 0.3s ease-out;
  
  &.size-small {
    width: 480rpx;
    min-width: 320rpx;
  }
  
  &.size-medium {
    width: 560rpx;
    min-width: 400rpx;
  }
  
  &.size-large {
    width: 640rpx;
    min-width: 480rpx;
  }
}

.dialog-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx 32rpx 16rpx;
  
  .icon-text,
  .default-icon {
    font-size: 48rpx;
    line-height: 1;
  }
  
  .icon-image {
    width: 64rpx;
    height: 64rpx;
    
    image {
      width: 100%;
      height: 100%;
    }
  }
}

.dialog-title {
  padding: 16rpx 32rpx;
  text-align: center;
  
  .title-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #2c3e50;
    line-height: 1.4;
  }
  
  .no-icon & {
    padding-top: 32rpx;
  }
}

.dialog-content {
  padding: 16rpx 32rpx 24rpx;
  text-align: center;
  
  .content-text {
    font-size: 28rpx;
    color: #7f8c8d;
    line-height: 1.5;
    
    &.rich-text {
      text-align: left;
    }
  }
  
  .no-title & {
    padding-top: 24rpx;
  }
}

.dialog-input {
  padding: 0 32rpx 24rpx;
  
  .input-field {
    width: 100%;
    height: 80rpx;
    padding: 0 16rpx;
    border: 2rpx solid #e9ecef;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: #2c3e50;
    background: #ffffff;
    transition: border-color 0.2s ease;
    
    &:focus {
      border-color: #007AFF;
      outline: none;
    }
    
    &::placeholder {
      color: #95a5a6;
    }
  }
  
  .input-error {
    margin-top: 8rpx;
    
    .error-text {
      font-size: 22rpx;
      color: #e74c3c;
      line-height: 1.2;
    }
  }
}

.dialog-buttons {
  display: flex;
  border-top: 2rpx solid #f1f2f6;
  
  .dialog-btn {
    flex: 1;
    height: 88rpx;
    border: none;
    background: transparent;
    font-size: 28rpx;
    transition: all 0.2s ease;
    position: relative;
    
    &:active {
      background: rgba(0, 0, 0, 0.05);
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    
    &.single-btn {
      border-radius: 0 0 16rpx 16rpx;
    }
    
    &.cancel-btn {
      color: #7f8c8d;
      border-right: 2rpx solid #f1f2f6;
      border-radius: 0 0 0 16rpx;
      
      &.single-btn {
        border-right: none;
      }
    }
    
    &.confirm-btn {
      font-weight: 600;
      border-radius: 0 0 16rpx 0;
      
      &.btn-default {
        color: #007AFF;
      }
      
      &.btn-info {
        color: #3498db;
      }
      
      &.btn-success {
        color: #27ae60;
      }
      
      &.btn-warning {
        color: #f39c12;
      }
      
      &.btn-danger {
        color: #e74c3c;
      }
      
      &.btn-confirm {
        color: #007AFF;
      }
      
      &.loading {
        .btn-text {
          opacity: 0.7;
        }
      }
    }
  }
  
  .btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
  }
  
  .btn-loading {
    .loading-icon {
      font-size: 24rpx;
      animation: spin 1s linear infinite;
    }
  }
}

.dialog-close {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  
  &:active {
    background: rgba(0, 0, 0, 0.1);
    transform: scale(0.9);
  }
  
  .close-icon {
    font-size: 24rpx;
    color: #95a5a6;
    line-height: 1;
  }
}

// 类型样式
.type-info {
  .dialog-icon {
    color: #3498db;
  }
}

.type-success {
  .dialog-icon {
    color: #27ae60;
  }
}

.type-warning {
  .dialog-icon {
    color: #f39c12;
  }
}

.type-danger {
  .dialog-icon {
    color: #e74c3c;
  }
}

.type-confirm {
  .dialog-icon {
    color: #007AFF;
  }
}

// 动画定义
@keyframes dialog-show {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(-20rpx);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式适配
@media (max-width: 480px) {
  .dialog-container {
    &.size-small,
    &.size-medium,
    &.size-large {
      width: calc(100vw - 80rpx);
      min-width: auto;
    }
  }
}
</style>