-- 删除节能灯管理系统菜单数据的SQL脚本
-- 执行前请确保已备份数据库

-- 1. 删除用户收藏菜单数据
DELETE FROM sysusermenu 
WHERE MenuId >= 1300150000101 AND MenuId <= 1300150050901;

-- 2. 删除角色菜单关联数据
DELETE FROM sysrolemenu 
WHERE MenuId >= 1300150000101 AND MenuId <= 1300150050901;

-- 3. 删除租户菜单关联数据
DELETE FROM systenantmenu 
WHERE MenuId >= 1300150000101 AND MenuId <= 1300150050901;

-- 4. 删除主菜单数据
DELETE FROM sysmenu 
WHERE Id >= 1300150000101 AND Id <= 1300150050901;

-- 验证删除结果
SELECT COUNT(*) as remaining_menus FROM sysmenu WHERE Id >= 1300150000101 AND Id <= 1300150050901;
SELECT COUNT(*) as remaining_role_menus FROM sysrolemenu WHERE MenuId >= 1300150000101 AND MenuId <= 1300150050901;
SELECT COUNT(*) as remaining_tenant_menus FROM systenantmenu WHERE MenuId >= 1300150000101 AND MenuId <= 1300150050901;
SELECT COUNT(*) as remaining_user_menus FROM sysusermenu WHERE MenuId >= 1300150000101 AND MenuId <= 1300150050901;

-- 如果上述查询返回0，说明删除成功