<template>
  <div class="schedule-enhanced-container">
    <!-- 任务概览统计 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon :size="24" color="#409EFF"><Clock /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statsData.totalTasks }}</div>
            <div class="stat-label">总任务数</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon :size="24" color="#67C23A"><CircleCheck /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statsData.activeTasks }}</div>
            <div class="stat-label">活跃任务</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon :size="24" color="#E6A23C"><Warning /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statsData.pendingTasks }}</div>
            <div class="stat-label">待执行</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon :size="24" color="#F56C6C"><CircleClose /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statsData.failedTasks }}</div>
            <div class="stat-label">失败任务</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 任务执行时间轴 -->
    <el-card class="timeline-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>今日任务时间轴</span>
          <div class="header-actions">
            <el-date-picker
              v-model="timelineDate"
              type="date"
              placeholder="选择日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="updateTimeline"
            />
            <el-button type="primary" @click="refreshTimeline">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="timeline-container">
        <div class="timeline-hours">
          <div v-for="hour in 24" :key="hour" class="hour-mark">
            {{ String(hour - 1).padStart(2, '0') }}:00
          </div>
        </div>
        <div class="timeline-tasks">
          <div 
            v-for="task in timelineTasks" 
            :key="task.id"
            class="timeline-task"
            :style="getTaskStyle(task)"
            @click="viewTaskDetail(task)"
          >
            <div class="task-info">
              <span class="task-name">{{ task.name }}</span>
              <span class="task-time">{{ task.executeTime }}</span>
            </div>
            <div class="task-status" :class="task.status">
              <el-icon v-if="task.status === 'completed'"><CircleCheck /></el-icon>
              <el-icon v-else-if="task.status === 'failed'"><CircleClose /></el-icon>
              <el-icon v-else-if="task.status === 'running'"><Loading /></el-icon>
              <el-icon v-else><Clock /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 智能任务推荐 -->
    <el-card class="recommendation-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>智能任务推荐</span>
          <el-button type="text" @click="generateRecommendations">
            <el-icon><MagicStick /></el-icon>
            生成推荐
          </el-button>
        </div>
      </template>
      
      <div class="recommendation-list">
        <div 
          v-for="recommendation in taskRecommendations" 
          :key="recommendation.id"
          class="recommendation-item"
        >
          <div class="recommendation-icon">
            <el-icon :size="20" :color="recommendation.iconColor">
              <component :is="recommendation.icon" />
            </el-icon>
          </div>
          <div class="recommendation-content">
            <h4>{{ recommendation.title }}</h4>
            <p>{{ recommendation.description }}</p>
            <div class="recommendation-details">
              <span class="detail-item">
                <el-icon><Clock /></el-icon>
                {{ recommendation.suggestedTime }}
              </span>
              <span class="detail-item">
                <el-icon><TrendCharts /></el-icon>
                节能: {{ recommendation.energySaving }}%
              </span>
              <span class="detail-item">
                <el-icon><Star /></el-icon>
                置信度: {{ recommendation.confidence }}%
              </span>
            </div>
          </div>
          <div class="recommendation-actions">
            <el-button type="primary" size="small" @click="applyRecommendation(recommendation)">
              应用
            </el-button>
            <el-button type="info" size="small" @click="customizeRecommendation(recommendation)">
              自定义
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 任务执行分析 -->
    <el-card class="analysis-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>任务执行分析</span>
          <el-radio-group v-model="analysisType" @change="updateAnalysis">
            <el-radio-button label="daily">日分析</el-radio-button>
            <el-radio-button label="weekly">周分析</el-radio-button>
            <el-radio-button label="monthly">月分析</el-radio-button>
          </el-radio-group>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="chart-container">
            <h4>任务执行成功率</h4>
            <div ref="successRateChartRef" style="height: 300px;"></div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="chart-container">
            <h4>任务类型分布</h4>
            <div ref="taskTypeChartRef" style="height: 300px;"></div>
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="24">
          <div class="chart-container">
            <h4>节能效果趋势</h4>
            <div ref="energyTrendChartRef" style="height: 250px;"></div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 批量任务创建向导 -->
    <el-card class="batch-wizard-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>批量任务创建向导</span>
          <el-button type="primary" @click="startBatchWizard">
            <el-icon><Plus /></el-icon>
            开始创建
          </el-button>
        </div>
      </template>
      
      <div class="wizard-templates">
        <div 
          v-for="template in batchTemplates" 
          :key="template.id"
          class="template-card"
          @click="selectBatchTemplate(template)"
        >
          <div class="template-icon">
            <el-icon :size="32" :color="template.color">
              <component :is="template.icon" />
            </el-icon>
          </div>
          <div class="template-info">
            <h4>{{ template.name }}</h4>
            <p>{{ template.description }}</p>
            <div class="template-stats">
              <span>{{ template.taskCount }}个任务</span>
              <span>{{ template.duration }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 任务详情弹窗 -->
    <el-dialog v-model="taskDetailVisible" title="任务详情" width="800px" destroy-on-close>
      <div v-if="selectedTask" class="task-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务名称">{{ selectedTask.name }}</el-descriptions-item>
          <el-descriptions-item label="任务类型">{{ selectedTask.type }}</el-descriptions-item>
          <el-descriptions-item label="执行时间">{{ selectedTask.executeTime }}</el-descriptions-item>
          <el-descriptions-item label="任务状态">
            <el-tag :type="getStatusType(selectedTask.status)">{{ getStatusText(selectedTask.status) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="目标设备">{{ selectedTask.targetDevices }}台</el-descriptions-item>
          <el-descriptions-item label="预计节能">{{ selectedTask.energySaving }}%</el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">{{ selectedTask.createTime }}</el-descriptions-item>
          <el-descriptions-item label="任务描述" :span="2">{{ selectedTask.description }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="execution-history" style="margin-top: 20px;">
          <h4>执行历史</h4>
          <el-timeline>
            <el-timeline-item
              v-for="record in selectedTask.executionHistory"
              :key="record.id"
              :timestamp="record.time"
              :type="record.success ? 'success' : 'danger'"
            >
              <div class="history-item">
                <div class="history-status">
                  <el-icon v-if="record.success"><CircleCheck /></el-icon>
                  <el-icon v-else><CircleClose /></el-icon>
                  <span>{{ record.success ? '执行成功' : '执行失败' }}</span>
                </div>
                <div class="history-details">
                  <p>影响设备: {{ record.affectedDevices }}台</p>
                  <p v-if="record.energySaved">节约能耗: {{ record.energySaved }}kWh</p>
                  <p v-if="record.error">错误信息: {{ record.error }}</p>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="taskDetailVisible = false">关闭</el-button>
          <el-button type="primary" @click="editTask(selectedTask)">编辑任务</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量创建向导弹窗 -->
    <el-dialog v-model="batchWizardVisible" title="批量任务创建向导" width="900px" destroy-on-close>
      <el-steps :active="wizardStep" finish-status="success">
        <el-step title="选择模板" />
        <el-step title="配置参数" />
        <el-step title="预览确认" />
        <el-step title="创建完成" />
      </el-steps>
      
      <div class="wizard-content">
        <!-- 步骤1: 选择模板 -->
        <div v-if="wizardStep === 0" class="wizard-step">
          <h3>选择批量任务模板</h3>
          <div class="template-selection">
            <div 
              v-for="template in batchTemplates" 
              :key="template.id"
              class="selectable-template"
              :class="{ active: selectedBatchTemplate?.id === template.id }"
              @click="selectedBatchTemplate = template"
            >
              <div class="template-icon">
                <el-icon :size="24" :color="template.color">
                  <component :is="template.icon" />
                </el-icon>
              </div>
              <div class="template-content">
                <h4>{{ template.name }}</h4>
                <p>{{ template.description }}</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 步骤2: 配置参数 -->
        <div v-if="wizardStep === 1" class="wizard-step">
          <h3>配置任务参数</h3>
          <el-form :model="batchConfig" label-width="120px">
            <el-form-item label="任务名称前缀">
              <el-input v-model="batchConfig.namePrefix" placeholder="例如：自动节能" />
            </el-form-item>
            <el-form-item label="执行时间范围">
              <el-time-picker
                v-model="batchConfig.timeRange"
                is-range
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="HH:mm"
                value-format="HH:mm"
              />
            </el-form-item>
            <el-form-item label="重复模式">
              <el-select v-model="batchConfig.repeatMode" placeholder="选择重复模式">
                <el-option label="每天" value="daily" />
                <el-option label="工作日" value="weekdays" />
                <el-option label="周末" value="weekends" />
                <el-option label="自定义" value="custom" />
              </el-select>
            </el-form-item>
            <el-form-item v-if="batchConfig.repeatMode === 'custom'" label="自定义周期">
              <el-checkbox-group v-model="batchConfig.customDays">
                <el-checkbox label="1">周一</el-checkbox>
                <el-checkbox label="2">周二</el-checkbox>
                <el-checkbox label="3">周三</el-checkbox>
                <el-checkbox label="4">周四</el-checkbox>
                <el-checkbox label="5">周五</el-checkbox>
                <el-checkbox label="6">周六</el-checkbox>
                <el-checkbox label="0">周日</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="目标设备">
              <el-select v-model="batchConfig.targetType" placeholder="选择目标类型">
                <el-option label="所有设备" value="all" />
                <el-option label="按区域" value="area" />
                <el-option label="按类型" value="type" />
                <el-option label="自定义选择" value="custom" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        
        <!-- 步骤3: 预览确认 -->
        <div v-if="wizardStep === 2" class="wizard-step">
          <h3>预览将要创建的任务</h3>
          <div class="task-preview">
            <el-table :data="previewTasks" border>
              <el-table-column prop="name" label="任务名称" />
              <el-table-column prop="executeTime" label="执行时间" />
              <el-table-column prop="repeatPattern" label="重复模式" />
              <el-table-column prop="targetDevices" label="目标设备" />
              <el-table-column prop="action" label="执行动作" />
            </el-table>
            <div class="preview-summary">
              <p>将创建 <strong>{{ previewTasks.length }}</strong> 个定时任务</p>
              <p>预计影响 <strong>{{ batchConfig.estimatedDevices }}</strong> 台设备</p>
              <p>预计节能效果: <strong>{{ batchConfig.estimatedSaving }}%</strong></p>
            </div>
          </div>
        </div>
        
        <!-- 步骤4: 创建完成 -->
        <div v-if="wizardStep === 3" class="wizard-step">
          <div class="completion-status">
            <el-result
              icon="success"
              title="批量任务创建成功"
              :sub-title="`成功创建了 ${createdTaskCount} 个定时任务`"
            >
              <template #extra>
                <el-button type="primary" @click="finishWizard">完成</el-button>
                <el-button @click="resetWizard">再次创建</el-button>
              </template>
            </el-result>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="wizardStep > 0" @click="wizardStep--">上一步</el-button>
          <el-button @click="batchWizardVisible = false">取消</el-button>
          <el-button 
            v-if="wizardStep < 3" 
            type="primary" 
            @click="nextWizardStep"
            :disabled="!canProceedToNext"
          >
            {{ wizardStep === 2 ? '创建任务' : '下一步' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="scheduleEnhanced">
import { ref, reactive, computed, onMounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts';
import {
  Clock, CircleCheck, CircleClose, Warning, Loading, Refresh, MagicStick,
  TrendCharts, Star, Plus, User
} from '@element-plus/icons-vue';

// 组件事件
const emit = defineEmits(['refresh', 'createTask', 'editTask']);

// 响应式数据
const timelineDate = ref(new Date().toISOString().split('T')[0]);
const analysisType = ref('daily');
const taskDetailVisible = ref(false);
const batchWizardVisible = ref(false);
const wizardStep = ref(0);
const createdTaskCount = ref(0);

const successRateChartRef = ref();
const taskTypeChartRef = ref();
const energyTrendChartRef = ref();

// 统计数据
const statsData = reactive({
  totalTasks: 156,
  activeTasks: 89,
  pendingTasks: 23,
  failedTasks: 5
});

// 时间轴任务数据
const timelineTasks = ref([
  {
    id: '1',
    name: '办公区域节能',
    executeTime: '08:30',
    status: 'completed',
    duration: 30
  },
  {
    id: '2',
    name: '会议室照明调节',
    executeTime: '12:00',
    status: 'completed',
    duration: 15
  },
  {
    id: '3',
    name: '走廊灯光控制',
    executeTime: '18:00',
    status: 'pending',
    duration: 20
  },
  {
    id: '4',
    name: '夜间安全照明',
    executeTime: '22:00',
    status: 'pending',
    duration: 480
  }
]);

// 智能推荐数据
const taskRecommendations = ref([
  {
    id: '1',
    title: '午休时段节能',
    description: '在午休时间(12:00-14:00)自动调暗办公区域照明',
    icon: 'Sunny',
    iconColor: '#E6A23C',
    suggestedTime: '12:00-14:00',
    energySaving: 25,
    confidence: 92
  },
  {
    id: '2',
    title: '下班后自动关闭',
    description: '工作日18:30后自动关闭非必要照明设备',
    icon: 'MoonNight',
    iconColor: '#909399',
    suggestedTime: '18:30',
    energySaving: 40,
    confidence: 88
  },
  {
    id: '3',
    title: '周末低功耗模式',
    description: '周末期间启用低功耗照明模式',
    icon: 'Calendar',
    iconColor: '#67C23A',
    suggestedTime: '周末全天',
    energySaving: 35,
    confidence: 85
  }
]);

// 批量模板数据
const batchTemplates = ref([
  {
    id: '1',
    name: '工作日节能方案',
    description: '为工作日创建完整的节能任务计划',
    icon: 'OfficeBuilding',
    color: '#409EFF',
    taskCount: 8,
    duration: '全天'
  },
  {
    id: '2',
    name: '会议室智能控制',
    description: '根据会议安排自动调节会议室照明',
    icon: 'User',
    color: '#67C23A',
    taskCount: 12,
    duration: '工作时间'
  },
  {
    id: '3',
    name: '安全照明计划',
    description: '夜间和节假日的安全照明任务',
    icon: 'Lock',
    color: '#E6A23C',
    taskCount: 6,
    duration: '夜间'
  }
]);

// 选中的任务和模板
const selectedTask = ref(null);
const selectedBatchTemplate = ref(null);

// 批量配置
const batchConfig = reactive({
  namePrefix: '',
  timeRange: [],
  repeatMode: 'daily',
  customDays: [],
  targetType: 'all',
  estimatedDevices: 45,
  estimatedSaving: 28
});

// 预览任务
const previewTasks = ref([]);

// 计算属性
const canProceedToNext = computed(() => {
  switch (wizardStep.value) {
    case 0:
      return selectedBatchTemplate.value !== null;
    case 1:
      return batchConfig.namePrefix && batchConfig.timeRange.length === 2;
    case 2:
      return previewTasks.value.length > 0;
    default:
      return true;
  }
});

onMounted(() => {
  initCharts();
});

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    initSuccessRateChart();
    initTaskTypeChart();
    initEnergyTrendChart();
  });
};

// 初始化成功率图表
const initSuccessRateChart = () => {
  if (!successRateChartRef.value) return;
  
  const chart = echarts.init(successRateChartRef.value);
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['成功率', '执行次数']
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: [
      {
        type: 'value',
        name: '成功率(%)',
        min: 0,
        max: 100
      },
      {
        type: 'value',
        name: '执行次数'
      }
    ],
    series: [
      {
        name: '成功率',
        type: 'line',
        data: [95, 92, 98, 94, 96, 89, 91],
        itemStyle: {
          color: '#67C23A'
        }
      },
      {
        name: '执行次数',
        type: 'bar',
        yAxisIndex: 1,
        data: [45, 52, 48, 56, 49, 32, 28],
        itemStyle: {
          color: '#409EFF'
        }
      }
    ]
  };
  chart.setOption(option);
};

// 初始化任务类型图表
const initTaskTypeChart = () => {
  if (!taskTypeChartRef.value) return;
  
  const chart = echarts.init(taskTypeChartRef.value);
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [{
      name: '任务类型',
      type: 'pie',
      radius: '50%',
      data: [
        { value: 45, name: '定时开关' },
        { value: 32, name: '定时调光' },
        { value: 28, name: '场景执行' },
        { value: 18, name: '批量控制' },
        { value: 12, name: '其他' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  };
  chart.setOption(option);
};

// 初始化节能趋势图表
const initEnergyTrendChart = () => {
  if (!energyTrendChartRef.value) return;
  
  const chart = echarts.init(energyTrendChartRef.value);
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['节能量(kWh)', '节省费用(元)']
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: [
      {
        type: 'value',
        name: '节能量(kWh)'
      },
      {
        type: 'value',
        name: '节省费用(元)'
      }
    ],
    series: [
      {
        name: '节能量(kWh)',
        type: 'bar',
        data: [1200, 1350, 1180, 1420, 1380, 1560],
        itemStyle: {
          color: '#67C23A'
        }
      },
      {
        name: '节省费用(元)',
        type: 'line',
        yAxisIndex: 1,
        data: [600, 675, 590, 710, 690, 780],
        itemStyle: {
          color: '#E6A23C'
        }
      }
    ]
  };
  chart.setOption(option);
};

// 获取任务样式
const getTaskStyle = (task) => {
  const hour = parseInt(task.executeTime.split(':')[0]);
  const minute = parseInt(task.executeTime.split(':')[1]);
  const left = ((hour + minute / 60) / 24) * 100;
  const width = Math.max((task.duration || 30) / (24 * 60) * 100, 2);
  
  return {
    left: `${left}%`,
    width: `${width}%`
  };
};

// 查看任务详情
const viewTaskDetail = (task) => {
  selectedTask.value = {
    ...task,
    type: '定时开关',
    targetDevices: 12,
    energySaving: 25,
    createTime: '2024-01-15 10:30:00',
    description: '自动控制办公区域照明设备的开关状态',
    executionHistory: [
      {
        id: '1',
        time: '2024-01-20 08:30:00',
        success: true,
        affectedDevices: 12,
        energySaved: 2.5
      },
      {
        id: '2',
        time: '2024-01-19 08:30:00',
        success: true,
        affectedDevices: 12,
        energySaved: 2.3
      },
      {
        id: '3',
        time: '2024-01-18 08:30:00',
        success: false,
        affectedDevices: 0,
        error: '设备通信超时'
      }
    ]
  };
  taskDetailVisible.value = true;
};

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    completed: 'success',
    failed: 'danger',
    running: 'warning',
    pending: 'info'
  };
  return statusMap[status] || 'info';
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    completed: '已完成',
    failed: '执行失败',
    running: '执行中',
    pending: '待执行'
  };
  return statusMap[status] || '未知';
};

// 编辑任务
const editTask = (task) => {
  emit('editTask', task);
  taskDetailVisible.value = false;
};

// 刷新时间轴
const refreshTimeline = () => {
  ElMessage.success('时间轴已刷新');
};

// 更新时间轴
const updateTimeline = () => {
  ElMessage.success('时间轴数据已更新');
};

// 生成推荐
const generateRecommendations = async () => {
  try {
    // 模拟AI分析
    await new Promise(resolve => setTimeout(resolve, 1500));
    ElMessage.success('已生成新的智能推荐');
  } catch (error) {
    ElMessage.error('生成推荐失败');
  }
};

// 应用推荐
const applyRecommendation = (recommendation) => {
  emit('createTask', {
    name: recommendation.title,
    description: recommendation.description,
    type: 'recommended',
    config: recommendation
  });
  ElMessage.success(`已应用推荐：${recommendation.title}`);
};

// 自定义推荐
const customizeRecommendation = (recommendation) => {
  emit('createTask', {
    name: recommendation.title,
    description: recommendation.description,
    type: 'customize',
    config: recommendation
  });
};

// 更新分析
const updateAnalysis = () => {
  initCharts();
  ElMessage.success('分析数据已更新');
};

// 开始批量向导
const startBatchWizard = () => {
  wizardStep.value = 0;
  selectedBatchTemplate.value = null;
  batchWizardVisible.value = true;
};

// 选择批量模板
const selectBatchTemplate = (template) => {
  selectedBatchTemplate.value = template;
};

// 下一步向导
const nextWizardStep = async () => {
  if (wizardStep.value === 2) {
    // 创建任务
    try {
      await createBatchTasks();
      wizardStep.value++;
    } catch (error) {
      ElMessage.error('创建任务失败');
    }
  } else {
    wizardStep.value++;
    if (wizardStep.value === 2) {
      generatePreviewTasks();
    }
  }
};

// 生成预览任务
const generatePreviewTasks = () => {
  const tasks = [];
  const [startTime, endTime] = batchConfig.timeRange;
  
  // 根据配置生成任务预览
  for (let i = 0; i < 5; i++) {
    tasks.push({
      name: `${batchConfig.namePrefix}_${i + 1}`,
      executeTime: startTime,
      repeatPattern: batchConfig.repeatMode === 'daily' ? '每天' : '自定义',
      targetDevices: `${Math.floor(Math.random() * 20) + 5}台`,
      action: '自动调节'
    });
  }
  
  previewTasks.value = tasks;
};

// 创建批量任务
const createBatchTasks = async () => {
  // 模拟创建过程
  await new Promise(resolve => setTimeout(resolve, 2000));
  createdTaskCount.value = previewTasks.value.length;
};

// 完成向导
const finishWizard = () => {
  batchWizardVisible.value = false;
  emit('refresh');
  ElMessage.success('批量任务创建完成');
};

// 重置向导
const resetWizard = () => {
  wizardStep.value = 0;
  selectedBatchTemplate.value = null;
  Object.assign(batchConfig, {
    namePrefix: '',
    timeRange: [],
    repeatMode: 'daily',
    customDays: [],
    targetType: 'all'
  });
  previewTasks.value = [];
};
</script>

<style scoped lang="scss">
.schedule-enhanced-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
  }
}

// 统计卡片样式
.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .stat-icon {
    margin-right: 16px;
  }
  
  .stat-content {
    .stat-value {
      font-size: 28px;
      font-weight: bold;
      color: #303133;
      line-height: 1;
    }
    
    .stat-label {
      font-size: 14px;
      color: #909399;
      margin-top: 4px;
    }
  }
}

// 时间轴样式
.timeline-container {
  position: relative;
  height: 120px;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

.timeline-hours {
  display: flex;
  height: 30px;
  border-bottom: 1px solid #e4e7ed;
  
  .hour-mark {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #909399;
    border-right: 1px solid #e4e7ed;
    
    &:last-child {
      border-right: none;
    }
  }
}

.timeline-tasks {
  position: relative;
  height: 90px;
  padding: 10px 0;
}

.timeline-task {
  position: absolute;
  height: 70px;
  background: #409eff;
  border-radius: 4px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  }
  
  &.completed {
    background: #67c23a;
  }
  
  &.failed {
    background: #f56c6c;
  }
  
  &.pending {
    background: #e6a23c;
  }
  
  .task-info {
    color: white;
    
    .task-name {
      font-size: 12px;
      font-weight: 500;
      display: block;
      margin-bottom: 2px;
    }
    
    .task-time {
      font-size: 10px;
      opacity: 0.8;
    }
  }
  
  .task-status {
    align-self: flex-end;
    color: white;
  }
}

// 推荐样式
.recommendation-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.recommendation-item {
  display: flex;
  align-items: center;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  transition: all 0.3s;
  
  &:hover {
    border-color: #409eff;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
  }
  
  .recommendation-icon {
    margin-right: 16px;
  }
  
  .recommendation-content {
    flex: 1;
    
    h4 {
      margin: 0 0 8px 0;
      font-size: 16px;
      color: #303133;
    }
    
    p {
      margin: 0 0 12px 0;
      font-size: 14px;
      color: #606266;
      line-height: 1.4;
    }
    
    .recommendation-details {
      display: flex;
      gap: 20px;
      
      .detail-item {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .recommendation-actions {
    display: flex;
    gap: 8px;
  }
}

// 图表样式
.chart-container {
  h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    color: #303133;
  }
}

// 批量模板样式
.wizard-templates {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.template-card {
  display: flex;
  align-items: center;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    border-color: #409eff;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
  }
  
  .template-icon {
    margin-right: 16px;
  }
  
  .template-info {
    h4 {
      margin: 0 0 8px 0;
      font-size: 16px;
      color: #303133;
    }
    
    p {
      margin: 0 0 8px 0;
      font-size: 14px;
      color: #606266;
    }
    
    .template-stats {
      display: flex;
      gap: 16px;
      font-size: 12px;
      color: #909399;
    }
  }
}

// 任务详情样式
.task-detail {
  .execution-history {
    h4 {
      margin: 0 0 16px 0;
      color: #303133;
    }
  }
  
  .history-item {
    .history-status {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      margin-bottom: 8px;
    }
    
    .history-details {
      p {
        margin: 4px 0;
        font-size: 14px;
        color: #606266;
      }
    }
  }
}

// 向导样式
.wizard-content {
  margin: 30px 0;
  min-height: 400px;
}

.wizard-step {
  h3 {
    margin: 0 0 20px 0;
    color: #303133;
  }
}

.template-selection {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.selectable-template {
  display: flex;
  align-items: center;
  padding: 20px;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    border-color: #409eff;
  }
  
  &.active {
    border-color: #409eff;
    background: #f0f9ff;
  }
  
  .template-icon {
    margin-right: 16px;
  }
  
  .template-content {
    h4 {
      margin: 0 0 8px 0;
      color: #303133;
    }
    
    p {
      margin: 0;
      color: #606266;
    }
  }
}

.task-preview {
  .preview-summary {
    margin-top: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    
    p {
      margin: 8px 0;
      color: #606266;
      
      strong {
        color: #409eff;
      }
    }
  }
}

.completion-status {
  text-align: center;
  padding: 40px 0;
}

.dialog-footer {
  text-align: right;
}
</style>