/**
 * 能耗管理Vuex模块
 */

const state = {
  // 能耗统计数据
  energyStats: {
    realtimePower: 0,
    totalConsumption: 0,
    energySavingRate: 0,
    todayCost: 0,
    totalCost: 0,
    activeLights: 0,
    avgTemperature: 22
  },
  // 实时监控数据
  realtimeData: {
    power: 0,
    lights: 0,
    temperature: 0,
    cost: 0
  },
  // 加载状态
  loading: false,
  // 错误信息
  error: null,
  // 最后更新时间
  lastUpdateTime: null
}

const getters = {
  // 获取格式化的能耗统计
  formattedEnergyStats: (state) => {
    return {
      realtimePower: `${state.energyStats.realtimePower}kW`,
      totalConsumption: `${state.energyStats.totalConsumption}kWh`,
      energySavingRate: `${state.energyStats.energySavingRate}%`,
      todayCost: `¥${state.energyStats.todayCost}`,
      totalCost: `¥${state.energyStats.totalCost}`,
      activeLights: state.energyStats.activeLights.toString(),
      avgTemperature: `${state.energyStats.avgTemperature}°C`
    }
  },
  
  // 获取实时监控数据
  formattedRealtimeData: (state) => {
    return [
      {
        icon: '⚡',
        iconClass: 'monitor-power',
        value: `${state.realtimeData.power}kW`,
        label: '实时功率'
      },
      {
        icon: '💡',
        iconClass: 'monitor-light',
        value: state.realtimeData.lights.toString(),
        label: '活跃灯具'
      },
      {
        icon: '🌡️',
        iconClass: 'monitor-temp',
        value: `${state.realtimeData.temperature}°C`,
        label: '平均温度'
      },
      {
        icon: '💰',
        iconClass: 'monitor-cost',
        value: `¥${state.realtimeData.cost}`,
        label: '今日电费'
      }
    ]
  },
  
  // 检查数据是否过期
  isDataExpired: (state) => {
    if (!state.lastUpdateTime) return true
    const now = Date.now()
    const updateTime = new Date(state.lastUpdateTime).getTime()
    return (now - updateTime) > 5 * 60 * 1000 // 5分钟过期
  }
}

const mutations = {
  // 设置能耗统计数据
  SET_ENERGY_STATS(state, data) {
    state.energyStats = { ...state.energyStats, ...data }
    state.lastUpdateTime = new Date().toISOString()
  },
  
  // 设置实时监控数据
  SET_REALTIME_DATA(state, data) {
    state.realtimeData = {
      power: data.realtimePower || data.power || 0,
      lights: data.activeLights || data.lights || 0,
      temperature: data.avgTemperature || data.temperature || 22,
      cost: data.todayCost || data.totalCost || data.cost || 0
    }
  },
  
  // 设置加载状态
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  
  // 设置错误信息
  SET_ERROR(state, error) {
    state.error = error
  },
  
  // 清空数据
  CLEAR_DATA(state) {
    state.energyStats = {
      realtimePower: 0,
      totalConsumption: 0,
      energySavingRate: 0,
      todayCost: 0,
      totalCost: 0,
      activeLights: 0,
      avgTemperature: 22
    }
    state.realtimeData = {
      power: 0,
      lights: 0,
      temperature: 0,
      cost: 0
    }
    state.lastUpdateTime = null
  }
}

const actions = {
  // 获取能耗统计数据
  async getEnergyStats({ commit, getters }, params = {}) {
    try {
      commit('SET_LOADING', true)
      commit('SET_ERROR', null)
      
      // 检查缓存数据是否有效
      if (!getters.isDataExpired && !params.forceRefresh) {
        return { success: true, data: state.energyStats, fromCache: true }
      }
      
      // 动态导入API函数
      const { getEnergyStats } = await import('../../api/energy.js')
      const response = await getEnergyStats(params)
      console.log("获取能耗统计",response)
      if (response.code === 200 || response.success) {
        const energyData = response.data || {}
        
        // 更新状态
        commit('SET_ENERGY_STATS', energyData)
        commit('SET_REALTIME_DATA', energyData)
        
        // 缓存数据到本地存储
        uni.setStorageSync('cached_energy_stats', {
          data: energyData,
          timestamp: Date.now()
        })
        
        return { success: true, data: energyData }
      } else {
        throw new Error(response.message || '获取能耗统计失败')
      }
    } catch (error) {
      console.error('获取能耗统计失败:', error)
      commit('SET_ERROR', error.message)
      
      // 尝试使用缓存数据
      const cachedData = uni.getStorageSync('cached_energy_stats')
      if (cachedData && (Date.now() - cachedData.timestamp < 30 * 60 * 1000)) {
        commit('SET_ENERGY_STATS', cachedData.data)
        commit('SET_REALTIME_DATA', cachedData.data)
        return { success: true, data: cachedData.data, fromCache: true }
      }
      
      // 返回默认数据
      const defaultData = {
        realtimePower: Math.floor(Math.random() * 50 + 10),
        totalConsumption: Math.floor(Math.random() * 1000 + 500),
        energySavingRate: Math.floor(Math.random() * 30 + 10),
        todayCost: Math.floor(Math.random() * 100 + 50),
        totalCost: Math.floor(Math.random() * 500 + 200),
        activeLights: Math.floor(Math.random() * 100 + 50),
        avgTemperature: Math.floor(Math.random() * 10 + 20)
      }
      
      commit('SET_ENERGY_STATS', defaultData)
      commit('SET_REALTIME_DATA', defaultData)
      
      return { success: true, data: defaultData, isDefault: true }
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 刷新实时数据
  async refreshRealtimeData({ commit, dispatch }, params = {}) {
    try {
      // 获取最新的能耗数据
      //const result = await dispatch('getEnergyStats', { ...params, forceRefresh: true })
      const result = await dispatch('getEnergyStats', { ...params })
      
      if (result.success) {
        // 更新实时监控数据
        commit('SET_REALTIME_DATA', result.data)
        return result
      }
      
      throw new Error('刷新实时数据失败')
    } catch (error) {
      console.error('刷新实时数据失败:', error)
      commit('SET_ERROR', error.message)
      throw error
    }
  },
  
  // 定时同步数据
  async syncData({ dispatch, getters }) {
    try {
      // 如果数据过期，则自动刷新
      if (getters.isDataExpired) {
        await dispatch('getEnergyStats', { forceRefresh: true })
      }
    } catch (error) {
      console.error('同步数据失败:', error)
    }
  },
  
  // 清空所有数据
  clearAllData({ commit }) {
    commit('CLEAR_DATA')
    commit('SET_ERROR', null)
    
    // 清除本地缓存
    uni.removeStorageSync('cached_energy_stats')
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}