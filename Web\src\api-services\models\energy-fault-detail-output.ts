/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { EnergyDeviceOutput } from './energy-device-output';
import { EnergyFaultOutput } from './energy-fault-output';
import { FaultHistoryItem } from './fault-history-item';
/**
 * 故障详情输出参数
 * @export
 * @interface EnergyFaultDetailOutput
 */
export interface EnergyFaultDetailOutput {
    /**
     * 主键ID
     * @type {number}
     * @memberof EnergyFaultDetailOutput
     */
    id?: number;
    /**
     * 设备ID
     * @type {number}
     * @memberof EnergyFaultDetailOutput
     */
    deviceId?: number;
    /**
     * 设备编码
     * @type {string}
     * @memberof EnergyFaultDetailOutput
     */
    deviceCode?: string | null;
    /**
     * 故障编码
     * @type {string}
     * @memberof EnergyFaultDetailOutput
     */
    faultCode?: string | null;
    /**
     * 设备名称
     * @type {string}
     * @memberof EnergyFaultDetailOutput
     */
    deviceName?: string | null;
    /**
     * 故障类型
     * @type {string}
     * @memberof EnergyFaultDetailOutput
     */
    faultType?: string | null;
    /**
     * 故障等级
     * @type {number}
     * @memberof EnergyFaultDetailOutput
     */
    faultLevel?: number;
    /**
     * 故障描述
     * @type {string}
     * @memberof EnergyFaultDetailOutput
     */
    faultDescription?: string | null;
    /**
     * 故障时间
     * @type {Date}
     * @memberof EnergyFaultDetailOutput
     */
    faultTime?: Date;
    /**
     * 故障状态
     * @type {number}
     * @memberof EnergyFaultDetailOutput
     */
    faultStatus?: number;
    /**
     * 维修人员
     * @type {string}
     * @memberof EnergyFaultDetailOutput
     */
    repairPerson?: string | null;
    /**
     * 维修时间
     * @type {Date}
     * @memberof EnergyFaultDetailOutput
     */
    repairTime?: Date | null;
    /**
     * 维修描述
     * @type {string}
     * @memberof EnergyFaultDetailOutput
     */
    repairDescription?: string | null;
    /**
     * 维修费用
     * @type {number}
     * @memberof EnergyFaultDetailOutput
     */
    repairCost?: number | null;
    /**
     * 解决时间
     * @type {Date}
     * @memberof EnergyFaultDetailOutput
     */
    resolveTime?: Date | null;
    /**
     * 解决方案
     * @type {string}
     * @memberof EnergyFaultDetailOutput
     */
    solution?: string | null;
    /**
     * 备注
     * @type {string}
     * @memberof EnergyFaultDetailOutput
     */
    remark?: string | null;
    /**
     * 创建时间
     * @type {Date}
     * @memberof EnergyFaultDetailOutput
     */
    createTime?: Date;
    /**
     * 更新时间
     * @type {Date}
     * @memberof EnergyFaultDetailOutput
     */
    updateTime?: Date | null;
    /**
     * 历史故障记录
     * @type {Array<EnergyFaultOutput>}
     * @memberof EnergyFaultDetailOutput
     */
    historyFaults?: Array<EnergyFaultOutput> | null;
    /**
     * 
     * @type {EnergyDeviceOutput}
     * @memberof EnergyFaultDetailOutput
     */
    device?: EnergyDeviceOutput;
    /**
     * 故障处理历史
     * @type {Array<FaultHistoryItem>}
     * @memberof EnergyFaultDetailOutput
     */
    history?: Array<FaultHistoryItem> | null;
    /**
     * 相关故障
     * @type {Array<EnergyFaultOutput>}
     * @memberof EnergyFaultDetailOutput
     */
    relatedFaults?: Array<EnergyFaultOutput> | null;
    /**
     * 故障状态名称
     * @type {string}
     * @memberof EnergyFaultDetailOutput
     */
    statusName?: string | null;
    /**
     * 故障持续时长(小时)
     * @type {number}
     * @memberof EnergyFaultDetailOutput
     */
    duration?: number | null;
    /**
     * 租户ID
     * @type {number}
     * @memberof EnergyFaultDetailOutput
     */
    tenantId?: number | null;
}
