/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 告警列表输出
 * @export
 * @interface AlarmListOutput
 */
export interface AlarmListOutput {
    /**
     * 告警ID
     * @type {number}
     * @memberof AlarmListOutput
     */
    id?: number;
    /**
     * 设备ID
     * @type {number}
     * @memberof AlarmListOutput
     */
    deviceId?: number;
    /**
     * 设备名称
     * @type {string}
     * @memberof AlarmListOutput
     */
    deviceName?: string | null;
    /**
     * 故障类型
     * @type {number}
     * @memberof AlarmListOutput
     */
    faultType?: number;
    /**
     * 故障级别
     * @type {number}
     * @memberof AlarmListOutput
     */
    faultLevel?: number;
    /**
     * 故障描述
     * @type {string}
     * @memberof AlarmListOutput
     */
    faultDescription?: string | null;
    /**
     * 处理状态
     * @type {number}
     * @memberof AlarmListOutput
     */
    status?: number;
    /**
     * 创建时间
     * @type {Date}
     * @memberof AlarmListOutput
     */
    createTime?: Date;
    /**
     * 处理时间
     * @type {Date}
     * @memberof AlarmListOutput
     */
    handleTime?: Date | null;
    /**
     * 处理备注
     * @type {string}
     * @memberof AlarmListOutput
     */
    remark?: string | null;
}
