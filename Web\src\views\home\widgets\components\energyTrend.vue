<template>
	<el-card shadow="hover" header="能耗趋势分析" v-loading="loading">
		<div class="energy-trend-container">
			<div class="energy-summary">
				<div class="summary-item today">
					<div class="summary-icon">
						<el-icon><ele-Sunny /></el-icon>
					</div>
					<div class="summary-info">
						<div class="summary-value">{{ energyData.today.toFixed(2) }}</div>
						<div class="summary-label">今日能耗 (kWh)</div>
						<div class="summary-change" :class="{ increase: energyData.todayChange > 0, decrease: energyData.todayChange < 0 }">
							<el-icon v-if="energyData.todayChange > 0"><ele-CaretTop /></el-icon>
							<el-icon v-else-if="energyData.todayChange < 0"><ele-CaretBottom /></el-icon>
							{{ Math.abs(energyData.todayChange).toFixed(1) }}%
						</div>
					</div>
				</div>
				<div class="summary-item month">
					<div class="summary-icon">
						<el-icon><ele-Calendar /></el-icon>
					</div>
					<div class="summary-info">
						<div class="summary-value">{{ energyData.month.toFixed(2) }}</div>
						<div class="summary-label">本月能耗 (kWh)</div>
						<div class="summary-change" :class="{ increase: energyData.monthChange > 0, decrease: energyData.monthChange < 0 }">
							<el-icon v-if="energyData.monthChange > 0"><ele-CaretTop /></el-icon>
							<el-icon v-else-if="energyData.monthChange < 0"><ele-CaretBottom /></el-icon>
							{{ Math.abs(energyData.monthChange).toFixed(1) }}%
						</div>
					</div>
				</div>
				<div class="summary-item cost">
					<div class="summary-icon">
						<el-icon><ele-Money /></el-icon>
					</div>
					<div class="summary-info">
						<div class="summary-value">¥{{ energyData.cost.toFixed(2) }}</div>
						<div class="summary-label">本月电费</div>
						<div class="summary-change savings">
							<el-icon><ele-TrendCharts /></el-icon>
							节省 ¥{{ energyData.savings.toFixed(2) }}
						</div>
					</div>
				</div>
			</div>
			<div class="chart-container">
				<div class="chart-header">
					<span class="chart-title">24小时能耗趋势</span>
					<el-radio-group v-model="chartPeriod" size="small" @change="updateChart">
						<el-radio-button label="24h">24小时</el-radio-button>
						<el-radio-button label="7d">7天</el-radio-button>
						<el-radio-button label="30d">30天</el-radio-button>
					</el-radio-group>
				</div>
				<scEcharts ref="energyChart" height="280px" :option="chartOption"></scEcharts>
			</div>
		</div>
	</el-card>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import scEcharts from '/@/components/scEcharts/index.vue';
import { useSystemApi } from '/@/api-services/system';
// 能耗趋势组件已使用模块化API

// 组件元信息
const title = '能耗趋势分析';
const icon = 'ele-TrendCharts';
const description = '实时显示能耗数据和趋势分析';

const loading = ref(true);
const refreshTimer = ref<NodeJS.Timeout | null>(null);
const chartPeriod = ref('24h');

// 能耗数据
const energyData = reactive({
	today: 0,
	todayChange: 0,
	month: 0,
	monthChange: 0,
	cost: 0,
	savings: 0
});

// 图表配置
const chartOption = ref({
	tooltip: {
		trigger: 'axis',
		axisPointer: {
			type: 'cross',
			label: {
				backgroundColor: '#6a7985'
			}
		},
		formatter: function(params: any) {
			let result = params[0].name + '<br/>';
			params.forEach((param: any) => {
				result += param.marker + param.seriesName + ': ' + param.value + ' kWh<br/>';
			});
			return result;
		}
	},
	legend: {
		data: ['实时功耗', '预测功耗'],
		top: 10
	},
	grid: {
		left: '3%',
		right: '4%',
		bottom: '3%',
		containLabel: true
	},
	xAxis: {
		type: 'category',
		boundaryGap: false,
		data: [],
		axisLabel: {
			rotate: 45,
			fontSize: 10
		}
	},
	yAxis: {
		type: 'value',
		name: '功耗 (kWh)',
		splitLine: {
			lineStyle: {
				type: 'dashed'
			}
		}
	},
	series: [
		{
			name: '实时功耗',
			type: 'line',
			stack: 'Total',
			smooth: true,
			lineStyle: {
				width: 2,
				color: '#1890ff'
			},
			areaStyle: {
				opacity: 0.3,
				color: {
					type: 'linear',
					x: 0,
					y: 0,
					x2: 0,
					y2: 1,
					colorStops: [
						{ offset: 0, color: '#1890ff' },
						{ offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
					]
				}
			},
			data: []
		},
		{
			name: '预测功耗',
			type: 'line',
			lineStyle: {
				width: 2,
				type: 'dashed',
				color: '#52c41a'
			},
			data: []
		}
	]
});

// 获取能耗数据
const fetchEnergyData = async () => {
	try {
		const systemApi = useSystemApi();
		// 获取能耗统计
		const statsResponse = await systemApi.energyConsumption.getStat({});
		if (statsResponse.data) {
			energyData.today = statsResponse.data.today || 0;
			energyData.todayChange = statsResponse.data.todayChange || 0;
			energyData.month = statsResponse.data.month || 0;
			energyData.monthChange = statsResponse.data.monthChange || 0;
			energyData.cost = statsResponse.data.cost || 0;
			energyData.savings = statsResponse.data.savings || 0;
		}

		// 获取趋势数据
		await updateChart();
	} catch (error) {
		console.error('获取能耗数据失败:', error);
		// 使用模拟数据
		energyData.today = 245.67;
		energyData.todayChange = -5.2;
		energyData.month = 7234.89;
		energyData.monthChange = -12.3;
		energyData.cost = 3617.45;
		energyData.savings = 456.78;
		
		// 生成模拟图表数据
		generateSimulatedData();
	} finally {
		loading.value = false;
	}
};

// 更新图表数据
const updateChart = async () => {
	try {
		const systemApi = useSystemApi();
		const response = await systemApi.energyConsumption.getTrend({
			period: chartPeriod.value
		});
		
		if (response.data) {
			chartOption.value.xAxis.data = response.data.labels || [];
			chartOption.value.series[0].data = response.data.actual || [];
			chartOption.value.series[1].data = response.data.predicted || [];
		}
	} catch (error) {
		console.error('获取趋势数据失败:', error);
		generateSimulatedData();
	}
};

// 生成模拟数据
const generateSimulatedData = () => {
	const labels = [];
	const actualData = [];
	const predictedData = [];
	
	const now = new Date();
	const dataPoints = chartPeriod.value === '24h' ? 24 : chartPeriod.value === '7d' ? 7 : 30;
	
	for (let i = dataPoints - 1; i >= 0; i--) {
		const time = new Date(now.getTime() - i * (chartPeriod.value === '24h' ? 3600000 : 86400000));
		const label = chartPeriod.value === '24h' 
			? time.getHours() + ':00'
			: (time.getMonth() + 1) + '/' + time.getDate();
		
		labels.push(label);
		actualData.push((Math.random() * 50 + 100).toFixed(2));
		predictedData.push((Math.random() * 40 + 110).toFixed(2));
	}
	
	chartOption.value.xAxis.data = labels;
	chartOption.value.series[0].data = actualData;
	chartOption.value.series[1].data = predictedData;
};

// 启动定时刷新
const startRefresh = () => {
	refreshTimer.value = setInterval(() => {
		fetchEnergyData();
	}, 60000); // 1分钟刷新一次
};

// 停止定时刷新
const stopRefresh = () => {
	if (refreshTimer.value) {
		clearInterval(refreshTimer.value);
		refreshTimer.value = null;
	}
};

onMounted(() => {
	fetchEnergyData();
	startRefresh();
});

onUnmounted(() => {
	stopRefresh();
});

// 导出组件元信息
defineExpose({
	title,
	icon,
	description
});
</script>

<style scoped lang="scss">
.energy-trend-container {
	display: flex;
	flex-direction: column;
	gap: 20px;
}

.energy-summary {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	gap: 16px;
	margin-bottom: 20px;
}

.summary-item {
	display: flex;
	align-items: center;
	padding: 20px;
	border-radius: 12px;
	background: #fff;
	border: 1px solid #f0f0f0;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 4px;
		background: linear-gradient(90deg, #1890ff, #52c41a);
	}

	&:hover {
		transform: translateY(-2px);
		box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
	}

	&.today::before {
		background: linear-gradient(90deg, #1890ff, #40a9ff);
	}

	&.month::before {
		background: linear-gradient(90deg, #52c41a, #73d13d);
	}

	&.cost::before {
		background: linear-gradient(90deg, #faad14, #ffc53d);
	}
}

.summary-icon {
	font-size: 32px;
	margin-right: 16px;
	color: #1890ff;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 48px;
	height: 48px;
	border-radius: 50%;
	background: rgba(24, 144, 255, 0.1);
}

.summary-info {
	flex: 1;
}

.summary-value {
	font-size: 28px;
	font-weight: bold;
	color: #262626;
	line-height: 1;
	margin-bottom: 4px;
}

.summary-label {
	font-size: 14px;
	color: #8c8c8c;
	margin-bottom: 8px;
}

.summary-change {
	font-size: 12px;
	display: flex;
	align-items: center;
	gap: 4px;

	&.increase {
		color: #ff4d4f;
	}

	&.decrease {
		color: #52c41a;
	}

	&.savings {
		color: #52c41a;
	}
}

.chart-container {
	background: #fff;
	border-radius: 8px;
	padding: 16px;
	border: 1px solid #f0f0f0;
}

.chart-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16px;
}

.chart-title {
	font-size: 16px;
	font-weight: 500;
	color: #262626;
}

@media (max-width: 768px) {
	.energy-summary {
		grid-template-columns: 1fr;
	}
	
	.summary-item {
		padding: 16px;
	}
	
	.summary-icon {
		font-size: 24px;
		width: 40px;
		height: 40px;
		margin-right: 12px;
	}
	
	.summary-value {
		font-size: 24px;
	}
	
	.chart-header {
		flex-direction: column;
		gap: 12px;
		align-items: flex-start;
	}
}
</style>