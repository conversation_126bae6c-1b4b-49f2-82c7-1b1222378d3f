/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 翻译表输出参数
 * @export
 * @interface SysLangTextOutput
 */
export interface SysLangTextOutput {
    /**
     * 主键Id
     * @type {number}
     * @memberof SysLangTextOutput
     */
    id?: number;
    /**
     * 所属实体名
     * @type {string}
     * @memberof SysLangTextOutput
     */
    entityName?: string | null;
    /**
     * 所属实体ID
     * @type {number}
     * @memberof SysLangTextOutput
     */
    entityId?: number;
    /**
     * 字段名
     * @type {string}
     * @memberof SysLangTextOutput
     */
    fieldName?: string | null;
    /**
     * 语言代码
     * @type {string}
     * @memberof SysLangTextOutput
     */
    langCode?: string | null;
    /**
     * 翻译内容
     * @type {string}
     * @memberof SysLangTextOutput
     */
    content?: string | null;
    /**
     * 创建时间
     * @type {Date}
     * @memberof SysLangTextOutput
     */
    createTime?: Date | null;
    /**
     * 更新时间
     * @type {Date}
     * @memberof SysLangTextOutput
     */
    updateTime?: Date | null;
    /**
     * 创建者Id
     * @type {number}
     * @memberof SysLangTextOutput
     */
    createUserId?: number | null;
    /**
     * 创建者姓名
     * @type {string}
     * @memberof SysLangTextOutput
     */
    createUserName?: string | null;
    /**
     * 修改者Id
     * @type {number}
     * @memberof SysLangTextOutput
     */
    updateUserId?: number | null;
    /**
     * 修改者姓名
     * @type {string}
     * @memberof SysLangTextOutput
     */
    updateUserName?: string | null;
}
