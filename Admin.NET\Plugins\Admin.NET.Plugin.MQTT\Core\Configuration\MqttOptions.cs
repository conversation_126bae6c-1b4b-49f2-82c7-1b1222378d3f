// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.IO;
using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using System.Runtime.InteropServices;
using System.Buffers;
using Microsoft.Extensions.Options;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;

namespace Admin.NET.Plugin.MQTT.Core.Configuration;

/// <summary>
/// MQTT配置选项类，自动从Admin.NET.Application项目的MQTT.json文件中读取和解析配置
/// 支持JSON配置文件的自动反序列化和错误处理
/// 优化版本：添加数据验证、内存优化、线程安全
/// </summary>
[StructLayout(LayoutKind.Auto)]
public sealed class MqttOptions : IConfigurableOptions, IDisposable, IValidatableObject
{
    /// <summary>
    /// 日志记录器
    /// </summary>
    private readonly ILogger _logger;
    private readonly IConfiguration _configuration;
    private readonly string _configFilePath;
    private FileSystemWatcher? _fileWatcher;
    private string? _lastDeviceId;
    private DateTime _lastConfigUpdate = DateTime.MinValue;
    private CancellationTokenSource? _reloadCancellationTokenSource;

    /// <summary>
    /// 配置文件路径
    /// </summary>
    private static readonly string ConfigFilePath = Path.Combine(
        AppDomain.CurrentDomain.BaseDirectory, 
        "..", "..", "..", "..", 
        "Admin.NET.Application", 
        "Configuration", 
        "MQTT.json"
    );
    /// <summary>
    /// MQTT服务器地址
    /// </summary>
    [Required(ErrorMessage = "MQTT服务器地址不能为空")]
    [StringLength(255, ErrorMessage = "服务器地址长度不能超过255个字符")]
    [RegularExpression(@"^[a-zA-Z0-9.-]+$", ErrorMessage = "服务器地址格式无效")]
    public string Server { get; set; } = string.Empty;

    /// <summary>
    /// MQTT服务器端口
    /// </summary>
    [Range(1, 65535, ErrorMessage = "端口号必须在1-65535之间")]
    public int Port { get; set; } = 1883;

    /// <summary>
    /// 客户端ID
    /// </summary>
    [StringLength(128, ErrorMessage = "客户端ID长度不能超过128个字符")]
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 用户名
    /// </summary>
    [StringLength(256, ErrorMessage = "用户名长度不能超过256个字符")]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// 密码
    /// </summary>
    [StringLength(512, ErrorMessage = "密码长度不能超过512个字符")]
    public string Password { get; set; } = string.Empty; 

    /// <summary>
    /// 是否启用SSL/TLS
    /// </summary>
    public bool UseTls { get; set; } = false;

    /// <summary>
    /// 连接超时时间（秒）
    /// </summary>
    [Range(5, 300, ErrorMessage = "连接超时时间必须在5-300秒之间")]
    public int ConnectTimeout { get; set; } = 30;

    /// <summary>
    /// 心跳间隔（秒）
    /// </summary>
    [Range(10, 3600, ErrorMessage = "心跳间隔必须在10-3600秒之间")]
    public int KeepAliveInterval { get; set; } = 60;

    /// <summary>
    /// 自动重连间隔（秒）
    /// </summary>
    [Range(1, 60, ErrorMessage = "重连间隔必须在1-60秒之间")]
    public int ReconnectInterval { get; set; } = 5;

    /// <summary>
    /// 最大重连次数
    /// </summary>
    [Range(0, 100, ErrorMessage = "最大重连次数必须在0-100之间")]
    public int MaxReconnectAttempts { get; set; } = 10;

    /// <summary>
    /// 默认QoS等级
    /// </summary>
    [Range(0, 2, ErrorMessage = "QoS等级必须在0-2之间")]
    public int DefaultQoS { get; set; } = 1;

    /// <summary>
    /// 是否启用持久会话
    /// </summary>
    public bool CleanSession { get; set; } = true;

    /// <summary>
    /// 遗嘱主题
    /// </summary>
    [StringLength(512, ErrorMessage = "遗嘱主题长度不能超过512个字符")]
    public string WillTopic { get; set; } = string.Empty;

    /// <summary>
    /// 遗嘱消息
    /// </summary>
    [StringLength(1024, ErrorMessage = "遗嘱消息长度不能超过1024个字符")]
    public string WillMessage { get; set; } = string.Empty;

    /// <summary>
    /// 遗嘱QoS等级
    /// </summary>
    [Range(0, 2, ErrorMessage = "遗嘱QoS等级必须在0-2之间")]
    public int WillQoS { get; set; } = 1;

    /// <summary>
    /// 遗嘱保留标志
    /// </summary>
    public bool WillRetain { get; set; } = false;

    /// <summary>
    /// 订阅主题列表
    /// </summary>
    public List<string> SubscribeTopics { get; set; } = new List<string>();

    /// <summary>
    /// 是否启用消息日志
    /// </summary>
    public bool EnableMessageLogging { get; set; } = true;

    /// <summary>
    /// 消息缓冲区大小
    /// </summary>
    [Range(100, 10000, ErrorMessage = "消息缓冲区大小必须在100-10000之间")]
    public int MessageBufferSize { get; set; } = 1000;

    /// <summary>
    /// 实例标识符
    /// 用于标识MQTT实例的唯一ID，与EMQX平台的InstanceId对应
    /// </summary>
    [StringLength(64, ErrorMessage = "实例标识符长度不能超过64个字符")]
    public string InstanceId { get; set; } = string.Empty;

    /// <summary>
    /// 访问密钥ID
    /// 用于MQTT连接认证的访问密钥标识，与EMQX平台的AccessKeyId对应
    /// </summary>
    [StringLength(128, ErrorMessage = "访问密钥ID长度不能超过128个字符")]
    public string AccessKeyId { get; set; } = string.Empty;

    /// <summary>
    /// 访问密钥密码
    /// 用于MQTT连接认证的访问密钥密码，与EMQX平台的AccessKeySecret对应
    /// </summary>
    [StringLength(256, ErrorMessage = "访问密钥密码长度不能超过256个字符")]
    public string AccessKeySecret { get; set; } = string.Empty;

    /// <summary>
    /// 默认设备ID
    /// 用于生成MQTT客户端凭证的默认设备标识符
    /// </summary>
    [StringLength(64, ErrorMessage = "默认设备ID长度不能超过64个字符")]
    public string DefaultDeviceId { get; set; } = "AdminNET_MQTT_Client";

    /// <summary>
    /// 默认构造函数，用于依赖注入
    /// </summary>
    public MqttOptions()
    {
        _configFilePath = ConfigFilePath;
    }

    /// <summary>
    /// 构造函数，接受日志记录器和配置服务
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="configuration">配置服务</param>
    public MqttOptions(ILogger<MqttOptions>? logger, IConfiguration? configuration)
    {
        _logger = logger;
        _configuration = configuration;
        _configFilePath = ConfigFilePath;
        
        LoadConfiguration();
        InitializeFileWatcher();
        
        // 自动生成MQTT客户端凭证
        AutoGenerateCredentials();
    }

    /// <summary>
    /// 从MQTT.json配置文件中加载配置
    /// 自动处理文件读取和JSON解析错误，支持高性能JSON解析
    /// </summary>
    private void LoadConfiguration()
    {
        try
        {
            // 获取配置文件的绝对路径
            var absolutePath = Path.GetFullPath(ConfigFilePath);
            
            if (!File.Exists(absolutePath))
            {
                _logger?.LogWarning("MQTT配置文件不存在: {ConfigPath}，使用默认配置", absolutePath);
                return;
            }

            // 使用内存池优化读取大文件
            using var fileStream = new FileStream(absolutePath, FileMode.Open, FileAccess.Read, FileShare.Read, bufferSize: 4096);
            var buffer = ArrayPool<byte>.Shared.Rent((int)fileStream.Length);
            
            try
            {
                var bytesRead = fileStream.Read(buffer, 0, (int)fileStream.Length);
                var jsonContent = System.Text.Encoding.UTF8.GetString(buffer, 0, bytesRead);
                
                if (string.IsNullOrWhiteSpace(jsonContent))
                {
                    _logger?.LogWarning("MQTT配置文件为空: {ConfigPath}，使用默认配置", absolutePath);
                    return;
                }

                // 优先使用System.Text.Json进行高性能解析
                try
                {
                    using var document = JsonDocument.Parse(jsonContent);
                    if (document.RootElement.TryGetProperty("MqttOptions", out var mqttOptionsElement))
                    {
                        DeserializeFromJsonElement(mqttOptionsElement);
                        _logger?.LogInformation("成功从配置文件加载MQTT配置: {ConfigPath}", absolutePath);
                        return;
                    }
                }
                catch (System.Text.Json.JsonException)
                {
                    // 如果System.Text.Json解析失败，回退到Newtonsoft.Json
                    _logger?.LogDebug("System.Text.Json解析失败，回退到Newtonsoft.Json");
                }

                // 回退到Newtonsoft.Json解析
                var jsonObject = JObject.Parse(jsonContent);
                if (jsonObject.TryGetValue("MqttOptions", out var mqttOptionsToken) && mqttOptionsToken is JObject mqttOptionsObject)
                {
                    DeserializeFromJObject(mqttOptionsObject);
                    _logger?.LogInformation("成功从配置文件加载MQTT配置: {ConfigPath}", absolutePath);
                }
                else
                {
                    _logger?.LogWarning("配置文件中未找到MqttOptions节点: {ConfigPath}，使用默认配置", absolutePath);
                }
            }
            finally
            {
                ArrayPool<byte>.Shared.Return(buffer);
            }
        }
        catch (System.Text.Json.JsonException ex)
        {
            _logger?.LogError(ex, "解析MQTT配置文件时发生JSON格式错误: {ConfigPath}", ConfigFilePath);
        }
        catch (Newtonsoft.Json.JsonException ex)
        {
            _logger?.LogError(ex, "解析MQTT配置文件时发生JSON格式错误: {ConfigPath}", ConfigFilePath);
        }
        catch (IOException ex)
        {
            _logger?.LogError(ex, "读取MQTT配置文件时发生IO错误: {ConfigPath}", ConfigFilePath);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "加载MQTT配置时发生未知错误: {ConfigPath}", ConfigFilePath);
        }
    }

    /// <summary>
    /// 从JsonElement反序列化配置属性（System.Text.Json）
    /// 高性能JSON解析，减少内存分配
    /// </summary>
    /// <param name="element">JSON元素</param>
    private void DeserializeFromJsonElement(JsonElement element)
    {
        try
        {
            // 字符串属性
            if (element.TryGetProperty("Server", out var serverElement) && serverElement.ValueKind == JsonValueKind.String)
                Server = serverElement.GetString() ?? Server;
                
            if (element.TryGetProperty("ClientId", out var clientIdElement) && clientIdElement.ValueKind == JsonValueKind.String)
                ClientId = clientIdElement.GetString() ?? ClientId;
                
            if (element.TryGetProperty("Username", out var usernameElement) && usernameElement.ValueKind == JsonValueKind.String)
                Username = usernameElement.GetString() ?? Username;
                
            if (element.TryGetProperty("Password", out var passwordElement) && passwordElement.ValueKind == JsonValueKind.String)
                Password = passwordElement.GetString() ?? Password;
                
            if (element.TryGetProperty("WillTopic", out var willTopicElement) && willTopicElement.ValueKind == JsonValueKind.String)
                WillTopic = willTopicElement.GetString() ?? WillTopic;
                
            if (element.TryGetProperty("WillMessage", out var willMessageElement) && willMessageElement.ValueKind == JsonValueKind.String)
                WillMessage = willMessageElement.GetString() ?? WillMessage;
                
            if (element.TryGetProperty("InstanceId", out var instanceIdElement) && instanceIdElement.ValueKind == JsonValueKind.String)
                InstanceId = instanceIdElement.GetString() ?? InstanceId;
                
            if (element.TryGetProperty("AccessKeyId", out var accessKeyIdElement) && accessKeyIdElement.ValueKind == JsonValueKind.String)
                AccessKeyId = accessKeyIdElement.GetString() ?? AccessKeyId;
                
            if (element.TryGetProperty("AccessKeySecret", out var accessKeySecretElement) && accessKeySecretElement.ValueKind == JsonValueKind.String)
                AccessKeySecret = accessKeySecretElement.GetString() ?? AccessKeySecret;

            // 数值属性
            if (element.TryGetProperty("Port", out var portElement) && portElement.ValueKind == JsonValueKind.Number)
                Port = portElement.GetInt32();
                
            if (element.TryGetProperty("ConnectionTimeoutSeconds", out var timeoutElement) && timeoutElement.ValueKind == JsonValueKind.Number)
                ConnectTimeout = timeoutElement.GetInt32();
                
            if (element.TryGetProperty("KeepAliveSeconds", out var keepAliveElement) && keepAliveElement.ValueKind == JsonValueKind.Number)
                KeepAliveInterval = keepAliveElement.GetInt32();
                
            if (element.TryGetProperty("DefaultQoS", out var qosElement) && qosElement.ValueKind == JsonValueKind.Number)
                DefaultQoS = qosElement.GetInt32();
                
            if (element.TryGetProperty("WillQoS", out var willQosElement) && willQosElement.ValueKind == JsonValueKind.Number)
                WillQoS = willQosElement.GetInt32();
                
            if (element.TryGetProperty("ReconnectInterval", out var reconnectIntervalElement) && reconnectIntervalElement.ValueKind == JsonValueKind.Number)
                ReconnectInterval = reconnectIntervalElement.GetInt32();
                
            if (element.TryGetProperty("MaxReconnectAttempts", out var maxReconnectElement) && maxReconnectElement.ValueKind == JsonValueKind.Number)
                MaxReconnectAttempts = maxReconnectElement.GetInt32();
                
            if (element.TryGetProperty("MessageBufferSize", out var bufferSizeElement) && bufferSizeElement.ValueKind == JsonValueKind.Number)
                MessageBufferSize = bufferSizeElement.GetInt32();

            // 布尔属性
            if (element.TryGetProperty("EnableSsl", out var sslElement) && (sslElement.ValueKind == JsonValueKind.True || sslElement.ValueKind == JsonValueKind.False))
                UseTls = sslElement.GetBoolean();
                
            if (element.TryGetProperty("WillRetain", out var willRetainElement) && (willRetainElement.ValueKind == JsonValueKind.True || willRetainElement.ValueKind == JsonValueKind.False))
                WillRetain = willRetainElement.GetBoolean();
                
            if (element.TryGetProperty("CleanSession", out var cleanSessionElement) && (cleanSessionElement.ValueKind == JsonValueKind.True || cleanSessionElement.ValueKind == JsonValueKind.False))
                CleanSession = cleanSessionElement.GetBoolean();
                
            if (element.TryGetProperty("EnableMessageLogging", out var messageLoggingElement) && (messageLoggingElement.ValueKind == JsonValueKind.True || messageLoggingElement.ValueKind == JsonValueKind.False))
                EnableMessageLogging = messageLoggingElement.GetBoolean();

            // 数组属性 - 订阅主题
            if (element.TryGetProperty("SubscribeTopics", out var topicsElement) && topicsElement.ValueKind == JsonValueKind.Array)
            {
                var topics = new List<string>();
                foreach (var topicElement in topicsElement.EnumerateArray())
                {
                    if (topicElement.ValueKind == JsonValueKind.String)
                    {
                        var topic = topicElement.GetString();
                        if (!string.IsNullOrWhiteSpace(topic))
                            topics.Add(topic);
                    }
                }
                SubscribeTopics = topics;
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "反序列化MQTT配置属性时发生错误（System.Text.Json）");
        }
    }

    /// <summary>
    /// 从JObject反序列化配置属性（Newtonsoft.Json回退方案）
    /// 安全地处理各种数据类型的转换
    /// </summary>
    /// <param name="jobject">JSON对象</param>
    private void DeserializeFromJObject(JObject jobject)
    {
        try
        {
            // 字符串属性
            if (jobject.TryGetValue("Server", out var serverToken) && serverToken.Type == JTokenType.String)
                Server = serverToken.Value<string>() ?? Server;
                
            if (jobject.TryGetValue("ClientId", out var clientIdToken) && clientIdToken.Type == JTokenType.String)
                ClientId = clientIdToken.Value<string>() ?? ClientId;
                
            if (jobject.TryGetValue("Username", out var usernameToken) && usernameToken.Type == JTokenType.String)
                Username = usernameToken.Value<string>() ?? Username;
                
            if (jobject.TryGetValue("Password", out var passwordToken) && passwordToken.Type == JTokenType.String)
                Password = passwordToken.Value<string>() ?? Password;
                
            if (jobject.TryGetValue("WillTopic", out var willTopicToken) && willTopicToken.Type == JTokenType.String)
                WillTopic = willTopicToken.Value<string>();
                
            if (jobject.TryGetValue("WillMessage", out var willMessageToken) && willMessageToken.Type == JTokenType.String)
                WillMessage = willMessageToken.Value<string>();
                
            if (jobject.TryGetValue("InstanceId", out var instanceIdToken) && instanceIdToken.Type == JTokenType.String)
                InstanceId = instanceIdToken.Value<string>() ?? InstanceId;
                
            if (jobject.TryGetValue("AccessKeyId", out var accessKeyIdToken) && accessKeyIdToken.Type == JTokenType.String)
                AccessKeyId = accessKeyIdToken.Value<string>() ?? AccessKeyId;
                
            if (jobject.TryGetValue("AccessKeySecret", out var accessKeySecretToken) && accessKeySecretToken.Type == JTokenType.String)
                AccessKeySecret = accessKeySecretToken.Value<string>() ?? AccessKeySecret;

            // 数值属性
            if (jobject.TryGetValue("Port", out var portToken) && portToken.Type == JTokenType.Integer)
                Port = portToken.Value<int>();
                
            if (jobject.TryGetValue("ConnectionTimeoutSeconds", out var timeoutToken) && timeoutToken.Type == JTokenType.Integer)
                ConnectTimeout = timeoutToken.Value<int>();
                
            if (jobject.TryGetValue("KeepAliveSeconds", out var keepAliveToken) && keepAliveToken.Type == JTokenType.Integer)
                KeepAliveInterval = keepAliveToken.Value<int>();
                
            if (jobject.TryGetValue("DefaultQoS", out var qosToken) && qosToken.Type == JTokenType.Integer)
                DefaultQoS = qosToken.Value<int>();
                
            if (jobject.TryGetValue("WillQoS", out var willQosToken) && willQosToken.Type == JTokenType.Integer)
                WillQoS = willQosToken.Value<int>();
                
            if (jobject.TryGetValue("ReconnectInterval", out var reconnectIntervalToken) && reconnectIntervalToken.Type == JTokenType.Integer)
                ReconnectInterval = reconnectIntervalToken.Value<int>();
                
            if (jobject.TryGetValue("MaxReconnectAttempts", out var maxReconnectToken) && maxReconnectToken.Type == JTokenType.Integer)
                MaxReconnectAttempts = maxReconnectToken.Value<int>();
                
            if (jobject.TryGetValue("MessageBufferSize", out var bufferSizeToken) && bufferSizeToken.Type == JTokenType.Integer)
                MessageBufferSize = bufferSizeToken.Value<int>();

            // 布尔属性
            if (jobject.TryGetValue("EnableSsl", out var sslToken) && sslToken.Type == JTokenType.Boolean)
                UseTls = sslToken.Value<bool>();
                
            if (jobject.TryGetValue("WillRetain", out var willRetainToken) && willRetainToken.Type == JTokenType.Boolean)
                WillRetain = willRetainToken.Value<bool>();
                
            if (jobject.TryGetValue("CleanSession", out var cleanSessionToken) && cleanSessionToken.Type == JTokenType.Boolean)
                CleanSession = cleanSessionToken.Value<bool>();
                
            if (jobject.TryGetValue("EnableMessageLogging", out var messageLoggingToken) && messageLoggingToken.Type == JTokenType.Boolean)
                EnableMessageLogging = messageLoggingToken.Value<bool>();

            // 数组属性 - 订阅主题
            if (jobject.TryGetValue("SubscribeTopics", out var topicsToken) && topicsToken.Type == JTokenType.Array)
            {
                var topics = new List<string>();
                foreach (var topicToken in topicsToken)
                {
                    if (topicToken.Type == JTokenType.String)
                    {
                        var topic = topicToken.Value<string>();
                        if (!string.IsNullOrWhiteSpace(topic))
                            topics.Add(topic);
                    }
                }
                SubscribeTopics = topics;
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "反序列化MQTT配置属性时发生错误");
        }
    }

    /// <summary>
    /// 重新加载配置文件
    /// 提供外部调用接口以支持配置热更新，支持配置验证
    /// </summary>
    public void ReloadConfiguration()
    {
        try
        {
            _logger?.LogInformation("检测到MQTT配置文件变化，正在重新加载配置...");
            
            // 备份当前配置
            var backupOptions = CreateBackup();
            
            try
            {
                LoadConfiguration();
                
                // 验证新配置
                var validationResults = new List<ValidationResult>();
                var validationContext = new ValidationContext(this);
                
                if (!Validator.TryValidateObject(this, validationContext, validationResults, true))
                {
                    _logger?.LogWarning("重新加载的配置验证失败，恢复备份配置。验证错误: {Errors}", 
                        string.Join("; ", validationResults.Select(r => r.ErrorMessage)));
                    RestoreFromBackup(backupOptions);
                    return;
                }
                
                // 重新生成凭证
                try
                {
                    // 使用之前的设备ID或默认设备ID
                    var deviceId = !string.IsNullOrWhiteSpace(_lastDeviceId) ? _lastDeviceId : DefaultDeviceId;
                    AutoGenerateCredentials();
                    _logger?.LogInformation("配置重新加载后已自动更新MQTT凭证");
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "配置重新加载后更新MQTT凭证失败");
                }
                
                _logger?.LogInformation("MQTT配置重新加载完成并验证通过");
            }
            catch (Exception loadEx)
            {
                _logger?.LogError(loadEx, "重新加载配置失败，恢复备份配置");
                RestoreFromBackup(backupOptions);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "重新加载MQTT配置时发生错误");
        }
    }

    /// <summary>
    /// 初始化配置文件监控器
    /// 监控MQTT.json配置文件的变化，实现配置热更新，支持防抖动
    /// </summary>
    private void InitializeFileWatcher()
    {
        try
        {
            var configDirectory = Path.GetDirectoryName(Path.GetFullPath(_configFilePath));
            var configFileName = Path.GetFileName(_configFilePath);
            
            if (string.IsNullOrEmpty(configDirectory) || string.IsNullOrEmpty(configFileName))
            {
                _logger?.LogWarning("无法初始化配置文件监视器：路径无效 {ConfigPath}", _configFilePath);
                return;
            }

            // 确保目录存在
            if (!Directory.Exists(configDirectory))
            {
                Directory.CreateDirectory(configDirectory);
                _logger?.LogInformation("创建配置文件目录: {Directory}", configDirectory);
            }

            _fileWatcher = new FileSystemWatcher(configDirectory, configFileName)
            {
                NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.Size | NotifyFilters.CreationTime,
                EnableRaisingEvents = true,
                IncludeSubdirectories = false
            };

            _fileWatcher.Changed += OnConfigFileChanged;
            _fileWatcher.Created += OnConfigFileChanged;
            _fileWatcher.Error += OnFileWatcherError;
            
            _logger?.LogInformation("已启动MQTT配置文件监控: {ConfigPath}", _configFilePath);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "初始化配置文件监控器失败: {ConfigPath}", _configFilePath);
        }
    }

    /// <summary>
    /// 配置文件变化事件处理器
    /// 当配置文件发生变化时自动重新加载配置并更新凭证，实现防抖动机制
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">文件系统事件参数</param>
    private void OnConfigFileChanged(object sender, FileSystemEventArgs e)
    {
        try
        {
            // 防抖动：使用CancellationToken取消之前的延迟任务
            _reloadCancellationTokenSource?.Cancel();
            _reloadCancellationTokenSource = new CancellationTokenSource();
            
            var token = _reloadCancellationTokenSource.Token;
            
            // 延迟1000毫秒后重新加载，避免文件正在写入时读取
            Task.Delay(1000, token).ContinueWith(async _ =>
            {
                if (token.IsCancellationRequested) return;
                
                try
                {
                    // 等待文件写入完成
                    await WaitForFileAccess(e.FullPath, token);
                    
                    if (!token.IsCancellationRequested)
                    {
                        ReloadConfiguration();
                    }
                }
                catch (OperationCanceledException)
                {
                    // 正常取消，不记录错误
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "配置文件变化处理时发生错误: {FilePath}", e.FullPath);
                }
            }, TaskContinuationOptions.OnlyOnRanToCompletion);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "处理配置文件变化事件时发生错误: {FilePath}", e.FullPath);
        }
    }
    
    /// <summary>
    /// 文件监视器错误事件处理
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">错误事件参数</param>
    private void OnFileWatcherError(object sender, ErrorEventArgs e)
    {
        _logger?.LogError(e.GetException(), "文件监视器发生错误，尝试重新初始化");
        
        try
        {
            _fileWatcher?.Dispose();
            InitializeFileWatcher();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "重新初始化文件监视器失败");
        }
    }

    /// <summary>
    /// 等待文件访问可用
    /// 确保文件写入完成后再读取
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>等待任务</returns>
    private async Task WaitForFileAccess(string filePath, CancellationToken cancellationToken)
    {
        const int maxAttempts = 10;
        const int delayMs = 100;
        
        for (int i = 0; i < maxAttempts; i++)
        {
            try
            {
                using var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.Read);
                return; // 文件可以访问
            }
            catch (IOException)
            {
                if (i == maxAttempts - 1) throw;
                await Task.Delay(delayMs, cancellationToken);
            }
        }
    }
    
    /// <summary>
    /// 创建当前配置的备份
    /// </summary>
    /// <returns>配置备份</returns>
    private MqttOptions CreateBackup()
    {
        return new MqttOptions
        {
            Server = this.Server,
            Port = this.Port,
            ClientId = this.ClientId,
            Username = this.Username,
            Password = this.Password,
            UseTls = this.UseTls,
            ConnectTimeout = this.ConnectTimeout,
            KeepAliveInterval = this.KeepAliveInterval,
            CleanSession = this.CleanSession,
            WillTopic = this.WillTopic,
            WillMessage = this.WillMessage,
            WillQoS = this.WillQoS,
            WillRetain = this.WillRetain,
            ReconnectInterval = this.ReconnectInterval,
            MaxReconnectAttempts = this.MaxReconnectAttempts,
            DefaultQoS = this.DefaultQoS,
            EnableMessageLogging = this.EnableMessageLogging,
            MessageBufferSize = this.MessageBufferSize,
            SubscribeTopics = new List<string>(this.SubscribeTopics ?? new List<string>()),
            InstanceId = this.InstanceId,
            AccessKeyId = this.AccessKeyId,
            AccessKeySecret = this.AccessKeySecret,
            DefaultDeviceId = this.DefaultDeviceId
        };
    }
    
    /// <summary>
    /// 从备份恢复配置
    /// </summary>
    /// <param name="backup">备份配置</param>
    private void RestoreFromBackup(MqttOptions backup)
    {
        this.Server = backup.Server;
        this.Port = backup.Port;
        this.ClientId = backup.ClientId;
        this.Username = backup.Username;
        this.Password = backup.Password;
        this.UseTls = backup.UseTls;
        this.ConnectTimeout = backup.ConnectTimeout;
        this.KeepAliveInterval = backup.KeepAliveInterval;
        this.CleanSession = backup.CleanSession;
        this.WillTopic = backup.WillTopic;
        this.WillMessage = backup.WillMessage;
        this.WillQoS = backup.WillQoS;
        this.WillRetain = backup.WillRetain;
        this.ReconnectInterval = backup.ReconnectInterval;
        this.MaxReconnectAttempts = backup.MaxReconnectAttempts;
        this.DefaultQoS = backup.DefaultQoS;
        this.EnableMessageLogging = backup.EnableMessageLogging;
        this.MessageBufferSize = backup.MessageBufferSize;
        this.SubscribeTopics = backup.SubscribeTopics;
        this.InstanceId = backup.InstanceId;
        this.AccessKeyId = backup.AccessKeyId;
        this.AccessKeySecret = backup.AccessKeySecret;
        this.DefaultDeviceId = backup.DefaultDeviceId;
    }
    
    /// <summary>
    /// 释放资源
    /// 清理文件监控器等资源
    /// </summary>
    public void Dispose()
    {
        try
        {
            _reloadCancellationTokenSource?.Cancel();
            _reloadCancellationTokenSource?.Dispose();
            _fileWatcher?.Dispose();
            _logger?.LogInformation("已释放MQTT配置文件监控器资源");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "释放配置文件监控器资源时发生错误");
        }
    }

    /// <summary>
    /// 动态生成MQTT客户端凭证
    /// 基于一机一密认证算法生成符合MQTT协议规范的客户端凭证
    /// </summary>
    /// <param name="deviceId">设备唯一标识符（如IMEI、设备ID等）</param>
    /// <returns>包含ClientId、Username、Password的凭证信息</returns>
    /// <exception cref="InvalidOperationException">当配置参数不完整时抛出异常</exception>
    public MqttCredentials GenerateCredentials(string deviceId)
    {
        if (string.IsNullOrWhiteSpace(deviceId))
            throw new ArgumentException("设备ID不能为空", nameof(deviceId));

        if (string.IsNullOrWhiteSpace(InstanceId))
            throw new InvalidOperationException("InstanceId配置不能为空，请检查MQTT.json配置文件");

        if (string.IsNullOrWhiteSpace(AccessKeyId))
            throw new InvalidOperationException("AccessKeyId配置不能为空，请检查MQTT.json配置文件");

        if (string.IsNullOrWhiteSpace(AccessKeySecret))
            throw new InvalidOperationException("AccessKeySecret配置不能为空，请检查MQTT.json配置文件");

        try
        {
            // 根据一机一密算法生成连接参数
            var clientId = $"{InstanceId}@@@{deviceId}";
            var username = $"DeviceCredential|{AccessKeyId}|{clientId}";
            var password = ComputeHmacSha1Signature(clientId, AccessKeySecret);

            _logger?.LogInformation("成功生成MQTT客户端凭证，ClientId: {ClientId}", clientId);

            return new MqttCredentials
            {
                ClientId = clientId,
                Username = username,
                Password = password
            };
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "生成MQTT客户端凭证时发生错误，设备ID: {DeviceId}", deviceId);
            throw;
        }
    }

    /// <summary>
    /// 计算HMAC-SHA1签名
    /// 使用指定的密钥对数据进行HMAC-SHA1签名计算
    /// </summary>
    /// <param name="data">待签名的数据</param>
    /// <param name="key">签名密钥</param>
    /// <returns>Base64编码的签名结果</returns>
    /// <exception cref="ArgumentException">当参数为空时抛出异常</exception>
    public string ComputeHmacSha1Signature(string data, string key)
    {
        if (string.IsNullOrWhiteSpace(data))
            throw new ArgumentException("待签名数据不能为空", nameof(data));

        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("签名密钥不能为空", nameof(key));

        try
        {
            using (var hmac = new System.Security.Cryptography.HMACSHA1(System.Text.Encoding.UTF8.GetBytes(key)))
            {
                var hashBytes = hmac.ComputeHash(System.Text.Encoding.UTF8.GetBytes(data));
                return Convert.ToBase64String(hashBytes);
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "计算HMAC-SHA1签名时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 使用生成的凭证更新当前MQTT配置
    /// 自动将生成的凭证应用到当前配置实例中
    /// </summary>
    /// <param name="deviceId">设备唯一标识符</param>
    /// <exception cref="InvalidOperationException">当配置参数不完整时抛出异常</exception>
    public void UpdateCredentials(string deviceId)
    {
        var credentials = GenerateCredentials(deviceId);
        
        ClientId = credentials.ClientId;
        Username = credentials.Username;
        Password = credentials.Password;
        
        // 记录最后使用的设备ID，用于配置热更新时自动重新生成凭证
        _lastDeviceId = deviceId;
        
        _logger?.LogInformation("已更新MQTT配置凭证，设备ID: {DeviceId}", deviceId);
     }

     /// <summary>
     /// 自动生成MQTT客户端凭证
     /// 在配置加载完成后自动调用，使用默认设备ID生成凭证
     /// </summary>
     private void AutoGenerateCredentials()
     {
         try
         {
             // 检查必要的配置参数是否已加载
             if (string.IsNullOrWhiteSpace(InstanceId) || 
                 string.IsNullOrWhiteSpace(AccessKeyId) || 
                 string.IsNullOrWhiteSpace(AccessKeySecret))
             {
                 _logger?.LogWarning("MQTT配置参数不完整，跳过自动凭证生成");
                 return;
             }

             // 使用默认设备ID生成凭证
             UpdateCredentials(DefaultDeviceId);
             _logger?.LogInformation("已自动生成MQTT客户端凭证，使用默认设备ID: {DeviceId}", DefaultDeviceId);
         }
         catch (Exception ex)
         {
             _logger?.LogError(ex, "自动生成MQTT客户端凭证时发生错误");
         }
     }

     /// <summary>
     /// 测试凭证生成功能
     /// 验证生成的凭证是否符合预期格式
     /// </summary>
     /// <param name="deviceId">设备ID</param>
     /// <returns>测试结果</returns>
     public string TestCredentialGeneration(string deviceId)
     {
         try
         {
             // 检查必要的配置参数
             if (string.IsNullOrWhiteSpace(InstanceId))
                 return "测试失败: InstanceId未配置";
             if (string.IsNullOrWhiteSpace(AccessKeyId))
                 return "测试失败: AccessKeyId未配置";
             if (string.IsNullOrWhiteSpace(AccessKeySecret))
                 return "测试失败: AccessKeySecret未配置";
             if (string.IsNullOrWhiteSpace(deviceId))
                 return "测试失败: DeviceId参数为空";

             // 生成凭证
             var credentials = GenerateCredentials(deviceId);

             // 验证凭证格式
             var results = new System.Text.StringBuilder();
             results.AppendLine("MQTT凭证生成测试结果:");
             results.AppendLine($"配置参数:");
             results.AppendLine($"  InstanceId: {InstanceId}");
             results.AppendLine($"  AccessKeyId: {AccessKeyId}");
             results.AppendLine($"  DeviceId: {deviceId}");
             results.AppendLine();

             results.AppendLine("生成的凭证:");
             results.AppendLine($"  ClientId: {credentials.ClientId}");
             results.AppendLine($"  Username: {credentials.Username}");
             results.AppendLine($"  Password: {credentials.Password}");
             results.AppendLine();

             // 验证格式
             var expectedClientId = $"{InstanceId}@@@{deviceId}";
             var expectedUsername = $"DeviceCredential|{AccessKeyId}|{expectedClientId}";

             results.AppendLine("格式验证:");
             results.AppendLine($"  ClientId格式正确: {(credentials.ClientId == expectedClientId ? "✓" : "✗")}");
             results.AppendLine($"  Username格式正确: {(credentials.Username == expectedUsername ? "✓" : "✗")}");
             results.AppendLine($"  Password非空: {(!string.IsNullOrWhiteSpace(credentials.Password) ? "✓" : "✗")}");
             results.AppendLine($"  Password长度: {credentials.Password?.Length ?? 0} 字符");

             return results.ToString();
         }
         catch (Exception ex)
         {
             return $"测试执行异常: {ex.Message}\n堆栈跟踪: {ex.StackTrace}";
         }
     }

     /// <summary>
     /// 运行完整的凭证验证测试
     /// 包括与SysAuthService算法的一致性验证
     /// </summary>
     /// <returns>完整的测试结果</returns>
     public static string RunCompleteTest()
     {
         try
         {
             var results = new System.Text.StringBuilder();
             results.AppendLine("=== MQTT凭证生成完整测试 ===");
             results.AppendLine();

             // 测试1: 使用预设参数进行算法一致性测试
             results.AppendLine("1. 算法一致性测试:");
             results.AppendLine(new string('-', 50));
             
             var testOptions = new MqttOptions
             {
                 InstanceId = "energylight",
                 AccessKeyId = "ak_c371a2975f7c4c0eb672b4675bfd2",
                 AccessKeySecret = "sk_test_secret_key_for_mqtt_auth"
             };
             
             var deviceId = "AdminNET_MQTT_Client";
             var testResult = testOptions.TestCredentialGeneration(deviceId);
             results.AppendLine(testResult);
             results.AppendLine();

             // 测试2: 验证HMAC-SHA1算法
             results.AppendLine("2. HMAC-SHA1算法验证:");
             results.AppendLine(new string('-', 50));
             
             var credentials = testOptions.GenerateCredentials(deviceId);
             var expectedSignature = testOptions.ComputeHmacSha1Signature(credentials.ClientId, testOptions.AccessKeySecret);
             
             results.AppendLine($"ClientId: {credentials.ClientId}");
             results.AppendLine($"AccessKeySecret: {testOptions.AccessKeySecret}");
             results.AppendLine($"生成的Password: {credentials.Password}");
             results.AppendLine($"预期的Signature: {expectedSignature}");
             results.AppendLine($"算法一致性: {(credentials.Password == expectedSignature ? "✓ 通过" : "✗ 失败")}");
             results.AppendLine();

             results.AppendLine("=== 测试完成 ===");
             return results.ToString();
         }
         catch (Exception ex)
         {
             return $"完整测试执行异常: {ex.Message}\n堆栈跟踪: {ex.StackTrace}";
         }
     }
     
     /// <summary>
     /// 验证配置数据
     /// 实现IValidatableObject接口，提供自定义验证逻辑
     /// </summary>
     /// <param name="validationContext">验证上下文</param>
     /// <returns>验证结果集合</returns>
     public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
     {
         var results = new List<ValidationResult>();
         
         // 验证服务器地址和端口的组合
         if (!string.IsNullOrWhiteSpace(Server) && Port > 0)
         {
             try
             {
                 var uri = new Uri($"mqtt://{Server}:{Port}");
                 if (uri.Host != Server)
                 {
                     results.Add(new ValidationResult("服务器地址格式无效", new[] { nameof(Server) }));
                 }
             }
             catch
             {
                 results.Add(new ValidationResult("服务器地址和端口组合无效", new[] { nameof(Server), nameof(Port) }));
             }
         }
         
         // 验证SSL/TLS配置
         if (UseTls && Port == 1883)
         {
             results.Add(new ValidationResult("启用SSL/TLS时建议使用8883端口", new[] { nameof(Port), nameof(UseTls) }));
         }
         
         // 验证遗嘱配置的完整性
         if (!string.IsNullOrWhiteSpace(WillTopic) && string.IsNullOrWhiteSpace(WillMessage))
         {
             results.Add(new ValidationResult("配置了遗嘱主题但未配置遗嘱消息", new[] { nameof(WillMessage) }));
         }
         
         // 验证订阅主题格式
         if (SubscribeTopics?.Any() == true)
         {
             foreach (var topic in SubscribeTopics)
             {
                 if (string.IsNullOrWhiteSpace(topic))
                 {
                     results.Add(new ValidationResult("订阅主题不能为空", new[] { nameof(SubscribeTopics) }));
                 }
                 else if (topic.Contains("#") && !topic.EndsWith("#") && !topic.EndsWith("/#"))
                 {
                     results.Add(new ValidationResult($"主题通配符'#'只能出现在主题末尾: {topic}", new[] { nameof(SubscribeTopics) }));
                 }
             }
         }
         
         // 验证凭证生成所需的参数
         if (!string.IsNullOrWhiteSpace(InstanceId) || !string.IsNullOrWhiteSpace(AccessKeyId) || !string.IsNullOrWhiteSpace(AccessKeySecret))
         {
             if (string.IsNullOrWhiteSpace(InstanceId))
                 results.Add(new ValidationResult("配置了访问密钥但未配置实例ID", new[] { nameof(InstanceId) }));
             if (string.IsNullOrWhiteSpace(AccessKeyId))
                 results.Add(new ValidationResult("配置了实例ID但未配置访问密钥ID", new[] { nameof(AccessKeyId) }));
             if (string.IsNullOrWhiteSpace(AccessKeySecret))
                 results.Add(new ValidationResult("配置了访问密钥ID但未配置访问密钥密码", new[] { nameof(AccessKeySecret) }));
         }
         
         return results;
     }
 }

/// <summary>
/// MQTT客户端凭证信息
/// 包含MQTT连接所需的客户端ID、用户名和密码
/// </summary>
public class MqttCredentials
{
    /// <summary>
    /// 客户端ID
    /// MQTT连接的唯一客户端标识符
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 用户名
    /// MQTT连接认证的用户名
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// 密码
    /// MQTT连接认证的密码（Base64编码的HMAC-SHA1签名）
    /// </summary>
    public string Password { get; set; } = string.Empty;
}